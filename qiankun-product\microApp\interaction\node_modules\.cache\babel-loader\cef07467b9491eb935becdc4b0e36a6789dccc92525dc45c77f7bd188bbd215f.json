{"ast": null, "code": "function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nfunction _defineProperty(e, r, t) { return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, { value: t, enumerable: !0, configurable: !0, writable: !0 }) : e[r] = t, e; }\nfunction _toPropertyKey(t) { var i = _toPrimitive(t, \"string\"); return \"symbol\" == typeof i ? i : i + \"\"; }\nfunction _toPrimitive(t, r) { if (\"object\" != typeof t || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || \"default\"); if (\"object\" != typeof i) return i; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (\"string\" === r ? String : Number)(t); }\nimport { defineComponent, inject, ref, openBlock, createElementBlock, normalizeClass, unref, withModifiers, renderSlot } from 'vue';\nimport '../../../hooks/index.mjs';\nimport '../../form/index.mjs';\nimport { throwError } from '../../../utils/error.mjs';\nimport { uploadContextKey } from './constants.mjs';\nimport { uploadDraggerProps, uploadDraggerEmits } from './upload-dragger.mjs';\nimport _export_sfc from '../../../_virtual/plugin-vue_export-helper.mjs';\nimport { useNamespace } from '../../../hooks/use-namespace/index.mjs';\nimport { useFormDisabled } from '../../form/src/hooks/use-form-common-props.mjs';\nvar _hoisted_1 = [\"onDrop\", \"onDragover\"];\nvar COMPONENT_NAME = \"ElUploadDrag\";\nvar __default__ = defineComponent({\n  name: COMPONENT_NAME\n});\nvar _sfc_main = /* @__PURE__ */defineComponent(_objectSpread(_objectSpread({}, __default__), {}, {\n  props: uploadDraggerProps,\n  emits: uploadDraggerEmits,\n  setup(__props, _ref) {\n    var emit = _ref.emit;\n    var uploaderContext = inject(uploadContextKey);\n    if (!uploaderContext) {\n      throwError(COMPONENT_NAME, \"usage: <el-upload><el-upload-dragger /></el-upload>\");\n    }\n    var ns = useNamespace(\"upload\");\n    var dragover = ref(false);\n    var disabled = useFormDisabled();\n    var onDrop = function onDrop(e) {\n      if (disabled.value) return;\n      dragover.value = false;\n      e.stopPropagation();\n      var files = Array.from(e.dataTransfer.files);\n      emit(\"file\", files);\n    };\n    var onDragover = function onDragover() {\n      if (!disabled.value) dragover.value = true;\n    };\n    return function (_ctx, _cache) {\n      return openBlock(), createElementBlock(\"div\", {\n        class: normalizeClass([unref(ns).b(\"dragger\"), unref(ns).is(\"dragover\", dragover.value)]),\n        onDrop: withModifiers(onDrop, [\"prevent\"]),\n        onDragover: withModifiers(onDragover, [\"prevent\"]),\n        onDragleave: _cache[0] || (_cache[0] = withModifiers(function ($event) {\n          return dragover.value = false;\n        }, [\"prevent\"]))\n      }, [renderSlot(_ctx.$slots, \"default\")], 42, _hoisted_1);\n    };\n  }\n}));\nvar UploadDragger = /* @__PURE__ */_export_sfc(_sfc_main, [[\"__file\", \"upload-dragger.vue\"]]);\nexport { UploadDragger as default };", "map": {"version": 3, "names": ["name", "COMPONENT_NAME", "uploaderContext", "inject", "uploadContextKey", "throwError", "ns", "useNamespace", "dragover", "ref", "disabled", "useFormDisabled", "onDrop", "e", "value", "stopPropagation", "files", "Array", "from", "dataTransfer", "emit", "onDragover"], "sources": ["../../../../../../packages/components/upload/src/upload-dragger.vue"], "sourcesContent": ["<template>\n  <div\n    :class=\"[ns.b('dragger'), ns.is('dragover', dragover)]\"\n    @drop.prevent=\"onDrop\"\n    @dragover.prevent=\"onDragover\"\n    @dragleave.prevent=\"dragover = false\"\n  >\n    <slot />\n  </div>\n</template>\n<script lang=\"ts\" setup>\nimport { inject, ref } from 'vue'\nimport { useNamespace } from '@element-plus/hooks'\nimport { useFormDisabled } from '@element-plus/components/form'\nimport { throwError } from '@element-plus/utils/error'\nimport { uploadContextKey } from './constants'\nimport { uploadDraggerEmits, uploadDraggerProps } from './upload-dragger'\n\nconst COMPONENT_NAME = 'ElUploadDrag'\n\ndefineOptions({\n  name: COMPONENT_NAME,\n})\n\ndefineProps(uploadDraggerProps)\nconst emit = defineEmits(uploadDraggerEmits)\n\nconst uploaderContext = inject(uploadContextKey)\nif (!uploaderContext) {\n  throwError(\n    COMPONENT_NAME,\n    'usage: <el-upload><el-upload-dragger /></el-upload>'\n  )\n}\n\nconst ns = useNamespace('upload')\nconst dragover = ref(false)\nconst disabled = useFormDisabled()\n\nconst onDrop = (e: DragEvent) => {\n  if (disabled.value) return\n  dragover.value = false\n\n  e.stopPropagation()\n\n  const files = Array.from(e.dataTransfer!.files)\n  emit('file', files)\n}\n\nconst onDragover = () => {\n  if (!disabled.value) dragover.value = true\n}\n</script>\n"], "mappings": ";;;;;;;;;;;;;;;;iCAoBc;EACZA,IAAM,EAAAC;AACR;;;;;;IAKM,IAAAC,eAAA,GAAkBC,MAAA,CAAOC,gBAAgB;IAC/C,IAAI,CAACF,eAAiB;MACpBG,UAAA,CACEJ,cAAA,EACA,qDACF;IAAA;IAGI,IAAAK,EAAA,GAAKC,YAAA,CAAa,QAAQ;IAC1B,IAAAC,QAAA,GAAWC,GAAA,CAAI,KAAK;IAC1B,IAAMC,QAAA,GAAWC,eAAgB;IAE3B,IAAAC,MAAA,GAAS,SAATA,OAAUC,CAAiB;MAC/B,IAAIH,QAAS,CAAAI,KAAA,EAAO;MACpBN,QAAA,CAASM,KAAQ;MAEjBD,CAAA,CAAEE,eAAgB;MAElB,IAAMC,KAAQ,GAAAC,KAAA,CAAMC,IAAK,CAAAL,CAAA,CAAEM,YAAA,CAAcH,KAAK;MAC9CI,IAAA,CAAK,QAAQJ,KAAK;IAAA,CACpB;IAEA,IAAMK,UAAA,GAAa,SAAbA,WAAA,EAAmB;MACvB,IAAI,CAACX,QAAS,CAAAI,KAAA,EAAON,QAAA,CAASM,KAAQ;IAAA,CACxC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
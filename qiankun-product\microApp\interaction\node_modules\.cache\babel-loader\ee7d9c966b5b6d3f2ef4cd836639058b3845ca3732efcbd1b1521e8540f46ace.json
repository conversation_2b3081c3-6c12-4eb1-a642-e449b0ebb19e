{"ast": null, "code": "\"use strict\";\n\nvar detector = module.exports = {};\ndetector.isIE = function (version) {\n  function isAnyIeVersion() {\n    var agent = navigator.userAgent.toLowerCase();\n    return agent.indexOf(\"msie\") !== -1 || agent.indexOf(\"trident\") !== -1 || agent.indexOf(\" edge/\") !== -1;\n  }\n  if (!isAnyIeVersion()) {\n    return false;\n  }\n  if (!version) {\n    return true;\n  }\n\n  //Shamelessly stolen from https://gist.github.com/padolsey/527683\n  var ieVersion = function () {\n    var undef,\n      v = 3,\n      div = document.createElement(\"div\"),\n      all = div.getElementsByTagName(\"i\");\n    do {\n      div.innerHTML = \"<!--[if gt IE \" + ++v + \"]><i></i><![endif]-->\";\n    } while (all[0]);\n    return v > 4 ? v : undef;\n  }();\n  return version === ieVersion;\n};\ndetector.isLegacyOpera = function () {\n  return !!window.opera;\n};", "map": {"version": 3, "names": ["detector", "module", "exports", "isIE", "version", "isAnyIeVersion", "agent", "navigator", "userAgent", "toLowerCase", "indexOf", "ieVersion", "undef", "v", "div", "document", "createElement", "all", "getElementsByTagName", "innerHTML", "isLegacyOpera", "window", "opera"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/node_modules/.pnpm/element-resize-detector@1.2.4/node_modules/element-resize-detector/src/browser-detector.js"], "sourcesContent": ["\"use strict\";\n\nvar detector = module.exports = {};\n\ndetector.isIE = function(version) {\n    function isAnyIeVersion() {\n        var agent = navigator.userAgent.toLowerCase();\n        return agent.indexOf(\"msie\") !== -1 || agent.indexOf(\"trident\") !== -1 || agent.indexOf(\" edge/\") !== -1;\n    }\n\n    if(!isAnyIeVersion()) {\n        return false;\n    }\n\n    if(!version) {\n        return true;\n    }\n\n    //Shamelessly stolen from https://gist.github.com/padolsey/527683\n    var ieVersion = (function(){\n        var undef,\n            v = 3,\n            div = document.createElement(\"div\"),\n            all = div.getElementsByTagName(\"i\");\n\n        do {\n            div.innerHTML = \"<!--[if gt IE \" + (++v) + \"]><i></i><![endif]-->\";\n        }\n        while (all[0]);\n\n        return v > 4 ? v : undef;\n    }());\n\n    return version === ieVersion;\n};\n\ndetector.isLegacyOpera = function() {\n    return !!window.opera;\n};\n"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,QAAQ,GAAGC,MAAM,CAACC,OAAO,GAAG,CAAC,CAAC;AAElCF,QAAQ,CAACG,IAAI,GAAG,UAASC,OAAO,EAAE;EAC9B,SAASC,cAAcA,CAAA,EAAG;IACtB,IAAIC,KAAK,GAAGC,SAAS,CAACC,SAAS,CAACC,WAAW,CAAC,CAAC;IAC7C,OAAOH,KAAK,CAACI,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,IAAIJ,KAAK,CAACI,OAAO,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,IAAIJ,KAAK,CAACI,OAAO,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;EAC5G;EAEA,IAAG,CAACL,cAAc,CAAC,CAAC,EAAE;IAClB,OAAO,KAAK;EAChB;EAEA,IAAG,CAACD,OAAO,EAAE;IACT,OAAO,IAAI;EACf;;EAEA;EACA,IAAIO,SAAS,GAAI,YAAU;IACvB,IAAIC,KAAK;MACLC,CAAC,GAAG,CAAC;MACLC,GAAG,GAAGC,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;MACnCC,GAAG,GAAGH,GAAG,CAACI,oBAAoB,CAAC,GAAG,CAAC;IAEvC,GAAG;MACCJ,GAAG,CAACK,SAAS,GAAG,gBAAgB,GAAI,EAAEN,CAAE,GAAG,uBAAuB;IACtE,CAAC,QACMI,GAAG,CAAC,CAAC,CAAC;IAEb,OAAOJ,CAAC,GAAG,CAAC,GAAGA,CAAC,GAAGD,KAAK;EAC5B,CAAC,CAAC,CAAE;EAEJ,OAAOR,OAAO,KAAKO,SAAS;AAChC,CAAC;AAEDX,QAAQ,CAACoB,aAAa,GAAG,YAAW;EAChC,OAAO,CAAC,CAACC,MAAM,CAACC,KAAK;AACzB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}
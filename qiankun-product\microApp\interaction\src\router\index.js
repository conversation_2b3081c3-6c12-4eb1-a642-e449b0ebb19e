import { createRouter, createWebHistory } from 'vue-router'

const TemplateManage = () => import('@/views/TemplateManage/TemplateManage.vue') // 消息模板管理
const MessageSwitch = () => import('@/views/MessageSwitch/MessageSwitch.vue') // 消息开关管理
const BoxMessage = () => import('@/views/BoxMessage/BoxMessage.vue') // 消息盒子
const PushMessage = () => import('@/views/BoxMessage/PushMessage.vue') // 消息推送
const PushMessageDetails = () => import('@/views/BoxMessage/PushMessageDetails.vue') // 消息推送详情
const PersonalDoList = () => import('@/views/PersonalDoList/PersonalDoList.vue') // 个人待办
const SendTextMessage = () => import('@/views/TextMessageManage/SendTextMessage.vue') // 发送短信
const SentTextMessage = () => import('@/views/TextMessageManage/SentTextMessage.vue') // 已发短信
const SentTextMessageSet = () => import('@/views/TextMessageManage/SentTextMessageSet.vue') // 已发短信集
const SentTextMessageDelay = () => import('@/views/TextMessageManage/SentTextMessageDelay.vue') // 预约短信集
const SentTextMessageSetDetails = () => import('@/views/TextMessageManage/SentTextMessageSetDetails.vue') // 已发短信集详情
const UplinkTextMessage = () => import('@/views/TextMessageManage/UplinkTextMessage.vue') // 上行短信
const DoNotDisturbUser = () => import('@/views/TextMessageManage/DoNotDisturbUser.vue') // 短信勿扰名单
const AddressBook = () => import('@/views/AddressBook/AddressBook.vue') // 通讯录
const ClusterManage = () => import('@/views/ClusterManage/ClusterManage.vue') // 群组管理
const ClusterInfoManage = () => import('@/views/ClusterManage/ClusterInfoManage.vue') // 群组信息管理
const ClusterFileManage = () => import('@/views/ClusterManage/ClusterFileManage.vue') // 群文件管理
const ClusterFileArchive = () => import('@/views/ClusterManage/ClusterFileArchive.vue') // 群文件存档
const CircleOfFriends = () => import('@/views/CircleOfFriends/CircleOfFriends.vue') // 朋友圈
const CircleOfFriendsComment = () => import('@/views/CircleOfFriends/CircleOfFriendsComment.vue') // 朋友圈评论
const NoticeAnnouncement = () => import('@/views/NoticeAnnouncement/NoticeAnnouncement.vue') // 通知公告
const NoticeAnnouncementList = () => import('@/views/NoticeAnnouncement/NoticeAnnouncementList.vue') // 通知公告--列表
const NoticeAnnouncementDraft = () => import('@/views/NoticeAnnouncement/NoticeAnnouncementDraft.vue') // 通知公告草稿
const PublishNoticeAnnouncement = () => import('@/views/NoticeAnnouncement/PublishNoticeAnnouncement.vue') // 发布通知公告
const NoticeAnnouncementDetails = () => import('@/views/NoticeAnnouncement/NoticeAnnouncementDetails.vue') // 通知公告详情
const MyCollection = () => import('@/views/MyCollection/MyCollection.vue') // 收藏
const VideoMeeting = () => import('@/views/VideoMeeting/VideoMeeting.vue') // 视频会议管理
const MyVideoMeeting = () => import('@/views/VideoMeeting/MyVideoMeeting.vue') // 我的视频会议
const VideoMeetinDetails = () => import('@/views/VideoMeeting/VideoMeetinDetails.vue') // 视频会议详情
const FrequentContact = () => import('@/views/FrequentContact/FrequentContact.vue') // 常用联系人
const CommunicationPartner = () => import('@/views/CommunicationPartner/CommunicationPartner.vue') // 交流对象
const LiveManagement = () => import('@/views/LiveManagement/LiveManagement.vue') // 直播管理
const LiveManagementDetails = () => import('@/views/LiveManagement/LiveManagementDetails.vue') // 直播管理
const LiveBroadcast = () => import('@/views/LiveManagement/LiveBroadcast.vue') // 在线直播
const LiveBroadcastDetails = () => import('@/views/LiveManagement/LiveBroadcastDetails.vue') // 在线直播详情
const LiveShare = () => import('@/views/LiveManagement/LiveShare.vue') // 直播分享页面
const popupWindowManagement = () => import('@/views/popupWindowManagement/popupWindowManagement.vue') // 弹窗管理

const routes = [
  { path: '/TemplateManage', name: 'TemplateManage', component: TemplateManage, meta: { moduleName: 'main' } },
  { path: '/MessageSwitch', name: 'MessageSwitch', component: MessageSwitch, meta: { moduleName: 'main' } },
  { path: '/BoxMessage', name: 'BoxMessage', component: BoxMessage, meta: { moduleName: 'main' } },
  { path: '/PushMessage', name: 'PushMessage', component: PushMessage, meta: { moduleName: 'main' } },
  { path: '/PushMessageDetails', name: 'PushMessageDetails', component: PushMessageDetails, meta: { moduleName: 'main' } },
  { path: '/PersonalDoList', name: 'PersonalDoList', component: PersonalDoList, meta: { moduleName: 'main' } },
  { path: '/SendTextMessage', name: 'SendTextMessage', component: SendTextMessage, meta: { moduleName: 'main' } },
  { path: '/SentTextMessage', name: 'SentTextMessage', component: SentTextMessage, meta: { moduleName: 'main' } },
  { path: '/SentTextMessageSet', name: 'SentTextMessageSet', component: SentTextMessageSet, meta: { moduleName: 'main' } },
  { path: '/SentTextMessageDelay', name: 'SentTextMessageDelay', component: SentTextMessageDelay, meta: { moduleName: 'main' } },
  { path: '/SentTextMessageSetDetails', name: 'SentTextMessageSetDetails', component: SentTextMessageSetDetails, meta: { moduleName: 'main' } },
  { path: '/UplinkTextMessage', name: 'UplinkTextMessage', component: UplinkTextMessage, meta: { moduleName: 'main' } },
  { path: '/DoNotDisturbUser', name: 'DoNotDisturbUser', component: DoNotDisturbUser, meta: { moduleName: 'main' } },
  { path: '/AddressBook', name: 'AddressBook', component: AddressBook, meta: { moduleName: 'main' } },
  { path: '/ClusterManage', name: 'ClusterManage', component: ClusterManage, meta: { moduleName: 'main' } },
  { path: '/ClusterInfoManage', name: 'ClusterInfoManage', component: ClusterInfoManage, meta: { moduleName: 'main' } },
  { path: '/ClusterFileManage', name: 'ClusterFileManage', component: ClusterFileManage, meta: { moduleName: 'main' } },
  { path: '/ClusterFileArchive', name: 'ClusterFileArchive', component: ClusterFileArchive, meta: { moduleName: 'main' } },
  { path: '/CircleOfFriends', name: 'CircleOfFriends', component: CircleOfFriends, meta: { moduleName: 'main' } },
  { path: '/CircleOfFriendsComment', name: 'CircleOfFriendsComment', component: CircleOfFriendsComment, meta: { moduleName: 'main' } },
  { path: '/NoticeAnnouncement', name: 'NoticeAnnouncement', component: NoticeAnnouncement, meta: { moduleName: 'main' } },
  { path: '/NoticeAnnouncementList', name: 'NoticeAnnouncementList', component: NoticeAnnouncementList, meta: { moduleName: 'main' } },
  { path: '/NoticeAnnouncementDraft', name: 'NoticeAnnouncementDraft', component: NoticeAnnouncementDraft, meta: { moduleName: 'main' } },
  { path: '/PublishNoticeAnnouncement', name: 'PublishNoticeAnnouncement', component: PublishNoticeAnnouncement, meta: { moduleName: 'main' } },
  { path: '/NoticeAnnouncementDetails', name: 'NoticeAnnouncementDetails', component: NoticeAnnouncementDetails, meta: { moduleName: 'main' } },
  { path: '/MyCollection', name: 'MyCollection', component: MyCollection, meta: { moduleName: 'main' } },
  { path: '/VideoMeeting', name: 'VideoMeeting', component: VideoMeeting, meta: { moduleName: 'main' } },
  { path: '/MyVideoMeeting', name: 'MyVideoMeeting', component: MyVideoMeeting, meta: { moduleName: 'main' } },
  { path: '/VideoMeetinDetails', name: 'VideoMeetinDetails', component: VideoMeetinDetails, meta: { moduleName: 'main' } },
  { path: '/FrequentContact', name: 'FrequentContact', component: FrequentContact, meta: { moduleName: 'main' } },
  { path: '/CommunicationPartner', name: 'CommunicationPartner', component: CommunicationPartner, meta: { moduleName: 'main' } },
  { path: '/LiveManagement', name: 'LiveManagement', component: LiveManagement, meta: { moduleName: 'main' } },
  { path: '/LiveManagementDetails', name: 'LiveManagementDetails', component: LiveManagementDetails, meta: { moduleName: 'main' } },
  { path: '/LiveBroadcast', name: 'LiveBroadcast', component: LiveBroadcast, meta: { moduleName: 'main' } },
  { path: '/LiveBroadcastDetails', name: 'LiveBroadcastDetails', component: LiveBroadcastDetails, meta: { moduleName: 'main' } },
  { path: '/LiveShare', name: 'LiveShare', component: LiveShare, meta: { moduleName: 'PUBLIC' } },
  { path: '/popupWindowManagement', name: 'popupWindowManagement', component: popupWindowManagement, meta: { moduleName: 'main' } },

]

const router = createRouter({
  history: createWebHistory(process.env.BASE_URL),
  routes
})

export { routes }
export default router

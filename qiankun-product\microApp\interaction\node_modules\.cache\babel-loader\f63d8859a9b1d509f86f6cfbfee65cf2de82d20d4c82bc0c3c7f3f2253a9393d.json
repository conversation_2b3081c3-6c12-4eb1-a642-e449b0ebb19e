{"ast": null, "code": "import '../../../utils/index.mjs';\nimport { columns } from './common.mjs';\nimport { buildProps, definePropType } from '../../../utils/vue/props/runtime.mjs';\nvar tableV2HeaderRowProps = buildProps({\n  class: String,\n  columns,\n  columnsStyles: {\n    type: definePropType(Object),\n    required: true\n  },\n  headerIndex: Number,\n  style: {\n    type: definePropType(Object)\n  }\n});\nexport { tableV2HeaderRowProps };", "map": {"version": 3, "names": ["tableV2HeaderRowProps", "buildProps", "class", "String", "columns", "columnsStyles", "type", "definePropType", "Object", "required", "headerIndex", "Number", "style"], "sources": ["../../../../../../packages/components/table-v2/src/header-row.ts"], "sourcesContent": ["import { buildProps, definePropType } from '@element-plus/utils'\nimport { columns } from './common'\n\nimport type { CSSProperties, ExtractPropTypes } from 'vue'\nimport type { KeyType } from './types'\n\nexport const tableV2HeaderRowProps = buildProps({\n  class: String,\n  columns,\n  columnsStyles: {\n    type: definePropType<Record<KeyType, CSSProperties>>(Object),\n    required: true,\n  },\n  headerIndex: Number,\n  style: { type: definePropType<CSSProperties>(Object) },\n} as const)\n\nexport type TableV2HeaderRowProps = ExtractPropTypes<\n  typeof tableV2HeaderRowProps\n>\n"], "mappings": ";;;AAEY,IAACA,qBAAqB,GAAGC,UAAU,CAAC;EAC9CC,KAAK,EAAEC,MAAM;EACbC,OAAO;EACPC,aAAa,EAAE;IACbC,IAAI,EAAEC,cAAc,CAACC,MAAM,CAAC;IAC5BC,QAAQ,EAAE;EACd,CAAG;EACDC,WAAW,EAAEC,MAAM;EACnBC,KAAK,EAAE;IAAEN,IAAI,EAAEC,cAAc,CAACC,MAAM;EAAC;AACvC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
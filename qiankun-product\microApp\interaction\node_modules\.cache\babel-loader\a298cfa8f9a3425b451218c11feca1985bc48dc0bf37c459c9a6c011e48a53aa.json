{"ast": null, "code": "function _toConsumableArray(r) { return _arrayWithoutHoles(r) || _iterableToArray(r) || _unsupportedIterableToArray(r) || _nonIterableSpread(); }\nfunction _nonIterableSpread() { throw new TypeError(\"Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); }\nfunction _iterableToArray(r) { if (\"undefined\" != typeof Symbol && null != r[Symbol.iterator] || null != r[\"@@iterator\"]) return Array.from(r); }\nfunction _arrayWithoutHoles(r) { if (Array.isArray(r)) return _arrayLikeToArray(r); }\nfunction _slicedToArray(r, e) { return _arrayWithHoles(r) || _iterableToArrayLimit(r, e) || _unsupportedIterableToArray(r, e) || _nonIterableRest(); }\nfunction _nonIterableRest() { throw new TypeError(\"Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); }\nfunction _unsupportedIterableToArray(r, a) { if (r) { if (\"string\" == typeof r) return _arrayLikeToArray(r, a); var t = {}.toString.call(r).slice(8, -1); return \"Object\" === t && r.constructor && (t = r.constructor.name), \"Map\" === t || \"Set\" === t ? Array.from(r) : \"Arguments\" === t || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t) ? _arrayLikeToArray(r, a) : void 0; } }\nfunction _arrayLikeToArray(r, a) { (null == a || a > r.length) && (a = r.length); for (var e = 0, n = Array(a); e < a; e++) n[e] = r[e]; return n; }\nfunction _iterableToArrayLimit(r, l) { var t = null == r ? null : \"undefined\" != typeof Symbol && r[Symbol.iterator] || r[\"@@iterator\"]; if (null != t) { var e, n, i, u, a = [], f = !0, o = !1; try { if (i = (t = t.call(r)).next, 0 === l) { if (Object(t) !== t) return; f = !1; } else for (; !(f = (e = i.call(t)).done) && (a.push(e.value), a.length !== l); f = !0); } catch (r) { o = !0, n = r; } finally { try { if (!f && null != t.return && (u = t.return(), Object(u) !== u)) return; } finally { if (o) throw n; } } return a; } }\nfunction _arrayWithHoles(r) { if (Array.isArray(r)) return r; }\nimport { computed } from 'vue';\nimport dayjs from 'dayjs';\nimport localeData from 'dayjs/plugin/localeData.js';\nimport '../../../hooks/index.mjs';\nimport '../../time-picker/index.mjs';\nimport '../../../constants/index.mjs';\nimport { getPrevMonthLastDays, getMonthDays, toNestedArr } from './date-table.mjs';\nimport { useLocale } from '../../../hooks/use-locale/index.mjs';\nimport { rangeArr } from '../../time-picker/src/utils.mjs';\nimport { WEEK_DAYS } from '../../../constants/date.mjs';\nvar useDateTable = function useDateTable(props, emit) {\n  dayjs.extend(localeData);\n  var firstDayOfWeek = dayjs.localeData().firstDayOfWeek();\n  var _useLocale = useLocale(),\n    t = _useLocale.t,\n    lang = _useLocale.lang;\n  var now = dayjs().locale(lang.value);\n  var isInRange = computed(function () {\n    return !!props.range && !!props.range.length;\n  });\n  var rows = computed(function () {\n    var days = [];\n    if (isInRange.value) {\n      var _props$range = _slicedToArray(props.range, 2),\n        start = _props$range[0],\n        end = _props$range[1];\n      var currentMonthRange = rangeArr(end.date() - start.date() + 1).map(function (index) {\n        return {\n          text: start.date() + index,\n          type: \"current\"\n        };\n      });\n      var remaining = currentMonthRange.length % 7;\n      remaining = remaining === 0 ? 0 : 7 - remaining;\n      var nextMonthRange = rangeArr(remaining).map(function (_, index) {\n        return {\n          text: index + 1,\n          type: \"next\"\n        };\n      });\n      days = currentMonthRange.concat(nextMonthRange);\n    } else {\n      var firstDay = props.date.startOf(\"month\").day();\n      var prevMonthDays = getPrevMonthLastDays(props.date, (firstDay - firstDayOfWeek + 7) % 7).map(function (day) {\n        return {\n          text: day,\n          type: \"prev\"\n        };\n      });\n      var currentMonthDays = getMonthDays(props.date).map(function (day) {\n        return {\n          text: day,\n          type: \"current\"\n        };\n      });\n      days = [].concat(_toConsumableArray(prevMonthDays), _toConsumableArray(currentMonthDays));\n      var _remaining = 7 - (days.length % 7 || 7);\n      var nextMonthDays = rangeArr(_remaining).map(function (_, index) {\n        return {\n          text: index + 1,\n          type: \"next\"\n        };\n      });\n      days = days.concat(nextMonthDays);\n    }\n    return toNestedArr(days);\n  });\n  var weekDays = computed(function () {\n    var start = firstDayOfWeek;\n    if (start === 0) {\n      return WEEK_DAYS.map(function (_) {\n        return t(`el.datepicker.weeks.${_}`);\n      });\n    } else {\n      return WEEK_DAYS.slice(start).concat(WEEK_DAYS.slice(0, start)).map(function (_) {\n        return t(`el.datepicker.weeks.${_}`);\n      });\n    }\n  });\n  var getFormattedDate = function getFormattedDate(day, type) {\n    switch (type) {\n      case \"prev\":\n        return props.date.startOf(\"month\").subtract(1, \"month\").date(day);\n      case \"next\":\n        return props.date.startOf(\"month\").add(1, \"month\").date(day);\n      case \"current\":\n        return props.date.date(day);\n    }\n  };\n  var handlePickDay = function handlePickDay(_ref) {\n    var text = _ref.text,\n      type = _ref.type;\n    var date = getFormattedDate(text, type);\n    emit(\"pick\", date);\n  };\n  var getSlotData = function getSlotData(_ref2) {\n    var text = _ref2.text,\n      type = _ref2.type;\n    var day = getFormattedDate(text, type);\n    return {\n      isSelected: day.isSame(props.selectedDay),\n      type: `${type}-month`,\n      day: day.format(\"YYYY-MM-DD\"),\n      date: day.toDate()\n    };\n  };\n  return {\n    now,\n    isInRange,\n    rows,\n    weekDays,\n    getFormattedDate,\n    handlePickDay,\n    getSlotData\n  };\n};\nexport { useDateTable };", "map": {"version": 3, "names": ["useDateTable", "props", "emit", "dayjs", "extend", "localeData", "firstDayOfWeek", "_useLocale", "useLocale", "t", "lang", "now", "locale", "value", "isInRange", "computed", "range", "length", "rows", "days", "_props$range", "_slicedToArray", "start", "end", "currentMonthRange", "rangeArr", "date", "map", "index", "text", "type", "remaining", "nextMonthRange", "_", "concat", "firstDay", "startOf", "day", "prevMonthDays", "getPrevMonthLastDays", "currentMonthDays", "getMonthDays", "_toConsumableArray", "nextMonthDays", "toNestedArr", "weekDays", "WEEK_DAYS", "slice", "getFormattedDate", "subtract", "add", "handlePickDay", "_ref", "getSlotData", "_ref2", "isSelected", "isSame", "selected<PERSON>ay", "format", "toDate"], "sources": ["../../../../../../packages/components/calendar/src/use-date-table.ts"], "sourcesContent": ["import { computed } from 'vue'\nimport dayjs from 'dayjs'\nimport localeData from 'dayjs/plugin/localeData.js'\nimport { useLocale } from '@element-plus/hooks'\nimport { rangeArr } from '@element-plus/components/time-picker'\nimport { WEEK_DAYS } from '@element-plus/constants'\nimport { getMonthDays, getPrevMonthLastDays, toNestedArr } from './date-table'\n\nimport type { SetupContext } from 'vue'\nimport type { Dayjs } from 'dayjs'\nimport type {\n  CalendarDateCell,\n  CalendarDateCellType,\n  DateTableEmits,\n  DateTableProps,\n} from './date-table'\n\nexport const useDateTable = (\n  props: DateTableProps,\n  emit: SetupContext<DateTableEmits>['emit']\n) => {\n  dayjs.extend(localeData)\n  // https://day.js.org/docs/en/i18n/locale-data\n  const firstDayOfWeek: number = dayjs.localeData().firstDayOfWeek()\n\n  const { t, lang } = useLocale()\n  const now = dayjs().locale(lang.value)\n\n  const isInRange = computed(() => !!props.range && !!props.range.length)\n\n  const rows = computed(() => {\n    let days: CalendarDateCell[] = []\n    if (isInRange.value) {\n      const [start, end] = props.range!\n      const currentMonthRange: CalendarDateCell[] = rangeArr(\n        end.date() - start.date() + 1\n      ).map((index) => ({\n        text: start.date() + index,\n        type: 'current',\n      }))\n\n      let remaining = currentMonthRange.length % 7\n      remaining = remaining === 0 ? 0 : 7 - remaining\n      const nextMonthRange: CalendarDateCell[] = rangeArr(remaining).map(\n        (_, index) => ({\n          text: index + 1,\n          type: 'next',\n        })\n      )\n      days = currentMonthRange.concat(nextMonthRange)\n    } else {\n      const firstDay = props.date.startOf('month').day()\n      const prevMonthDays: CalendarDateCell[] = getPrevMonthLastDays(\n        props.date,\n        (firstDay - firstDayOfWeek + 7) % 7\n      ).map((day) => ({\n        text: day,\n        type: 'prev',\n      }))\n      const currentMonthDays: CalendarDateCell[] = getMonthDays(props.date).map(\n        (day) => ({\n          text: day,\n          type: 'current',\n        })\n      )\n      days = [...prevMonthDays, ...currentMonthDays]\n      const remaining = 7 - (days.length % 7 || 7)\n      const nextMonthDays: CalendarDateCell[] = rangeArr(remaining).map(\n        (_, index) => ({\n          text: index + 1,\n          type: 'next',\n        })\n      )\n      days = days.concat(nextMonthDays)\n    }\n    return toNestedArr(days)\n  })\n\n  const weekDays = computed(() => {\n    const start = firstDayOfWeek\n    if (start === 0) {\n      return WEEK_DAYS.map((_) => t(`el.datepicker.weeks.${_}`))\n    } else {\n      return WEEK_DAYS.slice(start)\n        .concat(WEEK_DAYS.slice(0, start))\n        .map((_) => t(`el.datepicker.weeks.${_}`))\n    }\n  })\n\n  const getFormattedDate = (day: number, type: CalendarDateCellType): Dayjs => {\n    switch (type) {\n      case 'prev':\n        return props.date.startOf('month').subtract(1, 'month').date(day)\n      case 'next':\n        return props.date.startOf('month').add(1, 'month').date(day)\n      case 'current':\n        return props.date.date(day)\n    }\n  }\n\n  const handlePickDay = ({ text, type }: CalendarDateCell) => {\n    const date = getFormattedDate(text, type)\n    emit('pick', date)\n  }\n\n  const getSlotData = ({ text, type }: CalendarDateCell) => {\n    const day = getFormattedDate(text, type)\n    return {\n      isSelected: day.isSame(props.selectedDay),\n      type: `${type}-month`,\n      day: day.format('YYYY-MM-DD'),\n      date: day.toDate(),\n    }\n  }\n\n  return {\n    now,\n    isInRange,\n    rows,\n    weekDays,\n    getFormattedDate,\n    handlePickDay,\n    getSlotData,\n  }\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAOY,IAACA,YAAY,GAAG,SAAfA,YAAYA,CAAIC,KAAK,EAAEC,IAAI,EAAK;EAC3CC,KAAK,CAACC,MAAM,CAACC,UAAU,CAAC;EACxB,IAAMC,cAAc,GAAGH,KAAK,CAACE,UAAU,EAAE,CAACC,cAAc,EAAE;EAC1D,IAAAC,UAAA,GAAoBC,SAAS,EAAE;IAAvBC,CAAC,GAAAF,UAAA,CAADE,CAAC;IAAEC,IAAI,GAAAH,UAAA,CAAJG,IAAI;EACf,IAAMC,GAAG,GAAGR,KAAK,EAAE,CAACS,MAAM,CAACF,IAAI,CAACG,KAAK,CAAC;EACtC,IAAMC,SAAS,GAAGC,QAAQ,CAAC;IAAA,OAAM,CAAC,CAACd,KAAK,CAACe,KAAK,IAAI,CAAC,CAACf,KAAK,CAACe,KAAK,CAACC,MAAM;EAAA,EAAC;EACvE,IAAMC,IAAI,GAAGH,QAAQ,CAAC,YAAM;IAC1B,IAAII,IAAI,GAAG,EAAE;IACb,IAAIL,SAAS,CAACD,KAAK,EAAE;MACnB,IAAAO,YAAA,GAAAC,cAAA,CAAqBpB,KAAK,CAACe,KAAK;QAAzBM,KAAK,GAAAF,YAAA;QAAEG,GAAG,GAAAH,YAAA;MACjB,IAAMI,iBAAiB,GAAGC,QAAQ,CAACF,GAAG,CAACG,IAAI,EAAE,GAAGJ,KAAK,CAACI,IAAI,EAAE,GAAG,CAAC,CAAC,CAACC,GAAG,CAAC,UAACC,KAAK;QAAA,OAAM;UAChFC,IAAI,EAAEP,KAAK,CAACI,IAAI,EAAE,GAAGE,KAAK;UAC1BE,IAAI,EAAE;QACd,CAAO;MAAA,CAAC,CAAC;MACH,IAAIC,SAAS,GAAGP,iBAAiB,CAACP,MAAM,GAAG,CAAC;MAC5Cc,SAAS,GAAGA,SAAS,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC,GAAGA,SAAS;MAC/C,IAAMC,cAAc,GAAGP,QAAQ,CAACM,SAAS,CAAC,CAACJ,GAAG,CAAC,UAACM,CAAC,EAAEL,KAAK;QAAA,OAAM;UAC5DC,IAAI,EAAED,KAAK,GAAG,CAAC;UACfE,IAAI,EAAE;QACd,CAAO;MAAA,CAAC,CAAC;MACHX,IAAI,GAAGK,iBAAiB,CAACU,MAAM,CAACF,cAAc,CAAC;IACrD,CAAK,MAAM;MACL,IAAMG,QAAQ,GAAGlC,KAAK,CAACyB,IAAI,CAACU,OAAO,CAAC,OAAO,CAAC,CAACC,GAAG,EAAE;MAClD,IAAMC,aAAa,GAAGC,oBAAoB,CAACtC,KAAK,CAACyB,IAAI,EAAE,CAACS,QAAQ,GAAG7B,cAAc,GAAG,CAAC,IAAI,CAAC,CAAC,CAACqB,GAAG,CAAC,UAACU,GAAG;QAAA,OAAM;UACxGR,IAAI,EAAEQ,GAAG;UACTP,IAAI,EAAE;QACd,CAAO;MAAA,CAAC,CAAC;MACH,IAAMU,gBAAgB,GAAGC,YAAY,CAACxC,KAAK,CAACyB,IAAI,CAAC,CAACC,GAAG,CAAC,UAACU,GAAG;QAAA,OAAM;UAC9DR,IAAI,EAAEQ,GAAG;UACTP,IAAI,EAAE;QACd,CAAO;MAAA,CAAC,CAAC;MACHX,IAAI,MAAAe,MAAA,CAAAQ,kBAAA,CAAOJ,aAAa,GAAAI,kBAAA,CAAKF,gBAAgB,EAAC;MAC9C,IAAMT,UAAS,GAAG,CAAC,IAAIZ,IAAI,CAACF,MAAM,GAAG,CAAC,IAAI,CAAC,CAAC;MAC5C,IAAM0B,aAAa,GAAGlB,QAAQ,CAACM,UAAS,CAAC,CAACJ,GAAG,CAAC,UAACM,CAAC,EAAEL,KAAK;QAAA,OAAM;UAC3DC,IAAI,EAAED,KAAK,GAAG,CAAC;UACfE,IAAI,EAAE;QACd,CAAO;MAAA,CAAC,CAAC;MACHX,IAAI,GAAGA,IAAI,CAACe,MAAM,CAACS,aAAa,CAAC;IACvC;IACI,OAAOC,WAAW,CAACzB,IAAI,CAAC;EAC5B,CAAG,CAAC;EACF,IAAM0B,QAAQ,GAAG9B,QAAQ,CAAC,YAAM;IAC9B,IAAMO,KAAK,GAAGhB,cAAc;IAC5B,IAAIgB,KAAK,KAAK,CAAC,EAAE;MACf,OAAOwB,SAAS,CAACnB,GAAG,CAAC,UAACM,CAAC;QAAA,OAAKxB,CAAC,CAAC,uBAAuBwB,CAAC,EAAE,CAAC;MAAA,EAAC;IAChE,CAAK,MAAM;MACL,OAAOa,SAAS,CAACC,KAAK,CAACzB,KAAK,CAAC,CAACY,MAAM,CAACY,SAAS,CAACC,KAAK,CAAC,CAAC,EAAEzB,KAAK,CAAC,CAAC,CAACK,GAAG,CAAC,UAACM,CAAC;QAAA,OAAKxB,CAAC,CAAC,uBAAuBwB,CAAC,EAAE,CAAC;MAAA,EAAC;IAC/G;EACA,CAAG,CAAC;EACF,IAAMe,gBAAgB,GAAG,SAAnBA,gBAAgBA,CAAIX,GAAG,EAAEP,IAAI,EAAK;IACtC,QAAQA,IAAI;MACV,KAAK,MAAM;QACT,OAAO7B,KAAK,CAACyB,IAAI,CAACU,OAAO,CAAC,OAAO,CAAC,CAACa,QAAQ,CAAC,CAAC,EAAE,OAAO,CAAC,CAACvB,IAAI,CAACW,GAAG,CAAC;MACnE,KAAK,MAAM;QACT,OAAOpC,KAAK,CAACyB,IAAI,CAACU,OAAO,CAAC,OAAO,CAAC,CAACc,GAAG,CAAC,CAAC,EAAE,OAAO,CAAC,CAACxB,IAAI,CAACW,GAAG,CAAC;MAC9D,KAAK,SAAS;QACZ,OAAOpC,KAAK,CAACyB,IAAI,CAACA,IAAI,CAACW,GAAG,CAAC;IACnC;EACA,CAAG;EACD,IAAMc,aAAa,GAAG,SAAhBA,aAAaA,CAAAC,IAAA,EAAuB;IAAA,IAAjBvB,IAAI,GAAAuB,IAAA,CAAJvB,IAAI;MAAEC,IAAI,GAAAsB,IAAA,CAAJtB,IAAI;IACjC,IAAMJ,IAAI,GAAGsB,gBAAgB,CAACnB,IAAI,EAAEC,IAAI,CAAC;IACzC5B,IAAI,CAAC,MAAM,EAAEwB,IAAI,CAAC;EACtB,CAAG;EACD,IAAM2B,WAAW,GAAG,SAAdA,WAAWA,CAAAC,KAAA,EAAuB;IAAA,IAAjBzB,IAAI,GAAAyB,KAAA,CAAJzB,IAAI;MAAEC,IAAI,GAAAwB,KAAA,CAAJxB,IAAI;IAC/B,IAAMO,GAAG,GAAGW,gBAAgB,CAACnB,IAAI,EAAEC,IAAI,CAAC;IACxC,OAAO;MACLyB,UAAU,EAAElB,GAAG,CAACmB,MAAM,CAACvD,KAAK,CAACwD,WAAW,CAAC;MACzC3B,IAAI,EAAE,GAAGA,IAAI,QAAQ;MACrBO,GAAG,EAAEA,GAAG,CAACqB,MAAM,CAAC,YAAY,CAAC;MAC7BhC,IAAI,EAAEW,GAAG,CAACsB,MAAM;IACtB,CAAK;EACL,CAAG;EACD,OAAO;IACLhD,GAAG;IACHG,SAAS;IACTI,IAAI;IACJ2B,QAAQ;IACRG,gBAAgB;IAChBG,aAAa;IACbE;EACJ,CAAG;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
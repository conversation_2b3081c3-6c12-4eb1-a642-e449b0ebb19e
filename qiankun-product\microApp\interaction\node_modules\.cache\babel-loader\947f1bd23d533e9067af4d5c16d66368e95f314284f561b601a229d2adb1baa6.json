{"ast": null, "code": "import baseSlice from './_baseSlice.js';\n\n/**\n * Casts `array` to a slice if it's needed.\n *\n * @private\n * @param {Array} array The array to inspect.\n * @param {number} start The start position.\n * @param {number} [end=array.length] The end position.\n * @returns {Array} Returns the cast slice.\n */\nfunction castSlice(array, start, end) {\n  var length = array.length;\n  end = end === undefined ? length : end;\n  return !start && end >= length ? array : baseSlice(array, start, end);\n}\nexport default castSlice;", "map": {"version": 3, "names": ["baseSlice", "castSlice", "array", "start", "end", "length", "undefined"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/node_modules/.pnpm/lodash-es@4.17.21/node_modules/lodash-es/_castSlice.js"], "sourcesContent": ["import baseSlice from './_baseSlice.js';\n\n/**\n * Casts `array` to a slice if it's needed.\n *\n * @private\n * @param {Array} array The array to inspect.\n * @param {number} start The start position.\n * @param {number} [end=array.length] The end position.\n * @returns {Array} Returns the cast slice.\n */\nfunction castSlice(array, start, end) {\n  var length = array.length;\n  end = end === undefined ? length : end;\n  return (!start && end >= length) ? array : baseSlice(array, start, end);\n}\n\nexport default castSlice;\n"], "mappings": "AAAA,OAAOA,SAAS,MAAM,iBAAiB;;AAEvC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,SAASA,CAACC,KAAK,EAAEC,KAAK,EAAEC,GAAG,EAAE;EACpC,IAAIC,MAAM,GAAGH,KAAK,CAACG,MAAM;EACzBD,GAAG,GAAGA,GAAG,KAAKE,SAAS,GAAGD,MAAM,GAAGD,GAAG;EACtC,OAAQ,CAACD,KAAK,IAAIC,GAAG,IAAIC,MAAM,GAAIH,KAAK,GAAGF,SAAS,CAACE,KAAK,EAAEC,KAAK,EAAEC,GAAG,CAAC;AACzE;AAEA,eAAeH,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
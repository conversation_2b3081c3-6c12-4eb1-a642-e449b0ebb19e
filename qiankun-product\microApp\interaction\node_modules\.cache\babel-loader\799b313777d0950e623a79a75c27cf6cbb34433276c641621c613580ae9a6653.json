{"ast": null, "code": "function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nfunction _defineProperty(e, r, t) { return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, { value: t, enumerable: !0, configurable: !0, writable: !0 }) : e[r] = t, e; }\nfunction _toPropertyKey(t) { var i = _toPrimitive(t, \"string\"); return \"symbol\" == typeof i ? i : i + \"\"; }\nfunction _toPrimitive(t, r) { if (\"object\" != typeof t || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || \"default\"); if (\"object\" != typeof i) return i; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (\"string\" === r ? String : Number)(t); }\nimport '../../../../utils/index.mjs';\nimport { timePanelSharedProps } from './shared.mjs';\nimport { buildProps, definePropType } from '../../../../utils/vue/props/runtime.mjs';\nvar panelTimeRangeProps = buildProps(_objectSpread(_objectSpread({}, timePanelSharedProps), {}, {\n  parsedValue: {\n    type: definePropType(Array)\n  }\n}));\nexport { panelTimeRangeProps };", "map": {"version": 3, "names": ["panelTimeRangeProps", "buildProps", "_objectSpread", "timePanelSharedProps", "parsedValue", "type", "definePropType", "Array"], "sources": ["../../../../../../../packages/components/time-picker/src/props/panel-time-range.ts"], "sourcesContent": ["import { buildProps, definePropType } from '@element-plus/utils'\nimport { timePanelSharedProps } from './shared'\n\nimport type { ExtractPropTypes } from 'vue'\nimport type { Dayjs } from 'dayjs'\n\nexport const panelTimeRangeProps = buildProps({\n  ...timePanelSharedProps,\n  parsedValue: {\n    type: definePropType<[Dayjs, Dayjs]>(Array),\n  },\n} as const)\n\nexport type PanelTimeRangeProps = ExtractPropTypes<typeof panelTimeRangeProps>\n"], "mappings": ";;;;;;;;AAEY,IAACA,mBAAmB,GAAGC,UAAU,CAAAC,aAAA,CAAAA,aAAA,KACxCC,oBAAoB;EACvBC,WAAW,EAAE;IACXC,IAAI,EAAEC,cAAc,CAACC,KAAK;EAC9B;AAAG,EACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
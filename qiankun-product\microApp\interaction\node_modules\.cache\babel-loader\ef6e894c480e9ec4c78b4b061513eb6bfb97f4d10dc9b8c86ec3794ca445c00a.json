{"ast": null, "code": "import { resolveComponent as _resolveComponent, createVNode as _createVNode, withCtx as _withCtx, renderList as _renderList, Fragment as _Fragment, openBlock as _openBlock, createElementBlock as _createElementBlock, toDisplayString as _toDisplayString, createTextVNode as _createTextVNode, createBlock as _createBlock, vShow as _vShow, withDirectives as _withDirectives, createElementVNode as _createElementVNode, withModifiers as _withModifiers } from \"vue\";\nvar _hoisted_1 = {\n  class: \"ReturnReceiptUserNew\"\n};\nvar _hoisted_2 = {\n  class: \"globalFormButton\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  var _component_input_select_person = _resolveComponent(\"input-select-person\");\n  var _component_el_form_item = _resolveComponent(\"el-form-item\");\n  var _component_xyl_date_picker = _resolveComponent(\"xyl-date-picker\");\n  var _component_el_checkbox = _resolveComponent(\"el-checkbox\");\n  var _component_el_checkbox_group = _resolveComponent(\"el-checkbox-group\");\n  var _component_el_input = _resolveComponent(\"el-input\");\n  var _component_el_button = _resolveComponent(\"el-button\");\n  var _component_el_form = _resolveComponent(\"el-form\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createVNode(_component_el_form, {\n    ref: \"formRef\",\n    model: $setup.form,\n    rules: $setup.rules,\n    inline: \"\",\n    \"label-position\": \"top\",\n    class: \"globalForm\",\n    onSubmit: _cache[6] || (_cache[6] = _withModifiers(function () {}, [\"prevent\"]))\n  }, {\n    default: _withCtx(function () {\n      return [_createVNode(_component_el_form_item, {\n        label: \"回执人\",\n        prop: \"receiveUserId\"\n      }, {\n        default: _withCtx(function () {\n          return [_createVNode(_component_input_select_person, {\n            modelValue: $setup.form.receiveUserId,\n            \"onUpdate:modelValue\": _cache[0] || (_cache[0] = function ($event) {\n              return $setup.form.receiveUserId = $event;\n            }),\n            placeholder: \"请选择回执人\",\n            disabled: \"\"\n          }, null, 8 /* PROPS */, [\"modelValue\"])];\n        }),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_form_item, {\n        label: \"回执时间\",\n        prop: \"replyTime\"\n      }, {\n        default: _withCtx(function () {\n          return [_createVNode(_component_xyl_date_picker, {\n            modelValue: $setup.form.replyTime,\n            \"onUpdate:modelValue\": _cache[1] || (_cache[1] = function ($event) {\n              return $setup.form.replyTime = $event;\n            }),\n            placeholder: \"请选择回执时间\",\n            \"value-format\": \"x\",\n            type: \"datetime\",\n            disabled: \"\"\n          }, null, 8 /* PROPS */, [\"modelValue\"])];\n        }),\n        _: 1 /* STABLE */\n      }), _withDirectives(_createVNode(_component_el_form_item, {\n        label: \"回执选项\",\n        prop: \"content\",\n        class: \"globalFormTitle\"\n      }, {\n        default: _withCtx(function () {\n          return [_createVNode(_component_el_checkbox_group, {\n            modelValue: $setup.form.content,\n            \"onUpdate:modelValue\": _cache[2] || (_cache[2] = function ($event) {\n              return $setup.form.content = $event;\n            })\n          }, {\n            default: _withCtx(function () {\n              return [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.receiptOptions, function (item) {\n                return _openBlock(), _createBlock(_component_el_checkbox, {\n                  key: item,\n                  disabled: $setup.max <= $setup.form.content.length && !$setup.form.content.includes(item),\n                  label: item\n                }, {\n                  default: _withCtx(function () {\n                    return [_createTextVNode(_toDisplayString(item), 1 /* TEXT */)];\n                  }),\n                  _: 2 /* DYNAMIC */\n                }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"disabled\", \"label\"]);\n              }), 128 /* KEYED_FRAGMENT */))];\n            }),\n            _: 1 /* STABLE */\n          }, 8 /* PROPS */, [\"modelValue\"])];\n        }),\n        _: 1 /* STABLE */\n      }, 512 /* NEED_PATCH */), [[_vShow, ['2', '3'].includes($setup.isType)]]), _withDirectives(_createVNode(_component_el_form_item, {\n        label: \"回执备注\",\n        class: \"globalFormTitle\"\n      }, {\n        default: _withCtx(function () {\n          return [_createVNode(_component_el_input, {\n            modelValue: $setup.form.remarks,\n            \"onUpdate:modelValue\": _cache[3] || (_cache[3] = function ($event) {\n              return $setup.form.remarks = $event;\n            }),\n            rows: \"6\",\n            type: \"textarea\",\n            placeholder: \"请输入回执备注\",\n            clearable: \"\"\n          }, null, 8 /* PROPS */, [\"modelValue\"])];\n        }),\n        _: 1 /* STABLE */\n      }, 512 /* NEED_PATCH */), [[_vShow, ['2', '3'].includes($setup.isType)]]), _withDirectives(_createVNode(_component_el_form_item, {\n        label: \"回执内容\",\n        class: \"globalFormTitle\"\n      }, {\n        default: _withCtx(function () {\n          return [_createVNode(_component_el_input, {\n            modelValue: $setup.form.textContent,\n            \"onUpdate:modelValue\": _cache[4] || (_cache[4] = function ($event) {\n              return $setup.form.textContent = $event;\n            }),\n            rows: \"6\",\n            type: \"textarea\",\n            placeholder: \"请输入回执内容\",\n            clearable: \"\"\n          }, null, 8 /* PROPS */, [\"modelValue\"])];\n        }),\n        _: 1 /* STABLE */\n      }, 512 /* NEED_PATCH */), [[_vShow, ['1'].includes($setup.isType)]]), _createElementVNode(\"div\", _hoisted_2, [_createVNode(_component_el_button, {\n        type: \"primary\",\n        onClick: _cache[5] || (_cache[5] = function ($event) {\n          return $setup.submitForm($setup.formRef);\n        })\n      }, {\n        default: _withCtx(function () {\n          return _cache[7] || (_cache[7] = [_createTextVNode(\"提交\")]);\n        }),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_button, {\n        onClick: $setup.resetForm\n      }, {\n        default: _withCtx(function () {\n          return _cache[8] || (_cache[8] = [_createTextVNode(\"取消\")]);\n        }),\n        _: 1 /* STABLE */\n      })])];\n    }),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"model\", \"rules\"])]);\n}", "map": {"version": 3, "names": ["class", "_createElementBlock", "_hoisted_1", "_createVNode", "_component_el_form", "ref", "model", "$setup", "form", "rules", "inline", "onSubmit", "_cache", "_withModifiers", "default", "_withCtx", "_component_el_form_item", "label", "prop", "_component_input_select_person", "modelValue", "receiveUserId", "$event", "placeholder", "disabled", "_", "_component_xyl_date_picker", "replyTime", "type", "_component_el_checkbox_group", "content", "_Fragment", "_renderList", "receiptOptions", "item", "_createBlock", "_component_el_checkbox", "key", "max", "length", "includes", "_createTextVNode", "_toDisplayString", "isType", "_component_el_input", "remarks", "rows", "clearable", "textContent", "_createElementVNode", "_hoisted_2", "_component_el_button", "onClick", "submitForm", "formRef", "resetForm"], "sources": ["D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\interaction\\src\\views\\NoticeAnnouncement\\component\\ReturnReceiptUserNew.vue"], "sourcesContent": ["<template>\r\n  <div class=\"ReturnReceiptUserNew\">\r\n    <el-form ref=\"formRef\" :model=\"form\" :rules=\"rules\" inline label-position=\"top\" class=\"globalForm\"\r\n      @submit.enter.prevent>\r\n      <el-form-item label=\"回执人\" prop=\"receiveUserId\">\r\n        <input-select-person v-model=\"form.receiveUserId\" placeholder=\"请选择回执人\" disabled />\r\n      </el-form-item>\r\n      <el-form-item label=\"回执时间\" prop=\"replyTime\">\r\n        <xyl-date-picker v-model=\"form.replyTime\" placeholder=\"请选择回执时间\" value-format=\"x\" type=\"datetime\" disabled />\r\n      </el-form-item>\r\n      <el-form-item label=\"回执选项\" prop=\"content\" v-show=\"['2', '3'].includes(isType)\" class=\"globalFormTitle\">\r\n        <el-checkbox-group v-model=\"form.content\">\r\n          <el-checkbox v-for=\"item in receiptOptions\" :key=\"item\"\r\n            :disabled=\"max <= form.content.length && !form.content.includes(item)\" :label=\"item\">{{ item\r\n            }}</el-checkbox>\r\n        </el-checkbox-group>\r\n      </el-form-item>\r\n      <el-form-item label=\"回执备注\" v-show=\"['2', '3'].includes(isType)\" class=\"globalFormTitle\">\r\n        <el-input v-model=\"form.remarks\" rows=\"6\" type=\"textarea\" placeholder=\"请输入回执备注\" clearable />\r\n      </el-form-item>\r\n      <el-form-item label=\"回执内容\" v-show=\"['1'].includes(isType)\" class=\"globalFormTitle\">\r\n        <el-input v-model=\"form.textContent\" rows=\"6\" type=\"textarea\" placeholder=\"请输入回执内容\" clearable />\r\n      </el-form-item>\r\n      <div class=\"globalFormButton\">\r\n        <el-button type=\"primary\" @click=\"submitForm(formRef)\">提交</el-button>\r\n        <el-button @click=\"resetForm\">取消</el-button>\r\n      </div>\r\n    </el-form>\r\n  </div>\r\n</template>\r\n<script>\r\nexport default { name: 'ReturnReceiptUserNew' }\r\n</script>\r\n<script setup>\r\nimport api from '@/api'\r\nimport { reactive, ref, onMounted } from 'vue'\r\nimport { ElMessage } from 'element-plus'\r\nconst props = defineProps({ id: { type: String, default: '' }, uid: { type: String, default: '' } })\r\nconst emit = defineEmits(['callback'])\r\n\r\nconst formRef = ref()\r\nconst form = reactive({\r\n  receiveUserId: '',\r\n  replyTime: '',\r\n  content: [],\r\n  textContent: '',\r\n  remarks: ''\r\n})\r\nconst rules = reactive({\r\n  receiveUserId: [{ required: true, message: '请选择回执时间', trigger: ['blur', 'change'] }],\r\n  replyTime: [{ required: true, message: '请选择回执时间', trigger: ['blur', 'change'] }],\r\n  content: [{ type: 'array', required: true, message: '请选择回执选项', trigger: ['blur', 'change'] }],\r\n  textContent: [{ required: true, message: '请选择回执内容', trigger: ['blur', 'change'] }]\r\n})\r\nconst receiptOptions = ref([])\r\nconst max = ref(0)\r\nconst isType = ref('')\r\n\r\nonMounted(() => {\r\n  if (props.uid) { NoticeAnnouncementInfo() }\r\n})\r\n\r\nconst NoticeAnnouncementInfo = async () => {\r\n  const res = await api.NoticeAnnouncementInfo({ detailId: props.uid })\r\n  var { data } = res\r\n  receiptOptions.value = data.receiptOptions\r\n  isType.value = data.receipt.receiptOptionType.value\r\n  if (isType.value === '1') {\r\n    max.value = data.receiptOptions.length\r\n  } else if (isType.value === '2') {\r\n    max.value = 1\r\n  } else if (isType.value === '3') {\r\n    max.value = data.receiptOptions.length\r\n  }\r\n  if (props.id) { ReturnReceiptInfo() }\r\n}\r\nconst ReturnReceiptInfo = async () => {\r\n  const res = await api.ReturnReceiptInfo({ detailId: props.id })\r\n  var { data } = res\r\n  form.receiveUserId = data.receiveUserId\r\n  form.replyTime = data.replyTime\r\n  if (isType.value === '1') {\r\n    form.content = ['1']\r\n    form.textContent = data.content\r\n  } else if (isType.value === '2') {\r\n    form.textContent = '1'\r\n    form.content = data.content.split(',')\r\n    form.remarks = data.remarks\r\n  } else if (isType.value === '3') {\r\n    form.textContent = '1'\r\n    form.content = data.content.split(',')\r\n    form.remarks = data.remarks\r\n  }\r\n}\r\nconst submitForm = async (formEl) => {\r\n  if (!formEl) return\r\n  await formEl.validate((valid, fields) => {\r\n    if (valid) { globalJson() } else { ElMessage({ type: 'warning', message: '请根据提示信息完善字段内容！' }) }\r\n  })\r\n}\r\nconst globalJson = async () => {\r\n  const { code } = await api.globalJson('/notificationReceiver/edit', {\r\n    form: {\r\n      id: props.id,\r\n      notificationId: props.uid,\r\n      content: isType.value === '1' ? form.textContent : form.content.join(','),\r\n      remarks: form.remarks\r\n    }\r\n  })\r\n  if (code === 200) {\r\n    ElMessage({ type: 'success', message: '编辑成功' })\r\n    emit('callback')\r\n  }\r\n}\r\nconst resetForm = () => { emit('callback') }\r\n</script>\r\n<style lang=\"scss\">\r\n.ReturnReceiptUserNew {\r\n  width: 680px;\r\n}\r\n</style>\r\n"], "mappings": ";;EACOA,KAAK,EAAC;AAAsB;;EAsBxBA,KAAK,EAAC;AAAkB;;;;;;;;;;uBAtBjCC,mBAAA,CA2BM,OA3BNC,UA2BM,GA1BJC,YAAA,CAyBUC,kBAAA;IAzBDC,GAAG,EAAC,SAAS;IAAEC,KAAK,EAAEC,MAAA,CAAAC,IAAI;IAAGC,KAAK,EAAEF,MAAA,CAAAE,KAAK;IAAEC,MAAM,EAAN,EAAM;IAAC,gBAAc,EAAC,KAAK;IAACV,KAAK,EAAC,YAAY;IAC/FW,QAAM,EAAAC,MAAA,QAAAA,MAAA,MAHbC,cAAA,CAGM,cAAqB;;IAH3BC,OAAA,EAAAC,QAAA,CAIM;MAAA,OAEe,CAFfZ,YAAA,CAEea,uBAAA;QAFDC,KAAK,EAAC,KAAK;QAACC,IAAI,EAAC;;QAJrCJ,OAAA,EAAAC,QAAA,CAKQ;UAAA,OAAkF,CAAlFZ,YAAA,CAAkFgB,8BAAA;YAL1FC,UAAA,EAKsCb,MAAA,CAAAC,IAAI,CAACa,aAAa;YALxD,uBAAAT,MAAA,QAAAA,MAAA,gBAAAU,MAAA;cAAA,OAKsCf,MAAA,CAAAC,IAAI,CAACa,aAAa,GAAAC,MAAA;YAAA;YAAEC,WAAW,EAAC,QAAQ;YAACC,QAAQ,EAAR;;;QAL/EC,CAAA;UAOMtB,YAAA,CAEea,uBAAA;QAFDC,KAAK,EAAC,MAAM;QAACC,IAAI,EAAC;;QAPtCJ,OAAA,EAAAC,QAAA,CAQQ;UAAA,OAA4G,CAA5GZ,YAAA,CAA4GuB,0BAAA;YARpHN,UAAA,EAQkCb,MAAA,CAAAC,IAAI,CAACmB,SAAS;YARhD,uBAAAf,MAAA,QAAAA,MAAA,gBAAAU,MAAA;cAAA,OAQkCf,MAAA,CAAAC,IAAI,CAACmB,SAAS,GAAAL,MAAA;YAAA;YAAEC,WAAW,EAAC,SAAS;YAAC,cAAY,EAAC,GAAG;YAACK,IAAI,EAAC,UAAU;YAACJ,QAAQ,EAAR;;;QARzGC,CAAA;0BAUMtB,YAAA,CAMea,uBAAA;QANDC,KAAK,EAAC,MAAM;QAACC,IAAI,EAAC,SAAS;QAAsClB,KAAK,EAAC;;QAV3Fc,OAAA,EAAAC,QAAA,CAWQ;UAAA,OAIoB,CAJpBZ,YAAA,CAIoB0B,4BAAA;YAf5BT,UAAA,EAWoCb,MAAA,CAAAC,IAAI,CAACsB,OAAO;YAXhD,uBAAAlB,MAAA,QAAAA,MAAA,gBAAAU,MAAA;cAAA,OAWoCf,MAAA,CAAAC,IAAI,CAACsB,OAAO,GAAAR,MAAA;YAAA;;YAXhDR,OAAA,EAAAC,QAAA,CAYuB;cAAA,OAA8B,E,kBAA3Cd,mBAAA,CAEkB8B,SAAA,QAd5BC,WAAA,CAYsCzB,MAAA,CAAA0B,cAAc,EAZpD,UAY8BC,IAAI;qCAAxBC,YAAA,CAEkBC,sBAAA;kBAF2BC,GAAG,EAAEH,IAAI;kBACnDV,QAAQ,EAAEjB,MAAA,CAAA+B,GAAG,IAAI/B,MAAA,CAAAC,IAAI,CAACsB,OAAO,CAACS,MAAM,KAAKhC,MAAA,CAAAC,IAAI,CAACsB,OAAO,CAACU,QAAQ,CAACN,IAAI;kBAAIjB,KAAK,EAAEiB;;kBAb3FpB,OAAA,EAAAC,QAAA,CAaiG;oBAAA,OACnF,CAdd0B,gBAAA,CAAAC,gBAAA,CAaoGR,IAAI,iB;;kBAbxGT,CAAA;;;;YAAAA,CAAA;;;QAAAA,CAAA;qDAUmEe,QAAQ,CAACjC,MAAA,CAAAoC,MAAM,G,mBAO5ExC,YAAA,CAEea,uBAAA;QAFDC,KAAK,EAAC,MAAM;QAAsCjB,KAAK,EAAC;;QAjB5Ec,OAAA,EAAAC,QAAA,CAkBQ;UAAA,OAA4F,CAA5FZ,YAAA,CAA4FyC,mBAAA;YAlBpGxB,UAAA,EAkB2Bb,MAAA,CAAAC,IAAI,CAACqC,OAAO;YAlBvC,uBAAAjC,MAAA,QAAAA,MAAA,gBAAAU,MAAA;cAAA,OAkB2Bf,MAAA,CAAAC,IAAI,CAACqC,OAAO,GAAAvB,MAAA;YAAA;YAAEwB,IAAI,EAAC,GAAG;YAAClB,IAAI,EAAC,UAAU;YAACL,WAAW,EAAC,SAAS;YAACwB,SAAS,EAAT;;;QAlBxFtB,CAAA;qDAiBoDe,QAAQ,CAACjC,MAAA,CAAAoC,MAAM,G,mBAG7DxC,YAAA,CAEea,uBAAA;QAFDC,KAAK,EAAC,MAAM;QAAiCjB,KAAK,EAAC;;QApBvEc,OAAA,EAAAC,QAAA,CAqBQ;UAAA,OAAgG,CAAhGZ,YAAA,CAAgGyC,mBAAA;YArBxGxB,UAAA,EAqB2Bb,MAAA,CAAAC,IAAI,CAACwC,WAAW;YArB3C,uBAAApC,MAAA,QAAAA,MAAA,gBAAAU,MAAA;cAAA,OAqB2Bf,MAAA,CAAAC,IAAI,CAACwC,WAAW,GAAA1B,MAAA;YAAA;YAAEwB,IAAI,EAAC,GAAG;YAAClB,IAAI,EAAC,UAAU;YAACL,WAAW,EAAC,SAAS;YAACwB,SAAS,EAAT;;;QArB5FtB,CAAA;gDAoB+Ce,QAAQ,CAACjC,MAAA,CAAAoC,MAAM,G,GAGxDM,mBAAA,CAGM,OAHNC,UAGM,GAFJ/C,YAAA,CAAqEgD,oBAAA;QAA1DvB,IAAI,EAAC,SAAS;QAAEwB,OAAK,EAAAxC,MAAA,QAAAA,MAAA,gBAAAU,MAAA;UAAA,OAAEf,MAAA,CAAA8C,UAAU,CAAC9C,MAAA,CAAA+C,OAAO;QAAA;;QAxB5DxC,OAAA,EAAAC,QAAA,CAwB+D;UAAA,OAAEH,MAAA,QAAAA,MAAA,OAxBjE6B,gBAAA,CAwB+D,IAAE,E;;QAxBjEhB,CAAA;UAyBQtB,YAAA,CAA4CgD,oBAAA;QAAhCC,OAAK,EAAE7C,MAAA,CAAAgD;MAAS;QAzBpCzC,OAAA,EAAAC,QAAA,CAyBsC;UAAA,OAAEH,MAAA,QAAAA,MAAA,OAzBxC6B,gBAAA,CAyBsC,IAAE,E;;QAzBxChB,CAAA;;;IAAAA,CAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
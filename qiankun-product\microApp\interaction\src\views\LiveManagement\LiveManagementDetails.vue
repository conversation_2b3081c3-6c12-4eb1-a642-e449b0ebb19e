<template>
  <div class="LiveManagementDetails">
    <anchor-location>
      <div class="VideoMeetinDetailsTitle">{{ details?.theme }}</div>
      <div class="VideoMeetinDetailsTime">
        <div class="VideoMeetinDetailsTimeLeft">
          <div class="VideoMeetinDetailsTimeHours">{{ format(details.startTime, 'HH:mm') }}</div>
          <div class="VideoMeetinDetailsTimeDate">{{ format(details.startTime, 'YYYY年MM月DD日') }}</div>
        </div>
        <div class="VideoMeetinDetailsTimeCenter">
          <div class="VideoMeetinDetailsDuration">{{ details.during }}分钟</div>
          <!-- <div class="VideoMeetinDetailsStatus">{{ details.meetingStatus }}</div> -->
        </div>
        <div class="VideoMeetinDetailsTimeRight">
          <div class="VideoMeetinDetailsTimeHours">{{ format(details.endTime, 'HH:mm') }}</div>
          <div class="VideoMeetinDetailsTimeDate">{{ format(details.endTime, 'YYYY年MM月DD日') }}</div>
        </div>
      </div>
      <div class="LiveList">
        <div class="LiveName">直播简介</div>
        <div class="LiveContent">{{ details.liveDescribes }}</div>
      </div>
      <div class="LiveList" v-if="details.isReplay == 1 && details.liveReplayUrl">
        <div class="LiveName">直播回放</div>
        <div class="player-container">
          <video ref="videoPlayer" id="video-player" :src="details.liveReplayUrl" controls></video>
        </div>
      </div>
      <div class="LiveList">
        <div class="LiveInteractionBox">
          <div class="LiveName" style="margin-top: 0;">直播互动</div>
          <div class="LiveInteraction">
            <div class="LiveInteractionTotal">(共{{ totals }}条互动消息)</div>
            <el-button type="primary" @click="excelMsg()" v-if="false">导出互动消息</el-button>
          </div>
        </div>
        <el-scrollbar class="LiveCommentList">
          <div class="comment-container" v-if="commentList.length">
            <div class="comment-item" v-for="item in commentList" :key="item.id">
              <!-- 主评论 -->
              <div class="comment-main">
                <div class="comment-avatar">
                  <el-image :src="getAvatarUrl(item.headImg)" fit="cover" />
                </div>
                <div class="comment-content">
                  <div class="comment-header">
                    <span class="comment-username">{{ item.commentUserName }}</span>
                    <!-- <span class="comment-role" v-if="item.roles && item.roles.length">（{{ item.roles[0].roleName
                    }}）</span> -->
                  </div>
                  <div class="comment-text">{{ item.commentContent }}</div>
                  <div class="comment-footer">
                    <span class="comment-time">{{ formatTime(item.createDate) }}</span>
                    <span class="action-btn delete-btn" @click="deleteComment(item)">删除</span>
                    <span class="action-btn like-btn">
                      <el-image :src="getLikeIcon(item.hasClickPraises)" class="like-icon" />
                      <span>({{ item.praisesCount || 20 }})</span>
                    </span>
                  </div>
                </div>
              </div>

              <!-- 回复列表 -->
              <div class="reply-list" v-if="item.children && item.children.length">
                <div class="reply-item" v-for="reply in item.children" :key="reply.id">
                  <div class="reply-avatar">
                    <el-image :src="getAvatarUrl(reply.headImg)" fit="cover" />
                  </div>
                  <div class="reply-content">
                    <div class="reply-header">
                      <span class="reply-username">{{ reply.commentUserName }}</span>
                      <span class="reply-role" v-if="reply.roles && reply.roles.length">（{{ reply.roles[0].roleName
                      }}）</span>
                    </div>
                    <div class="reply-text">
                      <span v-if="reply.toCommenter && reply.parentId !== item.id">回复{{ reply.toCommenter }}：</span>
                      {{ reply.commentContent }}
                    </div>
                    <div class="reply-footer">
                      <span class="reply-time">{{ formatTime(reply.createDate) }}</span>
                      <span class="action-btn delete-btn" @click="deleteComment(reply)">删除</span>
                      <span class="action-btn like-btn">
                        <el-image :src="getLikeIcon(reply.hasClickPraises)" class="like-icon" />
                        <span>({{ reply.praisesCount || 20 }})</span>
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="no-comments" v-else>
            暂无互动消息
          </div>
        </el-scrollbar>
        <div class="globalPagination">
          <el-pagination v-model:currentPage="pageNo" v-model:page-size="pageSize" :page-sizes="[10, 20, 50, 100, 200]"
            layout="total, sizes, prev, pager, next, jumper" @size-change="handleQuery" @current-change="handleQuery"
            :total="totals" background />
        </div>
      </div>
    </anchor-location>
  </div>
</template>
<script>
export default { name: 'LiveManagementDetails' }
</script>
<script setup>
import api from '@/api'
import { ref, onMounted } from 'vue'
import { useRoute } from 'vue-router'
import { format } from 'common/js/time.js'
import { ElMessage, ElMessageBox } from 'element-plus'

const route = useRoute()
const details = ref({})
const pageNo = ref(1)
const pageSize = ref(10)
const totals = ref(0)
const commentList = ref([])

onMounted(() => {
  if (route.query.id) { videoConnectionInfo() }
})

const videoConnectionInfo = async () => {
  const res = await api.videoConnectionInfo({ detailId: route.query.id })
  var { data } = res
  details.value = data
  handleQuery()
}

const excelMsg = () => {
  console.log('导出互动消息')
}

const handleQuery = () => {
  twoLevelTree()
}

const twoLevelTree = async () => {
  const { data, total } = await api.twoLevelTree({
    businessCode: 'liveBroadcast',
    businessId: route.query.id,
    pageNo: pageNo.value,
    pageSize: pageSize.value,
    isAscByTime: 1
  })
  commentList.value = data
  totals.value = total
}

// 获取头像URL
const getAvatarUrl = (url) => {
  return url ? api.fileURL(url) : api.defaultImgURL('default_user_head.jpg')
}

// 格式化时间
const formatTime = (time) => {
  return format(time, 'YYYY-MM-DD HH:mm')
}

// 获取点赞图标
const getLikeIcon = (isLiked) => {
  return isLiked ? require('../../assets/img/fabulous_o.png') : require('../../assets/img/fabulous.png')
}

// 删除功能
const deleteComment = (item) => {
  ElMessageBox.confirm(`此操作将删除当前选中的评论, 是否继续?`, '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  })
    .then(() => {
      commentDel(item.id)
    })
    .catch(() => {
      ElMessage({ type: 'info', message: '已取消删除' })
    })
}

// 删除评论API调用
const commentDel = async (id) => {
  try {
    const { code } = await api.commentDel({ ids: [id] })
    if (code === 200) {
      ElMessage({ type: 'success', message: '删除成功' })
      // 重新获取评论列表
      handleQuery()
    }
  } catch (error) {
    ElMessage({ type: 'error', message: '删除失败' })
  }
}
</script>
<style lang="scss">
.LiveManagementDetails {
  width: 100%;
  height: 100%;
  padding: 0 20px;

  .anchor-location-body {
    padding: var(--zy-distance-one);
  }

  .VideoMeetinDetailsTitle {
    text-align: center;
    font-weight: bold;
    font-size: var(--zy-title-font-size);
    padding: var(--zy-distance-two) var(--zy-distance-one);
    line-height: var(--zy-line-height);
  }

  .VideoMeetinDetailsTime {
    display: flex;
    align-items: center;
    justify-content: space-evenly;
    padding: var(--zy-distance-two) 0;

    .VideoMeetinDetailsTimeLeft,
    .VideoMeetinDetailsTimeRight {
      text-align: center;
      padding: 0 var(--zy-distance-one);
    }

    .VideoMeetinDetailsTimeCenter {
      width: 99px;
      height: 99px;
      display: flex;
      align-items: center;
      flex-direction: column;
      justify-content: center;
      border-radius: 50%;
      border: 1px solid var(--zy-el-color-primary);
      background: var(--zy-el-color-primary-light-9);
      position: relative;

      &::after {
        content: '';
        position: absolute;
        width: 99px;
        height: 1px;
        background: linear-gradient(90deg, var(--zy-el-color-primary), #fff);
        top: 50%;
        right: 0;
        transform: translate(100%, -50%);
      }

      &::before {
        content: '';
        position: absolute;
        width: 99px;
        height: 1px;
        background: linear-gradient(90deg, #fff, var(--zy-el-color-primary));
        top: 50%;
        left: 0;
        transform: translate(-100%, -50%);
      }

      .VideoMeetinDetailsDuration {
        font-weight: bold;
        color: var(--zy-el-color-primary);
        font-size: calc(var(--zy-name-font-size) + 2px);
      }

      .VideoMeetinDetailsStatus {
        color: var(--zy-el-color-primary);
        font-size: var(--zy-name-font-size);
      }
    }

    .VideoMeetinDetailsTimeHours {
      font-weight: bold;
      font-size: calc(var(--zy-name-font-size) + 6px);
    }

    .VideoMeetinDetailsTimeDate {
      font-size: var(--zy-name-font-size);
    }
  }

  .LiveList {
    width: 100%;
    // display: flex;
    // flex-wrap: wrap;
    padding: 0 var(--zy-distance-one);

    .LiveName {
      font-weight: bold;
      font-size: 20px;
      line-height: var(--zy-line-height);
      margin-top: 10px;
    }

    .LiveContent {
      font-size: 16px;
      line-height: var(--zy-line-height);
      text-indent: 2em;
      margin-top: 5px
    }

    .player-container {
      width: 100%;
      height: 100%;
      margin-top: 5px;

      #video-player {
        width: 100%;
        height: 100%;
      }
    }

    .LiveInteractionBox {
      display: flex;
      align-items: center;
      margin-top: 10px;
      justify-content: space-between;
      width: 100%;
    }

    .LiveInteraction {
      display: flex;
      align-items: center;

      .LiveInteractionTotal {
        margin-right: 10px;
      }
    }

    .LiveCommentList {
      height: 600px;
      margin-top: 10px;
      border: 1px solid var(--zy-el-border-color-lighter);

      .comment-container {
        width: 100%;
        padding: 0 var(--zy-distance-two);
      }

      .comment-item {
        width: 100%;
        padding: 20px 0 10px 0;
        border-bottom: 1px solid var(--zy-el-border-color-lighter);

        &:last-child {
          border-bottom: none;
        }
      }

      .comment-main {
        display: flex;
        align-items: flex-start;
        gap: 12px;
      }

      .comment-avatar {
        flex-shrink: 0;

        .zy-el-image {
          width: 40px;
          height: 40px;
          border-radius: 50%;
          overflow: hidden;
        }
      }

      .comment-content {
        flex: 1;
      }

      .comment-header {
        display: flex;
        align-items: center;
        margin-bottom: 8px;
      }

      .comment-username {
        font-weight: bold;
        font-size: 14px;
        color: #333;
      }

      .comment-role {
        font-size: 12px;
        color: #666;
        margin-left: 4px;
      }

      .comment-text {
        font-size: 14px;
        line-height: 1.5;
        color: #333;
        margin-bottom: 8px;
      }

      .comment-footer {
        display: flex;
        align-items: center;
        gap: 16px;
      }

      .comment-time {
        font-size: 12px;
        color: #999;
      }

      .action-btn {
        display: flex;
        align-items: center;
        gap: 4px;
        cursor: pointer;
        font-size: 12px;
        color: #666;
        transition: color 0.3s;

        &:hover {
          color: #409eff;
        }

        &.delete-btn {
          color: #f56c6c;

          &:hover {
            color: #f78989;
          }
        }

        &.like-btn {
          cursor: default;

          .like-icon {
            width: 16px;
            height: 16px;
          }
        }
      }

      // 回复样式
      .reply-list {
        margin-top: 12px;
        margin-left: 52px;
        background-color: var(--zy-el-color-info-light-9);
        border-radius: 8px;
        padding: 12px;
        border-left: 3px solid var(--zy-el-color-primary-light-5);
      }

      .reply-item {
        display: flex;
        align-items: flex-start;
        gap: 8px;
        margin-bottom: 12px;

        &:last-child {
          margin-bottom: 0;
        }
      }

      .reply-avatar {
        flex-shrink: 0;

        .zy-el-image {
          width: 28px;
          height: 28px;
          border-radius: 50%;
          overflow: hidden;
        }
      }

      .reply-content {
        flex: 1;
      }

      .reply-header {
        display: flex;
        align-items: center;
        margin-bottom: 4px;
      }

      .reply-username {
        font-weight: bold;
        font-size: 13px;
        color: #333;
      }

      .reply-role {
        font-size: 11px;
        color: #666;
        margin-left: 4px;
      }

      .reply-text {
        font-size: 13px;
        line-height: 1.4;
        color: #333;
        margin-bottom: 6px;
      }

      .reply-footer {
        display: flex;
        align-items: center;
        gap: 12px;
      }

      .reply-time {
        font-size: 12px;
        color: #999;
      }

      .no-comments {
        text-align: center;
        color: #999;
        padding: 40px 0;
        font-size: 14px;
      }
    }
  }
}
</style>

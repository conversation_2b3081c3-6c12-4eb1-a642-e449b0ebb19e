{"ast": null, "code": "import assignValue from './_assignValue.js';\nimport baseAssignValue from './_baseAssignValue.js';\n\n/**\n * Copies properties of `source` to `object`.\n *\n * @private\n * @param {Object} source The object to copy properties from.\n * @param {Array} props The property identifiers to copy.\n * @param {Object} [object={}] The object to copy properties to.\n * @param {Function} [customizer] The function to customize copied values.\n * @returns {Object} Returns `object`.\n */\nfunction copyObject(source, props, object, customizer) {\n  var isNew = !object;\n  object || (object = {});\n  var index = -1,\n    length = props.length;\n  while (++index < length) {\n    var key = props[index];\n    var newValue = customizer ? customizer(object[key], source[key], key, object, source) : undefined;\n    if (newValue === undefined) {\n      newValue = source[key];\n    }\n    if (isNew) {\n      baseAssignValue(object, key, newValue);\n    } else {\n      assignValue(object, key, newValue);\n    }\n  }\n  return object;\n}\nexport default copyObject;", "map": {"version": 3, "names": ["assignValue", "baseAssignValue", "copyObject", "source", "props", "object", "customizer", "isNew", "index", "length", "key", "newValue", "undefined"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/node_modules/.pnpm/lodash-es@4.17.21/node_modules/lodash-es/_copyObject.js"], "sourcesContent": ["import assignValue from './_assignValue.js';\nimport baseAssignValue from './_baseAssignValue.js';\n\n/**\n * Copies properties of `source` to `object`.\n *\n * @private\n * @param {Object} source The object to copy properties from.\n * @param {Array} props The property identifiers to copy.\n * @param {Object} [object={}] The object to copy properties to.\n * @param {Function} [customizer] The function to customize copied values.\n * @returns {Object} Returns `object`.\n */\nfunction copyObject(source, props, object, customizer) {\n  var isNew = !object;\n  object || (object = {});\n\n  var index = -1,\n      length = props.length;\n\n  while (++index < length) {\n    var key = props[index];\n\n    var newValue = customizer\n      ? customizer(object[key], source[key], key, object, source)\n      : undefined;\n\n    if (newValue === undefined) {\n      newValue = source[key];\n    }\n    if (isNew) {\n      baseAssignValue(object, key, newValue);\n    } else {\n      assignValue(object, key, newValue);\n    }\n  }\n  return object;\n}\n\nexport default copyObject;\n"], "mappings": "AAAA,OAAOA,WAAW,MAAM,mBAAmB;AAC3C,OAAOC,eAAe,MAAM,uBAAuB;;AAEnD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,UAAUA,CAACC,MAAM,EAAEC,KAAK,EAAEC,MAAM,EAAEC,UAAU,EAAE;EACrD,IAAIC,KAAK,GAAG,CAACF,MAAM;EACnBA,MAAM,KAAKA,MAAM,GAAG,CAAC,CAAC,CAAC;EAEvB,IAAIG,KAAK,GAAG,CAAC,CAAC;IACVC,MAAM,GAAGL,KAAK,CAACK,MAAM;EAEzB,OAAO,EAAED,KAAK,GAAGC,MAAM,EAAE;IACvB,IAAIC,GAAG,GAAGN,KAAK,CAACI,KAAK,CAAC;IAEtB,IAAIG,QAAQ,GAAGL,UAAU,GACrBA,UAAU,CAACD,MAAM,CAACK,GAAG,CAAC,EAAEP,MAAM,CAACO,GAAG,CAAC,EAAEA,GAAG,EAAEL,MAAM,EAAEF,MAAM,CAAC,GACzDS,SAAS;IAEb,IAAID,QAAQ,KAAKC,SAAS,EAAE;MAC1BD,QAAQ,GAAGR,MAAM,CAACO,GAAG,CAAC;IACxB;IACA,IAAIH,KAAK,EAAE;MACTN,eAAe,CAACI,MAAM,EAAEK,GAAG,EAAEC,QAAQ,CAAC;IACxC,CAAC,MAAM;MACLX,WAAW,CAACK,MAAM,EAAEK,GAAG,EAAEC,QAAQ,CAAC;IACpC;EACF;EACA,OAAON,MAAM;AACf;AAEA,eAAeH,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
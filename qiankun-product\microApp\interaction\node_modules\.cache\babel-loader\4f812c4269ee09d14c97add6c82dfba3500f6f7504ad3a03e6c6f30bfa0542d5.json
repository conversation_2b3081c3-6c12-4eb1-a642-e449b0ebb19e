{"ast": null, "code": "import { getCurrentInstance, inject, ref } from 'vue';\nimport '../../../../utils/index.mjs';\nimport { TABLE_INJECTION_KEY } from '../tokens.mjs';\nimport { isClient } from '@vueuse/core';\nimport { addClass, removeClass, hasClass } from '../../../../utils/dom/style.mjs';\nimport { isElement } from '../../../../utils/types.mjs';\nfunction useEvent(props, emit) {\n  var instance = getCurrentInstance();\n  var parent = inject(TABLE_INJECTION_KEY);\n  var handleFilterClick = function handleFilterClick(event) {\n    event.stopPropagation();\n    return;\n  };\n  var handleHeaderClick = function handleHeaderClick(event, column) {\n    if (!column.filters && column.sortable) {\n      handleSortClick(event, column, false);\n    } else if (column.filterable && !column.sortable) {\n      handleFilterClick(event);\n    }\n    parent == null ? void 0 : parent.emit(\"header-click\", column, event);\n  };\n  var handleHeaderContextMenu = function handleHeaderContextMenu(event, column) {\n    parent == null ? void 0 : parent.emit(\"header-contextmenu\", column, event);\n  };\n  var draggingColumn = ref(null);\n  var dragging = ref(false);\n  var dragState = ref({});\n  var handleMouseDown = function handleMouseDown(event, column) {\n    if (!isClient) return;\n    if (column.children && column.children.length > 0) return;\n    if (draggingColumn.value && props.border) {\n      dragging.value = true;\n      var table = parent;\n      emit(\"set-drag-visible\", true);\n      var tableEl = table == null ? void 0 : table.vnode.el;\n      var tableLeft = tableEl.getBoundingClientRect().left;\n      var columnEl = instance.vnode.el.querySelector(`th.${column.id}`);\n      var columnRect = columnEl.getBoundingClientRect();\n      var minLeft = columnRect.left - tableLeft + 30;\n      addClass(columnEl, \"noclick\");\n      dragState.value = {\n        startMouseLeft: event.clientX,\n        startLeft: columnRect.right - tableLeft,\n        startColumnLeft: columnRect.left - tableLeft,\n        tableLeft\n      };\n      var resizeProxy = table == null ? void 0 : table.refs.resizeProxy;\n      resizeProxy.style.left = `${dragState.value.startLeft}px`;\n      document.onselectstart = function () {\n        return false;\n      };\n      document.ondragstart = function () {\n        return false;\n      };\n      var handleMouseMove2 = function handleMouseMove2(event2) {\n        var deltaLeft = event2.clientX - dragState.value.startMouseLeft;\n        var proxyLeft = dragState.value.startLeft + deltaLeft;\n        resizeProxy.style.left = `${Math.max(minLeft, proxyLeft)}px`;\n      };\n      var _handleMouseUp = function handleMouseUp() {\n        if (dragging.value) {\n          var _dragState$value = dragState.value,\n            startColumnLeft = _dragState$value.startColumnLeft,\n            startLeft = _dragState$value.startLeft;\n          var finalLeft = Number.parseInt(resizeProxy.style.left, 10);\n          var columnWidth = finalLeft - startColumnLeft;\n          column.width = column.realWidth = columnWidth;\n          table == null ? void 0 : table.emit(\"header-dragend\", column.width, startLeft - startColumnLeft, column, event);\n          requestAnimationFrame(function () {\n            props.store.scheduleLayout(false, true);\n          });\n          document.body.style.cursor = \"\";\n          dragging.value = false;\n          draggingColumn.value = null;\n          dragState.value = {};\n          emit(\"set-drag-visible\", false);\n        }\n        document.removeEventListener(\"mousemove\", handleMouseMove2);\n        document.removeEventListener(\"mouseup\", _handleMouseUp);\n        document.onselectstart = null;\n        document.ondragstart = null;\n        setTimeout(function () {\n          removeClass(columnEl, \"noclick\");\n        }, 0);\n      };\n      document.addEventListener(\"mousemove\", handleMouseMove2);\n      document.addEventListener(\"mouseup\", _handleMouseUp);\n    }\n  };\n  var handleMouseMove = function handleMouseMove(event, column) {\n    if (column.children && column.children.length > 0) return;\n    var el = event.target;\n    if (!isElement(el)) {\n      return;\n    }\n    var target = el == null ? void 0 : el.closest(\"th\");\n    if (!column || !column.resizable) return;\n    if (!dragging.value && props.border) {\n      var rect = target.getBoundingClientRect();\n      var bodyStyle = document.body.style;\n      if (rect.width > 12 && rect.right - event.pageX < 8) {\n        bodyStyle.cursor = \"col-resize\";\n        if (hasClass(target, \"is-sortable\")) {\n          target.style.cursor = \"col-resize\";\n        }\n        draggingColumn.value = column;\n      } else if (!dragging.value) {\n        bodyStyle.cursor = \"\";\n        if (hasClass(target, \"is-sortable\")) {\n          target.style.cursor = \"pointer\";\n        }\n        draggingColumn.value = null;\n      }\n    }\n  };\n  var handleMouseOut = function handleMouseOut() {\n    if (!isClient) return;\n    document.body.style.cursor = \"\";\n  };\n  var toggleOrder = function toggleOrder(_ref) {\n    var order = _ref.order,\n      sortOrders = _ref.sortOrders;\n    if (order === \"\") return sortOrders[0];\n    var index = sortOrders.indexOf(order || null);\n    return sortOrders[index > sortOrders.length - 2 ? 0 : index + 1];\n  };\n  var handleSortClick = function handleSortClick(event, column, givenOrder) {\n    var _a;\n    event.stopPropagation();\n    var order = column.order === givenOrder ? null : givenOrder || toggleOrder(column);\n    var target = (_a = event.target) == null ? void 0 : _a.closest(\"th\");\n    if (target) {\n      if (hasClass(target, \"noclick\")) {\n        removeClass(target, \"noclick\");\n        return;\n      }\n    }\n    if (!column.sortable) return;\n    var states = props.store.states;\n    var sortProp = states.sortProp.value;\n    var sortOrder;\n    var sortingColumn = states.sortingColumn.value;\n    if (sortingColumn !== column || sortingColumn === column && sortingColumn.order === null) {\n      if (sortingColumn) {\n        sortingColumn.order = null;\n      }\n      states.sortingColumn.value = column;\n      sortProp = column.property;\n    }\n    if (!order) {\n      sortOrder = column.order = null;\n    } else {\n      sortOrder = column.order = order;\n    }\n    states.sortProp.value = sortProp;\n    states.sortOrder.value = sortOrder;\n    parent == null ? void 0 : parent.store.commit(\"changeSortCondition\");\n  };\n  return {\n    handleHeaderClick,\n    handleHeaderContextMenu,\n    handleMouseDown,\n    handleMouseMove,\n    handleMouseOut,\n    handleSortClick,\n    handleFilterClick\n  };\n}\nexport { useEvent as default };", "map": {"version": 3, "names": ["useEvent", "props", "emit", "instance", "getCurrentInstance", "parent", "inject", "TABLE_INJECTION_KEY", "handleFilterClick", "event", "stopPropagation", "handleHeaderClick", "column", "filters", "sortable", "handleSortClick", "filterable", "handleHeaderContextMenu", "draggingColumn", "ref", "dragging", "dragState", "handleMouseDown", "isClient", "children", "length", "value", "border", "table", "tableEl", "vnode", "el", "tableLeft", "getBoundingClientRect", "left", "columnEl", "querySelector", "id", "columnRect", "minLeft", "addClass", "startMouseLeft", "clientX", "startLeft", "right", "startColumnLeft", "resizeProxy", "refs", "style", "document", "onselectstart", "ondragstart", "handleMouseMove2", "event2", "deltaLeft", "proxyLeft", "Math", "max", "handleMouseUp", "_dragState$value", "finalLeft", "Number", "parseInt", "columnWidth", "width", "realWidth", "requestAnimationFrame", "store", "scheduleLayout", "body", "cursor", "removeEventListener", "setTimeout", "removeClass", "addEventListener", "handleMouseMove", "target", "isElement", "closest", "resizable", "rect", "bodyStyle", "pageX", "hasClass", "handleMouseOut", "toggleOrder", "_ref", "order", "sortOrders", "index", "indexOf", "givenOrder", "_a", "states", "sortProp", "sortOrder", "sortingColumn", "property", "commit"], "sources": ["../../../../../../../packages/components/table/src/table-header/event-helper.ts"], "sourcesContent": ["// @ts-nocheck\nimport { getCurrentInstance, inject, ref } from 'vue'\nimport {\n  addClass,\n  hasClass,\n  isClient,\n  isElement,\n  removeClass,\n} from '@element-plus/utils'\nimport { TABLE_INJECTION_KEY } from '../tokens'\nimport type { TableHeaderProps } from '.'\nimport type { TableColumnCtx } from '../table-column/defaults'\n\nfunction useEvent<T>(props: TableHeaderProps<T>, emit) {\n  const instance = getCurrentInstance()\n  const parent = inject(TABLE_INJECTION_KEY)\n  const handleFilterClick = (event: Event) => {\n    event.stopPropagation()\n    return\n  }\n\n  const handleHeaderClick = (event: Event, column: TableColumnCtx<T>) => {\n    if (!column.filters && column.sortable) {\n      handleSortClick(event, column, false)\n    } else if (column.filterable && !column.sortable) {\n      handleFilterClick(event)\n    }\n    parent?.emit('header-click', column, event)\n  }\n\n  const handleHeaderContextMenu = (event: Event, column: TableColumnCtx<T>) => {\n    parent?.emit('header-contextmenu', column, event)\n  }\n  const draggingColumn = ref(null)\n  const dragging = ref(false)\n  const dragState = ref({})\n  const handleMouseDown = (event: MouseEvent, column: TableColumnCtx<T>) => {\n    if (!isClient) return\n    if (column.children && column.children.length > 0) return\n    /* istanbul ignore if */\n    if (draggingColumn.value && props.border) {\n      dragging.value = true\n\n      const table = parent\n      emit('set-drag-visible', true)\n      const tableEl = table?.vnode.el\n      const tableLeft = tableEl.getBoundingClientRect().left\n      const columnEl = instance.vnode.el.querySelector(`th.${column.id}`)\n      const columnRect = columnEl.getBoundingClientRect()\n      const minLeft = columnRect.left - tableLeft + 30\n\n      addClass(columnEl, 'noclick')\n\n      dragState.value = {\n        startMouseLeft: event.clientX,\n        startLeft: columnRect.right - tableLeft,\n        startColumnLeft: columnRect.left - tableLeft,\n        tableLeft,\n      }\n      const resizeProxy = table?.refs.resizeProxy as HTMLElement\n      resizeProxy.style.left = `${(dragState.value as any).startLeft}px`\n\n      document.onselectstart = function () {\n        return false\n      }\n      document.ondragstart = function () {\n        return false\n      }\n\n      const handleMouseMove = (event: MouseEvent) => {\n        const deltaLeft =\n          event.clientX - (dragState.value as any).startMouseLeft\n        const proxyLeft = (dragState.value as any).startLeft + deltaLeft\n\n        resizeProxy.style.left = `${Math.max(minLeft, proxyLeft)}px`\n      }\n\n      const handleMouseUp = () => {\n        if (dragging.value) {\n          const { startColumnLeft, startLeft } = dragState.value as any\n          const finalLeft = Number.parseInt(resizeProxy.style.left, 10)\n          const columnWidth = finalLeft - startColumnLeft\n          column.width = column.realWidth = columnWidth\n          table?.emit(\n            'header-dragend',\n            column.width,\n            startLeft - startColumnLeft,\n            column,\n            event\n          )\n          requestAnimationFrame(() => {\n            props.store.scheduleLayout(false, true)\n          })\n          document.body.style.cursor = ''\n          dragging.value = false\n          draggingColumn.value = null\n          dragState.value = {}\n          emit('set-drag-visible', false)\n        }\n\n        document.removeEventListener('mousemove', handleMouseMove)\n        document.removeEventListener('mouseup', handleMouseUp)\n        document.onselectstart = null\n        document.ondragstart = null\n\n        setTimeout(() => {\n          removeClass(columnEl, 'noclick')\n        }, 0)\n      }\n\n      document.addEventListener('mousemove', handleMouseMove)\n      document.addEventListener('mouseup', handleMouseUp)\n    }\n  }\n\n  const handleMouseMove = (event: MouseEvent, column: TableColumnCtx<T>) => {\n    if (column.children && column.children.length > 0) return\n    const el = event.target as HTMLElement\n    if (!isElement(el)) {\n      return\n    }\n    const target = el?.closest('th')\n\n    if (!column || !column.resizable) return\n\n    if (!dragging.value && props.border) {\n      const rect = target.getBoundingClientRect()\n\n      const bodyStyle = document.body.style\n      if (rect.width > 12 && rect.right - event.pageX < 8) {\n        bodyStyle.cursor = 'col-resize'\n        if (hasClass(target, 'is-sortable')) {\n          target.style.cursor = 'col-resize'\n        }\n        draggingColumn.value = column\n      } else if (!dragging.value) {\n        bodyStyle.cursor = ''\n        if (hasClass(target, 'is-sortable')) {\n          target.style.cursor = 'pointer'\n        }\n        draggingColumn.value = null\n      }\n    }\n  }\n\n  const handleMouseOut = () => {\n    if (!isClient) return\n    document.body.style.cursor = ''\n  }\n  const toggleOrder = ({ order, sortOrders }) => {\n    if (order === '') return sortOrders[0]\n    const index = sortOrders.indexOf(order || null)\n    return sortOrders[index > sortOrders.length - 2 ? 0 : index + 1]\n  }\n  const handleSortClick = (\n    event: Event,\n    column: TableColumnCtx<T>,\n    givenOrder: string | boolean\n  ) => {\n    event.stopPropagation()\n    const order =\n      column.order === givenOrder ? null : givenOrder || toggleOrder(column)\n\n    const target = (event.target as HTMLElement)?.closest('th')\n\n    if (target) {\n      if (hasClass(target, 'noclick')) {\n        removeClass(target, 'noclick')\n        return\n      }\n    }\n\n    if (!column.sortable) return\n\n    const states = props.store.states\n    let sortProp = states.sortProp.value\n    let sortOrder\n    const sortingColumn = states.sortingColumn.value\n\n    if (\n      sortingColumn !== column ||\n      (sortingColumn === column && sortingColumn.order === null)\n    ) {\n      if (sortingColumn) {\n        sortingColumn.order = null\n      }\n      states.sortingColumn.value = column\n      sortProp = column.property\n    }\n    if (!order) {\n      sortOrder = column.order = null\n    } else {\n      sortOrder = column.order = order\n    }\n\n    states.sortProp.value = sortProp\n    states.sortOrder.value = sortOrder\n\n    parent?.store.commit('changeSortCondition')\n  }\n\n  return {\n    handleHeaderClick,\n    handleHeaderContextMenu,\n    handleMouseDown,\n    handleMouseMove,\n    handleMouseOut,\n    handleSortClick,\n    handleFilterClick,\n  }\n}\n\nexport default useEvent\n"], "mappings": ";;;;;;AASA,SAASA,QAAQA,CAACC,KAAK,EAAEC,IAAI,EAAE;EAC7B,IAAMC,QAAQ,GAAGC,kBAAkB,EAAE;EACrC,IAAMC,MAAM,GAAGC,MAAM,CAACC,mBAAmB,CAAC;EAC1C,IAAMC,iBAAiB,GAAG,SAApBA,iBAAiBA,CAAIC,KAAK,EAAK;IACnCA,KAAK,CAACC,eAAe,EAAE;IACvB;EACJ,CAAG;EACD,IAAMC,iBAAiB,GAAG,SAApBA,iBAAiBA,CAAIF,KAAK,EAAEG,MAAM,EAAK;IAC3C,IAAI,CAACA,MAAM,CAACC,OAAO,IAAID,MAAM,CAACE,QAAQ,EAAE;MACtCC,eAAe,CAACN,KAAK,EAAEG,MAAM,EAAE,KAAK,CAAC;IAC3C,CAAK,MAAM,IAAIA,MAAM,CAACI,UAAU,IAAI,CAACJ,MAAM,CAACE,QAAQ,EAAE;MAChDN,iBAAiB,CAACC,KAAK,CAAC;IAC9B;IACIJ,MAAM,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,MAAM,CAACH,IAAI,CAAC,cAAc,EAAEU,MAAM,EAAEH,KAAK,CAAC;EACxE,CAAG;EACD,IAAMQ,uBAAuB,GAAG,SAA1BA,uBAAuBA,CAAIR,KAAK,EAAEG,MAAM,EAAK;IACjDP,MAAM,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,MAAM,CAACH,IAAI,CAAC,oBAAoB,EAAEU,MAAM,EAAEH,KAAK,CAAC;EAC9E,CAAG;EACD,IAAMS,cAAc,GAAGC,GAAG,CAAC,IAAI,CAAC;EAChC,IAAMC,QAAQ,GAAGD,GAAG,CAAC,KAAK,CAAC;EAC3B,IAAME,SAAS,GAAGF,GAAG,CAAC,EAAE,CAAC;EACzB,IAAMG,eAAe,GAAG,SAAlBA,eAAeA,CAAIb,KAAK,EAAEG,MAAM,EAAK;IACzC,IAAI,CAACW,QAAQ,EACX;IACF,IAAIX,MAAM,CAACY,QAAQ,IAAIZ,MAAM,CAACY,QAAQ,CAACC,MAAM,GAAG,CAAC,EAC/C;IACF,IAAIP,cAAc,CAACQ,KAAK,IAAIzB,KAAK,CAAC0B,MAAM,EAAE;MACxCP,QAAQ,CAACM,KAAK,GAAG,IAAI;MACrB,IAAME,KAAK,GAAGvB,MAAM;MACpBH,IAAI,CAAC,kBAAkB,EAAE,IAAI,CAAC;MAC9B,IAAM2B,OAAO,GAAGD,KAAK,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,KAAK,CAACE,KAAK,CAACC,EAAE;MACvD,IAAMC,SAAS,GAAGH,OAAO,CAACI,qBAAqB,EAAE,CAACC,IAAI;MACtD,IAAMC,QAAQ,GAAGhC,QAAQ,CAAC2B,KAAK,CAACC,EAAE,CAACK,aAAa,CAAC,MAAMxB,MAAM,CAACyB,EAAE,EAAE,CAAC;MACnE,IAAMC,UAAU,GAAGH,QAAQ,CAACF,qBAAqB,EAAE;MACnD,IAAMM,OAAO,GAAGD,UAAU,CAACJ,IAAI,GAAGF,SAAS,GAAG,EAAE;MAChDQ,QAAQ,CAACL,QAAQ,EAAE,SAAS,CAAC;MAC7Bd,SAAS,CAACK,KAAK,GAAG;QAChBe,cAAc,EAAEhC,KAAK,CAACiC,OAAO;QAC7BC,SAAS,EAAEL,UAAU,CAACM,KAAK,GAAGZ,SAAS;QACvCa,eAAe,EAAEP,UAAU,CAACJ,IAAI,GAAGF,SAAS;QAC5CA;MACR,CAAO;MACD,IAAMc,WAAW,GAAGlB,KAAK,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,KAAK,CAACmB,IAAI,CAACD,WAAW;MACnEA,WAAW,CAACE,KAAK,CAACd,IAAI,GAAG,GAAGb,SAAS,CAACK,KAAK,CAACiB,SAAS,IAAI;MACzDM,QAAQ,CAACC,aAAa,GAAG,YAAW;QAClC,OAAO,KAAK;MACpB,CAAO;MACDD,QAAQ,CAACE,WAAW,GAAG,YAAW;QAChC,OAAO,KAAK;MACpB,CAAO;MACD,IAAMC,gBAAgB,GAAG,SAAnBA,gBAAgBA,CAAIC,MAAM,EAAK;QACnC,IAAMC,SAAS,GAAGD,MAAM,CAACX,OAAO,GAAGrB,SAAS,CAACK,KAAK,CAACe,cAAc;QACjE,IAAMc,SAAS,GAAGlC,SAAS,CAACK,KAAK,CAACiB,SAAS,GAAGW,SAAS;QACvDR,WAAW,CAACE,KAAK,CAACd,IAAI,GAAG,GAAGsB,IAAI,CAACC,GAAG,CAAClB,OAAO,EAAEgB,SAAS,CAAC,IAAI;MACpE,CAAO;MACD,IAAMG,cAAa,GAAG,SAAhBA,aAAaA,CAAA,EAAS;QAC1B,IAAItC,QAAQ,CAACM,KAAK,EAAE;UAClB,IAAAiC,gBAAA,GAAuCtC,SAAS,CAACK,KAAK;YAA9CmB,eAAe,GAAAc,gBAAA,CAAfd,eAAe;YAAEF,SAAS,GAAAgB,gBAAA,CAAThB,SAAS;UAClC,IAAMiB,SAAS,GAAGC,MAAM,CAACC,QAAQ,CAAChB,WAAW,CAACE,KAAK,CAACd,IAAI,EAAE,EAAE,CAAC;UAC7D,IAAM6B,WAAW,GAAGH,SAAS,GAAGf,eAAe;UAC/CjC,MAAM,CAACoD,KAAK,GAAGpD,MAAM,CAACqD,SAAS,GAAGF,WAAW;UAC7CnC,KAAK,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,KAAK,CAAC1B,IAAI,CAAC,gBAAgB,EAAEU,MAAM,CAACoD,KAAK,EAAErB,SAAS,GAAGE,eAAe,EAAEjC,MAAM,EAAEH,KAAK,CAAC;UAC/GyD,qBAAqB,CAAC,YAAM;YAC1BjE,KAAK,CAACkE,KAAK,CAACC,cAAc,CAAC,KAAK,EAAE,IAAI,CAAC;UACnD,CAAW,CAAC;UACFnB,QAAQ,CAACoB,IAAI,CAACrB,KAAK,CAACsB,MAAM,GAAG,EAAE;UAC/BlD,QAAQ,CAACM,KAAK,GAAG,KAAK;UACtBR,cAAc,CAACQ,KAAK,GAAG,IAAI;UAC3BL,SAAS,CAACK,KAAK,GAAG,EAAE;UACpBxB,IAAI,CAAC,kBAAkB,EAAE,KAAK,CAAC;QACzC;QACQ+C,QAAQ,CAACsB,mBAAmB,CAAC,WAAW,EAAEnB,gBAAgB,CAAC;QAC3DH,QAAQ,CAACsB,mBAAmB,CAAC,SAAS,EAAEb,cAAa,CAAC;QACtDT,QAAQ,CAACC,aAAa,GAAG,IAAI;QAC7BD,QAAQ,CAACE,WAAW,GAAG,IAAI;QAC3BqB,UAAU,CAAC,YAAM;UACfC,WAAW,CAACtC,QAAQ,EAAE,SAAS,CAAC;QAC1C,CAAS,EAAE,CAAC,CAAC;MACb,CAAO;MACDc,QAAQ,CAACyB,gBAAgB,CAAC,WAAW,EAAEtB,gBAAgB,CAAC;MACxDH,QAAQ,CAACyB,gBAAgB,CAAC,SAAS,EAAEhB,cAAa,CAAC;IACzD;EACA,CAAG;EACD,IAAMiB,eAAe,GAAG,SAAlBA,eAAeA,CAAIlE,KAAK,EAAEG,MAAM,EAAK;IACzC,IAAIA,MAAM,CAACY,QAAQ,IAAIZ,MAAM,CAACY,QAAQ,CAACC,MAAM,GAAG,CAAC,EAC/C;IACF,IAAMM,EAAE,GAAGtB,KAAK,CAACmE,MAAM;IACvB,IAAI,CAACC,SAAS,CAAC9C,EAAE,CAAC,EAAE;MAClB;IACN;IACI,IAAM6C,MAAM,GAAG7C,EAAE,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,EAAE,CAAC+C,OAAO,CAAC,IAAI,CAAC;IACrD,IAAI,CAAClE,MAAM,IAAI,CAACA,MAAM,CAACmE,SAAS,EAC9B;IACF,IAAI,CAAC3D,QAAQ,CAACM,KAAK,IAAIzB,KAAK,CAAC0B,MAAM,EAAE;MACnC,IAAMqD,IAAI,GAAGJ,MAAM,CAAC3C,qBAAqB,EAAE;MAC3C,IAAMgD,SAAS,GAAGhC,QAAQ,CAACoB,IAAI,CAACrB,KAAK;MACrC,IAAIgC,IAAI,CAAChB,KAAK,GAAG,EAAE,IAAIgB,IAAI,CAACpC,KAAK,GAAGnC,KAAK,CAACyE,KAAK,GAAG,CAAC,EAAE;QACnDD,SAAS,CAACX,MAAM,GAAG,YAAY;QAC/B,IAAIa,QAAQ,CAACP,MAAM,EAAE,aAAa,CAAC,EAAE;UACnCA,MAAM,CAAC5B,KAAK,CAACsB,MAAM,GAAG,YAAY;QAC5C;QACQpD,cAAc,CAACQ,KAAK,GAAGd,MAAM;MACrC,CAAO,MAAM,IAAI,CAACQ,QAAQ,CAACM,KAAK,EAAE;QAC1BuD,SAAS,CAACX,MAAM,GAAG,EAAE;QACrB,IAAIa,QAAQ,CAACP,MAAM,EAAE,aAAa,CAAC,EAAE;UACnCA,MAAM,CAAC5B,KAAK,CAACsB,MAAM,GAAG,SAAS;QACzC;QACQpD,cAAc,CAACQ,KAAK,GAAG,IAAI;MACnC;IACA;EACA,CAAG;EACD,IAAM0D,cAAc,GAAG,SAAjBA,cAAcA,CAAA,EAAS;IAC3B,IAAI,CAAC7D,QAAQ,EACX;IACF0B,QAAQ,CAACoB,IAAI,CAACrB,KAAK,CAACsB,MAAM,GAAG,EAAE;EACnC,CAAG;EACD,IAAMe,WAAW,GAAG,SAAdA,WAAWA,CAAAC,IAAA,EAA8B;IAAA,IAAxBC,KAAK,GAAAD,IAAA,CAALC,KAAK;MAAEC,UAAU,GAAAF,IAAA,CAAVE,UAAU;IACtC,IAAID,KAAK,KAAK,EAAE,EACd,OAAOC,UAAU,CAAC,CAAC,CAAC;IACtB,IAAMC,KAAK,GAAGD,UAAU,CAACE,OAAO,CAACH,KAAK,IAAI,IAAI,CAAC;IAC/C,OAAOC,UAAU,CAACC,KAAK,GAAGD,UAAU,CAAC/D,MAAM,GAAG,CAAC,GAAG,CAAC,GAAGgE,KAAK,GAAG,CAAC,CAAC;EACpE,CAAG;EACD,IAAM1E,eAAe,GAAG,SAAlBA,eAAeA,CAAIN,KAAK,EAAEG,MAAM,EAAE+E,UAAU,EAAK;IACrD,IAAIC,EAAE;IACNnF,KAAK,CAACC,eAAe,EAAE;IACvB,IAAM6E,KAAK,GAAG3E,MAAM,CAAC2E,KAAK,KAAKI,UAAU,GAAG,IAAI,GAAGA,UAAU,IAAIN,WAAW,CAACzE,MAAM,CAAC;IACpF,IAAMgE,MAAM,GAAG,CAACgB,EAAE,GAAGnF,KAAK,CAACmE,MAAM,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGgB,EAAE,CAACd,OAAO,CAAC,IAAI,CAAC;IACtE,IAAIF,MAAM,EAAE;MACV,IAAIO,QAAQ,CAACP,MAAM,EAAE,SAAS,CAAC,EAAE;QAC/BH,WAAW,CAACG,MAAM,EAAE,SAAS,CAAC;QAC9B;MACR;IACA;IACI,IAAI,CAAChE,MAAM,CAACE,QAAQ,EAClB;IACF,IAAM+E,MAAM,GAAG5F,KAAK,CAACkE,KAAK,CAAC0B,MAAM;IACjC,IAAIC,QAAQ,GAAGD,MAAM,CAACC,QAAQ,CAACpE,KAAK;IACpC,IAAIqE,SAAS;IACb,IAAMC,aAAa,GAAGH,MAAM,CAACG,aAAa,CAACtE,KAAK;IAChD,IAAIsE,aAAa,KAAKpF,MAAM,IAAIoF,aAAa,KAAKpF,MAAM,IAAIoF,aAAa,CAACT,KAAK,KAAK,IAAI,EAAE;MACxF,IAAIS,aAAa,EAAE;QACjBA,aAAa,CAACT,KAAK,GAAG,IAAI;MAClC;MACMM,MAAM,CAACG,aAAa,CAACtE,KAAK,GAAGd,MAAM;MACnCkF,QAAQ,GAAGlF,MAAM,CAACqF,QAAQ;IAChC;IACI,IAAI,CAACV,KAAK,EAAE;MACVQ,SAAS,GAAGnF,MAAM,CAAC2E,KAAK,GAAG,IAAI;IACrC,CAAK,MAAM;MACLQ,SAAS,GAAGnF,MAAM,CAAC2E,KAAK,GAAGA,KAAK;IACtC;IACIM,MAAM,CAACC,QAAQ,CAACpE,KAAK,GAAGoE,QAAQ;IAChCD,MAAM,CAACE,SAAS,CAACrE,KAAK,GAAGqE,SAAS;IAClC1F,MAAM,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,MAAM,CAAC8D,KAAK,CAAC+B,MAAM,CAAC,qBAAqB,CAAC;EACxE,CAAG;EACD,OAAO;IACLvF,iBAAiB;IACjBM,uBAAuB;IACvBK,eAAe;IACfqD,eAAe;IACfS,cAAc;IACdrE,eAAe;IACfP;EACJ,CAAG;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
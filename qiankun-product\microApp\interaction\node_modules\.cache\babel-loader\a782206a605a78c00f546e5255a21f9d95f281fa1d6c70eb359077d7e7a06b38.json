{"ast": null, "code": "import { resolveComponent as _resolveComponent, with<PERSON><PERSON><PERSON> as _withKeys, createVNode as _createVNode, renderList as _renderList, Fragment as _Fragment, openBlock as _openBlock, createElementBlock as _createElementBlock, createBlock as _createBlock, withCtx as _withCtx, createCommentVNode as _createCommentVNode, toDisplayString as _toDisplayString, createTextVNode as _createTextVNode, createElementVNode as _createElementVNode } from \"vue\";\nvar _hoisted_1 = {\n  class: \"ReturnReceiptUser\"\n};\nvar _hoisted_2 = {\n  class: \"globalTable\"\n};\nvar _hoisted_3 = {\n  class: \"globalPagination\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  var _component_el_input = _resolveComponent(\"el-input\");\n  var _component_el_option = _resolveComponent(\"el-option\");\n  var _component_el_select = _resolveComponent(\"el-select\");\n  var _component_xyl_search_button = _resolveComponent(\"xyl-search-button\");\n  var _component_el_table_column = _resolveComponent(\"el-table-column\");\n  var _component_el_button = _resolveComponent(\"el-button\");\n  var _component_el_table = _resolveComponent(\"el-table\");\n  var _component_el_pagination = _resolveComponent(\"el-pagination\");\n  var _component_xyl_popup_window = _resolveComponent(\"xyl-popup-window\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createVNode(_component_xyl_search_button, {\n    onQueryClick: $setup.handleQuery,\n    onResetClick: $setup.handleReset,\n    onHandleButton: $setup.handleButton,\n    buttonList: $setup.buttonList\n  }, {\n    search: _withCtx(function () {\n      return [_createVNode(_component_el_input, {\n        modelValue: $setup.keyword,\n        \"onUpdate:modelValue\": _cache[0] || (_cache[0] = function ($event) {\n          return $setup.keyword = $event;\n        }),\n        placeholder: \"请输入关键词\",\n        onKeyup: _withKeys($setup.handleQuery, [\"enter\"]),\n        clearable: \"\"\n      }, null, 8 /* PROPS */, [\"modelValue\", \"onKeyup\"]), ['2', '3'].includes($setup.isType) ? (_openBlock(), _createBlock(_component_el_select, {\n        key: 0,\n        modelValue: $setup.receiptOptions,\n        \"onUpdate:modelValue\": _cache[1] || (_cache[1] = function ($event) {\n          return $setup.receiptOptions = $event;\n        }),\n        onChange: $setup.queryChange,\n        placeholder: \"请选择回执选项\",\n        clearable: \"\"\n      }, {\n        default: _withCtx(function () {\n          return [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.receiptOptionsData, function (item) {\n            return _openBlock(), _createBlock(_component_el_option, {\n              key: item,\n              label: item,\n              value: item\n            }, null, 8 /* PROPS */, [\"label\", \"value\"]);\n          }), 128 /* KEYED_FRAGMENT */))];\n        }),\n        _: 1 /* STABLE */\n      }, 8 /* PROPS */, [\"modelValue\"])) : _createCommentVNode(\"v-if\", true)];\n    }),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"onQueryClick\"]), _createElementVNode(\"div\", _hoisted_2, [_createVNode(_component_el_table, {\n    ref: \"tableRef\",\n    \"row-key\": \"id\",\n    data: $setup.tableData,\n    onSelect: $setup.handleTableSelect,\n    onSelectAll: $setup.handleTableSelect\n  }, {\n    default: _withCtx(function () {\n      return [_createVNode(_component_el_table_column, {\n        type: \"selection\",\n        \"reserve-selection\": \"\",\n        width: \"60\",\n        fixed: \"\"\n      }), _createVNode(_component_el_table_column, {\n        label: \"回执人\",\n        \"min-width\": \"120\",\n        prop: \"receiveUserName\"\n      }), _createVNode(_component_el_table_column, {\n        label: \"回执时间\",\n        width: \"190\"\n      }, {\n        default: _withCtx(function (scope) {\n          return [_createTextVNode(_toDisplayString($setup.format(scope.row.replyTime)), 1 /* TEXT */)];\n        }),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_table_column, {\n        label: ['1'].includes($setup.isType) ? '回执内容' : '回执选项',\n        \"min-width\": \"180\",\n        prop: \"content\",\n        \"show-overflow-tooltip\": \"\"\n      }, null, 8 /* PROPS */, [\"label\"]), ['2', '3'].includes($setup.isType) ? (_openBlock(), _createBlock(_component_el_table_column, {\n        key: 0,\n        label: \"回执备注\",\n        \"min-width\": \"180\",\n        prop: \"remarks\"\n      })) : _createCommentVNode(\"v-if\", true), _createVNode(_component_el_table_column, {\n        width: \"120\",\n        fixed: \"right\",\n        label: \"操作\",\n        \"class-name\": \"globalTableCustom\"\n      }, {\n        default: _withCtx(function (scope) {\n          return [_createVNode(_component_el_button, {\n            onClick: function onClick($event) {\n              return $setup.handleEdit(scope.row);\n            },\n            type: \"primary\",\n            plain: \"\"\n          }, {\n            default: _withCtx(function () {\n              return _cache[5] || (_cache[5] = [_createTextVNode(\"编辑\")]);\n            }),\n            _: 2 /* DYNAMIC */\n          }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"onClick\"])];\n        }),\n        _: 1 /* STABLE */\n      })];\n    }),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"data\", \"onSelect\", \"onSelectAll\"])]), _createElementVNode(\"div\", _hoisted_3, [_createVNode(_component_el_pagination, {\n    currentPage: $setup.pageNo,\n    \"onUpdate:currentPage\": _cache[2] || (_cache[2] = function ($event) {\n      return $setup.pageNo = $event;\n    }),\n    \"page-size\": $setup.pageSize,\n    \"onUpdate:pageSize\": _cache[3] || (_cache[3] = function ($event) {\n      return $setup.pageSize = $event;\n    }),\n    \"page-sizes\": $setup.pageSizes,\n    layout: \"total, sizes, prev, pager, next, jumper\",\n    onSizeChange: $setup.handleQuery,\n    onCurrentChange: $setup.handleQuery,\n    total: $setup.totals,\n    background: \"\"\n  }, null, 8 /* PROPS */, [\"currentPage\", \"page-size\", \"page-sizes\", \"onSizeChange\", \"onCurrentChange\", \"total\"])]), _createVNode(_component_xyl_popup_window, {\n    modelValue: $setup.show,\n    \"onUpdate:modelValue\": _cache[4] || (_cache[4] = function ($event) {\n      return $setup.show = $event;\n    }),\n    name: \"编辑回执详情\"\n  }, {\n    default: _withCtx(function () {\n      return [_createVNode($setup[\"ReturnReceiptUserNew\"], {\n        id: $setup.id,\n        uid: $setup.props.id,\n        onCallback: $setup.callback\n      }, null, 8 /* PROPS */, [\"id\", \"uid\"])];\n    }),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"modelValue\"])]);\n}", "map": {"version": 3, "names": ["class", "_createElementBlock", "_hoisted_1", "_createVNode", "_component_xyl_search_button", "onQueryClick", "$setup", "handleQuery", "onResetClick", "handleReset", "onHandleButton", "handleButton", "buttonList", "search", "_withCtx", "_component_el_input", "modelValue", "keyword", "_cache", "$event", "placeholder", "onKeyup", "_with<PERSON><PERSON><PERSON>", "clearable", "includes", "isType", "_createBlock", "_component_el_select", "key", "receiptOptions", "onChange", "query<PERSON>hange", "default", "_Fragment", "_renderList", "receiptOptionsData", "item", "_component_el_option", "label", "value", "_", "_createCommentVNode", "_createElementVNode", "_hoisted_2", "_component_el_table", "ref", "data", "tableData", "onSelect", "handleTableSelect", "onSelectAll", "_component_el_table_column", "type", "width", "fixed", "prop", "scope", "_createTextVNode", "_toDisplayString", "format", "row", "replyTime", "_component_el_button", "onClick", "handleEdit", "plain", "_hoisted_3", "_component_el_pagination", "currentPage", "pageNo", "pageSize", "pageSizes", "layout", "onSizeChange", "onCurrentChange", "total", "totals", "background", "_component_xyl_popup_window", "show", "name", "id", "uid", "props", "onCallback", "callback"], "sources": ["D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\interaction\\src\\views\\NoticeAnnouncement\\component\\ReturnReceiptUser.vue"], "sourcesContent": ["<template>\r\n  <div class=\"ReturnReceiptUser\">\r\n    <xyl-search-button @queryClick=\"handleQuery\"\r\n                       @resetClick=\"handleReset\"\r\n                       @handleButton=\"handleButton\"\r\n                       :buttonList=\"buttonList\">\r\n      <template #search>\r\n        <el-input v-model=\"keyword\"\r\n                  placeholder=\"请输入关键词\"\r\n                  @keyup.enter=\"handleQuery\"\r\n                  clearable />\r\n        <el-select v-model=\"receiptOptions\"\r\n                   v-if=\"['2', '3'].includes(isType)\"\r\n                   @change=\"queryChange\"\r\n                   placeholder=\"请选择回执选项\"\r\n                   clearable>\r\n          <el-option v-for=\"item in receiptOptionsData\"\r\n                     :key=\"item\"\r\n                     :label=\"item\"\r\n                     :value=\"item\" />\r\n        </el-select>\r\n      </template>\r\n    </xyl-search-button>\r\n    <div class=\"globalTable\">\r\n      <el-table ref=\"tableRef\"\r\n                row-key=\"id\"\r\n                :data=\"tableData\"\r\n                @select=\"handleTableSelect\"\r\n                @select-all=\"handleTableSelect\">\r\n        <el-table-column type=\"selection\"\r\n                         reserve-selection\r\n                         width=\"60\"\r\n                         fixed />\r\n        <el-table-column label=\"回执人\"\r\n                         min-width=\"120\"\r\n                         prop=\"receiveUserName\" />\r\n        <el-table-column label=\"回执时间\"\r\n                         width=\"190\">\r\n          <template #default=\"scope\">{{ format(scope.row.replyTime) }}</template>\r\n        </el-table-column>\r\n        <el-table-column :label=\"['1'].includes(isType) ? '回执内容' : '回执选项'\"\r\n                         min-width=\"180\"\r\n                         prop=\"content\"\r\n                         show-overflow-tooltip />\r\n        <el-table-column label=\"回执备注\"\r\n                         v-if=\"['2', '3'].includes(isType)\"\r\n                         min-width=\"180\"\r\n                         prop=\"remarks\" />\r\n        <el-table-column width=\"120\"\r\n                         fixed=\"right\"\r\n                         label=\"操作\"\r\n                         class-name=\"globalTableCustom\">\r\n          <template #default=\"scope\">\r\n            <el-button @click=\"handleEdit(scope.row)\"\r\n                       type=\"primary\"\r\n                       plain>编辑</el-button>\r\n          </template>\r\n        </el-table-column>\r\n      </el-table>\r\n    </div>\r\n    <div class=\"globalPagination\">\r\n      <el-pagination v-model:currentPage=\"pageNo\"\r\n                     v-model:page-size=\"pageSize\"\r\n                     :page-sizes=\"pageSizes\"\r\n                     layout=\"total, sizes, prev, pager, next, jumper\"\r\n                     @size-change=\"handleQuery\"\r\n                     @current-change=\"handleQuery\"\r\n                     :total=\"totals\"\r\n                     background />\r\n    </div>\r\n    <xyl-popup-window v-model=\"show\"\r\n                      name=\"编辑回执详情\">\r\n      <ReturnReceiptUserNew :id=\"id\"\r\n                            :uid=\"props.id\"\r\n                            @callback=\"callback\"></ReturnReceiptUserNew>\r\n    </xyl-popup-window>\r\n  </div>\r\n</template>\r\n<script>\r\nexport default { name: 'ReturnReceiptUser' }\r\n</script>\r\n<script setup>\r\nimport api from '@/api'\r\nimport { ref, onMounted } from 'vue'\r\nimport { format } from 'common/js/time.js'\r\nimport { GlobalTable } from 'common/js/GlobalTable.js'\r\nimport ReturnReceiptUserNew from './ReturnReceiptUserNew'\r\nconst props = defineProps({ id: { type: String, default: '' } })\r\nconst buttonList = [\r\n  { id: 'del', name: '删除', type: 'primary', has: '' }\r\n]\r\nconst id = ref('')\r\nconst show = ref(false)\r\nconst {\r\n  keyword,\r\n  tableRef,\r\n  totals,\r\n  pageNo,\r\n  pageSize,\r\n  pageSizes,\r\n  tableData,\r\n  handleQuery,\r\n  handleTableSelect,\r\n  handleDel,\r\n  tableRefReset,\r\n  tableQuery\r\n} = GlobalTable({ tableApi: 'ReturnReceiptList', delApi: 'ReturnReceiptDel', tableDataObj: { query: { notificationId: props.id } } })\r\n\r\nonMounted(() => {\r\n  if (props.id) { NoticeAnnouncementInfo() }\r\n})\r\nconst receiptOptions = ref('')\r\nconst receiptOptionsData = ref([])\r\nconst isType = ref('')\r\n\r\nconst NoticeAnnouncementInfo = async () => {\r\n  const res = await api.NoticeAnnouncementInfo({ detailId: props.id })\r\n  var { data } = res\r\n  receiptOptionsData.value = data.receiptOptions\r\n  isType.value = data.receipt.receiptOptionType.value\r\n  handleQuery()\r\n}\r\nconst queryChange = () => {\r\n  tableQuery.value = { query: { notificationId: props.id, content: receiptOptions.value || null } }\r\n}\r\nconst handleButton = (id) => {\r\n  switch (id) {\r\n    case 'del':\r\n      handleDel('回执')\r\n      break\r\n    default:\r\n      break\r\n  }\r\n}\r\nconst handleReset = () => {\r\n  keyword.value = ''\r\n  receiptOptions.value = ''\r\n  tableQuery.value = { query: { notificationId: props.id, content: receiptOptions.value || null } }\r\n  handleQuery()\r\n}\r\nconst handleEdit = (item) => {\r\n  id.value = item.id\r\n  show.value = true\r\n}\r\nconst callback = () => {\r\n  tableRefReset()\r\n  handleQuery()\r\n  show.value = false\r\n}\r\n</script>\r\n<style lang=\"scss\">\r\n.ReturnReceiptUser {\r\n  width: 990px;\r\n  height: calc(85vh - 52px);\r\n  padding: 0 20px;\r\n\r\n  .xyl-search-button {\r\n    .xyl-button {\r\n      width: calc(100% - 660px);\r\n    }\r\n\r\n    .xyl-search {\r\n      width: 660px;\r\n\r\n      .zy-el-select {\r\n        margin-left: 20px;\r\n      }\r\n    }\r\n  }\r\n\r\n  .globalTable {\r\n    width: 100%;\r\n    height: calc(100% - (var(--zy-height) + (var(--zy-distance-four) * 2) + 42px));\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;EACOA,KAAK,EAAC;AAAmB;;EAsBvBA,KAAK,EAAC;AAAa;;EAqCnBA,KAAK,EAAC;AAAkB;;;;;;;;;;;uBA3D/BC,mBAAA,CA2EM,OA3ENC,UA2EM,GA1EJC,YAAA,CAoBoBC,4BAAA;IApBAC,YAAU,EAAEC,MAAA,CAAAC,WAAW;IACvBC,YAAU,EAAEF,MAAA,CAAAG,WAAW;IACvBC,cAAY,EAAEJ,MAAA,CAAAK,YAAY;IAC1BC,UAAU,EAAEN,MAAA,CAAAM;;IACnBC,MAAM,EAAAC,QAAA,CACf;MAAA,OAGsB,CAHtBX,YAAA,CAGsBY,mBAAA;QAV9BC,UAAA,EAO2BV,MAAA,CAAAW,OAAO;QAPlC,uBAAAC,MAAA,QAAAA,MAAA,gBAAAC,MAAA;UAAA,OAO2Bb,MAAA,CAAAW,OAAO,GAAAE,MAAA;QAAA;QAChBC,WAAW,EAAC,QAAQ;QACnBC,OAAK,EATxBC,SAAA,CASgChB,MAAA,CAAAC,WAAW;QACzBgB,SAAS,EAAT;qEAEkBC,QAAQ,CAAClB,MAAA,CAAAmB,MAAM,K,cAD3CC,YAAA,CASYC,oBAAA;QApBpBC,GAAA;QAAAZ,UAAA,EAW4BV,MAAA,CAAAuB,cAAc;QAX1C,uBAAAX,MAAA,QAAAA,MAAA,gBAAAC,MAAA;UAAA,OAW4Bb,MAAA,CAAAuB,cAAc,GAAAV,MAAA;QAAA;QAEtBW,QAAM,EAAExB,MAAA,CAAAyB,WAAW;QACpBX,WAAW,EAAC,SAAS;QACrBG,SAAS,EAAT;;QAfnBS,OAAA,EAAAlB,QAAA,CAgBqB;UAAA,OAAkC,E,kBAA7Cb,mBAAA,CAG2BgC,SAAA,QAnBrCC,WAAA,CAgBoC5B,MAAA,CAAA6B,kBAAkB,EAhBtD,UAgB4BC,IAAI;iCAAtBV,YAAA,CAG2BW,oBAAA;cAFfT,GAAG,EAAEQ,IAAI;cACTE,KAAK,EAAEF,IAAI;cACXG,KAAK,EAAEH;;;;QAnB7BI,CAAA;2CAAAC,mBAAA,e;;IAAAD,CAAA;uCAuBIE,mBAAA,CAoCM,OApCNC,UAoCM,GAnCJxC,YAAA,CAkCWyC,mBAAA;IAlCDC,GAAG,EAAC,UAAU;IACd,SAAO,EAAC,IAAI;IACXC,IAAI,EAAExC,MAAA,CAAAyC,SAAS;IACfC,QAAM,EAAE1C,MAAA,CAAA2C,iBAAiB;IACzBC,WAAU,EAAE5C,MAAA,CAAA2C;;IA5B7BjB,OAAA,EAAAlB,QAAA,CA6BQ;MAAA,OAGyB,CAHzBX,YAAA,CAGyBgD,0BAAA;QAHRC,IAAI,EAAC,WAAW;QAChB,mBAAiB,EAAjB,EAAiB;QACjBC,KAAK,EAAC,IAAI;QACVC,KAAK,EAAL;UACjBnD,YAAA,CAE0CgD,0BAAA;QAFzBb,KAAK,EAAC,KAAK;QACX,WAAS,EAAC,KAAK;QACfiB,IAAI,EAAC;UACtBpD,YAAA,CAGkBgD,0BAAA;QAHDb,KAAK,EAAC,MAAM;QACZe,KAAK,EAAC;;QACVrB,OAAO,EAAAlB,QAAA,CAAS,UAAiC0C,KAAnC;UAAA,QAtCnCC,gBAAA,CAAAC,gBAAA,CAsCwCpD,MAAA,CAAAqD,MAAM,CAACH,KAAK,CAACI,GAAG,CAACC,SAAS,kB;;QAtClErB,CAAA;UAwCQrC,YAAA,CAGyCgD,0BAAA;QAHvBb,KAAK,QAAQd,QAAQ,CAAClB,MAAA,CAAAmB,MAAM;QAC7B,WAAS,EAAC,KAAK;QACf8B,IAAI,EAAC,SAAS;QACd,uBAAqB,EAArB;qDAEiB/B,QAAQ,CAAClB,MAAA,CAAAmB,MAAM,K,cADjDC,YAAA,CAGkCyB,0BAAA;QA/C1CvB,GAAA;QA4CyBU,KAAK,EAAC,MAAM;QAEZ,WAAS,EAAC,KAAK;QACfiB,IAAI,EAAC;YA/C9Bd,mBAAA,gBAgDQtC,YAAA,CASkBgD,0BAAA;QATDE,KAAK,EAAC,KAAK;QACXC,KAAK,EAAC,OAAO;QACbhB,KAAK,EAAC,IAAI;QACV,YAAU,EAAC;;QACfN,OAAO,EAAAlB,QAAA,CAChB,UAE+B0C,KAHR;UAAA,QACvBrD,YAAA,CAE+B2D,oBAAA;YAFnBC,OAAK,WAALA,OAAKA,CAAA5C,MAAA;cAAA,OAAEb,MAAA,CAAA0D,UAAU,CAACR,KAAK,CAACI,GAAG;YAAA;YAC5BR,IAAI,EAAC,SAAS;YACda,KAAK,EAAL;;YAvDvBjC,OAAA,EAAAlB,QAAA,CAuD6B;cAAA,OAAEI,MAAA,QAAAA,MAAA,OAvD/BuC,gBAAA,CAuD6B,IAAE,E;;YAvD/BjB,CAAA;;;QAAAA,CAAA;;;IAAAA,CAAA;4DA4DIE,mBAAA,CASM,OATNwB,UASM,GARJ/D,YAAA,CAO4BgE,wBAAA;IAPLC,WAAW,EAAE9D,MAAA,CAAA+D,MAAM;IA7DhD,wBAAAnD,MAAA,QAAAA,MAAA,gBAAAC,MAAA;MAAA,OA6D0Cb,MAAA,CAAA+D,MAAM,GAAAlD,MAAA;IAAA;IACnB,WAAS,EAAEb,MAAA,CAAAgE,QAAQ;IA9DhD,qBAAApD,MAAA,QAAAA,MAAA,gBAAAC,MAAA;MAAA,OA8DwCb,MAAA,CAAAgE,QAAQ,GAAAnD,MAAA;IAAA;IAC1B,YAAU,EAAEb,MAAA,CAAAiE,SAAS;IACtBC,MAAM,EAAC,yCAAyC;IAC/CC,YAAW,EAAEnE,MAAA,CAAAC,WAAW;IACxBmE,eAAc,EAAEpE,MAAA,CAAAC,WAAW;IAC3BoE,KAAK,EAAErE,MAAA,CAAAsE,MAAM;IACdC,UAAU,EAAV;qHAEjB1E,YAAA,CAKmB2E,2BAAA;IA3EvB9D,UAAA,EAsE+BV,MAAA,CAAAyE,IAAI;IAtEnC,uBAAA7D,MAAA,QAAAA,MAAA,gBAAAC,MAAA;MAAA,OAsE+Bb,MAAA,CAAAyE,IAAI,GAAA5D,MAAA;IAAA;IACb6D,IAAI,EAAC;;IAvE3BhD,OAAA,EAAAlB,QAAA,CAwEM;MAAA,OAEkE,CAFlEX,YAAA,CAEkEG,MAAA;QAF3C2E,EAAE,EAAE3E,MAAA,CAAA2E,EAAE;QACNC,GAAG,EAAE5E,MAAA,CAAA6E,KAAK,CAACF,EAAE;QACbG,UAAQ,EAAE9E,MAAA,CAAA+E;;;IA1EvC7C,CAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
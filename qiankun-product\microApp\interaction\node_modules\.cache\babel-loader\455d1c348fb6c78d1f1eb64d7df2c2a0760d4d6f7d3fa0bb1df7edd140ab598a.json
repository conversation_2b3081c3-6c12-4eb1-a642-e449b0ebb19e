{"ast": null, "code": "import baseFlatten from './_baseFlatten.js';\nimport baseIteratee from './_baseIteratee.js';\nimport baseRest from './_baseRest.js';\nimport baseUniq from './_baseUniq.js';\nimport isArrayLikeObject from './isArrayLikeObject.js';\nimport last from './last.js';\n\n/**\n * This method is like `_.union` except that it accepts `iteratee` which is\n * invoked for each element of each `arrays` to generate the criterion by\n * which uniqueness is computed. Result values are chosen from the first\n * array in which the value occurs. The iteratee is invoked with one argument:\n * (value).\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category Array\n * @param {...Array} [arrays] The arrays to inspect.\n * @param {Function} [iteratee=_.identity] The iteratee invoked per element.\n * @returns {Array} Returns the new array of combined values.\n * @example\n *\n * _.unionBy([2.1], [1.2, 2.3], Math.floor);\n * // => [2.1, 1.2]\n *\n * // The `_.property` iteratee shorthand.\n * _.unionBy([{ 'x': 1 }], [{ 'x': 2 }, { 'x': 1 }], 'x');\n * // => [{ 'x': 1 }, { 'x': 2 }]\n */\nvar unionBy = baseRest(function (arrays) {\n  var iteratee = last(arrays);\n  if (isArrayLikeObject(iteratee)) {\n    iteratee = undefined;\n  }\n  return baseUniq(baseFlatten(arrays, 1, isArrayLikeObject, true), baseIteratee(iteratee, 2));\n});\nexport default unionBy;", "map": {"version": 3, "names": ["baseFlatten", "baseIteratee", "baseRest", "baseUniq", "isArrayLikeObject", "last", "unionBy", "arrays", "iteratee", "undefined"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/node_modules/.pnpm/lodash-es@4.17.21/node_modules/lodash-es/unionBy.js"], "sourcesContent": ["import baseFlatten from './_baseFlatten.js';\nimport baseIteratee from './_baseIteratee.js';\nimport baseRest from './_baseRest.js';\nimport baseUniq from './_baseUniq.js';\nimport isArrayLikeObject from './isArrayLikeObject.js';\nimport last from './last.js';\n\n/**\n * This method is like `_.union` except that it accepts `iteratee` which is\n * invoked for each element of each `arrays` to generate the criterion by\n * which uniqueness is computed. Result values are chosen from the first\n * array in which the value occurs. The iteratee is invoked with one argument:\n * (value).\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category Array\n * @param {...Array} [arrays] The arrays to inspect.\n * @param {Function} [iteratee=_.identity] The iteratee invoked per element.\n * @returns {Array} Returns the new array of combined values.\n * @example\n *\n * _.unionBy([2.1], [1.2, 2.3], Math.floor);\n * // => [2.1, 1.2]\n *\n * // The `_.property` iteratee shorthand.\n * _.unionBy([{ 'x': 1 }], [{ 'x': 2 }, { 'x': 1 }], 'x');\n * // => [{ 'x': 1 }, { 'x': 2 }]\n */\nvar unionBy = baseRest(function(arrays) {\n  var iteratee = last(arrays);\n  if (isArrayLikeObject(iteratee)) {\n    iteratee = undefined;\n  }\n  return baseUniq(baseFlatten(arrays, 1, isArrayLikeObject, true), baseIteratee(iteratee, 2));\n});\n\nexport default unionBy;\n"], "mappings": "AAAA,OAAOA,WAAW,MAAM,mBAAmB;AAC3C,OAAOC,YAAY,MAAM,oBAAoB;AAC7C,OAAOC,QAAQ,MAAM,gBAAgB;AACrC,OAAOC,QAAQ,MAAM,gBAAgB;AACrC,OAAOC,iBAAiB,MAAM,wBAAwB;AACtD,OAAOC,IAAI,MAAM,WAAW;;AAE5B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIC,OAAO,GAAGJ,QAAQ,CAAC,UAASK,MAAM,EAAE;EACtC,IAAIC,QAAQ,GAAGH,IAAI,CAACE,MAAM,CAAC;EAC3B,IAAIH,iBAAiB,CAACI,QAAQ,CAAC,EAAE;IAC/BA,QAAQ,GAAGC,SAAS;EACtB;EACA,OAAON,QAAQ,CAACH,WAAW,CAACO,MAAM,EAAE,CAAC,EAAEH,iBAAiB,EAAE,IAAI,CAAC,EAAEH,YAAY,CAACO,QAAQ,EAAE,CAAC,CAAC,CAAC;AAC7F,CAAC,CAAC;AAEF,eAAeF,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
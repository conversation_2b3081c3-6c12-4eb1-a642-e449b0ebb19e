{"ast": null, "code": "import { resolveComponent as _resolveComponent, createVNode as _createVNode, withCtx as _withCtx, createTextVNode as _createTextVNode, createElementVNode as _createElementVNode, withModifiers as _withModifiers, openBlock as _openBlock, createElementBlock as _createElementBlock } from \"vue\";\nvar _hoisted_1 = {\n  class: \"AddressBookTypeNew\"\n};\nvar _hoisted_2 = {\n  class: \"globalFormButton\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  var _component_el_input = _resolveComponent(\"el-input\");\n  var _component_el_form_item = _resolveComponent(\"el-form-item\");\n  var _component_el_radio = _resolveComponent(\"el-radio\");\n  var _component_el_radio_group = _resolveComponent(\"el-radio-group\");\n  var _component_el_button = _resolveComponent(\"el-button\");\n  var _component_el_form = _resolveComponent(\"el-form\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createVNode(_component_el_form, {\n    ref: \"formRef\",\n    model: $setup.form,\n    rules: $setup.rules,\n    inline: \"\",\n    \"label-position\": \"top\",\n    class: \"globalForm\",\n    onSubmit: _cache[3] || (_cache[3] = _withModifiers(function () {}, [\"prevent\"]))\n  }, {\n    default: _withCtx(function () {\n      return [_createVNode(_component_el_form_item, {\n        label: \"分类名称\",\n        prop: \"name\"\n      }, {\n        default: _withCtx(function () {\n          return [_createVNode(_component_el_input, {\n            modelValue: $setup.form.name,\n            \"onUpdate:modelValue\": _cache[0] || (_cache[0] = function ($event) {\n              return $setup.form.name = $event;\n            }),\n            placeholder: \"请输入分类名称\",\n            clearable: \"\"\n          }, null, 8 /* PROPS */, [\"modelValue\"])];\n        }),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_form_item, {\n        label: \"是否公开\",\n        prop: \"isOpen\"\n      }, {\n        default: _withCtx(function () {\n          return [_createVNode(_component_el_radio_group, {\n            modelValue: $setup.form.isOpen,\n            \"onUpdate:modelValue\": _cache[1] || (_cache[1] = function ($event) {\n              return $setup.form.isOpen = $event;\n            })\n          }, {\n            default: _withCtx(function () {\n              return [_createVNode(_component_el_radio, {\n                label: 1\n              }, {\n                default: _withCtx(function () {\n                  return _cache[4] || (_cache[4] = [_createTextVNode(\"公开\")]);\n                }),\n                _: 1 /* STABLE */\n              }), _createVNode(_component_el_radio, {\n                label: 0\n              }, {\n                default: _withCtx(function () {\n                  return _cache[5] || (_cache[5] = [_createTextVNode(\"不公开\")]);\n                }),\n                _: 1 /* STABLE */\n              })];\n            }),\n            _: 1 /* STABLE */\n          }, 8 /* PROPS */, [\"modelValue\"])];\n        }),\n        _: 1 /* STABLE */\n      }), _createElementVNode(\"div\", _hoisted_2, [_createVNode(_component_el_button, {\n        type: \"primary\",\n        onClick: _cache[2] || (_cache[2] = function ($event) {\n          return $setup.submitForm($setup.formRef);\n        })\n      }, {\n        default: _withCtx(function () {\n          return _cache[6] || (_cache[6] = [_createTextVNode(\"提交\")]);\n        }),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_button, {\n        onClick: $setup.resetForm\n      }, {\n        default: _withCtx(function () {\n          return _cache[7] || (_cache[7] = [_createTextVNode(\"取消\")]);\n        }),\n        _: 1 /* STABLE */\n      })])];\n    }),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"model\", \"rules\"])]);\n}", "map": {"version": 3, "names": ["class", "_createElementBlock", "_hoisted_1", "_createVNode", "_component_el_form", "ref", "model", "$setup", "form", "rules", "inline", "onSubmit", "_cache", "_withModifiers", "default", "_withCtx", "_component_el_form_item", "label", "prop", "_component_el_input", "modelValue", "name", "$event", "placeholder", "clearable", "_", "_component_el_radio_group", "isOpen", "_component_el_radio", "_createTextVNode", "_createElementVNode", "_hoisted_2", "_component_el_button", "type", "onClick", "submitForm", "formRef", "resetForm"], "sources": ["D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\interaction\\src\\views\\AddressBook\\AddressBookTypeNew.vue"], "sourcesContent": ["<template>\r\n  <div class=\"AddressBookTypeNew\">\r\n    <el-form ref=\"formRef\"\r\n             :model=\"form\"\r\n             :rules=\"rules\"\r\n             inline\r\n             label-position=\"top\"\r\n             class=\"globalForm\"\r\n             @submit.enter.prevent>\r\n      <el-form-item label=\"分类名称\"\r\n                    prop=\"name\">\r\n        <el-input v-model=\"form.name\"\r\n                  placeholder=\"请输入分类名称\"\r\n                  clearable />\r\n      </el-form-item>\r\n      <el-form-item label=\"是否公开\"\r\n                    prop=\"isOpen\">\r\n        <el-radio-group v-model=\"form.isOpen\">\r\n          <el-radio :label=\"1\">公开</el-radio>\r\n          <el-radio :label=\"0\">不公开</el-radio>\r\n        </el-radio-group>\r\n      </el-form-item>\r\n      <div class=\"globalFormButton\">\r\n        <el-button type=\"primary\"\r\n                   @click=\"submitForm(formRef)\">提交</el-button>\r\n        <el-button @click=\"resetForm\">取消</el-button>\r\n      </div>\r\n    </el-form>\r\n  </div>\r\n</template>\r\n<script>\r\nexport default { name: 'AddressBookTypeNew' }\r\n</script>\r\n<script setup>\r\nimport api from '@/api'\r\nimport { reactive, ref, onMounted } from 'vue'\r\nimport { useRoute } from 'vue-router'\r\nconst route = useRoute()\r\nimport { ElMessage } from 'element-plus'\r\nconst props = defineProps({ id: { type: String, default: '' } })\r\nconst emit = defineEmits(['callback'])\r\n\r\nconst formRef = ref()\r\nconst form = reactive({\r\n  name: '', // 字典标识名称\r\n  isOpen: 1 // 字典标识名称\r\n})\r\nconst rules = reactive({\r\n  name: [{ required: true, message: '请输入分类名称', trigger: ['blur', 'change'] }],\r\n  isOpen: [{ required: true, message: '请选择是否公用', trigger: ['blur', 'change'] }]\r\n})\r\n\r\nonMounted(() => { if (props.id) { AddressBookTypeInfo() } })\r\n\r\nconst AddressBookTypeInfo = async () => {\r\n  const res = await api.AddressBookTypeInfo({ detailId: props.id })\r\n  var { data } = res\r\n  form.name = data.name\r\n  form.isOpen = data.isOpen\r\n}\r\nconst submitForm = async (formEl) => {\r\n  if (!formEl) return\r\n  await formEl.validate((valid, fields) => {\r\n    if (valid) { globalJson() } else { ElMessage({ type: 'warning', message: '请根据提示信息完善字段内容！' }) }\r\n  })\r\n}\r\nconst globalJson = async () => {\r\n  const { code } = await api.globalJson(props.id ? '/relationBookType/edit' : '/relationBookType/add', {\r\n    form: {\r\n      id: props.id,\r\n      name: form.name,\r\n      isOpen: form.isOpen,\r\n      ownerType: route.query.type || 'system'\r\n    }\r\n  })\r\n  if (code === 200) {\r\n    ElMessage({ type: 'success', message: props.id ? '编辑成功' : '新增成功' })\r\n    emit('callback')\r\n  }\r\n}\r\nconst resetForm = () => { emit('callback') }\r\n</script>\r\n<style lang=\"scss\">\r\n.AddressBookTypeNew {\r\n  width: 680px;\r\n}\r\n</style>\r\n"], "mappings": ";;EACOA,KAAK,EAAC;AAAoB;;EAqBtBA,KAAK,EAAC;AAAkB;;;;;;;;uBArBjCC,mBAAA,CA2BM,OA3BNC,UA2BM,GA1BJC,YAAA,CAyBUC,kBAAA;IAzBDC,GAAG,EAAC,SAAS;IACZC,KAAK,EAAEC,MAAA,CAAAC,IAAI;IACXC,KAAK,EAAEF,MAAA,CAAAE,KAAK;IACbC,MAAM,EAAN,EAAM;IACN,gBAAc,EAAC,KAAK;IACpBV,KAAK,EAAC,YAAY;IACjBW,QAAM,EAAAC,MAAA,QAAAA,MAAA,MARpBC,cAAA,CAQa,cAAqB;;IARlCC,OAAA,EAAAC,QAAA,CASM;MAAA,OAKe,CALfZ,YAAA,CAKea,uBAAA;QALDC,KAAK,EAAC,MAAM;QACZC,IAAI,EAAC;;QAVzBJ,OAAA,EAAAC,QAAA,CAWQ;UAAA,OAEsB,CAFtBZ,YAAA,CAEsBgB,mBAAA;YAb9BC,UAAA,EAW2Bb,MAAA,CAAAC,IAAI,CAACa,IAAI;YAXpC,uBAAAT,MAAA,QAAAA,MAAA,gBAAAU,MAAA;cAAA,OAW2Bf,MAAA,CAAAC,IAAI,CAACa,IAAI,GAAAC,MAAA;YAAA;YAClBC,WAAW,EAAC,SAAS;YACrBC,SAAS,EAAT;;;QAblBC,CAAA;UAeMtB,YAAA,CAMea,uBAAA;QANDC,KAAK,EAAC,MAAM;QACZC,IAAI,EAAC;;QAhBzBJ,OAAA,EAAAC,QAAA,CAiBQ;UAAA,OAGiB,CAHjBZ,YAAA,CAGiBuB,yBAAA;YApBzBN,UAAA,EAiBiCb,MAAA,CAAAC,IAAI,CAACmB,MAAM;YAjB5C,uBAAAf,MAAA,QAAAA,MAAA,gBAAAU,MAAA;cAAA,OAiBiCf,MAAA,CAAAC,IAAI,CAACmB,MAAM,GAAAL,MAAA;YAAA;;YAjB5CR,OAAA,EAAAC,QAAA,CAkBU;cAAA,OAAkC,CAAlCZ,YAAA,CAAkCyB,mBAAA;gBAAvBX,KAAK,EAAE;cAAC;gBAlB7BH,OAAA,EAAAC,QAAA,CAkB+B;kBAAA,OAAEH,MAAA,QAAAA,MAAA,OAlBjCiB,gBAAA,CAkB+B,IAAE,E;;gBAlBjCJ,CAAA;kBAmBUtB,YAAA,CAAmCyB,mBAAA;gBAAxBX,KAAK,EAAE;cAAC;gBAnB7BH,OAAA,EAAAC,QAAA,CAmB+B;kBAAA,OAAGH,MAAA,QAAAA,MAAA,OAnBlCiB,gBAAA,CAmB+B,KAAG,E;;gBAnBlCJ,CAAA;;;YAAAA,CAAA;;;QAAAA,CAAA;UAsBMK,mBAAA,CAIM,OAJNC,UAIM,GAHJ5B,YAAA,CACsD6B,oBAAA;QAD3CC,IAAI,EAAC,SAAS;QACbC,OAAK,EAAAtB,MAAA,QAAAA,MAAA,gBAAAU,MAAA;UAAA,OAAEf,MAAA,CAAA4B,UAAU,CAAC5B,MAAA,CAAA6B,OAAO;QAAA;;QAxB7CtB,OAAA,EAAAC,QAAA,CAwBgD;UAAA,OAAEH,MAAA,QAAAA,MAAA,OAxBlDiB,gBAAA,CAwBgD,IAAE,E;;QAxBlDJ,CAAA;UAyBQtB,YAAA,CAA4C6B,oBAAA;QAAhCE,OAAK,EAAE3B,MAAA,CAAA8B;MAAS;QAzBpCvB,OAAA,EAAAC,QAAA,CAyBsC;UAAA,OAAEH,MAAA,QAAAA,MAAA,OAzBxCiB,gBAAA,CAyBsC,IAAE,E;;QAzBxCJ,CAAA;;;IAAAA,CAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
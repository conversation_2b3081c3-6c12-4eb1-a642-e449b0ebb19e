{"ast": null, "code": "\"use strict\";\n\nvar utils = require(\"./utils\");\nmodule.exports = function batchProcessorMaker(options) {\n  options = options || {};\n  var reporter = options.reporter;\n  var asyncProcess = utils.getOption(options, \"async\", true);\n  var autoProcess = utils.getOption(options, \"auto\", true);\n  if (autoProcess && !asyncProcess) {\n    reporter && reporter.warn(\"Invalid options combination. auto=true and async=false is invalid. Setting async=true.\");\n    asyncProcess = true;\n  }\n  var batch = Batch();\n  var asyncFrameHandler;\n  var isProcessing = false;\n  function addFunction(level, fn) {\n    if (!isProcessing && autoProcess && asyncProcess && batch.size() === 0) {\n      // Since this is async, it is guaranteed to be executed after that the fn is added to the batch.\n      // This needs to be done before, since we're checking the size of the batch to be 0.\n      processBatchAsync();\n    }\n    batch.add(level, fn);\n  }\n  function processBatch() {\n    // Save the current batch, and create a new batch so that incoming functions are not added into the currently processing batch.\n    // Continue processing until the top-level batch is empty (functions may be added to the new batch while processing, and so on).\n    isProcessing = true;\n    while (batch.size()) {\n      var processingBatch = batch;\n      batch = Batch();\n      processingBatch.process();\n    }\n    isProcessing = false;\n  }\n  function forceProcessBatch(localAsyncProcess) {\n    if (isProcessing) {\n      return;\n    }\n    if (localAsyncProcess === undefined) {\n      localAsyncProcess = asyncProcess;\n    }\n    if (asyncFrameHandler) {\n      cancelFrame(asyncFrameHandler);\n      asyncFrameHandler = null;\n    }\n    if (localAsyncProcess) {\n      processBatchAsync();\n    } else {\n      processBatch();\n    }\n  }\n  function processBatchAsync() {\n    asyncFrameHandler = requestFrame(processBatch);\n  }\n  function clearBatch() {\n    batch = {};\n    batchSize = 0;\n    topLevel = 0;\n    bottomLevel = 0;\n  }\n  function cancelFrame(listener) {\n    // var cancel = window.cancelAnimationFrame || window.mozCancelAnimationFrame || window.webkitCancelAnimationFrame || window.clearTimeout;\n    var cancel = clearTimeout;\n    return cancel(listener);\n  }\n  function requestFrame(callback) {\n    // var raf = window.requestAnimationFrame || window.mozRequestAnimationFrame || window.webkitRequestAnimationFrame || function(fn) { return window.setTimeout(fn, 20); };\n    var raf = function raf(fn) {\n      return setTimeout(fn, 0);\n    };\n    return raf(callback);\n  }\n  return {\n    add: addFunction,\n    force: forceProcessBatch\n  };\n};\nfunction Batch() {\n  var batch = {};\n  var size = 0;\n  var topLevel = 0;\n  var bottomLevel = 0;\n  function add(level, fn) {\n    if (!fn) {\n      fn = level;\n      level = 0;\n    }\n    if (level > topLevel) {\n      topLevel = level;\n    } else if (level < bottomLevel) {\n      bottomLevel = level;\n    }\n    if (!batch[level]) {\n      batch[level] = [];\n    }\n    batch[level].push(fn);\n    size++;\n  }\n  function process() {\n    for (var level = bottomLevel; level <= topLevel; level++) {\n      var fns = batch[level];\n      for (var i = 0; i < fns.length; i++) {\n        var fn = fns[i];\n        fn();\n      }\n    }\n  }\n  function getSize() {\n    return size;\n  }\n  return {\n    add: add,\n    process: process,\n    size: getSize\n  };\n}", "map": {"version": 3, "names": ["utils", "require", "module", "exports", "batchProcessorMaker", "options", "reporter", "asyncProcess", "getOption", "autoProcess", "warn", "batch", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "isProcessing", "addFunction", "level", "fn", "size", "processBatchAsync", "add", "processBatch", "processingBatch", "process", "forceProcessBatch", "localAsyncProcess", "undefined", "cancelFrame", "requestFrame", "clearBatch", "batchSize", "topLevel", "bottomLevel", "listener", "cancel", "clearTimeout", "callback", "raf", "setTimeout", "force", "push", "fns", "i", "length", "getSize"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/node_modules/.pnpm/batch-processor@1.0.0/node_modules/batch-processor/src/batch-processor.js"], "sourcesContent": ["\"use strict\";\n\nvar utils = require(\"./utils\");\n\nmodule.exports = function batchProcessorMaker(options) {\n    options             = options || {};\n    var reporter        = options.reporter;\n    var asyncProcess    = utils.getOption(options, \"async\", true);\n    var autoProcess     = utils.getOption(options, \"auto\", true);\n\n    if(autoProcess && !asyncProcess) {\n        reporter && reporter.warn(\"Invalid options combination. auto=true and async=false is invalid. Setting async=true.\");\n        asyncProcess = true;\n    }\n\n    var batch = Batch();\n    var asyncFrameHandler;\n    var isProcessing = false;\n\n    function addFunction(level, fn) {\n        if(!isProcessing && autoProcess && asyncProcess && batch.size() === 0) {\n            // Since this is async, it is guaranteed to be executed after that the fn is added to the batch.\n            // This needs to be done before, since we're checking the size of the batch to be 0.\n            processBatchAsync();\n        }\n\n        batch.add(level, fn);\n    }\n\n    function processBatch() {\n        // Save the current batch, and create a new batch so that incoming functions are not added into the currently processing batch.\n        // Continue processing until the top-level batch is empty (functions may be added to the new batch while processing, and so on).\n        isProcessing = true;\n        while (batch.size()) {\n            var processingBatch = batch;\n            batch = Batch();\n            processingBatch.process();\n        }\n        isProcessing = false;\n    }\n\n    function forceProcessBatch(localAsyncProcess) {\n        if (isProcessing) {\n            return;\n        }\n\n        if(localAsyncProcess === undefined) {\n            localAsyncProcess = asyncProcess;\n        }\n\n        if(asyncFrameHandler) {\n            cancelFrame(asyncFrameHandler);\n            asyncFrameHandler = null;\n        }\n\n        if(localAsyncProcess) {\n            processBatchAsync();\n        } else {\n            processBatch();\n        }\n    }\n\n    function processBatchAsync() {\n        asyncFrameHandler = requestFrame(processBatch);\n    }\n\n    function clearBatch() {\n        batch           = {};\n        batchSize       = 0;\n        topLevel        = 0;\n        bottomLevel     = 0;\n    }\n\n    function cancelFrame(listener) {\n        // var cancel = window.cancelAnimationFrame || window.mozCancelAnimationFrame || window.webkitCancelAnimationFrame || window.clearTimeout;\n        var cancel = clearTimeout;\n        return cancel(listener);\n    }\n\n    function requestFrame(callback) {\n        // var raf = window.requestAnimationFrame || window.mozRequestAnimationFrame || window.webkitRequestAnimationFrame || function(fn) { return window.setTimeout(fn, 20); };\n        var raf = function(fn) { return setTimeout(fn, 0); };\n        return raf(callback);\n    }\n\n    return {\n        add: addFunction,\n        force: forceProcessBatch\n    };\n};\n\nfunction Batch() {\n    var batch       = {};\n    var size        = 0;\n    var topLevel    = 0;\n    var bottomLevel = 0;\n\n    function add(level, fn) {\n        if(!fn) {\n            fn = level;\n            level = 0;\n        }\n\n        if(level > topLevel) {\n            topLevel = level;\n        } else if(level < bottomLevel) {\n            bottomLevel = level;\n        }\n\n        if(!batch[level]) {\n            batch[level] = [];\n        }\n\n        batch[level].push(fn);\n        size++;\n    }\n\n    function process() {\n        for(var level = bottomLevel; level <= topLevel; level++) {\n            var fns = batch[level];\n\n            for(var i = 0; i < fns.length; i++) {\n                var fn = fns[i];\n                fn();\n            }\n        }\n    }\n\n    function getSize() {\n        return size;\n    }\n\n    return {\n        add: add,\n        process: process,\n        size: getSize\n    };\n}\n"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,KAAK,GAAGC,OAAO,CAAC,SAAS,CAAC;AAE9BC,MAAM,CAACC,OAAO,GAAG,SAASC,mBAAmBA,CAACC,OAAO,EAAE;EACnDA,OAAO,GAAeA,OAAO,IAAI,CAAC,CAAC;EACnC,IAAIC,QAAQ,GAAUD,OAAO,CAACC,QAAQ;EACtC,IAAIC,YAAY,GAAMP,KAAK,CAACQ,SAAS,CAACH,OAAO,EAAE,OAAO,EAAE,IAAI,CAAC;EAC7D,IAAII,WAAW,GAAOT,KAAK,CAACQ,SAAS,CAACH,OAAO,EAAE,MAAM,EAAE,IAAI,CAAC;EAE5D,IAAGI,WAAW,IAAI,CAACF,YAAY,EAAE;IAC7BD,QAAQ,IAAIA,QAAQ,CAACI,IAAI,CAAC,wFAAwF,CAAC;IACnHH,YAAY,GAAG,IAAI;EACvB;EAEA,IAAII,KAAK,GAAGC,KAAK,CAAC,CAAC;EACnB,IAAIC,iBAAiB;EACrB,IAAIC,YAAY,GAAG,KAAK;EAExB,SAASC,WAAWA,CAACC,KAAK,EAAEC,EAAE,EAAE;IAC5B,IAAG,CAACH,YAAY,IAAIL,WAAW,IAAIF,YAAY,IAAII,KAAK,CAACO,IAAI,CAAC,CAAC,KAAK,CAAC,EAAE;MACnE;MACA;MACAC,iBAAiB,CAAC,CAAC;IACvB;IAEAR,KAAK,CAACS,GAAG,CAACJ,KAAK,EAAEC,EAAE,CAAC;EACxB;EAEA,SAASI,YAAYA,CAAA,EAAG;IACpB;IACA;IACAP,YAAY,GAAG,IAAI;IACnB,OAAOH,KAAK,CAACO,IAAI,CAAC,CAAC,EAAE;MACjB,IAAII,eAAe,GAAGX,KAAK;MAC3BA,KAAK,GAAGC,KAAK,CAAC,CAAC;MACfU,eAAe,CAACC,OAAO,CAAC,CAAC;IAC7B;IACAT,YAAY,GAAG,KAAK;EACxB;EAEA,SAASU,iBAAiBA,CAACC,iBAAiB,EAAE;IAC1C,IAAIX,YAAY,EAAE;MACd;IACJ;IAEA,IAAGW,iBAAiB,KAAKC,SAAS,EAAE;MAChCD,iBAAiB,GAAGlB,YAAY;IACpC;IAEA,IAAGM,iBAAiB,EAAE;MAClBc,WAAW,CAACd,iBAAiB,CAAC;MAC9BA,iBAAiB,GAAG,IAAI;IAC5B;IAEA,IAAGY,iBAAiB,EAAE;MAClBN,iBAAiB,CAAC,CAAC;IACvB,CAAC,MAAM;MACHE,YAAY,CAAC,CAAC;IAClB;EACJ;EAEA,SAASF,iBAAiBA,CAAA,EAAG;IACzBN,iBAAiB,GAAGe,YAAY,CAACP,YAAY,CAAC;EAClD;EAEA,SAASQ,UAAUA,CAAA,EAAG;IAClBlB,KAAK,GAAa,CAAC,CAAC;IACpBmB,SAAS,GAAS,CAAC;IACnBC,QAAQ,GAAU,CAAC;IACnBC,WAAW,GAAO,CAAC;EACvB;EAEA,SAASL,WAAWA,CAACM,QAAQ,EAAE;IAC3B;IACA,IAAIC,MAAM,GAAGC,YAAY;IACzB,OAAOD,MAAM,CAACD,QAAQ,CAAC;EAC3B;EAEA,SAASL,YAAYA,CAACQ,QAAQ,EAAE;IAC5B;IACA,IAAIC,GAAG,GAAG,SAANA,GAAGA,CAAYpB,EAAE,EAAE;MAAE,OAAOqB,UAAU,CAACrB,EAAE,EAAE,CAAC,CAAC;IAAE,CAAC;IACpD,OAAOoB,GAAG,CAACD,QAAQ,CAAC;EACxB;EAEA,OAAO;IACHhB,GAAG,EAAEL,WAAW;IAChBwB,KAAK,EAAEf;EACX,CAAC;AACL,CAAC;AAED,SAASZ,KAAKA,CAAA,EAAG;EACb,IAAID,KAAK,GAAS,CAAC,CAAC;EACpB,IAAIO,IAAI,GAAU,CAAC;EACnB,IAAIa,QAAQ,GAAM,CAAC;EACnB,IAAIC,WAAW,GAAG,CAAC;EAEnB,SAASZ,GAAGA,CAACJ,KAAK,EAAEC,EAAE,EAAE;IACpB,IAAG,CAACA,EAAE,EAAE;MACJA,EAAE,GAAGD,KAAK;MACVA,KAAK,GAAG,CAAC;IACb;IAEA,IAAGA,KAAK,GAAGe,QAAQ,EAAE;MACjBA,QAAQ,GAAGf,KAAK;IACpB,CAAC,MAAM,IAAGA,KAAK,GAAGgB,WAAW,EAAE;MAC3BA,WAAW,GAAGhB,KAAK;IACvB;IAEA,IAAG,CAACL,KAAK,CAACK,KAAK,CAAC,EAAE;MACdL,KAAK,CAACK,KAAK,CAAC,GAAG,EAAE;IACrB;IAEAL,KAAK,CAACK,KAAK,CAAC,CAACwB,IAAI,CAACvB,EAAE,CAAC;IACrBC,IAAI,EAAE;EACV;EAEA,SAASK,OAAOA,CAAA,EAAG;IACf,KAAI,IAAIP,KAAK,GAAGgB,WAAW,EAAEhB,KAAK,IAAIe,QAAQ,EAAEf,KAAK,EAAE,EAAE;MACrD,IAAIyB,GAAG,GAAG9B,KAAK,CAACK,KAAK,CAAC;MAEtB,KAAI,IAAI0B,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,GAAG,CAACE,MAAM,EAAED,CAAC,EAAE,EAAE;QAChC,IAAIzB,EAAE,GAAGwB,GAAG,CAACC,CAAC,CAAC;QACfzB,EAAE,CAAC,CAAC;MACR;IACJ;EACJ;EAEA,SAAS2B,OAAOA,CAAA,EAAG;IACf,OAAO1B,IAAI;EACf;EAEA,OAAO;IACHE,GAAG,EAAEA,GAAG;IACRG,OAAO,EAAEA,OAAO;IAChBL,IAAI,EAAE0B;EACV,CAAC;AACL", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}
{"ast": null, "code": "import baseForOwnRight from './_baseForOwnRight.js';\nimport castFunction from './_castFunction.js';\n\n/**\n * This method is like `_.forOwn` except that it iterates over properties of\n * `object` in the opposite order.\n *\n * @static\n * @memberOf _\n * @since 2.0.0\n * @category Object\n * @param {Object} object The object to iterate over.\n * @param {Function} [iteratee=_.identity] The function invoked per iteration.\n * @returns {Object} Returns `object`.\n * @see _.forOwn\n * @example\n *\n * function Foo() {\n *   this.a = 1;\n *   this.b = 2;\n * }\n *\n * Foo.prototype.c = 3;\n *\n * _.forOwnRight(new Foo, function(value, key) {\n *   console.log(key);\n * });\n * // => Logs 'b' then 'a' assuming `_.forOwn` logs 'a' then 'b'.\n */\nfunction forOwnRight(object, iteratee) {\n  return object && baseForOwnRight(object, castFunction(iteratee));\n}\nexport default forOwnRight;", "map": {"version": 3, "names": ["baseForOwnRight", "castFunction", "forOwnRight", "object", "iteratee"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/node_modules/.pnpm/lodash-es@4.17.21/node_modules/lodash-es/forOwnRight.js"], "sourcesContent": ["import baseForOwnRight from './_baseForOwnRight.js';\nimport castFunction from './_castFunction.js';\n\n/**\n * This method is like `_.forOwn` except that it iterates over properties of\n * `object` in the opposite order.\n *\n * @static\n * @memberOf _\n * @since 2.0.0\n * @category Object\n * @param {Object} object The object to iterate over.\n * @param {Function} [iteratee=_.identity] The function invoked per iteration.\n * @returns {Object} Returns `object`.\n * @see _.forOwn\n * @example\n *\n * function Foo() {\n *   this.a = 1;\n *   this.b = 2;\n * }\n *\n * Foo.prototype.c = 3;\n *\n * _.forOwnRight(new Foo, function(value, key) {\n *   console.log(key);\n * });\n * // => Logs 'b' then 'a' assuming `_.forOwn` logs 'a' then 'b'.\n */\nfunction forOwnRight(object, iteratee) {\n  return object && baseForOwnRight(object, castFunction(iteratee));\n}\n\nexport default forOwnRight;\n"], "mappings": "AAAA,OAAOA,eAAe,MAAM,uBAAuB;AACnD,OAAOC,YAAY,MAAM,oBAAoB;;AAE7C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,WAAWA,CAACC,MAAM,EAAEC,QAAQ,EAAE;EACrC,OAAOD,MAAM,IAAIH,eAAe,CAACG,MAAM,EAAEF,YAAY,CAACG,QAAQ,CAAC,CAAC;AAClE;AAEA,eAAeF,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
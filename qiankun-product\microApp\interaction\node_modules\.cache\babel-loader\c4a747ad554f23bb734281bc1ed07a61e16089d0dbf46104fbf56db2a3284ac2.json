{"ast": null, "code": "function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nfunction _defineProperty(e, r, t) { return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, { value: t, enumerable: !0, configurable: !0, writable: !0 }) : e[r] = t, e; }\nfunction _toPropertyKey(t) { var i = _toPrimitive(t, \"string\"); return \"symbol\" == typeof i ? i : i + \"\"; }\nfunction _toPrimitive(t, r) { if (\"object\" != typeof t || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || \"default\"); if (\"object\" != typeof i) return i; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (\"string\" === r ? String : Number)(t); }\nimport { defineComponent, toRefs, reactive, openBlock, createBlock, normalizeProps, guardReactiveProps, withCtx, createVNode, mergeProps, renderSlot, Teleport, Transition, createCommentVNode, createElementBlock, Fragment } from 'vue';\nimport { pick } from 'lodash-unified';\nimport { tooltipV2ArrowProps } from './arrow.mjs';\nimport { tooltipV2ContentProps } from './content.mjs';\nimport { tooltipV2RootProps } from './root.mjs';\nimport { tooltipV2Props } from './tooltip.mjs';\nimport { tooltipV2TriggerProps } from './trigger.mjs';\nimport TooltipV2Root from './root2.mjs';\nimport TooltipV2Arrow from './arrow2.mjs';\nimport TooltipV2Content from './content2.mjs';\nimport TooltipV2Trigger from './trigger2.mjs';\nimport _export_sfc from '../../../_virtual/plugin-vue_export-helper.mjs';\nvar __default__ = defineComponent({\n  name: \"ElTooltipV2\"\n});\nvar _sfc_main = /* @__PURE__ */defineComponent(_objectSpread(_objectSpread({}, __default__), {}, {\n  props: tooltipV2Props,\n  setup(__props) {\n    var props = __props;\n    var refedProps = toRefs(props);\n    var arrowProps = reactive(pick(refedProps, Object.keys(tooltipV2ArrowProps)));\n    var contentProps = reactive(pick(refedProps, Object.keys(tooltipV2ContentProps)));\n    var rootProps = reactive(pick(refedProps, Object.keys(tooltipV2RootProps)));\n    var triggerProps = reactive(pick(refedProps, Object.keys(tooltipV2TriggerProps)));\n    return function (_ctx, _cache) {\n      return openBlock(), createBlock(TooltipV2Root, normalizeProps(guardReactiveProps(rootProps)), {\n        default: withCtx(function (_ref) {\n          var open = _ref.open;\n          return [createVNode(TooltipV2Trigger, mergeProps(triggerProps, {\n            nowrap: \"\"\n          }), {\n            default: withCtx(function () {\n              return [renderSlot(_ctx.$slots, \"trigger\")];\n            }),\n            _: 3\n          }, 16), (openBlock(), createBlock(Teleport, {\n            to: _ctx.to,\n            disabled: !_ctx.teleported\n          }, [_ctx.fullTransition ? (openBlock(), createBlock(Transition, normalizeProps(mergeProps({\n            key: 0\n          }, _ctx.transitionProps)), {\n            default: withCtx(function () {\n              return [_ctx.alwaysOn || open ? (openBlock(), createBlock(TooltipV2Content, normalizeProps(mergeProps({\n                key: 0\n              }, contentProps)), {\n                arrow: withCtx(function (_ref2) {\n                  var style = _ref2.style,\n                    side = _ref2.side;\n                  return [_ctx.showArrow ? (openBlock(), createBlock(TooltipV2Arrow, mergeProps({\n                    key: 0\n                  }, arrowProps, {\n                    style,\n                    side\n                  }), null, 16, [\"style\", \"side\"])) : createCommentVNode(\"v-if\", true)];\n                }),\n                default: withCtx(function () {\n                  return [renderSlot(_ctx.$slots, \"default\")];\n                }),\n                _: 3\n              }, 16)) : createCommentVNode(\"v-if\", true)];\n            }),\n            _: 2\n          }, 1040)) : (openBlock(), createElementBlock(Fragment, {\n            key: 1\n          }, [_ctx.alwaysOn || open ? (openBlock(), createBlock(TooltipV2Content, normalizeProps(mergeProps({\n            key: 0\n          }, contentProps)), {\n            arrow: withCtx(function (_ref3) {\n              var style = _ref3.style,\n                side = _ref3.side;\n              return [_ctx.showArrow ? (openBlock(), createBlock(TooltipV2Arrow, mergeProps({\n                key: 0\n              }, arrowProps, {\n                style,\n                side\n              }), null, 16, [\"style\", \"side\"])) : createCommentVNode(\"v-if\", true)];\n            }),\n            default: withCtx(function () {\n              return [renderSlot(_ctx.$slots, \"default\")];\n            }),\n            _: 3\n          }, 16)) : createCommentVNode(\"v-if\", true)], 64))], 8, [\"to\", \"disabled\"]))];\n        }),\n        _: 3\n      }, 16);\n    };\n  }\n}));\nvar TooltipV2 = /* @__PURE__ */_export_sfc(_sfc_main, [[\"__file\", \"tooltip.vue\"]]);\nexport { TooltipV2 as default };", "map": {"version": 3, "names": ["name", "refedProps", "toRefs", "props", "arrowProps", "reactive", "pick", "Object", "keys", "tooltipV2ArrowProps", "contentProps", "tooltipV2ContentProps", "rootProps", "tooltipV2RootProps", "triggerProps", "tooltipV2TriggerProps"], "sources": ["../../../../../../packages/components/tooltip-v2/src/tooltip.vue"], "sourcesContent": ["<template>\n  <tooltip-v2-root v-bind=\"rootProps\">\n    <template #default=\"{ open }\">\n      <tooltip-v2-trigger v-bind=\"triggerProps\" nowrap>\n        <slot name=\"trigger\" />\n      </tooltip-v2-trigger>\n      <teleport :to=\"to\" :disabled=\"!teleported\">\n        <template v-if=\"fullTransition\">\n          <transition v-bind=\"transitionProps\">\n            <tooltip-v2-content v-if=\"alwaysOn || open\" v-bind=\"contentProps\">\n              <slot />\n              <template #arrow=\"{ style, side }\">\n                <tooltip-v2-arrow\n                  v-if=\"showArrow\"\n                  v-bind=\"arrowProps\"\n                  :style=\"style\"\n                  :side=\"side\"\n                />\n              </template>\n            </tooltip-v2-content>\n          </transition>\n        </template>\n        <template v-else>\n          <tooltip-v2-content v-if=\"alwaysOn || open\" v-bind=\"contentProps\">\n            <slot />\n            <template #arrow=\"{ style, side }\">\n              <tooltip-v2-arrow\n                v-if=\"showArrow\"\n                v-bind=\"arrowProps\"\n                :style=\"style\"\n                :side=\"side\"\n              />\n            </template>\n          </tooltip-v2-content>\n        </template>\n      </teleport>\n    </template>\n  </tooltip-v2-root>\n</template>\n\n<script setup lang=\"ts\">\n// @ts-nocheck\nimport { reactive, toRefs } from 'vue'\nimport { pick } from 'lodash-unified'\nimport { tooltipV2ArrowProps } from './arrow'\nimport { tooltipV2ContentProps } from './content'\nimport { tooltipV2RootProps } from './root'\nimport { tooltipV2Props } from './tooltip'\nimport { tooltipV2TriggerProps } from './trigger'\nimport TooltipV2Root from './root.vue'\nimport TooltipV2Arrow from './arrow.vue'\nimport TooltipV2Content from './content.vue'\nimport TooltipV2Trigger from './trigger.vue'\n\ndefineOptions({\n  name: 'ElTooltipV2',\n})\n\nconst props = defineProps(tooltipV2Props)\n\nconst refedProps = toRefs(props)\n\nconst arrowProps = reactive(pick(refedProps, Object.keys(tooltipV2ArrowProps)))\n\nconst contentProps = reactive(\n  pick(refedProps, Object.keys(tooltipV2ContentProps))\n)\n\nconst rootProps = reactive(pick(refedProps, Object.keys(tooltipV2RootProps)))\n\nconst triggerProps = reactive(\n  pick(refedProps, Object.keys(tooltipV2TriggerProps))\n)\n</script>\n"], "mappings": ";;;;;;;;;;;;;;;;;iCAsDc;EACZA,IAAM;AACR;;;;;IAIM,IAAAC,UAAA,GAAaC,MAAA,CAAOC,KAAK;IAEzB,IAAAC,UAAA,GAAaC,QAAA,CAASC,IAAK,CAAAL,UAAA,EAAYM,MAAA,CAAOC,IAAK,CAAAC,mBAAmB,CAAC,CAAC;IAExE,IAAAC,YAAA,GAAeL,QAAA,CACnBC,IAAK,CAAAL,UAAA,EAAYM,MAAA,CAAOC,IAAK,CAAAG,qBAAqB,CAAC,CACrD;IAEM,IAAAC,SAAA,GAAYP,QAAA,CAASC,IAAK,CAAAL,UAAA,EAAYM,MAAA,CAAOC,IAAK,CAAAK,kBAAkB,CAAC,CAAC;IAEtE,IAAAC,YAAA,GAAeT,QAAA,CACnBC,IAAK,CAAAL,UAAA,EAAYM,MAAA,CAAOC,IAAK,CAAAO,qBAAqB,CAAC,CACrD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
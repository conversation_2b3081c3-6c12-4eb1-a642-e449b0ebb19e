{"ast": null, "code": "import { createElementVNode as _createElementVNode, resolveComponent as _resolveComponent, createVNode as _createVNode, withCtx as _withCtx, createCommentVNode as _createCommentVNode, normalizeStyle as _normalizeStyle, renderList as _renderList, Fragment as _Fragment, openBlock as _openBlock, createElementBlock as _createElementBlock, toDisplayString as _toDisplayString, normalizeClass as _normalizeClass } from \"vue\";\nvar _hoisted_1 = {\n  class: \"virtual-select-user\",\n  ref: \"virtualList\"\n};\nvar _hoisted_2 = {\n  class: \"virtual-placeholder\",\n  ref: \"virtualPlaceholder\"\n};\nvar _hoisted_3 = {\n  class: \"virtual-select-user-item\"\n};\nvar _hoisted_4 = {\n  class: \"virtual-select-user-del\"\n};\nvar _hoisted_5 = [\"title\"];\nvar _hoisted_6 = {\n  class: \"virtual-select-user-text ellipsis\"\n};\nvar _hoisted_7 = [\"onClick\"];\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  var _component_Close = _resolveComponent(\"Close\");\n  var _component_el_icon = _resolveComponent(\"el-icon\");\n  var _component_el_scrollbar = _resolveComponent(\"el-scrollbar\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createElementVNode(\"div\", _hoisted_2, [_createElementVNode(\"div\", _hoisted_3, [_cache[0] || (_cache[0] = _createElementVNode(\"div\", {\n    class: \"virtual-select-user-name ellipsis\"\n  }, null, -1 /* HOISTED */)), _cache[1] || (_cache[1] = _createElementVNode(\"div\", {\n    class: \"virtual-select-user-text ellipsis\"\n  }, null, -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_4, [_createVNode(_component_el_icon, null, {\n    default: _withCtx(function () {\n      return [_createVNode(_component_Close)];\n    }),\n    _: 1 /* STABLE */\n  })])])], 512 /* NEED_PATCH */), _createVNode(_component_el_scrollbar, {\n    onScroll: $setup.handleScroll,\n    ref: \"virtualScrollbar\"\n  }, {\n    default: _withCtx(function () {\n      return [_createCommentVNode(\" 虚拟高度 \"), _createElementVNode(\"div\", {\n        class: \"virtualBody\",\n        style: _normalizeStyle({\n          height: $setup.virtualRecord.virtualHeight + 'px'\n        })\n      }, null, 4 /* STYLE */), _createCommentVNode(\" 真实列表 \"), _createElementVNode(\"div\", {\n        class: _normalizeClass(['realBody']),\n        style: _normalizeStyle({\n          transform: `translateY(${$setup.virtualRecord.offset}px)`\n        })\n      }, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.virtualRecord.visibleData, function (item, index) {\n        return _openBlock(), _createElementBlock(\"div\", {\n          class: \"virtual-select-user-item\",\n          key: index + 'virtual-select-user_'\n        }, [_createElementVNode(\"div\", {\n          class: \"virtual-select-user-name ellipsis\",\n          title: `${item.userName} - ${$setup.handleMobile(item.mobile)}`\n        }, _toDisplayString(item.userName) + \" - \" + _toDisplayString($setup.handleMobile(item.mobile)), 9 /* TEXT, PROPS */, _hoisted_5), _createElementVNode(\"div\", _hoisted_6, _toDisplayString(item.position), 1 /* TEXT */), _createElementVNode(\"div\", {\n          class: \"virtual-select-user-del\",\n          onClick: function onClick($event) {\n            return $setup.deleteclick(item);\n          }\n        }, [_createVNode(_component_el_icon, null, {\n          default: _withCtx(function () {\n            return [_createVNode(_component_Close)];\n          }),\n          _: 1 /* STABLE */\n        })], 8 /* PROPS */, _hoisted_7)]);\n      }), 128 /* KEYED_FRAGMENT */))], 4 /* STYLE */)];\n    }),\n    _: 1 /* STABLE */\n  }, 512 /* NEED_PATCH */)], 512 /* NEED_PATCH */);\n}", "map": {"version": 3, "names": ["class", "ref", "_createElementBlock", "_hoisted_1", "_createElementVNode", "_hoisted_2", "_hoisted_3", "_hoisted_4", "_createVNode", "_component_el_icon", "default", "_withCtx", "_component_Close", "_", "_component_el_scrollbar", "onScroll", "$setup", "handleScroll", "_createCommentVNode", "style", "_normalizeStyle", "height", "virtualRecord", "virtualHeight", "_normalizeClass", "transform", "offset", "_Fragment", "_renderList", "visibleData", "item", "index", "key", "title", "userName", "handleMobile", "mobile", "_toDisplayString", "_hoisted_5", "_hoisted_6", "position", "onClick", "$event", "deleteclick", "_hoisted_7"], "sources": ["D:\\zy\\xm\\pc\\5.0\\qiankun-product\\common\\components\\virtualElement\\virtual-select-user.vue"], "sourcesContent": ["<template>\r\n  <div class=\"virtual-select-user\" ref=\"virtualList\">\r\n    <div class=\"virtual-placeholder\" ref=\"virtualPlaceholder\">\r\n      <div class=\"virtual-select-user-item\">\r\n        <div class=\"virtual-select-user-name ellipsis\"></div>\r\n        <div class=\"virtual-select-user-text ellipsis\"></div>\r\n        <div class=\"virtual-select-user-del\">\r\n          <el-icon>\r\n            <Close />\r\n          </el-icon>\r\n        </div>\r\n      </div>\r\n    </div>\r\n    <el-scrollbar @scroll=\"handleScroll\" ref=\"virtualScrollbar\">\r\n      <!-- 虚拟高度 -->\r\n      <div class=\"virtualBody\" :style=\"{ height: virtualRecord.virtualHeight + 'px' }\"></div>\r\n      <!-- 真实列表 -->\r\n      <div :class=\"['realBody']\" :style=\"{ transform: `translateY(${virtualRecord.offset}px)` }\">\r\n        <div\r\n          class=\"virtual-select-user-item\"\r\n          v-for=\"(item, index) in virtualRecord.visibleData\"\r\n          :key=\"index + 'virtual-select-user_'\">\r\n          <div class=\"virtual-select-user-name ellipsis\" :title=\"`${item.userName} - ${handleMobile(item.mobile)}`\">\r\n            {{ item.userName }} - {{ handleMobile(item.mobile) }}\r\n          </div>\r\n          <div class=\"virtual-select-user-text ellipsis\">{{ item.position }}</div>\r\n          <div class=\"virtual-select-user-del\" @click=\"deleteclick(item)\">\r\n            <el-icon>\r\n              <Close />\r\n            </el-icon>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </el-scrollbar>\r\n  </div>\r\n</template>\r\n<script>\r\nexport default { name: 'VirtualSelectUser' }\r\n</script>\r\n<script setup>\r\nimport { ref, reactive, watch, nextTick, onMounted } from 'vue'\r\nimport { encryptPhone } from 'common/js/utils.js'\r\nimport { systemMobileEncrypt } from 'common/js/system_var.js'\r\nimport elementResizeDetectorMaker from 'element-resize-detector'\r\nconst erd = elementResizeDetectorMaker()\r\nconst props = defineProps({ data: { type: Array, default: () => [] } })\r\nconst emit = defineEmits(['handleDel'])\r\nconst virtualList = ref()\r\nconst virtualScrollbar = ref()\r\nconst virtualPlaceholder = ref()\r\n// 组件记录(默认)\r\nconst virtualRecord = reactive({\r\n  height: 400,\r\n  // 展示几个\r\n  visibleCount: 16,\r\n  // 刷新频率\r\n  timeout: 4,\r\n  // 行高\r\n  itemHeight: 90,\r\n  // translateY偏移量\r\n  offset: 0,\r\n  // 虚拟占位高度\r\n  virtualHeight: 300,\r\n  // 记录滚动高度\r\n  recordScrollTop: 0,\r\n  dataList: [],\r\n  // 可展示的数据\r\n  visibleData: []\r\n})\r\n// 合并配置\r\nconst mergeFn = () => {\r\n  virtualRecord.dataList = JSON.parse(JSON.stringify(props.data))\r\n  // 虚拟高度\r\n  virtualRecord.virtualHeight = props.data.length * virtualRecord.itemHeight\r\n  // 展示数量\r\n  virtualRecord.visibleCount = Math.ceil(virtualRecord.height / virtualRecord.itemHeight)\r\n}\r\nlet lastTime = 0\r\nconst handleScroll = (scroll) => {\r\n  const currentTime = +new Date()\r\n  if (currentTime - lastTime > virtualRecord.timeout) {\r\n    virtualRecord.recordScrollTop = scroll.scrollTop\r\n    updateVisibleData(scroll.scrollTop)\r\n    lastTime = currentTime\r\n  }\r\n}\r\nconst updateVisibleData = (scrollTop) => {\r\n  let start = Math.floor(scrollTop / virtualRecord.itemHeight) - Math.floor(virtualRecord.visibleCount / 2)\r\n  start = start < 0 ? 0 : start\r\n  const end = start + virtualRecord.visibleCount * 2\r\n  virtualRecord.visibleData = virtualRecord.dataList.slice(start, end)\r\n  virtualRecord.offset = start * virtualRecord.itemHeight\r\n  nextTick(() => {\r\n    virtualScrollbar.value.update()\r\n  })\r\n}\r\nconst deleteclick = (data) => {\r\n  emit('handleDel', data)\r\n}\r\nconst handleMobile = (mobile) => (systemMobileEncrypt.value ? encryptPhone(mobile) : mobile)\r\nwatch(\r\n  () => props.data,\r\n  () => {\r\n    // 合并数据\r\n    mergeFn()\r\n    // 更新视图\r\n    updateVisibleData(virtualRecord.recordScrollTop)\r\n  },\r\n  { immediate: true, deep: true }\r\n)\r\nonMounted(() => {\r\n  nextTick(() => {\r\n    erd.listenTo(virtualList.value, (e) => {\r\n      virtualRecord.height = e.offsetHeight\r\n      // 合并数据\r\n      mergeFn()\r\n      // 更新视图\r\n      updateVisibleData(virtualRecord.recordScrollTop)\r\n    })\r\n    erd.listenTo(virtualPlaceholder.value, (e) => {\r\n      virtualRecord.itemHeight = e.offsetHeight\r\n      // 合并数据\r\n      mergeFn()\r\n      // 更新视图\r\n      updateVisibleData(virtualRecord.recordScrollTop)\r\n    })\r\n  })\r\n})\r\n</script>\r\n<style lang=\"scss\">\r\n.virtual-select-user {\r\n  width: 100%;\r\n  height: calc(100% - (var(--zy-height) + var(--zy-distance-three)));\r\n\r\n  .virtual-placeholder {\r\n    position: fixed;\r\n    top: -200%;\r\n    left: -200%;\r\n  }\r\n\r\n  .zy-el-scrollbar {\r\n    height: 100%;\r\n\r\n    .zy-el-scrollbar__view {\r\n      position: relative;\r\n    }\r\n\r\n    .virtualBody {\r\n      width: 100%;\r\n      position: absolute;\r\n      z-index: -10;\r\n    }\r\n\r\n    .realBody {\r\n      width: 100%;\r\n      position: absolute;\r\n    }\r\n  }\r\n\r\n  .virtual-select-user-item + .virtual-select-user-item {\r\n    margin-top: 12px;\r\n  }\r\n\r\n  .virtual-select-user-item {\r\n    width: 290px;\r\n    background: var(--el-color-info-light-9);\r\n    padding: var(--zy-distance-five) var(--zy-distance-one);\r\n    cursor: pointer;\r\n    position: relative;\r\n    border-radius: var(--el-border-radius-small);\r\n\r\n    &::after {\r\n      content: '';\r\n      position: absolute;\r\n      top: calc(var(--zy-distance-five) + var(--zy-font-text-distance-five));\r\n      left: var(--zy-distance-two);\r\n      transform: translateX(-50%);\r\n      width: var(--zy-text-font-size);\r\n      height: calc(var(--zy-text-font-size) * var(--zy-line-height));\r\n      background: url('./img/select_person_user_icon.png') no-repeat;\r\n      background-size: var(--zy-text-font-size) var(--zy-text-font-size);\r\n      background-position: center;\r\n    }\r\n\r\n    .virtual-select-user-del {\r\n      position: absolute;\r\n      top: 0;\r\n      right: 0;\r\n      width: var(--zy-distance-one);\r\n      height: 100%;\r\n      padding-top: var(--zy-distance-five);\r\n      text-align: center;\r\n      font-size: var(--zy-navigation-font-size);\r\n      line-height: var(--zy-line-height);\r\n    }\r\n\r\n    .virtual-select-user-name {\r\n      font-size: var(--zy-text-font-size);\r\n      line-height: var(--zy-line-height);\r\n      padding: var(--zy-font-text-distance-five) 0;\r\n      height: calc(var(--zy-text-font-size) * var(--zy-line-height));\r\n      box-sizing: content-box;\r\n    }\r\n\r\n    .virtual-select-user-text {\r\n      font-size: var(--zy-text-font-size);\r\n      line-height: var(--zy-line-height);\r\n      padding: var(--zy-font-text-distance-five) 0;\r\n      height: calc(var(--zy-text-font-size) * var(--zy-line-height));\r\n      color: var(--zy-el-text-color-secondary);\r\n      box-sizing: content-box;\r\n    }\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;EACOA,KAAK,EAAC,qBAAqB;EAACC,GAAG,EAAC;;;EAC9BD,KAAK,EAAC,qBAAqB;EAACC,GAAG,EAAC;;;EAC9BD,KAAK,EAAC;AAA0B;;EAG9BA,KAAK,EAAC;AAAyB;iBAN5C;;EAyBeA,KAAK,EAAC;AAAmC;iBAzBxD;;;;;uBACEE,mBAAA,CAiCM,OAjCNC,UAiCM,GAhCJC,mBAAA,CAUM,OAVNC,UAUM,GATJD,mBAAA,CAQM,OARNE,UAQM,G,0BAPJF,mBAAA,CAAqD;IAAhDJ,KAAK,EAAC;EAAmC,6B,0BAC9CI,mBAAA,CAAqD;IAAhDJ,KAAK,EAAC;EAAmC,6BAC9CI,mBAAA,CAIM,OAJNG,UAIM,GAHJC,YAAA,CAEUC,kBAAA;IATpBC,OAAA,EAAAC,QAAA,CAQY;MAAA,OAAS,CAATH,YAAA,CAASI,gBAAA,E;;IARrBC,CAAA;kCAaIL,YAAA,CAoBeM,uBAAA;IApBAC,QAAM,EAAEC,MAAA,CAAAC,YAAY;IAAEhB,GAAG,EAAC;;IAb7CS,OAAA,EAAAC,QAAA,CAcM;MAAA,OAAa,CAAbO,mBAAA,UAAa,EACbd,mBAAA,CAAuF;QAAlFJ,KAAK,EAAC,aAAa;QAAEmB,KAAK,EAfrCC,eAAA;UAAAC,MAAA,EAeiDL,MAAA,CAAAM,aAAa,CAACC,aAAa;QAAA;+BACtEL,mBAAA,UAAa,EACbd,mBAAA,CAeM;QAfAJ,KAAK,EAjBjBwB,eAAA,CAiBmB,YAAY;QAAGL,KAAK,EAjBvCC,eAAA;UAAAK,SAAA,gBAiBoET,MAAA,CAAAM,aAAa,CAACI,MAAM;QAAA;6BAChFxB,mBAAA,CAaMyB,SAAA,QA/BdC,WAAA,CAoBkCZ,MAAA,CAAAM,aAAa,CAACO,WAAW,EApB3D,UAoBkBC,IAAI,EAAEC,KAAK;6BAFrB7B,mBAAA,CAaM;UAZJF,KAAK,EAAC,0BAA0B;UAE/BgC,GAAG,EAAED,KAAK;YACX3B,mBAAA,CAEM;UAFDJ,KAAK,EAAC,mCAAmC;UAAEiC,KAAK,KAAKH,IAAI,CAACI,QAAQ,MAAMlB,MAAA,CAAAmB,YAAY,CAACL,IAAI,CAACM,MAAM;4BAChGN,IAAI,CAACI,QAAQ,IAAG,KAAG,GAAAG,gBAAA,CAAGrB,MAAA,CAAAmB,YAAY,CAACL,IAAI,CAACM,MAAM,yBAvB7DE,UAAA,GAyBUlC,mBAAA,CAAwE,OAAxEmC,UAAwE,EAAAF,gBAAA,CAAtBP,IAAI,CAACU,QAAQ,kBAC/DpC,mBAAA,CAIM;UAJDJ,KAAK,EAAC,yBAAyB;UAAEyC,OAAK,WAALA,OAAKA,CAAAC,MAAA;YAAA,OAAE1B,MAAA,CAAA2B,WAAW,CAACb,IAAI;UAAA;YAC3DtB,YAAA,CAEUC,kBAAA;UA7BtBC,OAAA,EAAAC,QAAA,CA4Bc;YAAA,OAAS,CAATH,YAAA,CAASI,gBAAA,E;;UA5BvBC,CAAA;4BAAA+B,UAAA,E;;;IAAA/B,CAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
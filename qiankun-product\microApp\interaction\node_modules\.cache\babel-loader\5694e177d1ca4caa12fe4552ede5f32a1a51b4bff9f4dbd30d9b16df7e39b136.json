{"ast": null, "code": "function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nfunction _defineProperty(e, r, t) { return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, { value: t, enumerable: !0, configurable: !0, writable: !0 }) : e[r] = t, e; }\nfunction _toPropertyKey(t) { var i = _toPrimitive(t, \"string\"); return \"symbol\" == typeof i ? i : i + \"\"; }\nfunction _toPrimitive(t, r) { if (\"object\" != typeof t || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || \"default\"); if (\"object\" != typeof i) return i; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (\"string\" === r ? String : Number)(t); }\nimport { defineComponent, inject, computed, openBlock, createElementBlock, normalizeStyle, unref, normalizeClass } from 'vue';\nimport { tooltipV2RootKey, tooltipV2ContentKey } from './constants.mjs';\nimport { tooltipV2ArrowProps, tooltipV2ArrowSpecialProps } from './arrow.mjs';\nimport _export_sfc from '../../../_virtual/plugin-vue_export-helper.mjs';\nvar __default__ = defineComponent({\n  name: \"ElTooltipV2Arrow\"\n});\nvar _sfc_main = /* @__PURE__ */defineComponent(_objectSpread(_objectSpread({}, __default__), {}, {\n  props: _objectSpread(_objectSpread({}, tooltipV2ArrowProps), tooltipV2ArrowSpecialProps),\n  setup(__props) {\n    var props = __props;\n    var _inject = inject(tooltipV2RootKey),\n      ns = _inject.ns;\n    var _inject2 = inject(tooltipV2ContentKey),\n      arrowRef = _inject2.arrowRef;\n    var arrowStyle = computed(function () {\n      var style = props.style,\n        width = props.width,\n        height = props.height;\n      var namespace = ns.namespace.value;\n      return _objectSpread({\n        [`--${namespace}-tooltip-v2-arrow-width`]: `${width}px`,\n        [`--${namespace}-tooltip-v2-arrow-height`]: `${height}px`,\n        [`--${namespace}-tooltip-v2-arrow-border-width`]: `${width / 2}px`,\n        [`--${namespace}-tooltip-v2-arrow-cover-width`]: width / 2 - 1\n      }, style || {});\n    });\n    return function (_ctx, _cache) {\n      return openBlock(), createElementBlock(\"span\", {\n        ref_key: \"arrowRef\",\n        ref: arrowRef,\n        style: normalizeStyle(unref(arrowStyle)),\n        class: normalizeClass(unref(ns).e(\"arrow\"))\n      }, null, 6);\n    };\n  }\n}));\nvar TooltipV2Arrow = /* @__PURE__ */_export_sfc(_sfc_main, [[\"__file\", \"arrow.vue\"]]);\nexport { TooltipV2Arrow as default };", "map": {"version": 3, "names": ["name", "_inject", "inject", "tooltipV2RootKey", "ns", "_inject2", "tooltipV2ContentKey", "arrowRef", "arrowStyle", "computed", "style", "props", "width", "height", "namespace", "value", "_objectSpread"], "sources": ["../../../../../../packages/components/tooltip-v2/src/arrow.vue"], "sourcesContent": ["<template>\n  <span ref=\"arrowRef\" :style=\"arrowStyle\" :class=\"ns.e('arrow')\" />\n</template>\n\n<script setup lang=\"ts\">\nimport { computed, inject } from 'vue'\nimport { tooltipV2ContentKey, tooltipV2RootKey } from './constants'\nimport { tooltipV2ArrowProps, tooltipV2ArrowSpecialProps } from './arrow'\n\nimport type { CSSProperties } from 'vue'\n\ndefineOptions({\n  name: 'ElTooltipV2Arrow',\n})\n\nconst props = defineProps({\n  ...tooltipV2ArrowProps,\n  ...tooltipV2ArrowSpecialProps,\n})\n\nconst { ns } = inject(tooltipV2RootKey)!\nconst { arrowRef } = inject(tooltipV2ContentKey)!\n\nconst arrowStyle = computed<CSSProperties>(() => {\n  const { style, width, height } = props\n  const namespace = ns.namespace.value\n\n  return {\n    [`--${namespace}-tooltip-v2-arrow-width`]: `${width}px`,\n    [`--${namespace}-tooltip-v2-arrow-height`]: `${height}px`,\n    [`--${namespace}-tooltip-v2-arrow-border-width`]: `${width / 2}px`,\n    [`--${namespace}-tooltip-v2-arrow-cover-width`]: width / 2 - 1,\n    ...(style || {}),\n  }\n})\n</script>\n"], "mappings": ";;;;;;;;;iCAWc;EACZA,IAAM;AACR;;;;;IAOM,IAAAC,OAAA,GAASC,MAAA,CAAOC,gBAAgB;MAA9BC,EAAO,GAAAH,OAAA,CAAPG,EAAO;IACT,IAAAC,QAAA,GAAeH,MAAA,CAAOI,mBAAmB;MAAvCC,QAAa,GAAAF,QAAA,CAAbE,QAAa;IAEf,IAAAC,UAAA,GAAaC,QAAA,CAAwB,YAAM;MACzC,IAAEC,KAAO,GAAkBC,KAAA,CAAzBD,KAAO;QAAAE,KAAA,GAAkBD,KAAA,CAAlBC,KAAA;QAAOC,MAAW,GAAAF,KAAA,CAAXE,MAAW;MAC3B,IAAAC,SAAA,GAAYV,EAAA,CAAGU,SAAU,CAAAC,KAAA;MAExB,OAAAC,aAAA;QACL,CAAC,KAAKF,SAAA,4BAAqC,GAAGF,KAAA;QAC9C,CAAC,KAAKE,SAAA,6BAAsC,GAAGD,MAAA;QAC/C,CAAC,KAAKC,SAA4C,sCAAGF,KAAQ;QAC7D,CAAC,KAAKE,SAA2C,kCAAAF,KAAA,GAAQ,CAAI;MAAA,GACzDF,KAAA,IAAS,EAAC;IAChB,CACD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
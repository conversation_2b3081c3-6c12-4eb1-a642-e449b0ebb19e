{"ast": null, "code": "import { resolveComponent as _resolveComponent, createVNode as _createVNode, withCtx as _withCtx, createTextVNode as _createTextVNode, createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock } from \"vue\";\nvar _hoisted_1 = {\n  class: \"MyCollectionType\"\n};\nvar _hoisted_2 = {\n  class: \"globalFormButton\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  var _component_el_input = _resolveComponent(\"el-input\");\n  var _component_el_form_item = _resolveComponent(\"el-form-item\");\n  var _component_el_tree_select = _resolveComponent(\"el-tree-select\");\n  var _component_el_button = _resolveComponent(\"el-button\");\n  var _component_el_form = _resolveComponent(\"el-form\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createVNode(_component_el_form, {\n    ref: \"formRef\",\n    model: $setup.form,\n    rules: $setup.rules,\n    inline: \"\",\n    \"label-position\": \"top\",\n    class: \"globalForm\"\n  }, {\n    default: _withCtx(function () {\n      return [_createVNode(_component_el_form_item, {\n        label: \"分类名称\",\n        prop: \"name\"\n      }, {\n        default: _withCtx(function () {\n          return [_createVNode(_component_el_input, {\n            modelValue: $setup.form.name,\n            \"onUpdate:modelValue\": _cache[0] || (_cache[0] = function ($event) {\n              return $setup.form.name = $event;\n            }),\n            placeholder: \"请输入分类名称\",\n            clearable: \"\"\n          }, null, 8 /* PROPS */, [\"modelValue\"])];\n        }),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_form_item, {\n        label: \"上级分类\"\n      }, {\n        default: _withCtx(function () {\n          return [_createVNode(_component_el_tree_select, {\n            modelValue: $setup.form.parentId,\n            \"onUpdate:modelValue\": _cache[1] || (_cache[1] = function ($event) {\n              return $setup.form.parentId = $event;\n            }),\n            data: $setup.typeData,\n            \"check-strictly\": \"\",\n            \"node-key\": \"id\",\n            \"render-after-expand\": false,\n            clearable: \"\"\n          }, null, 8 /* PROPS */, [\"modelValue\", \"data\"])];\n        }),\n        _: 1 /* STABLE */\n      }), _createElementVNode(\"div\", _hoisted_2, [_createVNode(_component_el_button, {\n        type: \"primary\",\n        onClick: _cache[2] || (_cache[2] = function ($event) {\n          return $setup.submitForm($setup.formRef);\n        })\n      }, {\n        default: _withCtx(function () {\n          return _cache[3] || (_cache[3] = [_createTextVNode(\"提交\")]);\n        }),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_button, {\n        onClick: $setup.resetForm\n      }, {\n        default: _withCtx(function () {\n          return _cache[4] || (_cache[4] = [_createTextVNode(\"取消\")]);\n        }),\n        _: 1 /* STABLE */\n      })])];\n    }),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"model\", \"rules\"])]);\n}", "map": {"version": 3, "names": ["class", "_createElementBlock", "_hoisted_1", "_createVNode", "_component_el_form", "ref", "model", "$setup", "form", "rules", "inline", "default", "_withCtx", "_component_el_form_item", "label", "prop", "_component_el_input", "modelValue", "name", "_cache", "$event", "placeholder", "clearable", "_", "_component_el_tree_select", "parentId", "data", "typeData", "_createElementVNode", "_hoisted_2", "_component_el_button", "type", "onClick", "submitForm", "formRef", "_createTextVNode", "resetForm"], "sources": ["D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\interaction\\src\\views\\MyCollection\\component\\MyCollectionType.vue"], "sourcesContent": ["<template>\r\n  <div class=\"MyCollectionType\">\r\n    <el-form ref=\"formRef\"\r\n             :model=\"form\"\r\n             :rules=\"rules\"\r\n             inline\r\n             label-position=\"top\"\r\n             class=\"globalForm\">\r\n      <el-form-item label=\"分类名称\"\r\n                    prop=\"name\">\r\n        <el-input v-model=\"form.name\"\r\n                  placeholder=\"请输入分类名称\"\r\n                  clearable />\r\n      </el-form-item>\r\n      <el-form-item label=\"上级分类\">\r\n        <el-tree-select v-model=\"form.parentId\"\r\n                        :data=\"typeData\"\r\n                        check-strictly\r\n                        node-key=\"id\"\r\n                        :render-after-expand=\"false\"\r\n                        clearable />\r\n      </el-form-item>\r\n      <div class=\"globalFormButton\">\r\n        <el-button type=\"primary\"\r\n                   @click=\"submitForm(formRef)\">提交</el-button>\r\n        <el-button @click=\"resetForm\">取消</el-button>\r\n      </div>\r\n    </el-form>\r\n  </div>\r\n</template>\r\n<script>\r\nexport default { name: 'MyCollectionType' }\r\n</script>\r\n<script setup>\r\nimport api from '@/api'\r\nimport { reactive, ref, onMounted } from 'vue'\r\nimport { ElMessage } from 'element-plus'\r\nconst props = defineProps({ id: { type: String, default: '' }, typeId: { type: String, default: '' } })\r\nconst emit = defineEmits(['callback'])\r\n\r\nconst formRef = ref()\r\nconst form = reactive({\r\n  parentId: '', // 上级分类\r\n  name: '' // 分类名称\r\n})\r\nconst rules = reactive({ name: [{ required: true, message: '请输入分类名称', trigger: ['blur', 'change'] }] })\r\n\r\nconst typeData = ref()\r\nonMounted(() => {\r\n  favoriteFolderList()\r\n  if (props.id) { favoriteFolderInfo() }\r\n})\r\n\r\nconst favoriteFolderList = async () => {\r\n  const res = await api.favoriteFolderList()\r\n  var { data } = res\r\n  typeData.value = props.id ? filterData(data) : data\r\n}\r\nconst filterData = (list) => {\r\n  return list.filter(item => {\r\n    return item.id !== props.id\r\n  }).map(item => {\r\n    item = Object.assign({}, item)\r\n    if (item.children) {\r\n      item.children = filterData(item.children)\r\n    }\r\n    return item\r\n  })\r\n}\r\nconst favoriteFolderInfo = async () => {\r\n  const res = await api.favoriteFolderInfo({ detailId: props.id })\r\n  var { data } = res\r\n  form.name = data.name\r\n  form.parentId = data.parentId === '0' ? '' : data.parentId\r\n}\r\nconst submitForm = async (formEl) => {\r\n  if (!formEl) return\r\n  await formEl.validate((valid, fields) => {\r\n    if (valid) { globalJson() } else { ElMessage({ type: 'warning', message: '请根据提示信息完善字段内容！' }) }\r\n  })\r\n}\r\nconst globalJson = async () => {\r\n  const { code } = await api.globalJson(props.id ? '/favoriteFolder/edit' : '/favoriteFolder/add', {\r\n    form: {\r\n      id: props.id,\r\n      name: form.name,\r\n      parentId: form.parentId || '0'\r\n    }\r\n  })\r\n  if (code === 200) {\r\n    ElMessage({ type: 'success', message: props.id ? '编辑成功' : '新增成功' })\r\n    emit('callback')\r\n  }\r\n}\r\nconst resetForm = () => { emit('callback') }\r\n</script>\r\n<style lang=\"scss\">\r\n.MyCollectionType {\r\n  width: 680px;\r\n}\r\n</style>\r\n"], "mappings": ";;EACOA,KAAK,EAAC;AAAkB;;EAqBpBA,KAAK,EAAC;AAAkB;;;;;;;uBArBjCC,mBAAA,CA2BM,OA3BNC,UA2BM,GA1BJC,YAAA,CAyBUC,kBAAA;IAzBDC,GAAG,EAAC,SAAS;IACZC,KAAK,EAAEC,MAAA,CAAAC,IAAI;IACXC,KAAK,EAAEF,MAAA,CAAAE,KAAK;IACbC,MAAM,EAAN,EAAM;IACN,gBAAc,EAAC,KAAK;IACpBV,KAAK,EAAC;;IAPnBW,OAAA,EAAAC,QAAA,CAQM;MAAA,OAKe,CALfT,YAAA,CAKeU,uBAAA;QALDC,KAAK,EAAC,MAAM;QACZC,IAAI,EAAC;;QATzBJ,OAAA,EAAAC,QAAA,CAUQ;UAAA,OAEsB,CAFtBT,YAAA,CAEsBa,mBAAA;YAZ9BC,UAAA,EAU2BV,MAAA,CAAAC,IAAI,CAACU,IAAI;YAVpC,uBAAAC,MAAA,QAAAA,MAAA,gBAAAC,MAAA;cAAA,OAU2Bb,MAAA,CAAAC,IAAI,CAACU,IAAI,GAAAE,MAAA;YAAA;YAClBC,WAAW,EAAC,SAAS;YACrBC,SAAS,EAAT;;;QAZlBC,CAAA;UAcMpB,YAAA,CAOeU,uBAAA;QAPDC,KAAK,EAAC;MAAM;QAdhCH,OAAA,EAAAC,QAAA,CAeQ;UAAA,OAK4B,CAL5BT,YAAA,CAK4BqB,yBAAA;YApBpCP,UAAA,EAeiCV,MAAA,CAAAC,IAAI,CAACiB,QAAQ;YAf9C,uBAAAN,MAAA,QAAAA,MAAA,gBAAAC,MAAA;cAAA,OAeiCb,MAAA,CAAAC,IAAI,CAACiB,QAAQ,GAAAL,MAAA;YAAA;YACrBM,IAAI,EAAEnB,MAAA,CAAAoB,QAAQ;YACf,gBAAc,EAAd,EAAc;YACd,UAAQ,EAAC,IAAI;YACZ,qBAAmB,EAAE,KAAK;YAC3BL,SAAS,EAAT;;;QApBxBC,CAAA;UAsBMK,mBAAA,CAIM,OAJNC,UAIM,GAHJ1B,YAAA,CACsD2B,oBAAA;QAD3CC,IAAI,EAAC,SAAS;QACbC,OAAK,EAAAb,MAAA,QAAAA,MAAA,gBAAAC,MAAA;UAAA,OAAEb,MAAA,CAAA0B,UAAU,CAAC1B,MAAA,CAAA2B,OAAO;QAAA;;QAxB7CvB,OAAA,EAAAC,QAAA,CAwBgD;UAAA,OAAEO,MAAA,QAAAA,MAAA,OAxBlDgB,gBAAA,CAwBgD,IAAE,E;;QAxBlDZ,CAAA;UAyBQpB,YAAA,CAA4C2B,oBAAA;QAAhCE,OAAK,EAAEzB,MAAA,CAAA6B;MAAS;QAzBpCzB,OAAA,EAAAC,QAAA,CAyBsC;UAAA,OAAEO,MAAA,QAAAA,MAAA,OAzBxCgB,gBAAA,CAyBsC,IAAE,E;;QAzBxCZ,CAAA;;;IAAAA,CAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
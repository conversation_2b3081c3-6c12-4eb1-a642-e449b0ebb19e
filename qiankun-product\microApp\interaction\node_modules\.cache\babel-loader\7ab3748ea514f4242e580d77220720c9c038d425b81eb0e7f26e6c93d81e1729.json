{"ast": null, "code": "import arrayMap from './_arrayMap.js';\nimport baseClone from './_baseClone.js';\nimport baseUnset from './_baseUnset.js';\nimport castPath from './_castPath.js';\nimport copyObject from './_copyObject.js';\nimport customOmitClone from './_customOmitClone.js';\nimport flatRest from './_flatRest.js';\nimport getAllKeysIn from './_getAllKeysIn.js';\n\n/** Used to compose bitmasks for cloning. */\nvar CLONE_DEEP_FLAG = 1,\n  CLONE_FLAT_FLAG = 2,\n  CLONE_SYMBOLS_FLAG = 4;\n\n/**\n * The opposite of `_.pick`; this method creates an object composed of the\n * own and inherited enumerable property paths of `object` that are not omitted.\n *\n * **Note:** This method is considerably slower than `_.pick`.\n *\n * @static\n * @since 0.1.0\n * @memberOf _\n * @category Object\n * @param {Object} object The source object.\n * @param {...(string|string[])} [paths] The property paths to omit.\n * @returns {Object} Returns the new object.\n * @example\n *\n * var object = { 'a': 1, 'b': '2', 'c': 3 };\n *\n * _.omit(object, ['a', 'c']);\n * // => { 'b': '2' }\n */\nvar omit = flatRest(function (object, paths) {\n  var result = {};\n  if (object == null) {\n    return result;\n  }\n  var isDeep = false;\n  paths = arrayMap(paths, function (path) {\n    path = castPath(path, object);\n    isDeep || (isDeep = path.length > 1);\n    return path;\n  });\n  copyObject(object, getAllKeysIn(object), result);\n  if (isDeep) {\n    result = baseClone(result, CLONE_DEEP_FLAG | CLONE_FLAT_FLAG | CLONE_SYMBOLS_FLAG, customOmitClone);\n  }\n  var length = paths.length;\n  while (length--) {\n    baseUnset(result, paths[length]);\n  }\n  return result;\n});\nexport default omit;", "map": {"version": 3, "names": ["arrayMap", "baseClone", "baseUnset", "<PERSON><PERSON><PERSON>", "copyObject", "customOmitClone", "flatRest", "getAllKeysIn", "CLONE_DEEP_FLAG", "CLONE_FLAT_FLAG", "CLONE_SYMBOLS_FLAG", "omit", "object", "paths", "result", "isDeep", "path", "length"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/node_modules/.pnpm/lodash-es@4.17.21/node_modules/lodash-es/omit.js"], "sourcesContent": ["import arrayMap from './_arrayMap.js';\nimport baseClone from './_baseClone.js';\nimport baseUnset from './_baseUnset.js';\nimport castPath from './_castPath.js';\nimport copyObject from './_copyObject.js';\nimport customOmitClone from './_customOmitClone.js';\nimport flatRest from './_flatRest.js';\nimport getAllKeysIn from './_getAllKeysIn.js';\n\n/** Used to compose bitmasks for cloning. */\nvar CLONE_DEEP_FLAG = 1,\n    CLONE_FLAT_FLAG = 2,\n    CLONE_SYMBOLS_FLAG = 4;\n\n/**\n * The opposite of `_.pick`; this method creates an object composed of the\n * own and inherited enumerable property paths of `object` that are not omitted.\n *\n * **Note:** This method is considerably slower than `_.pick`.\n *\n * @static\n * @since 0.1.0\n * @memberOf _\n * @category Object\n * @param {Object} object The source object.\n * @param {...(string|string[])} [paths] The property paths to omit.\n * @returns {Object} Returns the new object.\n * @example\n *\n * var object = { 'a': 1, 'b': '2', 'c': 3 };\n *\n * _.omit(object, ['a', 'c']);\n * // => { 'b': '2' }\n */\nvar omit = flatRest(function(object, paths) {\n  var result = {};\n  if (object == null) {\n    return result;\n  }\n  var isDeep = false;\n  paths = arrayMap(paths, function(path) {\n    path = castPath(path, object);\n    isDeep || (isDeep = path.length > 1);\n    return path;\n  });\n  copyObject(object, getAllKeysIn(object), result);\n  if (isDeep) {\n    result = baseClone(result, CLONE_DEEP_FLAG | CLONE_FLAT_FLAG | CLONE_SYMBOLS_FLAG, customOmitClone);\n  }\n  var length = paths.length;\n  while (length--) {\n    baseUnset(result, paths[length]);\n  }\n  return result;\n});\n\nexport default omit;\n"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,gBAAgB;AACrC,OAAOC,SAAS,MAAM,iBAAiB;AACvC,OAAOC,SAAS,MAAM,iBAAiB;AACvC,OAAOC,QAAQ,MAAM,gBAAgB;AACrC,OAAOC,UAAU,MAAM,kBAAkB;AACzC,OAAOC,eAAe,MAAM,uBAAuB;AACnD,OAAOC,QAAQ,MAAM,gBAAgB;AACrC,OAAOC,YAAY,MAAM,oBAAoB;;AAE7C;AACA,IAAIC,eAAe,GAAG,CAAC;EACnBC,eAAe,GAAG,CAAC;EACnBC,kBAAkB,GAAG,CAAC;;AAE1B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIC,IAAI,GAAGL,QAAQ,CAAC,UAASM,MAAM,EAAEC,KAAK,EAAE;EAC1C,IAAIC,MAAM,GAAG,CAAC,CAAC;EACf,IAAIF,MAAM,IAAI,IAAI,EAAE;IAClB,OAAOE,MAAM;EACf;EACA,IAAIC,MAAM,GAAG,KAAK;EAClBF,KAAK,GAAGb,QAAQ,CAACa,KAAK,EAAE,UAASG,IAAI,EAAE;IACrCA,IAAI,GAAGb,QAAQ,CAACa,IAAI,EAAEJ,MAAM,CAAC;IAC7BG,MAAM,KAAKA,MAAM,GAAGC,IAAI,CAACC,MAAM,GAAG,CAAC,CAAC;IACpC,OAAOD,IAAI;EACb,CAAC,CAAC;EACFZ,UAAU,CAACQ,MAAM,EAAEL,YAAY,CAACK,MAAM,CAAC,EAAEE,MAAM,CAAC;EAChD,IAAIC,MAAM,EAAE;IACVD,MAAM,GAAGb,SAAS,CAACa,MAAM,EAAEN,eAAe,GAAGC,eAAe,GAAGC,kBAAkB,EAAEL,eAAe,CAAC;EACrG;EACA,IAAIY,MAAM,GAAGJ,KAAK,CAACI,MAAM;EACzB,OAAOA,MAAM,EAAE,EAAE;IACff,SAAS,CAACY,MAAM,EAAED,KAAK,CAACI,MAAM,CAAC,CAAC;EAClC;EACA,OAAOH,MAAM;AACf,CAAC,CAAC;AAEF,eAAeH,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
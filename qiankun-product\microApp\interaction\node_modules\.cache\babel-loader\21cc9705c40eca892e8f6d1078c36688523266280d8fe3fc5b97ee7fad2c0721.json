{"ast": null, "code": "function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nfunction _defineProperty(e, r, t) { return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, { value: t, enumerable: !0, configurable: !0, writable: !0 }) : e[r] = t, e; }\nfunction _toPropertyKey(t) { var i = _toPrimitive(t, \"string\"); return \"symbol\" == typeof i ? i : i + \"\"; }\nfunction _toPrimitive(t, r) { if (\"object\" != typeof t || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || \"default\"); if (\"object\" != typeof i) return i; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (\"string\" === r ? String : Number)(t); }\nimport '../../../../utils/index.mjs';\nimport { panelSharedProps } from './shared.mjs';\nimport { buildProps, definePropType } from '../../../../utils/vue/props/runtime.mjs';\nvar panelDatePickProps = buildProps(_objectSpread(_objectSpread({}, panelSharedProps), {}, {\n  parsedValue: {\n    type: definePropType([Object, Array])\n  },\n  visible: {\n    type: Boolean\n  },\n  format: {\n    type: String,\n    default: \"\"\n  }\n}));\nexport { panelDatePickProps };", "map": {"version": 3, "names": ["panelDatePickProps", "buildProps", "_objectSpread", "panelSharedProps", "parsedValue", "type", "definePropType", "Object", "Array", "visible", "Boolean", "format", "String", "default"], "sources": ["../../../../../../../packages/components/date-picker/src/props/panel-date-pick.ts"], "sourcesContent": ["import { buildProps, definePropType } from '@element-plus/utils'\nimport { panelSharedProps } from './shared'\n\nimport type { ExtractPropTypes } from 'vue'\nimport type { Dayjs } from 'dayjs'\n\nexport const panelDatePickProps = buildProps({\n  ...panelSharedProps,\n  parsedValue: {\n    type: definePropType<Dayjs | Dayjs[]>([Object, Array]),\n  },\n  visible: {\n    type: Boolean,\n  },\n  format: {\n    type: String,\n    default: '',\n  },\n} as const)\n\nexport type PanelDatePickProps = ExtractPropTypes<typeof panelDatePickProps>\n"], "mappings": ";;;;;;;;AAEY,IAACA,kBAAkB,GAAGC,UAAU,CAAAC,aAAA,CAAAA,aAAA,KACvCC,gBAAgB;EACnBC,WAAW,EAAE;IACXC,IAAI,EAAEC,cAAc,CAAC,CAACC,MAAM,EAAEC,KAAK,CAAC;EACxC,CAAG;EACDC,OAAO,EAAE;IACPJ,IAAI,EAAEK;EACV,CAAG;EACDC,MAAM,EAAE;IACNN,IAAI,EAAEO,MAAM;IACZC,OAAO,EAAE;EACb;AAAG,EACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "import { computed } from 'vue';\nvar __default__ = {\n  name: 'GlobalDynamicInput'\n};\nexport default /*@__PURE__*/Object.assign(__default__, {\n  props: {\n    modelValue: [String, Number],\n    disabled: {\n      type: Boolean,\n      default: false\n    }\n  },\n  emits: ['update:modelValue'],\n  setup(__props, _ref) {\n    var __expose = _ref.expose,\n      __emit = _ref.emit;\n    __expose();\n    var props = __props;\n    var emit = __emit;\n    var content = computed({\n      get() {\n        return props.modelValue;\n      },\n      set(value) {\n        emit('update:modelValue', value);\n      }\n    });\n    var inputWid = computed(function () {\n      if (!content.value) {\n        return '120px';\n      } else {\n        return props.disabled ? String(content.value).length * 6 + 60 + 'px' : String(content.value).length * 13 + 70 + 'px';\n      }\n    });\n    var __returned__ = {\n      props,\n      emit,\n      content,\n      inputWid,\n      computed\n    };\n    Object.defineProperty(__returned__, '__isScriptSetup', {\n      enumerable: false,\n      value: true\n    });\n    return __returned__;\n  }\n});", "map": {"version": 3, "names": ["computed", "__default__", "name", "props", "__props", "emit", "__emit", "content", "get", "modelValue", "set", "value", "inputWid", "disabled", "String", "length"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/common/components/global-dynamic-title/global-dynamic-input.vue"], "sourcesContent": ["<template>\r\n  <div class=\"global-dynamic-input\">\r\n    <el-input v-model=\"content\" :disabled=\"props.disabled\" :style=\"{ width: inputWid }\" />\r\n  </div>\r\n</template>\r\n<script>\r\nexport default { name: 'GlobalDynamicInput' }\r\n</script>\r\n<script setup>\r\nimport { computed } from 'vue'\r\nconst props = defineProps({\r\n  modelValue: [String, Number],\r\n  disabled: { type: Boolean, default: false }\r\n})\r\nconst emit = defineEmits(['update:modelValue'])\r\n\r\nconst content = computed({\r\n  get() {\r\n    return props.modelValue\r\n  },\r\n  set(value) {\r\n    emit('update:modelValue', value)\r\n  }\r\n})\r\n\r\nconst inputWid = computed(() => {\r\n  if (!content.value) {\r\n    return '120px'\r\n  } else {\r\n    return props.disabled ? String(content.value).length * 6 + 60 + 'px' : String(content.value).length * 13 + 70 + 'px'\r\n  }\r\n})\r\n</script>\r\n"], "mappings": "AASA,SAASA,QAAQ,QAAQ,KAAK;AAH9B,IAAAC,WAAA,GAAe;EAAEC,IAAI,EAAE;AAAqB,CAAC;;;;;;;;;;;;;;IAI7C,IAAMC,KAAK,GAAGC,OAGZ;IACF,IAAMC,IAAI,GAAGC,MAAkC;IAE/C,IAAMC,OAAO,GAAGP,QAAQ,CAAC;MACvBQ,GAAGA,CAAA,EAAG;QACJ,OAAOL,KAAK,CAACM,UAAU;MACzB,CAAC;MACDC,GAAGA,CAACC,KAAK,EAAE;QACTN,IAAI,CAAC,mBAAmB,EAAEM,KAAK,CAAC;MAClC;IACF,CAAC,CAAC;IAEF,IAAMC,QAAQ,GAAGZ,QAAQ,CAAC,YAAM;MAC9B,IAAI,CAACO,OAAO,CAACI,KAAK,EAAE;QAClB,OAAO,OAAO;MAChB,CAAC,MAAM;QACL,OAAOR,KAAK,CAACU,QAAQ,GAAGC,MAAM,CAACP,OAAO,CAACI,KAAK,CAAC,CAACI,MAAM,GAAG,CAAC,GAAG,EAAE,GAAG,IAAI,GAAGD,MAAM,CAACP,OAAO,CAACI,KAAK,CAAC,CAACI,MAAM,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI;MACtH;IACF,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
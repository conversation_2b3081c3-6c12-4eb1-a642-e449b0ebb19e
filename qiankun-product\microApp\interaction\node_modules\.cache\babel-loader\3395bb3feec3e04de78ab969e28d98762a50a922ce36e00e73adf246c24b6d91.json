{"ast": null, "code": "import Symbol from './_Symbol.js';\nimport isArguments from './isArguments.js';\nimport isArray from './isArray.js';\n\n/** Built-in value references. */\nvar spreadableSymbol = Symbol ? Symbol.isConcatSpreadable : undefined;\n\n/**\n * Checks if `value` is a flattenable `arguments` object or array.\n *\n * @private\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is flattenable, else `false`.\n */\nfunction isFlattenable(value) {\n  return isArray(value) || isArguments(value) || !!(spreadableSymbol && value && value[spreadableSymbol]);\n}\nexport default isFlattenable;", "map": {"version": 3, "names": ["Symbol", "isArguments", "isArray", "spreadableSymbol", "isConcatSpreadable", "undefined", "isFlattenable", "value"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/node_modules/.pnpm/lodash-es@4.17.21/node_modules/lodash-es/_isFlattenable.js"], "sourcesContent": ["import Symbol from './_Symbol.js';\nimport isArguments from './isArguments.js';\nimport isArray from './isArray.js';\n\n/** Built-in value references. */\nvar spreadableSymbol = Symbol ? Symbol.isConcatSpreadable : undefined;\n\n/**\n * Checks if `value` is a flattenable `arguments` object or array.\n *\n * @private\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is flattenable, else `false`.\n */\nfunction isFlattenable(value) {\n  return isArray(value) || isArguments(value) ||\n    !!(spreadableSymbol && value && value[spreadableSymbol]);\n}\n\nexport default isFlattenable;\n"], "mappings": "AAAA,OAAOA,MAAM,MAAM,cAAc;AACjC,OAAOC,WAAW,MAAM,kBAAkB;AAC1C,OAAOC,OAAO,MAAM,cAAc;;AAElC;AACA,IAAIC,gBAAgB,GAAGH,MAAM,GAAGA,MAAM,CAACI,kBAAkB,GAAGC,SAAS;;AAErE;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,aAAaA,CAACC,KAAK,EAAE;EAC5B,OAAOL,OAAO,CAACK,KAAK,CAAC,IAAIN,WAAW,CAACM,KAAK,CAAC,IACzC,CAAC,EAAEJ,gBAAgB,IAAII,KAAK,IAAIA,KAAK,CAACJ,gBAAgB,CAAC,CAAC;AAC5D;AAEA,eAAeG,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
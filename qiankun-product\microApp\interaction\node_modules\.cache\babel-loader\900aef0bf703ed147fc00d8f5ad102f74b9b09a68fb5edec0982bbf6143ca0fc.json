{"ast": null, "code": "function _toConsumableArray(r) { return _arrayWithoutHoles(r) || _iterableToArray(r) || _unsupportedIterableToArray(r) || _nonIterableSpread(); }\nfunction _nonIterableSpread() { throw new TypeError(\"Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); }\nfunction _unsupportedIterableToArray(r, a) { if (r) { if (\"string\" == typeof r) return _arrayLikeToArray(r, a); var t = {}.toString.call(r).slice(8, -1); return \"Object\" === t && r.constructor && (t = r.constructor.name), \"Map\" === t || \"Set\" === t ? Array.from(r) : \"Arguments\" === t || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t) ? _arrayLikeToArray(r, a) : void 0; } }\nfunction _iterableToArray(r) { if (\"undefined\" != typeof Symbol && null != r[Symbol.iterator] || null != r[\"@@iterator\"]) return Array.from(r); }\nfunction _arrayWithoutHoles(r) { if (Array.isArray(r)) return _arrayLikeToArray(r); }\nfunction _arrayLikeToArray(r, a) { (null == a || a > r.length) && (a = r.length); for (var e = 0, n = Array(a); e < a; e++) n[e] = r[e]; return n; }\nimport { inject } from 'vue';\nimport '../../../../hooks/index.mjs';\nimport { getFixedColumnOffset, ensurePosition, getFixedColumnsClass } from '../util.mjs';\nimport { TABLE_INJECTION_KEY } from '../tokens.mjs';\nimport { useNamespace } from '../../../../hooks/use-namespace/index.mjs';\nfunction useStyles(props) {\n  var parent = inject(TABLE_INJECTION_KEY);\n  var ns = useNamespace(\"table\");\n  var getRowStyle = function getRowStyle(row, rowIndex) {\n    var rowStyle = parent == null ? void 0 : parent.props.rowStyle;\n    if (typeof rowStyle === \"function\") {\n      return rowStyle.call(null, {\n        row,\n        rowIndex\n      });\n    }\n    return rowStyle || null;\n  };\n  var getRowClass = function getRowClass(row, rowIndex) {\n    var classes = [ns.e(\"row\")];\n    if ((parent == null ? void 0 : parent.props.highlightCurrentRow) && row === props.store.states.currentRow.value) {\n      classes.push(\"current-row\");\n    }\n    if (props.stripe && rowIndex % 2 === 1) {\n      classes.push(ns.em(\"row\", \"striped\"));\n    }\n    var rowClassName = parent == null ? void 0 : parent.props.rowClassName;\n    if (typeof rowClassName === \"string\") {\n      classes.push(rowClassName);\n    } else if (typeof rowClassName === \"function\") {\n      classes.push(rowClassName.call(null, {\n        row,\n        rowIndex\n      }));\n    }\n    return classes;\n  };\n  var getCellStyle = function getCellStyle(rowIndex, columnIndex, row, column) {\n    var cellStyle = parent == null ? void 0 : parent.props.cellStyle;\n    var cellStyles = cellStyle != null ? cellStyle : {};\n    if (typeof cellStyle === \"function\") {\n      cellStyles = cellStyle.call(null, {\n        rowIndex,\n        columnIndex,\n        row,\n        column\n      });\n    }\n    var fixedStyle = getFixedColumnOffset(columnIndex, props == null ? void 0 : props.fixed, props.store);\n    ensurePosition(fixedStyle, \"left\");\n    ensurePosition(fixedStyle, \"right\");\n    return Object.assign({}, cellStyles, fixedStyle);\n  };\n  var getCellClass = function getCellClass(rowIndex, columnIndex, row, column, offset) {\n    var fixedClasses = getFixedColumnsClass(ns.b(), columnIndex, props == null ? void 0 : props.fixed, props.store, void 0, offset);\n    var classes = [column.id, column.align, column.className].concat(_toConsumableArray(fixedClasses));\n    var cellClassName = parent == null ? void 0 : parent.props.cellClassName;\n    if (typeof cellClassName === \"string\") {\n      classes.push(cellClassName);\n    } else if (typeof cellClassName === \"function\") {\n      classes.push(cellClassName.call(null, {\n        rowIndex,\n        columnIndex,\n        row,\n        column\n      }));\n    }\n    classes.push(ns.e(\"cell\"));\n    return classes.filter(function (className) {\n      return Boolean(className);\n    }).join(\" \");\n  };\n  var getSpan = function getSpan(row, column, rowIndex, columnIndex) {\n    var rowspan = 1;\n    var colspan = 1;\n    var fn = parent == null ? void 0 : parent.props.spanMethod;\n    if (typeof fn === \"function\") {\n      var result = fn({\n        row,\n        column,\n        rowIndex,\n        columnIndex\n      });\n      if (Array.isArray(result)) {\n        rowspan = result[0];\n        colspan = result[1];\n      } else if (typeof result === \"object\") {\n        rowspan = result.rowspan;\n        colspan = result.colspan;\n      }\n    }\n    return {\n      rowspan,\n      colspan\n    };\n  };\n  var getColspanRealWidth = function getColspanRealWidth(columns, colspan, index) {\n    if (colspan < 1) {\n      return columns[index].realWidth;\n    }\n    var widthArr = columns.map(function (_ref) {\n      var realWidth = _ref.realWidth,\n        width = _ref.width;\n      return realWidth || width;\n    }).slice(index, index + colspan);\n    return Number(widthArr.reduce(function (acc, width) {\n      return Number(acc) + Number(width);\n    }, -1));\n  };\n  return {\n    getRowStyle,\n    getRowClass,\n    getCellStyle,\n    getCellClass,\n    getSpan,\n    getColspanRealWidth\n  };\n}\nexport { useStyles as default };", "map": {"version": 3, "names": ["useStyles", "props", "parent", "inject", "TABLE_INJECTION_KEY", "ns", "useNamespace", "getRowStyle", "row", "rowIndex", "rowStyle", "call", "getRowClass", "classes", "e", "highlightCurrentRow", "store", "states", "currentRow", "value", "push", "stripe", "em", "rowClassName", "getCellStyle", "columnIndex", "column", "cellStyle", "cellStyles", "fixedStyle", "getFixedColumnOffset", "fixed", "ensurePosition", "Object", "assign", "getCellClass", "offset", "fixedClasses", "getFixedColumnsClass", "b", "id", "align", "className", "concat", "_toConsumableArray", "cellClassName", "filter", "Boolean", "join", "getSpan", "rowspan", "colspan", "fn", "spanMethod", "result", "Array", "isArray", "getColspanRealWidth", "columns", "index", "realWidth", "widthArr", "map", "_ref", "width", "slice", "Number", "reduce", "acc"], "sources": ["../../../../../../../packages/components/table/src/table-body/styles-helper.ts"], "sourcesContent": ["// @ts-nocheck\nimport { inject } from 'vue'\nimport { useNamespace } from '@element-plus/hooks'\nimport {\n  ensurePosition,\n  getFixedColumnOffset,\n  getFixedColumnsClass,\n} from '../util'\nimport { TABLE_INJECTION_KEY } from '../tokens'\nimport type { TableColumnCtx } from '../table-column/defaults'\nimport type { TableBodyProps } from './defaults'\n\nfunction useStyles<T>(props: Partial<TableBodyProps<T>>) {\n  const parent = inject(TABLE_INJECTION_KEY)\n  const ns = useNamespace('table')\n\n  const getRowStyle = (row: T, rowIndex: number) => {\n    const rowStyle = parent?.props.rowStyle\n    if (typeof rowStyle === 'function') {\n      return rowStyle.call(null, {\n        row,\n        rowIndex,\n      })\n    }\n    return rowStyle || null\n  }\n\n  const getRowClass = (row: T, rowIndex: number) => {\n    const classes = [ns.e('row')]\n    if (\n      parent?.props.highlightCurrentRow &&\n      row === props.store.states.currentRow.value\n    ) {\n      classes.push('current-row')\n    }\n\n    if (props.stripe && rowIndex % 2 === 1) {\n      classes.push(ns.em('row', 'striped'))\n    }\n    const rowClassName = parent?.props.rowClassName\n    if (typeof rowClassName === 'string') {\n      classes.push(rowClassName)\n    } else if (typeof rowClassName === 'function') {\n      classes.push(\n        rowClassName.call(null, {\n          row,\n          rowIndex,\n        })\n      )\n    }\n    return classes\n  }\n\n  const getCellStyle = (\n    rowIndex: number,\n    columnIndex: number,\n    row: T,\n    column: TableColumnCtx<T>\n  ) => {\n    const cellStyle = parent?.props.cellStyle\n    let cellStyles = cellStyle ?? {}\n    if (typeof cellStyle === 'function') {\n      cellStyles = cellStyle.call(null, {\n        rowIndex,\n        columnIndex,\n        row,\n        column,\n      })\n    }\n    const fixedStyle = getFixedColumnOffset(\n      columnIndex,\n      props?.fixed,\n      props.store\n    )\n    ensurePosition(fixedStyle, 'left')\n    ensurePosition(fixedStyle, 'right')\n    return Object.assign({}, cellStyles, fixedStyle)\n  }\n\n  const getCellClass = (\n    rowIndex: number,\n    columnIndex: number,\n    row: T,\n    column: TableColumnCtx<T>,\n    offset: number\n  ) => {\n    const fixedClasses = getFixedColumnsClass(\n      ns.b(),\n      columnIndex,\n      props?.fixed,\n      props.store,\n      undefined,\n      offset\n    )\n    const classes = [column.id, column.align, column.className, ...fixedClasses]\n    const cellClassName = parent?.props.cellClassName\n    if (typeof cellClassName === 'string') {\n      classes.push(cellClassName)\n    } else if (typeof cellClassName === 'function') {\n      classes.push(\n        cellClassName.call(null, {\n          rowIndex,\n          columnIndex,\n          row,\n          column,\n        })\n      )\n    }\n    classes.push(ns.e('cell'))\n    return classes.filter((className) => Boolean(className)).join(' ')\n  }\n  const getSpan = (\n    row: T,\n    column: TableColumnCtx<T>,\n    rowIndex: number,\n    columnIndex: number\n  ) => {\n    let rowspan = 1\n    let colspan = 1\n    const fn = parent?.props.spanMethod\n    if (typeof fn === 'function') {\n      const result = fn({\n        row,\n        column,\n        rowIndex,\n        columnIndex,\n      })\n      if (Array.isArray(result)) {\n        rowspan = result[0]\n        colspan = result[1]\n      } else if (typeof result === 'object') {\n        rowspan = result.rowspan\n        colspan = result.colspan\n      }\n    }\n    return { rowspan, colspan }\n  }\n  const getColspanRealWidth = (\n    columns: TableColumnCtx<T>[],\n    colspan: number,\n    index: number\n  ): number => {\n    if (colspan < 1) {\n      return columns[index].realWidth\n    }\n    const widthArr = columns\n      .map(({ realWidth, width }) => realWidth || width)\n      .slice(index, index + colspan)\n    return Number(\n      widthArr.reduce((acc, width) => Number(acc) + Number(width), -1)\n    )\n  }\n\n  return {\n    getRowStyle,\n    getRowClass,\n    getCellStyle,\n    getCellClass,\n    getSpan,\n    getColspanRealWidth,\n  }\n}\n\nexport default useStyles\n"], "mappings": ";;;;;;;;;;;AAQA,SAASA,SAASA,CAACC,KAAK,EAAE;EACxB,IAAMC,MAAM,GAAGC,MAAM,CAACC,mBAAmB,CAAC;EAC1C,IAAMC,EAAE,GAAGC,YAAY,CAAC,OAAO,CAAC;EAChC,IAAMC,WAAW,GAAG,SAAdA,WAAWA,CAAIC,GAAG,EAAEC,QAAQ,EAAK;IACrC,IAAMC,QAAQ,GAAGR,MAAM,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,MAAM,CAACD,KAAK,CAACS,QAAQ;IAChE,IAAI,OAAOA,QAAQ,KAAK,UAAU,EAAE;MAClC,OAAOA,QAAQ,CAACC,IAAI,CAAC,IAAI,EAAE;QACzBH,GAAG;QACHC;MACR,CAAO,CAAC;IACR;IACI,OAAOC,QAAQ,IAAI,IAAI;EAC3B,CAAG;EACD,IAAME,WAAW,GAAG,SAAdA,WAAWA,CAAIJ,GAAG,EAAEC,QAAQ,EAAK;IACrC,IAAMI,OAAO,GAAG,CAACR,EAAE,CAACS,CAAC,CAAC,KAAK,CAAC,CAAC;IAC7B,IAAI,CAACZ,MAAM,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,MAAM,CAACD,KAAK,CAACc,mBAAmB,KAAKP,GAAG,KAAKP,KAAK,CAACe,KAAK,CAACC,MAAM,CAACC,UAAU,CAACC,KAAK,EAAE;MAC/GN,OAAO,CAACO,IAAI,CAAC,aAAa,CAAC;IACjC;IACI,IAAInB,KAAK,CAACoB,MAAM,IAAIZ,QAAQ,GAAG,CAAC,KAAK,CAAC,EAAE;MACtCI,OAAO,CAACO,IAAI,CAACf,EAAE,CAACiB,EAAE,CAAC,KAAK,EAAE,SAAS,CAAC,CAAC;IAC3C;IACI,IAAMC,YAAY,GAAGrB,MAAM,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,MAAM,CAACD,KAAK,CAACsB,YAAY;IACxE,IAAI,OAAOA,YAAY,KAAK,QAAQ,EAAE;MACpCV,OAAO,CAACO,IAAI,CAACG,YAAY,CAAC;IAChC,CAAK,MAAM,IAAI,OAAOA,YAAY,KAAK,UAAU,EAAE;MAC7CV,OAAO,CAACO,IAAI,CAACG,YAAY,CAACZ,IAAI,CAAC,IAAI,EAAE;QACnCH,GAAG;QACHC;MACR,CAAO,CAAC,CAAC;IACT;IACI,OAAOI,OAAO;EAClB,CAAG;EACD,IAAMW,YAAY,GAAG,SAAfA,YAAYA,CAAIf,QAAQ,EAAEgB,WAAW,EAAEjB,GAAG,EAAEkB,MAAM,EAAK;IAC3D,IAAMC,SAAS,GAAGzB,MAAM,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,MAAM,CAACD,KAAK,CAAC0B,SAAS;IAClE,IAAIC,UAAU,GAAGD,SAAS,IAAI,IAAI,GAAGA,SAAS,GAAG,EAAE;IACnD,IAAI,OAAOA,SAAS,KAAK,UAAU,EAAE;MACnCC,UAAU,GAAGD,SAAS,CAAChB,IAAI,CAAC,IAAI,EAAE;QAChCF,QAAQ;QACRgB,WAAW;QACXjB,GAAG;QACHkB;MACR,CAAO,CAAC;IACR;IACI,IAAMG,UAAU,GAAGC,oBAAoB,CAACL,WAAW,EAAExB,KAAK,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,KAAK,CAAC8B,KAAK,EAAE9B,KAAK,CAACe,KAAK,CAAC;IACvGgB,cAAc,CAACH,UAAU,EAAE,MAAM,CAAC;IAClCG,cAAc,CAACH,UAAU,EAAE,OAAO,CAAC;IACnC,OAAOI,MAAM,CAACC,MAAM,CAAC,EAAE,EAAEN,UAAU,EAAEC,UAAU,CAAC;EACpD,CAAG;EACD,IAAMM,YAAY,GAAG,SAAfA,YAAYA,CAAI1B,QAAQ,EAAEgB,WAAW,EAAEjB,GAAG,EAAEkB,MAAM,EAAEU,MAAM,EAAK;IACnE,IAAMC,YAAY,GAAGC,oBAAoB,CAACjC,EAAE,CAACkC,CAAC,EAAE,EAAEd,WAAW,EAAExB,KAAK,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,KAAK,CAAC8B,KAAK,EAAE9B,KAAK,CAACe,KAAK,EAAE,KAAK,CAAC,EAAEoB,MAAM,CAAC;IACjI,IAAMvB,OAAO,IAAIa,MAAM,CAACc,EAAE,EAAEd,MAAM,CAACe,KAAK,EAAEf,MAAM,CAACgB,SAAS,EAAAC,MAAA,CAAAC,kBAAA,CAAKP,YAAY,EAAC;IAC5E,IAAMQ,aAAa,GAAG3C,MAAM,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,MAAM,CAACD,KAAK,CAAC4C,aAAa;IAC1E,IAAI,OAAOA,aAAa,KAAK,QAAQ,EAAE;MACrChC,OAAO,CAACO,IAAI,CAACyB,aAAa,CAAC;IACjC,CAAK,MAAM,IAAI,OAAOA,aAAa,KAAK,UAAU,EAAE;MAC9ChC,OAAO,CAACO,IAAI,CAACyB,aAAa,CAAClC,IAAI,CAAC,IAAI,EAAE;QACpCF,QAAQ;QACRgB,WAAW;QACXjB,GAAG;QACHkB;MACR,CAAO,CAAC,CAAC;IACT;IACIb,OAAO,CAACO,IAAI,CAACf,EAAE,CAACS,CAAC,CAAC,MAAM,CAAC,CAAC;IAC1B,OAAOD,OAAO,CAACiC,MAAM,CAAC,UAACJ,SAAS;MAAA,OAAKK,OAAO,CAACL,SAAS,CAAC;IAAA,EAAC,CAACM,IAAI,CAAC,GAAG,CAAC;EACtE,CAAG;EACD,IAAMC,OAAO,GAAG,SAAVA,OAAOA,CAAIzC,GAAG,EAAEkB,MAAM,EAAEjB,QAAQ,EAAEgB,WAAW,EAAK;IACtD,IAAIyB,OAAO,GAAG,CAAC;IACf,IAAIC,OAAO,GAAG,CAAC;IACf,IAAMC,EAAE,GAAGlD,MAAM,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,MAAM,CAACD,KAAK,CAACoD,UAAU;IAC5D,IAAI,OAAOD,EAAE,KAAK,UAAU,EAAE;MAC5B,IAAME,MAAM,GAAGF,EAAE,CAAC;QAChB5C,GAAG;QACHkB,MAAM;QACNjB,QAAQ;QACRgB;MACR,CAAO,CAAC;MACF,IAAI8B,KAAK,CAACC,OAAO,CAACF,MAAM,CAAC,EAAE;QACzBJ,OAAO,GAAGI,MAAM,CAAC,CAAC,CAAC;QACnBH,OAAO,GAAGG,MAAM,CAAC,CAAC,CAAC;MAC3B,CAAO,MAAM,IAAI,OAAOA,MAAM,KAAK,QAAQ,EAAE;QACrCJ,OAAO,GAAGI,MAAM,CAACJ,OAAO;QACxBC,OAAO,GAAGG,MAAM,CAACH,OAAO;MAChC;IACA;IACI,OAAO;MAAED,OAAO;MAAEC;IAAO,CAAE;EAC/B,CAAG;EACD,IAAMM,mBAAmB,GAAG,SAAtBA,mBAAmBA,CAAIC,OAAO,EAAEP,OAAO,EAAEQ,KAAK,EAAK;IACvD,IAAIR,OAAO,GAAG,CAAC,EAAE;MACf,OAAOO,OAAO,CAACC,KAAK,CAAC,CAACC,SAAS;IACrC;IACI,IAAMC,QAAQ,GAAGH,OAAO,CAACI,GAAG,CAAC,UAAAC,IAAA;MAAA,IAAGH,SAAS,GAAAG,IAAA,CAATH,SAAS;QAAEI,KAAK,GAAAD,IAAA,CAALC,KAAK;MAAA,OAAOJ,SAAS,IAAII,KAAK;IAAA,EAAC,CAACC,KAAK,CAACN,KAAK,EAAEA,KAAK,GAAGR,OAAO,CAAC;IACxG,OAAOe,MAAM,CAACL,QAAQ,CAACM,MAAM,CAAC,UAACC,GAAG,EAAEJ,KAAK;MAAA,OAAKE,MAAM,CAACE,GAAG,CAAC,GAAGF,MAAM,CAACF,KAAK,CAAC;IAAA,GAAE,CAAC,CAAC,CAAC,CAAC;EACnF,CAAG;EACD,OAAO;IACLzD,WAAW;IACXK,WAAW;IACXY,YAAY;IACZW,YAAY;IACZc,OAAO;IACPQ;EACJ,CAAG;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
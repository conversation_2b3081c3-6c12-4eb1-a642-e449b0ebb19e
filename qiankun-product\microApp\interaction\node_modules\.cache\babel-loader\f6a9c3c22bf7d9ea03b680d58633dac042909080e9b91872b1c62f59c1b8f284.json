{"ast": null, "code": "import baseIteratee from './_baseIteratee.js';\nimport baseSum from './_baseSum.js';\n\n/**\n * This method is like `_.sum` except that it accepts `iteratee` which is\n * invoked for each element in `array` to generate the value to be summed.\n * The iteratee is invoked with one argument: (value).\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category Math\n * @param {Array} array The array to iterate over.\n * @param {Function} [iteratee=_.identity] The iteratee invoked per element.\n * @returns {number} Returns the sum.\n * @example\n *\n * var objects = [{ 'n': 4 }, { 'n': 2 }, { 'n': 8 }, { 'n': 6 }];\n *\n * _.sumBy(objects, function(o) { return o.n; });\n * // => 20\n *\n * // The `_.property` iteratee shorthand.\n * _.sumBy(objects, 'n');\n * // => 20\n */\nfunction sumBy(array, iteratee) {\n  return array && array.length ? baseSum(array, baseIteratee(iteratee, 2)) : 0;\n}\nexport default sumBy;", "map": {"version": 3, "names": ["baseIteratee", "baseSum", "sumBy", "array", "iteratee", "length"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/node_modules/.pnpm/lodash-es@4.17.21/node_modules/lodash-es/sumBy.js"], "sourcesContent": ["import baseIteratee from './_baseIteratee.js';\nimport baseSum from './_baseSum.js';\n\n/**\n * This method is like `_.sum` except that it accepts `iteratee` which is\n * invoked for each element in `array` to generate the value to be summed.\n * The iteratee is invoked with one argument: (value).\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category Math\n * @param {Array} array The array to iterate over.\n * @param {Function} [iteratee=_.identity] The iteratee invoked per element.\n * @returns {number} Returns the sum.\n * @example\n *\n * var objects = [{ 'n': 4 }, { 'n': 2 }, { 'n': 8 }, { 'n': 6 }];\n *\n * _.sumBy(objects, function(o) { return o.n; });\n * // => 20\n *\n * // The `_.property` iteratee shorthand.\n * _.sumBy(objects, 'n');\n * // => 20\n */\nfunction sumBy(array, iteratee) {\n  return (array && array.length)\n    ? baseSum(array, baseIteratee(iteratee, 2))\n    : 0;\n}\n\nexport default sumBy;\n"], "mappings": "AAAA,OAAOA,YAAY,MAAM,oBAAoB;AAC7C,OAAOC,OAAO,MAAM,eAAe;;AAEnC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,KAAKA,CAACC,KAAK,EAAEC,QAAQ,EAAE;EAC9B,OAAQD,KAAK,IAAIA,KAAK,CAACE,MAAM,GACzBJ,OAAO,CAACE,KAAK,EAAEH,YAAY,CAACI,QAAQ,EAAE,CAAC,CAAC,CAAC,GACzC,CAAC;AACP;AAEA,eAAeF,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
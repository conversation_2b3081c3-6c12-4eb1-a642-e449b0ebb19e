{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.trim = exports.isObject = exports.isNil = exports.isNan = exports.size = exports.isString = exports.validateLocale = exports.splitSentences = void 0;\nvar splitSentences = function splitSentences() {\n  var text = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : '';\n  return text.replace(/\\.+/g, '.|').replace(/\\?/g, '?|').replace(/\\!/g, '!|').split(\"|\").map(function (sentence) {\n    return trim(sentence);\n  }).filter(Boolean);\n};\nexports.splitSentences = splitSentences;\nvar bcp47LocalePattern = /^(?:(en-GB-oed|i-ami|i-bnn|i-default|i-enochian|i-hak|i-klingon|i-lux|i-mingo|i-navajo|i-pwn|i-tao|i-tay|i-tsu|sgn-BE-FR|sgn-BE-NL|sgn-CH-DE)|(art-lojban|cel-gaulish|no-bok|no-nyn|zh-guoyu|zh-hakka|zh-min|zh-min-nan|zh-xiang))$|^((?:[a-z]{2,3}(?:(?:-[a-z]{3}){1,3})?)|[a-z]{4}|[a-z]{5,8})(?:-([a-z]{4}))?(?:-([a-z]{2}|\\d{3}))?((?:-(?:[\\da-z]{5,8}|\\d[\\da-z]{3}))*)?((?:-[\\da-wy-z](?:-[\\da-z]{2,8})+)*)?(-x(?:-[\\da-z]{1,8})+)?$|^(x(?:-[\\da-z]{1,8})+)$/i; // eslint-disable-line max-len\n\n/**\r\n * Validate a locale string to test if it is bcp47 compliant\r\n * @param {String} locale The tag locale to parse\r\n * @return {Boolean} True if tag is bcp47 compliant false otherwise\r\n */\n\nvar validateLocale = function validateLocale(locale) {\n  return typeof locale !== 'string' ? false : bcp47LocalePattern.test(locale);\n};\nexports.validateLocale = validateLocale;\nvar isString = function isString(value) {\n  return typeof value === 'string' || value instanceof String;\n};\nexports.isString = isString;\nvar size = function size(value) {\n  return value && Array.isArray(value) && value.length ? value.length : 0;\n};\nexports.size = size;\nvar isNan = function isNan(value) {\n  return typeof value === \"number\" && isNaN(value);\n};\nexports.isNan = isNan;\nvar isNil = function isNil(value) {\n  return value === null || value === undefined;\n};\nexports.isNil = isNil;\nvar isObject = function isObject(value) {\n  return Object.prototype.toString.call(value) === '[object Object]';\n};\nexports.isObject = isObject;\nvar trim = function trim(value) {\n  return isString(value) ? value.trim() : '';\n};\nexports.trim = trim;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "trim", "isObject", "isNil", "isNan", "size", "isString", "validateLocale", "splitSentences", "text", "arguments", "length", "undefined", "replace", "split", "map", "sentence", "filter", "Boolean", "bcp47LocalePattern", "locale", "test", "String", "Array", "isArray", "isNaN", "prototype", "toString", "call"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/common/speak-tts/utils.js"], "sourcesContent": ["\"use strict\";\r\n\r\nObject.defineProperty(exports, \"__esModule\", {\r\n  value: true\r\n});\r\nexports.trim = exports.isObject = exports.isNil = exports.isNan = exports.size = exports.isString = exports.validateLocale = exports.splitSentences = void 0;\r\n\r\nvar splitSentences = function splitSentences() {\r\n  var text = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : '';\r\n  return text.replace(/\\.+/g, '.|').replace(/\\?/g, '?|').replace(/\\!/g, '!|').split(\"|\").map(function (sentence) {\r\n    return trim(sentence);\r\n  }).filter(Boolean);\r\n};\r\n\r\nexports.splitSentences = splitSentences;\r\nvar bcp47LocalePattern = /^(?:(en-GB-oed|i-ami|i-bnn|i-default|i-enochian|i-hak|i-klingon|i-lux|i-mingo|i-navajo|i-pwn|i-tao|i-tay|i-tsu|sgn-BE-FR|sgn-BE-NL|sgn-CH-DE)|(art-lojban|cel-gaulish|no-bok|no-nyn|zh-guoyu|zh-hakka|zh-min|zh-min-nan|zh-xiang))$|^((?:[a-z]{2,3}(?:(?:-[a-z]{3}){1,3})?)|[a-z]{4}|[a-z]{5,8})(?:-([a-z]{4}))?(?:-([a-z]{2}|\\d{3}))?((?:-(?:[\\da-z]{5,8}|\\d[\\da-z]{3}))*)?((?:-[\\da-wy-z](?:-[\\da-z]{2,8})+)*)?(-x(?:-[\\da-z]{1,8})+)?$|^(x(?:-[\\da-z]{1,8})+)$/i; // eslint-disable-line max-len\r\n\r\n/**\r\n * Validate a locale string to test if it is bcp47 compliant\r\n * @param {String} locale The tag locale to parse\r\n * @return {Boolean} True if tag is bcp47 compliant false otherwise\r\n */\r\n\r\nvar validateLocale = function validateLocale(locale) {\r\n  return typeof locale !== 'string' ? false : bcp47LocalePattern.test(locale);\r\n};\r\n\r\nexports.validateLocale = validateLocale;\r\n\r\nvar isString = function isString(value) {\r\n  return typeof value === 'string' || value instanceof String;\r\n};\r\n\r\nexports.isString = isString;\r\n\r\nvar size = function size(value) {\r\n  return value && Array.isArray(value) && value.length ? value.length : 0;\r\n};\r\n\r\nexports.size = size;\r\n\r\nvar isNan = function isNan(value) {\r\n  return typeof value === \"number\" && isNaN(value);\r\n};\r\n\r\nexports.isNan = isNan;\r\n\r\nvar isNil = function isNil(value) {\r\n  return value === null || value === undefined;\r\n};\r\n\r\nexports.isNil = isNil;\r\n\r\nvar isObject = function isObject(value) {\r\n  return Object.prototype.toString.call(value) === '[object Object]';\r\n};\r\n\r\nexports.isObject = isObject;\r\n\r\nvar trim = function trim(value) {\r\n  return isString(value) ? value.trim() : '';\r\n};\r\n\r\nexports.trim = trim;"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAACE,IAAI,GAAGF,OAAO,CAACG,QAAQ,GAAGH,OAAO,CAACI,KAAK,GAAGJ,OAAO,CAACK,KAAK,GAAGL,OAAO,CAACM,IAAI,GAAGN,OAAO,CAACO,QAAQ,GAAGP,OAAO,CAACQ,cAAc,GAAGR,OAAO,CAACS,cAAc,GAAG,KAAK,CAAC;AAE5J,IAAIA,cAAc,GAAG,SAASA,cAAcA,CAAA,EAAG;EAC7C,IAAIC,IAAI,GAAGC,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKE,SAAS,GAAGF,SAAS,CAAC,CAAC,CAAC,GAAG,EAAE;EACjF,OAAOD,IAAI,CAACI,OAAO,CAAC,MAAM,EAAE,IAAI,CAAC,CAACA,OAAO,CAAC,KAAK,EAAE,IAAI,CAAC,CAACA,OAAO,CAAC,KAAK,EAAE,IAAI,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,CAAC,UAAUC,QAAQ,EAAE;IAC7G,OAAOf,IAAI,CAACe,QAAQ,CAAC;EACvB,CAAC,CAAC,CAACC,MAAM,CAACC,OAAO,CAAC;AACpB,CAAC;AAEDnB,OAAO,CAACS,cAAc,GAAGA,cAAc;AACvC,IAAIW,kBAAkB,GAAG,ocAAoc,CAAC,CAAC;;AAE/d;AACA;AACA;AACA;AACA;;AAEA,IAAIZ,cAAc,GAAG,SAASA,cAAcA,CAACa,MAAM,EAAE;EACnD,OAAO,OAAOA,MAAM,KAAK,QAAQ,GAAG,KAAK,GAAGD,kBAAkB,CAACE,IAAI,CAACD,MAAM,CAAC;AAC7E,CAAC;AAEDrB,OAAO,CAACQ,cAAc,GAAGA,cAAc;AAEvC,IAAID,QAAQ,GAAG,SAASA,QAAQA,CAACN,KAAK,EAAE;EACtC,OAAO,OAAOA,KAAK,KAAK,QAAQ,IAAIA,KAAK,YAAYsB,MAAM;AAC7D,CAAC;AAEDvB,OAAO,CAACO,QAAQ,GAAGA,QAAQ;AAE3B,IAAID,IAAI,GAAG,SAASA,IAAIA,CAACL,KAAK,EAAE;EAC9B,OAAOA,KAAK,IAAIuB,KAAK,CAACC,OAAO,CAACxB,KAAK,CAAC,IAAIA,KAAK,CAACW,MAAM,GAAGX,KAAK,CAACW,MAAM,GAAG,CAAC;AACzE,CAAC;AAEDZ,OAAO,CAACM,IAAI,GAAGA,IAAI;AAEnB,IAAID,KAAK,GAAG,SAASA,KAAKA,CAACJ,KAAK,EAAE;EAChC,OAAO,OAAOA,KAAK,KAAK,QAAQ,IAAIyB,KAAK,CAACzB,KAAK,CAAC;AAClD,CAAC;AAEDD,OAAO,CAACK,KAAK,GAAGA,KAAK;AAErB,IAAID,KAAK,GAAG,SAASA,KAAKA,CAACH,KAAK,EAAE;EAChC,OAAOA,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAKY,SAAS;AAC9C,CAAC;AAEDb,OAAO,CAACI,KAAK,GAAGA,KAAK;AAErB,IAAID,QAAQ,GAAG,SAASA,QAAQA,CAACF,KAAK,EAAE;EACtC,OAAOH,MAAM,CAAC6B,SAAS,CAACC,QAAQ,CAACC,IAAI,CAAC5B,KAAK,CAAC,KAAK,iBAAiB;AACpE,CAAC;AAEDD,OAAO,CAACG,QAAQ,GAAGA,QAAQ;AAE3B,IAAID,IAAI,GAAG,SAASA,IAAIA,CAACD,KAAK,EAAE;EAC9B,OAAOM,QAAQ,CAACN,KAAK,CAAC,GAAGA,KAAK,CAACC,IAAI,CAAC,CAAC,GAAG,EAAE;AAC5C,CAAC;AAEDF,OAAO,CAACE,IAAI,GAAGA,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}
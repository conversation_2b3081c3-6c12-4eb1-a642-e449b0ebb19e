{"ast": null, "code": "function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nfunction _defineProperty(e, r, t) { return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, { value: t, enumerable: !0, configurable: !0, writable: !0 }) : e[r] = t, e; }\nfunction _toPropertyKey(t) { var i = _toPrimitive(t, \"string\"); return \"symbol\" == typeof i ? i : i + \"\"; }\nfunction _toPrimitive(t, r) { if (\"object\" != typeof t || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || \"default\"); if (\"object\" != typeof i) return i; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (\"string\" === r ? String : Number)(t); }\nimport '../../../utils/index.mjs';\nimport { tooltipV2RootProps } from './root.mjs';\nimport { tooltipV2TriggerProps } from './trigger.mjs';\nimport { tooltipV2ArrowProps } from './arrow.mjs';\nimport { tooltipV2ContentProps } from './content.mjs';\nimport { buildProps, definePropType } from '../../../utils/vue/props/runtime.mjs';\nvar tooltipV2Props = buildProps(_objectSpread(_objectSpread(_objectSpread(_objectSpread(_objectSpread({}, tooltipV2RootProps), tooltipV2ArrowProps), tooltipV2TriggerProps), tooltipV2ContentProps), {}, {\n  alwaysOn: Boolean,\n  fullTransition: Boolean,\n  transitionProps: {\n    type: definePropType(Object),\n    default: null\n  },\n  teleported: Boolean,\n  to: {\n    type: definePropType(String),\n    default: \"body\"\n  }\n}));\nexport { tooltipV2Props };", "map": {"version": 3, "names": ["tooltipV2Props", "buildProps", "_objectSpread", "tooltipV2RootProps", "tooltipV2ArrowProps", "tooltipV2TriggerProps", "tooltipV2ContentProps", "alwaysOn", "Boolean", "fullTransition", "transitionProps", "type", "definePropType", "Object", "default", "teleported", "to", "String"], "sources": ["../../../../../../packages/components/tooltip-v2/src/tooltip.ts"], "sourcesContent": ["import { buildProps, definePropType } from '@element-plus/utils'\nimport { tooltipV2RootProps } from './root'\nimport { tooltipV2TriggerProps } from './trigger'\nimport { tooltipV2ArrowProps } from './arrow'\nimport { tooltipV2ContentProps } from './content'\n\nimport type { ExtractPropTypes, TeleportProps, TransitionProps } from 'vue'\n\nexport const tooltipV2Props = buildProps({\n  ...tooltipV2RootProps,\n  ...tooltipV2ArrowProps,\n  ...tooltipV2TriggerProps,\n  ...tooltipV2ContentProps,\n  alwaysOn: Boolean,\n  fullTransition: Boolean,\n  transitionProps: {\n    type: definePropType<TransitionProps | null>(Object),\n    default: null,\n  },\n  teleported: Boolean,\n  to: {\n    type: definePropType<TeleportProps['to']>(String),\n    default: 'body',\n  },\n} as const)\n\nexport type TooltipV2Props = ExtractPropTypes<typeof tooltipV2Props>\n"], "mappings": ";;;;;;;;;;;AAKY,IAACA,cAAc,GAAGC,UAAU,CAAAC,aAAA,CAAAA,aAAA,CAAAA,aAAA,CAAAA,aAAA,CAAAA,aAAA,KACnCC,kBAAkB,GAClBC,mBAAmB,GACnBC,qBAAqB,GACrBC,qBAAqB;EACxBC,QAAQ,EAAEC,OAAO;EACjBC,cAAc,EAAED,OAAO;EACvBE,eAAe,EAAE;IACfC,IAAI,EAAEC,cAAc,CAACC,MAAM,CAAC;IAC5BC,OAAO,EAAE;EACb,CAAG;EACDC,UAAU,EAAEP,OAAO;EACnBQ,EAAE,EAAE;IACFL,IAAI,EAAEC,cAAc,CAACK,MAAM,CAAC;IAC5BH,OAAO,EAAE;EACb;AAAG,EACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
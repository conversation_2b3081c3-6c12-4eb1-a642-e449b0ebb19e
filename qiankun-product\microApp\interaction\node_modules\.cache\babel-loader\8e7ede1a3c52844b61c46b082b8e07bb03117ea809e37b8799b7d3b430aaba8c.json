{"ast": null, "code": "import { defineComponent, inject, ref, computed, onBeforeUnmount, toRef, openBlock, createBlock, Transition, unref, withCtx, withDirectives, createElementVNode, normalizeClass, normalizeStyle, vShow } from 'vue';\nimport { isClient, useEventListener } from '@vueuse/core';\nimport '../../../utils/index.mjs';\nimport '../../../hooks/index.mjs';\nimport { scrollbarContextKey } from './constants.mjs';\nimport { BAR_MAP, renderThumbStyle } from './util.mjs';\nimport { thumbProps } from './thumb.mjs';\nimport _export_sfc from '../../../_virtual/plugin-vue_export-helper.mjs';\nimport { useNamespace } from '../../../hooks/use-namespace/index.mjs';\nimport { throwError } from '../../../utils/error.mjs';\nvar COMPONENT_NAME = \"Thumb\";\nvar _sfc_main = /* @__PURE__ */defineComponent({\n  __name: \"thumb\",\n  props: thumbProps,\n  setup(__props) {\n    var props = __props;\n    var scrollbar = inject(scrollbarContextKey);\n    var ns = useNamespace(\"scrollbar\");\n    if (!scrollbar) throwError(COMPONENT_NAME, \"can not inject scrollbar context\");\n    var instance = ref();\n    var thumb = ref();\n    var thumbState = ref({});\n    var visible = ref(false);\n    var cursorDown = false;\n    var cursorLeave = false;\n    var originalOnSelectStart = isClient ? document.onselectstart : null;\n    var bar = computed(function () {\n      return BAR_MAP[props.vertical ? \"vertical\" : \"horizontal\"];\n    });\n    var thumbStyle = computed(function () {\n      return renderThumbStyle({\n        size: props.size,\n        move: props.move,\n        bar: bar.value\n      });\n    });\n    var offsetRatio = computed(function () {\n      return Math.pow(instance.value[bar.value.offset], 2) / scrollbar.wrapElement[bar.value.scrollSize] / props.ratio / thumb.value[bar.value.offset];\n    });\n    var clickThumbHandler = function clickThumbHandler(e) {\n      var _a;\n      e.stopPropagation();\n      if (e.ctrlKey || [1, 2].includes(e.button)) return;\n      (_a = window.getSelection()) == null ? void 0 : _a.removeAllRanges();\n      startDrag(e);\n      var el = e.currentTarget;\n      if (!el) return;\n      thumbState.value[bar.value.axis] = el[bar.value.offset] - (e[bar.value.client] - el.getBoundingClientRect()[bar.value.direction]);\n    };\n    var clickTrackHandler = function clickTrackHandler(e) {\n      if (!thumb.value || !instance.value || !scrollbar.wrapElement) return;\n      var offset = Math.abs(e.target.getBoundingClientRect()[bar.value.direction] - e[bar.value.client]);\n      var thumbHalf = thumb.value[bar.value.offset] / 2;\n      var thumbPositionPercentage = (offset - thumbHalf) * 100 * offsetRatio.value / instance.value[bar.value.offset];\n      scrollbar.wrapElement[bar.value.scroll] = thumbPositionPercentage * scrollbar.wrapElement[bar.value.scrollSize] / 100;\n    };\n    var startDrag = function startDrag(e) {\n      e.stopImmediatePropagation();\n      cursorDown = true;\n      document.addEventListener(\"mousemove\", mouseMoveDocumentHandler);\n      document.addEventListener(\"mouseup\", _mouseUpDocumentHandler);\n      originalOnSelectStart = document.onselectstart;\n      document.onselectstart = function () {\n        return false;\n      };\n    };\n    var mouseMoveDocumentHandler = function mouseMoveDocumentHandler(e) {\n      if (!instance.value || !thumb.value) return;\n      if (cursorDown === false) return;\n      var prevPage = thumbState.value[bar.value.axis];\n      if (!prevPage) return;\n      var offset = (instance.value.getBoundingClientRect()[bar.value.direction] - e[bar.value.client]) * -1;\n      var thumbClickPosition = thumb.value[bar.value.offset] - prevPage;\n      var thumbPositionPercentage = (offset - thumbClickPosition) * 100 * offsetRatio.value / instance.value[bar.value.offset];\n      scrollbar.wrapElement[bar.value.scroll] = thumbPositionPercentage * scrollbar.wrapElement[bar.value.scrollSize] / 100;\n    };\n    var _mouseUpDocumentHandler = function mouseUpDocumentHandler() {\n      cursorDown = false;\n      thumbState.value[bar.value.axis] = 0;\n      document.removeEventListener(\"mousemove\", mouseMoveDocumentHandler);\n      document.removeEventListener(\"mouseup\", _mouseUpDocumentHandler);\n      restoreOnselectstart();\n      if (cursorLeave) visible.value = false;\n    };\n    var mouseMoveScrollbarHandler = function mouseMoveScrollbarHandler() {\n      cursorLeave = false;\n      visible.value = !!props.size;\n    };\n    var mouseLeaveScrollbarHandler = function mouseLeaveScrollbarHandler() {\n      cursorLeave = true;\n      visible.value = cursorDown;\n    };\n    onBeforeUnmount(function () {\n      restoreOnselectstart();\n      document.removeEventListener(\"mouseup\", _mouseUpDocumentHandler);\n    });\n    var restoreOnselectstart = function restoreOnselectstart() {\n      if (document.onselectstart !== originalOnSelectStart) document.onselectstart = originalOnSelectStart;\n    };\n    useEventListener(toRef(scrollbar, \"scrollbarElement\"), \"mousemove\", mouseMoveScrollbarHandler);\n    useEventListener(toRef(scrollbar, \"scrollbarElement\"), \"mouseleave\", mouseLeaveScrollbarHandler);\n    return function (_ctx, _cache) {\n      return openBlock(), createBlock(Transition, {\n        name: unref(ns).b(\"fade\"),\n        persisted: \"\"\n      }, {\n        default: withCtx(function () {\n          return [withDirectives(createElementVNode(\"div\", {\n            ref_key: \"instance\",\n            ref: instance,\n            class: normalizeClass([unref(ns).e(\"bar\"), unref(ns).is(unref(bar).key)]),\n            onMousedown: clickTrackHandler\n          }, [createElementVNode(\"div\", {\n            ref_key: \"thumb\",\n            ref: thumb,\n            class: normalizeClass(unref(ns).e(\"thumb\")),\n            style: normalizeStyle(unref(thumbStyle)),\n            onMousedown: clickThumbHandler\n          }, null, 38)], 34), [[vShow, _ctx.always || visible.value]])];\n        }),\n        _: 1\n      }, 8, [\"name\"]);\n    };\n  }\n});\nvar Thumb = /* @__PURE__ */_export_sfc(_sfc_main, [[\"__file\", \"thumb.vue\"]]);\nexport { Thumb as default };", "map": {"version": 3, "names": ["scrollbar", "inject", "scrollbarContextKey", "ns", "useNamespace", "throwError", "COMPONENT_NAME", "instance", "ref", "thumb", "thumbState", "visible", "cursorDown", "cursorLeave", "originalOnSelectStart", "isClient", "document", "onselectstart", "bar", "computed", "BAR_MAP", "props", "vertical", "thumbStyle", "renderThumbStyle", "size", "move", "value", "offsetRatio", "Math", "pow", "offset", "wrapElement", "scrollSize", "ratio", "clickThumbHandler", "e", "_a", "stopPropagation", "ctrl<PERSON>ey", "includes", "button", "window", "getSelection", "removeAllRanges", "startDrag", "el", "currentTarget", "axis", "client", "getBoundingClientRect", "direction", "clickTrackHandler", "abs", "target", "<PERSON><PERSON><PERSON>f", "thumbPositionPercentage", "scroll", "stopImmediatePropagation", "addEventListener", "mouseMoveDocumentHandler", "mouseUpDocumentHandler", "prevPage", "thumbClickPosition", "removeEventListener", "restoreOnselectstart", "mouseMoveScrollbarHandler", "mouseLeaveScrollbarHandler", "onBeforeUnmount", "useEventListener", "toRef"], "sources": ["../../../../../../packages/components/scrollbar/src/thumb.vue"], "sourcesContent": ["<template>\n  <transition :name=\"ns.b('fade')\">\n    <div\n      v-show=\"always || visible\"\n      ref=\"instance\"\n      :class=\"[ns.e('bar'), ns.is(bar.key)]\"\n      @mousedown=\"clickTrackHandler\"\n    >\n      <div\n        ref=\"thumb\"\n        :class=\"ns.e('thumb')\"\n        :style=\"thumbStyle\"\n        @mousedown=\"clickThumbHandler\"\n      />\n    </div>\n  </transition>\n</template>\n\n<script lang=\"ts\" setup>\nimport { computed, inject, onBeforeUnmount, ref, toRef } from 'vue'\nimport { useEventListener } from '@vueuse/core'\nimport { isClient, throwError } from '@element-plus/utils'\nimport { useNamespace } from '@element-plus/hooks'\nimport { scrollbarContextKey } from './constants'\nimport { BAR_MAP, renderThumbStyle } from './util'\nimport { thumbProps } from './thumb'\n\nconst COMPONENT_NAME = 'Thumb'\nconst props = defineProps(thumbProps)\n\nconst scrollbar = inject(scrollbarContextKey)\nconst ns = useNamespace('scrollbar')\n\nif (!scrollbar) throwError(COMPONENT_NAME, 'can not inject scrollbar context')\n\nconst instance = ref<HTMLDivElement>()\nconst thumb = ref<HTMLDivElement>()\n\nconst thumbState = ref<Partial<Record<'X' | 'Y', number>>>({})\nconst visible = ref(false)\n\nlet cursorDown = false\nlet cursorLeave = false\nlet originalOnSelectStart:\n  | ((this: GlobalEventHandlers, ev: Event) => any)\n  | null = isClient ? document.onselectstart : null\n\nconst bar = computed(() => BAR_MAP[props.vertical ? 'vertical' : 'horizontal'])\n\nconst thumbStyle = computed(() =>\n  renderThumbStyle({\n    size: props.size,\n    move: props.move,\n    bar: bar.value,\n  })\n)\n\nconst offsetRatio = computed(\n  () =>\n    // offsetRatioX = original width of thumb / current width of thumb / ratioX\n    // offsetRatioY = original height of thumb / current height of thumb / ratioY\n    // instance height = wrap height - GAP\n    instance.value![bar.value.offset] ** 2 /\n    scrollbar.wrapElement![bar.value.scrollSize] /\n    props.ratio /\n    thumb.value![bar.value.offset]\n)\n\nconst clickThumbHandler = (e: MouseEvent) => {\n  // prevent click event of middle and right button\n  e.stopPropagation()\n  if (e.ctrlKey || [1, 2].includes(e.button)) return\n\n  window.getSelection()?.removeAllRanges()\n  startDrag(e)\n\n  const el = e.currentTarget as HTMLDivElement\n  if (!el) return\n  thumbState.value[bar.value.axis] =\n    el[bar.value.offset] -\n    (e[bar.value.client] - el.getBoundingClientRect()[bar.value.direction])\n}\n\nconst clickTrackHandler = (e: MouseEvent) => {\n  if (!thumb.value || !instance.value || !scrollbar.wrapElement) return\n\n  const offset = Math.abs(\n    (e.target as HTMLElement).getBoundingClientRect()[bar.value.direction] -\n      e[bar.value.client]\n  )\n  const thumbHalf = thumb.value[bar.value.offset] / 2\n  const thumbPositionPercentage =\n    ((offset - thumbHalf) * 100 * offsetRatio.value) /\n    instance.value[bar.value.offset]\n\n  scrollbar.wrapElement[bar.value.scroll] =\n    (thumbPositionPercentage * scrollbar.wrapElement[bar.value.scrollSize]) /\n    100\n}\n\nconst startDrag = (e: MouseEvent) => {\n  e.stopImmediatePropagation()\n  cursorDown = true\n  document.addEventListener('mousemove', mouseMoveDocumentHandler)\n  document.addEventListener('mouseup', mouseUpDocumentHandler)\n  originalOnSelectStart = document.onselectstart\n  document.onselectstart = () => false\n}\n\nconst mouseMoveDocumentHandler = (e: MouseEvent) => {\n  if (!instance.value || !thumb.value) return\n  if (cursorDown === false) return\n\n  const prevPage = thumbState.value[bar.value.axis]\n  if (!prevPage) return\n\n  const offset =\n    (instance.value.getBoundingClientRect()[bar.value.direction] -\n      e[bar.value.client]) *\n    -1\n  const thumbClickPosition = thumb.value[bar.value.offset] - prevPage\n  const thumbPositionPercentage =\n    ((offset - thumbClickPosition) * 100 * offsetRatio.value) /\n    instance.value[bar.value.offset]\n  scrollbar.wrapElement[bar.value.scroll] =\n    (thumbPositionPercentage * scrollbar.wrapElement[bar.value.scrollSize]) /\n    100\n}\n\nconst mouseUpDocumentHandler = () => {\n  cursorDown = false\n  thumbState.value[bar.value.axis] = 0\n  document.removeEventListener('mousemove', mouseMoveDocumentHandler)\n  document.removeEventListener('mouseup', mouseUpDocumentHandler)\n  restoreOnselectstart()\n  if (cursorLeave) visible.value = false\n}\n\nconst mouseMoveScrollbarHandler = () => {\n  cursorLeave = false\n  visible.value = !!props.size\n}\n\nconst mouseLeaveScrollbarHandler = () => {\n  cursorLeave = true\n  visible.value = cursorDown\n}\n\nonBeforeUnmount(() => {\n  restoreOnselectstart()\n  document.removeEventListener('mouseup', mouseUpDocumentHandler)\n})\n\nconst restoreOnselectstart = () => {\n  if (document.onselectstart !== originalOnSelectStart)\n    document.onselectstart = originalOnSelectStart\n}\n\nuseEventListener(\n  toRef(scrollbar, 'scrollbarElement'),\n  'mousemove',\n  mouseMoveScrollbarHandler\n)\nuseEventListener(\n  toRef(scrollbar, 'scrollbarElement'),\n  'mouseleave',\n  mouseLeaveScrollbarHandler\n)\n</script>\n"], "mappings": ";;;;;;;;;;;;;;;;IA8BM,IAAAA,SAAA,GAAYC,MAAA,CAAOC,mBAAmB;IACtC,IAAAC,EAAA,GAAKC,YAAA,CAAa,WAAW;IAEnC,IAAI,CAACJ,SAAA,EAAWK,UAAA,CAAWC,cAAA,EAAgB,kCAAkC;IAE7E,IAAMC,QAAA,GAAWC,GAAoB;IACrC,IAAMC,KAAA,GAAQD,GAAoB;IAE5B,IAAAE,UAAA,GAAaF,GAAwC,GAAE;IACvD,IAAAG,OAAA,GAAUH,GAAA,CAAI,KAAK;IAEzB,IAAII,UAAa;IACjB,IAAIC,WAAc;IACd,IAAAC,qBAAA,GAEOC,QAAW,GAAAC,QAAA,CAASC,aAAgB;IAE/C,IAAMC,GAAA,GAAMC,QAAS;MAAA,OAAMC,OAAA,CAAQC,KAAM,CAAAC,QAAA,GAAW,aAAa,YAAa;IAAA;IAExE,IAAAC,UAAA,GAAaJ,QAAS;MAAA,OAC1BK,gBAAiB;QACfC,IAAA,EAAMJ,KAAM,CAAAI,IAAA;QACZC,IAAA,EAAML,KAAM,CAAAK,IAAA;QACZR,GAAA,EAAKA,GAAI,CAAAS;MAAA,CACV,CACH;IAAA;IAEM,IAAAC,WAAA,GAAcT,QAAA,CAClB;MAAA,OAIEU,IAAA,CAAAC,GAAA,CAAAvB,QAAA,CAASoB,KAAA,CAAOT,GAAI,CAAAS,KAAA,CAAMI,MAAA,GAAW,CACrC,IAAA/B,SAAA,CAAUgC,WAAA,CAAad,GAAI,CAAAS,KAAA,CAAMM,UAAA,IACjCZ,KAAM,CAAAa,KAAA,GACNzB,KAAA,CAAMkB,KAAO,CAAAT,GAAA,CAAIS,KAAA,CAAMI,MAC3B;IAAA;IAEM,IAAAI,iBAAA,GAAoB,SAApBA,kBAAqBC,CAAkB;MAE3C,IAAkBC,EAAA;MACdD,CAAA,CAAAE,eAAa,EAAC;MAA0B,IAAAF,CAAA,CAAAG,OAAA,WAAAC,QAAA,CAAAJ,CAAA,CAAAK,MAAA,GAErC;MACP,CAAAJ,EAAA,GAAAK,MAAW,CAAAC,YAAA,uBAAAN,EAAA,CAAAO,eAAA;MAEXC,SAAA,CAAAT,CAAW,CAAE;MACb,IAAKU,EAAA,GAAAV,CAAA,CAAAW,aAAA;MAAI,KAAAD,EAAA,EACT;MAGFpC,UAAA,CAAAiB,KAAA,CAAAT,GAAA,CAAAS,KAAA,CAAAqB,IAAA,IAAAF,EAAA,CAAA5B,GAAA,CAAAS,KAAA,CAAAI,MAAA,KAAAK,CAAA,CAAAlB,GAAA,CAAAS,KAAA,CAAAsB,MAAA,IAAAH,EAAA,CAAAI,qBAAA,GAAAhC,GAAA,CAAAS,KAAA,CAAAwB,SAAA;IAEA,CAAM;IACJ,IAAIC,iBAAiB,GAAS,SAA1BA,iBAAiBA,CAAShB,CAAA;MAAiC,KAAA3B,KAAA,CAAAkB,KAAA,KAAApB,QAAA,CAAAoB,KAAA,KAAA3B,SAAA,CAAAgC,WAAA,EAE/D;MAIA,IAAMD,MAAY,GAAAF,IAAA,CAAAwB,GAAA,CAAMjB,CAAM,CAAAkB,MAAA,CAAAJ,qBAAoB,GAAAhC,GAAA,CAAAS,KAAA,CAAAwB,SAAA,IAAAf,CAAA,CAAAlB,GAAA,CAAAS,KAAA,CAAAsB,MAAA;MAC5C,IAAAM,SAAA,GAAA9C,KAAA,CAAAkB,KAAA,CAAAT,GAAA,CAAAS,KAAA,CAAAI,MACoB;MAGhB,IAAAyB,uBAAsB,IAAAzB,MAAA,GAC7BwB,SAAA,UAAA3B,WAAoC,CAAAD,KAAA,GAAApB,QAAA,CAAYoB,KAAI,CAAAT,GAAA,CAAMS,KAC3D,CAAAI,MAAA;MACJ/B,SAAA,CAAAgC,WAAA,CAAAd,GAAA,CAAAS,KAAA,CAAA8B,MAAA,IAAAD,uBAAA,GAAAxD,SAAA,CAAAgC,WAAA,CAAAd,GAAA,CAAAS,KAAA,CAAAM,UAAA;IAEA,CAAM;IACJ,IAA2BY,SAAA,YAAAA,UAAAT,CAAA;MACdA,CAAA,CAAAsB,wBAAA;MACJ9C,UAAA;MACAI,QAAA,CAAA2C,gBAAA,CAAiB,WAAW,EAAsBC,wBAAA;MAC3D5C,QAAA,CAAA2C,gBAAiC,YAAAE,uBAAA;MACjC/C,qBAAA,GAAAE,QAA+B,CAAAC,aAAA;MACjCD,QAAA,CAAAC,aAAA;QAAA;MAAA;IAEA,CAAM;IACJ,IAAI2C,wBAA0B,YAA1BA,wBAA0BA,CAAAxB,CAAA;MAAO,KAAA7B,QAAA,CAAAoB,KAAA,KAAAlB,KAAA,CAAAkB,KAAA,EACrC;MAA0B,IAAAf,UAAA,YAE1B;MACA,IAAKkD,QAAA,GAAApD,UAAA,CAAAiB,KAAA,CAAAT,GAAA,CAAAS,KAAA,CAAAqB,IAAA;MAAU,KAAAc,QAAA,EAET;MAIN,IAAM/B,MAAqB,IAAAxB,QAAA,CAAAoB,KAAA,CAAAuB,qBAAgC,GAAAhC,GAAA,CAAAS,KAAA,CAAAwB,SAAA,IAAAf,CAAA,CAAAlB,GAAA,CAAAS,KAAA,CAAAsB,MAAA;MACrD,IAAAc,kBAAA,GAAAtD,KACF,CAAAkB,KAAA,CAAAT,GAAS,CAAsBS,KAAA,CAAAI,MAAA,IAAA+B,QAAA;MAEzB,IAAAN,uBAAsB,IAAAzB,MAAA,GAC7BgC,kBAAA,UAAoCnC,WAAA,CAAAD,KAAA,GAAYpB,QAAI,CAAAoB,KACrD,CAAAT,GAAA,CAAAS,KAAA,CAAAI,MAAA;MACJ/B,SAAA,CAAAgC,WAAA,CAAAd,GAAA,CAAAS,KAAA,CAAA8B,MAAA,IAAAD,uBAAA,GAAAxD,SAAA,CAAAgC,WAAA,CAAAd,GAAA,CAAAS,KAAA,CAAAM,UAAA;IAEA;IACe,IAAA4B,uBAAA,YAAAA,uBAAA;MACFjD,UAAA,QAAU;MACZF,UAAA,CAAAiB,KAAA,CAAAT,GAAA,CAAAS,KAAA,CAAAqB,IAAA;MACAhC,QAAA,CAAAgD,mBAAA,CAAoB,WAAW,EAAsBJ,wBAAA;MACzC5C,QAAA,CAAAgD,mBAAA,YAAAH,uBAAA;MACjBI,oBAAA;MAAa,IAAApD,WAAgB,EACnCF,OAAA,CAAAgB,KAAA;IAEA;IACgB,IAAAuC,yBAAA,YAAAA,0BAAA;MACNrD,WAAA,QAAgB;MAC1BF,OAAA,CAAAgB,KAAA,KAAAN,KAAA,CAAAI,IAAA;IAEA;IACgB,IAAA0C,0BAAA,YAAAA,2BAAA;MACdtD,WAAgB;MAClBF,OAAA,CAAAgB,KAAA,GAAAf,UAAA;IAEA;IACuBwD,eAAA;MACZH,oBAAA;MACVjD,QAAA,CAAAgD,mBAAA,YAAAH,uBAAA;IAED;IACE,IAAII,oBAA2B,YAA3BA,oBAA2BA,CAAA;MAC7B,IAAAjD,QAAyB,CAAAC,aAAA,KAAAH,qBAAA,EAC7BE,QAAA,CAAAC,aAAA,GAAAH,qBAAA;IAEA;IAKAuD,gBAAA,CACEC,KAAM,CAAAtE,SAAA,EAAW,kBAAkB,GACnC,aAAAkE,yBAEF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
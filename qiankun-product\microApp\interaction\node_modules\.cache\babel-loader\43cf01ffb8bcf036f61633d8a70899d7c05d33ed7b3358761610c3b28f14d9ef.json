{"ast": null, "code": "import SetCache from './_SetCache.js';\nimport arrayIncludes from './_arrayIncludes.js';\nimport arrayIncludesWith from './_arrayIncludesWith.js';\nimport arrayMap from './_arrayMap.js';\nimport baseUnary from './_baseUnary.js';\nimport cacheHas from './_cacheHas.js';\n\n/* Built-in method references for those with the same name as other `lodash` methods. */\nvar nativeMin = Math.min;\n\n/**\n * The base implementation of methods like `_.intersection`, without support\n * for iteratee shorthands, that accepts an array of arrays to inspect.\n *\n * @private\n * @param {Array} arrays The arrays to inspect.\n * @param {Function} [iteratee] The iteratee invoked per element.\n * @param {Function} [comparator] The comparator invoked per element.\n * @returns {Array} Returns the new array of shared values.\n */\nfunction baseIntersection(arrays, iteratee, comparator) {\n  var includes = comparator ? arrayIncludesWith : arrayIncludes,\n    length = arrays[0].length,\n    othLength = arrays.length,\n    othIndex = othLength,\n    caches = Array(othLength),\n    maxLength = Infinity,\n    result = [];\n  while (othIndex--) {\n    var array = arrays[othIndex];\n    if (othIndex && iteratee) {\n      array = arrayMap(array, baseUnary(iteratee));\n    }\n    maxLength = nativeMin(array.length, maxLength);\n    caches[othIndex] = !comparator && (iteratee || length >= 120 && array.length >= 120) ? new SetCache(othIndex && array) : undefined;\n  }\n  array = arrays[0];\n  var index = -1,\n    seen = caches[0];\n  outer: while (++index < length && result.length < maxLength) {\n    var value = array[index],\n      computed = iteratee ? iteratee(value) : value;\n    value = comparator || value !== 0 ? value : 0;\n    if (!(seen ? cacheHas(seen, computed) : includes(result, computed, comparator))) {\n      othIndex = othLength;\n      while (--othIndex) {\n        var cache = caches[othIndex];\n        if (!(cache ? cacheHas(cache, computed) : includes(arrays[othIndex], computed, comparator))) {\n          continue outer;\n        }\n      }\n      if (seen) {\n        seen.push(computed);\n      }\n      result.push(value);\n    }\n  }\n  return result;\n}\nexport default baseIntersection;", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON>", "arrayIncludes", "arrayIncludesWith", "arrayMap", "baseUnary", "cacheHas", "nativeMin", "Math", "min", "baseIntersection", "arrays", "iteratee", "comparator", "includes", "length", "oth<PERSON><PERSON><PERSON>", "othIndex", "caches", "Array", "max<PERSON><PERSON><PERSON>", "Infinity", "result", "array", "undefined", "index", "seen", "outer", "value", "computed", "cache", "push"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/node_modules/.pnpm/lodash-es@4.17.21/node_modules/lodash-es/_baseIntersection.js"], "sourcesContent": ["import SetCache from './_SetCache.js';\nimport arrayIncludes from './_arrayIncludes.js';\nimport arrayIncludesWith from './_arrayIncludesWith.js';\nimport arrayMap from './_arrayMap.js';\nimport baseUnary from './_baseUnary.js';\nimport cacheHas from './_cacheHas.js';\n\n/* Built-in method references for those with the same name as other `lodash` methods. */\nvar nativeMin = Math.min;\n\n/**\n * The base implementation of methods like `_.intersection`, without support\n * for iteratee shorthands, that accepts an array of arrays to inspect.\n *\n * @private\n * @param {Array} arrays The arrays to inspect.\n * @param {Function} [iteratee] The iteratee invoked per element.\n * @param {Function} [comparator] The comparator invoked per element.\n * @returns {Array} Returns the new array of shared values.\n */\nfunction baseIntersection(arrays, iteratee, comparator) {\n  var includes = comparator ? arrayIncludesWith : arrayIncludes,\n      length = arrays[0].length,\n      othLength = arrays.length,\n      othIndex = othLength,\n      caches = Array(othLength),\n      maxLength = Infinity,\n      result = [];\n\n  while (othIndex--) {\n    var array = arrays[othIndex];\n    if (othIndex && iteratee) {\n      array = arrayMap(array, baseUnary(iteratee));\n    }\n    maxLength = nativeMin(array.length, maxLength);\n    caches[othIndex] = !comparator && (iteratee || (length >= 120 && array.length >= 120))\n      ? new SetCache(othIndex && array)\n      : undefined;\n  }\n  array = arrays[0];\n\n  var index = -1,\n      seen = caches[0];\n\n  outer:\n  while (++index < length && result.length < maxLength) {\n    var value = array[index],\n        computed = iteratee ? iteratee(value) : value;\n\n    value = (comparator || value !== 0) ? value : 0;\n    if (!(seen\n          ? cacheHas(seen, computed)\n          : includes(result, computed, comparator)\n        )) {\n      othIndex = othLength;\n      while (--othIndex) {\n        var cache = caches[othIndex];\n        if (!(cache\n              ? cacheHas(cache, computed)\n              : includes(arrays[othIndex], computed, comparator))\n            ) {\n          continue outer;\n        }\n      }\n      if (seen) {\n        seen.push(computed);\n      }\n      result.push(value);\n    }\n  }\n  return result;\n}\n\nexport default baseIntersection;\n"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,gBAAgB;AACrC,OAAOC,aAAa,MAAM,qBAAqB;AAC/C,OAAOC,iBAAiB,MAAM,yBAAyB;AACvD,OAAOC,QAAQ,MAAM,gBAAgB;AACrC,OAAOC,SAAS,MAAM,iBAAiB;AACvC,OAAOC,QAAQ,MAAM,gBAAgB;;AAErC;AACA,IAAIC,SAAS,GAAGC,IAAI,CAACC,GAAG;;AAExB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,gBAAgBA,CAACC,MAAM,EAAEC,QAAQ,EAAEC,UAAU,EAAE;EACtD,IAAIC,QAAQ,GAAGD,UAAU,GAAGV,iBAAiB,GAAGD,aAAa;IACzDa,MAAM,GAAGJ,MAAM,CAAC,CAAC,CAAC,CAACI,MAAM;IACzBC,SAAS,GAAGL,MAAM,CAACI,MAAM;IACzBE,QAAQ,GAAGD,SAAS;IACpBE,MAAM,GAAGC,KAAK,CAACH,SAAS,CAAC;IACzBI,SAAS,GAAGC,QAAQ;IACpBC,MAAM,GAAG,EAAE;EAEf,OAAOL,QAAQ,EAAE,EAAE;IACjB,IAAIM,KAAK,GAAGZ,MAAM,CAACM,QAAQ,CAAC;IAC5B,IAAIA,QAAQ,IAAIL,QAAQ,EAAE;MACxBW,KAAK,GAAGnB,QAAQ,CAACmB,KAAK,EAAElB,SAAS,CAACO,QAAQ,CAAC,CAAC;IAC9C;IACAQ,SAAS,GAAGb,SAAS,CAACgB,KAAK,CAACR,MAAM,EAAEK,SAAS,CAAC;IAC9CF,MAAM,CAACD,QAAQ,CAAC,GAAG,CAACJ,UAAU,KAAKD,QAAQ,IAAKG,MAAM,IAAI,GAAG,IAAIQ,KAAK,CAACR,MAAM,IAAI,GAAI,CAAC,GAClF,IAAId,QAAQ,CAACgB,QAAQ,IAAIM,KAAK,CAAC,GAC/BC,SAAS;EACf;EACAD,KAAK,GAAGZ,MAAM,CAAC,CAAC,CAAC;EAEjB,IAAIc,KAAK,GAAG,CAAC,CAAC;IACVC,IAAI,GAAGR,MAAM,CAAC,CAAC,CAAC;EAEpBS,KAAK,EACL,OAAO,EAAEF,KAAK,GAAGV,MAAM,IAAIO,MAAM,CAACP,MAAM,GAAGK,SAAS,EAAE;IACpD,IAAIQ,KAAK,GAAGL,KAAK,CAACE,KAAK,CAAC;MACpBI,QAAQ,GAAGjB,QAAQ,GAAGA,QAAQ,CAACgB,KAAK,CAAC,GAAGA,KAAK;IAEjDA,KAAK,GAAIf,UAAU,IAAIe,KAAK,KAAK,CAAC,GAAIA,KAAK,GAAG,CAAC;IAC/C,IAAI,EAAEF,IAAI,GACFpB,QAAQ,CAACoB,IAAI,EAAEG,QAAQ,CAAC,GACxBf,QAAQ,CAACQ,MAAM,EAAEO,QAAQ,EAAEhB,UAAU,CAAC,CACzC,EAAE;MACLI,QAAQ,GAAGD,SAAS;MACpB,OAAO,EAAEC,QAAQ,EAAE;QACjB,IAAIa,KAAK,GAAGZ,MAAM,CAACD,QAAQ,CAAC;QAC5B,IAAI,EAAEa,KAAK,GACHxB,QAAQ,CAACwB,KAAK,EAAED,QAAQ,CAAC,GACzBf,QAAQ,CAACH,MAAM,CAACM,QAAQ,CAAC,EAAEY,QAAQ,EAAEhB,UAAU,CAAC,CAAC,EACnD;UACJ,SAASc,KAAK;QAChB;MACF;MACA,IAAID,IAAI,EAAE;QACRA,IAAI,CAACK,IAAI,CAACF,QAAQ,CAAC;MACrB;MACAP,MAAM,CAACS,IAAI,CAACH,KAAK,CAAC;IACpB;EACF;EACA,OAAON,MAAM;AACf;AAEA,eAAeZ,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
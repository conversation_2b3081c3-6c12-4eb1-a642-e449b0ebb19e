{"ast": null, "code": "function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nfunction _defineProperty(e, r, t) { return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, { value: t, enumerable: !0, configurable: !0, writable: !0 }) : e[r] = t, e; }\nfunction _toPropertyKey(t) { var i = _toPrimitive(t, \"string\"); return \"symbol\" == typeof i ? i : i + \"\"; }\nfunction _toPrimitive(t, r) { if (\"object\" != typeof t || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || \"default\"); if (\"object\" != typeof i) return i; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (\"string\" === r ? String : Number)(t); }\nimport { defineComponent, ref, computed, unref, watch, onMounted, onBeforeUnmount, provide, renderSlot } from 'vue';\nimport { useTimeoutFn } from '@vueuse/core';\nimport '../../../hooks/index.mjs';\nimport '../../../utils/index.mjs';\nimport { TOOLTIP_V2_OPEN, tooltipV2RootKey } from './constants.mjs';\nimport { tooltipV2RootProps } from './root.mjs';\nimport _export_sfc from '../../../_virtual/plugin-vue_export-helper.mjs';\nimport { isPropAbsent, isNumber } from '../../../utils/types.mjs';\nimport { useNamespace } from '../../../hooks/use-namespace/index.mjs';\nimport { useId } from '../../../hooks/use-id/index.mjs';\nvar __default__ = defineComponent({\n  name: \"ElTooltipV2Root\"\n});\nvar _sfc_main = /* @__PURE__ */defineComponent(_objectSpread(_objectSpread({}, __default__), {}, {\n  props: tooltipV2RootProps,\n  setup(__props, _ref) {\n    var expose = _ref.expose;\n    var props = __props;\n    var _open = ref(props.defaultOpen);\n    var triggerRef = ref(null);\n    var open = computed({\n      get: function get() {\n        return isPropAbsent(props.open) ? _open.value : props.open;\n      },\n      set: function set(open2) {\n        var _a;\n        _open.value = open2;\n        (_a = props[\"onUpdate:open\"]) == null ? void 0 : _a.call(props, open2);\n      }\n    });\n    var isOpenDelayed = computed(function () {\n      return isNumber(props.delayDuration) && props.delayDuration > 0;\n    });\n    var _useTimeoutFn = useTimeoutFn(function () {\n        open.value = true;\n      }, computed(function () {\n        return props.delayDuration;\n      }), {\n        immediate: false\n      }),\n      onDelayedOpen = _useTimeoutFn.start,\n      clearTimer = _useTimeoutFn.stop;\n    var ns = useNamespace(\"tooltip-v2\");\n    var contentId = useId();\n    var onNormalOpen = function onNormalOpen() {\n      clearTimer();\n      open.value = true;\n    };\n    var onDelayOpen = function onDelayOpen() {\n      unref(isOpenDelayed) ? onDelayedOpen() : onNormalOpen();\n    };\n    var onOpen = onNormalOpen;\n    var onClose = function onClose() {\n      clearTimer();\n      open.value = false;\n    };\n    var onChange = function onChange(open2) {\n      var _a;\n      if (open2) {\n        document.dispatchEvent(new CustomEvent(TOOLTIP_V2_OPEN));\n        onOpen();\n      }\n      (_a = props.onOpenChange) == null ? void 0 : _a.call(props, open2);\n    };\n    watch(open, onChange);\n    onMounted(function () {\n      document.addEventListener(TOOLTIP_V2_OPEN, onClose);\n    });\n    onBeforeUnmount(function () {\n      clearTimer();\n      document.removeEventListener(TOOLTIP_V2_OPEN, onClose);\n    });\n    provide(tooltipV2RootKey, {\n      contentId,\n      triggerRef,\n      ns,\n      onClose,\n      onDelayOpen,\n      onOpen\n    });\n    expose({\n      onOpen,\n      onClose\n    });\n    return function (_ctx, _cache) {\n      return renderSlot(_ctx.$slots, \"default\", {\n        open: unref(open)\n      });\n    };\n  }\n}));\nvar TooltipV2Root = /* @__PURE__ */_export_sfc(_sfc_main, [[\"__file\", \"root.vue\"]]);\nexport { TooltipV2Root as default };", "map": {"version": 3, "names": ["name", "_open", "ref", "props", "defaultOpen", "triggerRef", "open", "computed", "get", "isPropAbsent", "value", "set", "open2", "_a", "call", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "isNumber", "delayDuration", "_useTimeoutFn", "useTimeoutFn", "immediate", "onDelayedOpen", "start", "clearTimer", "stop", "ns", "useNamespace", "contentId", "useId", "onNormalOpen", "onDelayOpen", "unref", "onOpen", "onClose", "onChange", "document", "dispatchEvent", "CustomEvent", "TOOLTIP_V2_OPEN", "onOpenChange", "watch", "onMounted", "addEventListener", "onBeforeUnmount", "removeEventListener", "provide", "tooltipV2RootKey", "expose"], "sources": ["../../../../../../packages/components/tooltip-v2/src/root.vue"], "sourcesContent": ["<template>\n  <slot :open=\"open\" />\n</template>\n\n<script setup lang=\"ts\">\nimport {\n  computed,\n  onBeforeUnmount,\n  onMounted,\n  provide,\n  ref,\n  unref,\n  watch,\n} from 'vue'\nimport { useTimeoutFn } from '@vueuse/core'\nimport { useId, useNamespace } from '@element-plus/hooks'\nimport { isNumber, isPropAbsent } from '@element-plus/utils'\nimport { TOOLTIP_V2_OPEN, tooltipV2RootKey } from './constants'\nimport { tooltipV2RootProps } from './root'\n\ndefineOptions({\n  name: 'ElTooltipV2Root',\n})\n\nconst props = defineProps(tooltipV2RootProps)\n\n/**\n * internal open state, when no model value was provided, use this as indicator instead\n */\nconst _open = ref(props.defaultOpen)\nconst triggerRef = ref<HTMLElement | null>(null)\n\nconst open = computed<boolean>({\n  get: () => (isPropAbsent(props.open) ? _open.value : props.open),\n  set: (open) => {\n    _open.value = open\n    props['onUpdate:open']?.(open)\n  },\n})\n\nconst isOpenDelayed = computed(\n  () => isNumber(props.delayDuration) && props.delayDuration > 0\n)\n\nconst { start: onDelayedOpen, stop: clearTimer } = useTimeoutFn(\n  () => {\n    open.value = true\n  },\n  computed(() => props.delayDuration),\n  {\n    immediate: false,\n  }\n)\n\nconst ns = useNamespace('tooltip-v2')\n\nconst contentId = useId()\n\nconst onNormalOpen = () => {\n  clearTimer()\n  open.value = true\n}\n\nconst onDelayOpen = () => {\n  unref(isOpenDelayed) ? onDelayedOpen() : onNormalOpen()\n}\n\nconst onOpen = onNormalOpen\n\nconst onClose = () => {\n  clearTimer()\n  open.value = false\n}\n\nconst onChange = (open: boolean) => {\n  if (open) {\n    document.dispatchEvent(new CustomEvent(TOOLTIP_V2_OPEN))\n    onOpen()\n  }\n\n  props.onOpenChange?.(open)\n}\n\nwatch(open, onChange)\n\nonMounted(() => {\n  // Keeps only 1 tooltip open at a time\n  document.addEventListener(TOOLTIP_V2_OPEN, onClose)\n})\n\nonBeforeUnmount(() => {\n  clearTimer()\n  document.removeEventListener(TOOLTIP_V2_OPEN, onClose)\n})\n\nprovide(tooltipV2RootKey, {\n  contentId,\n  triggerRef,\n  ns,\n\n  onClose,\n  onDelayOpen,\n  onOpen,\n})\n\ndefineExpose({\n  /**\n   * @description open tooltip programmatically\n   */\n  onOpen,\n\n  /**\n   * @description close tooltip programmatically\n   */\n  onClose,\n})\n</script>\n"], "mappings": ";;;;;;;;;;;;;;;iCAoBc;EACZA,IAAM;AACR;;;;;;IAOM,IAAAC,KAAA,GAAQC,GAAI,CAAAC,KAAA,CAAMC,WAAW;IAC7B,IAAAC,UAAA,GAAaH,GAAA,CAAwB,IAAI;IAE/C,IAAMI,IAAA,GAAOC,QAAkB;MAC7BC,GAAA,EAAK,SAALA,IAAA;QAAA,OAAYC,YAAa,CAAAN,KAAA,CAAMG,IAAI,CAAI,GAAAL,KAAA,CAAMS,KAAA,GAAQP,KAAM,CAAAG,IAAA;MAAA;MAC3DK,GAAA,EAAK,SAALA,IAAMC,KAAS;QACb,IAAAC,EAAM;QACNZ,KAAA,CAAMS,KAAA,GAAAE,KAAA;QACR,CAAAC,EAAA,GAAAV,KAAA,sCAAAU,EAAA,CAAAC,IAAA,CAAAX,KAAA,EAAAS,KAAA;MAAA;IAGF,CAAM;IAIN,IAAMG,aAAS,GAAAR,QAAe,CAAM;MAAA,OAAAS,QAAA,CAAAb,KAAe,CAAAc,aAC3C,KAAAd,KAAA,CAAAc,aAAA;IAAA;IACJ,IAAAC,aAAA,GAAaC,YAAA;QAEfb,IAAA,CAAAI,KAAS,GAAM;MACf,GACaH,QAAA;QAAA,OAAAJ,KAAA,CAAAc,aAAA;MAAA;QAEfG,SAAA;MAEA,CAAM;MARWC,aAAA,GAAAH,aAAA,CAAAI,KAAA;MAAAC,UAAA,GAAAL,aAAA,CAAAM,IAAA;IAUjB,IAAMC,EAAA,GAAAC,YAAkB;IAExB,IAAMC,SAAA,GAAAC,KAAqB;IACd,IAAAC,YAAA,YAAAA,aAAA;MACXN,UAAa;MACfjB,IAAA,CAAAI,KAAA;IAEA;IACE,IAAAoB,WAAmB,YAAnBA,WAAmBA,CAAA,EAAkB;MACvCC,KAAA,CAAAhB,aAAA,IAAAM,aAAA,KAAAQ,YAAA;IAEA;IAEA,IAAMG,MAAA,GAAAH,YAAgB;IACT,IAAAI,OAAA,YAAAA,QAAA;MACXV,UAAa;MACfjB,IAAA,CAAAI,KAAA;IAEA,CAAM;IACJ,IAAIwB,QAAM,YAANA,QAAMA,CAAAtB,KAAA;MACR,IAAAC,EAAA;MACO,IAAAD,KAAA;QACTuB,QAAA,CAAAC,aAAA,KAAAC,WAAA,CAAAC,eAAA;QAEAN,MAAA;MAAyB;MAG3B,CAAAnB,EAAA,GAAAV,KAAoB,CAAAoC,YAAA,qBAAA1B,EAAA,CAAAC,IAAA,CAAAX,KAAA,EAAAS,KAAA;IAEpB;IAEW4B,KAAA,CAAAlC,IAAA,EAAA4B,QAAA;IACXO,SAAC;MAEDN,QAAA,CAAAO,gBAAsB,CAAAJ,eAAA,EAAAL,OAAA;IACpB,CAAW;IACFU,eAAA;MACVpB,UAAA;MAEDY,QAA0B,CAAAS,mBAAA,CAAAN,eAAA,EAAAL,OAAA;IAAA,CACxB;IACAY,OAAA,CAAAC,gBAAA;MACAnB,SAAA;MAEAtB,UAAA;MACAoB,EAAA;MACAQ,OAAA;MACDH,WAAA;MAEYE;IAAA,CAIX;IAKAe,MAAA;MACDf,MAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "import { defineComponent, inject, h, renderSlot } from 'vue';\nimport '../../../hooks/index.mjs';\nimport _export_sfc from '../../../_virtual/plugin-vue_export-helper.mjs';\nimport { useNamespace } from '../../../hooks/use-namespace/index.mjs';\nvar _sfc_main = defineComponent({\n  name: \"ElTreeNodeContent\",\n  props: {\n    node: {\n      type: Object,\n      required: true\n    },\n    renderContent: Function\n  },\n  setup(props) {\n    var ns = useNamespace(\"tree\");\n    var nodeInstance = inject(\"NodeInstance\");\n    var tree = inject(\"RootTree\");\n    return function () {\n      var node = props.node;\n      var data = node.data,\n        store = node.store;\n      return props.renderContent ? props.renderContent(h, {\n        _self: nodeInstance,\n        node,\n        data,\n        store\n      }) : renderSlot(tree.ctx.slots, \"default\", {\n        node,\n        data\n      }, function () {\n        return [h(\"span\", {\n          class: ns.be(\"node\", \"label\")\n        }, [node.label])];\n      });\n    };\n  }\n});\nvar NodeContent = /* @__PURE__ */_export_sfc(_sfc_main, [[\"__file\", \"tree-node-content.vue\"]]);\nexport { NodeContent as default };", "map": {"version": 3, "names": ["_sfc_main", "defineComponent", "name", "props", "node", "type", "Object", "required", "renderContent", "Function", "setup", "ns", "useNamespace", "nodeInstance", "inject", "tree", "data", "store", "h", "_self", "renderSlot", "ctx", "slots", "class", "be", "label"], "sources": ["../../../../../../packages/components/tree/src/tree-node-content.vue"], "sourcesContent": ["<script lang=\"ts\">\n// @ts-nocheck\nimport { defineComponent, h, inject, renderSlot } from 'vue'\n\nimport { useNamespace } from '@element-plus/hooks'\nimport type { ComponentInternalInstance } from 'vue'\nimport type { RootTreeType } from './tree.type'\n\nexport default defineComponent({\n  name: 'ElTreeNodeContent',\n  props: {\n    node: {\n      type: Object,\n      required: true,\n    },\n    renderContent: Function,\n  },\n  setup(props) {\n    const ns = useNamespace('tree')\n    const nodeInstance = inject<ComponentInternalInstance>('NodeInstance')\n    const tree = inject<RootTreeType>('RootTree')\n    return () => {\n      const node = props.node\n      const { data, store } = node\n      return props.renderContent\n        ? props.renderContent(h, { _self: nodeInstance, node, data, store })\n        : renderSlot(tree.ctx.slots, 'default', { node, data }, () => [\n            h('span', { class: ns.be('node', 'label') }, [node.label]),\n          ])\n    }\n  },\n})\n</script>\n"], "mappings": ";;;;AAQA,IAAKA,SAAA,GAAaC,eAAa;EAC7BC,IAAM;EACNC,KAAO;IACLC,IAAM;MACJC,IAAM,EAAAC,MAAA;MACNC,QAAU;IAAA,CACZ;IACAC,aAAe,EAAAC;EAAA,CACjB;EACAC,MAAMP,KAAO;IACL,IAAAQ,EAAA,GAAKC,YAAA,CAAa,MAAM;IACxB,IAAAC,YAAA,GAAeC,MAAA,CAAkC,cAAc;IAC/D,IAAAC,IAAA,GAAOD,MAAA,CAAqB,UAAU;IAC5C,OAAO,YAAM;MACX,IAAMV,IAAA,GAAOD,KAAM,CAAAC,IAAA;MACb,IAAEY,IAAA,GAAgBZ,IAAA,CAAhBY,IAAA;QAAMC,KAAU,GAAAb,IAAA,CAAVa,KAAU;MACjB,OAAAd,KAAA,CAAMK,aAAA,GACTL,KAAM,CAAAK,aAAA,CAAcU,CAAA,EAAG;QAAEC,KAAA,EAAON,YAAc;QAAAT,IAAA;QAAMY,IAAM;QAAAC;MAAA,CAAO,CACjE,GAAAG,UAAA,CAAWL,IAAA,CAAKM,GAAI,CAAAC,KAAA,EAAO,WAAW;QAAElB,IAAA;QAAMY;MAAK,GAAG;QAAA,OAAM,CAC1DE,CAAE,SAAQ;UAAEK,KAAA,EAAOZ,EAAG,CAAAa,EAAA,CAAG,MAAQ,SAAO;QAAE,GAAG,CAACpB,IAAA,CAAKqB,KAAK,CAAC,EAC1D;MAAA;IAAA,CACP;EAAA;AAEJ,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
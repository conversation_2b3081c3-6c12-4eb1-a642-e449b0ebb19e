{"ast": null, "code": "import { ref, onMounted } from 'vue';\nimport { useRoute } from 'vue-router';\nimport { ElMessage } from 'element-plus';\nimport LiveBroadcastDetails from './LiveBroadcastDetails.vue';\nvar __default__ = {\n  name: 'LiveShare'\n};\nexport default /*@__PURE__*/Object.assign(__default__, {\n  setup(__props, _ref) {\n    var __expose = _ref.expose;\n    __expose();\n    var route = useRoute();\n    // const router = useRouter()\n    var liveId = ref('');\n    var isShare = ref(false);\n    onMounted(function () {\n      // 从查询参数获取直播ID (因为现在使用 ?id= 格式)\n      liveId.value = route.query.id || route.params.id;\n\n      // 检查是否为分享链接\n      isShare.value = route.query.share === '1';\n      console.log('LiveShare页面加载，直播ID:', liveId.value);\n      console.log('是否为分享链接:', isShare.value);\n      console.log('路由参数:', route.params);\n      console.log('查询参数:', route.query);\n      if (!liveId.value) {\n        ElMessage.error('直播ID不能为空');\n        console.error('未找到直播ID，路由信息:', route);\n        return;\n      }\n    });\n\n    // 处理关闭回调 - 分享页面关闭时跳转到首页或显示提示\n    var handleCallback = function handleCallback() {\n      // 可以根据需要跳转到首页或其他页面\n      ElMessage.info('感谢观看，您可以通过浏览器返回按钮离开此页面');\n    };\n\n    // 处理模型值更新 - 分享页面允许关闭\n    var handleModelValueUpdate = function handleModelValueUpdate(value) {\n      if (!value) {\n        // 用户点击关闭按钮时的处理\n        handleCallback();\n      }\n    };\n    var __returned__ = {\n      route,\n      liveId,\n      isShare,\n      handleCallback,\n      handleModelValueUpdate,\n      ref,\n      onMounted,\n      get useRoute() {\n        return useRoute;\n      },\n      get ElMessage() {\n        return ElMessage;\n      },\n      LiveBroadcastDetails\n    };\n    Object.defineProperty(__returned__, '__isScriptSetup', {\n      enumerable: false,\n      value: true\n    });\n    return __returned__;\n  }\n});", "map": {"version": 3, "names": ["ref", "onMounted", "useRoute", "ElMessage", "LiveBroadcastDetails", "__default__", "name", "route", "liveId", "isShare", "value", "query", "id", "params", "share", "console", "log", "error", "handleCallback", "info", "handleModelValueUpdate"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/microApp/interaction/src/views/LiveManagement/LiveShare.vue"], "sourcesContent": ["<template>\n  <div class=\"LiveShare\">\n    <!-- 调试信息 -->\n    <div v-if=\"!liveId\" class=\"debug-info\">\n      <p>正在加载直播信息...</p>\n      <p>当前路由: {{ $route.path }}</p>\n      <p>查询参数: {{ JSON.stringify($route.query) }}</p>\n      <p>路径参数: {{ JSON.stringify($route.params) }}</p>\n    </div>\n\n    <!-- 直播详情组件，只有当有ID时才显示 -->\n    <LiveBroadcastDetails v-if=\"liveId\" :model-value=\"true\" :id=\"liveId\" :is-share=\"isShare\" @callback=\"handleCallback\"\n      @update:modelValue=\"handleModelValueUpdate\" />\n  </div>\n</template>\n\n<script>\nexport default { name: 'LiveShare' }\n</script>\n\n<script setup>\nimport { ref, onMounted } from 'vue'\nimport { useRoute } from 'vue-router'\nimport { ElMessage } from 'element-plus'\nimport LiveBroadcastDetails from './LiveBroadcastDetails.vue'\n\nconst route = useRoute()\n// const router = useRouter()\nconst liveId = ref('')\nconst isShare = ref(false)\n\nonMounted(() => {\n  // 从查询参数获取直播ID (因为现在使用 ?id= 格式)\n  liveId.value = route.query.id || route.params.id\n\n  // 检查是否为分享链接\n  isShare.value = route.query.share === '1'\n\n  console.log('LiveShare页面加载，直播ID:', liveId.value)\n  console.log('是否为分享链接:', isShare.value)\n  console.log('路由参数:', route.params)\n  console.log('查询参数:', route.query)\n\n  if (!liveId.value) {\n    ElMessage.error('直播ID不能为空')\n    console.error('未找到直播ID，路由信息:', route)\n    return\n  }\n})\n\n// 处理关闭回调 - 分享页面关闭时跳转到首页或显示提示\nconst handleCallback = () => {\n  // 可以根据需要跳转到首页或其他页面\n  ElMessage.info('感谢观看，您可以通过浏览器返回按钮离开此页面')\n}\n\n// 处理模型值更新 - 分享页面允许关闭\nconst handleModelValueUpdate = (value) => {\n  if (!value) {\n    // 用户点击关闭按钮时的处理\n    handleCallback()\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.LiveShare {\n  width: 100%;\n  height: 100vh;\n  overflow: hidden;\n\n  .debug-info {\n    padding: 20px;\n    background: #f5f5f5;\n    border: 1px solid #ddd;\n    margin: 20px;\n    border-radius: 8px;\n\n    p {\n      margin: 8px 0;\n      font-size: 14px;\n      color: #666;\n    }\n  }\n}\n</style>\n"], "mappings": "AAqBA,SAASA,GAAG,EAAEC,SAAS,QAAQ,KAAI;AACnC,SAASC,QAAQ,QAAQ,YAAW;AACpC,SAASC,SAAS,QAAQ,cAAa;AACvC,OAAOC,oBAAoB,MAAM,4BAA2B;AAP5D,IAAAC,WAAA,GAAe;EAAEC,IAAI,EAAE;AAAY;;;;;IASnC,IAAMC,KAAK,GAAGL,QAAQ,CAAC;IACvB;IACA,IAAMM,MAAM,GAAGR,GAAG,CAAC,EAAE;IACrB,IAAMS,OAAO,GAAGT,GAAG,CAAC,KAAK;IAEzBC,SAAS,CAAC,YAAM;MACd;MACAO,MAAM,CAACE,KAAK,GAAGH,KAAK,CAACI,KAAK,CAACC,EAAE,IAAIL,KAAK,CAACM,MAAM,CAACD,EAAC;;MAE/C;MACAH,OAAO,CAACC,KAAK,GAAGH,KAAK,CAACI,KAAK,CAACG,KAAK,KAAK,GAAE;MAExCC,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAER,MAAM,CAACE,KAAK;MAC/CK,OAAO,CAACC,GAAG,CAAC,UAAU,EAAEP,OAAO,CAACC,KAAK;MACrCK,OAAO,CAACC,GAAG,CAAC,OAAO,EAAET,KAAK,CAACM,MAAM;MACjCE,OAAO,CAACC,GAAG,CAAC,OAAO,EAAET,KAAK,CAACI,KAAK;MAEhC,IAAI,CAACH,MAAM,CAACE,KAAK,EAAE;QACjBP,SAAS,CAACc,KAAK,CAAC,UAAU;QAC1BF,OAAO,CAACE,KAAK,CAAC,eAAe,EAAEV,KAAK;QACpC;MACF;IACF,CAAC;;IAED;IACA,IAAMW,cAAc,GAAG,SAAjBA,cAAcA,CAAA,EAAS;MAC3B;MACAf,SAAS,CAACgB,IAAI,CAAC,wBAAwB;IACzC;;IAEA;IACA,IAAMC,sBAAsB,GAAG,SAAzBA,sBAAsBA,CAAIV,KAAK,EAAK;MACxC,IAAI,CAACA,KAAK,EAAE;QACV;QACAQ,cAAc,CAAC;MACjB;IACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
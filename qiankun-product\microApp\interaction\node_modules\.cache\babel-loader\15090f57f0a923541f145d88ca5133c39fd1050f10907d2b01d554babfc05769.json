{"ast": null, "code": "import { inject, ref, h } from 'vue';\nimport { debounce } from 'lodash-unified';\nimport '../../../../utils/index.mjs';\nimport { getCell, getColumnByCell, createTablePopper } from '../util.mjs';\nimport { TABLE_INJECTION_KEY } from '../tokens.mjs';\nimport { addClass, hasClass, removeClass } from '../../../../utils/dom/style.mjs';\nfunction isGreaterThan(a, b) {\n  var epsilon = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : 0.01;\n  return a - b > epsilon;\n}\nfunction useEvents(props) {\n  var parent = inject(TABLE_INJECTION_KEY);\n  var tooltipContent = ref(\"\");\n  var tooltipTrigger = ref(h(\"div\"));\n  var handleEvent = function handleEvent(event, row, name) {\n    var _a;\n    var table = parent;\n    var cell = getCell(event);\n    var column;\n    var namespace = (_a = table == null ? void 0 : table.vnode.el) == null ? void 0 : _a.dataset.prefix;\n    if (cell) {\n      column = getColumnByCell({\n        columns: props.store.states.columns.value\n      }, cell, namespace);\n      if (column) {\n        table == null ? void 0 : table.emit(`cell-${name}`, row, column, cell, event);\n      }\n    }\n    table == null ? void 0 : table.emit(`row-${name}`, row, column, event);\n  };\n  var handleDoubleClick = function handleDoubleClick(event, row) {\n    handleEvent(event, row, \"dblclick\");\n  };\n  var handleClick = function handleClick(event, row) {\n    props.store.commit(\"setCurrentRow\", row);\n    handleEvent(event, row, \"click\");\n  };\n  var handleContextMenu = function handleContextMenu(event, row) {\n    handleEvent(event, row, \"contextmenu\");\n  };\n  var handleMouseEnter = debounce(function (index) {\n    props.store.commit(\"setHoverRow\", index);\n  }, 30);\n  var handleMouseLeave = debounce(function () {\n    props.store.commit(\"setHoverRow\", null);\n  }, 30);\n  var getPadding = function getPadding(el) {\n    var style = window.getComputedStyle(el, null);\n    var paddingLeft = Number.parseInt(style.paddingLeft, 10) || 0;\n    var paddingRight = Number.parseInt(style.paddingRight, 10) || 0;\n    var paddingTop = Number.parseInt(style.paddingTop, 10) || 0;\n    var paddingBottom = Number.parseInt(style.paddingBottom, 10) || 0;\n    return {\n      left: paddingLeft,\n      right: paddingRight,\n      top: paddingTop,\n      bottom: paddingBottom\n    };\n  };\n  var toggleRowClassByCell = function toggleRowClassByCell(rowSpan, event, toggle) {\n    var node = event.target.parentNode;\n    while (rowSpan > 1) {\n      node = node == null ? void 0 : node.nextSibling;\n      if (!node || node.nodeName !== \"TR\") break;\n      toggle(node, \"hover-row hover-fixed-row\");\n      rowSpan--;\n    }\n  };\n  var handleCellMouseEnter = function handleCellMouseEnter(event, row, tooltipOptions) {\n    var _a;\n    var table = parent;\n    var cell = getCell(event);\n    var namespace = (_a = table == null ? void 0 : table.vnode.el) == null ? void 0 : _a.dataset.prefix;\n    if (cell) {\n      var column = getColumnByCell({\n        columns: props.store.states.columns.value\n      }, cell, namespace);\n      if (cell.rowSpan > 1) {\n        toggleRowClassByCell(cell.rowSpan, event, addClass);\n      }\n      var hoverState = table.hoverState = {\n        cell,\n        column,\n        row\n      };\n      table == null ? void 0 : table.emit(\"cell-mouse-enter\", hoverState.row, hoverState.column, hoverState.cell, event);\n    }\n    if (!tooltipOptions) {\n      return;\n    }\n    var cellChild = event.target.querySelector(\".cell\");\n    if (!(hasClass(cellChild, `${namespace}-tooltip`) && cellChild.childNodes.length)) {\n      return;\n    }\n    var range = document.createRange();\n    range.setStart(cellChild, 0);\n    range.setEnd(cellChild, cellChild.childNodes.length);\n    var _range$getBoundingCli = range.getBoundingClientRect(),\n      rangeWidth = _range$getBoundingCli.width,\n      rangeHeight = _range$getBoundingCli.height;\n    var offsetWidth = rangeWidth - Math.floor(rangeWidth);\n    var _cellChild$getBoundin = cellChild.getBoundingClientRect(),\n      cellChildWidth = _cellChild$getBoundin.width,\n      cellChildHeight = _cellChild$getBoundin.height;\n    if (offsetWidth < 1e-3) {\n      rangeWidth = Math.floor(rangeWidth);\n    }\n    var offsetHeight = rangeHeight - Math.floor(rangeHeight);\n    if (offsetHeight < 1e-3) {\n      rangeHeight = Math.floor(rangeHeight);\n    }\n    var _getPadding = getPadding(cellChild),\n      top = _getPadding.top,\n      left = _getPadding.left,\n      right = _getPadding.right,\n      bottom = _getPadding.bottom;\n    var horizontalPadding = left + right;\n    var verticalPadding = top + bottom;\n    if (isGreaterThan(rangeWidth + horizontalPadding, cellChildWidth) || isGreaterThan(rangeHeight + verticalPadding, cellChildHeight) || isGreaterThan(cellChild.scrollWidth, cellChildWidth)) {\n      createTablePopper(tooltipOptions, cell.innerText || cell.textContent, cell, table);\n    }\n  };\n  var handleCellMouseLeave = function handleCellMouseLeave(event) {\n    var cell = getCell(event);\n    if (!cell) return;\n    if (cell.rowSpan > 1) {\n      toggleRowClassByCell(cell.rowSpan, event, removeClass);\n    }\n    var oldHoverState = parent == null ? void 0 : parent.hoverState;\n    parent == null ? void 0 : parent.emit(\"cell-mouse-leave\", oldHoverState == null ? void 0 : oldHoverState.row, oldHoverState == null ? void 0 : oldHoverState.column, oldHoverState == null ? void 0 : oldHoverState.cell, event);\n  };\n  return {\n    handleDoubleClick,\n    handleClick,\n    handleContextMenu,\n    handleMouseEnter,\n    handleMouseLeave,\n    handleCellMouseEnter,\n    handleCellMouseLeave,\n    tooltipContent,\n    tooltipTrigger\n  };\n}\nexport { useEvents as default };", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "a", "b", "epsilon", "arguments", "length", "undefined", "useEvents", "props", "parent", "inject", "TABLE_INJECTION_KEY", "tooltipContent", "ref", "tooltipTrigger", "h", "handleEvent", "event", "row", "name", "_a", "table", "cell", "getCell", "column", "namespace", "vnode", "el", "dataset", "prefix", "getColumnByCell", "columns", "store", "states", "value", "emit", "handleDoubleClick", "handleClick", "commit", "handleContextMenu", "handleMouseEnter", "debounce", "index", "handleMouseLeave", "getPadding", "style", "window", "getComputedStyle", "paddingLeft", "Number", "parseInt", "paddingRight", "paddingTop", "paddingBottom", "left", "right", "top", "bottom", "toggleRowClassByCell", "rowSpan", "toggle", "node", "target", "parentNode", "nextS<PERSON>ling", "nodeName", "handleCellMouseEnter", "tooltipOptions", "addClass", "hoverState", "cellChild", "querySelector", "hasClass", "childNodes", "range", "document", "createRange", "setStart", "setEnd", "_range$getBoundingCli", "getBoundingClientRect", "rangeWidth", "width", "rangeHeight", "height", "offsetWidth", "Math", "floor", "_cellChild$getBoundin", "cell<PERSON><PERSON>d<PERSON><PERSON><PERSON>", "cellChildHeight", "offsetHeight", "_getPadding", "horizontalPadding", "verticalPadding", "scrollWidth", "createTablePopper", "innerText", "textContent", "handleCellMouseLeave", "removeClass", "oldHoverState"], "sources": ["../../../../../../../packages/components/table/src/table-body/events-helper.ts"], "sourcesContent": ["// @ts-nocheck\nimport { h, inject, ref } from 'vue'\nimport { debounce } from 'lodash-unified'\nimport { addClass, hasClass, removeClass } from '@element-plus/utils'\nimport { createTablePopper, getCell, getColumnByCell } from '../util'\nimport { TABLE_INJECTION_KEY } from '../tokens'\nimport type { TableColumnCtx } from '../table-column/defaults'\nimport type { TableBodyProps } from './defaults'\nimport type { TableOverflowTooltipOptions } from '../util'\n\nfunction isGreaterThan(a: number, b: number, epsilon = 0.01) {\n  return a - b > epsilon\n}\n\nfunction useEvents<T>(props: Partial<TableBodyProps<T>>) {\n  const parent = inject(TABLE_INJECTION_KEY)\n  const tooltipContent = ref('')\n  const tooltipTrigger = ref(h('div'))\n  const handleEvent = (event: Event, row: T, name: string) => {\n    const table = parent\n    const cell = getCell(event)\n    let column: TableColumnCtx<T>\n    const namespace = table?.vnode.el?.dataset.prefix\n    if (cell) {\n      column = getColumnByCell(\n        {\n          columns: props.store.states.columns.value,\n        },\n        cell,\n        namespace\n      )\n      if (column) {\n        table?.emit(`cell-${name}`, row, column, cell, event)\n      }\n    }\n    table?.emit(`row-${name}`, row, column, event)\n  }\n  const handleDoubleClick = (event: Event, row: T) => {\n    handleEvent(event, row, 'dblclick')\n  }\n  const handleClick = (event: Event, row: T) => {\n    props.store.commit('setCurrentRow', row)\n    handleEvent(event, row, 'click')\n  }\n  const handleContextMenu = (event: Event, row: T) => {\n    handleEvent(event, row, 'contextmenu')\n  }\n  const handleMouseEnter = debounce((index: number) => {\n    props.store.commit('setHoverRow', index)\n  }, 30)\n  const handleMouseLeave = debounce(() => {\n    props.store.commit('setHoverRow', null)\n  }, 30)\n  const getPadding = (el: HTMLElement) => {\n    const style = window.getComputedStyle(el, null)\n    const paddingLeft = Number.parseInt(style.paddingLeft, 10) || 0\n    const paddingRight = Number.parseInt(style.paddingRight, 10) || 0\n    const paddingTop = Number.parseInt(style.paddingTop, 10) || 0\n    const paddingBottom = Number.parseInt(style.paddingBottom, 10) || 0\n    return {\n      left: paddingLeft,\n      right: paddingRight,\n      top: paddingTop,\n      bottom: paddingBottom,\n    }\n  }\n\n  const toggleRowClassByCell = (\n    rowSpan: number,\n    event: MouseEvent,\n    toggle: (el: Element, cls: string) => void\n  ) => {\n    let node = event.target.parentNode\n    while (rowSpan > 1) {\n      node = node?.nextSibling\n      if (!node || node.nodeName !== 'TR') break\n      toggle(node, 'hover-row hover-fixed-row')\n      rowSpan--\n    }\n  }\n\n  const handleCellMouseEnter = (\n    event: MouseEvent,\n    row: T,\n    tooltipOptions: TableOverflowTooltipOptions\n  ) => {\n    const table = parent\n    const cell = getCell(event)\n    const namespace = table?.vnode.el?.dataset.prefix\n    if (cell) {\n      const column = getColumnByCell(\n        {\n          columns: props.store.states.columns.value,\n        },\n        cell,\n        namespace\n      )\n      if (cell.rowSpan > 1) {\n        toggleRowClassByCell(cell.rowSpan, event, addClass)\n      }\n      const hoverState = (table.hoverState = { cell, column, row })\n      table?.emit(\n        'cell-mouse-enter',\n        hoverState.row,\n        hoverState.column,\n        hoverState.cell,\n        event\n      )\n    }\n\n    if (!tooltipOptions) {\n      return\n    }\n\n    // 判断是否text-overflow, 如果是就显示tooltip\n    const cellChild = (event.target as HTMLElement).querySelector(\n      '.cell'\n    ) as HTMLElement\n    if (\n      !(\n        hasClass(cellChild, `${namespace}-tooltip`) &&\n        cellChild.childNodes.length\n      )\n    ) {\n      return\n    }\n    // use range width instead of scrollWidth to determine whether the text is overflowing\n    // to address a potential FireFox bug: https://bugzilla.mozilla.org/show_bug.cgi?id=1074543#c3\n    const range = document.createRange()\n    range.setStart(cellChild, 0)\n    range.setEnd(cellChild, cellChild.childNodes.length)\n    /** detail: https://github.com/element-plus/element-plus/issues/10790\n     *  What went wrong?\n     *  UI > Browser > Zoom, In Blink/WebKit, getBoundingClientRect() sometimes returns inexact values, probably due to lost precision during internal calculations. In the example above:\n     *    - Expected: 188\n     *    - Actual: 188.00000762939453\n     */\n    let { width: rangeWidth, height: rangeHeight } =\n      range.getBoundingClientRect()\n    const offsetWidth = rangeWidth - Math.floor(rangeWidth)\n    const { width: cellChildWidth, height: cellChildHeight } =\n      cellChild.getBoundingClientRect()\n    if (offsetWidth < 0.001) {\n      rangeWidth = Math.floor(rangeWidth)\n    }\n    const offsetHeight = rangeHeight - Math.floor(rangeHeight)\n    if (offsetHeight < 0.001) {\n      rangeHeight = Math.floor(rangeHeight)\n    }\n\n    const { top, left, right, bottom } = getPadding(cellChild)\n    const horizontalPadding = left + right\n    const verticalPadding = top + bottom\n    if (\n      isGreaterThan(rangeWidth + horizontalPadding, cellChildWidth) ||\n      isGreaterThan(rangeHeight + verticalPadding, cellChildHeight) ||\n      // When using a high-resolution screen, it is possible that a returns cellChild.scrollWidth value of 1921 and\n      // cellChildWidth returns a value of 1920.994140625. #16856 #16673\n      isGreaterThan(cellChild.scrollWidth, cellChildWidth)\n    ) {\n      createTablePopper(\n        tooltipOptions,\n        cell.innerText || cell.textContent,\n        cell,\n        table\n      )\n    }\n  }\n  const handleCellMouseLeave = (event) => {\n    const cell = getCell(event)\n    if (!cell) return\n    if (cell.rowSpan > 1) {\n      toggleRowClassByCell(cell.rowSpan, event, removeClass)\n    }\n    const oldHoverState = parent?.hoverState\n    parent?.emit(\n      'cell-mouse-leave',\n      oldHoverState?.row,\n      oldHoverState?.column,\n      oldHoverState?.cell,\n      event\n    )\n  }\n\n  return {\n    handleDoubleClick,\n    handleClick,\n    handleContextMenu,\n    handleMouseEnter,\n    handleMouseLeave,\n    handleCellMouseEnter,\n    handleCellMouseLeave,\n    tooltipContent,\n    tooltipTrigger,\n  }\n}\n\nexport default useEvents\n"], "mappings": ";;;;;;AAKA,SAASA,aAAaA,CAACC,CAAC,EAAEC,CAAC,EAAkB;EAAA,IAAhBC,OAAO,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,IAAI;EACzC,OAAOH,CAAC,GAAGC,CAAC,GAAGC,OAAO;AACxB;AACA,SAASI,SAASA,CAACC,KAAK,EAAE;EACxB,IAAMC,MAAM,GAAGC,MAAM,CAACC,mBAAmB,CAAC;EAC1C,IAAMC,cAAc,GAAGC,GAAG,CAAC,EAAE,CAAC;EAC9B,IAAMC,cAAc,GAAGD,GAAG,CAACE,CAAC,CAAC,KAAK,CAAC,CAAC;EACpC,IAAMC,WAAW,GAAG,SAAdA,WAAWA,CAAIC,KAAK,EAAEC,GAAG,EAAEC,IAAI,EAAK;IACxC,IAAIC,EAAE;IACN,IAAMC,KAAK,GAAGZ,MAAM;IACpB,IAAMa,IAAI,GAAGC,OAAO,CAACN,KAAK,CAAC;IAC3B,IAAIO,MAAM;IACV,IAAMC,SAAS,GAAG,CAACL,EAAE,GAAGC,KAAK,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,KAAK,CAACK,KAAK,CAACC,EAAE,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGP,EAAE,CAACQ,OAAO,CAACC,MAAM;IACrG,IAAIP,IAAI,EAAE;MACRE,MAAM,GAAGM,eAAe,CAAC;QACvBC,OAAO,EAAEvB,KAAK,CAACwB,KAAK,CAACC,MAAM,CAACF,OAAO,CAACG;MAC5C,CAAO,EAAEZ,IAAI,EAAEG,SAAS,CAAC;MACnB,IAAID,MAAM,EAAE;QACVH,KAAK,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,KAAK,CAACc,IAAI,CAAC,QAAQhB,IAAI,EAAE,EAAED,GAAG,EAAEM,MAAM,EAAEF,IAAI,EAAEL,KAAK,CAAC;MACrF;IACA;IACII,KAAK,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,KAAK,CAACc,IAAI,CAAC,OAAOhB,IAAI,EAAE,EAAED,GAAG,EAAEM,MAAM,EAAEP,KAAK,CAAC;EAC1E,CAAG;EACD,IAAMmB,iBAAiB,GAAG,SAApBA,iBAAiBA,CAAInB,KAAK,EAAEC,GAAG,EAAK;IACxCF,WAAW,CAACC,KAAK,EAAEC,GAAG,EAAE,UAAU,CAAC;EACvC,CAAG;EACD,IAAMmB,WAAW,GAAG,SAAdA,WAAWA,CAAIpB,KAAK,EAAEC,GAAG,EAAK;IAClCV,KAAK,CAACwB,KAAK,CAACM,MAAM,CAAC,eAAe,EAAEpB,GAAG,CAAC;IACxCF,WAAW,CAACC,KAAK,EAAEC,GAAG,EAAE,OAAO,CAAC;EACpC,CAAG;EACD,IAAMqB,iBAAiB,GAAG,SAApBA,iBAAiBA,CAAItB,KAAK,EAAEC,GAAG,EAAK;IACxCF,WAAW,CAACC,KAAK,EAAEC,GAAG,EAAE,aAAa,CAAC;EAC1C,CAAG;EACD,IAAMsB,gBAAgB,GAAGC,QAAQ,CAAC,UAACC,KAAK,EAAK;IAC3ClC,KAAK,CAACwB,KAAK,CAACM,MAAM,CAAC,aAAa,EAAEI,KAAK,CAAC;EAC5C,CAAG,EAAE,EAAE,CAAC;EACN,IAAMC,gBAAgB,GAAGF,QAAQ,CAAC,YAAM;IACtCjC,KAAK,CAACwB,KAAK,CAACM,MAAM,CAAC,aAAa,EAAE,IAAI,CAAC;EAC3C,CAAG,EAAE,EAAE,CAAC;EACN,IAAMM,UAAU,GAAG,SAAbA,UAAUA,CAAIjB,EAAE,EAAK;IACzB,IAAMkB,KAAK,GAAGC,MAAM,CAACC,gBAAgB,CAACpB,EAAE,EAAE,IAAI,CAAC;IAC/C,IAAMqB,WAAW,GAAGC,MAAM,CAACC,QAAQ,CAACL,KAAK,CAACG,WAAW,EAAE,EAAE,CAAC,IAAI,CAAC;IAC/D,IAAMG,YAAY,GAAGF,MAAM,CAACC,QAAQ,CAACL,KAAK,CAACM,YAAY,EAAE,EAAE,CAAC,IAAI,CAAC;IACjE,IAAMC,UAAU,GAAGH,MAAM,CAACC,QAAQ,CAACL,KAAK,CAACO,UAAU,EAAE,EAAE,CAAC,IAAI,CAAC;IAC7D,IAAMC,aAAa,GAAGJ,MAAM,CAACC,QAAQ,CAACL,KAAK,CAACQ,aAAa,EAAE,EAAE,CAAC,IAAI,CAAC;IACnE,OAAO;MACLC,IAAI,EAAEN,WAAW;MACjBO,KAAK,EAAEJ,YAAY;MACnBK,GAAG,EAAEJ,UAAU;MACfK,MAAM,EAAEJ;IACd,CAAK;EACL,CAAG;EACD,IAAMK,oBAAoB,GAAG,SAAvBA,oBAAoBA,CAAIC,OAAO,EAAE1C,KAAK,EAAE2C,MAAM,EAAK;IACvD,IAAIC,IAAI,GAAG5C,KAAK,CAAC6C,MAAM,CAACC,UAAU;IAClC,OAAOJ,OAAO,GAAG,CAAC,EAAE;MAClBE,IAAI,GAAGA,IAAI,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,IAAI,CAACG,WAAW;MAC/C,IAAI,CAACH,IAAI,IAAIA,IAAI,CAACI,QAAQ,KAAK,IAAI,EACjC;MACFL,MAAM,CAACC,IAAI,EAAE,2BAA2B,CAAC;MACzCF,OAAO,EAAE;IACf;EACA,CAAG;EACD,IAAMO,oBAAoB,GAAG,SAAvBA,oBAAoBA,CAAIjD,KAAK,EAAEC,GAAG,EAAEiD,cAAc,EAAK;IAC3D,IAAI/C,EAAE;IACN,IAAMC,KAAK,GAAGZ,MAAM;IACpB,IAAMa,IAAI,GAAGC,OAAO,CAACN,KAAK,CAAC;IAC3B,IAAMQ,SAAS,GAAG,CAACL,EAAE,GAAGC,KAAK,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,KAAK,CAACK,KAAK,CAACC,EAAE,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGP,EAAE,CAACQ,OAAO,CAACC,MAAM;IACrG,IAAIP,IAAI,EAAE;MACR,IAAME,MAAM,GAAGM,eAAe,CAAC;QAC7BC,OAAO,EAAEvB,KAAK,CAACwB,KAAK,CAACC,MAAM,CAACF,OAAO,CAACG;MAC5C,CAAO,EAAEZ,IAAI,EAAEG,SAAS,CAAC;MACnB,IAAIH,IAAI,CAACqC,OAAO,GAAG,CAAC,EAAE;QACpBD,oBAAoB,CAACpC,IAAI,CAACqC,OAAO,EAAE1C,KAAK,EAAEmD,QAAQ,CAAC;MAC3D;MACM,IAAMC,UAAU,GAAGhD,KAAK,CAACgD,UAAU,GAAG;QAAE/C,IAAI;QAAEE,MAAM;QAAEN;MAAG,CAAE;MAC3DG,KAAK,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,KAAK,CAACc,IAAI,CAAC,kBAAkB,EAAEkC,UAAU,CAACnD,GAAG,EAAEmD,UAAU,CAAC7C,MAAM,EAAE6C,UAAU,CAAC/C,IAAI,EAAEL,KAAK,CAAC;IACxH;IACI,IAAI,CAACkD,cAAc,EAAE;MACnB;IACN;IACI,IAAMG,SAAS,GAAGrD,KAAK,CAAC6C,MAAM,CAACS,aAAa,CAAC,OAAO,CAAC;IACrD,IAAI,EAAEC,QAAQ,CAACF,SAAS,EAAE,GAAG7C,SAAS,UAAU,CAAC,IAAI6C,SAAS,CAACG,UAAU,CAACpE,MAAM,CAAC,EAAE;MACjF;IACN;IACI,IAAMqE,KAAK,GAAGC,QAAQ,CAACC,WAAW,EAAE;IACpCF,KAAK,CAACG,QAAQ,CAACP,SAAS,EAAE,CAAC,CAAC;IAC5BI,KAAK,CAACI,MAAM,CAACR,SAAS,EAAEA,SAAS,CAACG,UAAU,CAACpE,MAAM,CAAC;IACpD,IAAA0E,qBAAA,GAAiDL,KAAK,CAACM,qBAAqB,EAAE;MAAjEC,UAAU,GAAAF,qBAAA,CAAjBG,KAAK;MAAsBC,WAAW,GAAAJ,qBAAA,CAAnBK,MAAM;IAC/B,IAAMC,WAAW,GAAGJ,UAAU,GAAGK,IAAI,CAACC,KAAK,CAACN,UAAU,CAAC;IACvD,IAAAO,qBAAA,GAA2DlB,SAAS,CAACU,qBAAqB,EAAE;MAA7ES,cAAc,GAAAD,qBAAA,CAArBN,KAAK;MAA0BQ,eAAe,GAAAF,qBAAA,CAAvBJ,MAAM;IACrC,IAAIC,WAAW,GAAG,IAAI,EAAE;MACtBJ,UAAU,GAAGK,IAAI,CAACC,KAAK,CAACN,UAAU,CAAC;IACzC;IACI,IAAMU,YAAY,GAAGR,WAAW,GAAGG,IAAI,CAACC,KAAK,CAACJ,WAAW,CAAC;IAC1D,IAAIQ,YAAY,GAAG,IAAI,EAAE;MACvBR,WAAW,GAAGG,IAAI,CAACC,KAAK,CAACJ,WAAW,CAAC;IAC3C;IACI,IAAAS,WAAA,GAAqChD,UAAU,CAAC0B,SAAS,CAAC;MAAlDd,GAAG,GAAAoC,WAAA,CAAHpC,GAAG;MAAEF,IAAI,GAAAsC,WAAA,CAAJtC,IAAI;MAAEC,KAAK,GAAAqC,WAAA,CAALrC,KAAK;MAAEE,MAAM,GAAAmC,WAAA,CAANnC,MAAM;IAChC,IAAMoC,iBAAiB,GAAGvC,IAAI,GAAGC,KAAK;IACtC,IAAMuC,eAAe,GAAGtC,GAAG,GAAGC,MAAM;IACpC,IAAIzD,aAAa,CAACiF,UAAU,GAAGY,iBAAiB,EAAEJ,cAAc,CAAC,IAAIzF,aAAa,CAACmF,WAAW,GAAGW,eAAe,EAAEJ,eAAe,CAAC,IAAI1F,aAAa,CAACsE,SAAS,CAACyB,WAAW,EAAEN,cAAc,CAAC,EAAE;MAC1LO,iBAAiB,CAAC7B,cAAc,EAAE7C,IAAI,CAAC2E,SAAS,IAAI3E,IAAI,CAAC4E,WAAW,EAAE5E,IAAI,EAAED,KAAK,CAAC;IACxF;EACA,CAAG;EACD,IAAM8E,oBAAoB,GAAG,SAAvBA,oBAAoBA,CAAIlF,KAAK,EAAK;IACtC,IAAMK,IAAI,GAAGC,OAAO,CAACN,KAAK,CAAC;IAC3B,IAAI,CAACK,IAAI,EACP;IACF,IAAIA,IAAI,CAACqC,OAAO,GAAG,CAAC,EAAE;MACpBD,oBAAoB,CAACpC,IAAI,CAACqC,OAAO,EAAE1C,KAAK,EAAEmF,WAAW,CAAC;IAC5D;IACI,IAAMC,aAAa,GAAG5F,MAAM,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,MAAM,CAAC4D,UAAU;IACjE5D,MAAM,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,MAAM,CAAC0B,IAAI,CAAC,kBAAkB,EAAEkE,aAAa,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,aAAa,CAACnF,GAAG,EAAEmF,aAAa,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,aAAa,CAAC7E,MAAM,EAAE6E,aAAa,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,aAAa,CAAC/E,IAAI,EAAEL,KAAK,CAAC;EACpO,CAAG;EACD,OAAO;IACLmB,iBAAiB;IACjBC,WAAW;IACXE,iBAAiB;IACjBC,gBAAgB;IAChBG,gBAAgB;IAChBuB,oBAAoB;IACpBiC,oBAAoB;IACpBvF,cAAc;IACdE;EACJ,CAAG;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
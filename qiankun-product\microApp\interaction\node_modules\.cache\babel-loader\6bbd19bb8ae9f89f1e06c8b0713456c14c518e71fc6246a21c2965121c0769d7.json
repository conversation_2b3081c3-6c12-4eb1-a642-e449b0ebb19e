{"ast": null, "code": "import { inject, computed } from 'vue';\nimport { TABLE_INJECTION_KEY } from '../tokens.mjs';\nfunction useMapState() {\n  var table = inject(TABLE_INJECTION_KEY);\n  var store = table == null ? void 0 : table.store;\n  var leftFixedLeafCount = computed(function () {\n    return store.states.fixedLeafColumnsLength.value;\n  });\n  var rightFixedLeafCount = computed(function () {\n    return store.states.rightFixedColumns.value.length;\n  });\n  var columnsCount = computed(function () {\n    return store.states.columns.value.length;\n  });\n  var leftFixedCount = computed(function () {\n    return store.states.fixedColumns.value.length;\n  });\n  var rightFixedCount = computed(function () {\n    return store.states.rightFixedColumns.value.length;\n  });\n  return {\n    leftFixedLeafCount,\n    rightFixedLeafCount,\n    columnsCount,\n    leftFixedCount,\n    rightFixedCount,\n    columns: store.states.columns\n  };\n}\nexport { useMapState as default };", "map": {"version": 3, "names": ["useMapState", "table", "inject", "TABLE_INJECTION_KEY", "store", "leftFixedLeafCount", "computed", "states", "fixedLeafColumnsLength", "value", "rightFixedLeafCount", "rightFixedColumns", "length", "columnsCount", "columns", "leftFixedCount", "fixedColumns", "rightFixedCount"], "sources": ["../../../../../../../packages/components/table/src/table-footer/mapState-helper.ts"], "sourcesContent": ["import { computed, inject } from 'vue'\nimport { TABLE_INJECTION_KEY } from '../tokens'\n\nfunction useMapState() {\n  const table = inject(TABLE_INJECTION_KEY)\n  const store = table?.store\n  const leftFixedLeafCount = computed(() => {\n    return store.states.fixedLeafColumnsLength.value\n  })\n  const rightFixedLeafCount = computed(() => {\n    return store.states.rightFixedColumns.value.length\n  })\n  const columnsCount = computed(() => {\n    return store.states.columns.value.length\n  })\n  const leftFixedCount = computed(() => {\n    return store.states.fixedColumns.value.length\n  })\n  const rightFixedCount = computed(() => {\n    return store.states.rightFixedColumns.value.length\n  })\n\n  return {\n    leftFixedLeafCount,\n    rightFixedLeafCount,\n    columnsCount,\n    leftFixedCount,\n    rightFixedCount,\n    columns: store.states.columns,\n  }\n}\n\nexport default useMapState\n"], "mappings": ";;AAEA,SAASA,WAAWA,CAAA,EAAG;EACrB,IAAMC,KAAK,GAAGC,MAAM,CAACC,mBAAmB,CAAC;EACzC,IAAMC,KAAK,GAAGH,KAAK,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,KAAK,CAACG,KAAK;EAClD,IAAMC,kBAAkB,GAAGC,QAAQ,CAAC,YAAM;IACxC,OAAOF,KAAK,CAACG,MAAM,CAACC,sBAAsB,CAACC,KAAK;EACpD,CAAG,CAAC;EACF,IAAMC,mBAAmB,GAAGJ,QAAQ,CAAC,YAAM;IACzC,OAAOF,KAAK,CAACG,MAAM,CAACI,iBAAiB,CAACF,KAAK,CAACG,MAAM;EACtD,CAAG,CAAC;EACF,IAAMC,YAAY,GAAGP,QAAQ,CAAC,YAAM;IAClC,OAAOF,KAAK,CAACG,MAAM,CAACO,OAAO,CAACL,KAAK,CAACG,MAAM;EAC5C,CAAG,CAAC;EACF,IAAMG,cAAc,GAAGT,QAAQ,CAAC,YAAM;IACpC,OAAOF,KAAK,CAACG,MAAM,CAACS,YAAY,CAACP,KAAK,CAACG,MAAM;EACjD,CAAG,CAAC;EACF,IAAMK,eAAe,GAAGX,QAAQ,CAAC,YAAM;IACrC,OAAOF,KAAK,CAACG,MAAM,CAACI,iBAAiB,CAACF,KAAK,CAACG,MAAM;EACtD,CAAG,CAAC;EACF,OAAO;IACLP,kBAAkB;IAClBK,mBAAmB;IACnBG,YAAY;IACZE,cAAc;IACdE,eAAe;IACfH,OAAO,EAAEV,KAAK,CAACG,MAAM,CAACO;EAC1B,CAAG;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
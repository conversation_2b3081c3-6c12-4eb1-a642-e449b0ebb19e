{"ast": null, "code": "import createFlow from './_createFlow.js';\n\n/**\n * This method is like `_.flow` except that it creates a function that\n * invokes the given functions from right to left.\n *\n * @static\n * @since 3.0.0\n * @memberOf _\n * @category Util\n * @param {...(Function|Function[])} [funcs] The functions to invoke.\n * @returns {Function} Returns the new composite function.\n * @see _.flow\n * @example\n *\n * function square(n) {\n *   return n * n;\n * }\n *\n * var addSquare = _.flowRight([square, _.add]);\n * addSquare(1, 2);\n * // => 9\n */\nvar flowRight = createFlow(true);\nexport default flowRight;", "map": {"version": 3, "names": ["createFlow", "flowRight"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/node_modules/.pnpm/lodash-es@4.17.21/node_modules/lodash-es/flowRight.js"], "sourcesContent": ["import createFlow from './_createFlow.js';\n\n/**\n * This method is like `_.flow` except that it creates a function that\n * invokes the given functions from right to left.\n *\n * @static\n * @since 3.0.0\n * @memberOf _\n * @category Util\n * @param {...(Function|Function[])} [funcs] The functions to invoke.\n * @returns {Function} Returns the new composite function.\n * @see _.flow\n * @example\n *\n * function square(n) {\n *   return n * n;\n * }\n *\n * var addSquare = _.flowRight([square, _.add]);\n * addSquare(1, 2);\n * // => 9\n */\nvar flowRight = createFlow(true);\n\nexport default flowRight;\n"], "mappings": "AAAA,OAAOA,UAAU,MAAM,kBAAkB;;AAEzC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIC,SAAS,GAAGD,UAAU,CAAC,IAAI,CAAC;AAEhC,eAAeC,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nfunction _defineProperty(e, r, t) { return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, { value: t, enumerable: !0, configurable: !0, writable: !0 }) : e[r] = t, e; }\nfunction _toPropertyKey(t) { var i = _toPrimitive(t, \"string\"); return \"symbol\" == typeof i ? i : i + \"\"; }\nfunction _toPrimitive(t, r) { if (\"object\" != typeof t || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || \"default\"); if (\"object\" != typeof i) return i; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (\"string\" === r ? String : Number)(t); }\nimport { getCurrentInstance, toRefs, ref, watch, unref } from 'vue';\nimport '../../../../utils/index.mjs';\nimport { orderBy, getKeysMap, toggleRowStatus, getRowIdentity, getColumnById, getColumnByKey } from '../util.mjs';\nimport useExpand from './expand.mjs';\nimport useCurrent from './current.mjs';\nimport useTree from './tree.mjs';\nimport { hasOwn } from '@vue/shared';\nvar sortData = function sortData(data, states) {\n  var sortingColumn = states.sortingColumn;\n  if (!sortingColumn || typeof sortingColumn.sortable === \"string\") {\n    return data;\n  }\n  return orderBy(data, states.sortProp, states.sortOrder, sortingColumn.sortMethod, sortingColumn.sortBy);\n};\nvar _doFlattenColumns = function doFlattenColumns(columns) {\n  var result = [];\n  columns.forEach(function (column) {\n    if (column.children && column.children.length > 0) {\n      result.push.apply(result, _doFlattenColumns(column.children));\n    } else {\n      result.push(column);\n    }\n  });\n  return result;\n};\nfunction useWatcher() {\n  var _a;\n  var instance = getCurrentInstance();\n  var _toRefs = toRefs((_a = instance.proxy) == null ? void 0 : _a.$props),\n    tableSize = _toRefs.size;\n  var rowKey = ref(null);\n  var data = ref([]);\n  var _data = ref([]);\n  var isComplex = ref(false);\n  var _columns = ref([]);\n  var originColumns = ref([]);\n  var columns = ref([]);\n  var fixedColumns = ref([]);\n  var rightFixedColumns = ref([]);\n  var leafColumns = ref([]);\n  var fixedLeafColumns = ref([]);\n  var rightFixedLeafColumns = ref([]);\n  var updateOrderFns = [];\n  var leafColumnsLength = ref(0);\n  var fixedLeafColumnsLength = ref(0);\n  var rightFixedLeafColumnsLength = ref(0);\n  var isAllSelected = ref(false);\n  var selection = ref([]);\n  var reserveSelection = ref(false);\n  var selectOnIndeterminate = ref(false);\n  var selectable = ref(null);\n  var filters = ref({});\n  var filteredData = ref(null);\n  var sortingColumn = ref(null);\n  var sortProp = ref(null);\n  var sortOrder = ref(null);\n  var hoverRow = ref(null);\n  watch(data, function () {\n    return instance.state && scheduleLayout(false);\n  }, {\n    deep: true\n  });\n  var assertRowKey = function assertRowKey() {\n    if (!rowKey.value) throw new Error(\"[ElTable] prop row-key is required\");\n  };\n  var _updateChildFixed = function updateChildFixed(column) {\n    var _a2;\n    (_a2 = column.children) == null ? void 0 : _a2.forEach(function (childColumn) {\n      childColumn.fixed = column.fixed;\n      _updateChildFixed(childColumn);\n    });\n  };\n  var updateColumns = function updateColumns() {\n    _columns.value.forEach(function (column) {\n      _updateChildFixed(column);\n    });\n    fixedColumns.value = _columns.value.filter(function (column) {\n      return column.fixed === true || column.fixed === \"left\";\n    });\n    rightFixedColumns.value = _columns.value.filter(function (column) {\n      return column.fixed === \"right\";\n    });\n    if (fixedColumns.value.length > 0 && _columns.value[0] && _columns.value[0].type === \"selection\" && !_columns.value[0].fixed) {\n      _columns.value[0].fixed = true;\n      fixedColumns.value.unshift(_columns.value[0]);\n    }\n    var notFixedColumns = _columns.value.filter(function (column) {\n      return !column.fixed;\n    });\n    originColumns.value = [].concat(fixedColumns.value).concat(notFixedColumns).concat(rightFixedColumns.value);\n    var leafColumns2 = _doFlattenColumns(notFixedColumns);\n    var fixedLeafColumns2 = _doFlattenColumns(fixedColumns.value);\n    var rightFixedLeafColumns2 = _doFlattenColumns(rightFixedColumns.value);\n    leafColumnsLength.value = leafColumns2.length;\n    fixedLeafColumnsLength.value = fixedLeafColumns2.length;\n    rightFixedLeafColumnsLength.value = rightFixedLeafColumns2.length;\n    columns.value = [].concat(fixedLeafColumns2).concat(leafColumns2).concat(rightFixedLeafColumns2);\n    isComplex.value = fixedColumns.value.length > 0 || rightFixedColumns.value.length > 0;\n  };\n  var scheduleLayout = function scheduleLayout(needUpdateColumns) {\n    var immediate = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;\n    if (needUpdateColumns) {\n      updateColumns();\n    }\n    if (immediate) {\n      instance.state.doLayout();\n    } else {\n      instance.state.debouncedUpdateLayout();\n    }\n  };\n  var isSelected = function isSelected(row) {\n    return selection.value.includes(row);\n  };\n  var clearSelection = function clearSelection() {\n    isAllSelected.value = false;\n    var oldSelection = selection.value;\n    selection.value = [];\n    if (oldSelection.length) {\n      instance.emit(\"selection-change\", []);\n    }\n  };\n  var cleanSelection = function cleanSelection() {\n    var deleted;\n    if (rowKey.value) {\n      deleted = [];\n      var selectedMap = getKeysMap(selection.value, rowKey.value);\n      var dataMap = getKeysMap(data.value, rowKey.value);\n      for (var key in selectedMap) {\n        if (hasOwn(selectedMap, key) && !dataMap[key]) {\n          deleted.push(selectedMap[key].row);\n        }\n      }\n    } else {\n      deleted = selection.value.filter(function (item) {\n        return !data.value.includes(item);\n      });\n    }\n    if (deleted.length) {\n      var newSelection = selection.value.filter(function (item) {\n        return !deleted.includes(item);\n      });\n      selection.value = newSelection;\n      instance.emit(\"selection-change\", newSelection.slice());\n    }\n  };\n  var getSelectionRows = function getSelectionRows() {\n    return (selection.value || []).slice();\n  };\n  var toggleRowSelection = function toggleRowSelection(row) {\n    var selected = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : void 0;\n    var emitChange = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : true;\n    var changed = toggleRowStatus(selection.value, row, selected);\n    if (changed) {\n      var newSelection = (selection.value || []).slice();\n      if (emitChange) {\n        instance.emit(\"select\", newSelection, row);\n      }\n      instance.emit(\"selection-change\", newSelection);\n    }\n  };\n  var _toggleAllSelection = function _toggleAllSelection() {\n    var _a2, _b;\n    var value = selectOnIndeterminate.value ? !isAllSelected.value : !(isAllSelected.value || selection.value.length);\n    isAllSelected.value = value;\n    var selectionChanged = false;\n    var childrenCount = 0;\n    var rowKey2 = (_b = (_a2 = instance == null ? void 0 : instance.store) == null ? void 0 : _a2.states) == null ? void 0 : _b.rowKey.value;\n    data.value.forEach(function (row, index) {\n      var rowIndex = index + childrenCount;\n      if (selectable.value) {\n        if (selectable.value.call(null, row, rowIndex) && toggleRowStatus(selection.value, row, value)) {\n          selectionChanged = true;\n        }\n      } else {\n        if (toggleRowStatus(selection.value, row, value)) {\n          selectionChanged = true;\n        }\n      }\n      childrenCount += _getChildrenCount(getRowIdentity(row, rowKey2));\n    });\n    if (selectionChanged) {\n      instance.emit(\"selection-change\", selection.value ? selection.value.slice() : []);\n    }\n    instance.emit(\"select-all\", (selection.value || []).slice());\n  };\n  var updateSelectionByRowKey = function updateSelectionByRowKey() {\n    var selectedMap = getKeysMap(selection.value, rowKey.value);\n    data.value.forEach(function (row) {\n      var rowId = getRowIdentity(row, rowKey.value);\n      var rowInfo = selectedMap[rowId];\n      if (rowInfo) {\n        selection.value[rowInfo.index] = row;\n      }\n    });\n  };\n  var updateAllSelected = function updateAllSelected() {\n    var _a2, _b, _c;\n    if (((_a2 = data.value) == null ? void 0 : _a2.length) === 0) {\n      isAllSelected.value = false;\n      return;\n    }\n    var selectedMap;\n    if (rowKey.value) {\n      selectedMap = getKeysMap(selection.value, rowKey.value);\n    }\n    var isSelected2 = function isSelected2(row) {\n      if (selectedMap) {\n        return !!selectedMap[getRowIdentity(row, rowKey.value)];\n      } else {\n        return selection.value.includes(row);\n      }\n    };\n    var isAllSelected_ = true;\n    var selectedCount = 0;\n    var childrenCount = 0;\n    for (var i = 0, j = (data.value || []).length; i < j; i++) {\n      var keyProp = (_c = (_b = instance == null ? void 0 : instance.store) == null ? void 0 : _b.states) == null ? void 0 : _c.rowKey.value;\n      var rowIndex = i + childrenCount;\n      var item = data.value[i];\n      var isRowSelectable = selectable.value && selectable.value.call(null, item, rowIndex);\n      if (!isSelected2(item)) {\n        if (!selectable.value || isRowSelectable) {\n          isAllSelected_ = false;\n          break;\n        }\n      } else {\n        selectedCount++;\n      }\n      childrenCount += _getChildrenCount(getRowIdentity(item, keyProp));\n    }\n    if (selectedCount === 0) isAllSelected_ = false;\n    isAllSelected.value = isAllSelected_;\n  };\n  var _getChildrenCount = function getChildrenCount(rowKey2) {\n    var _a2;\n    if (!instance || !instance.store) return 0;\n    var treeData = instance.store.states.treeData;\n    var count = 0;\n    var children = (_a2 = treeData.value[rowKey2]) == null ? void 0 : _a2.children;\n    if (children) {\n      count += children.length;\n      children.forEach(function (childKey) {\n        count += _getChildrenCount(childKey);\n      });\n    }\n    return count;\n  };\n  var updateFilters = function updateFilters(columns2, values) {\n    if (!Array.isArray(columns2)) {\n      columns2 = [columns2];\n    }\n    var filters_ = {};\n    columns2.forEach(function (col) {\n      filters.value[col.id] = values;\n      filters_[col.columnKey || col.id] = values;\n    });\n    return filters_;\n  };\n  var updateSort = function updateSort(column, prop, order) {\n    if (sortingColumn.value && sortingColumn.value !== column) {\n      sortingColumn.value.order = null;\n    }\n    sortingColumn.value = column;\n    sortProp.value = prop;\n    sortOrder.value = order;\n  };\n  var execFilter = function execFilter() {\n    var sourceData = unref(_data);\n    Object.keys(filters.value).forEach(function (columnId) {\n      var values = filters.value[columnId];\n      if (!values || values.length === 0) return;\n      var column = getColumnById({\n        columns: columns.value\n      }, columnId);\n      if (column && column.filterMethod) {\n        sourceData = sourceData.filter(function (row) {\n          return values.some(function (value) {\n            return column.filterMethod.call(null, value, row, column);\n          });\n        });\n      }\n    });\n    filteredData.value = sourceData;\n  };\n  var execSort = function execSort() {\n    data.value = sortData(filteredData.value, {\n      sortingColumn: sortingColumn.value,\n      sortProp: sortProp.value,\n      sortOrder: sortOrder.value\n    });\n  };\n  var execQuery = function execQuery() {\n    var ignore = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : void 0;\n    if (!(ignore && ignore.filter)) {\n      execFilter();\n    }\n    execSort();\n  };\n  var clearFilter = function clearFilter(columnKeys) {\n    var tableHeaderRef = instance.refs.tableHeaderRef;\n    if (!tableHeaderRef) return;\n    var panels = Object.assign({}, tableHeaderRef.filterPanels);\n    var keys = Object.keys(panels);\n    if (!keys.length) return;\n    if (typeof columnKeys === \"string\") {\n      columnKeys = [columnKeys];\n    }\n    if (Array.isArray(columnKeys)) {\n      var columns_ = columnKeys.map(function (key) {\n        return getColumnByKey({\n          columns: columns.value\n        }, key);\n      });\n      keys.forEach(function (key) {\n        var column = columns_.find(function (col) {\n          return col.id === key;\n        });\n        if (column) {\n          column.filteredValue = [];\n        }\n      });\n      instance.store.commit(\"filterChange\", {\n        column: columns_,\n        values: [],\n        silent: true,\n        multi: true\n      });\n    } else {\n      keys.forEach(function (key) {\n        var column = columns.value.find(function (col) {\n          return col.id === key;\n        });\n        if (column) {\n          column.filteredValue = [];\n        }\n      });\n      filters.value = {};\n      instance.store.commit(\"filterChange\", {\n        column: {},\n        values: [],\n        silent: true\n      });\n    }\n  };\n  var clearSort = function clearSort() {\n    if (!sortingColumn.value) return;\n    updateSort(null, null, null);\n    instance.store.commit(\"changeSortCondition\", {\n      silent: true\n    });\n  };\n  var _useExpand = useExpand({\n      data,\n      rowKey\n    }),\n    setExpandRowKeys = _useExpand.setExpandRowKeys,\n    toggleRowExpansion = _useExpand.toggleRowExpansion,\n    updateExpandRows = _useExpand.updateExpandRows,\n    expandStates = _useExpand.states,\n    isRowExpanded = _useExpand.isRowExpanded;\n  var _useTree = useTree({\n      data,\n      rowKey\n    }),\n    updateTreeExpandKeys = _useTree.updateTreeExpandKeys,\n    toggleTreeExpansion = _useTree.toggleTreeExpansion,\n    updateTreeData = _useTree.updateTreeData,\n    loadOrToggle = _useTree.loadOrToggle,\n    treeStates = _useTree.states;\n  var _useCurrent = useCurrent({\n      data,\n      rowKey\n    }),\n    updateCurrentRowData = _useCurrent.updateCurrentRowData,\n    updateCurrentRow = _useCurrent.updateCurrentRow,\n    setCurrentRowKey = _useCurrent.setCurrentRowKey,\n    currentData = _useCurrent.states;\n  var setExpandRowKeysAdapter = function setExpandRowKeysAdapter(val) {\n    setExpandRowKeys(val);\n    updateTreeExpandKeys(val);\n  };\n  var toggleRowExpansionAdapter = function toggleRowExpansionAdapter(row, expanded) {\n    var hasExpandColumn = columns.value.some(function (_ref) {\n      var type = _ref.type;\n      return type === \"expand\";\n    });\n    if (hasExpandColumn) {\n      toggleRowExpansion(row, expanded);\n    } else {\n      toggleTreeExpansion(row, expanded);\n    }\n  };\n  return {\n    assertRowKey,\n    updateColumns,\n    scheduleLayout,\n    isSelected,\n    clearSelection,\n    cleanSelection,\n    getSelectionRows,\n    toggleRowSelection,\n    _toggleAllSelection,\n    toggleAllSelection: null,\n    updateSelectionByRowKey,\n    updateAllSelected,\n    updateFilters,\n    updateCurrentRow,\n    updateSort,\n    execFilter,\n    execSort,\n    execQuery,\n    clearFilter,\n    clearSort,\n    toggleRowExpansion,\n    setExpandRowKeysAdapter,\n    setCurrentRowKey,\n    toggleRowExpansionAdapter,\n    isRowExpanded,\n    updateExpandRows,\n    updateCurrentRowData,\n    loadOrToggle,\n    updateTreeData,\n    states: _objectSpread(_objectSpread(_objectSpread({\n      tableSize,\n      rowKey,\n      data,\n      _data,\n      isComplex,\n      _columns,\n      originColumns,\n      columns,\n      fixedColumns,\n      rightFixedColumns,\n      leafColumns,\n      fixedLeafColumns,\n      rightFixedLeafColumns,\n      updateOrderFns,\n      leafColumnsLength,\n      fixedLeafColumnsLength,\n      rightFixedLeafColumnsLength,\n      isAllSelected,\n      selection,\n      reserveSelection,\n      selectOnIndeterminate,\n      selectable,\n      filters,\n      filteredData,\n      sortingColumn,\n      sortProp,\n      sortOrder,\n      hoverRow\n    }, expandStates), treeStates), currentData)\n  };\n}\nexport { useWatcher as default };", "map": {"version": 3, "names": ["sortData", "data", "states", "sortingColumn", "sortable", "orderBy", "sortProp", "sortOrder", "sortMethod", "sortBy", "doFlattenColumns", "columns", "result", "for<PERSON>ach", "column", "children", "length", "push", "apply", "useWatcher", "_a", "instance", "getCurrentInstance", "_toRefs", "toRefs", "proxy", "$props", "tableSize", "size", "<PERSON><PERSON><PERSON>", "ref", "_data", "isComplex", "_columns", "originColumns", "fixedColumns", "rightFixedColumns", "leafColumns", "fixedLeafColumns", "rightFixedLeafColumns", "updateOrderFns", "leafColumns<PERSON>ength", "fixedLeafColumnsLength", "rightFixedLeafColumnsLength", "isAllSelected", "selection", "reserveSelection", "selectOnIndeterminate", "selectable", "filters", "filteredData", "hoverRow", "watch", "state", "scheduleLayout", "deep", "assertRowKey", "value", "Error", "updateChildFixed", "_a2", "childColumn", "fixed", "updateColumns", "filter", "type", "unshift", "notFixedColumns", "concat", "leafColumns2", "fixedLeafColumns2", "rightFixedLeafColumns2", "needUpdateColumns", "immediate", "arguments", "undefined", "doLayout", "debouncedUpdateLayout", "isSelected", "row", "includes", "clearSelection", "oldSelection", "emit", "cleanSelection", "deleted", "selectedMap", "getKeysMap", "dataMap", "key", "hasOwn", "item", "newSelection", "slice", "getSelectionRows", "toggleRowSelection", "selected", "emitChange", "changed", "toggleRowStatus", "_toggleAllSelection", "_b", "selectionChanged", "childrenCount", "rowKey2", "store", "index", "rowIndex", "call", "get<PERSON><PERSON><PERSON>n<PERSON>ount", "getRowIdentity", "updateSelectionByRowKey", "rowId", "rowInfo", "updateAllSelected", "_c", "isSelected2", "isAllSelected_", "selectedCount", "i", "j", "keyProp", "isRowSelectable", "treeData", "count", "<PERSON><PERSON><PERSON>", "updateFilters", "columns2", "values", "Array", "isArray", "filters_", "col", "id", "column<PERSON>ey", "updateSort", "prop", "order", "execFilter", "sourceData", "unref", "Object", "keys", "columnId", "getColumnById", "filterMethod", "some", "execSort", "execQ<PERSON>y", "ignore", "clearFilter", "columnKeys", "tableHeaderRef", "refs", "panels", "assign", "filterPanels", "columns_", "map", "getColumnByKey", "find", "filteredValue", "commit", "silent", "multi", "clearSort", "_useExpand", "useExpand", "setExpandRowKeys", "toggleRowExpansion", "updateExpandRows", "expandStates", "isRowExpanded", "_useTree", "useTree", "updateTreeExpandKeys", "toggleTreeExpansion", "updateTreeData", "loadOrToggle", "treeStates", "_useCurrent", "useCurrent", "updateCurrentRowData", "updateCurrentRow", "setCurrentRowKey", "currentData", "setExpandRowKeysAdapter", "val", "toggleRowExpansionAdapter", "expanded", "hasExpandColumn", "_ref", "toggleAllSelection", "_objectSpread"], "sources": ["../../../../../../../packages/components/table/src/store/watcher.ts"], "sourcesContent": ["// @ts-nocheck\nimport { getCurrentInstance, ref, toRefs, unref, watch } from 'vue'\nimport { hasOwn } from '@element-plus/utils'\nimport {\n  getColumnById,\n  getColumnByKey,\n  getKeysMap,\n  getRowIdentity,\n  orderBy,\n  toggleRowStatus,\n} from '../util'\nimport useExpand from './expand'\nimport useCurrent from './current'\nimport useTree from './tree'\n\nimport type { Ref } from 'vue'\nimport type { TableColumnCtx } from '../table-column/defaults'\nimport type { Table, TableRefs } from '../table/defaults'\nimport type { StoreFilter } from '.'\n\nconst sortData = (data, states) => {\n  const sortingColumn = states.sortingColumn\n  if (!sortingColumn || typeof sortingColumn.sortable === 'string') {\n    return data\n  }\n  return orderBy(\n    data,\n    states.sortProp,\n    states.sortOrder,\n    sortingColumn.sortMethod,\n    sortingColumn.sortBy\n  )\n}\n\nconst doFlattenColumns = (columns) => {\n  const result = []\n  columns.forEach((column) => {\n    if (column.children && column.children.length > 0) {\n      // eslint-disable-next-line prefer-spread\n      result.push.apply(result, doFlattenColumns(column.children))\n    } else {\n      result.push(column)\n    }\n  })\n  return result\n}\n\nfunction useWatcher<T>() {\n  const instance = getCurrentInstance() as Table<T>\n  const { size: tableSize } = toRefs(instance.proxy?.$props as any)\n  const rowKey: Ref<string> = ref(null)\n  const data: Ref<T[]> = ref([])\n  const _data: Ref<T[]> = ref([])\n  const isComplex = ref(false)\n  const _columns: Ref<TableColumnCtx<T>[]> = ref([])\n  const originColumns: Ref<TableColumnCtx<T>[]> = ref([])\n  const columns: Ref<TableColumnCtx<T>[]> = ref([])\n  const fixedColumns: Ref<TableColumnCtx<T>[]> = ref([])\n  const rightFixedColumns: Ref<TableColumnCtx<T>[]> = ref([])\n  const leafColumns: Ref<TableColumnCtx<T>[]> = ref([])\n  const fixedLeafColumns: Ref<TableColumnCtx<T>[]> = ref([])\n  const rightFixedLeafColumns: Ref<TableColumnCtx<T>[]> = ref([])\n  const updateOrderFns: (() => void)[] = []\n  const leafColumnsLength = ref(0)\n  const fixedLeafColumnsLength = ref(0)\n  const rightFixedLeafColumnsLength = ref(0)\n  const isAllSelected = ref(false)\n  const selection: Ref<T[]> = ref([])\n  const reserveSelection = ref(false)\n  const selectOnIndeterminate = ref(false)\n  const selectable: Ref<(row: T, index: number) => boolean> = ref(null)\n  const filters: Ref<StoreFilter> = ref({})\n  const filteredData = ref(null)\n  const sortingColumn = ref(null)\n  const sortProp = ref(null)\n  const sortOrder = ref(null)\n  const hoverRow = ref(null)\n\n  watch(data, () => instance.state && scheduleLayout(false), {\n    deep: true,\n  })\n\n  // 检查 rowKey 是否存在\n  const assertRowKey = () => {\n    if (!rowKey.value) throw new Error('[ElTable] prop row-key is required')\n  }\n\n  // 更新 fixed\n  const updateChildFixed = (column: TableColumnCtx<T>) => {\n    column.children?.forEach((childColumn) => {\n      childColumn.fixed = column.fixed\n      updateChildFixed(childColumn)\n    })\n  }\n\n  // 更新列\n  const updateColumns = () => {\n    _columns.value.forEach((column) => {\n      updateChildFixed(column)\n    })\n    fixedColumns.value = _columns.value.filter(\n      (column) => column.fixed === true || column.fixed === 'left'\n    )\n    rightFixedColumns.value = _columns.value.filter(\n      (column) => column.fixed === 'right'\n    )\n    if (\n      fixedColumns.value.length > 0 &&\n      _columns.value[0] &&\n      _columns.value[0].type === 'selection' &&\n      !_columns.value[0].fixed\n    ) {\n      _columns.value[0].fixed = true\n      fixedColumns.value.unshift(_columns.value[0])\n    }\n\n    const notFixedColumns = _columns.value.filter((column) => !column.fixed)\n    originColumns.value = []\n      .concat(fixedColumns.value)\n      .concat(notFixedColumns)\n      .concat(rightFixedColumns.value)\n    const leafColumns = doFlattenColumns(notFixedColumns)\n    const fixedLeafColumns = doFlattenColumns(fixedColumns.value)\n    const rightFixedLeafColumns = doFlattenColumns(rightFixedColumns.value)\n\n    leafColumnsLength.value = leafColumns.length\n    fixedLeafColumnsLength.value = fixedLeafColumns.length\n    rightFixedLeafColumnsLength.value = rightFixedLeafColumns.length\n\n    columns.value = []\n      .concat(fixedLeafColumns)\n      .concat(leafColumns)\n      .concat(rightFixedLeafColumns)\n    isComplex.value =\n      fixedColumns.value.length > 0 || rightFixedColumns.value.length > 0\n  }\n\n  // 更新 DOM\n  const scheduleLayout = (needUpdateColumns?: boolean, immediate = false) => {\n    if (needUpdateColumns) {\n      updateColumns()\n    }\n    if (immediate) {\n      instance.state.doLayout()\n    } else {\n      instance.state.debouncedUpdateLayout()\n    }\n  }\n\n  // 选择\n  const isSelected = (row) => {\n    return selection.value.includes(row)\n  }\n\n  const clearSelection = () => {\n    isAllSelected.value = false\n    const oldSelection = selection.value\n    selection.value = []\n    if (oldSelection.length) {\n      instance.emit('selection-change', [])\n    }\n  }\n\n  const cleanSelection = () => {\n    let deleted\n    if (rowKey.value) {\n      deleted = []\n      const selectedMap = getKeysMap(selection.value, rowKey.value)\n      const dataMap = getKeysMap(data.value, rowKey.value)\n      for (const key in selectedMap) {\n        if (hasOwn(selectedMap, key) && !dataMap[key]) {\n          deleted.push(selectedMap[key].row)\n        }\n      }\n    } else {\n      deleted = selection.value.filter((item) => !data.value.includes(item))\n    }\n    if (deleted.length) {\n      const newSelection = selection.value.filter(\n        (item) => !deleted.includes(item)\n      )\n      selection.value = newSelection\n      instance.emit('selection-change', newSelection.slice())\n    }\n  }\n\n  const getSelectionRows = () => {\n    return (selection.value || []).slice()\n  }\n\n  const toggleRowSelection = (\n    row: T,\n    selected = undefined,\n    emitChange = true\n  ) => {\n    const changed = toggleRowStatus(selection.value, row, selected)\n    if (changed) {\n      const newSelection = (selection.value || []).slice()\n      // 调用 API 修改选中值，不触发 select 事件\n      if (emitChange) {\n        instance.emit('select', newSelection, row)\n      }\n      instance.emit('selection-change', newSelection)\n    }\n  }\n\n  const _toggleAllSelection = () => {\n    // when only some rows are selected (but not all), select or deselect all of them\n    // depending on the value of selectOnIndeterminate\n    const value = selectOnIndeterminate.value\n      ? !isAllSelected.value\n      : !(isAllSelected.value || selection.value.length)\n    isAllSelected.value = value\n\n    let selectionChanged = false\n    let childrenCount = 0\n    const rowKey = instance?.store?.states?.rowKey.value\n    data.value.forEach((row, index) => {\n      const rowIndex = index + childrenCount\n      if (selectable.value) {\n        if (\n          selectable.value.call(null, row, rowIndex) &&\n          toggleRowStatus(selection.value, row, value)\n        ) {\n          selectionChanged = true\n        }\n      } else {\n        if (toggleRowStatus(selection.value, row, value)) {\n          selectionChanged = true\n        }\n      }\n      childrenCount += getChildrenCount(getRowIdentity(row, rowKey))\n    })\n\n    if (selectionChanged) {\n      instance.emit(\n        'selection-change',\n        selection.value ? selection.value.slice() : []\n      )\n    }\n    instance.emit('select-all', (selection.value || []).slice())\n  }\n\n  const updateSelectionByRowKey = () => {\n    const selectedMap = getKeysMap(selection.value, rowKey.value)\n    data.value.forEach((row) => {\n      const rowId = getRowIdentity(row, rowKey.value)\n      const rowInfo = selectedMap[rowId]\n      if (rowInfo) {\n        selection.value[rowInfo.index] = row\n      }\n    })\n  }\n\n  const updateAllSelected = () => {\n    // data 为 null 时，解构时的默认值会被忽略\n    if (data.value?.length === 0) {\n      isAllSelected.value = false\n      return\n    }\n\n    let selectedMap\n    if (rowKey.value) {\n      selectedMap = getKeysMap(selection.value, rowKey.value)\n    }\n    const isSelected = function (row) {\n      if (selectedMap) {\n        return !!selectedMap[getRowIdentity(row, rowKey.value)]\n      } else {\n        return selection.value.includes(row)\n      }\n    }\n    let isAllSelected_ = true\n    let selectedCount = 0\n    let childrenCount = 0\n    for (let i = 0, j = (data.value || []).length; i < j; i++) {\n      const keyProp = instance?.store?.states?.rowKey.value\n      const rowIndex = i + childrenCount\n      const item = data.value[i]\n      const isRowSelectable =\n        selectable.value && selectable.value.call(null, item, rowIndex)\n      if (!isSelected(item)) {\n        if (!selectable.value || isRowSelectable) {\n          isAllSelected_ = false\n          break\n        }\n      } else {\n        selectedCount++\n      }\n      childrenCount += getChildrenCount(getRowIdentity(item, keyProp))\n    }\n\n    if (selectedCount === 0) isAllSelected_ = false\n    isAllSelected.value = isAllSelected_\n  }\n\n  // gets the number of all child nodes by rowKey\n  const getChildrenCount = (rowKey: string) => {\n    if (!instance || !instance.store) return 0\n    const { treeData } = instance.store.states\n    let count = 0\n    const children = treeData.value[rowKey]?.children\n    if (children) {\n      count += children.length\n      children.forEach((childKey) => {\n        count += getChildrenCount(childKey)\n      })\n    }\n    return count\n  }\n\n  // 过滤与排序\n  const updateFilters = (columns, values) => {\n    if (!Array.isArray(columns)) {\n      columns = [columns]\n    }\n    const filters_ = {}\n    columns.forEach((col) => {\n      filters.value[col.id] = values\n      filters_[col.columnKey || col.id] = values\n    })\n    return filters_\n  }\n\n  const updateSort = (column, prop, order) => {\n    if (sortingColumn.value && sortingColumn.value !== column) {\n      sortingColumn.value.order = null\n    }\n    sortingColumn.value = column\n    sortProp.value = prop\n    sortOrder.value = order\n  }\n\n  const execFilter = () => {\n    let sourceData = unref(_data)\n    Object.keys(filters.value).forEach((columnId) => {\n      const values = filters.value[columnId]\n      if (!values || values.length === 0) return\n      const column = getColumnById(\n        {\n          columns: columns.value,\n        },\n        columnId\n      )\n      if (column && column.filterMethod) {\n        sourceData = sourceData.filter((row) => {\n          return values.some((value) =>\n            column.filterMethod.call(null, value, row, column)\n          )\n        })\n      }\n    })\n\n    filteredData.value = sourceData\n  }\n\n  const execSort = () => {\n    data.value = sortData(filteredData.value, {\n      sortingColumn: sortingColumn.value,\n      sortProp: sortProp.value,\n      sortOrder: sortOrder.value,\n    })\n  }\n\n  // 根据 filters 与 sort 去过滤 data\n  const execQuery = (ignore = undefined) => {\n    if (!(ignore && ignore.filter)) {\n      execFilter()\n    }\n    execSort()\n  }\n\n  const clearFilter = (columnKeys) => {\n    const { tableHeaderRef } = instance.refs as TableRefs\n    if (!tableHeaderRef) return\n    const panels = Object.assign({}, tableHeaderRef.filterPanels)\n\n    const keys = Object.keys(panels)\n    if (!keys.length) return\n\n    if (typeof columnKeys === 'string') {\n      columnKeys = [columnKeys]\n    }\n\n    if (Array.isArray(columnKeys)) {\n      const columns_ = columnKeys.map((key) =>\n        getColumnByKey(\n          {\n            columns: columns.value,\n          },\n          key\n        )\n      )\n      keys.forEach((key) => {\n        const column = columns_.find((col) => col.id === key)\n        if (column) {\n          column.filteredValue = []\n        }\n      })\n      instance.store.commit('filterChange', {\n        column: columns_,\n        values: [],\n        silent: true,\n        multi: true,\n      })\n    } else {\n      keys.forEach((key) => {\n        const column = columns.value.find((col) => col.id === key)\n        if (column) {\n          column.filteredValue = []\n        }\n      })\n\n      filters.value = {}\n      instance.store.commit('filterChange', {\n        column: {},\n        values: [],\n        silent: true,\n      })\n    }\n  }\n\n  const clearSort = () => {\n    if (!sortingColumn.value) return\n\n    updateSort(null, null, null)\n    instance.store.commit('changeSortCondition', {\n      silent: true,\n    })\n  }\n  const {\n    setExpandRowKeys,\n    toggleRowExpansion,\n    updateExpandRows,\n    states: expandStates,\n    isRowExpanded,\n  } = useExpand({\n    data,\n    rowKey,\n  })\n  const {\n    updateTreeExpandKeys,\n    toggleTreeExpansion,\n    updateTreeData,\n    loadOrToggle,\n    states: treeStates,\n  } = useTree({\n    data,\n    rowKey,\n  })\n  const {\n    updateCurrentRowData,\n    updateCurrentRow,\n    setCurrentRowKey,\n    states: currentData,\n  } = useCurrent({\n    data,\n    rowKey,\n  })\n  // 适配层，expand-row-keys 在 Expand 与 TreeTable 中都有使用\n  const setExpandRowKeysAdapter = (val: string[]) => {\n    // 这里会触发额外的计算，但为了兼容性，暂时这么做\n    setExpandRowKeys(val)\n    updateTreeExpandKeys(val)\n  }\n\n  // 展开行与 TreeTable 都要使用\n  const toggleRowExpansionAdapter = (row: T, expanded?: boolean) => {\n    const hasExpandColumn = columns.value.some(({ type }) => type === 'expand')\n    if (hasExpandColumn) {\n      toggleRowExpansion(row, expanded)\n    } else {\n      toggleTreeExpansion(row, expanded)\n    }\n  }\n\n  return {\n    assertRowKey,\n    updateColumns,\n    scheduleLayout,\n    isSelected,\n    clearSelection,\n    cleanSelection,\n    getSelectionRows,\n    toggleRowSelection,\n    _toggleAllSelection,\n    toggleAllSelection: null,\n    updateSelectionByRowKey,\n    updateAllSelected,\n    updateFilters,\n    updateCurrentRow,\n    updateSort,\n    execFilter,\n    execSort,\n    execQuery,\n    clearFilter,\n    clearSort,\n    toggleRowExpansion,\n    setExpandRowKeysAdapter,\n    setCurrentRowKey,\n    toggleRowExpansionAdapter,\n    isRowExpanded,\n    updateExpandRows,\n    updateCurrentRowData,\n    loadOrToggle,\n    updateTreeData,\n    states: {\n      tableSize,\n      rowKey,\n      data,\n      _data,\n      isComplex,\n      _columns,\n      originColumns,\n      columns,\n      fixedColumns,\n      rightFixedColumns,\n      leafColumns,\n      fixedLeafColumns,\n      rightFixedLeafColumns,\n      updateOrderFns,\n      leafColumnsLength,\n      fixedLeafColumnsLength,\n      rightFixedLeafColumnsLength,\n      isAllSelected,\n      selection,\n      reserveSelection,\n      selectOnIndeterminate,\n      selectable,\n      filters,\n      filteredData,\n      sortingColumn,\n      sortProp,\n      sortOrder,\n      hoverRow,\n      ...expandStates,\n      ...treeStates,\n      ...currentData,\n    },\n  }\n}\n\nexport default useWatcher\n"], "mappings": ";;;;;;;;;;;;AAaA,IAAMA,QAAQ,GAAG,SAAXA,QAAQA,CAAIC,IAAI,EAAEC,MAAM,EAAK;EACjC,IAAMC,aAAa,GAAGD,MAAM,CAACC,aAAa;EAC1C,IAAI,CAACA,aAAa,IAAI,OAAOA,aAAa,CAACC,QAAQ,KAAK,QAAQ,EAAE;IAChE,OAAOH,IAAI;EACf;EACE,OAAOI,OAAO,CAACJ,IAAI,EAAEC,MAAM,CAACI,QAAQ,EAAEJ,MAAM,CAACK,SAAS,EAAEJ,aAAa,CAACK,UAAU,EAAEL,aAAa,CAACM,MAAM,CAAC;AACzG,CAAC;AACD,IAAMC,iBAAgB,GAAG,SAAnBA,gBAAgBA,CAAIC,OAAO,EAAK;EACpC,IAAMC,MAAM,GAAG,EAAE;EACjBD,OAAO,CAACE,OAAO,CAAC,UAACC,MAAM,EAAK;IAC1B,IAAIA,MAAM,CAACC,QAAQ,IAAID,MAAM,CAACC,QAAQ,CAACC,MAAM,GAAG,CAAC,EAAE;MACjDJ,MAAM,CAACK,IAAI,CAACC,KAAK,CAACN,MAAM,EAAEF,iBAAgB,CAACI,MAAM,CAACC,QAAQ,CAAC,CAAC;IAClE,CAAK,MAAM;MACLH,MAAM,CAACK,IAAI,CAACH,MAAM,CAAC;IACzB;EACA,CAAG,CAAC;EACF,OAAOF,MAAM;AACf,CAAC;AACD,SAASO,UAAUA,CAAA,EAAG;EACpB,IAAIC,EAAE;EACN,IAAMC,QAAQ,GAAGC,kBAAkB,EAAE;EACrC,IAAAC,OAAA,GAA4BC,MAAM,CAAC,CAACJ,EAAE,GAAGC,QAAQ,CAACI,KAAK,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGL,EAAE,CAACM,MAAM,CAAC;IAAxEC,SAAS,GAAAJ,OAAA,CAAfK,IAAI;EACZ,IAAMC,MAAM,GAAGC,GAAG,CAAC,IAAI,CAAC;EACxB,IAAM7B,IAAI,GAAG6B,GAAG,CAAC,EAAE,CAAC;EACpB,IAAMC,KAAK,GAAGD,GAAG,CAAC,EAAE,CAAC;EACrB,IAAME,SAAS,GAAGF,GAAG,CAAC,KAAK,CAAC;EAC5B,IAAMG,QAAQ,GAAGH,GAAG,CAAC,EAAE,CAAC;EACxB,IAAMI,aAAa,GAAGJ,GAAG,CAAC,EAAE,CAAC;EAC7B,IAAMnB,OAAO,GAAGmB,GAAG,CAAC,EAAE,CAAC;EACvB,IAAMK,YAAY,GAAGL,GAAG,CAAC,EAAE,CAAC;EAC5B,IAAMM,iBAAiB,GAAGN,GAAG,CAAC,EAAE,CAAC;EACjC,IAAMO,WAAW,GAAGP,GAAG,CAAC,EAAE,CAAC;EAC3B,IAAMQ,gBAAgB,GAAGR,GAAG,CAAC,EAAE,CAAC;EAChC,IAAMS,qBAAqB,GAAGT,GAAG,CAAC,EAAE,CAAC;EACrC,IAAMU,cAAc,GAAG,EAAE;EACzB,IAAMC,iBAAiB,GAAGX,GAAG,CAAC,CAAC,CAAC;EAChC,IAAMY,sBAAsB,GAAGZ,GAAG,CAAC,CAAC,CAAC;EACrC,IAAMa,2BAA2B,GAAGb,GAAG,CAAC,CAAC,CAAC;EAC1C,IAAMc,aAAa,GAAGd,GAAG,CAAC,KAAK,CAAC;EAChC,IAAMe,SAAS,GAAGf,GAAG,CAAC,EAAE,CAAC;EACzB,IAAMgB,gBAAgB,GAAGhB,GAAG,CAAC,KAAK,CAAC;EACnC,IAAMiB,qBAAqB,GAAGjB,GAAG,CAAC,KAAK,CAAC;EACxC,IAAMkB,UAAU,GAAGlB,GAAG,CAAC,IAAI,CAAC;EAC5B,IAAMmB,OAAO,GAAGnB,GAAG,CAAC,EAAE,CAAC;EACvB,IAAMoB,YAAY,GAAGpB,GAAG,CAAC,IAAI,CAAC;EAC9B,IAAM3B,aAAa,GAAG2B,GAAG,CAAC,IAAI,CAAC;EAC/B,IAAMxB,QAAQ,GAAGwB,GAAG,CAAC,IAAI,CAAC;EAC1B,IAAMvB,SAAS,GAAGuB,GAAG,CAAC,IAAI,CAAC;EAC3B,IAAMqB,QAAQ,GAAGrB,GAAG,CAAC,IAAI,CAAC;EAC1BsB,KAAK,CAACnD,IAAI,EAAE;IAAA,OAAMoB,QAAQ,CAACgC,KAAK,IAAIC,cAAc,CAAC,KAAK,CAAC;EAAA,GAAE;IACzDC,IAAI,EAAE;EACV,CAAG,CAAC;EACF,IAAMC,YAAY,GAAG,SAAfA,YAAYA,CAAA,EAAS;IACzB,IAAI,CAAC3B,MAAM,CAAC4B,KAAK,EACf,MAAM,IAAIC,KAAK,CAAC,oCAAoC,CAAC;EAC3D,CAAG;EACD,IAAMC,iBAAgB,GAAG,SAAnBA,gBAAgBA,CAAI7C,MAAM,EAAK;IACnC,IAAI8C,GAAG;IACP,CAACA,GAAG,GAAG9C,MAAM,CAACC,QAAQ,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG6C,GAAG,CAAC/C,OAAO,CAAC,UAACgD,WAAW,EAAK;MACtEA,WAAW,CAACC,KAAK,GAAGhD,MAAM,CAACgD,KAAK;MAChCH,iBAAgB,CAACE,WAAW,CAAC;IACnC,CAAK,CAAC;EACN,CAAG;EACD,IAAME,aAAa,GAAG,SAAhBA,aAAaA,CAAA,EAAS;IAC1B9B,QAAQ,CAACwB,KAAK,CAAC5C,OAAO,CAAC,UAACC,MAAM,EAAK;MACjC6C,iBAAgB,CAAC7C,MAAM,CAAC;IAC9B,CAAK,CAAC;IACFqB,YAAY,CAACsB,KAAK,GAAGxB,QAAQ,CAACwB,KAAK,CAACO,MAAM,CAAC,UAAClD,MAAM;MAAA,OAAKA,MAAM,CAACgD,KAAK,KAAK,IAAI,IAAIhD,MAAM,CAACgD,KAAK,KAAK,MAAM;IAAA,EAAC;IACxG1B,iBAAiB,CAACqB,KAAK,GAAGxB,QAAQ,CAACwB,KAAK,CAACO,MAAM,CAAC,UAAClD,MAAM;MAAA,OAAKA,MAAM,CAACgD,KAAK,KAAK,OAAO;IAAA,EAAC;IACrF,IAAI3B,YAAY,CAACsB,KAAK,CAACzC,MAAM,GAAG,CAAC,IAAIiB,QAAQ,CAACwB,KAAK,CAAC,CAAC,CAAC,IAAIxB,QAAQ,CAACwB,KAAK,CAAC,CAAC,CAAC,CAACQ,IAAI,KAAK,WAAW,IAAI,CAAChC,QAAQ,CAACwB,KAAK,CAAC,CAAC,CAAC,CAACK,KAAK,EAAE;MAC5H7B,QAAQ,CAACwB,KAAK,CAAC,CAAC,CAAC,CAACK,KAAK,GAAG,IAAI;MAC9B3B,YAAY,CAACsB,KAAK,CAACS,OAAO,CAACjC,QAAQ,CAACwB,KAAK,CAAC,CAAC,CAAC,CAAC;IACnD;IACI,IAAMU,eAAe,GAAGlC,QAAQ,CAACwB,KAAK,CAACO,MAAM,CAAC,UAAClD,MAAM;MAAA,OAAK,CAACA,MAAM,CAACgD,KAAK;IAAA,EAAC;IACxE5B,aAAa,CAACuB,KAAK,GAAG,EAAE,CAACW,MAAM,CAACjC,YAAY,CAACsB,KAAK,CAAC,CAACW,MAAM,CAACD,eAAe,CAAC,CAACC,MAAM,CAAChC,iBAAiB,CAACqB,KAAK,CAAC;IAC3G,IAAMY,YAAY,GAAG3D,iBAAgB,CAACyD,eAAe,CAAC;IACtD,IAAMG,iBAAiB,GAAG5D,iBAAgB,CAACyB,YAAY,CAACsB,KAAK,CAAC;IAC9D,IAAMc,sBAAsB,GAAG7D,iBAAgB,CAAC0B,iBAAiB,CAACqB,KAAK,CAAC;IACxEhB,iBAAiB,CAACgB,KAAK,GAAGY,YAAY,CAACrD,MAAM;IAC7C0B,sBAAsB,CAACe,KAAK,GAAGa,iBAAiB,CAACtD,MAAM;IACvD2B,2BAA2B,CAACc,KAAK,GAAGc,sBAAsB,CAACvD,MAAM;IACjEL,OAAO,CAAC8C,KAAK,GAAG,EAAE,CAACW,MAAM,CAACE,iBAAiB,CAAC,CAACF,MAAM,CAACC,YAAY,CAAC,CAACD,MAAM,CAACG,sBAAsB,CAAC;IAChGvC,SAAS,CAACyB,KAAK,GAAGtB,YAAY,CAACsB,KAAK,CAACzC,MAAM,GAAG,CAAC,IAAIoB,iBAAiB,CAACqB,KAAK,CAACzC,MAAM,GAAG,CAAC;EACzF,CAAG;EACD,IAAMsC,cAAc,GAAG,SAAjBA,cAAcA,CAAIkB,iBAAiB,EAAwB;IAAA,IAAtBC,SAAS,GAAAC,SAAA,CAAA1D,MAAA,QAAA0D,SAAA,QAAAC,SAAA,GAAAD,SAAA,MAAG,KAAK;IAC1D,IAAIF,iBAAiB,EAAE;MACrBT,aAAa,EAAE;IACrB;IACI,IAAIU,SAAS,EAAE;MACbpD,QAAQ,CAACgC,KAAK,CAACuB,QAAQ,EAAE;IAC/B,CAAK,MAAM;MACLvD,QAAQ,CAACgC,KAAK,CAACwB,qBAAqB,EAAE;IAC5C;EACA,CAAG;EACD,IAAMC,UAAU,GAAG,SAAbA,UAAUA,CAAIC,GAAG,EAAK;IAC1B,OAAOlC,SAAS,CAACY,KAAK,CAACuB,QAAQ,CAACD,GAAG,CAAC;EACxC,CAAG;EACD,IAAME,cAAc,GAAG,SAAjBA,cAAcA,CAAA,EAAS;IAC3BrC,aAAa,CAACa,KAAK,GAAG,KAAK;IAC3B,IAAMyB,YAAY,GAAGrC,SAAS,CAACY,KAAK;IACpCZ,SAAS,CAACY,KAAK,GAAG,EAAE;IACpB,IAAIyB,YAAY,CAAClE,MAAM,EAAE;MACvBK,QAAQ,CAAC8D,IAAI,CAAC,kBAAkB,EAAE,EAAE,CAAC;IAC3C;EACA,CAAG;EACD,IAAMC,cAAc,GAAG,SAAjBA,cAAcA,CAAA,EAAS;IAC3B,IAAIC,OAAO;IACX,IAAIxD,MAAM,CAAC4B,KAAK,EAAE;MAChB4B,OAAO,GAAG,EAAE;MACZ,IAAMC,WAAW,GAAGC,UAAU,CAAC1C,SAAS,CAACY,KAAK,EAAE5B,MAAM,CAAC4B,KAAK,CAAC;MAC7D,IAAM+B,OAAO,GAAGD,UAAU,CAACtF,IAAI,CAACwD,KAAK,EAAE5B,MAAM,CAAC4B,KAAK,CAAC;MACpD,KAAK,IAAMgC,GAAG,IAAIH,WAAW,EAAE;QAC7B,IAAII,MAAM,CAACJ,WAAW,EAAEG,GAAG,CAAC,IAAI,CAACD,OAAO,CAACC,GAAG,CAAC,EAAE;UAC7CJ,OAAO,CAACpE,IAAI,CAACqE,WAAW,CAACG,GAAG,CAAC,CAACV,GAAG,CAAC;QAC5C;MACA;IACA,CAAK,MAAM;MACLM,OAAO,GAAGxC,SAAS,CAACY,KAAK,CAACO,MAAM,CAAC,UAAC2B,IAAI;QAAA,OAAK,CAAC1F,IAAI,CAACwD,KAAK,CAACuB,QAAQ,CAACW,IAAI,CAAC;MAAA,EAAC;IAC5E;IACI,IAAIN,OAAO,CAACrE,MAAM,EAAE;MAClB,IAAM4E,YAAY,GAAG/C,SAAS,CAACY,KAAK,CAACO,MAAM,CAAC,UAAC2B,IAAI;QAAA,OAAK,CAACN,OAAO,CAACL,QAAQ,CAACW,IAAI,CAAC;MAAA,EAAC;MAC9E9C,SAAS,CAACY,KAAK,GAAGmC,YAAY;MAC9BvE,QAAQ,CAAC8D,IAAI,CAAC,kBAAkB,EAAES,YAAY,CAACC,KAAK,EAAE,CAAC;IAC7D;EACA,CAAG;EACD,IAAMC,gBAAgB,GAAG,SAAnBA,gBAAgBA,CAAA,EAAS;IAC7B,OAAO,CAACjD,SAAS,CAACY,KAAK,IAAI,EAAE,EAAEoC,KAAK,EAAE;EAC1C,CAAG;EACD,IAAME,kBAAkB,GAAG,SAArBA,kBAAkBA,CAAIhB,GAAG,EAA2C;IAAA,IAAzCiB,QAAQ,GAAAtB,SAAA,CAAA1D,MAAA,QAAA0D,SAAA,QAAAC,SAAA,GAAAD,SAAA,MAAG,KAAK,CAAC;IAAA,IAAEuB,UAAU,GAAAvB,SAAA,CAAA1D,MAAA,QAAA0D,SAAA,QAAAC,SAAA,GAAAD,SAAA,MAAG,IAAI;IACnE,IAAMwB,OAAO,GAAGC,eAAe,CAACtD,SAAS,CAACY,KAAK,EAAEsB,GAAG,EAAEiB,QAAQ,CAAC;IAC/D,IAAIE,OAAO,EAAE;MACX,IAAMN,YAAY,GAAG,CAAC/C,SAAS,CAACY,KAAK,IAAI,EAAE,EAAEoC,KAAK,EAAE;MACpD,IAAII,UAAU,EAAE;QACd5E,QAAQ,CAAC8D,IAAI,CAAC,QAAQ,EAAES,YAAY,EAAEb,GAAG,CAAC;MAClD;MACM1D,QAAQ,CAAC8D,IAAI,CAAC,kBAAkB,EAAES,YAAY,CAAC;IACrD;EACA,CAAG;EACD,IAAMQ,mBAAmB,GAAG,SAAtBA,mBAAmBA,CAAA,EAAS;IAChC,IAAIxC,GAAG,EAAEyC,EAAE;IACX,IAAM5C,KAAK,GAAGV,qBAAqB,CAACU,KAAK,GAAG,CAACb,aAAa,CAACa,KAAK,GAAG,EAAEb,aAAa,CAACa,KAAK,IAAIZ,SAAS,CAACY,KAAK,CAACzC,MAAM,CAAC;IACnH4B,aAAa,CAACa,KAAK,GAAGA,KAAK;IAC3B,IAAI6C,gBAAgB,GAAG,KAAK;IAC5B,IAAIC,aAAa,GAAG,CAAC;IACrB,IAAMC,OAAO,GAAG,CAACH,EAAE,GAAG,CAACzC,GAAG,GAAGvC,QAAQ,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,QAAQ,CAACoF,KAAK,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG7C,GAAG,CAAC1D,MAAM,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGmG,EAAE,CAACxE,MAAM,CAAC4B,KAAK;IAC1IxD,IAAI,CAACwD,KAAK,CAAC5C,OAAO,CAAC,UAACkE,GAAG,EAAE2B,KAAK,EAAK;MACjC,IAAMC,QAAQ,GAAGD,KAAK,GAAGH,aAAa;MACtC,IAAIvD,UAAU,CAACS,KAAK,EAAE;QACpB,IAAIT,UAAU,CAACS,KAAK,CAACmD,IAAI,CAAC,IAAI,EAAE7B,GAAG,EAAE4B,QAAQ,CAAC,IAAIR,eAAe,CAACtD,SAAS,CAACY,KAAK,EAAEsB,GAAG,EAAEtB,KAAK,CAAC,EAAE;UAC9F6C,gBAAgB,GAAG,IAAI;QACjC;MACA,CAAO,MAAM;QACL,IAAIH,eAAe,CAACtD,SAAS,CAACY,KAAK,EAAEsB,GAAG,EAAEtB,KAAK,CAAC,EAAE;UAChD6C,gBAAgB,GAAG,IAAI;QACjC;MACA;MACMC,aAAa,IAAIM,iBAAgB,CAACC,cAAc,CAAC/B,GAAG,EAAEyB,OAAO,CAAC,CAAC;IACrE,CAAK,CAAC;IACF,IAAIF,gBAAgB,EAAE;MACpBjF,QAAQ,CAAC8D,IAAI,CAAC,kBAAkB,EAAEtC,SAAS,CAACY,KAAK,GAAGZ,SAAS,CAACY,KAAK,CAACoC,KAAK,EAAE,GAAG,EAAE,CAAC;IACvF;IACIxE,QAAQ,CAAC8D,IAAI,CAAC,YAAY,EAAE,CAACtC,SAAS,CAACY,KAAK,IAAI,EAAE,EAAEoC,KAAK,EAAE,CAAC;EAChE,CAAG;EACD,IAAMkB,uBAAuB,GAAG,SAA1BA,uBAAuBA,CAAA,EAAS;IACpC,IAAMzB,WAAW,GAAGC,UAAU,CAAC1C,SAAS,CAACY,KAAK,EAAE5B,MAAM,CAAC4B,KAAK,CAAC;IAC7DxD,IAAI,CAACwD,KAAK,CAAC5C,OAAO,CAAC,UAACkE,GAAG,EAAK;MAC1B,IAAMiC,KAAK,GAAGF,cAAc,CAAC/B,GAAG,EAAElD,MAAM,CAAC4B,KAAK,CAAC;MAC/C,IAAMwD,OAAO,GAAG3B,WAAW,CAAC0B,KAAK,CAAC;MAClC,IAAIC,OAAO,EAAE;QACXpE,SAAS,CAACY,KAAK,CAACwD,OAAO,CAACP,KAAK,CAAC,GAAG3B,GAAG;MAC5C;IACA,CAAK,CAAC;EACN,CAAG;EACD,IAAMmC,iBAAiB,GAAG,SAApBA,iBAAiBA,CAAA,EAAS;IAC9B,IAAItD,GAAG,EAAEyC,EAAE,EAAEc,EAAE;IACf,IAAI,CAAC,CAACvD,GAAG,GAAG3D,IAAI,CAACwD,KAAK,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGG,GAAG,CAAC5C,MAAM,MAAM,CAAC,EAAE;MAC5D4B,aAAa,CAACa,KAAK,GAAG,KAAK;MAC3B;IACN;IACI,IAAI6B,WAAW;IACf,IAAIzD,MAAM,CAAC4B,KAAK,EAAE;MAChB6B,WAAW,GAAGC,UAAU,CAAC1C,SAAS,CAACY,KAAK,EAAE5B,MAAM,CAAC4B,KAAK,CAAC;IAC7D;IACI,IAAM2D,WAAW,GAAG,SAAdA,WAAWA,CAAYrC,GAAG,EAAE;MAChC,IAAIO,WAAW,EAAE;QACf,OAAO,CAAC,CAACA,WAAW,CAACwB,cAAc,CAAC/B,GAAG,EAAElD,MAAM,CAAC4B,KAAK,CAAC,CAAC;MAC/D,CAAO,MAAM;QACL,OAAOZ,SAAS,CAACY,KAAK,CAACuB,QAAQ,CAACD,GAAG,CAAC;MAC5C;IACA,CAAK;IACD,IAAIsC,cAAc,GAAG,IAAI;IACzB,IAAIC,aAAa,GAAG,CAAC;IACrB,IAAIf,aAAa,GAAG,CAAC;IACrB,KAAK,IAAIgB,CAAC,GAAG,CAAC,EAAEC,CAAC,GAAG,CAACvH,IAAI,CAACwD,KAAK,IAAI,EAAE,EAAEzC,MAAM,EAAEuG,CAAC,GAAGC,CAAC,EAAED,CAAC,EAAE,EAAE;MACzD,IAAME,OAAO,GAAG,CAACN,EAAE,GAAG,CAACd,EAAE,GAAGhF,QAAQ,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,QAAQ,CAACoF,KAAK,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGJ,EAAE,CAACnG,MAAM,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGiH,EAAE,CAACtF,MAAM,CAAC4B,KAAK;MACxI,IAAMkD,QAAQ,GAAGY,CAAC,GAAGhB,aAAa;MAClC,IAAMZ,IAAI,GAAG1F,IAAI,CAACwD,KAAK,CAAC8D,CAAC,CAAC;MAC1B,IAAMG,eAAe,GAAG1E,UAAU,CAACS,KAAK,IAAIT,UAAU,CAACS,KAAK,CAACmD,IAAI,CAAC,IAAI,EAAEjB,IAAI,EAAEgB,QAAQ,CAAC;MACvF,IAAI,CAACS,WAAW,CAACzB,IAAI,CAAC,EAAE;QACtB,IAAI,CAAC3C,UAAU,CAACS,KAAK,IAAIiE,eAAe,EAAE;UACxCL,cAAc,GAAG,KAAK;UACtB;QACV;MACA,CAAO,MAAM;QACLC,aAAa,EAAE;MACvB;MACMf,aAAa,IAAIM,iBAAgB,CAACC,cAAc,CAACnB,IAAI,EAAE8B,OAAO,CAAC,CAAC;IACtE;IACI,IAAIH,aAAa,KAAK,CAAC,EACrBD,cAAc,GAAG,KAAK;IACxBzE,aAAa,CAACa,KAAK,GAAG4D,cAAc;EACxC,CAAG;EACD,IAAMR,iBAAgB,GAAG,SAAnBA,gBAAgBA,CAAIL,OAAO,EAAK;IACpC,IAAI5C,GAAG;IACP,IAAI,CAACvC,QAAQ,IAAI,CAACA,QAAQ,CAACoF,KAAK,EAC9B,OAAO,CAAC;IACV,IAAQkB,QAAQ,GAAKtG,QAAQ,CAACoF,KAAK,CAACvG,MAAM,CAAlCyH,QAAQ;IAChB,IAAIC,KAAK,GAAG,CAAC;IACb,IAAM7G,QAAQ,GAAG,CAAC6C,GAAG,GAAG+D,QAAQ,CAAClE,KAAK,CAAC+C,OAAO,CAAC,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG5C,GAAG,CAAC7C,QAAQ;IAChF,IAAIA,QAAQ,EAAE;MACZ6G,KAAK,IAAI7G,QAAQ,CAACC,MAAM;MACxBD,QAAQ,CAACF,OAAO,CAAC,UAACgH,QAAQ,EAAK;QAC7BD,KAAK,IAAIf,iBAAgB,CAACgB,QAAQ,CAAC;MAC3C,CAAO,CAAC;IACR;IACI,OAAOD,KAAK;EAChB,CAAG;EACD,IAAME,aAAa,GAAG,SAAhBA,aAAaA,CAAIC,QAAQ,EAAEC,MAAM,EAAK;IAC1C,IAAI,CAACC,KAAK,CAACC,OAAO,CAACH,QAAQ,CAAC,EAAE;MAC5BA,QAAQ,GAAG,CAACA,QAAQ,CAAC;IAC3B;IACI,IAAMI,QAAQ,GAAG,EAAE;IACnBJ,QAAQ,CAAClH,OAAO,CAAC,UAACuH,GAAG,EAAK;MACxBnF,OAAO,CAACQ,KAAK,CAAC2E,GAAG,CAACC,EAAE,CAAC,GAAGL,MAAM;MAC9BG,QAAQ,CAACC,GAAG,CAACE,SAAS,IAAIF,GAAG,CAACC,EAAE,CAAC,GAAGL,MAAM;IAChD,CAAK,CAAC;IACF,OAAOG,QAAQ;EACnB,CAAG;EACD,IAAMI,UAAU,GAAG,SAAbA,UAAUA,CAAIzH,MAAM,EAAE0H,IAAI,EAAEC,KAAK,EAAK;IAC1C,IAAItI,aAAa,CAACsD,KAAK,IAAItD,aAAa,CAACsD,KAAK,KAAK3C,MAAM,EAAE;MACzDX,aAAa,CAACsD,KAAK,CAACgF,KAAK,GAAG,IAAI;IACtC;IACItI,aAAa,CAACsD,KAAK,GAAG3C,MAAM;IAC5BR,QAAQ,CAACmD,KAAK,GAAG+E,IAAI;IACrBjI,SAAS,CAACkD,KAAK,GAAGgF,KAAK;EAC3B,CAAG;EACD,IAAMC,UAAU,GAAG,SAAbA,UAAUA,CAAA,EAAS;IACvB,IAAIC,UAAU,GAAGC,KAAK,CAAC7G,KAAK,CAAC;IAC7B8G,MAAM,CAACC,IAAI,CAAC7F,OAAO,CAACQ,KAAK,CAAC,CAAC5C,OAAO,CAAC,UAACkI,QAAQ,EAAK;MAC/C,IAAMf,MAAM,GAAG/E,OAAO,CAACQ,KAAK,CAACsF,QAAQ,CAAC;MACtC,IAAI,CAACf,MAAM,IAAIA,MAAM,CAAChH,MAAM,KAAK,CAAC,EAChC;MACF,IAAMF,MAAM,GAAGkI,aAAa,CAAC;QAC3BrI,OAAO,EAAEA,OAAO,CAAC8C;MACzB,CAAO,EAAEsF,QAAQ,CAAC;MACZ,IAAIjI,MAAM,IAAIA,MAAM,CAACmI,YAAY,EAAE;QACjCN,UAAU,GAAGA,UAAU,CAAC3E,MAAM,CAAC,UAACe,GAAG,EAAK;UACtC,OAAOiD,MAAM,CAACkB,IAAI,CAAC,UAACzF,KAAK;YAAA,OAAK3C,MAAM,CAACmI,YAAY,CAACrC,IAAI,CAAC,IAAI,EAAEnD,KAAK,EAAEsB,GAAG,EAAEjE,MAAM,CAAC;UAAA,EAAC;QAC3F,CAAS,CAAC;MACV;IACA,CAAK,CAAC;IACFoC,YAAY,CAACO,KAAK,GAAGkF,UAAU;EACnC,CAAG;EACD,IAAMQ,QAAQ,GAAG,SAAXA,QAAQA,CAAA,EAAS;IACrBlJ,IAAI,CAACwD,KAAK,GAAGzD,QAAQ,CAACkD,YAAY,CAACO,KAAK,EAAE;MACxCtD,aAAa,EAAEA,aAAa,CAACsD,KAAK;MAClCnD,QAAQ,EAAEA,QAAQ,CAACmD,KAAK;MACxBlD,SAAS,EAAEA,SAAS,CAACkD;IAC3B,CAAK,CAAC;EACN,CAAG;EACD,IAAM2F,SAAS,GAAG,SAAZA,SAASA,CAAA,EAAwB;IAAA,IAApBC,MAAM,GAAA3E,SAAA,CAAA1D,MAAA,QAAA0D,SAAA,QAAAC,SAAA,GAAAD,SAAA,MAAG,KAAK,CAAC;IAChC,IAAI,EAAE2E,MAAM,IAAIA,MAAM,CAACrF,MAAM,CAAC,EAAE;MAC9B0E,UAAU,EAAE;IAClB;IACIS,QAAQ,EAAE;EACd,CAAG;EACD,IAAMG,WAAW,GAAG,SAAdA,WAAWA,CAAIC,UAAU,EAAK;IAClC,IAAQC,cAAc,GAAKnI,QAAQ,CAACoI,IAAI,CAAhCD,cAAc;IACtB,IAAI,CAACA,cAAc,EACjB;IACF,IAAME,MAAM,GAAGb,MAAM,CAACc,MAAM,CAAC,EAAE,EAAEH,cAAc,CAACI,YAAY,CAAC;IAC7D,IAAMd,IAAI,GAAGD,MAAM,CAACC,IAAI,CAACY,MAAM,CAAC;IAChC,IAAI,CAACZ,IAAI,CAAC9H,MAAM,EACd;IACF,IAAI,OAAOuI,UAAU,KAAK,QAAQ,EAAE;MAClCA,UAAU,GAAG,CAACA,UAAU,CAAC;IAC/B;IACI,IAAItB,KAAK,CAACC,OAAO,CAACqB,UAAU,CAAC,EAAE;MAC7B,IAAMM,QAAQ,GAAGN,UAAU,CAACO,GAAG,CAAC,UAACrE,GAAG;QAAA,OAAKsE,cAAc,CAAC;UACtDpJ,OAAO,EAAEA,OAAO,CAAC8C;QACzB,CAAO,EAAEgC,GAAG,CAAC;MAAA,EAAC;MACRqD,IAAI,CAACjI,OAAO,CAAC,UAAC4E,GAAG,EAAK;QACpB,IAAM3E,MAAM,GAAG+I,QAAQ,CAACG,IAAI,CAAC,UAAC5B,GAAG;UAAA,OAAKA,GAAG,CAACC,EAAE,KAAK5C,GAAG;QAAA,EAAC;QACrD,IAAI3E,MAAM,EAAE;UACVA,MAAM,CAACmJ,aAAa,GAAG,EAAE;QACnC;MACA,CAAO,CAAC;MACF5I,QAAQ,CAACoF,KAAK,CAACyD,MAAM,CAAC,cAAc,EAAE;QACpCpJ,MAAM,EAAE+I,QAAQ;QAChB7B,MAAM,EAAE,EAAE;QACVmC,MAAM,EAAE,IAAI;QACZC,KAAK,EAAE;MACf,CAAO,CAAC;IACR,CAAK,MAAM;MACLtB,IAAI,CAACjI,OAAO,CAAC,UAAC4E,GAAG,EAAK;QACpB,IAAM3E,MAAM,GAAGH,OAAO,CAAC8C,KAAK,CAACuG,IAAI,CAAC,UAAC5B,GAAG;UAAA,OAAKA,GAAG,CAACC,EAAE,KAAK5C,GAAG;QAAA,EAAC;QAC1D,IAAI3E,MAAM,EAAE;UACVA,MAAM,CAACmJ,aAAa,GAAG,EAAE;QACnC;MACA,CAAO,CAAC;MACFhH,OAAO,CAACQ,KAAK,GAAG,EAAE;MAClBpC,QAAQ,CAACoF,KAAK,CAACyD,MAAM,CAAC,cAAc,EAAE;QACpCpJ,MAAM,EAAE,EAAE;QACVkH,MAAM,EAAE,EAAE;QACVmC,MAAM,EAAE;MAChB,CAAO,CAAC;IACR;EACA,CAAG;EACD,IAAME,SAAS,GAAG,SAAZA,SAASA,CAAA,EAAS;IACtB,IAAI,CAAClK,aAAa,CAACsD,KAAK,EACtB;IACF8E,UAAU,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;IAC5BlH,QAAQ,CAACoF,KAAK,CAACyD,MAAM,CAAC,qBAAqB,EAAE;MAC3CC,MAAM,EAAE;IACd,CAAK,CAAC;EACN,CAAG;EACD,IAAAG,UAAA,GAMIC,SAAS,CAAC;MACZtK,IAAI;MACJ4B;IACJ,CAAG,CAAC;IARA2I,gBAAgB,GAAAF,UAAA,CAAhBE,gBAAgB;IAChBC,kBAAkB,GAAAH,UAAA,CAAlBG,kBAAkB;IAClBC,gBAAgB,GAAAJ,UAAA,CAAhBI,gBAAgB;IACRC,YAAY,GAAAL,UAAA,CAApBpK,MAAM;IACN0K,aAAa,GAAAN,UAAA,CAAbM,aAAa;EAKf,IAAAC,QAAA,GAMIC,OAAO,CAAC;MACV7K,IAAI;MACJ4B;IACJ,CAAG,CAAC;IARAkJ,oBAAoB,GAAAF,QAAA,CAApBE,oBAAoB;IACpBC,mBAAmB,GAAAH,QAAA,CAAnBG,mBAAmB;IACnBC,cAAc,GAAAJ,QAAA,CAAdI,cAAc;IACdC,YAAY,GAAAL,QAAA,CAAZK,YAAY;IACJC,UAAU,GAAAN,QAAA,CAAlB3K,MAAM;EAKR,IAAAkL,WAAA,GAKIC,UAAU,CAAC;MACbpL,IAAI;MACJ4B;IACJ,CAAG,CAAC;IAPAyJ,oBAAoB,GAAAF,WAAA,CAApBE,oBAAoB;IACpBC,gBAAgB,GAAAH,WAAA,CAAhBG,gBAAgB;IAChBC,gBAAgB,GAAAJ,WAAA,CAAhBI,gBAAgB;IACRC,WAAW,GAAAL,WAAA,CAAnBlL,MAAM;EAKR,IAAMwL,uBAAuB,GAAG,SAA1BA,uBAAuBA,CAAIC,GAAG,EAAK;IACvCnB,gBAAgB,CAACmB,GAAG,CAAC;IACrBZ,oBAAoB,CAACY,GAAG,CAAC;EAC7B,CAAG;EACD,IAAMC,yBAAyB,GAAG,SAA5BA,yBAAyBA,CAAI7G,GAAG,EAAE8G,QAAQ,EAAK;IACnD,IAAMC,eAAe,GAAGnL,OAAO,CAAC8C,KAAK,CAACyF,IAAI,CAAC,UAAA6C,IAAA;MAAA,IAAG9H,IAAI,GAAA8H,IAAA,CAAJ9H,IAAI;MAAA,OAAOA,IAAI,KAAK,QAAQ;IAAA,EAAC;IAC3E,IAAI6H,eAAe,EAAE;MACnBrB,kBAAkB,CAAC1F,GAAG,EAAE8G,QAAQ,CAAC;IACvC,CAAK,MAAM;MACLb,mBAAmB,CAACjG,GAAG,EAAE8G,QAAQ,CAAC;IACxC;EACA,CAAG;EACD,OAAO;IACLrI,YAAY;IACZO,aAAa;IACbT,cAAc;IACdwB,UAAU;IACVG,cAAc;IACdG,cAAc;IACdU,gBAAgB;IAChBC,kBAAkB;IAClBK,mBAAmB;IACnB4F,kBAAkB,EAAE,IAAI;IACxBjF,uBAAuB;IACvBG,iBAAiB;IACjBY,aAAa;IACbyD,gBAAgB;IAChBhD,UAAU;IACVG,UAAU;IACVS,QAAQ;IACRC,SAAS;IACTE,WAAW;IACXe,SAAS;IACTI,kBAAkB;IAClBiB,uBAAuB;IACvBF,gBAAgB;IAChBI,yBAAyB;IACzBhB,aAAa;IACbF,gBAAgB;IAChBY,oBAAoB;IACpBJ,YAAY;IACZD,cAAc;IACd/K,MAAM,EAAA+L,aAAA,CAAAA,aAAA,CAAAA,aAAA;MACJtK,SAAS;MACTE,MAAM;MACN5B,IAAI;MACJ8B,KAAK;MACLC,SAAS;MACTC,QAAQ;MACRC,aAAa;MACbvB,OAAO;MACPwB,YAAY;MACZC,iBAAiB;MACjBC,WAAW;MACXC,gBAAgB;MAChBC,qBAAqB;MACrBC,cAAc;MACdC,iBAAiB;MACjBC,sBAAsB;MACtBC,2BAA2B;MAC3BC,aAAa;MACbC,SAAS;MACTC,gBAAgB;MAChBC,qBAAqB;MACrBC,UAAU;MACVC,OAAO;MACPC,YAAY;MACZ/C,aAAa;MACbG,QAAQ;MACRC,SAAS;MACT4C;IAAQ,GACLwH,YAAY,GACZQ,UAAU,GACVM,WAAW;EAEpB,CAAG;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
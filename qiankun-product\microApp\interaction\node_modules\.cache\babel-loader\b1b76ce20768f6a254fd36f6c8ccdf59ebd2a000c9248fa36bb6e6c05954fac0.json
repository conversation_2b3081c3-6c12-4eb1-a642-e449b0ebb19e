{"ast": null, "code": "import baseToPairs from './_baseToPairs.js';\nimport getTag from './_getTag.js';\nimport mapToArray from './_mapToArray.js';\nimport setToPairs from './_setToPairs.js';\n\n/** `Object#toString` result references. */\nvar mapTag = '[object Map]',\n  setTag = '[object Set]';\n\n/**\n * Creates a `_.toPairs` or `_.toPairsIn` function.\n *\n * @private\n * @param {Function} keysFunc The function to get the keys of a given object.\n * @returns {Function} Returns the new pairs function.\n */\nfunction createToPairs(keysFunc) {\n  return function (object) {\n    var tag = getTag(object);\n    if (tag == mapTag) {\n      return mapToArray(object);\n    }\n    if (tag == setTag) {\n      return setToPairs(object);\n    }\n    return baseToPairs(object, keysFunc(object));\n  };\n}\nexport default createToPairs;", "map": {"version": 3, "names": ["baseToPairs", "getTag", "mapToArray", "setToPairs", "mapTag", "setTag", "createToPairs", "keysFunc", "object", "tag"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/node_modules/.pnpm/lodash-es@4.17.21/node_modules/lodash-es/_createToPairs.js"], "sourcesContent": ["import baseToPairs from './_baseToPairs.js';\nimport getTag from './_getTag.js';\nimport mapToArray from './_mapToArray.js';\nimport setToPairs from './_setToPairs.js';\n\n/** `Object#toString` result references. */\nvar mapTag = '[object Map]',\n    setTag = '[object Set]';\n\n/**\n * Creates a `_.toPairs` or `_.toPairsIn` function.\n *\n * @private\n * @param {Function} keysFunc The function to get the keys of a given object.\n * @returns {Function} Returns the new pairs function.\n */\nfunction createToPairs(keysFunc) {\n  return function(object) {\n    var tag = getTag(object);\n    if (tag == mapTag) {\n      return mapToArray(object);\n    }\n    if (tag == setTag) {\n      return setToPairs(object);\n    }\n    return baseToPairs(object, keysFunc(object));\n  };\n}\n\nexport default createToPairs;\n"], "mappings": "AAAA,OAAOA,WAAW,MAAM,mBAAmB;AAC3C,OAAOC,MAAM,MAAM,cAAc;AACjC,OAAOC,UAAU,MAAM,kBAAkB;AACzC,OAAOC,UAAU,MAAM,kBAAkB;;AAEzC;AACA,IAAIC,MAAM,GAAG,cAAc;EACvBC,MAAM,GAAG,cAAc;;AAE3B;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,aAAaA,CAACC,QAAQ,EAAE;EAC/B,OAAO,UAASC,MAAM,EAAE;IACtB,IAAIC,GAAG,GAAGR,MAAM,CAACO,MAAM,CAAC;IACxB,IAAIC,GAAG,IAAIL,MAAM,EAAE;MACjB,OAAOF,UAAU,CAACM,MAAM,CAAC;IAC3B;IACA,IAAIC,GAAG,IAAIJ,MAAM,EAAE;MACjB,OAAOF,UAAU,CAACK,MAAM,CAAC;IAC3B;IACA,OAAOR,WAAW,CAACQ,MAAM,EAAED,QAAQ,CAACC,MAAM,CAAC,CAAC;EAC9C,CAAC;AACH;AAEA,eAAeF,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
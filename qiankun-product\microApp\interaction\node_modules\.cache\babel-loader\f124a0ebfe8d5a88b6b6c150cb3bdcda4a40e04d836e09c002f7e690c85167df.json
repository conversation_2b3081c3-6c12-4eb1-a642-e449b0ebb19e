{"ast": null, "code": "import { resolveComponent as _resolveComponent, createVNode as _createVNode, withCtx as _withCtx, createTextVNode as _createTextVNode, createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock } from \"vue\";\nvar _hoisted_1 = {\n  class: \"ClusterFileManageNew\"\n};\nvar _hoisted_2 = {\n  class: \"globalFormButton\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  var _component_xyl_upload_file = _resolveComponent(\"xyl-upload-file\");\n  var _component_el_form_item = _resolveComponent(\"el-form-item\");\n  var _component_el_button = _resolveComponent(\"el-button\");\n  var _component_el_form = _resolveComponent(\"el-form\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createVNode(_component_el_form, {\n    ref: \"formRef\",\n    model: $setup.form,\n    rules: $setup.rules,\n    inline: \"\",\n    \"label-position\": \"top\",\n    class: \"globalForm\"\n  }, {\n    default: _withCtx(function () {\n      return [_createVNode(_component_el_form_item, {\n        label: \"群文件\",\n        prop: \"fileId\",\n        class: \"globalFormTitle\"\n      }, {\n        default: _withCtx(function () {\n          return [_createVNode(_component_xyl_upload_file, {\n            fileData: $setup.fileData,\n            onFileUpload: $setup.fileUpload,\n            max: 1\n          }, null, 8 /* PROPS */, [\"fileData\"])];\n        }),\n        _: 1 /* STABLE */\n      }), _createElementVNode(\"div\", _hoisted_2, [_createVNode(_component_el_button, {\n        type: \"primary\",\n        onClick: _cache[0] || (_cache[0] = function ($event) {\n          return $setup.submitForm($setup.formRef);\n        })\n      }, {\n        default: _withCtx(function () {\n          return _cache[1] || (_cache[1] = [_createTextVNode(\"提交\")]);\n        }),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_button, {\n        onClick: $setup.resetForm\n      }, {\n        default: _withCtx(function () {\n          return _cache[2] || (_cache[2] = [_createTextVNode(\"取消\")]);\n        }),\n        _: 1 /* STABLE */\n      })])];\n    }),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"model\", \"rules\"])]);\n}", "map": {"version": 3, "names": ["class", "_createElementBlock", "_hoisted_1", "_createVNode", "_component_el_form", "ref", "model", "$setup", "form", "rules", "inline", "default", "_withCtx", "_component_el_form_item", "label", "prop", "_component_xyl_upload_file", "fileData", "onFileUpload", "fileUpload", "max", "_", "_createElementVNode", "_hoisted_2", "_component_el_button", "type", "onClick", "_cache", "$event", "submitForm", "formRef", "_createTextVNode", "resetForm"], "sources": ["D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\interaction\\src\\views\\ClusterManage\\component\\ClusterFileManageNew.vue"], "sourcesContent": ["<template>\r\n  <div class=\"ClusterFileManageNew\">\r\n    <el-form ref=\"formRef\"\r\n             :model=\"form\"\r\n             :rules=\"rules\"\r\n             inline\r\n             label-position=\"top\"\r\n             class=\"globalForm\">\r\n      <el-form-item label=\"群文件\"\r\n                    prop=\"fileId\"\r\n                    class=\"globalFormTitle\">\r\n        <xyl-upload-file :fileData=\"fileData\"\r\n                         @fileUpload=\"fileUpload\"\r\n                         :max=\"1\" />\r\n      </el-form-item>\r\n      <div class=\"globalFormButton\">\r\n        <el-button type=\"primary\"\r\n                   @click=\"submitForm(formRef)\">提交</el-button>\r\n        <el-button @click=\"resetForm\">取消</el-button>\r\n      </div>\r\n    </el-form>\r\n  </div>\r\n</template>\r\n<script>\r\nexport default { name: 'ClusterFileManageNew' }\r\n</script>\r\n<script setup>\r\nimport api from '@/api'\r\nimport { reactive, ref, onMounted } from 'vue'\r\nimport { ElMessage } from 'element-plus'\r\nconst props = defineProps({ id: { type: String, default: '' }, dataId: { type: String, default: '' } })\r\nconst emit = defineEmits(['callback'])\r\n\r\nconst formRef = ref()\r\nconst form = reactive({\r\n  fileId: ''\r\n})\r\nconst rules = reactive({\r\n  fileId: [{ required: true, message: '请上传文件', trigger: ['blur', 'change'] }]\r\n})\r\nconst fileData = ref([])\r\n\r\nonMounted(() => { if (props.id) { ClusterFileInfo() } })\r\nconst fileUpload = (file) => {\r\n  form.fileId = file[0].id\r\n  fileData.value = file\r\n}\r\nconst ClusterFileInfo = async () => {\r\n  const res = await api.ClusterFileInfo({ detailId: props.id })\r\n  var { data } = res\r\n  form.groupName = data.groupName\r\n  form.ownerUserId = data.ownerUserId\r\n}\r\nconst submitForm = async (formEl) => {\r\n  if (!formEl) return\r\n  await formEl.validate((valid, fields) => {\r\n    if (valid) { globalJson() } else { ElMessage({ type: 'warning', message: '请根据提示信息完善字段内容！' }) }\r\n  })\r\n}\r\nconst globalJson = async () => {\r\n  const { code } = await api.globalJson(props.id ? '/chatGroupFile/edit' : '/chatGroupFile/add', {\r\n    form: {\r\n      id: props.id,\r\n      fileId: form.fileId,\r\n      chatGroupId: props.dataId\r\n    }\r\n  })\r\n  if (code === 200) {\r\n    ElMessage({ type: 'success', message: props.id ? '编辑成功' : '新增成功' })\r\n    emit('callback')\r\n  }\r\n}\r\nconst resetForm = () => { emit('callback') }\r\n</script>\r\n<style lang=\"scss\">\r\n.ClusterFileManageNew {\r\n  width: 680px;\r\n}\r\n</style>\r\n"], "mappings": ";;EACOA,KAAK,EAAC;AAAsB;;EAcxBA,KAAK,EAAC;AAAkB;;;;;;uBAdjCC,mBAAA,CAoBM,OApBNC,UAoBM,GAnBJC,YAAA,CAkBUC,kBAAA;IAlBDC,GAAG,EAAC,SAAS;IACZC,KAAK,EAAEC,MAAA,CAAAC,IAAI;IACXC,KAAK,EAAEF,MAAA,CAAAE,KAAK;IACbC,MAAM,EAAN,EAAM;IACN,gBAAc,EAAC,KAAK;IACpBV,KAAK,EAAC;;IAPnBW,OAAA,EAAAC,QAAA,CAQM;MAAA,OAMe,CANfT,YAAA,CAMeU,uBAAA;QANDC,KAAK,EAAC,KAAK;QACXC,IAAI,EAAC,QAAQ;QACbf,KAAK,EAAC;;QAV1BW,OAAA,EAAAC,QAAA,CAWQ;UAAA,OAE4B,CAF5BT,YAAA,CAE4Ba,0BAAA;YAFVC,QAAQ,EAAEV,MAAA,CAAAU,QAAQ;YAClBC,YAAU,EAAEX,MAAA,CAAAY,UAAU;YACtBC,GAAG,EAAE;;;QAb/BC,CAAA;UAeMC,mBAAA,CAIM,OAJNC,UAIM,GAHJpB,YAAA,CACsDqB,oBAAA;QAD3CC,IAAI,EAAC,SAAS;QACbC,OAAK,EAAAC,MAAA,QAAAA,MAAA,gBAAAC,MAAA;UAAA,OAAErB,MAAA,CAAAsB,UAAU,CAACtB,MAAA,CAAAuB,OAAO;QAAA;;QAjB7CnB,OAAA,EAAAC,QAAA,CAiBgD;UAAA,OAAEe,MAAA,QAAAA,MAAA,OAjBlDI,gBAAA,CAiBgD,IAAE,E;;QAjBlDV,CAAA;UAkBQlB,YAAA,CAA4CqB,oBAAA;QAAhCE,OAAK,EAAEnB,MAAA,CAAAyB;MAAS;QAlBpCrB,OAAA,EAAAC,QAAA,CAkBsC;UAAA,OAAEe,MAAA,QAAAA,MAAA,OAlBxCI,gBAAA,CAkBsC,IAAE,E;;QAlBxCV,CAAA;;;IAAAA,CAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
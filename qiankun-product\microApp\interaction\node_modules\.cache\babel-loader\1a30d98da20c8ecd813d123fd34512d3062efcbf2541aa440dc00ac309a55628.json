{"ast": null, "code": "\"use strict\";\n\nmodule.exports = function (options) {\n  var getState = options.stateHandler.getState;\n\n  /**\n   * Tells if the element has been made detectable and ready to be listened for resize events.\n   * @public\n   * @param {element} The element to check.\n   * @returns {boolean} True or false depending on if the element is detectable or not.\n   */\n  function isDetectable(element) {\n    var state = getState(element);\n    return state && !!state.isDetectable;\n  }\n\n  /**\n   * Marks the element that it has been made detectable and ready to be listened for resize events.\n   * @public\n   * @param {element} The element to mark.\n   */\n  function markAsDetectable(element) {\n    getState(element).isDetectable = true;\n  }\n\n  /**\n   * Tells if the element is busy or not.\n   * @public\n   * @param {element} The element to check.\n   * @returns {boolean} True or false depending on if the element is busy or not.\n   */\n  function isBusy(element) {\n    return !!getState(element).busy;\n  }\n\n  /**\n   * Marks the object is busy and should not be made detectable.\n   * @public\n   * @param {element} element The element to mark.\n   * @param {boolean} busy If the element is busy or not.\n   */\n  function markBusy(element, busy) {\n    getState(element).busy = !!busy;\n  }\n  return {\n    isDetectable: isDetectable,\n    markAsDetectable: markAsDetectable,\n    isBusy: isBusy,\n    markBusy: markBusy\n  };\n};", "map": {"version": 3, "names": ["module", "exports", "options", "getState", "state<PERSON><PERSON><PERSON>", "isDetectable", "element", "state", "markAsDetectable", "isBusy", "busy", "mark<PERSON><PERSON><PERSON>"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/node_modules/.pnpm/element-resize-detector@1.2.4/node_modules/element-resize-detector/src/element-utils.js"], "sourcesContent": ["\"use strict\";\n\nmodule.exports = function(options) {\n    var getState = options.stateHandler.getState;\n\n    /**\n     * Tells if the element has been made detectable and ready to be listened for resize events.\n     * @public\n     * @param {element} The element to check.\n     * @returns {boolean} True or false depending on if the element is detectable or not.\n     */\n    function isDetectable(element) {\n        var state = getState(element);\n        return state && !!state.isDetectable;\n    }\n\n    /**\n     * Marks the element that it has been made detectable and ready to be listened for resize events.\n     * @public\n     * @param {element} The element to mark.\n     */\n    function markAsDetectable(element) {\n        getState(element).isDetectable = true;\n    }\n\n    /**\n     * Tells if the element is busy or not.\n     * @public\n     * @param {element} The element to check.\n     * @returns {boolean} True or false depending on if the element is busy or not.\n     */\n    function isBusy(element) {\n        return !!getState(element).busy;\n    }\n\n    /**\n     * Marks the object is busy and should not be made detectable.\n     * @public\n     * @param {element} element The element to mark.\n     * @param {boolean} busy If the element is busy or not.\n     */\n    function markBusy(element, busy) {\n        getState(element).busy = !!busy;\n    }\n\n    return {\n        isDetectable: isDetectable,\n        markAsDetectable: markAsDetectable,\n        isBusy: isBusy,\n        markBusy: markBusy\n    };\n};\n"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,OAAO,GAAG,UAASC,OAAO,EAAE;EAC/B,IAAIC,QAAQ,GAAGD,OAAO,CAACE,YAAY,CAACD,QAAQ;;EAE5C;AACJ;AACA;AACA;AACA;AACA;EACI,SAASE,YAAYA,CAACC,OAAO,EAAE;IAC3B,IAAIC,KAAK,GAAGJ,QAAQ,CAACG,OAAO,CAAC;IAC7B,OAAOC,KAAK,IAAI,CAAC,CAACA,KAAK,CAACF,YAAY;EACxC;;EAEA;AACJ;AACA;AACA;AACA;EACI,SAASG,gBAAgBA,CAACF,OAAO,EAAE;IAC/BH,QAAQ,CAACG,OAAO,CAAC,CAACD,YAAY,GAAG,IAAI;EACzC;;EAEA;AACJ;AACA;AACA;AACA;AACA;EACI,SAASI,MAAMA,CAACH,OAAO,EAAE;IACrB,OAAO,CAAC,CAACH,QAAQ,CAACG,OAAO,CAAC,CAACI,IAAI;EACnC;;EAEA;AACJ;AACA;AACA;AACA;AACA;EACI,SAASC,QAAQA,CAACL,OAAO,EAAEI,IAAI,EAAE;IAC7BP,QAAQ,CAACG,OAAO,CAAC,CAACI,IAAI,GAAG,CAAC,CAACA,IAAI;EACnC;EAEA,OAAO;IACHL,YAAY,EAAEA,YAAY;IAC1BG,gBAAgB,EAAEA,gBAAgB;IAClCC,MAAM,EAAEA,MAAM;IACdE,QAAQ,EAAEA;EACd,CAAC;AACL,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}
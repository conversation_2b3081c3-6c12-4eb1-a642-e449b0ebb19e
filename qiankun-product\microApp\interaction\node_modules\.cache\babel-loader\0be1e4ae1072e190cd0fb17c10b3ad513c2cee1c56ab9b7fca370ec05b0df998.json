{"ast": null, "code": "function _regeneratorRuntime() { \"use strict\"; /*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */ _regeneratorRuntime = function _regeneratorRuntime() { return e; }; var t, e = {}, r = Object.prototype, n = r.hasOwnProperty, o = Object.defineProperty || function (t, e, r) { t[e] = r.value; }, i = \"function\" == typeof Symbol ? Symbol : {}, a = i.iterator || \"@@iterator\", c = i.asyncIterator || \"@@asyncIterator\", u = i.toStringTag || \"@@toStringTag\"; function define(t, e, r) { return Object.defineProperty(t, e, { value: r, enumerable: !0, configurable: !0, writable: !0 }), t[e]; } try { define({}, \"\"); } catch (t) { define = function define(t, e, r) { return t[e] = r; }; } function wrap(t, e, r, n) { var i = e && e.prototype instanceof Generator ? e : Generator, a = Object.create(i.prototype), c = new Context(n || []); return o(a, \"_invoke\", { value: makeInvokeMethod(t, r, c) }), a; } function tryCatch(t, e, r) { try { return { type: \"normal\", arg: t.call(e, r) }; } catch (t) { return { type: \"throw\", arg: t }; } } e.wrap = wrap; var h = \"suspendedStart\", l = \"suspendedYield\", f = \"executing\", s = \"completed\", y = {}; function Generator() {} function GeneratorFunction() {} function GeneratorFunctionPrototype() {} var p = {}; define(p, a, function () { return this; }); var d = Object.getPrototypeOf, v = d && d(d(values([]))); v && v !== r && n.call(v, a) && (p = v); var g = GeneratorFunctionPrototype.prototype = Generator.prototype = Object.create(p); function defineIteratorMethods(t) { [\"next\", \"throw\", \"return\"].forEach(function (e) { define(t, e, function (t) { return this._invoke(e, t); }); }); } function AsyncIterator(t, e) { function invoke(r, o, i, a) { var c = tryCatch(t[r], t, o); if (\"throw\" !== c.type) { var u = c.arg, h = u.value; return h && \"object\" == typeof h && n.call(h, \"__await\") ? e.resolve(h.__await).then(function (t) { invoke(\"next\", t, i, a); }, function (t) { invoke(\"throw\", t, i, a); }) : e.resolve(h).then(function (t) { u.value = t, i(u); }, function (t) { return invoke(\"throw\", t, i, a); }); } a(c.arg); } var r; o(this, \"_invoke\", { value: function value(t, n) { function callInvokeWithMethodAndArg() { return new e(function (e, r) { invoke(t, n, e, r); }); } return r = r ? r.then(callInvokeWithMethodAndArg, callInvokeWithMethodAndArg) : callInvokeWithMethodAndArg(); } }); } function makeInvokeMethod(e, r, n) { var o = h; return function (i, a) { if (o === f) throw Error(\"Generator is already running\"); if (o === s) { if (\"throw\" === i) throw a; return { value: t, done: !0 }; } for (n.method = i, n.arg = a;;) { var c = n.delegate; if (c) { var u = maybeInvokeDelegate(c, n); if (u) { if (u === y) continue; return u; } } if (\"next\" === n.method) n.sent = n._sent = n.arg;else if (\"throw\" === n.method) { if (o === h) throw o = s, n.arg; n.dispatchException(n.arg); } else \"return\" === n.method && n.abrupt(\"return\", n.arg); o = f; var p = tryCatch(e, r, n); if (\"normal\" === p.type) { if (o = n.done ? s : l, p.arg === y) continue; return { value: p.arg, done: n.done }; } \"throw\" === p.type && (o = s, n.method = \"throw\", n.arg = p.arg); } }; } function maybeInvokeDelegate(e, r) { var n = r.method, o = e.iterator[n]; if (o === t) return r.delegate = null, \"throw\" === n && e.iterator.return && (r.method = \"return\", r.arg = t, maybeInvokeDelegate(e, r), \"throw\" === r.method) || \"return\" !== n && (r.method = \"throw\", r.arg = new TypeError(\"The iterator does not provide a '\" + n + \"' method\")), y; var i = tryCatch(o, e.iterator, r.arg); if (\"throw\" === i.type) return r.method = \"throw\", r.arg = i.arg, r.delegate = null, y; var a = i.arg; return a ? a.done ? (r[e.resultName] = a.value, r.next = e.nextLoc, \"return\" !== r.method && (r.method = \"next\", r.arg = t), r.delegate = null, y) : a : (r.method = \"throw\", r.arg = new TypeError(\"iterator result is not an object\"), r.delegate = null, y); } function pushTryEntry(t) { var e = { tryLoc: t[0] }; 1 in t && (e.catchLoc = t[1]), 2 in t && (e.finallyLoc = t[2], e.afterLoc = t[3]), this.tryEntries.push(e); } function resetTryEntry(t) { var e = t.completion || {}; e.type = \"normal\", delete e.arg, t.completion = e; } function Context(t) { this.tryEntries = [{ tryLoc: \"root\" }], t.forEach(pushTryEntry, this), this.reset(!0); } function values(e) { if (e || \"\" === e) { var r = e[a]; if (r) return r.call(e); if (\"function\" == typeof e.next) return e; if (!isNaN(e.length)) { var o = -1, i = function next() { for (; ++o < e.length;) if (n.call(e, o)) return next.value = e[o], next.done = !1, next; return next.value = t, next.done = !0, next; }; return i.next = i; } } throw new TypeError(typeof e + \" is not iterable\"); } return GeneratorFunction.prototype = GeneratorFunctionPrototype, o(g, \"constructor\", { value: GeneratorFunctionPrototype, configurable: !0 }), o(GeneratorFunctionPrototype, \"constructor\", { value: GeneratorFunction, configurable: !0 }), GeneratorFunction.displayName = define(GeneratorFunctionPrototype, u, \"GeneratorFunction\"), e.isGeneratorFunction = function (t) { var e = \"function\" == typeof t && t.constructor; return !!e && (e === GeneratorFunction || \"GeneratorFunction\" === (e.displayName || e.name)); }, e.mark = function (t) { return Object.setPrototypeOf ? Object.setPrototypeOf(t, GeneratorFunctionPrototype) : (t.__proto__ = GeneratorFunctionPrototype, define(t, u, \"GeneratorFunction\")), t.prototype = Object.create(g), t; }, e.awrap = function (t) { return { __await: t }; }, defineIteratorMethods(AsyncIterator.prototype), define(AsyncIterator.prototype, c, function () { return this; }), e.AsyncIterator = AsyncIterator, e.async = function (t, r, n, o, i) { void 0 === i && (i = Promise); var a = new AsyncIterator(wrap(t, r, n, o), i); return e.isGeneratorFunction(r) ? a : a.next().then(function (t) { return t.done ? t.value : a.next(); }); }, defineIteratorMethods(g), define(g, u, \"Generator\"), define(g, a, function () { return this; }), define(g, \"toString\", function () { return \"[object Generator]\"; }), e.keys = function (t) { var e = Object(t), r = []; for (var n in e) r.push(n); return r.reverse(), function next() { for (; r.length;) { var t = r.pop(); if (t in e) return next.value = t, next.done = !1, next; } return next.done = !0, next; }; }, e.values = values, Context.prototype = { constructor: Context, reset: function reset(e) { if (this.prev = 0, this.next = 0, this.sent = this._sent = t, this.done = !1, this.delegate = null, this.method = \"next\", this.arg = t, this.tryEntries.forEach(resetTryEntry), !e) for (var r in this) \"t\" === r.charAt(0) && n.call(this, r) && !isNaN(+r.slice(1)) && (this[r] = t); }, stop: function stop() { this.done = !0; var t = this.tryEntries[0].completion; if (\"throw\" === t.type) throw t.arg; return this.rval; }, dispatchException: function dispatchException(e) { if (this.done) throw e; var r = this; function handle(n, o) { return a.type = \"throw\", a.arg = e, r.next = n, o && (r.method = \"next\", r.arg = t), !!o; } for (var o = this.tryEntries.length - 1; o >= 0; --o) { var i = this.tryEntries[o], a = i.completion; if (\"root\" === i.tryLoc) return handle(\"end\"); if (i.tryLoc <= this.prev) { var c = n.call(i, \"catchLoc\"), u = n.call(i, \"finallyLoc\"); if (c && u) { if (this.prev < i.catchLoc) return handle(i.catchLoc, !0); if (this.prev < i.finallyLoc) return handle(i.finallyLoc); } else if (c) { if (this.prev < i.catchLoc) return handle(i.catchLoc, !0); } else { if (!u) throw Error(\"try statement without catch or finally\"); if (this.prev < i.finallyLoc) return handle(i.finallyLoc); } } } }, abrupt: function abrupt(t, e) { for (var r = this.tryEntries.length - 1; r >= 0; --r) { var o = this.tryEntries[r]; if (o.tryLoc <= this.prev && n.call(o, \"finallyLoc\") && this.prev < o.finallyLoc) { var i = o; break; } } i && (\"break\" === t || \"continue\" === t) && i.tryLoc <= e && e <= i.finallyLoc && (i = null); var a = i ? i.completion : {}; return a.type = t, a.arg = e, i ? (this.method = \"next\", this.next = i.finallyLoc, y) : this.complete(a); }, complete: function complete(t, e) { if (\"throw\" === t.type) throw t.arg; return \"break\" === t.type || \"continue\" === t.type ? this.next = t.arg : \"return\" === t.type ? (this.rval = this.arg = t.arg, this.method = \"return\", this.next = \"end\") : \"normal\" === t.type && e && (this.next = e), y; }, finish: function finish(t) { for (var e = this.tryEntries.length - 1; e >= 0; --e) { var r = this.tryEntries[e]; if (r.finallyLoc === t) return this.complete(r.completion, r.afterLoc), resetTryEntry(r), y; } }, catch: function _catch(t) { for (var e = this.tryEntries.length - 1; e >= 0; --e) { var r = this.tryEntries[e]; if (r.tryLoc === t) { var n = r.completion; if (\"throw\" === n.type) { var o = n.arg; resetTryEntry(r); } return o; } } throw Error(\"illegal catch attempt\"); }, delegateYield: function delegateYield(e, r, n) { return this.delegate = { iterator: values(e), resultName: r, nextLoc: n }, \"next\" === this.method && (this.arg = t), y; } }, e; }\nfunction asyncGeneratorStep(n, t, e, r, o, a, c) { try { var i = n[a](c), u = i.value; } catch (n) { return void e(n); } i.done ? t(u) : Promise.resolve(u).then(r, o); }\nfunction _asyncToGenerator(n) { return function () { var t = this, e = arguments; return new Promise(function (r, o) { var a = n.apply(t, e); function _next(n) { asyncGeneratorStep(a, r, o, _next, _throw, \"next\", n); } function _throw(n) { asyncGeneratorStep(a, r, o, _next, _throw, \"throw\", n); } _next(void 0); }); }; }\nfunction _slicedToArray(r, e) { return _arrayWithHoles(r) || _iterableToArrayLimit(r, e) || _unsupportedIterableToArray(r, e) || _nonIterableRest(); }\nfunction _nonIterableRest() { throw new TypeError(\"Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); }\nfunction _unsupportedIterableToArray(r, a) { if (r) { if (\"string\" == typeof r) return _arrayLikeToArray(r, a); var t = {}.toString.call(r).slice(8, -1); return \"Object\" === t && r.constructor && (t = r.constructor.name), \"Map\" === t || \"Set\" === t ? Array.from(r) : \"Arguments\" === t || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t) ? _arrayLikeToArray(r, a) : void 0; } }\nfunction _arrayLikeToArray(r, a) { (null == a || a > r.length) && (a = r.length); for (var e = 0, n = Array(a); e < a; e++) n[e] = r[e]; return n; }\nfunction _iterableToArrayLimit(r, l) { var t = null == r ? null : \"undefined\" != typeof Symbol && r[Symbol.iterator] || r[\"@@iterator\"]; if (null != t) { var e, n, i, u, a = [], f = !0, o = !1; try { if (i = (t = t.call(r)).next, 0 === l) { if (Object(t) !== t) return; f = !1; } else for (; !(f = (e = i.call(t)).done) && (a.push(e.value), a.length !== l); f = !0); } catch (r) { o = !0, n = r; } finally { try { if (!f && null != t.return && (u = t.return(), Object(u) !== u)) return; } finally { if (o) throw n; } } return a; } }\nfunction _arrayWithHoles(r) { if (Array.isArray(r)) return r; }\nimport { ref, computed, unref, watch, nextTick } from 'vue';\nimport dayjs from 'dayjs';\nimport { flatten } from 'lodash-unified';\nimport '../../../../hooks/index.mjs';\nimport '../../../../utils/index.mjs';\nimport { buildPickerTable } from '../utils.mjs';\nimport { useLocale } from '../../../../hooks/use-locale/index.mjs';\nimport { castArray } from '../../../../utils/arrays.mjs';\nimport { useNamespace } from '../../../../hooks/use-namespace/index.mjs';\nvar isNormalDay = function isNormalDay() {\n  var type = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : \"\";\n  return [\"normal\", \"today\"].includes(type);\n};\nvar useBasicDateTable = function useBasicDateTable(props, emit) {\n  var _useLocale = useLocale(),\n    lang = _useLocale.lang;\n  var tbodyRef = ref();\n  var currentCellRef = ref();\n  var lastRow = ref();\n  var lastColumn = ref();\n  var tableRows = ref([[], [], [], [], [], []]);\n  var focusWithClick = false;\n  var firstDayOfWeek = props.date.$locale().weekStart || 7;\n  var WEEKS_CONSTANT = props.date.locale(\"en\").localeData().weekdaysShort().map(function (_) {\n    return _.toLowerCase();\n  });\n  var offsetDay = computed(function () {\n    return firstDayOfWeek > 3 ? 7 - firstDayOfWeek : -firstDayOfWeek;\n  });\n  var startDate = computed(function () {\n    var startDayOfMonth = props.date.startOf(\"month\");\n    return startDayOfMonth.subtract(startDayOfMonth.day() || 7, \"day\");\n  });\n  var WEEKS = computed(function () {\n    return WEEKS_CONSTANT.concat(WEEKS_CONSTANT).slice(firstDayOfWeek, firstDayOfWeek + 7);\n  });\n  var hasCurrent = computed(function () {\n    return flatten(unref(rows)).some(function (row) {\n      return row.isCurrent;\n    });\n  });\n  var days = computed(function () {\n    var startOfMonth = props.date.startOf(\"month\");\n    var startOfMonthDay = startOfMonth.day() || 7;\n    var dateCountOfMonth = startOfMonth.daysInMonth();\n    var dateCountOfLastMonth = startOfMonth.subtract(1, \"month\").daysInMonth();\n    return {\n      startOfMonthDay,\n      dateCountOfMonth,\n      dateCountOfLastMonth\n    };\n  });\n  var selectedDate = computed(function () {\n    return props.selectionMode === \"dates\" ? castArray(props.parsedValue) : [];\n  });\n  var setDateText = function setDateText(cell, _ref) {\n    var count = _ref.count,\n      rowIndex = _ref.rowIndex,\n      columnIndex = _ref.columnIndex;\n    var _unref = unref(days),\n      startOfMonthDay = _unref.startOfMonthDay,\n      dateCountOfMonth = _unref.dateCountOfMonth,\n      dateCountOfLastMonth = _unref.dateCountOfLastMonth;\n    var offset = unref(offsetDay);\n    if (rowIndex >= 0 && rowIndex <= 1) {\n      var numberOfDaysFromPreviousMonth = startOfMonthDay + offset < 0 ? 7 + startOfMonthDay + offset : startOfMonthDay + offset;\n      if (columnIndex + rowIndex * 7 >= numberOfDaysFromPreviousMonth) {\n        cell.text = count;\n        return true;\n      } else {\n        cell.text = dateCountOfLastMonth - (numberOfDaysFromPreviousMonth - columnIndex % 7) + 1 + rowIndex * 7;\n        cell.type = \"prev-month\";\n      }\n    } else {\n      if (count <= dateCountOfMonth) {\n        cell.text = count;\n      } else {\n        cell.text = count - dateCountOfMonth;\n        cell.type = \"next-month\";\n      }\n      return true;\n    }\n    return false;\n  };\n  var _setCellMetadata = function setCellMetadata(cell, _ref2, count) {\n    var columnIndex = _ref2.columnIndex,\n      rowIndex = _ref2.rowIndex;\n    var disabledDate = props.disabledDate,\n      cellClassName = props.cellClassName;\n    var _selectedDate = unref(selectedDate);\n    var shouldIncrement = setDateText(cell, {\n      count,\n      rowIndex,\n      columnIndex\n    });\n    var cellDate = cell.dayjs.toDate();\n    cell.selected = _selectedDate.find(function (d) {\n      return d.isSame(cell.dayjs, \"day\");\n    });\n    cell.isSelected = !!cell.selected;\n    cell.isCurrent = isCurrent(cell);\n    cell.disabled = disabledDate == null ? void 0 : disabledDate(cellDate);\n    cell.customClass = cellClassName == null ? void 0 : cellClassName(cellDate);\n    return shouldIncrement;\n  };\n  var setRowMetadata = function setRowMetadata(row) {\n    if (props.selectionMode === \"week\") {\n      var _ref3 = props.showWeekNumber ? [1, 7] : [0, 6],\n        _ref4 = _slicedToArray(_ref3, 2),\n        start = _ref4[0],\n        end = _ref4[1];\n      var isActive = isWeekActive(row[start + 1]);\n      row[start].inRange = isActive;\n      row[start].start = isActive;\n      row[end].inRange = isActive;\n      row[end].end = isActive;\n    }\n  };\n  var rows = computed(function () {\n    var minDate = props.minDate,\n      maxDate = props.maxDate,\n      rangeState = props.rangeState,\n      showWeekNumber = props.showWeekNumber;\n    var offset = unref(offsetDay);\n    var rows_ = unref(tableRows);\n    var dateUnit = \"day\";\n    var count = 1;\n    if (showWeekNumber) {\n      for (var rowIndex = 0; rowIndex < 6; rowIndex++) {\n        if (!rows_[rowIndex][0]) {\n          rows_[rowIndex][0] = {\n            type: \"week\",\n            text: unref(startDate).add(rowIndex * 7 + 1, dateUnit).week()\n          };\n        }\n      }\n    }\n    buildPickerTable({\n      row: 6,\n      column: 7\n    }, rows_, {\n      startDate: minDate,\n      columnIndexOffset: showWeekNumber ? 1 : 0,\n      nextEndDate: rangeState.endDate || maxDate || rangeState.selecting && minDate || null,\n      now: dayjs().locale(unref(lang)).startOf(dateUnit),\n      unit: dateUnit,\n      relativeDateGetter: function relativeDateGetter(idx) {\n        return unref(startDate).add(idx - offset, dateUnit);\n      },\n      setCellMetadata: function setCellMetadata() {\n        for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n          args[_key] = arguments[_key];\n        }\n        if (_setCellMetadata.apply(void 0, args.concat([count]))) {\n          count += 1;\n        }\n      },\n      setRowMetadata\n    });\n    return rows_;\n  });\n  watch(function () {\n    return props.date;\n  }, /*#__PURE__*/_asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee() {\n    var _a;\n    return _regeneratorRuntime().wrap(function _callee$(_context) {\n      while (1) switch (_context.prev = _context.next) {\n        case 0:\n          if (!((_a = unref(tbodyRef)) == null ? void 0 : _a.contains(document.activeElement))) {\n            _context.next = 5;\n            break;\n          }\n          _context.next = 3;\n          return nextTick();\n        case 3:\n          _context.next = 5;\n          return focus();\n        case 5:\n        case \"end\":\n          return _context.stop();\n      }\n    }, _callee);\n  })));\n  var focus = /*#__PURE__*/function () {\n    var _ref6 = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee2() {\n      var _a;\n      return _regeneratorRuntime().wrap(function _callee2$(_context2) {\n        while (1) switch (_context2.prev = _context2.next) {\n          case 0:\n            return _context2.abrupt(\"return\", (_a = unref(currentCellRef)) == null ? void 0 : _a.focus());\n          case 1:\n          case \"end\":\n            return _context2.stop();\n        }\n      }, _callee2);\n    }));\n    return function focus() {\n      return _ref6.apply(this, arguments);\n    };\n  }();\n  var isCurrent = function isCurrent(cell) {\n    return props.selectionMode === \"date\" && isNormalDay(cell.type) && cellMatchesDate(cell, props.parsedValue);\n  };\n  var cellMatchesDate = function cellMatchesDate(cell, date) {\n    if (!date) return false;\n    return dayjs(date).locale(unref(lang)).isSame(props.date.date(Number(cell.text)), \"day\");\n  };\n  var getDateOfCell = function getDateOfCell(row, column) {\n    var offsetFromStart = row * 7 + (column - (props.showWeekNumber ? 1 : 0)) - unref(offsetDay);\n    return unref(startDate).add(offsetFromStart, \"day\");\n  };\n  var handleMouseMove = function handleMouseMove(event) {\n    var _a;\n    if (!props.rangeState.selecting) return;\n    var target = event.target;\n    if (target.tagName === \"SPAN\") {\n      target = (_a = target.parentNode) == null ? void 0 : _a.parentNode;\n    }\n    if (target.tagName === \"DIV\") {\n      target = target.parentNode;\n    }\n    if (target.tagName !== \"TD\") return;\n    var row = target.parentNode.rowIndex - 1;\n    var column = target.cellIndex;\n    if (unref(rows)[row][column].disabled) return;\n    if (row !== unref(lastRow) || column !== unref(lastColumn)) {\n      lastRow.value = row;\n      lastColumn.value = column;\n      emit(\"changerange\", {\n        selecting: true,\n        endDate: getDateOfCell(row, column)\n      });\n    }\n  };\n  var isSelectedCell = function isSelectedCell(cell) {\n    return !unref(hasCurrent) && (cell == null ? void 0 : cell.text) === 1 && cell.type === \"normal\" || cell.isCurrent;\n  };\n  var handleFocus = function handleFocus(event) {\n    if (focusWithClick || unref(hasCurrent) || props.selectionMode !== \"date\") return;\n    handlePickDate(event, true);\n  };\n  var handleMouseDown = function handleMouseDown(event) {\n    var target = event.target.closest(\"td\");\n    if (!target) return;\n    focusWithClick = true;\n  };\n  var handleMouseUp = function handleMouseUp(event) {\n    var target = event.target.closest(\"td\");\n    if (!target) return;\n    focusWithClick = false;\n  };\n  var handleRangePick = function handleRangePick(newDate) {\n    if (!props.rangeState.selecting || !props.minDate) {\n      emit(\"pick\", {\n        minDate: newDate,\n        maxDate: null\n      });\n      emit(\"select\", true);\n    } else {\n      if (newDate >= props.minDate) {\n        emit(\"pick\", {\n          minDate: props.minDate,\n          maxDate: newDate\n        });\n      } else {\n        emit(\"pick\", {\n          minDate: newDate,\n          maxDate: props.minDate\n        });\n      }\n      emit(\"select\", false);\n    }\n  };\n  var handleWeekPick = function handleWeekPick(newDate) {\n    var weekNumber = newDate.week();\n    var value = `${newDate.year()}w${weekNumber}`;\n    emit(\"pick\", {\n      year: newDate.year(),\n      week: weekNumber,\n      value,\n      date: newDate.startOf(\"week\")\n    });\n  };\n  var handleDatesPick = function handleDatesPick(newDate, selected) {\n    var newValue = selected ? castArray(props.parsedValue).filter(function (d) {\n      return (d == null ? void 0 : d.valueOf()) !== newDate.valueOf();\n    }) : castArray(props.parsedValue).concat([newDate]);\n    emit(\"pick\", newValue);\n  };\n  var handlePickDate = function handlePickDate(event) {\n    var isKeyboardMovement = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;\n    var target = event.target.closest(\"td\");\n    if (!target) return;\n    var row = target.parentNode.rowIndex - 1;\n    var column = target.cellIndex;\n    var cell = unref(rows)[row][column];\n    if (cell.disabled || cell.type === \"week\") return;\n    var newDate = getDateOfCell(row, column);\n    switch (props.selectionMode) {\n      case \"range\":\n        {\n          handleRangePick(newDate);\n          break;\n        }\n      case \"date\":\n        {\n          emit(\"pick\", newDate, isKeyboardMovement);\n          break;\n        }\n      case \"week\":\n        {\n          handleWeekPick(newDate);\n          break;\n        }\n      case \"dates\":\n        {\n          handleDatesPick(newDate, !!cell.selected);\n          break;\n        }\n      default:\n        {\n          break;\n        }\n    }\n  };\n  var isWeekActive = function isWeekActive(cell) {\n    if (props.selectionMode !== \"week\") return false;\n    var newDate = props.date.startOf(\"day\");\n    if (cell.type === \"prev-month\") {\n      newDate = newDate.subtract(1, \"month\");\n    }\n    if (cell.type === \"next-month\") {\n      newDate = newDate.add(1, \"month\");\n    }\n    newDate = newDate.date(Number.parseInt(cell.text, 10));\n    if (props.parsedValue && !Array.isArray(props.parsedValue)) {\n      var dayOffset = (props.parsedValue.day() - firstDayOfWeek + 7) % 7 - 1;\n      var weekDate = props.parsedValue.subtract(dayOffset, \"day\");\n      return weekDate.isSame(newDate, \"day\");\n    }\n    return false;\n  };\n  return {\n    WEEKS,\n    rows,\n    tbodyRef,\n    currentCellRef,\n    focus,\n    isCurrent,\n    isWeekActive,\n    isSelectedCell,\n    handlePickDate,\n    handleMouseUp,\n    handleMouseDown,\n    handleMouseMove,\n    handleFocus\n  };\n};\nvar useBasicDateTableDOM = function useBasicDateTableDOM(props, _ref7) {\n  var isCurrent = _ref7.isCurrent,\n    isWeekActive = _ref7.isWeekActive;\n  var ns = useNamespace(\"date-table\");\n  var _useLocale2 = useLocale(),\n    t = _useLocale2.t;\n  var tableKls = computed(function () {\n    return [ns.b(), {\n      \"is-week-mode\": props.selectionMode === \"week\"\n    }];\n  });\n  var tableLabel = computed(function () {\n    return t(\"el.datepicker.dateTablePrompt\");\n  });\n  var weekLabel = computed(function () {\n    return t(\"el.datepicker.week\");\n  });\n  var getCellClasses = function getCellClasses(cell) {\n    var classes = [];\n    if (isNormalDay(cell.type) && !cell.disabled) {\n      classes.push(\"available\");\n      if (cell.type === \"today\") {\n        classes.push(\"today\");\n      }\n    } else {\n      classes.push(cell.type);\n    }\n    if (isCurrent(cell)) {\n      classes.push(\"current\");\n    }\n    if (cell.inRange && (isNormalDay(cell.type) || props.selectionMode === \"week\")) {\n      classes.push(\"in-range\");\n      if (cell.start) {\n        classes.push(\"start-date\");\n      }\n      if (cell.end) {\n        classes.push(\"end-date\");\n      }\n    }\n    if (cell.disabled) {\n      classes.push(\"disabled\");\n    }\n    if (cell.selected) {\n      classes.push(\"selected\");\n    }\n    if (cell.customClass) {\n      classes.push(cell.customClass);\n    }\n    return classes.join(\" \");\n  };\n  var getRowKls = function getRowKls(cell) {\n    return [ns.e(\"row\"), {\n      current: isWeekActive(cell)\n    }];\n  };\n  return {\n    tableKls,\n    tableLabel,\n    weekLabel,\n    getCellClasses,\n    getRowKls,\n    t\n  };\n};\nexport { useBasicDateTable, useBasicDateTableDOM };", "map": {"version": 3, "names": ["isNormalDay", "type", "arguments", "length", "undefined", "includes", "useBasicDateTable", "props", "emit", "_useLocale", "useLocale", "lang", "tbodyRef", "ref", "currentCellRef", "lastRow", "lastColumn", "tableRows", "focusWithClick", "firstDayOfWeek", "date", "$locale", "weekStart", "WEEKS_CONSTANT", "locale", "localeData", "weekdaysShort", "map", "_", "toLowerCase", "offsetDay", "computed", "startDate", "startDayOfMonth", "startOf", "subtract", "day", "WEEKS", "concat", "slice", "has<PERSON><PERSON>rent", "flatten", "unref", "rows", "some", "row", "isCurrent", "days", "startOfMonth", "startOfMonthDay", "dateCountOfMonth", "daysInMonth", "dateCountOfLastMonth", "selectedDate", "selectionMode", "<PERSON><PERSON><PERSON><PERSON>", "parsedValue", "setDateText", "cell", "_ref", "count", "rowIndex", "columnIndex", "_unref", "offset", "numberOfDaysFromPreviousMonth", "text", "setCellMetadata", "_ref2", "disabledDate", "cellClassName", "_selectedDate", "shouldIncrement", "cellDate", "dayjs", "toDate", "selected", "find", "d", "isSame", "isSelected", "disabled", "customClass", "setRowMetadata", "_ref3", "showWeekNumber", "_ref4", "_slicedToArray", "start", "end", "isActive", "isWeekActive", "inRange", "minDate", "maxDate", "rangeState", "rows_", "dateUnit", "add", "week", "buildPickerTable", "column", "columnIndexOffset", "nextEndDate", "endDate", "selecting", "now", "unit", "relativeDateGetter", "idx", "_len", "args", "Array", "_key", "apply", "watch", "_asyncToGenerator", "_regeneratorRuntime", "mark", "_callee", "_a", "wrap", "_callee$", "_context", "prev", "next", "contains", "document", "activeElement", "nextTick", "focus", "stop", "_ref6", "_callee2", "_callee2$", "_context2", "abrupt", "cellMatchesDate", "Number", "getDateOfCell", "offsetFromStart", "handleMouseMove", "event", "target", "tagName", "parentNode", "cellIndex", "value", "isSelectedCell", "handleFocus", "handlePickDate", "handleMouseDown", "closest", "handleMouseUp", "handleRangePick", "newDate", "handleWeekPick", "weekNumber", "year", "handleDatesPick", "newValue", "filter", "valueOf", "isKeyboardMovement", "parseInt", "isArray", "dayOffset", "weekDate", "useBasicDateTableDOM", "_ref7", "ns", "useNamespace", "_useLocale2", "t", "tableKls", "b", "tableLabel", "week<PERSON><PERSON><PERSON>", "getCellClasses", "classes", "push", "join", "getRowKls", "e", "current"], "sources": ["../../../../../../../packages/components/date-picker/src/composables/use-basic-date-table.ts"], "sourcesContent": ["import { computed, nextTick, ref, unref, watch } from 'vue'\nimport dayjs from 'dayjs'\nimport { flatten } from 'lodash-unified'\nimport { useLocale, useNamespace } from '@element-plus/hooks'\nimport { castArray } from '@element-plus/utils'\nimport { buildPickerTable } from '../utils'\n\nimport type { SetupContext } from 'vue'\nimport type { Dayjs } from 'dayjs'\nimport type { DateCell } from '../date-picker.type'\nimport type {\n  BasicDateTableEmits,\n  BasicDateTableProps,\n} from '../props/basic-date-table'\n\nconst isNormalDay = (type = '') => {\n  return ['normal', 'today'].includes(type)\n}\n\nexport const useBasicDateTable = (\n  props: BasicDateTableProps,\n  emit: SetupContext<BasicDateTableEmits>['emit']\n) => {\n  const { lang } = useLocale()\n  const tbodyRef = ref<HTMLElement>()\n  const currentCellRef = ref<HTMLElement>()\n  // data\n  const lastRow = ref<number>()\n  const lastColumn = ref<number>()\n  const tableRows = ref<DateCell[][]>([[], [], [], [], [], []])\n\n  let focusWithClick = false\n\n  // todo better way to get Day.js locale object\n  const firstDayOfWeek = (props.date as any).$locale().weekStart || 7\n  const WEEKS_CONSTANT = props.date\n    .locale('en')\n    .localeData()\n    .weekdaysShort()\n    .map((_) => _.toLowerCase())\n\n  const offsetDay = computed(() => {\n    // Sunday 7(0), cal the left and right offset days, 3217654, such as Monday is -1, the is to adjust the position of the first two rows of dates\n    return firstDayOfWeek > 3 ? 7 - firstDayOfWeek : -firstDayOfWeek\n  })\n\n  const startDate = computed(() => {\n    const startDayOfMonth = props.date.startOf('month')\n    return startDayOfMonth.subtract(startDayOfMonth.day() || 7, 'day')\n  })\n\n  const WEEKS = computed(() => {\n    return WEEKS_CONSTANT.concat(WEEKS_CONSTANT).slice(\n      firstDayOfWeek,\n      firstDayOfWeek + 7\n    )\n  })\n\n  const hasCurrent = computed<boolean>(() => {\n    return flatten(unref(rows)).some((row) => {\n      return row.isCurrent\n    })\n  })\n\n  const days = computed(() => {\n    const startOfMonth = props.date.startOf('month')\n    const startOfMonthDay = startOfMonth.day() || 7 // day of first day\n    const dateCountOfMonth = startOfMonth.daysInMonth()\n\n    const dateCountOfLastMonth = startOfMonth.subtract(1, 'month').daysInMonth()\n\n    return {\n      startOfMonthDay,\n      dateCountOfMonth,\n      dateCountOfLastMonth,\n    }\n  })\n\n  const selectedDate = computed(() => {\n    return props.selectionMode === 'dates'\n      ? (castArray(props.parsedValue) as Dayjs[])\n      : ([] as Dayjs[])\n  })\n\n  // Return value indicates should the counter be incremented\n  type CellCoordinate = { columnIndex: number; rowIndex: number }\n  type CellMeta = CellCoordinate & {\n    count: number\n  }\n  const setDateText = (\n    cell: DateCell,\n    { count, rowIndex, columnIndex }: CellMeta\n  ): boolean => {\n    const { startOfMonthDay, dateCountOfMonth, dateCountOfLastMonth } =\n      unref(days)\n    const offset = unref(offsetDay)\n    if (rowIndex >= 0 && rowIndex <= 1) {\n      const numberOfDaysFromPreviousMonth =\n        startOfMonthDay + offset < 0\n          ? 7 + startOfMonthDay + offset\n          : startOfMonthDay + offset\n\n      if (columnIndex + rowIndex * 7 >= numberOfDaysFromPreviousMonth) {\n        cell.text = count\n        return true\n      } else {\n        cell.text =\n          dateCountOfLastMonth -\n          (numberOfDaysFromPreviousMonth - (columnIndex % 7)) +\n          1 +\n          rowIndex * 7\n        cell.type = 'prev-month'\n      }\n    } else {\n      if (count <= dateCountOfMonth) {\n        cell.text = count\n      } else {\n        cell.text = count - dateCountOfMonth\n        cell.type = 'next-month'\n      }\n      return true\n    }\n    return false\n  }\n\n  const setCellMetadata = (\n    cell: DateCell,\n    { columnIndex, rowIndex }: CellCoordinate,\n    count: number\n  ) => {\n    const { disabledDate, cellClassName } = props\n    const _selectedDate = unref(selectedDate)\n    const shouldIncrement = setDateText(cell, { count, rowIndex, columnIndex })\n\n    const cellDate = cell.dayjs!.toDate()\n    cell.selected = _selectedDate.find((d) => d.isSame(cell.dayjs, 'day'))\n    cell.isSelected = !!cell.selected\n    cell.isCurrent = isCurrent(cell)\n    cell.disabled = disabledDate?.(cellDate)\n    cell.customClass = cellClassName?.(cellDate)\n    return shouldIncrement\n  }\n\n  const setRowMetadata = (row: DateCell[]) => {\n    if (props.selectionMode === 'week') {\n      const [start, end] = props.showWeekNumber ? [1, 7] : [0, 6]\n      const isActive = isWeekActive(row[start + 1])\n      row[start].inRange = isActive\n      row[start].start = isActive\n      row[end].inRange = isActive\n      row[end].end = isActive\n    }\n  }\n\n  const rows = computed(() => {\n    const { minDate, maxDate, rangeState, showWeekNumber } = props\n\n    const offset = unref(offsetDay)\n    const rows_ = unref(tableRows)\n    const dateUnit = 'day'\n    let count = 1\n\n    if (showWeekNumber) {\n      for (let rowIndex = 0; rowIndex < 6; rowIndex++) {\n        if (!rows_[rowIndex][0]) {\n          rows_[rowIndex][0] = {\n            type: 'week',\n            text: unref(startDate)\n              .add(rowIndex * 7 + 1, dateUnit)\n              .week(),\n          }\n        }\n      }\n    }\n\n    buildPickerTable({ row: 6, column: 7 }, rows_, {\n      startDate: minDate,\n      columnIndexOffset: showWeekNumber ? 1 : 0,\n      nextEndDate:\n        rangeState.endDate ||\n        maxDate ||\n        (rangeState.selecting && minDate) ||\n        null,\n      now: dayjs().locale(unref(lang)).startOf(dateUnit),\n      unit: dateUnit,\n      relativeDateGetter: (idx: number) =>\n        unref(startDate).add(idx - offset, dateUnit),\n      setCellMetadata: (...args) => {\n        if (setCellMetadata(...args, count)) {\n          count += 1\n        }\n      },\n\n      setRowMetadata,\n    })\n\n    return rows_\n  })\n\n  watch(\n    () => props.date,\n    async () => {\n      if (unref(tbodyRef)?.contains(document.activeElement)) {\n        await nextTick()\n        await focus()\n        // currentCellRef.value?.focus()\n      }\n    }\n  )\n\n  const focus = async () => unref(currentCellRef)?.focus()\n\n  const isCurrent = (cell: DateCell): boolean => {\n    return (\n      props.selectionMode === 'date' &&\n      isNormalDay(cell.type) &&\n      cellMatchesDate(cell, props.parsedValue as Dayjs)\n    )\n  }\n\n  const cellMatchesDate = (cell: DateCell, date: Dayjs) => {\n    if (!date) return false\n    return dayjs(date)\n      .locale(unref(lang))\n      .isSame(props.date.date(Number(cell.text)), 'day')\n  }\n\n  const getDateOfCell = (row: number, column: number) => {\n    const offsetFromStart =\n      row * 7 + (column - (props.showWeekNumber ? 1 : 0)) - unref(offsetDay)\n    return unref(startDate).add(offsetFromStart, 'day')\n  }\n\n  const handleMouseMove = (event: MouseEvent) => {\n    if (!props.rangeState.selecting) return\n\n    let target = event.target as HTMLElement\n    if (target.tagName === 'SPAN') {\n      target = target.parentNode?.parentNode as HTMLElement\n    }\n    if (target.tagName === 'DIV') {\n      target = target.parentNode as HTMLElement\n    }\n    if (target.tagName !== 'TD') return\n\n    const row = (target.parentNode as HTMLTableRowElement).rowIndex - 1\n    const column = (target as HTMLTableCellElement).cellIndex\n\n    // can not select disabled date\n    if (unref(rows)[row][column].disabled) return\n\n    // only update rangeState when mouse moves to a new cell\n    // this avoids frequent Date object creation and improves performance\n    if (row !== unref(lastRow) || column !== unref(lastColumn)) {\n      lastRow.value = row\n      lastColumn.value = column\n      emit('changerange', {\n        selecting: true,\n        endDate: getDateOfCell(row, column),\n      })\n    }\n  }\n\n  const isSelectedCell = (cell: DateCell) => {\n    return (\n      (!unref(hasCurrent) && cell?.text === 1 && cell.type === 'normal') ||\n      cell.isCurrent\n    )\n  }\n\n  const handleFocus = (event: FocusEvent) => {\n    if (focusWithClick || unref(hasCurrent) || props.selectionMode !== 'date')\n      return\n    handlePickDate(event, true)\n  }\n\n  const handleMouseDown = (event: MouseEvent) => {\n    const target = (event.target as HTMLElement).closest('td')\n    if (!target) return\n    focusWithClick = true\n  }\n\n  const handleMouseUp = (event: MouseEvent) => {\n    const target = (event.target as HTMLElement).closest('td')\n    if (!target) return\n    focusWithClick = false\n  }\n\n  const handleRangePick = (newDate: Dayjs) => {\n    if (!props.rangeState.selecting || !props.minDate) {\n      emit('pick', { minDate: newDate, maxDate: null })\n      emit('select', true)\n    } else {\n      if (newDate >= props.minDate) {\n        emit('pick', { minDate: props.minDate, maxDate: newDate })\n      } else {\n        emit('pick', { minDate: newDate, maxDate: props.minDate })\n      }\n      emit('select', false)\n    }\n  }\n\n  const handleWeekPick = (newDate: Dayjs) => {\n    const weekNumber = newDate.week()\n    const value = `${newDate.year()}w${weekNumber}`\n    emit('pick', {\n      year: newDate.year(),\n      week: weekNumber,\n      value,\n      date: newDate.startOf('week'),\n    })\n  }\n\n  const handleDatesPick = (newDate: Dayjs, selected: boolean) => {\n    const newValue = selected\n      ? castArray(props.parsedValue).filter(\n          (d) => d?.valueOf() !== newDate.valueOf()\n        )\n      : castArray(props.parsedValue).concat([newDate])\n    emit('pick', newValue)\n  }\n\n  const handlePickDate = (\n    event: FocusEvent | MouseEvent,\n    isKeyboardMovement = false\n  ) => {\n    const target = (event.target as HTMLElement).closest('td')\n\n    if (!target) return\n\n    const row = (target.parentNode as HTMLTableRowElement).rowIndex - 1\n    const column = (target as HTMLTableCellElement).cellIndex\n    const cell = unref(rows)[row][column]\n\n    if (cell.disabled || cell.type === 'week') return\n\n    const newDate = getDateOfCell(row, column)\n\n    switch (props.selectionMode) {\n      case 'range': {\n        handleRangePick(newDate)\n        break\n      }\n      case 'date': {\n        emit('pick', newDate, isKeyboardMovement)\n        break\n      }\n      case 'week': {\n        handleWeekPick(newDate)\n        break\n      }\n      case 'dates': {\n        handleDatesPick(newDate, !!cell.selected)\n        break\n      }\n      default: {\n        break\n      }\n    }\n  }\n\n  const isWeekActive = (cell: DateCell) => {\n    if (props.selectionMode !== 'week') return false\n    let newDate = props.date.startOf('day')\n\n    if (cell.type === 'prev-month') {\n      newDate = newDate.subtract(1, 'month')\n    }\n\n    if (cell.type === 'next-month') {\n      newDate = newDate.add(1, 'month')\n    }\n\n    newDate = newDate.date(Number.parseInt(cell.text as any, 10))\n\n    if (props.parsedValue && !Array.isArray(props.parsedValue)) {\n      const dayOffset = ((props.parsedValue.day() - firstDayOfWeek + 7) % 7) - 1\n      const weekDate = props.parsedValue.subtract(dayOffset, 'day')\n      return weekDate.isSame(newDate, 'day')\n    }\n    return false\n  }\n\n  return {\n    WEEKS,\n    rows,\n    tbodyRef,\n    currentCellRef,\n\n    // cellMatchesDate,\n    // getDateOfCell,\n    focus,\n    isCurrent,\n    isWeekActive,\n    isSelectedCell,\n\n    handlePickDate,\n    handleMouseUp,\n    handleMouseDown,\n    handleMouseMove,\n    handleFocus,\n  }\n}\n\nexport const useBasicDateTableDOM = (\n  props: BasicDateTableProps,\n  {\n    isCurrent,\n    isWeekActive,\n  }: Pick<ReturnType<typeof useBasicDateTable>, 'isCurrent' | 'isWeekActive'>\n) => {\n  const ns = useNamespace('date-table')\n  const { t } = useLocale()\n\n  const tableKls = computed(() => [\n    ns.b(),\n    { 'is-week-mode': props.selectionMode === 'week' },\n  ])\n\n  const tableLabel = computed(() => t('el.datepicker.dateTablePrompt'))\n  const weekLabel = computed(() => t('el.datepicker.week'))\n\n  const getCellClasses = (cell: DateCell) => {\n    const classes: string[] = []\n    if (isNormalDay(cell.type) && !cell.disabled) {\n      classes.push('available')\n      if (cell.type === 'today') {\n        classes.push('today')\n      }\n    } else {\n      classes.push(cell.type!)\n    }\n\n    if (isCurrent(cell)) {\n      classes.push('current')\n    }\n\n    if (\n      cell.inRange &&\n      (isNormalDay(cell.type) || props.selectionMode === 'week')\n    ) {\n      classes.push('in-range')\n\n      if (cell.start) {\n        classes.push('start-date')\n      }\n\n      if (cell.end) {\n        classes.push('end-date')\n      }\n    }\n\n    if (cell.disabled) {\n      classes.push('disabled')\n    }\n\n    if (cell.selected) {\n      classes.push('selected')\n    }\n\n    if (cell.customClass) {\n      classes.push(cell.customClass)\n    }\n\n    return classes.join(' ')\n  }\n\n  const getRowKls = (cell: DateCell) => [\n    ns.e('row'),\n    { current: isWeekActive(cell) },\n  ]\n\n  return {\n    tableKls,\n    tableLabel,\n    weekLabel,\n\n    getCellClasses,\n    getRowKls,\n    t,\n  }\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAMA,IAAMA,WAAW,GAAG,SAAdA,WAAWA,CAAA,EAAkB;EAAA,IAAdC,IAAI,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,EAAE;EAC5B,OAAO,CAAC,QAAQ,EAAE,OAAO,CAAC,CAACG,QAAQ,CAACJ,IAAI,CAAC;AAC3C,CAAC;AACW,IAACK,iBAAiB,GAAG,SAApBA,iBAAiBA,CAAIC,KAAK,EAAEC,IAAI,EAAK;EAChD,IAAAC,UAAA,GAAiBC,SAAS,EAAE;IAApBC,IAAI,GAAAF,UAAA,CAAJE,IAAI;EACZ,IAAMC,QAAQ,GAAGC,GAAG,EAAE;EACtB,IAAMC,cAAc,GAAGD,GAAG,EAAE;EAC5B,IAAME,OAAO,GAAGF,GAAG,EAAE;EACrB,IAAMG,UAAU,GAAGH,GAAG,EAAE;EACxB,IAAMI,SAAS,GAAGJ,GAAG,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC;EAC/C,IAAIK,cAAc,GAAG,KAAK;EAC1B,IAAMC,cAAc,GAAGZ,KAAK,CAACa,IAAI,CAACC,OAAO,EAAE,CAACC,SAAS,IAAI,CAAC;EAC1D,IAAMC,cAAc,GAAGhB,KAAK,CAACa,IAAI,CAACI,MAAM,CAAC,IAAI,CAAC,CAACC,UAAU,EAAE,CAACC,aAAa,EAAE,CAACC,GAAG,CAAC,UAACC,CAAC;IAAA,OAAKA,CAAC,CAACC,WAAW,EAAE;EAAA,EAAC;EACvG,IAAMC,SAAS,GAAGC,QAAQ,CAAC,YAAM;IAC/B,OAAOZ,cAAc,GAAG,CAAC,GAAG,CAAC,GAAGA,cAAc,GAAG,CAACA,cAAc;EACpE,CAAG,CAAC;EACF,IAAMa,SAAS,GAAGD,QAAQ,CAAC,YAAM;IAC/B,IAAME,eAAe,GAAG1B,KAAK,CAACa,IAAI,CAACc,OAAO,CAAC,OAAO,CAAC;IACnD,OAAOD,eAAe,CAACE,QAAQ,CAACF,eAAe,CAACG,GAAG,EAAE,IAAI,CAAC,EAAE,KAAK,CAAC;EACtE,CAAG,CAAC;EACF,IAAMC,KAAK,GAAGN,QAAQ,CAAC,YAAM;IAC3B,OAAOR,cAAc,CAACe,MAAM,CAACf,cAAc,CAAC,CAACgB,KAAK,CAACpB,cAAc,EAAEA,cAAc,GAAG,CAAC,CAAC;EAC1F,CAAG,CAAC;EACF,IAAMqB,UAAU,GAAGT,QAAQ,CAAC,YAAM;IAChC,OAAOU,OAAO,CAACC,KAAK,CAACC,IAAI,CAAC,CAAC,CAACC,IAAI,CAAC,UAACC,GAAG,EAAK;MACxC,OAAOA,GAAG,CAACC,SAAS;IAC1B,CAAK,CAAC;EACN,CAAG,CAAC;EACF,IAAMC,IAAI,GAAGhB,QAAQ,CAAC,YAAM;IAC1B,IAAMiB,YAAY,GAAGzC,KAAK,CAACa,IAAI,CAACc,OAAO,CAAC,OAAO,CAAC;IAChD,IAAMe,eAAe,GAAGD,YAAY,CAACZ,GAAG,EAAE,IAAI,CAAC;IAC/C,IAAMc,gBAAgB,GAAGF,YAAY,CAACG,WAAW,EAAE;IACnD,IAAMC,oBAAoB,GAAGJ,YAAY,CAACb,QAAQ,CAAC,CAAC,EAAE,OAAO,CAAC,CAACgB,WAAW,EAAE;IAC5E,OAAO;MACLF,eAAe;MACfC,gBAAgB;MAChBE;IACN,CAAK;EACL,CAAG,CAAC;EACF,IAAMC,YAAY,GAAGtB,QAAQ,CAAC,YAAM;IAClC,OAAOxB,KAAK,CAAC+C,aAAa,KAAK,OAAO,GAAGC,SAAS,CAAChD,KAAK,CAACiD,WAAW,CAAC,GAAG,EAAE;EAC9E,CAAG,CAAC;EACF,IAAMC,WAAW,GAAG,SAAdA,WAAWA,CAAIC,IAAI,EAAAC,IAAA,EAAuC;IAAA,IAAnCC,KAAK,GAAAD,IAAA,CAALC,KAAK;MAAEC,QAAQ,GAAAF,IAAA,CAARE,QAAQ;MAAEC,WAAW,GAAAH,IAAA,CAAXG,WAAW;IACvD,IAAAC,MAAA,GAAoErB,KAAK,CAACK,IAAI,CAAC;MAAvEE,eAAe,GAAAc,MAAA,CAAfd,eAAe;MAAEC,gBAAgB,GAAAa,MAAA,CAAhBb,gBAAgB;MAAEE,oBAAoB,GAAAW,MAAA,CAApBX,oBAAoB;IAC/D,IAAMY,MAAM,GAAGtB,KAAK,CAACZ,SAAS,CAAC;IAC/B,IAAI+B,QAAQ,IAAI,CAAC,IAAIA,QAAQ,IAAI,CAAC,EAAE;MAClC,IAAMI,6BAA6B,GAAGhB,eAAe,GAAGe,MAAM,GAAG,CAAC,GAAG,CAAC,GAAGf,eAAe,GAAGe,MAAM,GAAGf,eAAe,GAAGe,MAAM;MAC5H,IAAIF,WAAW,GAAGD,QAAQ,GAAG,CAAC,IAAII,6BAA6B,EAAE;QAC/DP,IAAI,CAACQ,IAAI,GAAGN,KAAK;QACjB,OAAO,IAAI;MACnB,CAAO,MAAM;QACLF,IAAI,CAACQ,IAAI,GAAGd,oBAAoB,IAAIa,6BAA6B,GAAGH,WAAW,GAAG,CAAC,CAAC,GAAG,CAAC,GAAGD,QAAQ,GAAG,CAAC;QACvGH,IAAI,CAACzD,IAAI,GAAG,YAAY;MAChC;IACA,CAAK,MAAM;MACL,IAAI2D,KAAK,IAAIV,gBAAgB,EAAE;QAC7BQ,IAAI,CAACQ,IAAI,GAAGN,KAAK;MACzB,CAAO,MAAM;QACLF,IAAI,CAACQ,IAAI,GAAGN,KAAK,GAAGV,gBAAgB;QACpCQ,IAAI,CAACzD,IAAI,GAAG,YAAY;MAChC;MACM,OAAO,IAAI;IACjB;IACI,OAAO,KAAK;EAChB,CAAG;EACD,IAAMkE,gBAAe,GAAG,SAAlBA,eAAeA,CAAIT,IAAI,EAAAU,KAAA,EAA6BR,KAAK,EAAK;IAAA,IAAnCE,WAAW,GAAAM,KAAA,CAAXN,WAAW;MAAED,QAAQ,GAAAO,KAAA,CAARP,QAAQ;IACpD,IAAQQ,YAAY,GAAoB9D,KAAK,CAArC8D,YAAY;MAAEC,aAAa,GAAK/D,KAAK,CAAvB+D,aAAa;IACnC,IAAMC,aAAa,GAAG7B,KAAK,CAACW,YAAY,CAAC;IACzC,IAAMmB,eAAe,GAAGf,WAAW,CAACC,IAAI,EAAE;MAAEE,KAAK;MAAEC,QAAQ;MAAEC;IAAW,CAAE,CAAC;IAC3E,IAAMW,QAAQ,GAAGf,IAAI,CAACgB,KAAK,CAACC,MAAM,EAAE;IACpCjB,IAAI,CAACkB,QAAQ,GAAGL,aAAa,CAACM,IAAI,CAAC,UAACC,CAAC;MAAA,OAAKA,CAAC,CAACC,MAAM,CAACrB,IAAI,CAACgB,KAAK,EAAE,KAAK,CAAC;IAAA,EAAC;IACtEhB,IAAI,CAACsB,UAAU,GAAG,CAAC,CAACtB,IAAI,CAACkB,QAAQ;IACjClB,IAAI,CAACZ,SAAS,GAAGA,SAAS,CAACY,IAAI,CAAC;IAChCA,IAAI,CAACuB,QAAQ,GAAGZ,YAAY,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,YAAY,CAACI,QAAQ,CAAC;IACtEf,IAAI,CAACwB,WAAW,GAAGZ,aAAa,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,aAAa,CAACG,QAAQ,CAAC;IAC3E,OAAOD,eAAe;EAC1B,CAAG;EACD,IAAMW,cAAc,GAAG,SAAjBA,cAAcA,CAAItC,GAAG,EAAK;IAC9B,IAAItC,KAAK,CAAC+C,aAAa,KAAK,MAAM,EAAE;MAClC,IAAA8B,KAAA,GAAqB7E,KAAK,CAAC8E,cAAc,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC;QAAAC,KAAA,GAAAC,cAAA,CAAAH,KAAA;QAApDI,KAAK,GAAAF,KAAA;QAAEG,GAAG,GAAAH,KAAA;MACjB,IAAMI,QAAQ,GAAGC,YAAY,CAAC9C,GAAG,CAAC2C,KAAK,GAAG,CAAC,CAAC,CAAC;MAC7C3C,GAAG,CAAC2C,KAAK,CAAC,CAACI,OAAO,GAAGF,QAAQ;MAC7B7C,GAAG,CAAC2C,KAAK,CAAC,CAACA,KAAK,GAAGE,QAAQ;MAC3B7C,GAAG,CAAC4C,GAAG,CAAC,CAACG,OAAO,GAAGF,QAAQ;MAC3B7C,GAAG,CAAC4C,GAAG,CAAC,CAACA,GAAG,GAAGC,QAAQ;IAC7B;EACA,CAAG;EACD,IAAM/C,IAAI,GAAGZ,QAAQ,CAAC,YAAM;IAC1B,IAAQ8D,OAAO,GAA0CtF,KAAK,CAAtDsF,OAAO;MAAEC,OAAO,GAAiCvF,KAAK,CAA7CuF,OAAO;MAAEC,UAAU,GAAqBxF,KAAK,CAApCwF,UAAU;MAAEV,cAAc,GAAK9E,KAAK,CAAxB8E,cAAc;IACpD,IAAMrB,MAAM,GAAGtB,KAAK,CAACZ,SAAS,CAAC;IAC/B,IAAMkE,KAAK,GAAGtD,KAAK,CAACzB,SAAS,CAAC;IAC9B,IAAMgF,QAAQ,GAAG,KAAK;IACtB,IAAIrC,KAAK,GAAG,CAAC;IACb,IAAIyB,cAAc,EAAE;MAClB,KAAK,IAAIxB,QAAQ,GAAG,CAAC,EAAEA,QAAQ,GAAG,CAAC,EAAEA,QAAQ,EAAE,EAAE;QAC/C,IAAI,CAACmC,KAAK,CAACnC,QAAQ,CAAC,CAAC,CAAC,CAAC,EAAE;UACvBmC,KAAK,CAACnC,QAAQ,CAAC,CAAC,CAAC,CAAC,GAAG;YACnB5D,IAAI,EAAE,MAAM;YACZiE,IAAI,EAAExB,KAAK,CAACV,SAAS,CAAC,CAACkE,GAAG,CAACrC,QAAQ,GAAG,CAAC,GAAG,CAAC,EAAEoC,QAAQ,CAAC,CAACE,IAAI;UACvE,CAAW;QACX;MACA;IACA;IACIC,gBAAgB,CAAC;MAAEvD,GAAG,EAAE,CAAC;MAAEwD,MAAM,EAAE;IAAC,CAAE,EAAEL,KAAK,EAAE;MAC7ChE,SAAS,EAAE6D,OAAO;MAClBS,iBAAiB,EAAEjB,cAAc,GAAG,CAAC,GAAG,CAAC;MACzCkB,WAAW,EAAER,UAAU,CAACS,OAAO,IAAIV,OAAO,IAAIC,UAAU,CAACU,SAAS,IAAIZ,OAAO,IAAI,IAAI;MACrFa,GAAG,EAAEhC,KAAK,EAAE,CAAClD,MAAM,CAACkB,KAAK,CAAC/B,IAAI,CAAC,CAAC,CAACuB,OAAO,CAAC+D,QAAQ,CAAC;MAClDU,IAAI,EAAEV,QAAQ;MACdW,kBAAkB,EAAE,SAApBA,kBAAkBA,CAAGC,GAAG;QAAA,OAAKnE,KAAK,CAACV,SAAS,CAAC,CAACkE,GAAG,CAACW,GAAG,GAAG7C,MAAM,EAAEiC,QAAQ,CAAC;MAAA;MACzE9B,eAAe,EAAE,SAAjBA,eAAeA,CAAA,EAAe;QAAA,SAAA2C,IAAA,GAAA5G,SAAA,CAAAC,MAAA,EAAT4G,IAAI,OAAAC,KAAA,CAAAF,IAAA,GAAAG,IAAA,MAAAA,IAAA,GAAAH,IAAA,EAAAG,IAAA;UAAJF,IAAI,CAAAE,IAAA,IAAA/G,SAAA,CAAA+G,IAAA;QAAA;QACvB,IAAI9C,gBAAe,CAAA+C,KAAA,SAAIH,IAAI,CAAAzE,MAAA,EAAEsB,KAAK,GAAC,EAAE;UACnCA,KAAK,IAAI,CAAC;QACpB;MACA,CAAO;MACDuB;IACN,CAAK,CAAC;IACF,OAAOa,KAAK;EAChB,CAAG,CAAC;EACFmB,KAAK,CAAC;IAAA,OAAM5G,KAAK,CAACa,IAAI;EAAA,gBAAAgG,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,CAAE,SAAAC,QAAA;IAAA,IAAAC,EAAA;IAAA,OAAAH,mBAAA,GAAAI,IAAA,UAAAC,SAAAC,QAAA;MAAA,kBAAAA,QAAA,CAAAC,IAAA,GAAAD,QAAA,CAAAE,IAAA;QAAA;UAAA,MAElB,CAACL,EAAE,GAAG9E,KAAK,CAAC9B,QAAQ,CAAC,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG4G,EAAE,CAACM,QAAQ,CAACC,QAAQ,CAACC,aAAa,CAAC;YAAAL,QAAA,CAAAE,IAAA;YAAA;UAAA;UAAAF,QAAA,CAAAE,IAAA;UAAA,OACzEI,QAAQ,EAAE;QAAA;UAAAN,QAAA,CAAAE,IAAA;UAAA,OACVK,KAAK,EAAE;QAAA;QAAA;UAAA,OAAAP,QAAA,CAAAQ,IAAA;MAAA;IAAA,GAAAZ,OAAA;EAAA,CAEhB,GAAC;EACF,IAAMW,KAAK;IAAA,IAAAE,KAAA,GAAAhB,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,CAAG,SAAAe,SAAA;MAAA,IAAAb,EAAA;MAAA,OAAAH,mBAAA,GAAAI,IAAA,UAAAa,UAAAC,SAAA;QAAA,kBAAAA,SAAA,CAAAX,IAAA,GAAAW,SAAA,CAAAV,IAAA;UAAA;YAAA,OAAAU,SAAA,CAAAC,MAAA,WAEL,CAAChB,EAAE,GAAG9E,KAAK,CAAC5B,cAAc,CAAC,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG0G,EAAE,CAACU,KAAK,EAAE;UAAA;UAAA;YAAA,OAAAK,SAAA,CAAAJ,IAAA;QAAA;MAAA,GAAAE,QAAA;IAAA,CAClE;IAAA,gBAHKH,KAAKA,CAAA;MAAA,OAAAE,KAAA,CAAAlB,KAAA,OAAAhH,SAAA;IAAA;EAAA,GAGV;EACD,IAAM4C,SAAS,GAAG,SAAZA,SAASA,CAAIY,IAAI,EAAK;IAC1B,OAAOnD,KAAK,CAAC+C,aAAa,KAAK,MAAM,IAAItD,WAAW,CAAC0D,IAAI,CAACzD,IAAI,CAAC,IAAIwI,eAAe,CAAC/E,IAAI,EAAEnD,KAAK,CAACiD,WAAW,CAAC;EAC/G,CAAG;EACD,IAAMiF,eAAe,GAAG,SAAlBA,eAAeA,CAAI/E,IAAI,EAAEtC,IAAI,EAAK;IACtC,IAAI,CAACA,IAAI,EACP,OAAO,KAAK;IACd,OAAOsD,KAAK,CAACtD,IAAI,CAAC,CAACI,MAAM,CAACkB,KAAK,CAAC/B,IAAI,CAAC,CAAC,CAACoE,MAAM,CAACxE,KAAK,CAACa,IAAI,CAACA,IAAI,CAACsH,MAAM,CAAChF,IAAI,CAACQ,IAAI,CAAC,CAAC,EAAE,KAAK,CAAC;EAC5F,CAAG;EACD,IAAMyE,aAAa,GAAG,SAAhBA,aAAaA,CAAI9F,GAAG,EAAEwD,MAAM,EAAK;IACrC,IAAMuC,eAAe,GAAG/F,GAAG,GAAG,CAAC,IAAIwD,MAAM,IAAI9F,KAAK,CAAC8E,cAAc,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG3C,KAAK,CAACZ,SAAS,CAAC;IAC9F,OAAOY,KAAK,CAACV,SAAS,CAAC,CAACkE,GAAG,CAAC0C,eAAe,EAAE,KAAK,CAAC;EACvD,CAAG;EACD,IAAMC,eAAe,GAAG,SAAlBA,eAAeA,CAAIC,KAAK,EAAK;IACjC,IAAItB,EAAE;IACN,IAAI,CAACjH,KAAK,CAACwF,UAAU,CAACU,SAAS,EAC7B;IACF,IAAIsC,MAAM,GAAGD,KAAK,CAACC,MAAM;IACzB,IAAIA,MAAM,CAACC,OAAO,KAAK,MAAM,EAAE;MAC7BD,MAAM,GAAG,CAACvB,EAAE,GAAGuB,MAAM,CAACE,UAAU,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGzB,EAAE,CAACyB,UAAU;IACxE;IACI,IAAIF,MAAM,CAACC,OAAO,KAAK,KAAK,EAAE;MAC5BD,MAAM,GAAGA,MAAM,CAACE,UAAU;IAChC;IACI,IAAIF,MAAM,CAACC,OAAO,KAAK,IAAI,EACzB;IACF,IAAMnG,GAAG,GAAGkG,MAAM,CAACE,UAAU,CAACpF,QAAQ,GAAG,CAAC;IAC1C,IAAMwC,MAAM,GAAG0C,MAAM,CAACG,SAAS;IAC/B,IAAIxG,KAAK,CAACC,IAAI,CAAC,CAACE,GAAG,CAAC,CAACwD,MAAM,CAAC,CAACpB,QAAQ,EACnC;IACF,IAAIpC,GAAG,KAAKH,KAAK,CAAC3B,OAAO,CAAC,IAAIsF,MAAM,KAAK3D,KAAK,CAAC1B,UAAU,CAAC,EAAE;MAC1DD,OAAO,CAACoI,KAAK,GAAGtG,GAAG;MACnB7B,UAAU,CAACmI,KAAK,GAAG9C,MAAM;MACzB7F,IAAI,CAAC,aAAa,EAAE;QAClBiG,SAAS,EAAE,IAAI;QACfD,OAAO,EAAEmC,aAAa,CAAC9F,GAAG,EAAEwD,MAAM;MAC1C,CAAO,CAAC;IACR;EACA,CAAG;EACD,IAAM+C,cAAc,GAAG,SAAjBA,cAAcA,CAAI1F,IAAI,EAAK;IAC/B,OAAO,CAAChB,KAAK,CAACF,UAAU,CAAC,IAAI,CAACkB,IAAI,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,IAAI,CAACQ,IAAI,MAAM,CAAC,IAAIR,IAAI,CAACzD,IAAI,KAAK,QAAQ,IAAIyD,IAAI,CAACZ,SAAS;EACtH,CAAG;EACD,IAAMuG,WAAW,GAAG,SAAdA,WAAWA,CAAIP,KAAK,EAAK;IAC7B,IAAI5H,cAAc,IAAIwB,KAAK,CAACF,UAAU,CAAC,IAAIjC,KAAK,CAAC+C,aAAa,KAAK,MAAM,EACvE;IACFgG,cAAc,CAACR,KAAK,EAAE,IAAI,CAAC;EAC/B,CAAG;EACD,IAAMS,eAAe,GAAG,SAAlBA,eAAeA,CAAIT,KAAK,EAAK;IACjC,IAAMC,MAAM,GAAGD,KAAK,CAACC,MAAM,CAACS,OAAO,CAAC,IAAI,CAAC;IACzC,IAAI,CAACT,MAAM,EACT;IACF7H,cAAc,GAAG,IAAI;EACzB,CAAG;EACD,IAAMuI,aAAa,GAAG,SAAhBA,aAAaA,CAAIX,KAAK,EAAK;IAC/B,IAAMC,MAAM,GAAGD,KAAK,CAACC,MAAM,CAACS,OAAO,CAAC,IAAI,CAAC;IACzC,IAAI,CAACT,MAAM,EACT;IACF7H,cAAc,GAAG,KAAK;EAC1B,CAAG;EACD,IAAMwI,eAAe,GAAG,SAAlBA,eAAeA,CAAIC,OAAO,EAAK;IACnC,IAAI,CAACpJ,KAAK,CAACwF,UAAU,CAACU,SAAS,IAAI,CAAClG,KAAK,CAACsF,OAAO,EAAE;MACjDrF,IAAI,CAAC,MAAM,EAAE;QAAEqF,OAAO,EAAE8D,OAAO;QAAE7D,OAAO,EAAE;MAAI,CAAE,CAAC;MACjDtF,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC;IAC1B,CAAK,MAAM;MACL,IAAImJ,OAAO,IAAIpJ,KAAK,CAACsF,OAAO,EAAE;QAC5BrF,IAAI,CAAC,MAAM,EAAE;UAAEqF,OAAO,EAAEtF,KAAK,CAACsF,OAAO;UAAEC,OAAO,EAAE6D;QAAO,CAAE,CAAC;MAClE,CAAO,MAAM;QACLnJ,IAAI,CAAC,MAAM,EAAE;UAAEqF,OAAO,EAAE8D,OAAO;UAAE7D,OAAO,EAAEvF,KAAK,CAACsF;QAAO,CAAE,CAAC;MAClE;MACMrF,IAAI,CAAC,QAAQ,EAAE,KAAK,CAAC;IAC3B;EACA,CAAG;EACD,IAAMoJ,cAAc,GAAG,SAAjBA,cAAcA,CAAID,OAAO,EAAK;IAClC,IAAME,UAAU,GAAGF,OAAO,CAACxD,IAAI,EAAE;IACjC,IAAMgD,KAAK,GAAG,GAAGQ,OAAO,CAACG,IAAI,EAAE,IAAID,UAAU,EAAE;IAC/CrJ,IAAI,CAAC,MAAM,EAAE;MACXsJ,IAAI,EAAEH,OAAO,CAACG,IAAI,EAAE;MACpB3D,IAAI,EAAE0D,UAAU;MAChBV,KAAK;MACL/H,IAAI,EAAEuI,OAAO,CAACzH,OAAO,CAAC,MAAM;IAClC,CAAK,CAAC;EACN,CAAG;EACD,IAAM6H,eAAe,GAAG,SAAlBA,eAAeA,CAAIJ,OAAO,EAAE/E,QAAQ,EAAK;IAC7C,IAAMoF,QAAQ,GAAGpF,QAAQ,GAAGrB,SAAS,CAAChD,KAAK,CAACiD,WAAW,CAAC,CAACyG,MAAM,CAAC,UAACnF,CAAC;MAAA,OAAK,CAACA,CAAC,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,CAAC,CAACoF,OAAO,EAAE,MAAMP,OAAO,CAACO,OAAO,EAAE;IAAA,EAAC,GAAG3G,SAAS,CAAChD,KAAK,CAACiD,WAAW,CAAC,CAAClB,MAAM,CAAC,CAACqH,OAAO,CAAC,CAAC;IACjLnJ,IAAI,CAAC,MAAM,EAAEwJ,QAAQ,CAAC;EAC1B,CAAG;EACD,IAAMV,cAAc,GAAG,SAAjBA,cAAcA,CAAIR,KAAK,EAAiC;IAAA,IAA/BqB,kBAAkB,GAAAjK,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,KAAK;IACvD,IAAM6I,MAAM,GAAGD,KAAK,CAACC,MAAM,CAACS,OAAO,CAAC,IAAI,CAAC;IACzC,IAAI,CAACT,MAAM,EACT;IACF,IAAMlG,GAAG,GAAGkG,MAAM,CAACE,UAAU,CAACpF,QAAQ,GAAG,CAAC;IAC1C,IAAMwC,MAAM,GAAG0C,MAAM,CAACG,SAAS;IAC/B,IAAMxF,IAAI,GAAGhB,KAAK,CAACC,IAAI,CAAC,CAACE,GAAG,CAAC,CAACwD,MAAM,CAAC;IACrC,IAAI3C,IAAI,CAACuB,QAAQ,IAAIvB,IAAI,CAACzD,IAAI,KAAK,MAAM,EACvC;IACF,IAAM0J,OAAO,GAAGhB,aAAa,CAAC9F,GAAG,EAAEwD,MAAM,CAAC;IAC1C,QAAQ9F,KAAK,CAAC+C,aAAa;MACzB,KAAK,OAAO;QAAE;UACZoG,eAAe,CAACC,OAAO,CAAC;UACxB;QACR;MACM,KAAK,MAAM;QAAE;UACXnJ,IAAI,CAAC,MAAM,EAAEmJ,OAAO,EAAEQ,kBAAkB,CAAC;UACzC;QACR;MACM,KAAK,MAAM;QAAE;UACXP,cAAc,CAACD,OAAO,CAAC;UACvB;QACR;MACM,KAAK,OAAO;QAAE;UACZI,eAAe,CAACJ,OAAO,EAAE,CAAC,CAACjG,IAAI,CAACkB,QAAQ,CAAC;UACzC;QACR;MACM;QAAS;UACP;QACR;IACA;EACA,CAAG;EACD,IAAMe,YAAY,GAAG,SAAfA,YAAYA,CAAIjC,IAAI,EAAK;IAC7B,IAAInD,KAAK,CAAC+C,aAAa,KAAK,MAAM,EAChC,OAAO,KAAK;IACd,IAAIqG,OAAO,GAAGpJ,KAAK,CAACa,IAAI,CAACc,OAAO,CAAC,KAAK,CAAC;IACvC,IAAIwB,IAAI,CAACzD,IAAI,KAAK,YAAY,EAAE;MAC9B0J,OAAO,GAAGA,OAAO,CAACxH,QAAQ,CAAC,CAAC,EAAE,OAAO,CAAC;IAC5C;IACI,IAAIuB,IAAI,CAACzD,IAAI,KAAK,YAAY,EAAE;MAC9B0J,OAAO,GAAGA,OAAO,CAACzD,GAAG,CAAC,CAAC,EAAE,OAAO,CAAC;IACvC;IACIyD,OAAO,GAAGA,OAAO,CAACvI,IAAI,CAACsH,MAAM,CAAC0B,QAAQ,CAAC1G,IAAI,CAACQ,IAAI,EAAE,EAAE,CAAC,CAAC;IACtD,IAAI3D,KAAK,CAACiD,WAAW,IAAI,CAACwD,KAAK,CAACqD,OAAO,CAAC9J,KAAK,CAACiD,WAAW,CAAC,EAAE;MAC1D,IAAM8G,SAAS,GAAG,CAAC/J,KAAK,CAACiD,WAAW,CAACpB,GAAG,EAAE,GAAGjB,cAAc,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC;MACxE,IAAMoJ,QAAQ,GAAGhK,KAAK,CAACiD,WAAW,CAACrB,QAAQ,CAACmI,SAAS,EAAE,KAAK,CAAC;MAC7D,OAAOC,QAAQ,CAACxF,MAAM,CAAC4E,OAAO,EAAE,KAAK,CAAC;IAC5C;IACI,OAAO,KAAK;EAChB,CAAG;EACD,OAAO;IACLtH,KAAK;IACLM,IAAI;IACJ/B,QAAQ;IACRE,cAAc;IACdoH,KAAK;IACLpF,SAAS;IACT6C,YAAY;IACZyD,cAAc;IACdE,cAAc;IACdG,aAAa;IACbF,eAAe;IACfV,eAAe;IACfQ;EACJ,CAAG;AACH;AACY,IAACmB,oBAAoB,GAAG,SAAvBA,oBAAoBA,CAAIjK,KAAK,EAAAkK,KAAA,EAGpC;EAAA,IAFJ3H,SAAS,GAAA2H,KAAA,CAAT3H,SAAS;IACT6C,YAAY,GAAA8E,KAAA,CAAZ9E,YAAY;EAEZ,IAAM+E,EAAE,GAAGC,YAAY,CAAC,YAAY,CAAC;EACrC,IAAAC,WAAA,GAAclK,SAAS,EAAE;IAAjBmK,CAAC,GAAAD,WAAA,CAADC,CAAC;EACT,IAAMC,QAAQ,GAAG/I,QAAQ,CAAC;IAAA,OAAM,CAC9B2I,EAAE,CAACK,CAAC,EAAE,EACN;MAAE,cAAc,EAAExK,KAAK,CAAC+C,aAAa,KAAK;IAAM,CAAE,CACnD;EAAA,EAAC;EACF,IAAM0H,UAAU,GAAGjJ,QAAQ,CAAC;IAAA,OAAM8I,CAAC,CAAC,+BAA+B,CAAC;EAAA,EAAC;EACrE,IAAMI,SAAS,GAAGlJ,QAAQ,CAAC;IAAA,OAAM8I,CAAC,CAAC,oBAAoB,CAAC;EAAA,EAAC;EACzD,IAAMK,cAAc,GAAG,SAAjBA,cAAcA,CAAIxH,IAAI,EAAK;IAC/B,IAAMyH,OAAO,GAAG,EAAE;IAClB,IAAInL,WAAW,CAAC0D,IAAI,CAACzD,IAAI,CAAC,IAAI,CAACyD,IAAI,CAACuB,QAAQ,EAAE;MAC5CkG,OAAO,CAACC,IAAI,CAAC,WAAW,CAAC;MACzB,IAAI1H,IAAI,CAACzD,IAAI,KAAK,OAAO,EAAE;QACzBkL,OAAO,CAACC,IAAI,CAAC,OAAO,CAAC;MAC7B;IACA,CAAK,MAAM;MACLD,OAAO,CAACC,IAAI,CAAC1H,IAAI,CAACzD,IAAI,CAAC;IAC7B;IACI,IAAI6C,SAAS,CAACY,IAAI,CAAC,EAAE;MACnByH,OAAO,CAACC,IAAI,CAAC,SAAS,CAAC;IAC7B;IACI,IAAI1H,IAAI,CAACkC,OAAO,KAAK5F,WAAW,CAAC0D,IAAI,CAACzD,IAAI,CAAC,IAAIM,KAAK,CAAC+C,aAAa,KAAK,MAAM,CAAC,EAAE;MAC9E6H,OAAO,CAACC,IAAI,CAAC,UAAU,CAAC;MACxB,IAAI1H,IAAI,CAAC8B,KAAK,EAAE;QACd2F,OAAO,CAACC,IAAI,CAAC,YAAY,CAAC;MAClC;MACM,IAAI1H,IAAI,CAAC+B,GAAG,EAAE;QACZ0F,OAAO,CAACC,IAAI,CAAC,UAAU,CAAC;MAChC;IACA;IACI,IAAI1H,IAAI,CAACuB,QAAQ,EAAE;MACjBkG,OAAO,CAACC,IAAI,CAAC,UAAU,CAAC;IAC9B;IACI,IAAI1H,IAAI,CAACkB,QAAQ,EAAE;MACjBuG,OAAO,CAACC,IAAI,CAAC,UAAU,CAAC;IAC9B;IACI,IAAI1H,IAAI,CAACwB,WAAW,EAAE;MACpBiG,OAAO,CAACC,IAAI,CAAC1H,IAAI,CAACwB,WAAW,CAAC;IACpC;IACI,OAAOiG,OAAO,CAACE,IAAI,CAAC,GAAG,CAAC;EAC5B,CAAG;EACD,IAAMC,SAAS,GAAG,SAAZA,SAASA,CAAI5H,IAAI;IAAA,OAAK,CAC1BgH,EAAE,CAACa,CAAC,CAAC,KAAK,CAAC,EACX;MAAEC,OAAO,EAAE7F,YAAY,CAACjC,IAAI;IAAC,CAAE,CAChC;EAAA;EACD,OAAO;IACLoH,QAAQ;IACRE,UAAU;IACVC,SAAS;IACTC,cAAc;IACdI,SAAS;IACTT;EACJ,CAAG;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
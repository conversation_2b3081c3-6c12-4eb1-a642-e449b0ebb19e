<template>
  <el-dialog v-model="visible" title="手机号验证" width="400px" :before-close="handleClose" :close-on-click-modal="false"
    class="phone-verify-dialog">
    <div class="verify-form">
      <el-form ref="formRef" :model="form" :rules="rules" label-position="top">
        <el-form-item prop="name">
          <el-input v-model="form.name" placeholder="请输入姓名" clearable class="form-input" />
        </el-form-item>

        <el-form-item prop="phone">
          <el-input v-model="form.phone" placeholder="请输入手机号码" clearable maxlength="11" class="form-input" />
        </el-form-item>

        <el-form-item prop="code">
          <div class="code-input-group">
            <el-input v-model="form.code" placeholder="请输入验证码" clearable maxlength="6" class="code-input" />
            <el-button type="primary" :disabled="!canSendCode || countdown > 0" @click="sendCode" class="send-code-btn">
              {{ countdown > 0 ? `${countdown}s` : '发送验证码' }}
            </el-button>
          </div>
        </el-form-item>
      </el-form>

      <el-button type="primary" @click="handleVerify" :loading="verifying" class="verify-btn">
        验证
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
export default { name: 'PhoneVerifyDialog' }
</script>

<script setup>
import { ref, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import api from '@/api'

const props = defineProps({
  modelValue: { type: Boolean, default: false }
})

const emit = defineEmits(['update:modelValue', 'verify-success'])

const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const formRef = ref(null)
const form = ref({
  name: '',
  phone: '',
  code: ''
})

const verifying = ref(false)
const countdown = ref(0)
const verifyCodeId = ref('') // 存储验证码ID
let countdownTimer = null

// 表单验证规则
const rules = {
  name: [
    { required: true, message: '请输入姓名', trigger: 'blur' }
  ],
  phone: [
    { required: true, message: '请输入手机号码', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
  ],
  code: [
    { required: true, message: '请输入验证码', trigger: 'blur' },
    { pattern: /^\d{6}$/, message: '请输入6位数字验证码', trigger: 'blur' }
  ]
}

// 是否可以发送验证码
const canSendCode = computed(() => {
  return form.value.phone && /^1[3-9]\d{9}$/.test(form.value.phone)
})

// 监听弹窗关闭，重置表单
watch(() => props.modelValue, (newVal) => {
  if (!newVal) {
    resetForm()
  }
})

// 发送验证码
const sendCode = async () => {
  if (!canSendCode.value) {
    ElMessage.error('请输入正确的手机号码')
    return
  }
  const res = await api.sendVerifyCode({
    sendType: 'no_login',
    mobile: form.value.phone
  })
  if (res.code === 200) {
    // 保存验证码ID，用于后续验证
    verifyCodeId.value = res.data
    ElMessage.success('验证码已发送')
    startCountdown()
  } else {
    ElMessage.error(res.message || '发送验证码失败')
  }
}

// 开始倒计时
const startCountdown = () => {
  countdown.value = 60
  countdownTimer = setInterval(() => {
    countdown.value--
    if (countdown.value <= 0) {
      clearInterval(countdownTimer)
      countdownTimer = null
    }
  }, 1000)
}

// 验证手机号
const handleVerify = async () => {
  if (!formRef.value) return
  try {
    const valid = await formRef.value.validate()
    if (!valid) return
    verifying.value = true
    if (!verifyCodeId.value) {
      ElMessage.error('请先获取验证码')
      return
    }

    const res = await api.login({
      grant_type: 'anonymous',
      userName: form.value.name,
      mobile: form.value.phone,
      verifyCode: form.value.code,
      verifyCodeId: verifyCodeId.value // 使用从发送验证码接口获取的ID
    })
    if (res.code === 200) {
      ElMessage.success('验证成功')

      console.log('开始存储token到会话存储')
      // 保存登录信息到会话存储
      sessionStorage.setItem('microAppToken', res.data.token)
      sessionStorage.setItem('refresh_token', `bearer ${res.data.refreshToken.value}`)
      sessionStorage.setItem('expires', res.data.expires_in)
      sessionStorage.setItem('expiration', res.data.refreshToken.expiration)
      sessionStorage.setItem('verify', res.data.passwordElementMatchReg ? 1 : 0)

      console.log('Token存储完成:', sessionStorage.getItem('microAppToken'))

      // 通知父组件验证成功
      console.log('准备通知父组件验证成功')
      emit('verify-success', res.data)

      // 关闭弹窗
      visible.value = false
    } else {
      ElMessage.error(res.message || '验证失败')
    }
  } catch (error) {
    ElMessage.error('验证失败')
    console.error('验证失败:', error)
  } finally {
    verifying.value = false
  }
}

// 重置表单
const resetForm = () => {
  form.value = {
    name: '',
    phone: '',
    code: ''
  }
  verifyCodeId.value = '' // 清空验证码ID
  if (formRef.value) {
    formRef.value.clearValidate()
  }
  if (countdownTimer) {
    clearInterval(countdownTimer)
    countdownTimer = null
  }
  countdown.value = 0
  verifying.value = false
}

// 关闭弹窗
const handleClose = () => {
  visible.value = false
}
</script>

<style lang="scss" scoped>
.phone-verify-dialog {
  .verify-form {
    padding: 20px 0;

    .form-input {
      height: 48px;
      margin-bottom: 20px;

      :deep(.el-input__inner) {
        height: 48px;
        line-height: 48px;
        border-radius: 8px;
        border: 1px solid #e0e0e0;
        font-size: 16px;

        &::placeholder {
          color: #c0c0c0;
        }
      }
    }

    .code-input-group {
      display: flex;
      gap: 12px;
      margin-bottom: 20px;
      width: 100%;

      .code-input {
        flex: 1;
        height: 48px;

        :deep(.el-input__inner) {
          height: 48px;
          line-height: 48px;
          border-radius: 8px;
          border: 1px solid #e0e0e0;
          font-size: 16px;
        }
      }

      .send-code-btn {
        height: 48px;
        padding: 0 16px;
        border-radius: 8px;
        background: #6c9fff;
        border: none;
        color: white;
        font-size: 14px;
        white-space: nowrap;

        &:hover:not(:disabled) {
          background: #5a8cff;
        }

        &:disabled {
          background: #c0c0c0;
          color: #fff;
        }
      }
    }

    .verify-btn {
      width: 100%;
      height: 48px;
      background: #6c9fff;
      border: none;
      border-radius: 8px;
      color: white;
      font-size: 16px;
      font-weight: 500;

      &:hover:not(:disabled) {
        background: #5a8cff;
      }
    }
  }
}

:deep(.el-dialog__header) {
  text-align: center;
  padding: 20px 20px 0;

  .el-dialog__title {
    font-size: 18px;
    font-weight: 500;
    color: #333;
  }
}

:deep(.el-dialog__body) {
  padding: 10px 20px 20px;
}
</style>

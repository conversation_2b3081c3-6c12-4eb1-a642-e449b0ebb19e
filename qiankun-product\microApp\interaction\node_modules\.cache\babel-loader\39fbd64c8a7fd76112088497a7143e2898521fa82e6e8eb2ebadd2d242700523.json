{"ast": null, "code": "import { getCurrentInstance, ref, unref } from 'vue';\nimport { getRowIdentity } from '../util.mjs';\nfunction useCurrent(watcherData) {\n  var instance = getCurrentInstance();\n  var _currentRowKey = ref(null);\n  var currentRow = ref(null);\n  var setCurrentRowKey = function setCurrentRowKey(key) {\n    instance.store.assertRowKey();\n    _currentRowKey.value = key;\n    setCurrentRowByKey(key);\n  };\n  var restoreCurrentRowKey = function restoreCurrentRowKey() {\n    _currentRowKey.value = null;\n  };\n  var setCurrentRowByKey = function setCurrentRowByKey(key) {\n    var data = watcherData.data,\n      rowKey = watcherData.rowKey;\n    var _currentRow = null;\n    if (rowKey.value) {\n      _currentRow = (unref(data) || []).find(function (item) {\n        return getRowIdentity(item, rowKey.value) === key;\n      });\n    }\n    currentRow.value = _currentRow;\n    instance.emit(\"current-change\", currentRow.value, null);\n  };\n  var updateCurrentRow = function updateCurrentRow(_currentRow) {\n    var oldCurrentRow = currentRow.value;\n    if (_currentRow && _currentRow !== oldCurrentRow) {\n      currentRow.value = _currentRow;\n      instance.emit(\"current-change\", currentRow.value, oldCurrentRow);\n      return;\n    }\n    if (!_currentRow && oldCurrentRow) {\n      currentRow.value = null;\n      instance.emit(\"current-change\", null, oldCurrentRow);\n    }\n  };\n  var updateCurrentRowData = function updateCurrentRowData() {\n    var rowKey = watcherData.rowKey.value;\n    var data = watcherData.data.value || [];\n    var oldCurrentRow = currentRow.value;\n    if (!data.includes(oldCurrentRow) && oldCurrentRow) {\n      if (rowKey) {\n        var currentRowKey = getRowIdentity(oldCurrentRow, rowKey);\n        setCurrentRowByKey(currentRowKey);\n      } else {\n        currentRow.value = null;\n      }\n      if (currentRow.value === null) {\n        instance.emit(\"current-change\", null, oldCurrentRow);\n      }\n    } else if (_currentRowKey.value) {\n      setCurrentRowByKey(_currentRowKey.value);\n      restoreCurrentRowKey();\n    }\n  };\n  return {\n    setCurrentRowKey,\n    restoreCurrentRowKey,\n    setCurrentRowByKey,\n    updateCurrentRow,\n    updateCurrentRowData,\n    states: {\n      _currentRowKey,\n      currentRow\n    }\n  };\n}\nexport { useCurrent as default };", "map": {"version": 3, "names": ["useCurrent", "watcherData", "instance", "getCurrentInstance", "_currentRowKey", "ref", "currentRow", "setCurrentRowKey", "key", "store", "assertRowKey", "value", "setCurrentRowByKey", "restoreCurrentRowKey", "data", "<PERSON><PERSON><PERSON>", "_currentRow", "unref", "find", "item", "getRowIdentity", "emit", "updateCurrentRow", "oldCurrentRow", "updateCurrentRowData", "includes", "currentRowKey", "states"], "sources": ["../../../../../../../packages/components/table/src/store/current.ts"], "sourcesContent": ["// @ts-nocheck\nimport { getCurrentInstance, ref, unref } from 'vue'\nimport { getRowIdentity } from '../util'\n\nimport type { Ref } from 'vue'\nimport type { Table } from '../table/defaults'\nimport type { WatcherPropsData } from '.'\n\nfunction useCurrent<T>(watcherData: WatcherPropsData<T>) {\n  const instance = getCurrentInstance() as Table<T>\n  const _currentRowKey = ref<string>(null)\n  const currentRow: Ref<T> = ref(null)\n\n  const setCurrentRowKey = (key: string) => {\n    instance.store.assertRowKey()\n    _currentRowKey.value = key\n    setCurrentRowByKey(key)\n  }\n\n  const restoreCurrentRowKey = () => {\n    _currentRowKey.value = null\n  }\n\n  const setCurrentRowByKey = (key: string) => {\n    const { data, rowKey } = watcherData\n    let _currentRow = null\n    if (rowKey.value) {\n      _currentRow = (unref(data) || []).find(\n        (item) => getRowIdentity(item, rowKey.value) === key\n      )\n    }\n    currentRow.value = _currentRow\n    instance.emit('current-change', currentRow.value, null)\n  }\n\n  const updateCurrentRow = (_currentRow: T) => {\n    const oldCurrentRow = currentRow.value\n    if (_currentRow && _currentRow !== oldCurrentRow) {\n      currentRow.value = _currentRow\n      instance.emit('current-change', currentRow.value, oldCurrentRow)\n      return\n    }\n    if (!_currentRow && oldCurrentRow) {\n      currentRow.value = null\n      instance.emit('current-change', null, oldCurrentRow)\n    }\n  }\n\n  const updateCurrentRowData = () => {\n    const rowKey = watcherData.rowKey.value\n    // data 为 null 时，解构时的默认值会被忽略\n    const data = watcherData.data.value || []\n    const oldCurrentRow = currentRow.value\n    // 当 currentRow 不在 data 中时尝试更新数据\n    if (!data.includes(oldCurrentRow) && oldCurrentRow) {\n      if (rowKey) {\n        const currentRowKey = getRowIdentity(oldCurrentRow, rowKey)\n        setCurrentRowByKey(currentRowKey)\n      } else {\n        currentRow.value = null\n      }\n      if (currentRow.value === null) {\n        instance.emit('current-change', null, oldCurrentRow)\n      }\n    } else if (_currentRowKey.value) {\n      // 把初始时下设置的 rowKey 转化成 rowData\n      setCurrentRowByKey(_currentRowKey.value)\n      restoreCurrentRowKey()\n    }\n  }\n\n  return {\n    setCurrentRowKey,\n    restoreCurrentRowKey,\n    setCurrentRowByKey,\n    updateCurrentRow,\n    updateCurrentRowData,\n    states: {\n      _currentRowKey,\n      currentRow,\n    },\n  }\n}\n\nexport default useCurrent\n"], "mappings": ";;AAEA,SAASA,UAAUA,CAACC,WAAW,EAAE;EAC/B,IAAMC,QAAQ,GAAGC,kBAAkB,EAAE;EACrC,IAAMC,cAAc,GAAGC,GAAG,CAAC,IAAI,CAAC;EAChC,IAAMC,UAAU,GAAGD,GAAG,CAAC,IAAI,CAAC;EAC5B,IAAME,gBAAgB,GAAG,SAAnBA,gBAAgBA,CAAIC,GAAG,EAAK;IAChCN,QAAQ,CAACO,KAAK,CAACC,YAAY,EAAE;IAC7BN,cAAc,CAACO,KAAK,GAAGH,GAAG;IAC1BI,kBAAkB,CAACJ,GAAG,CAAC;EAC3B,CAAG;EACD,IAAMK,oBAAoB,GAAG,SAAvBA,oBAAoBA,CAAA,EAAS;IACjCT,cAAc,CAACO,KAAK,GAAG,IAAI;EAC/B,CAAG;EACD,IAAMC,kBAAkB,GAAG,SAArBA,kBAAkBA,CAAIJ,GAAG,EAAK;IAClC,IAAQM,IAAI,GAAab,WAAW,CAA5Ba,IAAI;MAAEC,MAAM,GAAKd,WAAW,CAAtBc,MAAM;IACpB,IAAIC,WAAW,GAAG,IAAI;IACtB,IAAID,MAAM,CAACJ,KAAK,EAAE;MAChBK,WAAW,GAAG,CAACC,KAAK,CAACH,IAAI,CAAC,IAAI,EAAE,EAAEI,IAAI,CAAC,UAACC,IAAI;QAAA,OAAKC,cAAc,CAACD,IAAI,EAAEJ,MAAM,CAACJ,KAAK,CAAC,KAAKH,GAAG;MAAA,EAAC;IAClG;IACIF,UAAU,CAACK,KAAK,GAAGK,WAAW;IAC9Bd,QAAQ,CAACmB,IAAI,CAAC,gBAAgB,EAAEf,UAAU,CAACK,KAAK,EAAE,IAAI,CAAC;EAC3D,CAAG;EACD,IAAMW,gBAAgB,GAAG,SAAnBA,gBAAgBA,CAAIN,WAAW,EAAK;IACxC,IAAMO,aAAa,GAAGjB,UAAU,CAACK,KAAK;IACtC,IAAIK,WAAW,IAAIA,WAAW,KAAKO,aAAa,EAAE;MAChDjB,UAAU,CAACK,KAAK,GAAGK,WAAW;MAC9Bd,QAAQ,CAACmB,IAAI,CAAC,gBAAgB,EAAEf,UAAU,CAACK,KAAK,EAAEY,aAAa,CAAC;MAChE;IACN;IACI,IAAI,CAACP,WAAW,IAAIO,aAAa,EAAE;MACjCjB,UAAU,CAACK,KAAK,GAAG,IAAI;MACvBT,QAAQ,CAACmB,IAAI,CAAC,gBAAgB,EAAE,IAAI,EAAEE,aAAa,CAAC;IAC1D;EACA,CAAG;EACD,IAAMC,oBAAoB,GAAG,SAAvBA,oBAAoBA,CAAA,EAAS;IACjC,IAAMT,MAAM,GAAGd,WAAW,CAACc,MAAM,CAACJ,KAAK;IACvC,IAAMG,IAAI,GAAGb,WAAW,CAACa,IAAI,CAACH,KAAK,IAAI,EAAE;IACzC,IAAMY,aAAa,GAAGjB,UAAU,CAACK,KAAK;IACtC,IAAI,CAACG,IAAI,CAACW,QAAQ,CAACF,aAAa,CAAC,IAAIA,aAAa,EAAE;MAClD,IAAIR,MAAM,EAAE;QACV,IAAMW,aAAa,GAAGN,cAAc,CAACG,aAAa,EAAER,MAAM,CAAC;QAC3DH,kBAAkB,CAACc,aAAa,CAAC;MACzC,CAAO,MAAM;QACLpB,UAAU,CAACK,KAAK,GAAG,IAAI;MAC/B;MACM,IAAIL,UAAU,CAACK,KAAK,KAAK,IAAI,EAAE;QAC7BT,QAAQ,CAACmB,IAAI,CAAC,gBAAgB,EAAE,IAAI,EAAEE,aAAa,CAAC;MAC5D;IACA,CAAK,MAAM,IAAInB,cAAc,CAACO,KAAK,EAAE;MAC/BC,kBAAkB,CAACR,cAAc,CAACO,KAAK,CAAC;MACxCE,oBAAoB,EAAE;IAC5B;EACA,CAAG;EACD,OAAO;IACLN,gBAAgB;IAChBM,oBAAoB;IACpBD,kBAAkB;IAClBU,gBAAgB;IAChBE,oBAAoB;IACpBG,MAAM,EAAE;MACNvB,cAAc;MACdE;IACN;EACA,CAAG;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
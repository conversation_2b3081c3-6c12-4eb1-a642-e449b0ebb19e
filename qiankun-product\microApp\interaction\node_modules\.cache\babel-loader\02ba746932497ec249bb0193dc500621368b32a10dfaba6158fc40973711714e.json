{"ast": null, "code": "import { inject, ref, computed, toRaw } from 'vue';\nimport { isEqual } from 'lodash-unified';\nimport '../../../form/index.mjs';\nimport '../../../../utils/index.mjs';\nimport { checkboxGroupContextKey } from '../constants.mjs';\nimport { isPropAbsent, isBoolean } from '../../../../utils/types.mjs';\nimport { isArray, isObject } from '@vue/shared';\nimport { useFormSize } from '../../../form/src/hooks/use-form-common-props.mjs';\nvar useCheckboxStatus = function useCheckboxStatus(props, slots, _ref) {\n  var model = _ref.model;\n  var checkboxGroup = inject(checkboxGroupContextKey, void 0);\n  var isFocused = ref(false);\n  var actualValue = computed(function () {\n    if (!isPropAbsent(props.value)) {\n      return props.value;\n    }\n    return props.label;\n  });\n  var isChecked = computed(function () {\n    var value = model.value;\n    if (isBoolean(value)) {\n      return value;\n    } else if (isArray(value)) {\n      if (isObject(actualValue.value)) {\n        return value.map(toRaw).some(function (o) {\n          return isEqual(o, actualValue.value);\n        });\n      } else {\n        return value.map(toRaw).includes(actualValue.value);\n      }\n    } else if (value !== null && value !== void 0) {\n      return value === props.trueValue || value === props.trueLabel;\n    } else {\n      return !!value;\n    }\n  });\n  var checkboxButtonSize = useFormSize(computed(function () {\n    var _a;\n    return (_a = checkboxGroup == null ? void 0 : checkboxGroup.size) == null ? void 0 : _a.value;\n  }), {\n    prop: true\n  });\n  var checkboxSize = useFormSize(computed(function () {\n    var _a;\n    return (_a = checkboxGroup == null ? void 0 : checkboxGroup.size) == null ? void 0 : _a.value;\n  }));\n  var hasOwnLabel = computed(function () {\n    return !!slots.default || !isPropAbsent(actualValue.value);\n  });\n  return {\n    checkboxButtonSize,\n    isChecked,\n    isFocused,\n    checkboxSize,\n    hasOwnLabel,\n    actualValue\n  };\n};\nexport { useCheckboxStatus };", "map": {"version": 3, "names": ["useCheckboxStatus", "props", "slots", "_ref", "model", "checkboxGroup", "inject", "checkboxGroupContextKey", "isFocused", "ref", "actualValue", "computed", "isPropAbsent", "value", "label", "isChecked", "isBoolean", "isArray", "isObject", "map", "toRaw", "some", "o", "isEqual", "includes", "trueValue", "<PERSON><PERSON><PERSON><PERSON>", "checkboxButtonSize", "useFormSize", "_a", "size", "prop", "checkboxSize", "hasOwnLabel", "default"], "sources": ["../../../../../../../packages/components/checkbox/src/composables/use-checkbox-status.ts"], "sourcesContent": ["import { computed, inject, ref, toRaw } from 'vue'\nimport { isEqual } from 'lodash-unified'\nimport { useFormSize } from '@element-plus/components/form'\nimport { isArray, isBoolean, isObject, isPropAbsent } from '@element-plus/utils'\nimport { checkboxGroupContextKey } from '../constants'\n\nimport type { ComponentInternalInstance } from 'vue'\nimport type { CheckboxProps } from '../checkbox'\nimport type { CheckboxModel } from '../composables'\n\nexport const useCheckboxStatus = (\n  props: CheckboxProps,\n  slots: ComponentInternalInstance['slots'],\n  { model }: Pick<CheckboxModel, 'model'>\n) => {\n  const checkboxGroup = inject(checkboxGroupContextKey, undefined)\n  const isFocused = ref(false)\n  const actualValue = computed(() => {\n    // In version 2.x, if there's no props.value, props.label will act as props.value\n    // In version 3.x, remove this computed value, use props.value instead.\n    if (!isPropAbsent(props.value)) {\n      return props.value\n    }\n    return props.label\n  })\n  const isChecked = computed<boolean>(() => {\n    const value = model.value\n    if (isBoolean(value)) {\n      return value\n    } else if (isArray(value)) {\n      if (isObject(actualValue.value)) {\n        return value.map(toRaw).some((o) => isEqual(o, actualValue.value))\n      } else {\n        return value.map(toRaw).includes(actualValue.value)\n      }\n    } else if (value !== null && value !== undefined) {\n      return value === props.trueValue || value === props.trueLabel\n    } else {\n      return !!value\n    }\n  })\n\n  const checkboxButtonSize = useFormSize(\n    computed(() => checkboxGroup?.size?.value),\n    {\n      prop: true,\n    }\n  )\n  const checkboxSize = useFormSize(computed(() => checkboxGroup?.size?.value))\n\n  const hasOwnLabel = computed<boolean>(() => {\n    return !!slots.default || !isPropAbsent(actualValue.value)\n  })\n\n  return {\n    checkboxButtonSize,\n    isChecked,\n    isFocused,\n    checkboxSize,\n    hasOwnLabel,\n    actualValue,\n  }\n}\n\nexport type CheckboxStatus = ReturnType<typeof useCheckboxStatus>\n"], "mappings": ";;;;;;;;AAKY,IAACA,iBAAiB,GAAG,SAApBA,iBAAiBA,CAAIC,KAAK,EAAEC,KAAK,EAAAC,IAAA,EAAgB;EAAA,IAAZC,KAAK,GAAAD,IAAA,CAALC,KAAK;EACrD,IAAMC,aAAa,GAAGC,MAAM,CAACC,uBAAuB,EAAE,KAAK,CAAC,CAAC;EAC7D,IAAMC,SAAS,GAAGC,GAAG,CAAC,KAAK,CAAC;EAC5B,IAAMC,WAAW,GAAGC,QAAQ,CAAC,YAAM;IACjC,IAAI,CAACC,YAAY,CAACX,KAAK,CAACY,KAAK,CAAC,EAAE;MAC9B,OAAOZ,KAAK,CAACY,KAAK;IACxB;IACI,OAAOZ,KAAK,CAACa,KAAK;EACtB,CAAG,CAAC;EACF,IAAMC,SAAS,GAAGJ,QAAQ,CAAC,YAAM;IAC/B,IAAME,KAAK,GAAGT,KAAK,CAACS,KAAK;IACzB,IAAIG,SAAS,CAACH,KAAK,CAAC,EAAE;MACpB,OAAOA,KAAK;IAClB,CAAK,MAAM,IAAII,OAAO,CAACJ,KAAK,CAAC,EAAE;MACzB,IAAIK,QAAQ,CAACR,WAAW,CAACG,KAAK,CAAC,EAAE;QAC/B,OAAOA,KAAK,CAACM,GAAG,CAACC,KAAK,CAAC,CAACC,IAAI,CAAC,UAACC,CAAC;UAAA,OAAKC,OAAO,CAACD,CAAC,EAAEZ,WAAW,CAACG,KAAK,CAAC;QAAA,EAAC;MAC1E,CAAO,MAAM;QACL,OAAOA,KAAK,CAACM,GAAG,CAACC,KAAK,CAAC,CAACI,QAAQ,CAACd,WAAW,CAACG,KAAK,CAAC;MAC3D;IACA,CAAK,MAAM,IAAIA,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAK,KAAK,CAAC,EAAE;MAC7C,OAAOA,KAAK,KAAKZ,KAAK,CAACwB,SAAS,IAAIZ,KAAK,KAAKZ,KAAK,CAACyB,SAAS;IACnE,CAAK,MAAM;MACL,OAAO,CAAC,CAACb,KAAK;IACpB;EACA,CAAG,CAAC;EACF,IAAMc,kBAAkB,GAAGC,WAAW,CAACjB,QAAQ,CAAC,YAAM;IACpD,IAAIkB,EAAE;IACN,OAAO,CAACA,EAAE,GAAGxB,aAAa,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,aAAa,CAACyB,IAAI,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGD,EAAE,CAAChB,KAAK;EACjG,CAAG,CAAC,EAAE;IACFkB,IAAI,EAAE;EACV,CAAG,CAAC;EACF,IAAMC,YAAY,GAAGJ,WAAW,CAACjB,QAAQ,CAAC,YAAM;IAC9C,IAAIkB,EAAE;IACN,OAAO,CAACA,EAAE,GAAGxB,aAAa,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,aAAa,CAACyB,IAAI,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGD,EAAE,CAAChB,KAAK;EACjG,CAAG,CAAC,CAAC;EACH,IAAMoB,WAAW,GAAGtB,QAAQ,CAAC,YAAM;IACjC,OAAO,CAAC,CAACT,KAAK,CAACgC,OAAO,IAAI,CAACtB,YAAY,CAACF,WAAW,CAACG,KAAK,CAAC;EAC9D,CAAG,CAAC;EACF,OAAO;IACLc,kBAAkB;IAClBZ,SAAS;IACTP,SAAS;IACTwB,YAAY;IACZC,WAAW;IACXvB;EACJ,CAAG;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
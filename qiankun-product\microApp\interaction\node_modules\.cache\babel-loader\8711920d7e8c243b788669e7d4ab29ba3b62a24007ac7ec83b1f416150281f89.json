{"ast": null, "code": "import { resolveComponent as _resolveComponent, createVNode as _createVNode, withCtx as _withCtx, createElementVNode as _createElementVNode, renderSlot as _renderSlot, normalizeClass as _normalizeClass, openBlock as _openBlock, createElementBlock as _createElementBlock } from \"vue\";\nvar _hoisted_1 = {\n  class: \"VideoMeetingTitleIcon left\"\n};\nvar _hoisted_2 = {\n  class: \"VideoMeetingTitleIcon right\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  var _component_DArrowRight = _resolveComponent(\"DArrowRight\");\n  var _component_el_icon = _resolveComponent(\"el-icon\");\n  return _openBlock(), _createElementBlock(\"div\", {\n    class: _normalizeClass([\"VideoMeetingTitle\", $setup.props.className])\n  }, [_createElementVNode(\"div\", _hoisted_1, [_createVNode(_component_el_icon, null, {\n    default: _withCtx(function () {\n      return [_createVNode(_component_DArrowRight)];\n    }),\n    _: 1 /* STABLE */\n  })]), _renderSlot(_ctx.$slots, \"default\"), _createElementVNode(\"div\", _hoisted_2, [_createVNode(_component_el_icon, null, {\n    default: _withCtx(function () {\n      return [_createVNode(_component_DArrowRight)];\n    }),\n    _: 1 /* STABLE */\n  })])], 2 /* CLASS */);\n}", "map": {"version": 3, "names": ["class", "_createElementBlock", "_normalizeClass", "$setup", "props", "className", "_createElementVNode", "_hoisted_1", "_createVNode", "_component_el_icon", "default", "_withCtx", "_component_DArrowRight", "_", "_renderSlot", "_ctx", "$slots", "_hoisted_2"], "sources": ["D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\interaction\\src\\views\\VideoMeeting\\component\\VideoMeetingTitle.vue"], "sourcesContent": ["<template>\r\n  <div class=\"VideoMeetingTitle\" :class=\"props.className\">\r\n    <div class=\"VideoMeetingTitleIcon left\">\r\n      <el-icon>\r\n        <DArrowRight />\r\n      </el-icon>\r\n    </div>\r\n    <slot></slot>\r\n    <div class=\"VideoMeetingTitleIcon right\">\r\n      <el-icon>\r\n        <DArrowRight />\r\n      </el-icon>\r\n    </div>\r\n  </div>\r\n</template>\r\n<script>\r\nexport default { name: 'VideoMeetingTitle' }\r\n</script>\r\n<script setup>\r\nconst props = defineProps({ className: { type: String, default: 'left' } })\r\n</script>\r\n<style lang=\"scss\">\r\n.VideoMeetingTitle {\r\n  width: 100%;\r\n  display: flex;\r\n  align-items: center;\r\n  padding: var(--zy-distance-two) 0;\r\n  font-size: var(--zy-name-font-size);\r\n  position: relative;\r\n\r\n  &.left {\r\n    padding-left: 40px;\r\n\r\n    .VideoMeetingTitleIcon.left {\r\n      position: absolute;\r\n      top: 50%;\r\n      left: 0;\r\n      transform: translateY(-50%);\r\n    }\r\n\r\n    .VideoMeetingTitleIcon.right {\r\n      display: none;\r\n    }\r\n  }\r\n\r\n  &.center {\r\n    justify-content: center;\r\n\r\n    .VideoMeetingTitleIcon {\r\n      position: relative;\r\n    }\r\n\r\n    .VideoMeetingTitleIcon.right {\r\n      transform: rotate(180deg);\r\n    }\r\n  }\r\n\r\n  .VideoMeetingTitleIcon {\r\n    min-width: 40px;\r\n    display: flex;\r\n    align-items: center;\r\n    padding-left: 20px;\r\n\r\n    &::after {\r\n      content: \"\";\r\n      position: absolute;\r\n      top: 50%;\r\n      left: 2px;\r\n      width: 14px;\r\n      height: 14px;\r\n      border-radius: 2px;\r\n      transform: translateY(-50%) rotate(45deg);\r\n      background-color: var(--zy-el-color-primary-light-5);\r\n      z-index: 1;\r\n    }\r\n\r\n    &::before {\r\n      content: \"\";\r\n      position: absolute;\r\n      top: 50%;\r\n      left: 8px;\r\n      width: 14px;\r\n      height: 14px;\r\n      border-radius: 2px;\r\n      transform: translateY(-50%) rotate(45deg);\r\n      background-color: var(--zy-el-color-primary);\r\n      z-index: 2;\r\n    }\r\n\r\n    .zy-el-icon {\r\n      font-size: var(--zy-name-font-size);\r\n      color: var(--zy-el-color-primary);\r\n    }\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;EAESA,KAAK,EAAC;AAA4B;;EAMlCA,KAAK,EAAC;AAA6B;;;;uBAP1CC,mBAAA,CAYM;IAZDD,KAAK,EADZE,eAAA,EACa,mBAAmB,EAASC,MAAA,CAAAC,KAAK,CAACC,SAAS;MACpDC,mBAAA,CAIM,OAJNC,UAIM,GAHJC,YAAA,CAEUC,kBAAA;IALhBC,OAAA,EAAAC,QAAA,CAIQ;MAAA,OAAe,CAAfH,YAAA,CAAeI,sBAAA,E;;IAJvBC,CAAA;QAOIC,WAAA,CAAaC,IAAA,CAAAC,MAAA,cACbV,mBAAA,CAIM,OAJNW,UAIM,GAHJT,YAAA,CAEUC,kBAAA;IAXhBC,OAAA,EAAAC,QAAA,CAUQ;MAAA,OAAe,CAAfH,YAAA,CAAeI,sBAAA,E;;IAVvBC,CAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
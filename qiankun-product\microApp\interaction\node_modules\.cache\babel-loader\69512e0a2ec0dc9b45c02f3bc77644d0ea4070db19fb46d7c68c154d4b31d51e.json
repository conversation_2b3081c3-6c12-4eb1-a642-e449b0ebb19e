{"ast": null, "code": "function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nfunction _defineProperty(e, r, t) { return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, { value: t, enumerable: !0, configurable: !0, writable: !0 }) : e[r] = t, e; }\nfunction _toPropertyKey(t) { var i = _toPrimitive(t, \"string\"); return \"symbol\" == typeof i ? i : i + \"\"; }\nfunction _toPrimitive(t, r) { if (\"object\" != typeof t || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || \"default\"); if (\"object\" != typeof i) return i; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (\"string\" === r ? String : Number)(t); }\nimport { defineComponent, inject, ref, computed, unref, watch, onMounted, provide, openBlock, createElementBlock, normalizeStyle, normalizeClass, renderSlot, createVNode, withCtx, Fragment, createTextVNode, toDisplayString, createCommentVNode } from 'vue';\nimport { offset } from '@floating-ui/dom';\nimport '../../../hooks/index.mjs';\nimport '../../visual-hidden/index.mjs';\nimport { tooltipV2RootKey, tooltipV2ContentKey } from './constants.mjs';\nimport { tooltipV2ContentProps } from './content.mjs';\nimport { tooltipV2CommonProps } from './common.mjs';\nimport _export_sfc from '../../../_virtual/plugin-vue_export-helper.mjs';\nimport { useFloating, arrowMiddleware } from '../../../hooks/use-floating/index.mjs';\nimport { useZIndex } from '../../../hooks/use-z-index/index.mjs';\nimport { useNamespace } from '../../../hooks/use-namespace/index.mjs';\nimport ElVisuallyHidden from '../../visual-hidden/src/visual-hidden.mjs';\nvar _hoisted_1 = [\"data-side\"];\nvar __default__ = defineComponent({\n  name: \"ElTooltipV2Content\"\n});\nvar _sfc_main = /* @__PURE__ */defineComponent(_objectSpread(_objectSpread({}, __default__), {}, {\n  props: _objectSpread(_objectSpread({}, tooltipV2ContentProps), tooltipV2CommonProps),\n  setup(__props) {\n    var props = __props;\n    var _inject = inject(tooltipV2RootKey),\n      triggerRef = _inject.triggerRef,\n      contentId = _inject.contentId;\n    var placement = ref(props.placement);\n    var strategy = ref(props.strategy);\n    var arrowRef = ref(null);\n    var _useFloating = useFloating({\n        placement,\n        strategy,\n        middleware: computed(function () {\n          var middleware = [offset(props.offset)];\n          if (props.showArrow) {\n            middleware.push(arrowMiddleware({\n              arrowRef\n            }));\n          }\n          return middleware;\n        })\n      }),\n      referenceRef = _useFloating.referenceRef,\n      contentRef = _useFloating.contentRef,\n      middlewareData = _useFloating.middlewareData,\n      x = _useFloating.x,\n      y = _useFloating.y,\n      update = _useFloating.update;\n    var zIndex = useZIndex().nextZIndex();\n    var ns = useNamespace(\"tooltip-v2\");\n    var side = computed(function () {\n      return placement.value.split(\"-\")[0];\n    });\n    var contentStyle = computed(function () {\n      return {\n        position: unref(strategy),\n        top: `${unref(y) || 0}px`,\n        left: `${unref(x) || 0}px`,\n        zIndex\n      };\n    });\n    var arrowStyle = computed(function () {\n      if (!props.showArrow) return {};\n      var _unref = unref(middlewareData),\n        arrow = _unref.arrow;\n      return {\n        [`--${ns.namespace.value}-tooltip-v2-arrow-x`]: `${arrow == null ? void 0 : arrow.x}px` || \"\",\n        [`--${ns.namespace.value}-tooltip-v2-arrow-y`]: `${arrow == null ? void 0 : arrow.y}px` || \"\"\n      };\n    });\n    var contentClass = computed(function () {\n      return [ns.e(\"content\"), ns.is(\"dark\", props.effect === \"dark\"), ns.is(unref(strategy)), props.contentClass];\n    });\n    watch(arrowRef, function () {\n      return update();\n    });\n    watch(function () {\n      return props.placement;\n    }, function (val) {\n      return placement.value = val;\n    });\n    onMounted(function () {\n      watch(function () {\n        return props.reference || triggerRef.value;\n      }, function (el) {\n        referenceRef.value = el || void 0;\n      }, {\n        immediate: true\n      });\n    });\n    provide(tooltipV2ContentKey, {\n      arrowRef\n    });\n    return function (_ctx, _cache) {\n      return openBlock(), createElementBlock(\"div\", {\n        ref_key: \"contentRef\",\n        ref: contentRef,\n        style: normalizeStyle(unref(contentStyle)),\n        \"data-tooltip-v2-root\": \"\"\n      }, [!_ctx.nowrap ? (openBlock(), createElementBlock(\"div\", {\n        key: 0,\n        \"data-side\": unref(side),\n        class: normalizeClass(unref(contentClass))\n      }, [renderSlot(_ctx.$slots, \"default\", {\n        contentStyle: unref(contentStyle),\n        contentClass: unref(contentClass)\n      }), createVNode(unref(ElVisuallyHidden), {\n        id: unref(contentId),\n        role: \"tooltip\"\n      }, {\n        default: withCtx(function () {\n          return [_ctx.ariaLabel ? (openBlock(), createElementBlock(Fragment, {\n            key: 0\n          }, [createTextVNode(toDisplayString(_ctx.ariaLabel), 1)], 64)) : renderSlot(_ctx.$slots, \"default\", {\n            key: 1\n          })];\n        }),\n        _: 3\n      }, 8, [\"id\"]), renderSlot(_ctx.$slots, \"arrow\", {\n        style: normalizeStyle(unref(arrowStyle)),\n        side: unref(side)\n      })], 10, _hoisted_1)) : createCommentVNode(\"v-if\", true)], 4);\n    };\n  }\n}));\nvar TooltipV2Content = /* @__PURE__ */_export_sfc(_sfc_main, [[\"__file\", \"content.vue\"]]);\nexport { TooltipV2Content as default };", "map": {"version": 3, "names": ["name", "_inject", "inject", "tooltipV2RootKey", "triggerRef", "contentId", "placement", "ref", "props", "strategy", "arrowRef", "_useFloating", "useFloating", "middleware", "computed", "offset", "showArrow", "push", "arrowMiddleware", "referenceRef", "contentRef", "middlewareData", "x", "y", "update", "zIndex", "useZIndex", "nextZIndex", "ns", "useNamespace", "side", "value", "split", "contentStyle", "position", "unref", "top", "left", "arrowStyle", "_unref", "arrow", "namespace", "contentClass", "e", "is", "effect", "watch", "val", "onMounted", "reference", "el", "immediate", "provide", "tooltipV2ContentKey"], "sources": ["../../../../../../packages/components/tooltip-v2/src/content.vue"], "sourcesContent": ["<template>\n  <div ref=\"contentRef\" :style=\"contentStyle\" data-tooltip-v2-root>\n    <div v-if=\"!nowrap\" :data-side=\"side\" :class=\"contentClass\">\n      <slot :content-style=\"contentStyle\" :content-class=\"contentClass\" />\n      <el-visually-hidden :id=\"contentId\" role=\"tooltip\">\n        <template v-if=\"ariaLabel\">\n          {{ ariaLabel }}\n        </template>\n        <slot v-else />\n      </el-visually-hidden>\n      <slot name=\"arrow\" :style=\"arrowStyle\" :side=\"side\" />\n    </div>\n  </div>\n</template>\n\n<script setup lang=\"ts\">\nimport { computed, inject, onMounted, provide, ref, unref, watch } from 'vue'\nimport { offset } from '@floating-ui/dom'\nimport {\n  arrowMiddleware,\n  useFloating,\n  useNamespace,\n  useZIndex,\n} from '@element-plus/hooks'\nimport ElVisuallyHidden from '@element-plus/components/visual-hidden'\nimport { tooltipV2ContentKey, tooltipV2RootKey } from './constants'\nimport { tooltipV2ContentProps } from './content'\nimport { tooltipV2CommonProps } from './common'\n\nimport type { CSSProperties } from 'vue'\nimport type { Middleware } from '@floating-ui/dom'\n\ndefineOptions({\n  name: 'ElTooltipV2Content',\n})\n\nconst props = defineProps({ ...tooltipV2ContentProps, ...tooltipV2CommonProps })\n\nconst { triggerRef, contentId } = inject(tooltipV2RootKey)!\n\nconst placement = ref(props.placement)\nconst strategy = ref(props.strategy)\nconst arrowRef = ref<HTMLElement | null>(null)\n\nconst { referenceRef, contentRef, middlewareData, x, y, update } = useFloating({\n  placement,\n  strategy,\n  middleware: computed(() => {\n    const middleware: Middleware[] = [offset(props.offset)]\n\n    if (props.showArrow) {\n      middleware.push(\n        arrowMiddleware({\n          arrowRef,\n        })\n      )\n    }\n\n    return middleware\n  }),\n})\n\nconst zIndex = useZIndex().nextZIndex()\n\nconst ns = useNamespace('tooltip-v2')\n\nconst side = computed(() => {\n  return placement.value.split('-')[0]\n})\n\nconst contentStyle = computed<CSSProperties>(() => {\n  return {\n    position: unref(strategy),\n    top: `${unref(y) || 0}px`,\n    left: `${unref(x) || 0}px`,\n    zIndex,\n  }\n})\n\nconst arrowStyle = computed<CSSProperties>(() => {\n  if (!props.showArrow) return {}\n\n  const { arrow } = unref(middlewareData)\n\n  return {\n    [`--${ns.namespace.value}-tooltip-v2-arrow-x`]: `${arrow?.x}px` || '',\n    [`--${ns.namespace.value}-tooltip-v2-arrow-y`]: `${arrow?.y}px` || '',\n  }\n})\n\nconst contentClass = computed(() => [\n  ns.e('content'),\n  ns.is('dark', props.effect === 'dark'),\n  ns.is(unref(strategy)),\n  props.contentClass,\n])\n\nwatch(arrowRef, () => update())\n\nwatch(\n  () => props.placement,\n  (val) => (placement.value = val)\n)\n\nonMounted(() => {\n  watch(\n    () => props.reference || triggerRef.value,\n    (el) => {\n      referenceRef.value = el || undefined\n    },\n    {\n      immediate: true,\n    }\n  )\n})\n\nprovide(tooltipV2ContentKey, { arrowRef })\n</script>\n"], "mappings": ";;;;;;;;;;;;;;;;;;iCAgCc;EACZA,IAAM;AACR;;;;;IAIA,IAAAC,OAAA,GAAkCC,MAAA,CAAOC,gBAAgB;MAAjDC,UAAA,GAAAH,OAAA,CAAAG,UAAA;MAAYC,SAAc,GAAAJ,OAAA,CAAdI,SAAc;IAE5B,IAAAC,SAAA,GAAYC,GAAI,CAAAC,KAAA,CAAMF,SAAS;IAC/B,IAAAG,QAAA,GAAWF,GAAI,CAAAC,KAAA,CAAMC,QAAQ;IAC7B,IAAAC,QAAA,GAAWH,GAAA,CAAwB,IAAI;IAE7C,IAAAI,YAAA,GAAmEC,WAAY;QAC7EN,SAAA;QACAG,QAAA;QACAI,UAAA,EAAYC,QAAA,CAAS,YAAM;UACzB,IAAMD,UAA2B,IAACE,MAAO,CAAAP,KAAA,CAAMO,MAAM,CAAC;UAEtD,IAAIP,KAAA,CAAMQ,SAAW;YACnBH,UAAA,CAAWI,IAAA,CACTC,eAAgB;cACdR;YAAA,CACD,CACH;UAAA;UAGK,OAAAG,UAAA;QAAA,CACR;MAAA,CACF;MAhBOM,YAAc,GAAAR,YAAA,CAAdQ,YAAc;MAAAC,UAAA,GAAAT,YAAA,CAAAS,UAAA;MAAYC,cAAA,GAAAV,YAAA,CAAAU,cAAA;MAAgBC,CAAG,GAAAX,YAAA,CAAHW,CAAG;MAAAC,CAAA,GAAAZ,YAAA,CAAAY,CAAA;MAAGC,MAAA,GAAAb,YAAA,CAAAa,MAAA;IAkBlD,IAAAC,MAAA,GAASC,SAAU,GAAEC,UAAW;IAEhC,IAAAC,EAAA,GAAKC,YAAA,CAAa,YAAY;IAE9B,IAAAC,IAAA,GAAOhB,QAAA,CAAS,YAAM;MAC1B,OAAOR,SAAU,CAAAyB,KAAA,CAAMC,KAAM,IAAG,CAAE;IAAA,CACnC;IAEK,IAAAC,YAAA,GAAenB,QAAA,CAAwB,YAAM;MAC1C;QACLoB,QAAA,EAAUC,KAAA,CAAM1B,QAAQ;QACxB2B,GAAK,KAAGD,KAAM,CAAAZ,CAAC,CAAK;QACpBc,IAAM,KAAGF,KAAM,CAAAb,CAAC,CAAK;QACrBG;MAAA,CACF;IAAA,CACD;IAEK,IAAAa,UAAA,GAAaxB,QAAA,CAAwB,YAAM;MAC/C,IAAI,CAACN,KAAM,CAAAQ,SAAA,EAAW,OAAO,EAAC;MAExB,IAAAuB,MAAA,GAAYJ,KAAA,CAAMd,cAAc;QAA9BmB,KAAU,GAAAD,MAAA,CAAVC,KAAU;MAEX;QACL,CAAC,KAAKZ,EAAA,CAAGa,SAAA,CAAUV,KAA6B,2BAAGS,KAAA,IAAgB,gBAAAA,KAAA,CAAAlB,CAAA;QACnE,CAAC,KAAKM,EAAA,CAAGa,SAAA,CAAUV,KAA6B,2BAAGS,KAAA,IAAgB,gBAAAA,KAAA,CAAAjB,CAAA;MAAA,CACrE;IAAA,CACD;IAEK,IAAAmB,YAAA,GAAe5B,QAAA,CAAS;MAAA,OAAM,CAClCc,EAAA,CAAGe,CAAA,CAAE,SAAS,GACdf,EAAG,CAAAgB,EAAA,CAAG,MAAQ,EAAApC,KAAA,CAAMqC,MAAA,KAAW,MAAM,GACrCjB,EAAG,CAAAgB,EAAA,CAAGT,KAAM,CAAA1B,QAAQ,CAAC,GACrBD,KAAM,CAAAkC,YAAA,CACP;IAAA;IAEKI,KAAA,CAAApC,QAAA,EAAU;MAAA,OAAMc,MAAA,EAAQ;IAAA;IAE9BsB,KAAA,CACE;MAAA,OAAMtC,KAAM,CAAAF,SAAA;IAAA,GACZ,UAACyC,GAAS;MAAA,OAAAzC,SAAA,CAAUyB,KAAA,GAAQgB,GAC9B;IAAA;IAEAC,SAAA,CAAU,YAAM;MACdF,KAAA,CACE;QAAA,OAAMtC,KAAM,CAAAyC,SAAA,IAAa7C,UAAW,CAAA2B,KAAA;MAAA,GACpC,UAACmB,EAAO;QACN/B,YAAA,CAAaY,KAAA,GAAQmB,EAAM;MAAA,CAE7B;QACEC,SAAW;MAAA,CAEf;IAAA,CACD;IAEOC,OAAA,CAAAC,mBAAA,EAAqB;MAAE3C;IAAA,CAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "import '../../../../utils/index.mjs';\nimport { isClient } from '@vueuse/core';\nvar isDragging = false;\nfunction draggable(element, options) {\n  if (!isClient) return;\n  var moveFn = function moveFn(event) {\n    var _a;\n    (_a = options.drag) == null ? void 0 : _a.call(options, event);\n  };\n  var _upFn = function upFn(event) {\n    var _a;\n    document.removeEventListener(\"mousemove\", moveFn);\n    document.removeEventListener(\"mouseup\", _upFn);\n    document.removeEventListener(\"touchmove\", moveFn);\n    document.removeEventListener(\"touchend\", _upFn);\n    document.onselectstart = null;\n    document.ondragstart = null;\n    isDragging = false;\n    (_a = options.end) == null ? void 0 : _a.call(options, event);\n  };\n  var downFn = function downFn(event) {\n    var _a;\n    if (isDragging) return;\n    event.preventDefault();\n    document.onselectstart = function () {\n      return false;\n    };\n    document.ondragstart = function () {\n      return false;\n    };\n    document.addEventListener(\"mousemove\", moveFn);\n    document.addEventListener(\"mouseup\", _upFn);\n    document.addEventListener(\"touchmove\", moveFn);\n    document.addEventListener(\"touchend\", _upFn);\n    isDragging = true;\n    (_a = options.start) == null ? void 0 : _a.call(options, event);\n  };\n  element.addEventListener(\"mousedown\", downFn);\n  element.addEventListener(\"touchstart\", downFn);\n}\nexport { draggable };", "map": {"version": 3, "names": ["isDragging", "draggable", "element", "options", "isClient", "moveFn", "event", "_a", "drag", "call", "upFn", "document", "removeEventListener", "onselectstart", "ondragstart", "end", "downFn", "preventDefault", "addEventListener", "start"], "sources": ["../../../../../../../packages/components/color-picker/src/utils/draggable.ts"], "sourcesContent": ["import { isClient } from '@element-plus/utils'\n\nlet isDragging = false\n\nexport interface DraggableOptions {\n  drag?: (event: MouseEvent | TouchEvent) => void\n  start?: (event: MouseEvent | TouchEvent) => void\n  end?: (event: MouseEvent | TouchEvent) => void\n}\n\nexport function draggable(element: HTMLElement, options: DraggableOptions) {\n  if (!isClient) return\n\n  const moveFn = function (event: MouseEvent | TouchEvent) {\n    options.drag?.(event)\n  }\n\n  const upFn = function (event: MouseEvent | TouchEvent) {\n    document.removeEventListener('mousemove', moveFn)\n    document.removeEventListener('mouseup', upFn)\n    document.removeEventListener('touchmove', moveFn)\n    document.removeEventListener('touchend', upFn)\n    document.onselectstart = null\n    document.ondragstart = null\n\n    isDragging = false\n\n    options.end?.(event)\n  }\n\n  const downFn = function (event: MouseEvent | TouchEvent) {\n    if (isDragging) return\n    event.preventDefault()\n    document.onselectstart = () => false\n    document.ondragstart = () => false\n    document.addEventListener('mousemove', moveFn)\n    document.addEventListener('mouseup', upFn)\n    document.addEventListener('touchmove', moveFn)\n    document.addEventListener('touchend', upFn)\n\n    isDragging = true\n\n    options.start?.(event)\n  }\n\n  element.addEventListener('mousedown', downFn)\n  element.addEventListener('touchstart', downFn)\n}\n"], "mappings": ";;AACA,IAAIA,UAAU,GAAG,KAAK;AACf,SAASC,SAASA,CAACC,OAAO,EAAEC,OAAO,EAAE;EAC1C,IAAI,CAACC,QAAQ,EACX;EACF,IAAMC,MAAM,GAAG,SAATA,MAAMA,CAAYC,KAAK,EAAE;IAC7B,IAAIC,EAAE;IACN,CAACA,EAAE,GAAGJ,OAAO,CAACK,IAAI,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGD,EAAE,CAACE,IAAI,CAACN,OAAO,EAAEG,KAAK,CAAC;EAClE,CAAG;EACD,IAAMI,KAAI,GAAG,SAAPA,IAAIA,CAAYJ,KAAK,EAAE;IAC3B,IAAIC,EAAE;IACNI,QAAQ,CAACC,mBAAmB,CAAC,WAAW,EAAEP,MAAM,CAAC;IACjDM,QAAQ,CAACC,mBAAmB,CAAC,SAAS,EAAEF,KAAI,CAAC;IAC7CC,QAAQ,CAACC,mBAAmB,CAAC,WAAW,EAAEP,MAAM,CAAC;IACjDM,QAAQ,CAACC,mBAAmB,CAAC,UAAU,EAAEF,KAAI,CAAC;IAC9CC,QAAQ,CAACE,aAAa,GAAG,IAAI;IAC7BF,QAAQ,CAACG,WAAW,GAAG,IAAI;IAC3Bd,UAAU,GAAG,KAAK;IAClB,CAACO,EAAE,GAAGJ,OAAO,CAACY,GAAG,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGR,EAAE,CAACE,IAAI,CAACN,OAAO,EAAEG,KAAK,CAAC;EACjE,CAAG;EACD,IAAMU,MAAM,GAAG,SAATA,MAAMA,CAAYV,KAAK,EAAE;IAC7B,IAAIC,EAAE;IACN,IAAIP,UAAU,EACZ;IACFM,KAAK,CAACW,cAAc,EAAE;IACtBN,QAAQ,CAACE,aAAa,GAAG;MAAA,OAAM,KAAK;IAAA;IACpCF,QAAQ,CAACG,WAAW,GAAG;MAAA,OAAM,KAAK;IAAA;IAClCH,QAAQ,CAACO,gBAAgB,CAAC,WAAW,EAAEb,MAAM,CAAC;IAC9CM,QAAQ,CAACO,gBAAgB,CAAC,SAAS,EAAER,KAAI,CAAC;IAC1CC,QAAQ,CAACO,gBAAgB,CAAC,WAAW,EAAEb,MAAM,CAAC;IAC9CM,QAAQ,CAACO,gBAAgB,CAAC,UAAU,EAAER,KAAI,CAAC;IAC3CV,UAAU,GAAG,IAAI;IACjB,CAACO,EAAE,GAAGJ,OAAO,CAACgB,KAAK,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGZ,EAAE,CAACE,IAAI,CAACN,OAAO,EAAEG,KAAK,CAAC;EACnE,CAAG;EACDJ,OAAO,CAACgB,gBAAgB,CAAC,WAAW,EAAEF,MAAM,CAAC;EAC7Cd,OAAO,CAACgB,gBAAgB,CAAC,YAAY,EAAEF,MAAM,CAAC;AAChD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "import { resolveComponent as _resolveComponent, createVNode as _createVNode, withCtx as _withCtx, createElementVNode as _createElementVNode, createTextVNode as _createTextVNode, openBlock as _openBlock, createElementBlock as _createElementBlock } from \"vue\";\nvar _hoisted_1 = {\n  class: \"SendTextMessageModifyContent\"\n};\nvar _hoisted_2 = {\n  class: \"globalFormButton\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  var _component_el_input = _resolveComponent(\"el-input\");\n  var _component_el_form_item = _resolveComponent(\"el-form-item\");\n  var _component_el_button = _resolveComponent(\"el-button\");\n  var _component_el_form = _resolveComponent(\"el-form\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createVNode(_component_el_form, {\n    ref: \"formRef\",\n    model: $setup.form,\n    rules: $setup.rules,\n    inline: \"\",\n    \"label-position\": \"top\",\n    class: \"globalForm\"\n  }, {\n    default: _withCtx(function () {\n      return [_createVNode(_component_el_form_item, {\n        label: \"原短信内容\",\n        class: \"globalFormTitle\"\n      }, {\n        default: _withCtx(function () {\n          return [_createVNode(_component_el_input, {\n            modelValue: $setup.form.oldContent,\n            \"onUpdate:modelValue\": _cache[0] || (_cache[0] = function ($event) {\n              return $setup.form.oldContent = $event;\n            }),\n            disabled: \"\",\n            type: \"textarea\",\n            rows: \"6\"\n          }, null, 8 /* PROPS */, [\"modelValue\"])];\n        }),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_form_item, {\n        label: \"修改后内容\",\n        class: \"globalFormTitle\",\n        prop: \"content\"\n      }, {\n        label: _withCtx(function () {\n          return _cache[3] || (_cache[3] = [_createElementVNode(\"span\", null, \"修改后内容\", -1 /* HOISTED */), _createElementVNode(\"span\", {\n            style: {\n              \"color\": \"red\",\n              \"margin-left\": \"20px\"\n            }\n          }, \"注：若短信内容包括姓名等一对一信息，不支持批量修改\", -1 /* HOISTED */)]);\n        }),\n        default: _withCtx(function () {\n          return [_createVNode(_component_el_input, {\n            modelValue: $setup.form.content,\n            \"onUpdate:modelValue\": _cache[1] || (_cache[1] = function ($event) {\n              return $setup.form.content = $event;\n            }),\n            placeholder: \"请输入短信内容\",\n            type: \"textarea\",\n            rows: \"6\"\n          }, null, 8 /* PROPS */, [\"modelValue\"])];\n        }),\n        _: 1 /* STABLE */\n      }), _createElementVNode(\"div\", _hoisted_2, [_createVNode(_component_el_button, {\n        type: \"primary\",\n        onClick: _cache[2] || (_cache[2] = function ($event) {\n          return $setup.submitForm($setup.formRef);\n        })\n      }, {\n        default: _withCtx(function () {\n          return _cache[4] || (_cache[4] = [_createTextVNode(\"提交\")]);\n        }),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_button, {\n        onClick: $setup.resetForm\n      }, {\n        default: _withCtx(function () {\n          return _cache[5] || (_cache[5] = [_createTextVNode(\"取消\")]);\n        }),\n        _: 1 /* STABLE */\n      })])];\n    }),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"model\", \"rules\"])]);\n}", "map": {"version": 3, "names": ["class", "_createElementBlock", "_hoisted_1", "_createVNode", "_component_el_form", "ref", "model", "$setup", "form", "rules", "inline", "default", "_withCtx", "_component_el_form_item", "label", "_component_el_input", "modelValue", "<PERSON><PERSON><PERSON><PERSON>", "_cache", "$event", "disabled", "type", "rows", "_", "prop", "_createElementVNode", "style", "content", "placeholder", "_hoisted_2", "_component_el_button", "onClick", "submitForm", "formRef", "_createTextVNode", "resetForm"], "sources": ["D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\interaction\\src\\views\\TextMessageManage\\component\\SendTextMessageModifyContent.vue"], "sourcesContent": ["<template>\r\n  <div class=\"SendTextMessageModifyContent\">\r\n    <el-form ref=\"formRef\"\r\n             :model=\"form\"\r\n             :rules=\"rules\"\r\n             inline\r\n             label-position=\"top\"\r\n             class=\"globalForm\">\r\n      <el-form-item label=\"原短信内容\"\r\n                    class=\"globalFormTitle\">\r\n        <el-input v-model=\"form.oldContent\"\r\n                  disabled\r\n                  type=\"textarea\"\r\n                  rows=\"6\" />\r\n      </el-form-item>\r\n      <el-form-item label=\"修改后内容\"\r\n                    class=\"globalFormTitle\"\r\n                    prop=\"content\">\r\n        <template #label>\r\n          <span>修改后内容</span><span style=\"color:red;margin-left: 20px;\">注：若短信内容包括姓名等一对一信息，不支持批量修改</span>\r\n        </template>\r\n        <el-input v-model=\"form.content\"\r\n                  placeholder=\"请输入短信内容\"\r\n                  type=\"textarea\"\r\n                  rows=\"6\" />\r\n      </el-form-item>\r\n      <div class=\"globalFormButton\">\r\n        <el-button type=\"primary\"\r\n                   @click=\"submitForm(formRef)\">提交</el-button>\r\n        <el-button @click=\"resetForm\">取消</el-button>\r\n      </div>\r\n    </el-form>\r\n  </div>\r\n</template>\r\n<script>\r\nexport default { name: 'SendTextMessageModifyContent' }\r\n</script>\r\n<script setup>\r\nimport api from '@/api'\r\nimport { ElMessage } from 'element-plus'\r\nimport { reactive, ref, onMounted } from 'vue'\r\n\r\nconst props = defineProps({\r\n  id: { type: String, default: '' },\r\n  oldContent: { type: String, default: '' }\r\n})\r\nconst emit = defineEmits(['callback'])\r\n\r\nconst formRef = ref()\r\nconst form = reactive({\r\n  oldContent: '',\r\n  content: ''\r\n})\r\nconst rules = reactive({\r\n  content: [{ required: true, message: '请输入短信内容', trigger: ['blur', 'change'] }]\r\n})\r\n\r\nonMounted(() => {\r\n  form.oldContent = props.oldContent\r\n})\r\n\r\nconst submitForm = async (formEl) => {\r\n  if (!formEl) return\r\n  await formEl.validate((valid, fields) => {\r\n    if (valid) { globalJson() } else { ElMessage({ type: 'warning', message: '请根据提示信息完善字段内容！' }) }\r\n  })\r\n}\r\nconst globalJson = async () => {\r\n  const { code } = await api.globalJson('delayTextMessage/editContent', {\r\n    batchId: props.id,\r\n    newContent: form.content\r\n  })\r\n  if (code === 200) {\r\n    ElMessage({ type: 'success', message: '修改成功' })\r\n    emit('callback')\r\n  }\r\n}\r\nconst resetForm = () => { emit('callback') }\r\n</script>\r\n<style lang=\"scss\">\r\n.SendTextMessageModifyContent {\r\n  width: 680px;\r\n}\r\n</style>\r\n"], "mappings": ";;EACOA,KAAK,EAAC;AAA8B;;EAyBhCA,KAAK,EAAC;AAAkB;;;;;;uBAzBjCC,mBAAA,CA+BM,OA/BNC,UA+BM,GA9BJC,YAAA,CA6BUC,kBAAA;IA7BDC,GAAG,EAAC,SAAS;IACZC,KAAK,EAAEC,MAAA,CAAAC,IAAI;IACXC,KAAK,EAAEF,MAAA,CAAAE,KAAK;IACbC,MAAM,EAAN,EAAM;IACN,gBAAc,EAAC,KAAK;IACpBV,KAAK,EAAC;;IAPnBW,OAAA,EAAAC,QAAA,CAQM;MAAA,OAMe,CANfT,YAAA,CAMeU,uBAAA;QANDC,KAAK,EAAC,OAAO;QACbd,KAAK,EAAC;;QAT1BW,OAAA,EAAAC,QAAA,CAUQ;UAAA,OAGqB,CAHrBT,YAAA,CAGqBY,mBAAA;YAb7BC,UAAA,EAU2BT,MAAA,CAAAC,IAAI,CAACS,UAAU;YAV1C,uBAAAC,MAAA,QAAAA,MAAA,gBAAAC,MAAA;cAAA,OAU2BZ,MAAA,CAAAC,IAAI,CAACS,UAAU,GAAAE,MAAA;YAAA;YACxBC,QAAQ,EAAR,EAAQ;YACRC,IAAI,EAAC,UAAU;YACfC,IAAI,EAAC;;;QAbvBC,CAAA;UAeMpB,YAAA,CAUeU,uBAAA;QAVDC,KAAK,EAAC,OAAO;QACbd,KAAK,EAAC,iBAAiB;QACvBwB,IAAI,EAAC;;QACNV,KAAK,EAAAF,QAAA,CACd;UAAA,OAAkBM,MAAA,QAAAA,MAAA,OAAlBO,mBAAA,CAAkB,cAAZ,OAAK,qBAAOA,mBAAA,CAA2E;YAArEC,KAAoC,EAApC;cAAA;cAAA;YAAA;UAAoC,GAAC,2BAAyB,oB;;QAnBhGf,OAAA,EAAAC,QAAA,CAqBQ;UAAA,OAGqB,CAHrBT,YAAA,CAGqBY,mBAAA;YAxB7BC,UAAA,EAqB2BT,MAAA,CAAAC,IAAI,CAACmB,OAAO;YArBvC,uBAAAT,MAAA,QAAAA,MAAA,gBAAAC,MAAA;cAAA,OAqB2BZ,MAAA,CAAAC,IAAI,CAACmB,OAAO,GAAAR,MAAA;YAAA;YACrBS,WAAW,EAAC,SAAS;YACrBP,IAAI,EAAC,UAAU;YACfC,IAAI,EAAC;;;QAxBvBC,CAAA;UA0BME,mBAAA,CAIM,OAJNI,UAIM,GAHJ1B,YAAA,CACsD2B,oBAAA;QAD3CT,IAAI,EAAC,SAAS;QACbU,OAAK,EAAAb,MAAA,QAAAA,MAAA,gBAAAC,MAAA;UAAA,OAAEZ,MAAA,CAAAyB,UAAU,CAACzB,MAAA,CAAA0B,OAAO;QAAA;;QA5B7CtB,OAAA,EAAAC,QAAA,CA4BgD;UAAA,OAAEM,MAAA,QAAAA,MAAA,OA5BlDgB,gBAAA,CA4BgD,IAAE,E;;QA5BlDX,CAAA;UA6BQpB,YAAA,CAA4C2B,oBAAA;QAAhCC,OAAK,EAAExB,MAAA,CAAA4B;MAAS;QA7BpCxB,OAAA,EAAAC,QAAA,CA6BsC;UAAA,OAAEM,MAAA,QAAAA,MAAA,OA7BxCgB,gBAAA,CA6BsC,IAAE,E;;QA7BxCX,CAAA;;;IAAAA,CAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
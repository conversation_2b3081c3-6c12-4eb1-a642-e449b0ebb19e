{"ast": null, "code": "function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nfunction _defineProperty(e, r, t) { return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, { value: t, enumerable: !0, configurable: !0, writable: !0 }) : e[r] = t, e; }\nfunction _toPropertyKey(t) { var i = _toPrimitive(t, \"string\"); return \"symbol\" == typeof i ? i : i + \"\"; }\nfunction _toPrimitive(t, r) { if (\"object\" != typeof t || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || \"default\"); if (\"object\" != typeof i) return i; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (\"string\" === r ? String : Number)(t); }\nimport '../../../utils/index.mjs';\nimport '../../../hooks/index.mjs';\nimport { buildProps, definePropType } from '../../../utils/vue/props/runtime.mjs';\nimport { useAriaProps } from '../../../hooks/use-aria/index.mjs';\nvar tooltipV2Strategies = [\"absolute\", \"fixed\"];\nvar tooltipV2Placements = [\"top-start\", \"top-end\", \"top\", \"bottom-start\", \"bottom-end\", \"bottom\", \"left-start\", \"left-end\", \"left\", \"right-start\", \"right-end\", \"right\"];\nvar tooltipV2ContentProps = buildProps(_objectSpread({\n  arrowPadding: {\n    type: definePropType(Number),\n    default: 5\n  },\n  effect: {\n    type: String,\n    default: \"\"\n  },\n  contentClass: String,\n  placement: {\n    type: definePropType(String),\n    values: tooltipV2Placements,\n    default: \"bottom\"\n  },\n  reference: {\n    type: definePropType(Object),\n    default: null\n  },\n  offset: {\n    type: Number,\n    default: 8\n  },\n  strategy: {\n    type: definePropType(String),\n    values: tooltipV2Strategies,\n    default: \"absolute\"\n  },\n  showArrow: {\n    type: Boolean,\n    default: false\n  }\n}, useAriaProps([\"ariaLabel\"])));\nexport { tooltipV2ContentProps };", "map": {"version": 3, "names": ["tooltipV2Strategies", "tooltipV2Placements", "tooltipV2ContentProps", "buildProps", "_objectSpread", "arrowPadding", "type", "definePropType", "Number", "default", "effect", "String", "contentClass", "placement", "values", "reference", "Object", "offset", "strategy", "showArrow", "Boolean", "useAriaProps"], "sources": ["../../../../../../packages/components/tooltip-v2/src/content.ts"], "sourcesContent": ["import { buildProps, definePropType } from '@element-plus/utils'\nimport { useAriaProps } from '@element-plus/hooks'\n\nimport type { ExtractPropTypes } from 'vue'\nimport type { Placement, Strategy, VirtualElement } from '@floating-ui/dom'\n\nconst tooltipV2Strategies = ['absolute', 'fixed'] as const\n\nconst tooltipV2Placements = [\n  'top-start',\n  'top-end',\n  'top',\n  'bottom-start',\n  'bottom-end',\n  'bottom',\n  'left-start',\n  'left-end',\n  'left',\n  'right-start',\n  'right-end',\n  'right',\n] as const\n\nexport const tooltipV2ContentProps = buildProps({\n  arrowPadding: {\n    type: definePropType<number>(Number),\n    default: 5,\n  },\n  effect: {\n    type: String,\n    default: '',\n  },\n  contentClass: String,\n  /**\n   * Placement of tooltip content relative to reference element (when absent it refers to trigger)\n   */\n  placement: {\n    type: definePropType<Placement>(String),\n    values: tooltipV2Placements,\n    default: 'bottom',\n  },\n  /**\n   * Reference element for tooltip content to set its position\n   */\n  reference: {\n    type: definePropType<HTMLElement | VirtualElement | null>(Object),\n    default: null,\n  },\n  offset: {\n    type: Number,\n    default: 8,\n  },\n  strategy: {\n    type: definePropType<Strategy>(String),\n    values: tooltipV2Strategies,\n    default: 'absolute',\n  },\n  showArrow: {\n    type: Boolean,\n    default: false,\n  },\n  ...useAriaProps(['ariaLabel']),\n} as const)\n\nexport type TooltipV2ContentProps = ExtractPropTypes<\n  typeof tooltipV2ContentProps\n>\n"], "mappings": ";;;;;;;;;AAEA,IAAMA,mBAAmB,GAAG,CAAC,UAAU,EAAE,OAAO,CAAC;AACjD,IAAMC,mBAAmB,GAAG,CAC1B,WAAW,EACX,SAAS,EACT,KAAK,EACL,cAAc,EACd,YAAY,EACZ,QAAQ,EACR,YAAY,EACZ,UAAU,EACV,MAAM,EACN,aAAa,EACb,WAAW,EACX,OAAO,CACR;AACW,IAACC,qBAAqB,GAAGC,UAAU,CAAAC,aAAA;EAC7CC,YAAY,EAAE;IACZC,IAAI,EAAEC,cAAc,CAACC,MAAM,CAAC;IAC5BC,OAAO,EAAE;EACb,CAAG;EACDC,MAAM,EAAE;IACNJ,IAAI,EAAEK,MAAM;IACZF,OAAO,EAAE;EACb,CAAG;EACDG,YAAY,EAAED,MAAM;EACpBE,SAAS,EAAE;IACTP,IAAI,EAAEC,cAAc,CAACI,MAAM,CAAC;IAC5BG,MAAM,EAAEb,mBAAmB;IAC3BQ,OAAO,EAAE;EACb,CAAG;EACDM,SAAS,EAAE;IACTT,IAAI,EAAEC,cAAc,CAACS,MAAM,CAAC;IAC5BP,OAAO,EAAE;EACb,CAAG;EACDQ,MAAM,EAAE;IACNX,IAAI,EAAEE,MAAM;IACZC,OAAO,EAAE;EACb,CAAG;EACDS,QAAQ,EAAE;IACRZ,IAAI,EAAEC,cAAc,CAACI,MAAM,CAAC;IAC5BG,MAAM,EAAEd,mBAAmB;IAC3BS,OAAO,EAAE;EACb,CAAG;EACDU,SAAS,EAAE;IACTb,IAAI,EAAEc,OAAO;IACbX,OAAO,EAAE;EACb;AAAG,GACEY,YAAY,CAAC,CAAC,WAAW,CAAC,CAAC,CAC/B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
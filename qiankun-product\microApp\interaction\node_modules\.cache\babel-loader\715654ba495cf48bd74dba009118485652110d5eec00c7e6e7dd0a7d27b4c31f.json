{"ast": null, "code": "import { defineComponent, inject, openBlock, createElement<PERSON>lock, normalizeStyle, normalizeClass, withModifiers, renderSlot, createElementVNode, toDisplayString } from 'vue';\nimport '../../../hooks/index.mjs';\nimport { useOption } from './useOption.mjs';\nimport { useProps } from './useProps.mjs';\nimport { OptionProps } from './defaults.mjs';\nimport { selectV2InjectionKey } from './token.mjs';\nimport _export_sfc from '../../../_virtual/plugin-vue_export-helper.mjs';\nimport { useNamespace } from '../../../hooks/use-namespace/index.mjs';\nvar _sfc_main = defineComponent({\n  props: OptionProps,\n  emits: [\"select\", \"hover\"],\n  setup(props, _ref) {\n    var emit = _ref.emit;\n    var select = inject(selectV2InjectionKey);\n    var ns = useNamespace(\"select\");\n    var _useOption = useOption(props, {\n        emit\n      }),\n      hoverItem = _useOption.hoverItem,\n      selectOptionClick = _useOption.selectOptionClick;\n    var _useProps = useProps(select.props),\n      getLabel = _useProps.getLabel;\n    return {\n      ns,\n      hoverItem,\n      selectOptionClick,\n      getLabel\n    };\n  }\n});\nvar _hoisted_1 = [\"aria-selected\"];\nfunction _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {\n  return openBlock(), createElementBlock(\"li\", {\n    \"aria-selected\": _ctx.selected,\n    style: normalizeStyle(_ctx.style),\n    class: normalizeClass([_ctx.ns.be(\"dropdown\", \"item\"), _ctx.ns.is(\"selected\", _ctx.selected), _ctx.ns.is(\"disabled\", _ctx.disabled), _ctx.ns.is(\"created\", _ctx.created), _ctx.ns.is(\"hovering\", _ctx.hovering)]),\n    onMouseenter: _cache[0] || (_cache[0] = function () {\n      return _ctx.hoverItem && _ctx.hoverItem.apply(_ctx, arguments);\n    }),\n    onClick: _cache[1] || (_cache[1] = withModifiers(function () {\n      return _ctx.selectOptionClick && _ctx.selectOptionClick.apply(_ctx, arguments);\n    }, [\"stop\"]))\n  }, [renderSlot(_ctx.$slots, \"default\", {\n    item: _ctx.item,\n    index: _ctx.index,\n    disabled: _ctx.disabled\n  }, function () {\n    return [createElementVNode(\"span\", null, toDisplayString(_ctx.getLabel(_ctx.item)), 1)];\n  })], 46, _hoisted_1);\n}\nvar OptionItem = /* @__PURE__ */_export_sfc(_sfc_main, [[\"render\", _sfc_render], [\"__file\", \"option-item.vue\"]]);\nexport { OptionItem as default };", "map": {"version": 3, "names": ["_sfc_main", "defineComponent", "props", "OptionProps", "emits", "setup", "_ref", "emit", "select", "inject", "selectV2InjectionKey", "ns", "useNamespace", "_useOption", "useOption", "hoverItem", "selectOptionClick", "_useProps", "useProps", "get<PERSON><PERSON><PERSON>", "createElementBlock", "_ctx", "selected", "style", "normalizeStyle", "class", "normalizeClass", "be", "is", "disabled", "created", "hovering", "onMouseenter", "_cache", "apply", "arguments", "onClick", "withModifiers", "renderSlot", "$slots", "item", "index", "createElementVNode", "toDisplayString"], "sources": ["../../../../../../packages/components/select-v2/src/option-item.vue"], "sourcesContent": ["<template>\n  <li\n    :aria-selected=\"selected\"\n    :style=\"style\"\n    :class=\"[\n      ns.be('dropdown', 'item'),\n      ns.is('selected', selected),\n      ns.is('disabled', disabled),\n      ns.is('created', created),\n      ns.is('hovering', hovering),\n    ]\"\n    @mouseenter=\"hoverItem\"\n    @click.stop=\"selectOptionClick\"\n  >\n    <slot :item=\"item\" :index=\"index\" :disabled=\"disabled\">\n      <span>{{ getLabel(item) }}</span>\n    </slot>\n  </li>\n</template>\n\n<script lang=\"ts\">\nimport { defineComponent, inject } from 'vue'\nimport { useNamespace } from '@element-plus/hooks'\nimport { useOption } from './useOption'\nimport { useProps } from './useProps'\nimport { OptionProps } from './defaults'\nimport { selectV2InjectionKey } from './token'\n\nexport default defineComponent({\n  props: OptionProps,\n  emits: ['select', 'hover'],\n  setup(props, { emit }) {\n    const select = inject(selectV2InjectionKey)!\n    const ns = useNamespace('select')\n    const { hoverItem, selectOptionClick } = useOption(props, { emit })\n    const { getLabel } = useProps(select.props)\n\n    return {\n      ns,\n      hoverItem,\n      selectOptionClick,\n      getLabel,\n    }\n  },\n})\n</script>\n"], "mappings": ";;;;;;;;AA4BA,IAAKA,SAAA,GAAaC,eAAa;EAC7BC,KAAO,EAAAC,WAAA;EACPC,KAAA,EAAO,CAAC,UAAU,OAAO;EACzBC,MAAMH,KAAO,EAAAI,IAAA,EAAU;IAAA,IAARC,IAAQ,GAAAD,IAAA,CAARC,IAAQ;IACf,IAAAC,MAAA,GAASC,MAAA,CAAOC,oBAAoB;IACpC,IAAAC,EAAA,GAAKC,YAAA,CAAa,QAAQ;IAChC,IAAAC,UAAA,GAAyCC,SAAA,CAAUZ,KAAO;QAAEK;MAAA,CAAM;MAA1DQ,SAAW,GAAAF,UAAA,CAAXE,SAAW;MAAAC,iBAAA,GAAAH,UAAA,CAAAG,iBAAA;IACnB,IAAAC,SAAA,GAAqBC,QAAS,CAAAV,MAAA,CAAON,KAAK;MAAlCiB,QAAA,GAAAF,SAAA,CAAAE,QAAA;IAED;MACLR,EAAA;MACAI,SAAA;MACAC,iBAAA;MACAG;IAAA,CACF;EAAA;AAEJ,CAAC;;;sBA3CCC,kBAgBK;IAfF,eAAe,EAAAC,IAAA,CAAAC,QAAA;IACfC,KAAA,EAAKC,cAAA,CAAEH,IAAK,CAAAE,KAAA;IACZE,KAAK,EAAAC,cAAA,EAAUL,IAAA,CAAAV,EAAA,CAAGgB,EAAE,sBAA4BN,IAAA,CAAAV,EAAA,CAAGiB,EAAE,aAAaP,IAAQ,CAAAC,QAAA,GAASD,IAAA,CAAAV,EAAA,CAAGiB,EAAE,aAAaP,IAAQ,CAAAQ,QAAA,GAASR,IAAA,CAAAV,EAAA,CAAGiB,EAAE,YAAYP,IAAO,CAAAS,OAAA,GAAST,IAAA,CAAAV,EAAA,CAAGiB,EAAE,aAAaP,IAAQ,CAAAU,QAAA;IAOjLC,YAAA,EAAUC,MAAE,QAAAA,MAAA;MAAA,OAAAZ,IAAA,CAAAN,SAAA,IAAAM,IAAA,CAAAN,SAAA,CAAAmB,KAAA,CAAAb,IAAA,EAAAc,SAAA;IAAA;IACZC,OAAA,EAAKH,MAAA,QAAAA,MAAA,MAAAI,aAAA;MAAA,OAAOhB,IAAiB,CAAAL,iBAAA,IAAAK,IAAA,CAAAL,iBAAA,CAAAkB,KAAA,CAAAb,IAAA,EAAAc,SAAA;IAAA;EAAA,IAE9BG,UAEO,CAAAjB,IAAA,CAAAkB,MAAA;IAFAC,IAAM,EAAAnB,IAAA,CAAAmB,IAAA;IAAOC,KAAO,EAAApB,IAAA,CAAAoB,KAAA;IAAQZ,QAAU,EAAAR,IAAA,CAAAQ;EAAA,GAA7C;IAAA,OAEO,CADLa,kBAAA,CAAiC,MAAxB,QAAAC,eAAA,CAAAtB,IAAA,CAAAF,QAAA,CAASE,IAAI,CAAAmB,IAAA;EAAA,G", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "function _slicedToArray(r, e) { return _arrayWithHoles(r) || _iterableToArrayLimit(r, e) || _unsupportedIterableToArray(r, e) || _nonIterableRest(); }\nfunction _nonIterableRest() { throw new TypeError(\"Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); }\nfunction _unsupportedIterableToArray(r, a) { if (r) { if (\"string\" == typeof r) return _arrayLikeToArray(r, a); var t = {}.toString.call(r).slice(8, -1); return \"Object\" === t && r.constructor && (t = r.constructor.name), \"Map\" === t || \"Set\" === t ? Array.from(r) : \"Arguments\" === t || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t) ? _arrayLikeToArray(r, a) : void 0; } }\nfunction _arrayLikeToArray(r, a) { (null == a || a > r.length) && (a = r.length); for (var e = 0, n = Array(a); e < a; e++) n[e] = r[e]; return n; }\nfunction _iterableToArrayLimit(r, l) { var t = null == r ? null : \"undefined\" != typeof Symbol && r[Symbol.iterator] || r[\"@@iterator\"]; if (null != t) { var e, n, i, u, a = [], f = !0, o = !1; try { if (i = (t = t.call(r)).next, 0 === l) { if (Object(t) !== t) return; f = !1; } else for (; !(f = (e = i.call(t)).done) && (a.push(e.value), a.length !== l); f = !0); } catch (r) { o = !0, n = r; } finally { try { if (!f && null != t.return && (u = t.return(), Object(u) !== u)) return; } finally { if (o) throw n; } } return a; } }\nfunction _arrayWithHoles(r) { if (Array.isArray(r)) return r; }\nimport { getCurrentInstance, inject, ref, unref, watch } from 'vue';\nimport '../../../../utils/index.mjs';\nimport '../../../../hooks/index.mjs';\nimport { isValidRange, getDefaultValue } from '../utils.mjs';\nimport { ROOT_PICKER_INJECTION_KEY } from '../constants.mjs';\nimport { useShortcut } from './use-shortcut.mjs';\nimport { useNamespace } from '../../../../hooks/use-namespace/index.mjs';\nimport { useLocale } from '../../../../hooks/use-locale/index.mjs';\nimport { isArray } from '@vue/shared';\nvar useRangePicker = function useRangePicker(props, _ref) {\n  var defaultValue = _ref.defaultValue,\n    leftDate = _ref.leftDate,\n    rightDate = _ref.rightDate,\n    unit = _ref.unit,\n    onParsedValueChanged = _ref.onParsedValueChanged;\n  var _getCurrentInstance = getCurrentInstance(),\n    emit = _getCurrentInstance.emit;\n  var _inject = inject(ROOT_PICKER_INJECTION_KEY),\n    pickerNs = _inject.pickerNs;\n  var drpNs = useNamespace(\"date-range-picker\");\n  var _useLocale = useLocale(),\n    t = _useLocale.t,\n    lang = _useLocale.lang;\n  var handleShortcutClick = useShortcut(lang);\n  var minDate = ref();\n  var maxDate = ref();\n  var rangeState = ref({\n    endDate: null,\n    selecting: false\n  });\n  var handleChangeRange = function handleChangeRange(val) {\n    rangeState.value = val;\n  };\n  var handleRangeConfirm = function handleRangeConfirm() {\n    var visible = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : false;\n    var _minDate = unref(minDate);\n    var _maxDate = unref(maxDate);\n    if (isValidRange([_minDate, _maxDate])) {\n      emit(\"pick\", [_minDate, _maxDate], visible);\n    }\n  };\n  var onSelect = function onSelect(selecting) {\n    rangeState.value.selecting = selecting;\n    if (!selecting) {\n      rangeState.value.endDate = null;\n    }\n  };\n  var restoreDefault = function restoreDefault() {\n    var _getDefaultValue = getDefaultValue(unref(defaultValue), {\n        lang: unref(lang),\n        unit,\n        unlinkPanels: props.unlinkPanels\n      }),\n      _getDefaultValue2 = _slicedToArray(_getDefaultValue, 2),\n      start = _getDefaultValue2[0],\n      end = _getDefaultValue2[1];\n    minDate.value = void 0;\n    maxDate.value = void 0;\n    leftDate.value = start;\n    rightDate.value = end;\n  };\n  watch(defaultValue, function (val) {\n    if (val) {\n      restoreDefault();\n    }\n  }, {\n    immediate: true\n  });\n  watch(function () {\n    return props.parsedValue;\n  }, function (parsedValue) {\n    if (isArray(parsedValue) && parsedValue.length === 2) {\n      var _parsedValue = _slicedToArray(parsedValue, 2),\n        start = _parsedValue[0],\n        end = _parsedValue[1];\n      minDate.value = start;\n      leftDate.value = start;\n      maxDate.value = end;\n      onParsedValueChanged(unref(minDate), unref(maxDate));\n    } else {\n      restoreDefault();\n    }\n  }, {\n    immediate: true\n  });\n  return {\n    minDate,\n    maxDate,\n    rangeState,\n    lang,\n    ppNs: pickerNs,\n    drpNs,\n    handleChangeRange,\n    handleRangeConfirm,\n    handleShortcutClick,\n    onSelect,\n    t\n  };\n};\nexport { useRangePicker };", "map": {"version": 3, "names": ["useRangePicker", "props", "_ref", "defaultValue", "leftDate", "rightDate", "unit", "onParsedValueChanged", "_getCurrentInstance", "getCurrentInstance", "emit", "_inject", "inject", "ROOT_PICKER_INJECTION_KEY", "pickerNs", "drpNs", "useNamespace", "_useLocale", "useLocale", "t", "lang", "handleShortcutClick", "useShortcut", "minDate", "ref", "maxDate", "rangeState", "endDate", "selecting", "handleChangeRange", "val", "value", "handleRangeConfirm", "visible", "arguments", "length", "undefined", "_minDate", "unref", "_maxDate", "isValidRange", "onSelect", "restoreDefault", "_getDefaultValue", "getDefaultValue", "unlinkPanels", "_getDefaultValue2", "_slicedToArray", "start", "end", "watch", "immediate", "parsedValue", "isArray", "_parsedValue", "ppNs"], "sources": ["../../../../../../../packages/components/date-picker/src/composables/use-range-picker.ts"], "sourcesContent": ["import { getCurrentInstance, inject, ref, unref, watch } from 'vue'\nimport { isArray } from '@element-plus/utils'\nimport { useLocale, useNamespace } from '@element-plus/hooks'\nimport { getDefaultValue, isValidRange } from '../utils'\nimport { ROOT_PICKER_INJECTION_KEY } from '../constants'\nimport { useShortcut } from './use-shortcut'\n\nimport type { Ref } from 'vue'\nimport type { Dayjs } from 'dayjs'\nimport type { PanelRangeSharedProps, RangeState } from '../props/shared'\nimport type { DefaultValue } from '../utils'\n\ntype UseRangePickerProps = {\n  onParsedValueChanged: (\n    minDate: Dayjs | undefined,\n    maxDate: Dayjs | undefined\n  ) => void\n  defaultValue: Ref<DefaultValue>\n  leftDate: Ref<Dayjs>\n  rightDate: Ref<Dayjs>\n  unit: 'month' | 'year'\n}\n\nexport const useRangePicker = (\n  props: PanelRangeSharedProps,\n  {\n    defaultValue,\n    leftDate,\n    rightDate,\n    unit,\n\n    onParsedValueChanged,\n  }: UseRangePickerProps\n) => {\n  const { emit } = getCurrentInstance()!\n\n  const { pickerNs } = inject(ROOT_PICKER_INJECTION_KEY)!\n  const drpNs = useNamespace('date-range-picker')\n  const { t, lang } = useLocale()\n  const handleShortcutClick = useShortcut(lang)\n  const minDate = ref<Dayjs>()\n  const maxDate = ref<Dayjs>()\n  const rangeState = ref<RangeState>({\n    endDate: null,\n    selecting: false,\n  })\n\n  const handleChangeRange = (val: RangeState) => {\n    rangeState.value = val\n  }\n\n  const handleRangeConfirm = (visible = false) => {\n    const _minDate = unref(minDate)\n    const _maxDate = unref(maxDate)\n\n    if (isValidRange([_minDate, _maxDate])) {\n      emit('pick', [_minDate, _maxDate], visible)\n    }\n  }\n\n  const onSelect = (selecting: boolean) => {\n    rangeState.value.selecting = selecting\n    if (!selecting) {\n      rangeState.value.endDate = null\n    }\n  }\n\n  const restoreDefault = () => {\n    const [start, end] = getDefaultValue(unref(defaultValue), {\n      lang: unref(lang),\n      unit,\n      unlinkPanels: props.unlinkPanels,\n    })\n    minDate.value = undefined\n    maxDate.value = undefined\n    leftDate.value = start\n    rightDate.value = end\n  }\n\n  watch(\n    defaultValue,\n    (val) => {\n      if (val) {\n        restoreDefault()\n      }\n    },\n    { immediate: true }\n  )\n\n  watch(\n    () => props.parsedValue,\n    (parsedValue) => {\n      if (isArray(parsedValue) && parsedValue.length === 2) {\n        const [start, end] = parsedValue\n        minDate.value = start\n        leftDate.value = start\n        maxDate.value = end\n        onParsedValueChanged(unref(minDate), unref(maxDate))\n      } else {\n        restoreDefault()\n      }\n    },\n    { immediate: true }\n  )\n\n  return {\n    minDate,\n    maxDate,\n    rangeState,\n    lang,\n    ppNs: pickerNs,\n    drpNs,\n\n    handleChangeRange,\n    handleRangeConfirm,\n    handleShortcutClick,\n    onSelect,\n    t,\n  }\n}\n"], "mappings": ";;;;;;;;;;;;;;;AAMY,IAACA,cAAc,GAAG,SAAjBA,cAAcA,CAAIC,KAAK,EAAAC,IAAA,EAM9B;EAAA,IALJC,YAAY,GAAAD,IAAA,CAAZC,YAAY;IACZC,QAAQ,GAAAF,IAAA,CAARE,QAAQ;IACRC,SAAS,GAAAH,IAAA,CAATG,SAAS;IACTC,IAAI,GAAAJ,IAAA,CAAJI,IAAI;IACJC,oBAAoB,GAAAL,IAAA,CAApBK,oBAAoB;EAEpB,IAAAC,mBAAA,GAAiBC,kBAAkB,EAAE;IAA7BC,IAAI,GAAAF,mBAAA,CAAJE,IAAI;EACZ,IAAAC,OAAA,GAAqBC,MAAM,CAACC,yBAAyB,CAAC;IAA9CC,QAAQ,GAAAH,OAAA,CAARG,QAAQ;EAChB,IAAMC,KAAK,GAAGC,YAAY,CAAC,mBAAmB,CAAC;EAC/C,IAAAC,UAAA,GAAoBC,SAAS,EAAE;IAAvBC,CAAC,GAAAF,UAAA,CAADE,CAAC;IAAEC,IAAI,GAAAH,UAAA,CAAJG,IAAI;EACf,IAAMC,mBAAmB,GAAGC,WAAW,CAACF,IAAI,CAAC;EAC7C,IAAMG,OAAO,GAAGC,GAAG,EAAE;EACrB,IAAMC,OAAO,GAAGD,GAAG,EAAE;EACrB,IAAME,UAAU,GAAGF,GAAG,CAAC;IACrBG,OAAO,EAAE,IAAI;IACbC,SAAS,EAAE;EACf,CAAG,CAAC;EACF,IAAMC,iBAAiB,GAAG,SAApBA,iBAAiBA,CAAIC,GAAG,EAAK;IACjCJ,UAAU,CAACK,KAAK,GAAGD,GAAG;EAC1B,CAAG;EACD,IAAME,kBAAkB,GAAG,SAArBA,kBAAkBA,CAAA,EAAwB;IAAA,IAApBC,OAAO,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,KAAK;IACzC,IAAMG,QAAQ,GAAGC,KAAK,CAACf,OAAO,CAAC;IAC/B,IAAMgB,QAAQ,GAAGD,KAAK,CAACb,OAAO,CAAC;IAC/B,IAAIe,YAAY,CAAC,CAACH,QAAQ,EAAEE,QAAQ,CAAC,CAAC,EAAE;MACtC7B,IAAI,CAAC,MAAM,EAAE,CAAC2B,QAAQ,EAAEE,QAAQ,CAAC,EAAEN,OAAO,CAAC;IACjD;EACA,CAAG;EACD,IAAMQ,QAAQ,GAAG,SAAXA,QAAQA,CAAIb,SAAS,EAAK;IAC9BF,UAAU,CAACK,KAAK,CAACH,SAAS,GAAGA,SAAS;IACtC,IAAI,CAACA,SAAS,EAAE;MACdF,UAAU,CAACK,KAAK,CAACJ,OAAO,GAAG,IAAI;IACrC;EACA,CAAG;EACD,IAAMe,cAAc,GAAG,SAAjBA,cAAcA,CAAA,EAAS;IAC3B,IAAAC,gBAAA,GAAqBC,eAAe,CAACN,KAAK,CAACnC,YAAY,CAAC,EAAE;QACxDiB,IAAI,EAAEkB,KAAK,CAAClB,IAAI,CAAC;QACjBd,IAAI;QACJuC,YAAY,EAAE5C,KAAK,CAAC4C;MAC1B,CAAK,CAAC;MAAAC,iBAAA,GAAAC,cAAA,CAAAJ,gBAAA;MAJKK,KAAK,GAAAF,iBAAA;MAAEG,GAAG,GAAAH,iBAAA;IAKjBvB,OAAO,CAACQ,KAAK,GAAG,KAAK,CAAC;IACtBN,OAAO,CAACM,KAAK,GAAG,KAAK,CAAC;IACtB3B,QAAQ,CAAC2B,KAAK,GAAGiB,KAAK;IACtB3C,SAAS,CAAC0B,KAAK,GAAGkB,GAAG;EACzB,CAAG;EACDC,KAAK,CAAC/C,YAAY,EAAE,UAAC2B,GAAG,EAAK;IAC3B,IAAIA,GAAG,EAAE;MACPY,cAAc,EAAE;IACtB;EACA,CAAG,EAAE;IAAES,SAAS,EAAE;EAAI,CAAE,CAAC;EACvBD,KAAK,CAAC;IAAA,OAAMjD,KAAK,CAACmD,WAAW;EAAA,GAAE,UAACA,WAAW,EAAK;IAC9C,IAAIC,OAAO,CAACD,WAAW,CAAC,IAAIA,WAAW,CAACjB,MAAM,KAAK,CAAC,EAAE;MACpD,IAAAmB,YAAA,GAAAP,cAAA,CAAqBK,WAAW;QAAzBJ,KAAK,GAAAM,YAAA;QAAEL,GAAG,GAAAK,YAAA;MACjB/B,OAAO,CAACQ,KAAK,GAAGiB,KAAK;MACrB5C,QAAQ,CAAC2B,KAAK,GAAGiB,KAAK;MACtBvB,OAAO,CAACM,KAAK,GAAGkB,GAAG;MACnB1C,oBAAoB,CAAC+B,KAAK,CAACf,OAAO,CAAC,EAAEe,KAAK,CAACb,OAAO,CAAC,CAAC;IAC1D,CAAK,MAAM;MACLiB,cAAc,EAAE;IACtB;EACA,CAAG,EAAE;IAAES,SAAS,EAAE;EAAI,CAAE,CAAC;EACvB,OAAO;IACL5B,OAAO;IACPE,OAAO;IACPC,UAAU;IACVN,IAAI;IACJmC,IAAI,EAAEzC,QAAQ;IACdC,KAAK;IACLc,iBAAiB;IACjBG,kBAAkB;IAClBX,mBAAmB;IACnBoB,QAAQ;IACRtB;EACJ,CAAG;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
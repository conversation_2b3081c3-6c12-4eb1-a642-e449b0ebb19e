{"ast": null, "code": "import { defineComponent, openBlock, createElementBlock, unref, normalizeClass, withModifiers, createElementVNode, toDisplayString, createCommentVNode, Fragment, renderList, createVNode } from 'vue';\nimport { basicDateTableProps, basicDateTableEmits } from '../props/basic-date-table.mjs';\nimport { useBasicDateTable, useBasicDateTableDOM } from '../composables/use-basic-date-table.mjs';\nimport ElDatePickerCell from './basic-cell-render.mjs';\nimport _export_sfc from '../../../../_virtual/plugin-vue_export-helper.mjs';\nvar _hoisted_1 = [\"aria-label\"];\nvar _hoisted_2 = {\n  key: 0,\n  scope: \"col\"\n};\nvar _hoisted_3 = [\"aria-label\"];\nvar _hoisted_4 = [\"aria-current\", \"aria-selected\", \"tabindex\"];\nvar _sfc_main = /* @__PURE__ */defineComponent({\n  __name: \"basic-date-table\",\n  props: basicDateTableProps,\n  emits: basicDateTableEmits,\n  setup(__props, _ref) {\n    var expose = _ref.expose,\n      emit = _ref.emit;\n    var props = __props;\n    var _useBasicDateTable = useBasicDateTable(props, emit),\n      WEEKS = _useBasicDateTable.WEEKS,\n      rows = _useBasicDateTable.rows,\n      tbodyRef = _useBasicDateTable.tbodyRef,\n      currentCellRef = _useBasicDateTable.currentCellRef,\n      focus = _useBasicDateTable.focus,\n      isCurrent = _useBasicDateTable.isCurrent,\n      isWeekActive = _useBasicDateTable.isWeekActive,\n      isSelectedCell = _useBasicDateTable.isSelectedCell,\n      handlePickDate = _useBasicDateTable.handlePickDate,\n      handleMouseUp = _useBasicDateTable.handleMouseUp,\n      handleMouseDown = _useBasicDateTable.handleMouseDown,\n      handleMouseMove = _useBasicDateTable.handleMouseMove,\n      handleFocus = _useBasicDateTable.handleFocus;\n    var _useBasicDateTableDOM = useBasicDateTableDOM(props, {\n        isCurrent,\n        isWeekActive\n      }),\n      tableLabel = _useBasicDateTableDOM.tableLabel,\n      tableKls = _useBasicDateTableDOM.tableKls,\n      weekLabel = _useBasicDateTableDOM.weekLabel,\n      getCellClasses = _useBasicDateTableDOM.getCellClasses,\n      getRowKls = _useBasicDateTableDOM.getRowKls,\n      t = _useBasicDateTableDOM.t;\n    expose({\n      focus\n    });\n    return function (_ctx, _cache) {\n      return openBlock(), createElementBlock(\"table\", {\n        \"aria-label\": unref(tableLabel),\n        class: normalizeClass(unref(tableKls)),\n        cellspacing: \"0\",\n        cellpadding: \"0\",\n        role: \"grid\",\n        onClick: _cache[1] || (_cache[1] = function () {\n          return unref(handlePickDate) && unref(handlePickDate).apply(void 0, arguments);\n        }),\n        onMousemove: _cache[2] || (_cache[2] = function () {\n          return unref(handleMouseMove) && unref(handleMouseMove).apply(void 0, arguments);\n        }),\n        onMousedown: _cache[3] || (_cache[3] = withModifiers(function () {\n          return unref(handleMouseDown) && unref(handleMouseDown).apply(void 0, arguments);\n        }, [\"prevent\"])),\n        onMouseup: _cache[4] || (_cache[4] = function () {\n          return unref(handleMouseUp) && unref(handleMouseUp).apply(void 0, arguments);\n        })\n      }, [createElementVNode(\"tbody\", {\n        ref_key: \"tbodyRef\",\n        ref: tbodyRef\n      }, [createElementVNode(\"tr\", null, [_ctx.showWeekNumber ? (openBlock(), createElementBlock(\"th\", _hoisted_2, toDisplayString(unref(weekLabel)), 1)) : createCommentVNode(\"v-if\", true), (openBlock(true), createElementBlock(Fragment, null, renderList(unref(WEEKS), function (week, key) {\n        return openBlock(), createElementBlock(\"th\", {\n          key,\n          \"aria-label\": unref(t)(\"el.datepicker.weeksFull.\" + week),\n          scope: \"col\"\n        }, toDisplayString(unref(t)(\"el.datepicker.weeks.\" + week)), 9, _hoisted_3);\n      }), 128))]), (openBlock(true), createElementBlock(Fragment, null, renderList(unref(rows), function (row, rowKey) {\n        return openBlock(), createElementBlock(\"tr\", {\n          key: rowKey,\n          class: normalizeClass(unref(getRowKls)(row[1]))\n        }, [(openBlock(true), createElementBlock(Fragment, null, renderList(row, function (cell, columnKey) {\n          return openBlock(), createElementBlock(\"td\", {\n            key: `${rowKey}.${columnKey}`,\n            ref_for: true,\n            ref: function ref(el) {\n              return unref(isSelectedCell)(cell) && (currentCellRef.value = el);\n            },\n            class: normalizeClass(unref(getCellClasses)(cell)),\n            \"aria-current\": cell.isCurrent ? \"date\" : void 0,\n            \"aria-selected\": cell.isCurrent,\n            tabindex: unref(isSelectedCell)(cell) ? 0 : -1,\n            onFocus: _cache[0] || (_cache[0] = function () {\n              return unref(handleFocus) && unref(handleFocus).apply(void 0, arguments);\n            })\n          }, [createVNode(unref(ElDatePickerCell), {\n            cell\n          }, null, 8, [\"cell\"])], 42, _hoisted_4);\n        }), 128))], 2);\n      }), 128))], 512)], 42, _hoisted_1);\n    };\n  }\n});\nvar DateTable = /* @__PURE__ */_export_sfc(_sfc_main, [[\"__file\", \"basic-date-table.vue\"]]);\nexport { DateTable as default };", "map": {"version": 3, "names": ["_useBasicDateTable", "useBasicDateTable", "props", "emit", "WEEKS", "rows", "tbodyRef", "currentCellRef", "focus", "isCurrent", "isWeekActive", "isSelectedCell", "handlePickDate", "handleMouseUp", "handleMouseDown", "handleMouseMove", "handleFocus", "_useBasicDateTableDOM", "useBasicDateTableDOM", "tableLabel", "tableKls", "week<PERSON><PERSON><PERSON>", "getCellClasses", "getRowKls", "t", "expose"], "sources": ["../../../../../../../packages/components/date-picker/src/date-picker-com/basic-date-table.vue"], "sourcesContent": ["<template>\n  <table\n    :aria-label=\"tableLabel\"\n    :class=\"tableKls\"\n    cellspacing=\"0\"\n    cellpadding=\"0\"\n    role=\"grid\"\n    @click=\"handlePickDate\"\n    @mousemove=\"handleMouseMove\"\n    @mousedown.prevent=\"handleMouseDown\"\n    @mouseup=\"handleMouseUp\"\n  >\n    <tbody ref=\"tbodyRef\">\n      <tr>\n        <th v-if=\"showWeekNumber\" scope=\"col\">{{ weekLabel }}</th>\n        <th\n          v-for=\"(week, key) in WEEKS\"\n          :key=\"key\"\n          :aria-label=\"t('el.datepicker.weeksFull.' + week)\"\n          scope=\"col\"\n        >\n          {{ t('el.datepicker.weeks.' + week) }}\n        </th>\n      </tr>\n      <tr\n        v-for=\"(row, rowKey) in rows\"\n        :key=\"rowKey\"\n        :class=\"getRowKls(row[1])\"\n      >\n        <td\n          v-for=\"(cell, columnKey) in row\"\n          :key=\"`${rowKey}.${columnKey}`\"\n          :ref=\"(el) => isSelectedCell(cell) && (currentCellRef = el as HTMLElement)\"\n          :class=\"getCellClasses(cell)\"\n          :aria-current=\"cell.isCurrent ? 'date' : undefined\"\n          :aria-selected=\"cell.isCurrent\"\n          :tabindex=\"isSelectedCell(cell) ? 0 : -1\"\n          @focus=\"handleFocus\"\n        >\n          <el-date-picker-cell :cell=\"cell\" />\n        </td>\n      </tr>\n    </tbody>\n  </table>\n</template>\n\n<script lang=\"ts\" setup>\nimport {\n  basicDateTableEmits,\n  basicDateTableProps,\n} from '../props/basic-date-table'\nimport {\n  useBasicDateTable,\n  useBasicDateTableDOM,\n} from '../composables/use-basic-date-table'\nimport ElDatePickerCell from './basic-cell-render'\n\nconst props = defineProps(basicDateTableProps)\nconst emit = defineEmits(basicDateTableEmits)\n\nconst {\n  WEEKS,\n  rows,\n  tbodyRef,\n  currentCellRef,\n\n  focus,\n  isCurrent,\n  isWeekActive,\n  isSelectedCell,\n\n  handlePickDate,\n  handleMouseUp,\n  handleMouseDown,\n  handleMouseMove,\n  handleFocus,\n} = useBasicDateTable(props, emit)\nconst { tableLabel, tableKls, weekLabel, getCellClasses, getRowKls, t } =\n  useBasicDateTableDOM(props, {\n    isCurrent,\n    isWeekActive,\n  })\n\ndefineExpose({\n  /**\n   * @description focus on current cell\n   */\n  focus,\n})\n</script>\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;IA4DM,IAAAA,kBAAA,GAgBFC,iBAAA,CAAkBC,KAAA,EAAOC,IAAI;MAf/BC,KAAA,GAAAJ,kBAAA,CAAAI,KAAA;MACAC,IAAA,GAAAL,kBAAA,CAAAK,IAAA;MACAC,QAAA,GAAAN,kBAAA,CAAAM,QAAA;MACAC,cAAA,GAAAP,kBAAA,CAAAO,cAAA;MAEAC,KAAA,GAAAR,kBAAA,CAAAQ,KAAA;MACAC,SAAA,GAAAT,kBAAA,CAAAS,SAAA;MACAC,YAAA,GAAAV,kBAAA,CAAAU,YAAA;MACAC,cAAA,GAAAX,kBAAA,CAAAW,cAAA;MAEAC,cAAA,GAAAZ,kBAAA,CAAAY,cAAA;MACAC,aAAA,GAAAb,kBAAA,CAAAa,aAAA;MACAC,eAAA,GAAAd,kBAAA,CAAAc,eAAA;MACAC,eAAA,GAAAf,kBAAA,CAAAe,eAAA;MACAC,WAAA,GAAAhB,kBAAA,CAAAgB,WAAA;IAEI,IAAAC,qBAAA,GACJC,oBAAA,CAAqBhB,KAAO;QAC1BO,SAAA;QACAC;MAAA,CACD;MAJKS,UAAA,GAAAF,qBAAA,CAAAE,UAAA;MAAYC,QAAU,GAAAH,qBAAA,CAAVG,QAAU;MAAAC,SAAA,GAAAJ,qBAAA,CAAAI,SAAA;MAAWC,cAAA,GAAAL,qBAAA,CAAAK,cAAA;MAAgBC,SAAW,GAAAN,qBAAA,CAAXM,SAAW;MAAAC,CAAA,GAAAP,qBAAA,CAAAO,CAAA;IAMvDC,MAAA;MAIXjB;IAAA,CACD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
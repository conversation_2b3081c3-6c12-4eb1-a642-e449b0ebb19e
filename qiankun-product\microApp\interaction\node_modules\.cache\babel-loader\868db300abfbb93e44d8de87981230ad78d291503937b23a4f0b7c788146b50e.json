{"ast": null, "code": "import baseSum from './_baseSum.js';\nimport identity from './identity.js';\n\n/**\n * Computes the sum of the values in `array`.\n *\n * @static\n * @memberOf _\n * @since 3.4.0\n * @category Math\n * @param {Array} array The array to iterate over.\n * @returns {number} Returns the sum.\n * @example\n *\n * _.sum([4, 2, 8, 6]);\n * // => 20\n */\nfunction sum(array) {\n  return array && array.length ? baseSum(array, identity) : 0;\n}\nexport default sum;", "map": {"version": 3, "names": ["baseSum", "identity", "sum", "array", "length"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/node_modules/.pnpm/lodash-es@4.17.21/node_modules/lodash-es/sum.js"], "sourcesContent": ["import baseSum from './_baseSum.js';\nimport identity from './identity.js';\n\n/**\n * Computes the sum of the values in `array`.\n *\n * @static\n * @memberOf _\n * @since 3.4.0\n * @category Math\n * @param {Array} array The array to iterate over.\n * @returns {number} Returns the sum.\n * @example\n *\n * _.sum([4, 2, 8, 6]);\n * // => 20\n */\nfunction sum(array) {\n  return (array && array.length)\n    ? baseSum(array, identity)\n    : 0;\n}\n\nexport default sum;\n"], "mappings": "AAAA,OAAOA,OAAO,MAAM,eAAe;AACnC,OAAOC,QAAQ,MAAM,eAAe;;AAEpC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,GAAGA,CAACC,KAAK,EAAE;EAClB,OAAQA,KAAK,IAAIA,KAAK,CAACC,MAAM,GACzBJ,OAAO,CAACG,KAAK,EAAEF,QAAQ,CAAC,GACxB,CAAC;AACP;AAEA,eAAeC,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
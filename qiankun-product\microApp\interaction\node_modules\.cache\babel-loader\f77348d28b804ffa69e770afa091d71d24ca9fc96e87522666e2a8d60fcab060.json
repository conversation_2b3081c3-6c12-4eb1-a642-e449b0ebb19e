{"ast": null, "code": "import baseSortedIndex from './_baseSortedIndex.js';\nimport eq from './eq.js';\n\n/**\n * This method is like `_.indexOf` except that it performs a binary\n * search on a sorted `array`.\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category Array\n * @param {Array} array The array to inspect.\n * @param {*} value The value to search for.\n * @returns {number} Returns the index of the matched value, else `-1`.\n * @example\n *\n * _.sortedIndexOf([4, 5, 5, 5, 6], 5);\n * // => 1\n */\nfunction sortedIndexOf(array, value) {\n  var length = array == null ? 0 : array.length;\n  if (length) {\n    var index = baseSortedIndex(array, value);\n    if (index < length && eq(array[index], value)) {\n      return index;\n    }\n  }\n  return -1;\n}\nexport default sortedIndexOf;", "map": {"version": 3, "names": ["baseSortedIndex", "eq", "sortedIndexOf", "array", "value", "length", "index"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/node_modules/.pnpm/lodash-es@4.17.21/node_modules/lodash-es/sortedIndexOf.js"], "sourcesContent": ["import baseSortedIndex from './_baseSortedIndex.js';\nimport eq from './eq.js';\n\n/**\n * This method is like `_.indexOf` except that it performs a binary\n * search on a sorted `array`.\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category Array\n * @param {Array} array The array to inspect.\n * @param {*} value The value to search for.\n * @returns {number} Returns the index of the matched value, else `-1`.\n * @example\n *\n * _.sortedIndexOf([4, 5, 5, 5, 6], 5);\n * // => 1\n */\nfunction sortedIndexOf(array, value) {\n  var length = array == null ? 0 : array.length;\n  if (length) {\n    var index = baseSortedIndex(array, value);\n    if (index < length && eq(array[index], value)) {\n      return index;\n    }\n  }\n  return -1;\n}\n\nexport default sortedIndexOf;\n"], "mappings": "AAAA,OAAOA,eAAe,MAAM,uBAAuB;AACnD,OAAOC,EAAE,MAAM,SAAS;;AAExB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,aAAaA,CAACC,KAAK,EAAEC,KAAK,EAAE;EACnC,IAAIC,MAAM,GAAGF,KAAK,IAAI,IAAI,GAAG,CAAC,GAAGA,KAAK,CAACE,MAAM;EAC7C,IAAIA,MAAM,EAAE;IACV,IAAIC,KAAK,GAAGN,eAAe,CAACG,KAAK,EAAEC,KAAK,CAAC;IACzC,IAAIE,KAAK,GAAGD,MAAM,IAAIJ,EAAE,CAACE,KAAK,CAACG,KAAK,CAAC,EAAEF,KAAK,CAAC,EAAE;MAC7C,OAAOE,KAAK;IACd;EACF;EACA,OAAO,CAAC,CAAC;AACX;AAEA,eAAeJ,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
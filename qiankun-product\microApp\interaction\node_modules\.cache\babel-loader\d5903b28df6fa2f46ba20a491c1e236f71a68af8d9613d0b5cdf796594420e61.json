{"ast": null, "code": "function useOption(props, _ref) {\n  var emit = _ref.emit;\n  return {\n    hoverItem: function hoverItem() {\n      if (!props.disabled) {\n        emit(\"hover\", props.index);\n      }\n    },\n    selectOptionClick: function selectOptionClick() {\n      if (!props.disabled) {\n        emit(\"select\", props.item, props.index);\n      }\n    }\n  };\n}\nexport { useOption };", "map": {"version": 3, "names": ["useOption", "props", "_ref", "emit", "hoverItem", "disabled", "index", "selectOptionClick", "item"], "sources": ["../../../../../../packages/components/select-v2/src/useOption.ts"], "sourcesContent": ["// @ts-nocheck\nimport type { IOptionV2Props } from './token'\n\nexport function useOption(props: IOptionV2Props, { emit }) {\n  return {\n    hoverItem: () => {\n      if (!props.disabled) {\n        emit('hover', props.index)\n      }\n    },\n    selectOptionClick: () => {\n      if (!props.disabled) {\n        emit('select', props.item, props.index)\n      }\n    },\n  }\n}\n"], "mappings": "AAAO,SAASA,SAASA,CAACC,KAAK,EAAAC,IAAA,EAAY;EAAA,IAARC,IAAI,GAAAD,IAAA,CAAJC,IAAI;EACrC,OAAO;IACLC,SAAS,EAAE,SAAXA,SAASA,CAAA,EAAQ;MACf,IAAI,CAACH,KAAK,CAACI,QAAQ,EAAE;QACnBF,IAAI,CAAC,OAAO,EAAEF,KAAK,CAACK,KAAK,CAAC;MAClC;IACA,CAAK;IACDC,iBAAiB,EAAE,SAAnBA,iBAAiBA,CAAA,EAAQ;MACvB,IAAI,CAACN,KAAK,CAACI,QAAQ,EAAE;QACnBF,IAAI,CAAC,QAAQ,EAAEF,KAAK,CAACO,IAAI,EAAEP,KAAK,CAACK,KAAK,CAAC;MAC/C;IACA;EACA,CAAG;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "import { defineComponent, ref, computed, unref, nextTick, createVNode } from 'vue';\nimport '../../../../hooks/index.mjs';\nimport '../../../../utils/index.mjs';\nimport { tableV2HeaderProps } from '../header.mjs';\nimport { enforceUnit } from '../utils.mjs';\nimport { useNamespace } from '../../../../hooks/use-namespace/index.mjs';\nimport { castArray } from 'lodash-unified';\nvar COMPONENT_NAME = \"ElTableV2Header\";\nvar TableV2Header = defineComponent({\n  name: COMPONENT_NAME,\n  props: tableV2HeaderProps,\n  setup(props, _ref) {\n    var slots = _ref.slots,\n      expose = _ref.expose;\n    var ns = useNamespace(\"table-v2\");\n    var headerRef = ref();\n    var headerStyle = computed(function () {\n      return enforceUnit({\n        width: props.width,\n        height: props.height\n      });\n    });\n    var rowStyle = computed(function () {\n      return enforceUnit({\n        width: props.rowWidth,\n        height: props.height\n      });\n    });\n    var headerHeights = computed(function () {\n      return castArray(unref(props.headerHeight));\n    });\n    var scrollToLeft = function scrollToLeft(left) {\n      var headerEl = unref(headerRef);\n      nextTick(function () {\n        (headerEl == null ? void 0 : headerEl.scroll) && headerEl.scroll({\n          left\n        });\n      });\n    };\n    var renderFixedRows = function renderFixedRows() {\n      var fixedRowClassName = ns.e(\"fixed-header-row\");\n      var columns = props.columns,\n        fixedHeaderData = props.fixedHeaderData,\n        rowHeight = props.rowHeight;\n      return fixedHeaderData == null ? void 0 : fixedHeaderData.map(function (fixedRowData, fixedRowIndex) {\n        var _a;\n        var style = enforceUnit({\n          height: rowHeight,\n          width: \"100%\"\n        });\n        return (_a = slots.fixed) == null ? void 0 : _a.call(slots, {\n          class: fixedRowClassName,\n          columns,\n          rowData: fixedRowData,\n          rowIndex: -(fixedRowIndex + 1),\n          style\n        });\n      });\n    };\n    var renderDynamicRows = function renderDynamicRows() {\n      var dynamicRowClassName = ns.e(\"dynamic-header-row\");\n      var columns = props.columns;\n      return unref(headerHeights).map(function (rowHeight, rowIndex) {\n        var _a;\n        var style = enforceUnit({\n          width: \"100%\",\n          height: rowHeight\n        });\n        return (_a = slots.dynamic) == null ? void 0 : _a.call(slots, {\n          class: dynamicRowClassName,\n          columns,\n          headerIndex: rowIndex,\n          style\n        });\n      });\n    };\n    expose({\n      scrollToLeft\n    });\n    return function () {\n      if (props.height <= 0) return;\n      return createVNode(\"div\", {\n        \"ref\": headerRef,\n        \"class\": props.class,\n        \"style\": unref(headerStyle),\n        \"role\": \"rowgroup\"\n      }, [createVNode(\"div\", {\n        \"style\": unref(rowStyle),\n        \"class\": ns.e(\"header\")\n      }, [renderDynamicRows(), renderFixedRows()])]);\n    };\n  }\n});\nexport { TableV2Header as default };", "map": {"version": 3, "names": ["COMPONENT_NAME", "TableV2Header", "defineComponent", "name", "props", "tableV2HeaderProps", "slots", "_ref", "expose", "ns", "useNamespace", "headerRef", "ref", "headerStyle", "computed", "enforceUnit", "width", "height", "rowStyle", "row<PERSON>id<PERSON>", "headerHeights", "<PERSON><PERSON><PERSON><PERSON>", "unref", "headerHeight", "scrollToLeft", "left", "nextTick", "headerEl", "scroll", "renderFixedRows", "fixedRowClassName", "e", "columns", "fixedHeaderData", "rowHeight", "map", "fixedRowData", "fixedRowIndex", "_a", "style", "fixed", "call", "class", "rowData", "rowIndex", "renderDynamicRows", "dynamicRowClassName", "dynamic", "headerIndex", "createVNode"], "sources": ["../../../../../../../packages/components/table-v2/src/components/header.tsx"], "sourcesContent": ["import { computed, defineComponent, nextTick, ref, unref } from 'vue'\nimport { useNamespace } from '@element-plus/hooks'\nimport { ensureArray } from '@element-plus/utils'\nimport { tableV2HeaderProps } from '../header'\nimport { enforceUnit } from '../utils'\n\nimport type { CSSProperties, UnwrapRef } from 'vue'\nimport type { TableV2HeaderProps } from '../header'\nimport type { UseColumnsReturn } from '../composables/use-columns'\n\nconst COMPONENT_NAME = 'ElTableV2Header'\nconst TableV2Header = defineComponent({\n  name: COMPONENT_NAME,\n  props: tableV2HeaderProps,\n  setup(props, { slots, expose }) {\n    const ns = useNamespace('table-v2')\n\n    const headerRef = ref<HTMLElement>()\n\n    const headerStyle = computed(() =>\n      enforceUnit({\n        width: props.width,\n        height: props.height,\n      })\n    )\n\n    const rowStyle = computed(() =>\n      enforceUnit({\n        width: props.rowWidth,\n        height: props.height,\n      })\n    )\n\n    const headerHeights = computed(() => ensureArray(unref(props.headerHeight)))\n\n    const scrollToLeft = (left?: number) => {\n      const headerEl = unref(headerRef)\n      nextTick(() => {\n        headerEl?.scroll &&\n          headerEl.scroll({\n            left,\n          })\n      })\n    }\n\n    const renderFixedRows = () => {\n      const fixedRowClassName = ns.e('fixed-header-row')\n\n      const { columns, fixedHeaderData, rowHeight } = props\n\n      return fixedHeaderData?.map((fixedRowData, fixedRowIndex) => {\n        const style: CSSProperties = enforceUnit({\n          height: rowHeight,\n          width: '100%',\n        })\n\n        return slots.fixed?.({\n          class: fixedRowClassName,\n          columns,\n          rowData: fixedRowData,\n          rowIndex: -(fixedRowIndex + 1),\n          style,\n        })\n      })\n    }\n\n    const renderDynamicRows = () => {\n      const dynamicRowClassName = ns.e('dynamic-header-row')\n      const { columns } = props\n\n      return unref(headerHeights).map((rowHeight, rowIndex) => {\n        const style: CSSProperties = enforceUnit({\n          width: '100%',\n          height: rowHeight,\n        })\n\n        return slots.dynamic?.({\n          class: dynamicRowClassName,\n          columns,\n          headerIndex: rowIndex,\n          style,\n        })\n      })\n    }\n\n    expose({\n      /**\n       * @description scroll to position based on the provided value\n       */\n      scrollToLeft,\n    })\n\n    return () => {\n      if (props.height <= 0) return\n\n      return (\n        <div\n          ref={headerRef}\n          class={props.class}\n          style={unref(headerStyle)}\n          role=\"rowgroup\"\n        >\n          <div style={unref(rowStyle)} class={ns.e('header')}>\n            {renderDynamicRows()}\n            {renderFixedRows()}\n          </div>\n        </div>\n      )\n    }\n  },\n})\n\nexport default TableV2Header\n\nexport type TableV2HeaderInstance = InstanceType<typeof TableV2Header> & {\n  /**\n   * @description scroll to position based on the provided value\n   */\n  scrollToLeft: (left?: number) => void\n}\n\nexport type TableV2HeaderRendererParams = {\n  class: string\n  columns: TableV2HeaderProps['columns']\n  columnsStyles: UnwrapRef<UseColumnsReturn['columnsStyles']>\n  headerIndex: number\n  style: CSSProperties\n}\n\nexport type TableV2HeaderRowRendererParams = {\n  rowData: any\n  rowIndex: number\n} & Omit<TableV2HeaderRendererParams, 'headerIndex'>\n"], "mappings": ";;;;;;;AAUA,IAAMA,cAAc,GAAG,iBAAvB;AACM,IAAAC,aAAa,GAAGC,eAAe,CAAC;EACpCC,IAAI,EAAEH,cAD8B;EAEpCI,KAAK,EAAEC,kBAF6B;qBAGd;IAAA,IAAjBC,KAAA,GAAAC,IAAA,CAAAD,KAAA;MAAUE,MAAF,GAAAD,IAAA,CAAEC,MAAF;IAAmB,IAAAC,EAAA,GAAAC,YAAA;IAC9B,IAAMC,SAAiB,GAAAC,GAAA;IAEvB,IAAMC,WAAY,GAAAC,QAAlB;MAAA,OAAAC,WAAA;QAEAC,KAAA,EAAAZ,KAAA,CAAAY,KAAoB;QAEhBC,MAAK,EAAOb,KAAC,CADHa;QAEV;IAAA,EAAM;IAFI,IADdC,QAAA,GAAAJ,QAAA;MAAA,OAAAC,WAAA;QAOAC,KAAA,EAAAZ,KAAc,CAAGe,QAAA;QAEbF,MAAK,EAAOb,KAAC,CADHa;QAEV;IAAA,EAAM;IAFI,IADdG,aAAA,GAAAN,QAAA;MAAA,OAAAO,SAAA,CAAAC,KAAA,CAAAlB,KAAA,CAAAmB,YAAA;IAAA;IAOA,IAAMC,YAAa,YAAbA,YAAaJ,CAAGK,IAAQ,EAAC;;MAEzBC,QAAA;QACJ,CAAAC,QAAc,QAAQ,YAAAA,QAAtB,CAAAC,MAAA,KAAAD,QAAA,CAAAC,MAAA;UACAH;QACE;MAEI;IADc;IAGnB,IALDI,eAAA,YAAAA,gBAAA;MAFF,IAAAC,iBAAA,GAAArB,EAAA,CAAAsB,CAAA;UAUMC,OAAA,GAGE5B,KAAA,CAHF4B,OAAA;QACJC,eAAA,GAEM7B,KAAA,CAFN6B,eAAA;QAEMC,SAAA,GAAA9B,KAAA,CAAA8B,SAAA;aAAAD,eAAA,oBAAAA,eAAA,CAAAE,GAAA,WAAAC,YAAA,EAAAC,aAAA;QAA4B,IAAAC,EAAA;QAA5B,IAANC,KAAA,GAAAxB,WAAA;UAEOE,MAAA,EAAAiB,SAAA;UACClB,KAAA;QACJ;QACA,OAAK,CAAEsB,EAAA,GAAAhC,KAAA,CAAAkC,KAAA,qBAAAF,EAAA,CAAAG,IAAA,CAAAnC,KAAA;UAFToC,KAAA,EAAAZ,iBAAA;UAKOE,OAAA;UACLW,OAAO,EADYP,YAAA;UAEnBQ,QAFmB,IAAAP,aAAA;UAGnBE;QACA;MACA;IALmB;IAOtB,IAbDM,iBAAA,YAAAA,kBAAA;MALF,IAAAC,mBAAA,GAAArC,EAAA,CAAAsB,CAAA;UAqBMC,OAAA,GACJ5B,KAAA,CADI4B,OAAA;MAEJ,OAAMV,KAAA,CAAAF,aAAA,EAAAe,GAAA,WAAAD,SAAA,EAAAU,QAAA;QAAE,IAAAN,EAAA;QAAF,IAANC,KAAA,GAAAxB,WAAA;UAEOC,KAAA,QAAM;UACLC,MAAA,EAAAiB;QACJ;QACA,QAAMI,EAAE,GAAAhC,KAAA,CAAAyC,OAAA,qBAAAT,EAAA,CAAAG,IAAA,CAAAnC,KAAA;UAFVoC,KAAA,EAAAI,mBAAA;UAKOd,OAAA;UACLgB,WADqB,EAAAJ,QAAA;UAErBL;QACA;MACA;IAJqB;IAMxB/B,MAZD;MAJFgB;;IAmBA,OAAO;MACL,IAAApB,KAAA,CAAAa,MAAA,OACN;MACA,OAAAgC,WAAA;QACM,OAAAtC,SAAA;QAJF,SAAAP,KAAA,CAAAsC,KAAA;QAOA,OAAa,EAAApB,KAAA,CAAAT,WAAA;QACX,MAAS,EAAC;MAEV,IAAAoC,WAAA;QAAA,OAES,EAFT3B,KAAA,CAAAJ,QAAA;QAAA,OAGW,EAAAT,EAAA,CAAAsB,CAAA,CAAK,QAHhB;SAIW,CAAAc,iBAAM,IAAAhB,eAJjB;;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "import createMathOperation from './_createMathOperation.js';\n\n/**\n * Subtract two numbers.\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category Math\n * @param {number} minuend The first number in a subtraction.\n * @param {number} subtrahend The second number in a subtraction.\n * @returns {number} Returns the difference.\n * @example\n *\n * _.subtract(6, 4);\n * // => 2\n */\nvar subtract = createMathOperation(function (minuend, subtrahend) {\n  return minuend - subtrahend;\n}, 0);\nexport default subtract;", "map": {"version": 3, "names": ["createMathOperation", "subtract", "minuend", "subtrahend"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/node_modules/.pnpm/lodash-es@4.17.21/node_modules/lodash-es/subtract.js"], "sourcesContent": ["import createMathOperation from './_createMathOperation.js';\n\n/**\n * Subtract two numbers.\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category Math\n * @param {number} minuend The first number in a subtraction.\n * @param {number} subtrahend The second number in a subtraction.\n * @returns {number} Returns the difference.\n * @example\n *\n * _.subtract(6, 4);\n * // => 2\n */\nvar subtract = createMathOperation(function(minuend, subtrahend) {\n  return minuend - subtrahend;\n}, 0);\n\nexport default subtract;\n"], "mappings": "AAAA,OAAOA,mBAAmB,MAAM,2BAA2B;;AAE3D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIC,QAAQ,GAAGD,mBAAmB,CAAC,UAASE,OAAO,EAAEC,UAAU,EAAE;EAC/D,OAAOD,OAAO,GAAGC,UAAU;AAC7B,CAAC,EAAE,CAAC,CAAC;AAEL,eAAeF,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
<template>
  <el-dialog v-model="visible" title="分享直播" width="500px" :before-close="handleClose" class="share-dialog">
    <div class="share-content">
      <!-- 直播信息 -->
      <div class="live-info">
        <div class="live-title">{{ shareInfo.title }}</div>
        <div class="live-desc">{{ shareInfo.description }}</div>
      </div>

      <!-- 分享链接 -->
      <div class="share-link-section">
        <div class="section-title">分享链接</div>
        <div class="link-input-group">
          <el-input v-model="shareUrl" readonly class="share-input" placeholder="生成分享链接中..." />
          <el-button type="primary" @click="copyLink" :loading="copying">
            {{ copying ? '复制中' : '复制链接' }}
          </el-button>
        </div>
      </div>

      <!-- 分享方式 -->
      <div class="share-methods">
        <div class="section-title">分享到</div>
        <div class="share-buttons">
          <div class="share-btn" @click="shareToWeChat">
            <div class="share-icon wechat-icon">微</div>
            <span>微信</span>
          </div>
          <div class="share-btn" @click="shareToQQ">
            <div class="share-icon qq-icon">Q</div>
            <span>QQ</span>
          </div>
          <div class="share-btn" @click="shareToWeibo">
            <div class="share-icon weibo-icon">微</div>
            <span>微博</span>
          </div>
        </div>
      </div>

      <!-- 二维码分享 -->
      <div class="qr-section">
        <div class="section-title">扫码观看</div>
        <div class="qr-code" ref="qrCodeRef">
          <!-- 这里可以集成二维码生成库 -->
          <div class="qr-placeholder">
            二维码
            <div class="qr-text">{{ shareUrl }}</div>
          </div>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script>
export default { name: 'ShareDialog' }
</script>

<script setup>
import { ref, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { generateLiveShareUrl, copyToClipboard, shareToSocial, getShareInfo } from '../../../utils/shareUtils.js'

const props = defineProps({
  modelValue: { type: Boolean, default: false },
  liveId: { type: String, default: '' },
  liveData: { type: Object, default: () => ({}) }
})

const emit = defineEmits(['update:modelValue'])

const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const shareUrl = ref('')
const copying = ref(false)
const qrCodeRef = ref(null)

// 计算分享信息
const shareInfo = computed(() => {
  return getShareInfo(props.liveData)
})

// 监听弹窗打开，生成分享链接
watch(() => props.modelValue, (newVal) => {
  if (newVal && props.liveId) {
    generateShareUrl()
  }
})

// 生成分享链接
const generateShareUrl = () => {
  try {
    shareUrl.value = generateLiveShareUrl(props.liveId)
  } catch (error) {
    ElMessage.error('生成分享链接失败')
    console.error('生成分享链接失败:', error)
  }
}

// 复制链接
const copyLink = async () => {
  if (!shareUrl.value) {
    ElMessage.error('分享链接为空')
    return
  }

  copying.value = true
  try {
    const success = await copyToClipboard(shareUrl.value)
    if (success) {
      ElMessage.success('分享链接已复制到剪贴板')
    } else {
      ElMessage.error('复制失败')
    }
  } catch (error) {
    ElMessage.error('复制失败')
  } finally {
    copying.value = false
  }
}

// 分享到微信
const shareToWeChat = async () => {
  const success = await copyToClipboard(shareUrl.value)
  if (success) {
    ElMessage.success('链接已复制，请在微信中粘贴分享')
  }
}

// 分享到QQ
const shareToQQ = () => {
  shareToSocial(shareUrl.value, shareInfo.value.title, 'qq')
}

// 分享到微博
const shareToWeibo = () => {
  shareToSocial(shareUrl.value, shareInfo.value.title, 'weibo')
}

// 关闭弹窗
const handleClose = () => {
  visible.value = false
}
</script>

<style lang="scss" scoped>
.share-dialog {
  .share-content {
    .live-info {
      margin-bottom: 24px;
      padding: 16px;
      background: #f8f9fa;
      border-radius: 8px;

      .live-title {
        font-size: 16px;
        font-weight: 600;
        color: #333;
        margin-bottom: 8px;
      }

      .live-desc {
        font-size: 14px;
        color: #666;
      }
    }

    .section-title {
      font-size: 14px;
      font-weight: 600;
      color: #333;
      margin-bottom: 12px;
    }

    .share-link-section {
      margin-bottom: 24px;

      .link-input-group {
        display: flex;
        gap: 12px;

        .share-input {
          flex: 1;
        }
      }
    }

    .share-methods {
      margin-bottom: 24px;

      .share-buttons {
        display: flex;
        gap: 16px;

        .share-btn {
          display: flex;
          flex-direction: column;
          align-items: center;
          gap: 8px;
          padding: 12px;
          border-radius: 8px;
          cursor: pointer;
          transition: background-color 0.2s;

          &:hover {
            background: #f5f5f5;
          }

          .share-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;

            &.wechat-icon {
              background: #07c160;
            }

            &.qq-icon {
              background: #12b7f5;
            }

            &.weibo-icon {
              background: #e6162d;
            }
          }

          span {
            font-size: 12px;
            color: #666;
          }
        }
      }
    }

    .qr-section {
      .qr-code {
        display: flex;
        justify-content: center;

        .qr-placeholder {
          width: 120px;
          height: 120px;
          border: 2px dashed #ddd;
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          color: #999;
          font-size: 14px;

          .qr-text {
            font-size: 10px;
            margin-top: 8px;
            word-break: break-all;
            text-align: center;
            padding: 0 8px;
          }
        }
      }
    }
  }
}
</style>

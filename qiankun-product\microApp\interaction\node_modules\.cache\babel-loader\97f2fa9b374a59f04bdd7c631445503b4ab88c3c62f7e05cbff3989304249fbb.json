{"ast": null, "code": "function _toConsumableArray(r) { return _arrayWithoutHoles(r) || _iterableToArray(r) || _unsupportedIterableToArray(r) || _nonIterableSpread(); }\nfunction _nonIterableSpread() { throw new TypeError(\"Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); }\nfunction _unsupportedIterableToArray(r, a) { if (r) { if (\"string\" == typeof r) return _arrayLikeToArray(r, a); var t = {}.toString.call(r).slice(8, -1); return \"Object\" === t && r.constructor && (t = r.constructor.name), \"Map\" === t || \"Set\" === t ? Array.from(r) : \"Arguments\" === t || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t) ? _arrayLikeToArray(r, a) : void 0; } }\nfunction _iterableToArray(r) { if (\"undefined\" != typeof Symbol && null != r[Symbol.iterator] || null != r[\"@@iterator\"]) return Array.from(r); }\nfunction _arrayWithoutHoles(r) { if (Array.isArray(r)) return _arrayLikeToArray(r); }\nfunction _arrayLikeToArray(r, a) { (null == a || a > r.length) && (a = r.length); for (var e = 0, n = Array(a); e < a; e++) n[e] = r[e]; return n; }\nimport { inject } from 'vue';\nimport '../../../../hooks/index.mjs';\nimport { getFixedColumnOffset, ensurePosition, getFixedColumnsClass } from '../util.mjs';\nimport { TABLE_INJECTION_KEY } from '../tokens.mjs';\nimport { useNamespace } from '../../../../hooks/use-namespace/index.mjs';\nfunction useStyle(props) {\n  var parent = inject(TABLE_INJECTION_KEY);\n  var ns = useNamespace(\"table\");\n  var getHeaderRowStyle = function getHeaderRowStyle(rowIndex) {\n    var headerRowStyle = parent == null ? void 0 : parent.props.headerRowStyle;\n    if (typeof headerRowStyle === \"function\") {\n      return headerRowStyle.call(null, {\n        rowIndex\n      });\n    }\n    return headerRowStyle;\n  };\n  var getHeaderRowClass = function getHeaderRowClass(rowIndex) {\n    var classes = [];\n    var headerRowClassName = parent == null ? void 0 : parent.props.headerRowClassName;\n    if (typeof headerRowClassName === \"string\") {\n      classes.push(headerRowClassName);\n    } else if (typeof headerRowClassName === \"function\") {\n      classes.push(headerRowClassName.call(null, {\n        rowIndex\n      }));\n    }\n    return classes.join(\" \");\n  };\n  var getHeaderCellStyle = function getHeaderCellStyle(rowIndex, columnIndex, row, column) {\n    var _a;\n    var headerCellStyles = (_a = parent == null ? void 0 : parent.props.headerCellStyle) != null ? _a : {};\n    if (typeof headerCellStyles === \"function\") {\n      headerCellStyles = headerCellStyles.call(null, {\n        rowIndex,\n        columnIndex,\n        row,\n        column\n      });\n    }\n    var fixedStyle = getFixedColumnOffset(columnIndex, column.fixed, props.store, row);\n    ensurePosition(fixedStyle, \"left\");\n    ensurePosition(fixedStyle, \"right\");\n    return Object.assign({}, headerCellStyles, fixedStyle);\n  };\n  var getHeaderCellClass = function getHeaderCellClass(rowIndex, columnIndex, row, column) {\n    var fixedClasses = getFixedColumnsClass(ns.b(), columnIndex, column.fixed, props.store, row);\n    var classes = [column.id, column.order, column.headerAlign, column.className, column.labelClassName].concat(_toConsumableArray(fixedClasses));\n    if (!column.children) {\n      classes.push(\"is-leaf\");\n    }\n    if (column.sortable) {\n      classes.push(\"is-sortable\");\n    }\n    var headerCellClassName = parent == null ? void 0 : parent.props.headerCellClassName;\n    if (typeof headerCellClassName === \"string\") {\n      classes.push(headerCellClassName);\n    } else if (typeof headerCellClassName === \"function\") {\n      classes.push(headerCellClassName.call(null, {\n        rowIndex,\n        columnIndex,\n        row,\n        column\n      }));\n    }\n    classes.push(ns.e(\"cell\"));\n    return classes.filter(function (className) {\n      return Boolean(className);\n    }).join(\" \");\n  };\n  return {\n    getHeaderRowStyle,\n    getHeaderRowClass,\n    getHeaderCellStyle,\n    getHeaderCellClass\n  };\n}\nexport { useStyle as default };", "map": {"version": 3, "names": ["useStyle", "props", "parent", "inject", "TABLE_INJECTION_KEY", "ns", "useNamespace", "getHeaderRowStyle", "rowIndex", "headerRowStyle", "call", "getHeaderRowClass", "classes", "headerRowClassName", "push", "join", "getHeaderCellStyle", "columnIndex", "row", "column", "_a", "headerCellStyles", "headerCellStyle", "fixedStyle", "getFixedColumnOffset", "fixed", "store", "ensurePosition", "Object", "assign", "getHeaderCellClass", "fixedClasses", "getFixedColumnsClass", "b", "id", "order", "headerAlign", "className", "labelClassName", "concat", "_toConsumableArray", "children", "sortable", "headerCellClassName", "e", "filter", "Boolean"], "sources": ["../../../../../../../packages/components/table/src/table-header/style.helper.ts"], "sourcesContent": ["import { inject } from 'vue'\nimport { useNamespace } from '@element-plus/hooks'\nimport {\n  ensurePosition,\n  getFixedColumnOffset,\n  getFixedColumnsClass,\n} from '../util'\nimport { TABLE_INJECTION_KEY } from '../tokens'\nimport type { TableColumnCtx } from '../table-column/defaults'\nimport type { TableHeaderProps } from '.'\n\nfunction useStyle<T>(props: TableHeaderProps<T>) {\n  const parent = inject(TABLE_INJECTION_KEY)\n  const ns = useNamespace('table')\n\n  const getHeaderRowStyle = (rowIndex: number) => {\n    const headerRowStyle = parent?.props.headerRowStyle\n    if (typeof headerRowStyle === 'function') {\n      return headerRowStyle.call(null, { rowIndex })\n    }\n    return headerRowStyle\n  }\n\n  const getHeaderRowClass = (rowIndex: number): string => {\n    const classes: string[] = []\n    const headerRowClassName = parent?.props.headerRowClassName\n    if (typeof headerRowClassName === 'string') {\n      classes.push(headerRowClassName)\n    } else if (typeof headerRowClassName === 'function') {\n      classes.push(headerRowClassName.call(null, { rowIndex }))\n    }\n\n    return classes.join(' ')\n  }\n\n  const getHeaderCellStyle = (\n    rowIndex: number,\n    columnIndex: number,\n    row: T,\n    column: TableColumnCtx<T>\n  ) => {\n    let headerCellStyles = parent?.props.headerCellStyle ?? {}\n    if (typeof headerCellStyles === 'function') {\n      headerCellStyles = headerCellStyles.call(null, {\n        rowIndex,\n        columnIndex,\n        row,\n        column,\n      })\n    }\n    const fixedStyle = getFixedColumnOffset<T>(\n      columnIndex,\n      column.fixed,\n      props.store,\n      row as unknown as TableColumnCtx<T>[]\n    )\n    ensurePosition(fixedStyle, 'left')\n    ensurePosition(fixedStyle, 'right')\n    return Object.assign({}, headerCellStyles, fixedStyle)\n  }\n\n  const getHeaderCellClass = (\n    rowIndex: number,\n    columnIndex: number,\n    row: T,\n    column: TableColumnCtx<T>\n  ) => {\n    const fixedClasses = getFixedColumnsClass<T>(\n      ns.b(),\n      columnIndex,\n      column.fixed,\n      props.store,\n      row as unknown as TableColumnCtx<T>[]\n    )\n    const classes = [\n      column.id,\n      column.order,\n      column.headerAlign,\n      column.className,\n      column.labelClassName,\n      ...fixedClasses,\n    ]\n\n    if (!column.children) {\n      classes.push('is-leaf')\n    }\n\n    if (column.sortable) {\n      classes.push('is-sortable')\n    }\n\n    const headerCellClassName = parent?.props.headerCellClassName\n    if (typeof headerCellClassName === 'string') {\n      classes.push(headerCellClassName)\n    } else if (typeof headerCellClassName === 'function') {\n      classes.push(\n        headerCellClassName.call(null, {\n          rowIndex,\n          columnIndex,\n          row,\n          column,\n        })\n      )\n    }\n\n    classes.push(ns.e('cell'))\n\n    return classes.filter((className) => Boolean(className)).join(' ')\n  }\n\n  return {\n    getHeaderRowStyle,\n    getHeaderRowClass,\n    getHeaderCellStyle,\n    getHeaderCellClass,\n  }\n}\n\nexport default useStyle\n"], "mappings": ";;;;;;;;;;;AAQA,SAASA,QAAQA,CAACC,KAAK,EAAE;EACvB,IAAMC,MAAM,GAAGC,MAAM,CAACC,mBAAmB,CAAC;EAC1C,IAAMC,EAAE,GAAGC,YAAY,CAAC,OAAO,CAAC;EAChC,IAAMC,iBAAiB,GAAG,SAApBA,iBAAiBA,CAAIC,QAAQ,EAAK;IACtC,IAAMC,cAAc,GAAGP,MAAM,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,MAAM,CAACD,KAAK,CAACQ,cAAc;IAC5E,IAAI,OAAOA,cAAc,KAAK,UAAU,EAAE;MACxC,OAAOA,cAAc,CAACC,IAAI,CAAC,IAAI,EAAE;QAAEF;MAAQ,CAAE,CAAC;IACpD;IACI,OAAOC,cAAc;EACzB,CAAG;EACD,IAAME,iBAAiB,GAAG,SAApBA,iBAAiBA,CAAIH,QAAQ,EAAK;IACtC,IAAMI,OAAO,GAAG,EAAE;IAClB,IAAMC,kBAAkB,GAAGX,MAAM,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,MAAM,CAACD,KAAK,CAACY,kBAAkB;IACpF,IAAI,OAAOA,kBAAkB,KAAK,QAAQ,EAAE;MAC1CD,OAAO,CAACE,IAAI,CAACD,kBAAkB,CAAC;IACtC,CAAK,MAAM,IAAI,OAAOA,kBAAkB,KAAK,UAAU,EAAE;MACnDD,OAAO,CAACE,IAAI,CAACD,kBAAkB,CAACH,IAAI,CAAC,IAAI,EAAE;QAAEF;MAAQ,CAAE,CAAC,CAAC;IAC/D;IACI,OAAOI,OAAO,CAACG,IAAI,CAAC,GAAG,CAAC;EAC5B,CAAG;EACD,IAAMC,kBAAkB,GAAG,SAArBA,kBAAkBA,CAAIR,QAAQ,EAAES,WAAW,EAAEC,GAAG,EAAEC,MAAM,EAAK;IACjE,IAAIC,EAAE;IACN,IAAIC,gBAAgB,GAAG,CAACD,EAAE,GAAGlB,MAAM,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,MAAM,CAACD,KAAK,CAACqB,eAAe,KAAK,IAAI,GAAGF,EAAE,GAAG,EAAE;IACtG,IAAI,OAAOC,gBAAgB,KAAK,UAAU,EAAE;MAC1CA,gBAAgB,GAAGA,gBAAgB,CAACX,IAAI,CAAC,IAAI,EAAE;QAC7CF,QAAQ;QACRS,WAAW;QACXC,GAAG;QACHC;MACR,CAAO,CAAC;IACR;IACI,IAAMI,UAAU,GAAGC,oBAAoB,CAACP,WAAW,EAAEE,MAAM,CAACM,KAAK,EAAExB,KAAK,CAACyB,KAAK,EAAER,GAAG,CAAC;IACpFS,cAAc,CAACJ,UAAU,EAAE,MAAM,CAAC;IAClCI,cAAc,CAACJ,UAAU,EAAE,OAAO,CAAC;IACnC,OAAOK,MAAM,CAACC,MAAM,CAAC,EAAE,EAAER,gBAAgB,EAAEE,UAAU,CAAC;EAC1D,CAAG;EACD,IAAMO,kBAAkB,GAAG,SAArBA,kBAAkBA,CAAItB,QAAQ,EAAES,WAAW,EAAEC,GAAG,EAAEC,MAAM,EAAK;IACjE,IAAMY,YAAY,GAAGC,oBAAoB,CAAC3B,EAAE,CAAC4B,CAAC,EAAE,EAAEhB,WAAW,EAAEE,MAAM,CAACM,KAAK,EAAExB,KAAK,CAACyB,KAAK,EAAER,GAAG,CAAC;IAC9F,IAAMN,OAAO,IACXO,MAAM,CAACe,EAAE,EACTf,MAAM,CAACgB,KAAK,EACZhB,MAAM,CAACiB,WAAW,EAClBjB,MAAM,CAACkB,SAAS,EAChBlB,MAAM,CAACmB,cAAc,EAAAC,MAAA,CAAAC,kBAAA,CAClBT,YAAY,EAChB;IACD,IAAI,CAACZ,MAAM,CAACsB,QAAQ,EAAE;MACpB7B,OAAO,CAACE,IAAI,CAAC,SAAS,CAAC;IAC7B;IACI,IAAIK,MAAM,CAACuB,QAAQ,EAAE;MACnB9B,OAAO,CAACE,IAAI,CAAC,aAAa,CAAC;IACjC;IACI,IAAM6B,mBAAmB,GAAGzC,MAAM,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,MAAM,CAACD,KAAK,CAAC0C,mBAAmB;IACtF,IAAI,OAAOA,mBAAmB,KAAK,QAAQ,EAAE;MAC3C/B,OAAO,CAACE,IAAI,CAAC6B,mBAAmB,CAAC;IACvC,CAAK,MAAM,IAAI,OAAOA,mBAAmB,KAAK,UAAU,EAAE;MACpD/B,OAAO,CAACE,IAAI,CAAC6B,mBAAmB,CAACjC,IAAI,CAAC,IAAI,EAAE;QAC1CF,QAAQ;QACRS,WAAW;QACXC,GAAG;QACHC;MACR,CAAO,CAAC,CAAC;IACT;IACIP,OAAO,CAACE,IAAI,CAACT,EAAE,CAACuC,CAAC,CAAC,MAAM,CAAC,CAAC;IAC1B,OAAOhC,OAAO,CAACiC,MAAM,CAAC,UAACR,SAAS;MAAA,OAAKS,OAAO,CAACT,SAAS,CAAC;IAAA,EAAC,CAACtB,IAAI,CAAC,GAAG,CAAC;EACtE,CAAG;EACD,OAAO;IACLR,iBAAiB;IACjBI,iBAAiB;IACjBK,kBAAkB;IAClBc;EACJ,CAAG;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "import baseIteratee from './_baseIteratee.js';\nimport basePullAt from './_basePullAt.js';\n\n/**\n * Removes all elements from `array` that `predicate` returns truthy for\n * and returns an array of the removed elements. The predicate is invoked\n * with three arguments: (value, index, array).\n *\n * **Note:** Unlike `_.filter`, this method mutates `array`. Use `_.pull`\n * to pull elements from an array by value.\n *\n * @static\n * @memberOf _\n * @since 2.0.0\n * @category Array\n * @param {Array} array The array to modify.\n * @param {Function} [predicate=_.identity] The function invoked per iteration.\n * @returns {Array} Returns the new array of removed elements.\n * @example\n *\n * var array = [1, 2, 3, 4];\n * var evens = _.remove(array, function(n) {\n *   return n % 2 == 0;\n * });\n *\n * console.log(array);\n * // => [1, 3]\n *\n * console.log(evens);\n * // => [2, 4]\n */\nfunction remove(array, predicate) {\n  var result = [];\n  if (!(array && array.length)) {\n    return result;\n  }\n  var index = -1,\n    indexes = [],\n    length = array.length;\n  predicate = baseIteratee(predicate, 3);\n  while (++index < length) {\n    var value = array[index];\n    if (predicate(value, index, array)) {\n      result.push(value);\n      indexes.push(index);\n    }\n  }\n  basePullAt(array, indexes);\n  return result;\n}\nexport default remove;", "map": {"version": 3, "names": ["baseIteratee", "basePullAt", "remove", "array", "predicate", "result", "length", "index", "indexes", "value", "push"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/node_modules/.pnpm/lodash-es@4.17.21/node_modules/lodash-es/remove.js"], "sourcesContent": ["import baseIteratee from './_baseIteratee.js';\nimport basePullAt from './_basePullAt.js';\n\n/**\n * Removes all elements from `array` that `predicate` returns truthy for\n * and returns an array of the removed elements. The predicate is invoked\n * with three arguments: (value, index, array).\n *\n * **Note:** Unlike `_.filter`, this method mutates `array`. Use `_.pull`\n * to pull elements from an array by value.\n *\n * @static\n * @memberOf _\n * @since 2.0.0\n * @category Array\n * @param {Array} array The array to modify.\n * @param {Function} [predicate=_.identity] The function invoked per iteration.\n * @returns {Array} Returns the new array of removed elements.\n * @example\n *\n * var array = [1, 2, 3, 4];\n * var evens = _.remove(array, function(n) {\n *   return n % 2 == 0;\n * });\n *\n * console.log(array);\n * // => [1, 3]\n *\n * console.log(evens);\n * // => [2, 4]\n */\nfunction remove(array, predicate) {\n  var result = [];\n  if (!(array && array.length)) {\n    return result;\n  }\n  var index = -1,\n      indexes = [],\n      length = array.length;\n\n  predicate = baseIteratee(predicate, 3);\n  while (++index < length) {\n    var value = array[index];\n    if (predicate(value, index, array)) {\n      result.push(value);\n      indexes.push(index);\n    }\n  }\n  basePullAt(array, indexes);\n  return result;\n}\n\nexport default remove;\n"], "mappings": "AAAA,OAAOA,YAAY,MAAM,oBAAoB;AAC7C,OAAOC,UAAU,MAAM,kBAAkB;;AAEzC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,MAAMA,CAACC,KAAK,EAAEC,SAAS,EAAE;EAChC,IAAIC,MAAM,GAAG,EAAE;EACf,IAAI,EAAEF,KAAK,IAAIA,KAAK,CAACG,MAAM,CAAC,EAAE;IAC5B,OAAOD,MAAM;EACf;EACA,IAAIE,KAAK,GAAG,CAAC,CAAC;IACVC,OAAO,GAAG,EAAE;IACZF,MAAM,GAAGH,KAAK,CAACG,MAAM;EAEzBF,SAAS,GAAGJ,YAAY,CAACI,SAAS,EAAE,CAAC,CAAC;EACtC,OAAO,EAAEG,KAAK,GAAGD,MAAM,EAAE;IACvB,IAAIG,KAAK,GAAGN,KAAK,CAACI,KAAK,CAAC;IACxB,IAAIH,SAAS,CAACK,KAAK,EAAEF,KAAK,EAAEJ,KAAK,CAAC,EAAE;MAClCE,MAAM,CAACK,IAAI,CAACD,KAAK,CAAC;MAClBD,OAAO,CAACE,IAAI,CAACH,KAAK,CAAC;IACrB;EACF;EACAN,UAAU,CAACE,KAAK,EAAEK,OAAO,CAAC;EAC1B,OAAOH,MAAM;AACf;AAEA,eAAeH,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
<template>
  <div class="LiveShare">
    <!-- 直播详情组件，始终显示 -->
    <LiveBroadcastDetails :model-value="true" :id="liveId" @callback="handleCallback"
      @update:modelValue="handleModelValueUpdate" />
  </div>
</template>

<script>
export default { name: 'LiveShare' }
</script>

<script setup>
import { ref, onMounted } from 'vue'
import { useRoute } from 'vue-router'
import { ElMessage } from 'element-plus'
import LiveBroadcastDetails from './LiveBroadcastDetails.vue'

const route = useRoute()
// const router = useRouter()
const liveId = ref('')

onMounted(() => {
  // 从路由参数获取直播ID
  liveId.value = route.params.id

  if (!liveId.value) {
    ElMessage.error('直播ID不能为空')
    // 可以跳转到错误页面或首页
    return
  }
})

// 处理关闭回调 - 分享页面关闭时跳转到首页或显示提示
const handleCallback = () => {
  // 可以根据需要跳转到首页或其他页面
  ElMessage.info('感谢观看，您可以通过浏览器返回按钮离开此页面')
}

// 处理模型值更新 - 分享页面允许关闭
const handleModelValueUpdate = (value) => {
  if (!value) {
    // 用户点击关闭按钮时的处理
    handleCallback()
  }
}
</script>

<style lang="scss" scoped>
.LiveShare {
  width: 100%;
  height: 100vh;
  overflow: hidden;
}
</style>

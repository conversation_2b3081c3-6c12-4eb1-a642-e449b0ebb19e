<template>
  <div class="LiveShare">
    <!-- 调试信息 -->
    <div v-if="!liveId" class="debug-info">
      <p>正在加载直播信息...</p>
      <p>当前路由: {{ $route.path }}</p>
      <p>查询参数: {{ JSON.stringify($route.query) }}</p>
      <p>路径参数: {{ JSON.stringify($route.params) }}</p>
    </div>

    <!-- 直播详情组件，只有当有ID时才显示 -->
    <LiveBroadcastDetails v-if="liveId" :model-value="true" :id="liveId" :is-share="isShare" @callback="handleCallback"
      @update:modelValue="handleModelValueUpdate" />
  </div>
</template>

<script>
export default { name: 'LiveShare' }
</script>

<script setup>
import { ref, onMounted } from 'vue'
import { useRoute } from 'vue-router'
import { ElMessage } from 'element-plus'
import LiveBroadcastDetails from './LiveBroadcastDetails.vue'

const route = useRoute()
// const router = useRouter()
const liveId = ref('')
const isShare = ref(false)

onMounted(() => {
  // 从查询参数获取直播ID (因为现在使用 ?id= 格式)
  liveId.value = route.query.id || route.params.id

  // 检查是否为分享链接
  isShare.value = route.query.share === '1'

  console.log('LiveShare页面加载，直播ID:', liveId.value)
  console.log('是否为分享链接:', isShare.value)
  console.log('路由参数:', route.params)
  console.log('查询参数:', route.query)

  if (!liveId.value) {
    ElMessage.error('直播ID不能为空')
    console.error('未找到直播ID，路由信息:', route)
    return
  }
})

// 处理关闭回调 - 分享页面关闭时跳转到首页或显示提示
const handleCallback = () => {
  // 可以根据需要跳转到首页或其他页面
  ElMessage.info('感谢观看，您可以通过浏览器返回按钮离开此页面')
}

// 处理模型值更新 - 分享页面允许关闭
const handleModelValueUpdate = (value) => {
  if (!value) {
    // 用户点击关闭按钮时的处理
    handleCallback()
  }
}
</script>

<style lang="scss" scoped>
.LiveShare {
  width: 100%;
  height: 100vh;
  overflow: hidden;

  .debug-info {
    padding: 20px;
    background: #f5f5f5;
    border: 1px solid #ddd;
    margin: 20px;
    border-radius: 8px;

    p {
      margin: 8px 0;
      font-size: 14px;
      color: #666;
    }
  }
}
</style>

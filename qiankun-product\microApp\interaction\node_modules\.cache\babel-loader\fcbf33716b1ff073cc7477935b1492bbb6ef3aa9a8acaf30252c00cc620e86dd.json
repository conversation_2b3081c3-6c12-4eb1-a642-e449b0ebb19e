{"ast": null, "code": "import '../../../utils/index.mjs';\nimport { transferProps, transferCheckedChangeFn } from './transfer.mjs';\nimport { buildProps, definePropType } from '../../../utils/vue/props/runtime.mjs';\nvar CHECKED_CHANGE_EVENT = \"checked-change\";\nvar transferPanelProps = buildProps({\n  data: transferProps.data,\n  optionRender: {\n    type: definePropType(Function)\n  },\n  placeholder: String,\n  title: String,\n  filterable: Boolean,\n  format: transferProps.format,\n  filterMethod: transferProps.filterMethod,\n  defaultChecked: transferProps.leftDefaultChecked,\n  props: transferProps.props\n});\nvar transferPanelEmits = {\n  [CHECKED_CHANGE_EVENT]: transferCheckedChangeFn\n};\nexport { CHECKED_CHANGE_EVENT, transferPanelEmits, transferPanelProps };", "map": {"version": 3, "names": ["CHECKED_CHANGE_EVENT", "transferPanelProps", "buildProps", "data", "transferProps", "optionRender", "type", "definePropType", "Function", "placeholder", "String", "title", "filterable", "Boolean", "format", "filterMethod", "defaultChecked", "leftDefaultChecked", "props", "transferPanelEmits", "transferCheckedChangeFn"], "sources": ["../../../../../../packages/components/transfer/src/transfer-panel.ts"], "sourcesContent": ["import { buildProps, definePropType } from '@element-plus/utils'\nimport { transferCheckedChangeFn, transferProps } from './transfer'\n\nimport type { ExtractPropTypes, VNode } from 'vue'\nimport type { TransferDataItem, TransferKey } from './transfer'\nimport type TransferPanel from './transfer-panel.vue'\n\nexport interface TransferPanelState {\n  checked: TransferKey[]\n  allChecked: boolean\n  query: string\n  checkChangeByUser: boolean\n}\n\nexport const CHECKED_CHANGE_EVENT = 'checked-change'\n\nexport const transferPanelProps = buildProps({\n  data: transferProps.data,\n  optionRender: {\n    type: definePropType<(option: TransferDataItem) => VNode | VNode[]>(\n      Function\n    ),\n  },\n  placeholder: String,\n  title: String,\n  filterable: Boolean,\n  format: transferProps.format,\n  filterMethod: transferProps.filterMethod,\n  defaultChecked: transferProps.leftDefaultChecked,\n  props: transferProps.props,\n} as const)\nexport type TransferPanelProps = ExtractPropTypes<typeof transferPanelProps>\n\nexport const transferPanelEmits = {\n  [CHECKED_CHANGE_EVENT]: transferCheckedChangeFn,\n}\nexport type TransferPanelEmits = typeof transferPanelEmits\n\nexport type TransferPanelInstance = InstanceType<typeof TransferPanel>\n"], "mappings": ";;;AAEY,IAACA,oBAAoB,GAAG;AACxB,IAACC,kBAAkB,GAAGC,UAAU,CAAC;EAC3CC,IAAI,EAAEC,aAAa,CAACD,IAAI;EACxBE,YAAY,EAAE;IACZC,IAAI,EAAEC,cAAc,CAACC,QAAQ;EACjC,CAAG;EACDC,WAAW,EAAEC,MAAM;EACnBC,KAAK,EAAED,MAAM;EACbE,UAAU,EAAEC,OAAO;EACnBC,MAAM,EAAEV,aAAa,CAACU,MAAM;EAC5BC,YAAY,EAAEX,aAAa,CAACW,YAAY;EACxCC,cAAc,EAAEZ,aAAa,CAACa,kBAAkB;EAChDC,KAAK,EAAEd,aAAa,CAACc;AACvB,CAAC;AACW,IAACC,kBAAkB,GAAG;EAChC,CAACnB,oBAAoB,GAAGoB;AAC1B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
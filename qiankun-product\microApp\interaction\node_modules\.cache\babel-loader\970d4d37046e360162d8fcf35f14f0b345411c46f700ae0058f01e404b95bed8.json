{"ast": null, "code": "/** Error message constants. */\nvar FUNC_ERROR_TEXT = 'Expected a function';\n\n/**\n * The base implementation of `_.delay` and `_.defer` which accepts `args`\n * to provide to `func`.\n *\n * @private\n * @param {Function} func The function to delay.\n * @param {number} wait The number of milliseconds to delay invocation.\n * @param {Array} args The arguments to provide to `func`.\n * @returns {number|Object} Returns the timer id or timeout object.\n */\nfunction baseDelay(func, wait, args) {\n  if (typeof func != 'function') {\n    throw new TypeError(FUNC_ERROR_TEXT);\n  }\n  return setTimeout(function () {\n    func.apply(undefined, args);\n  }, wait);\n}\nexport default baseDelay;", "map": {"version": 3, "names": ["FUNC_ERROR_TEXT", "baseDelay", "func", "wait", "args", "TypeError", "setTimeout", "apply", "undefined"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/node_modules/.pnpm/lodash-es@4.17.21/node_modules/lodash-es/_baseDelay.js"], "sourcesContent": ["/** Error message constants. */\nvar FUNC_ERROR_TEXT = 'Expected a function';\n\n/**\n * The base implementation of `_.delay` and `_.defer` which accepts `args`\n * to provide to `func`.\n *\n * @private\n * @param {Function} func The function to delay.\n * @param {number} wait The number of milliseconds to delay invocation.\n * @param {Array} args The arguments to provide to `func`.\n * @returns {number|Object} Returns the timer id or timeout object.\n */\nfunction baseDelay(func, wait, args) {\n  if (typeof func != 'function') {\n    throw new TypeError(FUNC_ERROR_TEXT);\n  }\n  return setTimeout(function() { func.apply(undefined, args); }, wait);\n}\n\nexport default baseDelay;\n"], "mappings": "AAAA;AACA,IAAIA,eAAe,GAAG,qBAAqB;;AAE3C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,SAASA,CAACC,IAAI,EAAEC,IAAI,EAAEC,IAAI,EAAE;EACnC,IAAI,OAAOF,IAAI,IAAI,UAAU,EAAE;IAC7B,MAAM,IAAIG,SAAS,CAACL,eAAe,CAAC;EACtC;EACA,OAAOM,UAAU,CAAC,YAAW;IAAEJ,IAAI,CAACK,KAAK,CAACC,SAAS,EAAEJ,IAAI,CAAC;EAAE,CAAC,EAAED,IAAI,CAAC;AACtE;AAEA,eAAeF,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
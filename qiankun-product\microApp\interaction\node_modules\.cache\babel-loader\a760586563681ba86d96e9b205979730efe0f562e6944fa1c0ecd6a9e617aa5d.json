{"ast": null, "code": "import { createElementVNode as _createElementVNode, toDisplayString as _toDisplayString, resolveComponent as _resolveComponent, withCtx as _withCtx, createVNode as _createVNode, createTextVNode as _createTextVNode, renderList as _renderList, Fragment as _Fragment, openBlock as _openBlock, createElementBlock as _createElementBlock } from \"vue\";\nvar _hoisted_1 = {\n  class: \"SendTextMessageVerify\"\n};\nvar _hoisted_2 = {\n  class: \"SendTextMessageVerifyBody\"\n};\nvar _hoisted_3 = {\n  class: \"SendTextMessageVerifyUserBody\"\n};\nvar _hoisted_4 = {\n  class: \"SendTextMessageVerifyUserName\"\n};\nvar _hoisted_5 = {\n  class: \"SendTextMessageVerifyUserText\"\n};\nvar _hoisted_6 = {\n  class: \"SendTextMessageVerifyCellphoneBody\"\n};\nvar _hoisted_7 = {\n  class: \"SendTextMessageVerifyCellphone\"\n};\nvar _hoisted_8 = {\n  class: \"SendTextMessageVerifyButton\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  var _component_el_scrollbar = _resolveComponent(\"el-scrollbar\");\n  var _component_el_input = _resolveComponent(\"el-input\");\n  var _component_el_button = _resolveComponent(\"el-button\");\n  var _component_el_form_item = _resolveComponent(\"el-form-item\");\n  var _component_el_form = _resolveComponent(\"el-form\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_cache[4] || (_cache[4] = _createElementVNode(\"div\", {\n    class: \"SendTextMessageVerifyTips\"\n  }, \"手机短信发出不可撤回，请您认真确认短信接收人和接收内容！\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_2, [_createElementVNode(\"div\", _hoisted_3, [_createElementVNode(\"div\", _hoisted_4, \"接收人（\" + _toDisplayString($setup.props.name.split('、').length) + \"）\", 1 /* TEXT */), _createVNode(_component_el_scrollbar, {\n    always: \"\",\n    class: \"SendTextMessageVerifyUserScrollbar\"\n  }, {\n    default: _withCtx(function () {\n      return [_createElementVNode(\"div\", _hoisted_5, _toDisplayString($setup.props.name), 1 /* TEXT */)];\n    }),\n    _: 1 /* STABLE */\n  }), _createVNode(_component_el_form, {\n    ref: \"formRef\",\n    model: $setup.form,\n    rules: $setup.rules,\n    inline: \"\",\n    \"label-position\": \"top\",\n    class: \"globalForm\"\n  }, {\n    default: _withCtx(function () {\n      return [_createVNode(_component_el_form_item, {\n        label: \"短信验证码\",\n        prop: \"verifyCode\",\n        class: \"globalFormVerifyCode\"\n      }, {\n        default: _withCtx(function () {\n          return [_createVNode(_component_el_input, {\n            modelValue: $setup.form.verifyCode,\n            \"onUpdate:modelValue\": _cache[0] || (_cache[0] = function ($event) {\n              return $setup.form.verifyCode = $event;\n            }),\n            placeholder: \"短信验证码\",\n            clearable: \"\"\n          }, null, 8 /* PROPS */, [\"modelValue\"]), _createVNode(_component_el_button, {\n            type: \"primary\",\n            disabled: $setup.verifyCodeText != '获取验证码',\n            onClick: $setup.handleVerifyCode\n          }, {\n            default: _withCtx(function () {\n              return [_createTextVNode(_toDisplayString($setup.verifyCodeText), 1 /* TEXT */)];\n            }),\n            _: 1 /* STABLE */\n          }, 8 /* PROPS */, [\"disabled\"])];\n        }),\n        _: 1 /* STABLE */\n      })];\n    }),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"model\", \"rules\"])]), _createElementVNode(\"div\", _hoisted_6, [_createElementVNode(\"div\", _hoisted_7, [_createVNode(_component_el_scrollbar, {\n    always: \"\",\n    class: \"SendTextMessageVerifyCellphoneScrollbar\"\n  }, {\n    default: _withCtx(function () {\n      return [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.contentArr, function (item, index) {\n        return _openBlock(), _createElementBlock(\"div\", {\n          class: \"SendTextMessageVerifyCellphoneText\",\n          key: index\n        }, _toDisplayString(item), 1 /* TEXT */);\n      }), 128 /* KEYED_FRAGMENT */))];\n    }),\n    _: 1 /* STABLE */\n  })])])]), _createElementVNode(\"div\", _hoisted_8, [_createVNode(_component_el_button, {\n    type: \"primary\",\n    onClick: _cache[1] || (_cache[1] = function ($event) {\n      return $setup.submitForm($setup.formRef);\n    })\n  }, {\n    default: _withCtx(function () {\n      return _cache[2] || (_cache[2] = [_createTextVNode(\"提交\")]);\n    }),\n    _: 1 /* STABLE */\n  }), _createVNode(_component_el_button, {\n    onClick: $setup.resetForm\n  }, {\n    default: _withCtx(function () {\n      return _cache[3] || (_cache[3] = [_createTextVNode(\"取消\")]);\n    }),\n    _: 1 /* STABLE */\n  })])]);\n}", "map": {"version": 3, "names": ["class", "_createElementBlock", "_hoisted_1", "_createElementVNode", "_hoisted_2", "_hoisted_3", "_hoisted_4", "_toDisplayString", "$setup", "props", "name", "split", "length", "_createVNode", "_component_el_scrollbar", "always", "default", "_withCtx", "_hoisted_5", "_", "_component_el_form", "ref", "model", "form", "rules", "inline", "_component_el_form_item", "label", "prop", "_component_el_input", "modelValue", "verifyCode", "_cache", "$event", "placeholder", "clearable", "_component_el_button", "type", "disabled", "verifyCodeText", "onClick", "handleVerifyCode", "_createTextVNode", "_hoisted_6", "_hoisted_7", "_Fragment", "_renderList", "contentArr", "item", "index", "key", "_hoisted_8", "submitForm", "formRef", "resetForm"], "sources": ["D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\interaction\\src\\views\\TextMessageManage\\component\\SendTextMessageVerify.vue"], "sourcesContent": ["<template>\r\n  <div class=\"SendTextMessageVerify\">\r\n    <div class=\"SendTextMessageVerifyTips\">手机短信发出不可撤回，请您认真确认短信接收人和接收内容！</div>\r\n    <div class=\"SendTextMessageVerifyBody\">\r\n      <div class=\"SendTextMessageVerifyUserBody\">\r\n        <div class=\"SendTextMessageVerifyUserName\">接收人（{{ props.name.split('、').length }}）</div>\r\n        <el-scrollbar always class=\"SendTextMessageVerifyUserScrollbar\">\r\n          <div class=\"SendTextMessageVerifyUserText\">{{ props.name }}</div>\r\n        </el-scrollbar>\r\n        <el-form ref=\"formRef\" :model=\"form\" :rules=\"rules\" inline label-position=\"top\" class=\"globalForm\">\r\n          <el-form-item label=\"短信验证码\" prop=\"verifyCode\" class=\"globalFormVerifyCode\">\r\n            <el-input v-model=\"form.verifyCode\" placeholder=\"短信验证码\" clearable />\r\n            <el-button type=\"primary\" :disabled=\"verifyCodeText != '获取验证码'\" @click=\"handleVerifyCode\">{{ verifyCodeText\r\n              }}</el-button>\r\n          </el-form-item>\r\n        </el-form>\r\n      </div>\r\n      <div class=\"SendTextMessageVerifyCellphoneBody\">\r\n        <div class=\"SendTextMessageVerifyCellphone\">\r\n          <el-scrollbar always class=\"SendTextMessageVerifyCellphoneScrollbar\">\r\n            <div class=\"SendTextMessageVerifyCellphoneText\" v-for=\"(item, index) in contentArr\" :key=\"index\">{{ item }}\r\n            </div>\r\n          </el-scrollbar>\r\n        </div>\r\n      </div>\r\n    </div>\r\n    <div class=\"SendTextMessageVerifyButton\">\r\n      <el-button type=\"primary\" @click=\"submitForm(formRef)\">提交</el-button>\r\n      <el-button @click=\"resetForm\">取消</el-button>\r\n    </div>\r\n  </div>\r\n</template>\r\n<script>\r\nexport default { name: 'SendTextMessageVerify' }\r\n</script>\r\n<script setup>\r\nimport api from '@/api'\r\nimport { ref, reactive, onMounted } from 'vue'\r\nimport { ElMessage } from 'element-plus'\r\nconst props = defineProps({\r\n  name: { type: String, default: '' },\r\n  content: { type: String, default: '' },\r\n  params: { type: Object, default: () => ({}) }\r\n})\r\nconst emit = defineEmits(['callback'])\r\n\r\nconst formRef = ref()\r\nconst form = reactive({ verfiyCodeId: '', verifyCode: '' })\r\nconst rules = reactive({ verifyCode: [{ required: true, message: '请输入短信验证码', trigger: ['blur', 'change'] }] })\r\n\r\nconst contentArr = ref([])\r\nconst countdown = ref(0)\r\nconst verifyCodeText = ref('获取验证码')\r\n\r\nconst group = (array, subGroupLength) => {\r\n  let index = 0\r\n  const newArray = []\r\n  while (index < array.length) {\r\n    newArray.push(array.slice(index, index += subGroupLength))\r\n  }\r\n  return newArray\r\n}\r\n\r\nonMounted(() => {\r\n  console.log(props.content.length)\r\n  contentArr.value = group(props.content, 500)\r\n  console.log(contentArr.value)\r\n})\r\nconst handleVerifyCode = async () => {\r\n  const { code, data } = await api.verifyCodeSend({ sendType: 'has_login' })\r\n  if (code === 200) {\r\n    ElMessage({ type: 'success', message: '验证码发送成功' })\r\n    countdown.value = 60\r\n    form.verfiyCodeId = data\r\n    verifyCodeCountdown()\r\n  }\r\n}\r\nconst verifyCodeCountdown = () => {\r\n  if (countdown.value === 0) {\r\n    verifyCodeText.value = '获取验证码'\r\n    countdown.value = 60\r\n    return\r\n  } else {\r\n    verifyCodeText.value = '重新发送' + countdown.value + 'S'\r\n    countdown.value--\r\n  }\r\n  setTimeout(() => { verifyCodeCountdown() }, 1000)\r\n}\r\nconst submitForm = async (formEl) => {\r\n  console.log({ ...props.params, verfiyCodeId: form.verfiyCodeId, verifyCode: form.verifyCode })\r\n  if (!formEl) return\r\n  await formEl.validate((valid, fields) => {\r\n    if (valid) { globalJson() } else { ElMessage({ type: 'warning', message: '请根据提示信息完善字段内容！' }) }\r\n  })\r\n}\r\nconst globalJson = async () => {\r\n  const { code } = await api.globalJson('/textMessage/add', { ...props.params, verfiyCodeId: form.verfiyCodeId, verifyCode: form.verifyCode })\r\n  if (code === 200) {\r\n    ElMessage({ type: 'success', message: '发送成功' })\r\n    emit('callback', true)\r\n  }\r\n}\r\nconst resetForm = () => { emit('callback') }\r\n</script>\r\n<style lang=\"scss\">\r\n.SendTextMessageVerify {\r\n  width: 860px;\r\n\r\n  .SendTextMessageVerifyTips {\r\n    padding: 0 var(--zy-distance-one);\r\n    padding-top: var(--zy-distance-two);\r\n    padding-bottom: var(--zy-font-name-distance-five);\r\n    color: var(--el-color-danger);\r\n    font-size: var(--zy-name-font-size);\r\n    line-height: var(--zy-line-height);\r\n  }\r\n\r\n  .SendTextMessageVerifyBody {\r\n    display: flex;\r\n    align-items: center;\r\n  }\r\n\r\n  .SendTextMessageVerifyUserBody {\r\n\r\n    .SendTextMessageVerifyUserName {\r\n      font-size: var(--zy-name-font-size);\r\n      line-height: var(--zy-line-height);\r\n      padding: 0 var(--zy-distance-one);\r\n      padding-bottom: var(--zy-font-name-distance-five);\r\n      font-weight: bold;\r\n    }\r\n\r\n    .SendTextMessageVerifyUserScrollbar {\r\n      width: calc(100% - (var(--zy-distance-one)*2));\r\n      height: 460px;\r\n      margin: auto;\r\n      border: 1px solid var(--zy-el-border-color-lighter);\r\n\r\n      .zy-el-scrollbar__view {\r\n        padding: var(--zy-distance-two);\r\n      }\r\n    }\r\n\r\n    .globalForm {\r\n      width: 520px;\r\n      height: auto;\r\n    }\r\n  }\r\n\r\n  .SendTextMessageVerifyCellphoneBody {\r\n    width: 340px;\r\n    padding: var(--zy-distance-one);\r\n    padding-top: 0;\r\n    padding-left: 0;\r\n\r\n    .SendTextMessageVerifyCellphone {\r\n      width: 300px;\r\n      height: 600px;\r\n      background: url('../../../assets/img/send_text_message_verify_cellphone.png') no-repeat;\r\n      background-size: 100% 100%;\r\n      padding: 80px 5px 32px 5px;\r\n\r\n      .SendTextMessageVerifyCellphoneScrollbar {\r\n        width: 100%;\r\n        height: 100%;\r\n\r\n        .zy-el-scrollbar__view {\r\n          padding: 0 var(--zy-distance-three);\r\n        }\r\n      }\r\n\r\n      .SendTextMessageVerifyCellphoneText {\r\n        font-size: var(--zy-text-font-size);\r\n        line-height: var(--zy-line-height);\r\n        background-color: var(--zy-el-color-info-light-9);\r\n        padding: var(--zy-font-name-distance-five) var(--zy-distance-five);\r\n        border-radius: var(--el-border-radius-base);\r\n      }\r\n\r\n      .SendTextMessageVerifyCellphoneText+.SendTextMessageVerifyCellphoneText {\r\n        margin-top: var(--zy-distance-four);\r\n      }\r\n    }\r\n  }\r\n\r\n\r\n  .SendTextMessageVerifyButton {\r\n    width: 100%;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    padding-bottom: var(--zy-distance-two);\r\n\r\n    .zy-el-button+.zy-el-button {\r\n      margin-left: var(--zy-distance-two);\r\n    }\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;EACOA,KAAK,EAAC;AAAuB;;EAE3BA,KAAK,EAAC;AAA2B;;EAC/BA,KAAK,EAAC;AAA+B;;EACnCA,KAAK,EAAC;AAA+B;;EAEnCA,KAAK,EAAC;AAA+B;;EAUzCA,KAAK,EAAC;AAAoC;;EACxCA,KAAK,EAAC;AAAgC;;EAQ1CA,KAAK,EAAC;AAA6B;;;;;;;uBAzB1CC,mBAAA,CA6BM,OA7BNC,UA6BM,G,0BA5BJC,mBAAA,CAAyE;IAApEH,KAAK,EAAC;EAA2B,GAAC,8BAA4B,sBACnEG,mBAAA,CAsBM,OAtBNC,UAsBM,GArBJD,mBAAA,CAYM,OAZNE,UAYM,GAXJF,mBAAA,CAAwF,OAAxFG,UAAwF,EAA7C,MAAI,GAAAC,gBAAA,CAAGC,MAAA,CAAAC,KAAK,CAACC,IAAI,CAACC,KAAK,MAAMC,MAAM,IAAG,GAAC,iBAClFC,YAAA,CAEeC,uBAAA;IAFDC,MAAM,EAAN,EAAM;IAACf,KAAK,EAAC;;IANnCgB,OAAA,EAAAC,QAAA,CAOU;MAAA,OAAiE,CAAjEd,mBAAA,CAAiE,OAAjEe,UAAiE,EAAAX,gBAAA,CAAnBC,MAAA,CAAAC,KAAK,CAACC,IAAI,iB;;IAPlES,CAAA;MASQN,YAAA,CAMUO,kBAAA;IANDC,GAAG,EAAC,SAAS;IAAEC,KAAK,EAAEd,MAAA,CAAAe,IAAI;IAAGC,KAAK,EAAEhB,MAAA,CAAAgB,KAAK;IAAEC,MAAM,EAAN,EAAM;IAAC,gBAAc,EAAC,KAAK;IAACzB,KAAK,EAAC;;IAT9FgB,OAAA,EAAAC,QAAA,CAUU;MAAA,OAIe,CAJfJ,YAAA,CAIea,uBAAA;QAJDC,KAAK,EAAC,OAAO;QAACC,IAAI,EAAC,YAAY;QAAC5B,KAAK,EAAC;;QAV9DgB,OAAA,EAAAC,QAAA,CAWY;UAAA,OAAoE,CAApEJ,YAAA,CAAoEgB,mBAAA;YAXhFC,UAAA,EAW+BtB,MAAA,CAAAe,IAAI,CAACQ,UAAU;YAX9C,uBAAAC,MAAA,QAAAA,MAAA,gBAAAC,MAAA;cAAA,OAW+BzB,MAAA,CAAAe,IAAI,CAACQ,UAAU,GAAAE,MAAA;YAAA;YAAEC,WAAW,EAAC,OAAO;YAACC,SAAS,EAAT;mDACxDtB,YAAA,CACgBuB,oBAAA;YADLC,IAAI,EAAC,SAAS;YAAEC,QAAQ,EAAE9B,MAAA,CAAA+B,cAAc;YAAcC,OAAK,EAAEhC,MAAA,CAAAiC;;YAZpFzB,OAAA,EAAAC,QAAA,CAYsG;cAAA,OACtF,CAbhByB,gBAAA,CAAAnC,gBAAA,CAYyGC,MAAA,CAAA+B,cAAc,iB;;YAZvHpB,CAAA;;;QAAAA,CAAA;;;IAAAA,CAAA;2CAiBMhB,mBAAA,CAOM,OAPNwC,UAOM,GANJxC,mBAAA,CAKM,OALNyC,UAKM,GAJJ/B,YAAA,CAGeC,uBAAA;IAHDC,MAAM,EAAN,EAAM;IAACf,KAAK,EAAC;;IAnBrCgB,OAAA,EAAAC,QAAA,CAoB4D;MAAA,OAAmC,E,kBAAnFhB,mBAAA,CACM4C,SAAA,QArBlBC,WAAA,CAoBoFtC,MAAA,CAAAuC,UAAU,EApB9F,UAoBoEC,IAAI,EAAEC,KAAK;6BAAnEhD,mBAAA,CACM;UADDD,KAAK,EAAC,oCAAoC;UAAsCkD,GAAG,EAAED;4BAAUD,IAAI;;;IApBpH7B,CAAA;YA0BIhB,mBAAA,CAGM,OAHNgD,UAGM,GAFJtC,YAAA,CAAqEuB,oBAAA;IAA1DC,IAAI,EAAC,SAAS;IAAEG,OAAK,EAAAR,MAAA,QAAAA,MAAA,gBAAAC,MAAA;MAAA,OAAEzB,MAAA,CAAA4C,UAAU,CAAC5C,MAAA,CAAA6C,OAAO;IAAA;;IA3B1DrC,OAAA,EAAAC,QAAA,CA2B6D;MAAA,OAAEe,MAAA,QAAAA,MAAA,OA3B/DU,gBAAA,CA2B6D,IAAE,E;;IA3B/DvB,CAAA;MA4BMN,YAAA,CAA4CuB,oBAAA;IAAhCI,OAAK,EAAEhC,MAAA,CAAA8C;EAAS;IA5BlCtC,OAAA,EAAAC,QAAA,CA4BoC;MAAA,OAAEe,MAAA,QAAAA,MAAA,OA5BtCU,gBAAA,CA4BoC,IAAE,E;;IA5BtCvB,CAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
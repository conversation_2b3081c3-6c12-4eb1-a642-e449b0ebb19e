{"ast": null, "code": "import baseIteratee from './_baseIteratee.js';\nimport baseSortedIndexBy from './_baseSortedIndexBy.js';\n\n/**\n * This method is like `_.sortedIndex` except that it accepts `iteratee`\n * which is invoked for `value` and each element of `array` to compute their\n * sort ranking. The iteratee is invoked with one argument: (value).\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category Array\n * @param {Array} array The sorted array to inspect.\n * @param {*} value The value to evaluate.\n * @param {Function} [iteratee=_.identity] The iteratee invoked per element.\n * @returns {number} Returns the index at which `value` should be inserted\n *  into `array`.\n * @example\n *\n * var objects = [{ 'x': 4 }, { 'x': 5 }];\n *\n * _.sortedIndexBy(objects, { 'x': 4 }, function(o) { return o.x; });\n * // => 0\n *\n * // The `_.property` iteratee shorthand.\n * _.sortedIndexBy(objects, { 'x': 4 }, 'x');\n * // => 0\n */\nfunction sortedIndexBy(array, value, iteratee) {\n  return baseSortedIndexBy(array, value, baseIteratee(iteratee, 2));\n}\nexport default sortedIndexBy;", "map": {"version": 3, "names": ["baseIteratee", "baseSortedIndexBy", "sortedIndexBy", "array", "value", "iteratee"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/node_modules/.pnpm/lodash-es@4.17.21/node_modules/lodash-es/sortedIndexBy.js"], "sourcesContent": ["import baseIteratee from './_baseIteratee.js';\nimport baseSortedIndexBy from './_baseSortedIndexBy.js';\n\n/**\n * This method is like `_.sortedIndex` except that it accepts `iteratee`\n * which is invoked for `value` and each element of `array` to compute their\n * sort ranking. The iteratee is invoked with one argument: (value).\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category Array\n * @param {Array} array The sorted array to inspect.\n * @param {*} value The value to evaluate.\n * @param {Function} [iteratee=_.identity] The iteratee invoked per element.\n * @returns {number} Returns the index at which `value` should be inserted\n *  into `array`.\n * @example\n *\n * var objects = [{ 'x': 4 }, { 'x': 5 }];\n *\n * _.sortedIndexBy(objects, { 'x': 4 }, function(o) { return o.x; });\n * // => 0\n *\n * // The `_.property` iteratee shorthand.\n * _.sortedIndexBy(objects, { 'x': 4 }, 'x');\n * // => 0\n */\nfunction sortedIndexBy(array, value, iteratee) {\n  return baseSortedIndexBy(array, value, baseIteratee(iteratee, 2));\n}\n\nexport default sortedIndexBy;\n"], "mappings": "AAAA,OAAOA,YAAY,MAAM,oBAAoB;AAC7C,OAAOC,iBAAiB,MAAM,yBAAyB;;AAEvD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,aAAaA,CAACC,KAAK,EAAEC,KAAK,EAAEC,QAAQ,EAAE;EAC7C,OAAOJ,iBAAiB,CAACE,KAAK,EAAEC,KAAK,EAAEJ,YAAY,CAACK,QAAQ,EAAE,CAAC,CAAC,CAAC;AACnE;AAEA,eAAeH,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "import { renderList as _renderList, Fragment as _Fragment, openBlock as _openBlock, createElementBlock as _createElementBlock, createElementVNode as _createElementVNode, resolveComponent as _resolveComponent, createVNode as _createVNode, withCtx as _withCtx, createBlock as _createBlock, createCommentVNode as _createCommentVNode } from \"vue\";\nvar _hoisted_1 = {\n  class: \"global-drag-input\",\n  ref: \"optionsRef\"\n};\nvar _hoisted_2 = {\n  class: \"global-drag-input-button\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  var _component_el_input = _resolveComponent(\"el-input\");\n  var _component_CirclePlus = _resolveComponent(\"CirclePlus\");\n  var _component_el_icon = _resolveComponent(\"el-icon\");\n  var _component_Remove = _resolveComponent(\"Remove\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.options, function (item) {\n    return _openBlock(), _createElementBlock(\"div\", {\n      class: \"global-drag-input-item\",\n      key: item.uid\n    }, [_cache[0] || (_cache[0] = _createElementVNode(\"div\", {\n      class: \"global-drag-input-icon\"\n    }, null, -1 /* HOISTED */)), _createVNode(_component_el_input, {\n      modelValue: item.name,\n      \"onUpdate:modelValue\": function onUpdateModelValue($event) {\n        return item.name = $event;\n      },\n      onChange: $setup.optionsChange,\n      placeholder: \"请输入\",\n      disabled: $setup.disabled,\n      clearable: \"\"\n    }, null, 8 /* PROPS */, [\"modelValue\", \"onUpdate:modelValue\", \"disabled\"]), _createElementVNode(\"div\", _hoisted_2, [!$setup.disabled ? (_openBlock(), _createBlock(_component_el_icon, {\n      key: 0,\n      onClick: $setup.optionsNew\n    }, {\n      default: _withCtx(function () {\n        return [_createVNode(_component_CirclePlus)];\n      }),\n      _: 1 /* STABLE */\n    })) : _createCommentVNode(\"v-if\", true), $setup.options.length > 1 && !$setup.disabled ? (_openBlock(), _createBlock(_component_el_icon, {\n      key: 1,\n      onClick: function onClick($event) {\n        return $setup.optionsDel(item);\n      }\n    }, {\n      default: _withCtx(function () {\n        return [_createVNode(_component_Remove)];\n      }),\n      _: 2 /* DYNAMIC */\n    }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"onClick\"])) : _createCommentVNode(\"v-if\", true)])]);\n  }), 128 /* KEYED_FRAGMENT */))], 512 /* NEED_PATCH */);\n}", "map": {"version": 3, "names": ["class", "ref", "_createElementBlock", "_hoisted_1", "_Fragment", "_renderList", "$setup", "options", "item", "key", "uid", "_createElementVNode", "_createVNode", "_component_el_input", "modelValue", "name", "onUpdateModelValue", "$event", "onChange", "optionsChange", "placeholder", "disabled", "clearable", "_hoisted_2", "_createBlock", "_component_el_icon", "onClick", "optionsNew", "default", "_withCtx", "_component_CirclePlus", "_", "_createCommentVNode", "length", "optionsDel", "_component_Remove"], "sources": ["D:\\zy\\xm\\pc\\5.0\\qiankun-product\\common\\components\\global-drag-input\\global-drag-input.vue"], "sourcesContent": ["<template>\r\n  <div class=\"global-drag-input\" ref=\"optionsRef\">\r\n    <div class=\"global-drag-input-item\" v-for=\"item in options\" :key=\"item.uid\">\r\n      <div class=\"global-drag-input-icon\"></div>\r\n      <el-input v-model=\"item.name\" @change=\"optionsChange\" placeholder=\"请输入\" :disabled=\"disabled\" clearable />\r\n      <div class=\"global-drag-input-button\">\r\n        <el-icon @click=\"optionsNew\" v-if=\"!disabled\">\r\n          <CirclePlus />\r\n        </el-icon>\r\n        <el-icon @click=\"optionsDel(item)\" v-if=\"options.length > 1 && !disabled\">\r\n          <Remove />\r\n        </el-icon>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n<script>\r\nexport default { name: 'GlobalDragInput' }\r\n</script>\r\n<script setup>\r\nimport { ref, onMounted, computed, watch, nextTick } from 'vue'\r\nimport Sortable from 'sortablejs' // 引入插件\r\nconst props = defineProps({\r\n  data: { type: Array, default: () => [] },\r\n  disabled: { type: Boolean, default: false }\r\n})\r\nconst emit = defineEmits(['callback'])\r\nconst guid = () => {\r\n  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, (c) => {\r\n    var r = (Math.random() * 16) | 0,\r\n      v = c == 'x' ? r : (r & 0x3) | 0x8\r\n    return v.toString(16)\r\n  })\r\n}\r\nconst optionsRef = ref()\r\nconst options = ref([{ uid: guid(), name: '' }])\r\nconst disabled = computed(() => props.disabled)\r\n\r\nonMounted(() => {\r\n  nextTick(() => {\r\n    if (!props.disabled) {\r\n      rowDrop()\r\n    }\r\n  })\r\n})\r\n\r\nconst optionsNew = () => {\r\n  options.value.push({ uid: guid(), name: '' })\r\n}\r\nconst optionsChange = () => {\r\n  emit(\r\n    'callback',\r\n    options.value.filter((v) => v.name.replace(/(^\\s*)|(\\s*$)/g, '')).map((v) => v.name.replace(/(^\\s*)|(\\s*$)/g, ''))\r\n  )\r\n}\r\nconst optionsDel = (row) => {\r\n  options.value = options.value.filter((v) => v.uid !== row.uid)\r\n  if (row.name) {\r\n    optionsChange()\r\n  }\r\n}\r\nconst rowDrop = () => {\r\n  Sortable.create(optionsRef.value, {\r\n    handle: '.global-drag-input-icon',\r\n    animation: 150,\r\n    onEnd({ newIndex, oldIndex }) {\r\n      if (disabled.value) return\r\n      if (newIndex == oldIndex) return\r\n      options.value.splice(newIndex, 0, options.value.splice(oldIndex, 1)[0])\r\n      const newArray = options.value.slice(0)\r\n      options.value = []\r\n      nextTick(() => {\r\n        options.value = newArray\r\n        optionsChange()\r\n      })\r\n    }\r\n  })\r\n}\r\nwatch(\r\n  () => props.data,\r\n  () => {\r\n    if (props.data.length) {\r\n      options.value = props.data.map((v) => ({ uid: guid(), name: v }))\r\n      optionsChange()\r\n    }\r\n  },\r\n  { immediate: true }\r\n)\r\n</script>\r\n<style lang=\"scss\">\r\n.global-drag-input {\r\n  width: 100%;\r\n  padding-right: 20px;\r\n  padding-bottom: 22px;\r\n\r\n  .global-drag-input-item + .global-drag-input-item {\r\n    margin-top: 10px;\r\n  }\r\n\r\n  .global-drag-input-item {\r\n    width: 100%;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: space-between;\r\n\r\n    .global-drag-input-icon {\r\n      width: 20px;\r\n      height: 20px;\r\n      background: url('./img/global_form_options.png') no-repeat;\r\n      background-size: 100% 100%;\r\n      cursor: pointer;\r\n    }\r\n\r\n    .zy-el-input {\r\n      width: calc(100% - 88px);\r\n    }\r\n\r\n    .global-drag-input-button {\r\n      width: 52px;\r\n      display: flex;\r\n      align-items: center;\r\n\r\n      .zy-el-icon {\r\n        color: var(--zy-el-color-primary);\r\n        font-size: 17px;\r\n        margin-left: 9px;\r\n        cursor: pointer;\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;EACOA,KAAK,EAAC,mBAAmB;EAACC,GAAG,EAAC;;;EAI1BD,KAAK,EAAC;AAA0B;;;;;;uBAJzCE,mBAAA,CAaM,OAbNC,UAaM,I,kBAZJD,mBAAA,CAWME,SAAA,QAbVC,WAAA,CAEuDC,MAAA,CAAAC,OAAO,EAF9D,UAE+CC,IAAI;yBAA/CN,mBAAA,CAWM;MAXDF,KAAK,EAAC,wBAAwB;MAA0BS,GAAG,EAAED,IAAI,CAACE;kCACrEC,mBAAA,CAA0C;MAArCX,KAAK,EAAC;IAAwB,6BACnCY,YAAA,CAAyGC,mBAAA;MAJ/GC,UAAA,EAIyBN,IAAI,CAACO,IAAI;MAJlC,gCAAAC,mBAAAC,MAAA;QAAA,OAIyBT,IAAI,CAACO,IAAI,GAAAE,MAAA;MAAA;MAAGC,QAAM,EAAEZ,MAAA,CAAAa,aAAa;MAAEC,WAAW,EAAC,KAAK;MAAEC,QAAQ,EAAEf,MAAA,CAAAe,QAAQ;MAAEC,SAAS,EAAT;gFAC7FX,mBAAA,CAOM,OAPNY,UAOM,G,CANgCjB,MAAA,CAAAe,QAAQ,I,cAA5CG,YAAA,CAEUC,kBAAA;MARlBhB,GAAA;MAMkBiB,OAAK,EAAEpB,MAAA,CAAAqB;;MANzBC,OAAA,EAAAC,QAAA,CAOU;QAAA,OAAc,CAAdjB,YAAA,CAAckB,qBAAA,E;;MAPxBC,CAAA;UAAAC,mBAAA,gBASiD1B,MAAA,CAAAC,OAAO,CAAC0B,MAAM,SAAS3B,MAAA,CAAAe,QAAQ,I,cAAxEG,YAAA,CAEUC,kBAAA;MAXlBhB,GAAA;MASkBiB,OAAK,WAALA,OAAKA,CAAAT,MAAA;QAAA,OAAEX,MAAA,CAAA4B,UAAU,CAAC1B,IAAI;MAAA;;MATxCoB,OAAA,EAAAC,QAAA,CAUU;QAAA,OAAU,CAAVjB,YAAA,CAAUuB,iBAAA,E;;MAVpBJ,CAAA;wDAAAC,mBAAA,e", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
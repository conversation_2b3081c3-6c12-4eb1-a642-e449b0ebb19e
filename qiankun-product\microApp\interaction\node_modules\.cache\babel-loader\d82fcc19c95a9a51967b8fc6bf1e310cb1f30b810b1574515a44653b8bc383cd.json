{"ast": null, "code": "import { computed } from 'vue';\nvar __default__ = {\n  name: 'AnchorLocationNavigation'\n};\nexport default /*@__PURE__*/Object.assign(__default__, {\n  props: {\n    modelValue: [String, Number],\n    data: {\n      type: Array,\n      default: function _default() {\n        return [];\n      }\n    }\n  },\n  emits: ['update:modelValue', 'change'],\n  setup(__props, _ref) {\n    var __expose = _ref.expose,\n      __emit = _ref.emit;\n    __expose();\n    var props = __props;\n    var emit = __emit;\n    var value = computed({\n      get() {\n        return props.modelValue;\n      },\n      set(value) {\n        emit('update:modelValue', value);\n      }\n    });\n    var data = computed(function () {\n      return props.data;\n    });\n    var change = function change(item) {\n      emit('change', item);\n      value.value = item.value;\n    };\n    var __returned__ = {\n      props,\n      emit,\n      value,\n      data,\n      change,\n      computed\n    };\n    Object.defineProperty(__returned__, '__isScriptSetup', {\n      enumerable: false,\n      value: true\n    });\n    return __returned__;\n  }\n});", "map": {"version": 3, "names": ["computed", "__default__", "name", "props", "__props", "emit", "__emit", "value", "get", "modelValue", "set", "data", "change", "item"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/common/components/anchor-location/anchor-location-navigation.vue"], "sourcesContent": ["<template>\r\n  <div class=\"anchor-location-navigation\">\r\n    <div class=\"anchor-location-navigation-list\">\r\n      <div\r\n        class=\"anchor-location-navigation-item\"\r\n        v-for=\"item in data\"\r\n        :key=\"item.value\"\r\n        :title=\"item.label\"\r\n        :class=\"{ 'is-active': value === item.value }\"\r\n        @click=\"change(item)\">\r\n        {{ item.label }}\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nexport default { name: 'AnchorLocationNavigation' }\r\n</script>\r\n\r\n<script setup>\r\nimport { computed } from 'vue'\r\nconst props = defineProps({ modelValue: [String, Number], data: { type: Array, default: () => [] } })\r\nconst emit = defineEmits(['update:modelValue', 'change'])\r\nconst value = computed({\r\n  get() {\r\n    return props.modelValue\r\n  },\r\n  set(value) {\r\n    emit('update:modelValue', value)\r\n  }\r\n})\r\nconst data = computed(() => props.data)\r\nconst change = (item) => {\r\n  emit('change', item)\r\n  value.value = item.value\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n.anchor-location-navigation {\r\n  width: 960px;\r\n  position: absolute;\r\n  top: 50%;\r\n  left: 50%;\r\n  transform: translate(-50%, -50%);\r\n\r\n  .anchor-location-navigation-list {\r\n    position: absolute;\r\n    top: 50%;\r\n    left: -10px;\r\n    transform: translate(-100%, -50%);\r\n\r\n    .anchor-location-navigation-item {\r\n      font-size: var(--zy-name-font-size);\r\n      line-height: var(--zy-line-height);\r\n      padding-right: 20px;\r\n      margin-bottom: 40px;\r\n      position: relative;\r\n      cursor: pointer;\r\n\r\n      &::after {\r\n        content: '';\r\n        position: absolute;\r\n        top: 50%;\r\n        right: 0;\r\n        width: 6px;\r\n        height: 6px;\r\n        border-radius: 50%;\r\n        border: 2px solid var(--zy-el-border-color-lighter);\r\n        transform: translateY(-50%);\r\n      }\r\n\r\n      &::before {\r\n        content: '';\r\n        position: absolute;\r\n        top: calc(50% + 5px);\r\n        right: 4px;\r\n        width: 2px;\r\n        height: calc((var(--zy-name-font-size) * var(--zy-line-height)) + 30px);\r\n        background-color: var(--zy-el-border-color-lighter);\r\n      }\r\n\r\n      &:last-child {\r\n        &::before {\r\n          background-color: transparent;\r\n        }\r\n      }\r\n    }\r\n\r\n    .is-active {\r\n      font-weight: bold;\r\n\r\n      &::after {\r\n        border: 2px solid var(--zy-el-color-primary);\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n@media screen and (max-width: 1580px) {\r\n  .anchor-location-navigation {\r\n    .anchor-location-navigation-list {\r\n      .anchor-location-navigation-item {\r\n        color: transparent;\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n"], "mappings": "AAqBA,SAASA,QAAQ,QAAQ,KAAK;AAJ9B,IAAAC,WAAA,GAAe;EAAEC,IAAI,EAAE;AAA2B,CAAC;;;;;;;;;;;;;;;;IAKnD,IAAMC,KAAK,GAAGC,OAAuF;IACrG,IAAMC,IAAI,GAAGC,MAA4C;IACzD,IAAMC,KAAK,GAAGP,QAAQ,CAAC;MACrBQ,GAAGA,CAAA,EAAG;QACJ,OAAOL,KAAK,CAACM,UAAU;MACzB,CAAC;MACDC,GAAGA,CAACH,KAAK,EAAE;QACTF,IAAI,CAAC,mBAAmB,EAAEE,KAAK,CAAC;MAClC;IACF,CAAC,CAAC;IACF,IAAMI,IAAI,GAAGX,QAAQ,CAAC;MAAA,OAAMG,KAAK,CAACQ,IAAI;IAAA,EAAC;IACvC,IAAMC,MAAM,GAAG,SAATA,MAAMA,CAAIC,IAAI,EAAK;MACvBR,IAAI,CAAC,QAAQ,EAAEQ,IAAI,CAAC;MACpBN,KAAK,CAACA,KAAK,GAAGM,IAAI,CAACN,KAAK;IAC1B,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "import { computed, watch } from 'vue';\nimport '../../../../utils/index.mjs';\nimport { CHECKED_CHANGE_EVENT } from '../transfer-panel.mjs';\nimport { usePropsAlias } from './use-props-alias.mjs';\nimport { isFunction } from '@vue/shared';\nvar useCheck = function useCheck(props, panelState, emit) {\n  var propsAlias = usePropsAlias(props);\n  var filteredData = computed(function () {\n    return props.data.filter(function (item) {\n      if (isFunction(props.filterMethod)) {\n        return props.filterMethod(panelState.query, item);\n      } else {\n        var label = String(item[propsAlias.value.label] || item[propsAlias.value.key]);\n        return label.toLowerCase().includes(panelState.query.toLowerCase());\n      }\n    });\n  });\n  var checkableData = computed(function () {\n    return filteredData.value.filter(function (item) {\n      return !item[propsAlias.value.disabled];\n    });\n  });\n  var checkedSummary = computed(function () {\n    var checkedLength = panelState.checked.length;\n    var dataLength = props.data.length;\n    var _props$format = props.format,\n      noChecked = _props$format.noChecked,\n      hasChecked = _props$format.hasChecked;\n    if (noChecked && hasChecked) {\n      return checkedLength > 0 ? hasChecked.replace(/\\${checked}/g, checkedLength.toString()).replace(/\\${total}/g, dataLength.toString()) : noChecked.replace(/\\${total}/g, dataLength.toString());\n    } else {\n      return `${checkedLength}/${dataLength}`;\n    }\n  });\n  var isIndeterminate = computed(function () {\n    var checkedLength = panelState.checked.length;\n    return checkedLength > 0 && checkedLength < checkableData.value.length;\n  });\n  var updateAllChecked = function updateAllChecked() {\n    var checkableDataKeys = checkableData.value.map(function (item) {\n      return item[propsAlias.value.key];\n    });\n    panelState.allChecked = checkableDataKeys.length > 0 && checkableDataKeys.every(function (item) {\n      return panelState.checked.includes(item);\n    });\n  };\n  var handleAllCheckedChange = function handleAllCheckedChange(value) {\n    panelState.checked = value ? checkableData.value.map(function (item) {\n      return item[propsAlias.value.key];\n    }) : [];\n  };\n  watch(function () {\n    return panelState.checked;\n  }, function (val, oldVal) {\n    updateAllChecked();\n    if (panelState.checkChangeByUser) {\n      var movedKeys = val.concat(oldVal).filter(function (v) {\n        return !val.includes(v) || !oldVal.includes(v);\n      });\n      emit(CHECKED_CHANGE_EVENT, val, movedKeys);\n    } else {\n      emit(CHECKED_CHANGE_EVENT, val);\n      panelState.checkChangeByUser = true;\n    }\n  });\n  watch(checkableData, function () {\n    updateAllChecked();\n  });\n  watch(function () {\n    return props.data;\n  }, function () {\n    var checked = [];\n    var filteredDataKeys = filteredData.value.map(function (item) {\n      return item[propsAlias.value.key];\n    });\n    panelState.checked.forEach(function (item) {\n      if (filteredDataKeys.includes(item)) {\n        checked.push(item);\n      }\n    });\n    panelState.checkChangeByUser = false;\n    panelState.checked = checked;\n  });\n  watch(function () {\n    return props.defaultChecked;\n  }, function (val, oldVal) {\n    if (oldVal && val.length === oldVal.length && val.every(function (item) {\n      return oldVal.includes(item);\n    })) return;\n    var checked = [];\n    var checkableDataKeys = checkableData.value.map(function (item) {\n      return item[propsAlias.value.key];\n    });\n    val.forEach(function (item) {\n      if (checkableDataKeys.includes(item)) {\n        checked.push(item);\n      }\n    });\n    panelState.checkChangeByUser = false;\n    panelState.checked = checked;\n  }, {\n    immediate: true\n  });\n  return {\n    filteredData,\n    checkableData,\n    checkedSummary,\n    isIndeterminate,\n    updateAllChecked,\n    handleAllCheckedChange\n  };\n};\nexport { useCheck };", "map": {"version": 3, "names": ["useCheck", "props", "panelState", "emit", "props<PERSON><PERSON><PERSON>", "usePropsAlias", "filteredData", "computed", "data", "filter", "item", "isFunction", "filterMethod", "query", "label", "String", "value", "key", "toLowerCase", "includes", "checkableData", "disabled", "checkedSummary", "<PERSON><PERSON><PERSON><PERSON>", "checked", "length", "dataLength", "_props$format", "format", "noChecked", "hasChecked", "replace", "toString", "isIndeterminate", "updateAllChecked", "checkableDataKeys", "map", "allChecked", "every", "handleAllCheckedChange", "watch", "val", "oldVal", "checkChangeByUser", "<PERSON><PERSON><PERSON><PERSON>", "concat", "v", "CHECKED_CHANGE_EVENT", "filteredDataKeys", "for<PERSON>ach", "push", "defaultChecked", "immediate"], "sources": ["../../../../../../../packages/components/transfer/src/composables/use-check.ts"], "sourcesContent": ["import { computed, watch } from 'vue'\nimport { isFunction } from '@element-plus/utils'\nimport { CHECKED_CHANGE_EVENT } from '../transfer-panel'\nimport { usePropsAlias } from './use-props-alias'\n\nimport type { SetupContext } from 'vue'\nimport type { CheckboxValueType } from '@element-plus/components/checkbox'\nimport type { TransferKey } from '../transfer'\nimport type {\n  TransferPanelEmits,\n  TransferPanelProps,\n  TransferPanelState,\n} from '../transfer-panel'\n\nexport const useCheck = (\n  props: TransferPanelProps,\n  panelState: TransferPanelState,\n  emit: SetupContext<TransferPanelEmits>['emit']\n) => {\n  const propsAlias = usePropsAlias(props)\n\n  const filteredData = computed(() => {\n    return props.data.filter((item) => {\n      if (isFunction(props.filterMethod)) {\n        return props.filterMethod(panelState.query, item)\n      } else {\n        const label = String(\n          item[propsAlias.value.label] || item[propsAlias.value.key]\n        )\n        return label.toLowerCase().includes(panelState.query.toLowerCase())\n      }\n    })\n  })\n\n  const checkableData = computed(() =>\n    filteredData.value.filter((item) => !item[propsAlias.value.disabled])\n  )\n\n  const checkedSummary = computed(() => {\n    const checkedLength = panelState.checked.length\n    const dataLength = props.data.length\n    const { noChecked, hasChecked } = props.format\n\n    if (noChecked && hasChecked) {\n      return checkedLength > 0\n        ? hasChecked\n            .replace(/\\${checked}/g, checkedLength.toString())\n            .replace(/\\${total}/g, dataLength.toString())\n        : noChecked.replace(/\\${total}/g, dataLength.toString())\n    } else {\n      return `${checkedLength}/${dataLength}`\n    }\n  })\n\n  const isIndeterminate = computed(() => {\n    const checkedLength = panelState.checked.length\n    return checkedLength > 0 && checkedLength < checkableData.value.length\n  })\n\n  const updateAllChecked = () => {\n    const checkableDataKeys = checkableData.value.map(\n      (item) => item[propsAlias.value.key]\n    )\n    panelState.allChecked =\n      checkableDataKeys.length > 0 &&\n      checkableDataKeys.every((item) => panelState.checked.includes(item))\n  }\n\n  const handleAllCheckedChange = (value: CheckboxValueType) => {\n    panelState.checked = value\n      ? checkableData.value.map((item) => item[propsAlias.value.key])\n      : []\n  }\n\n  watch(\n    () => panelState.checked,\n    (val, oldVal) => {\n      updateAllChecked()\n\n      if (panelState.checkChangeByUser) {\n        const movedKeys = val\n          .concat(oldVal)\n          .filter((v) => !val.includes(v) || !oldVal.includes(v))\n        emit(CHECKED_CHANGE_EVENT, val, movedKeys)\n      } else {\n        emit(CHECKED_CHANGE_EVENT, val)\n        panelState.checkChangeByUser = true\n      }\n    }\n  )\n\n  watch(checkableData, () => {\n    updateAllChecked()\n  })\n\n  watch(\n    () => props.data,\n    () => {\n      const checked: TransferKey[] = []\n      const filteredDataKeys = filteredData.value.map(\n        (item) => item[propsAlias.value.key]\n      )\n      panelState.checked.forEach((item) => {\n        if (filteredDataKeys.includes(item)) {\n          checked.push(item)\n        }\n      })\n      panelState.checkChangeByUser = false\n      panelState.checked = checked\n    }\n  )\n\n  watch(\n    () => props.defaultChecked,\n    (val, oldVal) => {\n      if (\n        oldVal &&\n        val.length === oldVal.length &&\n        val.every((item) => oldVal.includes(item))\n      )\n        return\n\n      const checked: TransferKey[] = []\n      const checkableDataKeys = checkableData.value.map(\n        (item) => item[propsAlias.value.key]\n      )\n\n      val.forEach((item) => {\n        if (checkableDataKeys.includes(item)) {\n          checked.push(item)\n        }\n      })\n      panelState.checkChangeByUser = false\n      panelState.checked = checked\n    },\n    {\n      immediate: true,\n    }\n  )\n\n  return {\n    filteredData,\n    checkableData,\n    checkedSummary,\n    isIndeterminate,\n    updateAllChecked,\n    handleAllCheckedChange,\n  }\n}\n"], "mappings": ";;;;;AAIY,IAACA,QAAQ,GAAG,SAAXA,QAAQA,CAAIC,KAAK,EAAEC,UAAU,EAAEC,IAAI,EAAK;EACnD,IAAMC,UAAU,GAAGC,aAAa,CAACJ,KAAK,CAAC;EACvC,IAAMK,YAAY,GAAGC,QAAQ,CAAC,YAAM;IAClC,OAAON,KAAK,CAACO,IAAI,CAACC,MAAM,CAAC,UAACC,IAAI,EAAK;MACjC,IAAIC,UAAU,CAACV,KAAK,CAACW,YAAY,CAAC,EAAE;QAClC,OAAOX,KAAK,CAACW,YAAY,CAACV,UAAU,CAACW,KAAK,EAAEH,IAAI,CAAC;MACzD,CAAO,MAAM;QACL,IAAMI,KAAK,GAAGC,MAAM,CAACL,IAAI,CAACN,UAAU,CAACY,KAAK,CAACF,KAAK,CAAC,IAAIJ,IAAI,CAACN,UAAU,CAACY,KAAK,CAACC,GAAG,CAAC,CAAC;QAChF,OAAOH,KAAK,CAACI,WAAW,EAAE,CAACC,QAAQ,CAACjB,UAAU,CAACW,KAAK,CAACK,WAAW,EAAE,CAAC;MAC3E;IACA,CAAK,CAAC;EACN,CAAG,CAAC;EACF,IAAME,aAAa,GAAGb,QAAQ,CAAC;IAAA,OAAMD,YAAY,CAACU,KAAK,CAACP,MAAM,CAAC,UAACC,IAAI;MAAA,OAAK,CAACA,IAAI,CAACN,UAAU,CAACY,KAAK,CAACK,QAAQ,CAAC;IAAA,EAAC;EAAA,EAAC;EAC3G,IAAMC,cAAc,GAAGf,QAAQ,CAAC,YAAM;IACpC,IAAMgB,aAAa,GAAGrB,UAAU,CAACsB,OAAO,CAACC,MAAM;IAC/C,IAAMC,UAAU,GAAGzB,KAAK,CAACO,IAAI,CAACiB,MAAM;IACpC,IAAAE,aAAA,GAAkC1B,KAAK,CAAC2B,MAAM;MAAtCC,SAAS,GAAAF,aAAA,CAATE,SAAS;MAAEC,UAAU,GAAAH,aAAA,CAAVG,UAAU;IAC7B,IAAID,SAAS,IAAIC,UAAU,EAAE;MAC3B,OAAOP,aAAa,GAAG,CAAC,GAAGO,UAAU,CAACC,OAAO,CAAC,cAAc,EAAER,aAAa,CAACS,QAAQ,EAAE,CAAC,CAACD,OAAO,CAAC,YAAY,EAAEL,UAAU,CAACM,QAAQ,EAAE,CAAC,GAAGH,SAAS,CAACE,OAAO,CAAC,YAAY,EAAEL,UAAU,CAACM,QAAQ,EAAE,CAAC;IACnM,CAAK,MAAM;MACL,OAAO,GAAGT,aAAa,IAAIG,UAAU,EAAE;IAC7C;EACA,CAAG,CAAC;EACF,IAAMO,eAAe,GAAG1B,QAAQ,CAAC,YAAM;IACrC,IAAMgB,aAAa,GAAGrB,UAAU,CAACsB,OAAO,CAACC,MAAM;IAC/C,OAAOF,aAAa,GAAG,CAAC,IAAIA,aAAa,GAAGH,aAAa,CAACJ,KAAK,CAACS,MAAM;EAC1E,CAAG,CAAC;EACF,IAAMS,gBAAgB,GAAG,SAAnBA,gBAAgBA,CAAA,EAAS;IAC7B,IAAMC,iBAAiB,GAAGf,aAAa,CAACJ,KAAK,CAACoB,GAAG,CAAC,UAAC1B,IAAI;MAAA,OAAKA,IAAI,CAACN,UAAU,CAACY,KAAK,CAACC,GAAG,CAAC;IAAA,EAAC;IACvFf,UAAU,CAACmC,UAAU,GAAGF,iBAAiB,CAACV,MAAM,GAAG,CAAC,IAAIU,iBAAiB,CAACG,KAAK,CAAC,UAAC5B,IAAI;MAAA,OAAKR,UAAU,CAACsB,OAAO,CAACL,QAAQ,CAACT,IAAI,CAAC;IAAA,EAAC;EAChI,CAAG;EACD,IAAM6B,sBAAsB,GAAG,SAAzBA,sBAAsBA,CAAIvB,KAAK,EAAK;IACxCd,UAAU,CAACsB,OAAO,GAAGR,KAAK,GAAGI,aAAa,CAACJ,KAAK,CAACoB,GAAG,CAAC,UAAC1B,IAAI;MAAA,OAAKA,IAAI,CAACN,UAAU,CAACY,KAAK,CAACC,GAAG,CAAC;IAAA,EAAC,GAAG,EAAE;EACnG,CAAG;EACDuB,KAAK,CAAC;IAAA,OAAMtC,UAAU,CAACsB,OAAO;EAAA,GAAE,UAACiB,GAAG,EAAEC,MAAM,EAAK;IAC/CR,gBAAgB,EAAE;IAClB,IAAIhC,UAAU,CAACyC,iBAAiB,EAAE;MAChC,IAAMC,SAAS,GAAGH,GAAG,CAACI,MAAM,CAACH,MAAM,CAAC,CAACjC,MAAM,CAAC,UAACqC,CAAC;QAAA,OAAK,CAACL,GAAG,CAACtB,QAAQ,CAAC2B,CAAC,CAAC,IAAI,CAACJ,MAAM,CAACvB,QAAQ,CAAC2B,CAAC,CAAC;MAAA,EAAC;MAC3F3C,IAAI,CAAC4C,oBAAoB,EAAEN,GAAG,EAAEG,SAAS,CAAC;IAChD,CAAK,MAAM;MACLzC,IAAI,CAAC4C,oBAAoB,EAAEN,GAAG,CAAC;MAC/BvC,UAAU,CAACyC,iBAAiB,GAAG,IAAI;IACzC;EACA,CAAG,CAAC;EACFH,KAAK,CAACpB,aAAa,EAAE,YAAM;IACzBc,gBAAgB,EAAE;EACtB,CAAG,CAAC;EACFM,KAAK,CAAC;IAAA,OAAMvC,KAAK,CAACO,IAAI;EAAA,GAAE,YAAM;IAC5B,IAAMgB,OAAO,GAAG,EAAE;IAClB,IAAMwB,gBAAgB,GAAG1C,YAAY,CAACU,KAAK,CAACoB,GAAG,CAAC,UAAC1B,IAAI;MAAA,OAAKA,IAAI,CAACN,UAAU,CAACY,KAAK,CAACC,GAAG,CAAC;IAAA,EAAC;IACrFf,UAAU,CAACsB,OAAO,CAACyB,OAAO,CAAC,UAACvC,IAAI,EAAK;MACnC,IAAIsC,gBAAgB,CAAC7B,QAAQ,CAACT,IAAI,CAAC,EAAE;QACnCc,OAAO,CAAC0B,IAAI,CAACxC,IAAI,CAAC;MAC1B;IACA,CAAK,CAAC;IACFR,UAAU,CAACyC,iBAAiB,GAAG,KAAK;IACpCzC,UAAU,CAACsB,OAAO,GAAGA,OAAO;EAChC,CAAG,CAAC;EACFgB,KAAK,CAAC;IAAA,OAAMvC,KAAK,CAACkD,cAAc;EAAA,GAAE,UAACV,GAAG,EAAEC,MAAM,EAAK;IACjD,IAAIA,MAAM,IAAID,GAAG,CAAChB,MAAM,KAAKiB,MAAM,CAACjB,MAAM,IAAIgB,GAAG,CAACH,KAAK,CAAC,UAAC5B,IAAI;MAAA,OAAKgC,MAAM,CAACvB,QAAQ,CAACT,IAAI,CAAC;IAAA,EAAC,EACtF;IACF,IAAMc,OAAO,GAAG,EAAE;IAClB,IAAMW,iBAAiB,GAAGf,aAAa,CAACJ,KAAK,CAACoB,GAAG,CAAC,UAAC1B,IAAI;MAAA,OAAKA,IAAI,CAACN,UAAU,CAACY,KAAK,CAACC,GAAG,CAAC;IAAA,EAAC;IACvFwB,GAAG,CAACQ,OAAO,CAAC,UAACvC,IAAI,EAAK;MACpB,IAAIyB,iBAAiB,CAAChB,QAAQ,CAACT,IAAI,CAAC,EAAE;QACpCc,OAAO,CAAC0B,IAAI,CAACxC,IAAI,CAAC;MAC1B;IACA,CAAK,CAAC;IACFR,UAAU,CAACyC,iBAAiB,GAAG,KAAK;IACpCzC,UAAU,CAACsB,OAAO,GAAGA,OAAO;EAChC,CAAG,EAAE;IACD4B,SAAS,EAAE;EACf,CAAG,CAAC;EACF,OAAO;IACL9C,YAAY;IACZc,aAAa;IACbE,cAAc;IACdW,eAAe;IACfC,gBAAgB;IAChBK;EACJ,CAAG;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "import '../../base/style/index.mjs';\nimport 'element-plus/theme-chalk/src/dialog.scss';\nimport '../../overlay/style/index.mjs';", "map": {"version": 3, "names": [], "sources": [], "sourcesContent": ["import '../../base/style/index.mjs';\nimport 'element-plus/theme-chalk/src/dialog.scss';\nimport '../../overlay/style/index.mjs';\n//# sourceMappingURL=index.mjs.map\n"], "mappings": "", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
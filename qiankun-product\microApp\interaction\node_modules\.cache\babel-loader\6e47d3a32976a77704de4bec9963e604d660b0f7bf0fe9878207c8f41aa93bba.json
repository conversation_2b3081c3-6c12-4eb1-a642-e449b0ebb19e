{"ast": null, "code": "import '../../../utils/index.mjs';\nimport '../../time-picker/index.mjs';\nimport { rangeArr } from '../../time-picker/src/utils.mjs';\nimport { buildProps, definePropType } from '../../../utils/vue/props/runtime.mjs';\nimport { isObject } from '@vue/shared';\nvar getPrevMonthLastDays = function getPrevMonthLastDays(date, count) {\n  var lastDay = date.subtract(1, \"month\").endOf(\"month\").date();\n  return rangeArr(count).map(function (_, index) {\n    return lastDay - (count - index - 1);\n  });\n};\nvar getMonthDays = function getMonthDays(date) {\n  var days = date.daysInMonth();\n  return rangeArr(days).map(function (_, index) {\n    return index + 1;\n  });\n};\nvar toNestedArr = function toNestedArr(days) {\n  return rangeArr(days.length / 7).map(function (index) {\n    var start = index * 7;\n    return days.slice(start, start + 7);\n  });\n};\nvar dateTableProps = buildProps({\n  selectedDay: {\n    type: definePropType(Object)\n  },\n  range: {\n    type: definePropType(Array)\n  },\n  date: {\n    type: definePropType(Object),\n    required: true\n  },\n  hideHeader: {\n    type: Boolean\n  }\n});\nvar dateTableEmits = {\n  pick: function pick(value) {\n    return isObject(value);\n  }\n};\nexport { dateTableEmits, dateTableProps, getMonthDays, getPrevMonthLastDays, toNestedArr };", "map": {"version": 3, "names": ["getPrevMonthLastDays", "date", "count", "lastDay", "subtract", "endOf", "rangeArr", "map", "_", "index", "getMonthDays", "days", "daysInMonth", "toNestedArr", "length", "start", "slice", "dateTableProps", "buildProps", "selected<PERSON>ay", "type", "definePropType", "Object", "range", "Array", "required", "<PERSON><PERSON>ead<PERSON>", "Boolean", "dateTableEmits", "pick", "value", "isObject"], "sources": ["../../../../../../packages/components/calendar/src/date-table.ts"], "sourcesContent": ["import { buildProps, definePropType, isObject } from '@element-plus/utils'\nimport { rangeArr } from '@element-plus/components/time-picker'\nimport type { ExtractPropTypes } from 'vue'\nimport type { Dayjs } from 'dayjs'\n\nexport type CalendarDateCellType = 'next' | 'prev' | 'current'\nexport type CalendarDateCell = {\n  text: number\n  type: CalendarDateCellType\n}\n\nexport const getPrevMonthLastDays = (date: Dayjs, count: number) => {\n  const lastDay = date.subtract(1, 'month').endOf('month').date()\n  return rangeArr(count).map((_, index) => lastDay - (count - index - 1))\n}\n\nexport const getMonthDays = (date: Dayjs) => {\n  const days = date.daysInMonth()\n  return rangeArr(days).map((_, index) => index + 1)\n}\n\nexport const toNestedArr = (days: CalendarDateCell[]) =>\n  rangeArr(days.length / 7).map((index) => {\n    const start = index * 7\n    return days.slice(start, start + 7)\n  })\n\nexport const dateTableProps = buildProps({\n  selectedDay: {\n    type: definePropType<Dayjs>(Object),\n  },\n  range: {\n    type: definePropType<[Dayjs, Dayjs]>(Array),\n  },\n  date: {\n    type: definePropType<Dayjs>(Object),\n    required: true,\n  },\n  hideHeader: {\n    type: Boolean,\n  },\n} as const)\nexport type DateTableProps = ExtractPropTypes<typeof dateTableProps>\n\nexport const dateTableEmits = {\n  pick: (value: Dayjs) => isObject(value),\n}\nexport type DateTableEmits = typeof dateTableEmits\n"], "mappings": ";;;;;AAEY,IAACA,oBAAoB,GAAG,SAAvBA,oBAAoBA,CAAIC,IAAI,EAAEC,KAAK,EAAK;EACnD,IAAMC,OAAO,GAAGF,IAAI,CAACG,QAAQ,CAAC,CAAC,EAAE,OAAO,CAAC,CAACC,KAAK,CAAC,OAAO,CAAC,CAACJ,IAAI,EAAE;EAC/D,OAAOK,QAAQ,CAACJ,KAAK,CAAC,CAACK,GAAG,CAAC,UAACC,CAAC,EAAEC,KAAK;IAAA,OAAKN,OAAO,IAAID,KAAK,GAAGO,KAAK,GAAG,CAAC,CAAC;EAAA,EAAC;AACzE;AACY,IAACC,YAAY,GAAG,SAAfA,YAAYA,CAAIT,IAAI,EAAK;EACpC,IAAMU,IAAI,GAAGV,IAAI,CAACW,WAAW,EAAE;EAC/B,OAAON,QAAQ,CAACK,IAAI,CAAC,CAACJ,GAAG,CAAC,UAACC,CAAC,EAAEC,KAAK;IAAA,OAAKA,KAAK,GAAG,CAAC;EAAA,EAAC;AACpD;AACY,IAACI,WAAW,GAAG,SAAdA,WAAWA,CAAIF,IAAI;EAAA,OAAKL,QAAQ,CAACK,IAAI,CAACG,MAAM,GAAG,CAAC,CAAC,CAACP,GAAG,CAAC,UAACE,KAAK,EAAK;IAC5E,IAAMM,KAAK,GAAGN,KAAK,GAAG,CAAC;IACvB,OAAOE,IAAI,CAACK,KAAK,CAACD,KAAK,EAAEA,KAAK,GAAG,CAAC,CAAC;EACrC,CAAC;AAAA;AACW,IAACE,cAAc,GAAGC,UAAU,CAAC;EACvCC,WAAW,EAAE;IACXC,IAAI,EAAEC,cAAc,CAACC,MAAM;EAC/B,CAAG;EACDC,KAAK,EAAE;IACLH,IAAI,EAAEC,cAAc,CAACG,KAAK;EAC9B,CAAG;EACDvB,IAAI,EAAE;IACJmB,IAAI,EAAEC,cAAc,CAACC,MAAM,CAAC;IAC5BG,QAAQ,EAAE;EACd,CAAG;EACDC,UAAU,EAAE;IACVN,IAAI,EAAEO;EACV;AACA,CAAC;AACW,IAACC,cAAc,GAAG;EAC5BC,IAAI,EAAE,SAANA,IAAIA,CAAGC,KAAK;IAAA,OAAKC,QAAQ,CAACD,KAAK,CAAC;EAAA;AAClC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
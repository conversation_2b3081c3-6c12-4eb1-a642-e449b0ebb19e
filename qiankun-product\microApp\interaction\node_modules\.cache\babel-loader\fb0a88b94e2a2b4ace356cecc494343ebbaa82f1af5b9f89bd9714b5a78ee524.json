{"ast": null, "code": "var layoutOpt = function layoutOpt(editor) {\n  return editor.getParam('tp_layout_options', {\n    selection: 'div,p,table,tr,td,h1,h2,h3,h4,h5,h6,ul,blockquote',\n    clearStyle: [],\n    filterTags: ['table>*', 'img'],\n    style: {\n      'text-align': 'justify',\n      'text-indent': '2em',\n      'line-height': 1.5\n    },\n    tagsStyle: {}\n  });\n};\nvar layout_filterTags = {};\nvar layout_filterTagsRegex = {};\nvar doAct = function doAct(editor) {\n  var dom = editor.dom;\n  var blocks = [];\n  var global$1 = tinymce.util.Tools.resolve('tinymce.util.Tools');\n  editor.execCommand('selectAll');\n  var layout_opt = layoutOpt(editor);\n  for (var key in layout_opt.filterTags) {\n    layout_opt.filterTags[key].indexOf('>*') != -1 ? layout_filterTagsRegex[layout_opt.filterTags[key].replace('>*', '').toUpperCase()] = true : layout_filterTags[layout_opt.filterTags[key].toUpperCase()] = true;\n  }\n  layout_opt.selection = layout_opt.selection || 'div,p,table,tr,td,h1,h2,h3,h4,h5,h6,ul,blockquote';\n  for (var key in layout_opt.tagsStyle) {\n    var ckeyList = key.split(',');\n    layout_opt.selection += ',' + key;\n    for (var ckey in ckeyList) ckeyList[ckey].indexOf('>*') != -1 ? layout_filterTagsRegex[ckeyList[ckey].replace('>*', '').toUpperCase()] = key : layout_filterTags[ckeyList[ckey].toUpperCase()] = key;\n  }\n  blocks = editor.selection.getNode().querySelectorAll(layout_opt.selection);\n  var _indent2$getValue = function _indent2$getValue(key2, str) {\n    var m = str.match(new RegExp(key2 + ':?(.+?)\"?[;}]'));\n    return m ? m[1] : false;\n  };\n  var filterFun = function filterFun(el, _r) {\n    var parentSelector = 'BODY';\n    var parents = el.tagName;\n    if (layout_filterTags[parents] || layout_filterTagsRegex[parents]) {\n      !layout_opt.tagsStyle[layout_filterTags[parents]] || _r ? _r ? removeStyleFun(el, layout_opt.tagsStyle[layout_filterTags[parents]]) : '' : setStyleFun(el, layout_opt.tagsStyle[layout_filterTags[parents]]);\n      return true;\n    }\n    var _p = el.parentNode;\n    var _pName = _p.tagName;\n    while (_pName !== parentSelector) {\n      var o = _p;\n      parents = _pName + '>' + parents;\n      if (layout_filterTags[parents] || layout_filterTagsRegex[_pName]) {\n        !layout_opt.tagsStyle[layout_filterTagsRegex[_pName]] || _r ? _r ? removeStyleFun(el, layout_opt.tagsStyle[layout_filterTagsRegex[_pName]]) : '' : setStyleFun(el, layout_opt.tagsStyle[layout_filterTagsRegex[_pName]]);\n        !layout_opt.tagsStyle[layout_filterTags[parents]] || _r ? _r ? removeStyleFun(el, layout_opt.tagsStyle[layout_filterTags[parents]]) : '' : setStyleFun(el, layout_opt.tagsStyle[layout_filterTags[parents]]);\n        return true;\n      }\n      _p = o.parentNode;\n      _pName = _p.tagName;\n    }\n    return false;\n  };\n  var clearStyleFun = function clearStyleFun(_block) {\n    var style = dom.getAttrib(_block, 'style');\n    for (var key2 in layout_opt.clearStyle) {\n      var reg = new RegExp(layout_opt.clearStyle[key2] + ':?(.+?)\"?[;}]');\n      style = style.replace(reg, '');\n    }\n    dom.setAttrib(_block, 'style', style);\n  };\n  var removeStyleFun = function removeStyleFun(_block, _style) {\n    var style = dom.getAttrib(_block, 'style');\n    for (var key2 in _style) {\n      var reg = new RegExp(key2 + ':?(.+?)\"?[;}]');\n      style = style.replace(reg, '');\n    }\n    dom.setAttrib(_block, 'style', style);\n  };\n  var setStyleFun = function setStyleFun(_block, _style) {\n    for (var key2 in _style) {\n      dom.setStyle(_block, key2, _style[key2]);\n    }\n    if (_style['text-indent']) {\n      var kv = '',\n        kl = '';\n      if (_block && _block.children['0'] && _block.children['0'].attributes && _block.children['0'].attributes.style) {\n        kv = _indent2$getValue('font-size', _block.children['0'].attributes.style.textContent);\n        kl = _indent2$getValue('letter-spacing', _block.children['0'].attributes.style.textContent);\n        if (kv) {\n          kv = (parseInt(kv) + parseInt(kl ? kl : 0)) * 2 + 'pt';\n        } else kv = (parseInt(kl ? kl : 0) + 16) * 2 + 'pt';\n      }\n      dom.setStyle(_block, 'text-indent', layout_opt.style['text-indent'] && layout_opt.style['text-indent'] != '2em' ? layout_opt.style['text-indent'] : kv ? kv : '2em');\n    }\n  };\n  // var layoutAct = ''\n  // if (blocks[0]) {\n  //   blocks[0].dataset.layoutFv = blocks[0].dataset.layoutFv ? '' : blocks[0].dataset.layoutFv = 'layoutFV'\n  // }\n  global$1.each(blocks, function (block) {\n    //   if (layoutAct == '') {\n    //     if (dom.hasClass(block, 'layoutFV')) {\n    //       layoutAct = 'remove'\n    //       dom.removeClass(block, 'layoutFV')\n    //     } else {\n    //       layoutAct = 'add'\n    //       dom.addClass(block, 'layoutFV')\n    //     }\n    //   }\n    //   if (layoutAct == 'add') {\n    //     !filterFun(block) ? setStyleFun(block, layout_opt.style) : ''\n    //     layout_opt.clearStyle ? clearStyleFun(block) : ''\n    //   } else {\n    //     !filterFun(block, 'remove') ? removeStyleFun(block, layout_opt.style) : ''\n    //   }\n    !filterFun(block) ? setStyleFun(block, layout_opt.style) : '';\n    layout_opt.clearStyle ? clearStyleFun(block) : '';\n  });\n};\nvar create = function create(editor) {\n  editor.undoManager.transact(function () {\n    editor.focus();\n    doAct(editor);\n  });\n};\nvar setup = function setup(editor) {\n  editor.ui.registry.addToggleButton('tpLayout', {\n    icon: 'tpLayout',\n    tooltip: '一键排版',\n    onAction: function onAction() {\n      return create(editor);\n    }\n  });\n  editor.ui.registry.addMenuItem('tpLayout', {\n    icon: 'tpLayout',\n    text: '一键排版',\n    onAction: function onAction() {\n      return create(editor);\n    }\n  });\n};\nvar register = function register(editor) {\n  return editor.addCommand('mceTpLayout', function () {\n    create(editor);\n  });\n};\nexport var initTpLayout = function initTpLayout(editor) {\n  setup(editor);\n  register(editor);\n};", "map": {"version": 3, "names": ["layoutOpt", "editor", "getPara<PERSON>", "selection", "clearStyle", "filterTags", "style", "tagsStyle", "layout_filterTags", "layout_filterTagsRegex", "doAct", "dom", "blocks", "global$1", "<PERSON><PERSON><PERSON>", "util", "Tools", "resolve", "execCommand", "layout_opt", "key", "indexOf", "replace", "toUpperCase", "ckeyList", "split", "ckey", "getNode", "querySelectorAll", "_indent2$getValue", "key2", "str", "m", "match", "RegExp", "filterFun", "el", "_r", "parentSelector", "parents", "tagName", "removeStyleFun", "setStyleFun", "_p", "parentNode", "_pName", "o", "clearStyleFun", "_block", "getAttrib", "reg", "setAttrib", "_style", "setStyle", "kv", "kl", "children", "attributes", "textContent", "parseInt", "each", "block", "create", "undoManager", "transact", "focus", "setup", "ui", "registry", "addToggleButton", "icon", "tooltip", "onAction", "addMenuItem", "text", "register", "addCommand", "initTpLayout"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/common/components/TinyMceEditor/tpLayout.js"], "sourcesContent": ["\r\nconst layoutOpt = (editor) => editor.getParam('tp_layout_options', { selection: 'div,p,table,tr,td,h1,h2,h3,h4,h5,h6,ul,blockquote', clearStyle: [], filterTags: ['table>*', 'img'], style: { 'text-align': 'justify', 'text-indent': '2em', 'line-height': 1.5 }, tagsStyle: {} })\r\nvar layout_filterTags = {}\r\nvar layout_filterTagsRegex = {}\r\nconst doAct = (editor) => {\r\n  var dom = editor.dom\r\n  var blocks = []\r\n  var global$1 = tinymce.util.Tools.resolve('tinymce.util.Tools')\r\n  editor.execCommand('selectAll')\r\n  var layout_opt = layoutOpt(editor)\r\n  for (var key in layout_opt.filterTags) {\r\n    layout_opt.filterTags[key].indexOf('>*') != -1 ? layout_filterTagsRegex[layout_opt.filterTags[key].replace('>*', '').toUpperCase()] = true : layout_filterTags[layout_opt.filterTags[key].toUpperCase()] = true\r\n  }\r\n  layout_opt.selection = layout_opt.selection || 'div,p,table,tr,td,h1,h2,h3,h4,h5,h6,ul,blockquote'\r\n  for (var key in layout_opt.tagsStyle) {\r\n    var ckeyList = key.split(',')\r\n    layout_opt.selection += ',' + key\r\n    for (var ckey in ckeyList)\r\n      ckeyList[ckey].indexOf('>*') != -1 ? layout_filterTagsRegex[ckeyList[ckey].replace('>*', '').toUpperCase()] = key : layout_filterTags[ckeyList[ckey].toUpperCase()] = key\r\n  }\r\n  blocks = editor.selection.getNode().querySelectorAll(layout_opt.selection)\r\n  const _indent2$getValue = (key2, str) => {\r\n    var m = str.match(new RegExp(key2 + ':?(.+?)\"?[;}]'))\r\n    return m ? m[1] : false\r\n  }\r\n  const filterFun = (el, _r) => {\r\n    var parentSelector = 'BODY'\r\n    var parents = el.tagName\r\n    if (layout_filterTags[parents] || layout_filterTagsRegex[parents]) {\r\n      !layout_opt.tagsStyle[layout_filterTags[parents]] || _r ? _r ? removeStyleFun(el, layout_opt.tagsStyle[layout_filterTags[parents]]) : '' : setStyleFun(el, layout_opt.tagsStyle[layout_filterTags[parents]])\r\n      return true\r\n    }\r\n    var _p = el.parentNode\r\n    var _pName = _p.tagName\r\n    while (_pName !== parentSelector) {\r\n      var o = _p\r\n      parents = _pName + '>' + parents\r\n      if (layout_filterTags[parents] || layout_filterTagsRegex[_pName]) {\r\n        !layout_opt.tagsStyle[layout_filterTagsRegex[_pName]] || _r ? _r ? removeStyleFun(el, layout_opt.tagsStyle[layout_filterTagsRegex[_pName]]) : '' : setStyleFun(el, layout_opt.tagsStyle[layout_filterTagsRegex[_pName]])\r\n        !layout_opt.tagsStyle[layout_filterTags[parents]] || _r ? _r ? removeStyleFun(el, layout_opt.tagsStyle[layout_filterTags[parents]]) : '' : setStyleFun(el, layout_opt.tagsStyle[layout_filterTags[parents]])\r\n        return true\r\n      }\r\n      _p = o.parentNode\r\n      _pName = _p.tagName\r\n    }\r\n    return false\r\n  }\r\n  const clearStyleFun = (_block) => {\r\n    var style = dom.getAttrib(_block, 'style')\r\n    for (var key2 in layout_opt.clearStyle) {\r\n      var reg = new RegExp(layout_opt.clearStyle[key2] + ':?(.+?)\"?[;}]')\r\n      style = style.replace(reg, '')\r\n    }\r\n    dom.setAttrib(_block, 'style', style)\r\n  }\r\n  const removeStyleFun = (_block, _style) => {\r\n    var style = dom.getAttrib(_block, 'style')\r\n    for (var key2 in _style) {\r\n      var reg = new RegExp(key2 + ':?(.+?)\"?[;}]')\r\n      style = style.replace(reg, '')\r\n    }\r\n    dom.setAttrib(_block, 'style', style)\r\n  }\r\n  const setStyleFun = (_block, _style) => {\r\n    for (var key2 in _style) {\r\n      dom.setStyle(_block, key2, _style[key2])\r\n    }\r\n    if (_style['text-indent']) {\r\n      var kv = '', kl = ''\r\n      if (_block && _block.children['0'] && _block.children['0'].attributes && _block.children['0'].attributes.style) {\r\n        kv = _indent2$getValue('font-size', _block.children['0'].attributes.style.textContent)\r\n        kl = _indent2$getValue('letter-spacing', _block.children['0'].attributes.style.textContent)\r\n        if (kv) {\r\n          kv = (parseInt(kv) + parseInt(kl ? kl : 0)) * 2 + 'pt'\r\n        } else\r\n          kv = (parseInt(kl ? kl : 0) + 16) * 2 + 'pt'\r\n      }\r\n      dom.setStyle(_block, 'text-indent', layout_opt.style['text-indent'] && layout_opt.style['text-indent'] != '2em' ? layout_opt.style['text-indent'] : kv ? kv : '2em')\r\n    }\r\n  }\r\n  // var layoutAct = ''\r\n  // if (blocks[0]) {\r\n  //   blocks[0].dataset.layoutFv = blocks[0].dataset.layoutFv ? '' : blocks[0].dataset.layoutFv = 'layoutFV'\r\n  // }\r\n  global$1.each(blocks, (block) => {\r\n    //   if (layoutAct == '') {\r\n    //     if (dom.hasClass(block, 'layoutFV')) {\r\n    //       layoutAct = 'remove'\r\n    //       dom.removeClass(block, 'layoutFV')\r\n    //     } else {\r\n    //       layoutAct = 'add'\r\n    //       dom.addClass(block, 'layoutFV')\r\n    //     }\r\n    //   }\r\n    //   if (layoutAct == 'add') {\r\n    //     !filterFun(block) ? setStyleFun(block, layout_opt.style) : ''\r\n    //     layout_opt.clearStyle ? clearStyleFun(block) : ''\r\n    //   } else {\r\n    //     !filterFun(block, 'remove') ? removeStyleFun(block, layout_opt.style) : ''\r\n    //   }\r\n    !filterFun(block) ? setStyleFun(block, layout_opt.style) : ''\r\n    layout_opt.clearStyle ? clearStyleFun(block) : ''\r\n  })\r\n}\r\nconst create = (editor) => {\r\n  editor.undoManager.transact(() => {\r\n    editor.focus()\r\n    doAct(editor)\r\n  })\r\n}\r\nconst setup = (editor) => {\r\n  editor.ui.registry.addToggleButton('tpLayout', { icon: 'tpLayout', tooltip: '一键排版', onAction: () => create(editor) })\r\n  editor.ui.registry.addMenuItem('tpLayout', { icon: 'tpLayout', text: '一键排版', onAction: () => create(editor) })\r\n}\r\nconst register = (editor) => editor.addCommand('mceTpLayout', () => { create(editor) })\r\nexport const initTpLayout = (editor) => {\r\n  setup(editor)\r\n  register(editor)\r\n}\r\n"], "mappings": "AACA,IAAMA,SAAS,GAAG,SAAZA,SAASA,CAAIC,MAAM;EAAA,OAAKA,MAAM,CAACC,QAAQ,CAAC,mBAAmB,EAAE;IAAEC,SAAS,EAAE,mDAAmD;IAAEC,UAAU,EAAE,EAAE;IAAEC,UAAU,EAAE,CAAC,SAAS,EAAE,KAAK,CAAC;IAAEC,KAAK,EAAE;MAAE,YAAY,EAAE,SAAS;MAAE,aAAa,EAAE,KAAK;MAAE,aAAa,EAAE;IAAI,CAAC;IAAEC,SAAS,EAAE,CAAC;EAAE,CAAC,CAAC;AAAA;AACnR,IAAIC,iBAAiB,GAAG,CAAC,CAAC;AAC1B,IAAIC,sBAAsB,GAAG,CAAC,CAAC;AAC/B,IAAMC,KAAK,GAAG,SAARA,KAAKA,CAAIT,MAAM,EAAK;EACxB,IAAIU,GAAG,GAAGV,MAAM,CAACU,GAAG;EACpB,IAAIC,MAAM,GAAG,EAAE;EACf,IAAIC,QAAQ,GAAGC,OAAO,CAACC,IAAI,CAACC,KAAK,CAACC,OAAO,CAAC,oBAAoB,CAAC;EAC/DhB,MAAM,CAACiB,WAAW,CAAC,WAAW,CAAC;EAC/B,IAAIC,UAAU,GAAGnB,SAAS,CAACC,MAAM,CAAC;EAClC,KAAK,IAAImB,GAAG,IAAID,UAAU,CAACd,UAAU,EAAE;IACrCc,UAAU,CAACd,UAAU,CAACe,GAAG,CAAC,CAACC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,GAAGZ,sBAAsB,CAACU,UAAU,CAACd,UAAU,CAACe,GAAG,CAAC,CAACE,OAAO,CAAC,IAAI,EAAE,EAAE,CAAC,CAACC,WAAW,CAAC,CAAC,CAAC,GAAG,IAAI,GAAGf,iBAAiB,CAACW,UAAU,CAACd,UAAU,CAACe,GAAG,CAAC,CAACG,WAAW,CAAC,CAAC,CAAC,GAAG,IAAI;EACjN;EACAJ,UAAU,CAAChB,SAAS,GAAGgB,UAAU,CAAChB,SAAS,IAAI,mDAAmD;EAClG,KAAK,IAAIiB,GAAG,IAAID,UAAU,CAACZ,SAAS,EAAE;IACpC,IAAIiB,QAAQ,GAAGJ,GAAG,CAACK,KAAK,CAAC,GAAG,CAAC;IAC7BN,UAAU,CAAChB,SAAS,IAAI,GAAG,GAAGiB,GAAG;IACjC,KAAK,IAAIM,IAAI,IAAIF,QAAQ,EACvBA,QAAQ,CAACE,IAAI,CAAC,CAACL,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,GAAGZ,sBAAsB,CAACe,QAAQ,CAACE,IAAI,CAAC,CAACJ,OAAO,CAAC,IAAI,EAAE,EAAE,CAAC,CAACC,WAAW,CAAC,CAAC,CAAC,GAAGH,GAAG,GAAGZ,iBAAiB,CAACgB,QAAQ,CAACE,IAAI,CAAC,CAACH,WAAW,CAAC,CAAC,CAAC,GAAGH,GAAG;EAC7K;EACAR,MAAM,GAAGX,MAAM,CAACE,SAAS,CAACwB,OAAO,CAAC,CAAC,CAACC,gBAAgB,CAACT,UAAU,CAAChB,SAAS,CAAC;EAC1E,IAAM0B,iBAAiB,GAAG,SAApBA,iBAAiBA,CAAIC,IAAI,EAAEC,GAAG,EAAK;IACvC,IAAIC,CAAC,GAAGD,GAAG,CAACE,KAAK,CAAC,IAAIC,MAAM,CAACJ,IAAI,GAAG,eAAe,CAAC,CAAC;IACrD,OAAOE,CAAC,GAAGA,CAAC,CAAC,CAAC,CAAC,GAAG,KAAK;EACzB,CAAC;EACD,IAAMG,SAAS,GAAG,SAAZA,SAASA,CAAIC,EAAE,EAAEC,EAAE,EAAK;IAC5B,IAAIC,cAAc,GAAG,MAAM;IAC3B,IAAIC,OAAO,GAAGH,EAAE,CAACI,OAAO;IACxB,IAAIhC,iBAAiB,CAAC+B,OAAO,CAAC,IAAI9B,sBAAsB,CAAC8B,OAAO,CAAC,EAAE;MACjE,CAACpB,UAAU,CAACZ,SAAS,CAACC,iBAAiB,CAAC+B,OAAO,CAAC,CAAC,IAAIF,EAAE,GAAGA,EAAE,GAAGI,cAAc,CAACL,EAAE,EAAEjB,UAAU,CAACZ,SAAS,CAACC,iBAAiB,CAAC+B,OAAO,CAAC,CAAC,CAAC,GAAG,EAAE,GAAGG,WAAW,CAACN,EAAE,EAAEjB,UAAU,CAACZ,SAAS,CAACC,iBAAiB,CAAC+B,OAAO,CAAC,CAAC,CAAC;MAC5M,OAAO,IAAI;IACb;IACA,IAAII,EAAE,GAAGP,EAAE,CAACQ,UAAU;IACtB,IAAIC,MAAM,GAAGF,EAAE,CAACH,OAAO;IACvB,OAAOK,MAAM,KAAKP,cAAc,EAAE;MAChC,IAAIQ,CAAC,GAAGH,EAAE;MACVJ,OAAO,GAAGM,MAAM,GAAG,GAAG,GAAGN,OAAO;MAChC,IAAI/B,iBAAiB,CAAC+B,OAAO,CAAC,IAAI9B,sBAAsB,CAACoC,MAAM,CAAC,EAAE;QAChE,CAAC1B,UAAU,CAACZ,SAAS,CAACE,sBAAsB,CAACoC,MAAM,CAAC,CAAC,IAAIR,EAAE,GAAGA,EAAE,GAAGI,cAAc,CAACL,EAAE,EAAEjB,UAAU,CAACZ,SAAS,CAACE,sBAAsB,CAACoC,MAAM,CAAC,CAAC,CAAC,GAAG,EAAE,GAAGH,WAAW,CAACN,EAAE,EAAEjB,UAAU,CAACZ,SAAS,CAACE,sBAAsB,CAACoC,MAAM,CAAC,CAAC,CAAC;QACxN,CAAC1B,UAAU,CAACZ,SAAS,CAACC,iBAAiB,CAAC+B,OAAO,CAAC,CAAC,IAAIF,EAAE,GAAGA,EAAE,GAAGI,cAAc,CAACL,EAAE,EAAEjB,UAAU,CAACZ,SAAS,CAACC,iBAAiB,CAAC+B,OAAO,CAAC,CAAC,CAAC,GAAG,EAAE,GAAGG,WAAW,CAACN,EAAE,EAAEjB,UAAU,CAACZ,SAAS,CAACC,iBAAiB,CAAC+B,OAAO,CAAC,CAAC,CAAC;QAC5M,OAAO,IAAI;MACb;MACAI,EAAE,GAAGG,CAAC,CAACF,UAAU;MACjBC,MAAM,GAAGF,EAAE,CAACH,OAAO;IACrB;IACA,OAAO,KAAK;EACd,CAAC;EACD,IAAMO,aAAa,GAAG,SAAhBA,aAAaA,CAAIC,MAAM,EAAK;IAChC,IAAI1C,KAAK,GAAGK,GAAG,CAACsC,SAAS,CAACD,MAAM,EAAE,OAAO,CAAC;IAC1C,KAAK,IAAIlB,IAAI,IAAIX,UAAU,CAACf,UAAU,EAAE;MACtC,IAAI8C,GAAG,GAAG,IAAIhB,MAAM,CAACf,UAAU,CAACf,UAAU,CAAC0B,IAAI,CAAC,GAAG,eAAe,CAAC;MACnExB,KAAK,GAAGA,KAAK,CAACgB,OAAO,CAAC4B,GAAG,EAAE,EAAE,CAAC;IAChC;IACAvC,GAAG,CAACwC,SAAS,CAACH,MAAM,EAAE,OAAO,EAAE1C,KAAK,CAAC;EACvC,CAAC;EACD,IAAMmC,cAAc,GAAG,SAAjBA,cAAcA,CAAIO,MAAM,EAAEI,MAAM,EAAK;IACzC,IAAI9C,KAAK,GAAGK,GAAG,CAACsC,SAAS,CAACD,MAAM,EAAE,OAAO,CAAC;IAC1C,KAAK,IAAIlB,IAAI,IAAIsB,MAAM,EAAE;MACvB,IAAIF,GAAG,GAAG,IAAIhB,MAAM,CAACJ,IAAI,GAAG,eAAe,CAAC;MAC5CxB,KAAK,GAAGA,KAAK,CAACgB,OAAO,CAAC4B,GAAG,EAAE,EAAE,CAAC;IAChC;IACAvC,GAAG,CAACwC,SAAS,CAACH,MAAM,EAAE,OAAO,EAAE1C,KAAK,CAAC;EACvC,CAAC;EACD,IAAMoC,WAAW,GAAG,SAAdA,WAAWA,CAAIM,MAAM,EAAEI,MAAM,EAAK;IACtC,KAAK,IAAItB,IAAI,IAAIsB,MAAM,EAAE;MACvBzC,GAAG,CAAC0C,QAAQ,CAACL,MAAM,EAAElB,IAAI,EAAEsB,MAAM,CAACtB,IAAI,CAAC,CAAC;IAC1C;IACA,IAAIsB,MAAM,CAAC,aAAa,CAAC,EAAE;MACzB,IAAIE,EAAE,GAAG,EAAE;QAAEC,EAAE,GAAG,EAAE;MACpB,IAAIP,MAAM,IAAIA,MAAM,CAACQ,QAAQ,CAAC,GAAG,CAAC,IAAIR,MAAM,CAACQ,QAAQ,CAAC,GAAG,CAAC,CAACC,UAAU,IAAIT,MAAM,CAACQ,QAAQ,CAAC,GAAG,CAAC,CAACC,UAAU,CAACnD,KAAK,EAAE;QAC9GgD,EAAE,GAAGzB,iBAAiB,CAAC,WAAW,EAAEmB,MAAM,CAACQ,QAAQ,CAAC,GAAG,CAAC,CAACC,UAAU,CAACnD,KAAK,CAACoD,WAAW,CAAC;QACtFH,EAAE,GAAG1B,iBAAiB,CAAC,gBAAgB,EAAEmB,MAAM,CAACQ,QAAQ,CAAC,GAAG,CAAC,CAACC,UAAU,CAACnD,KAAK,CAACoD,WAAW,CAAC;QAC3F,IAAIJ,EAAE,EAAE;UACNA,EAAE,GAAG,CAACK,QAAQ,CAACL,EAAE,CAAC,GAAGK,QAAQ,CAACJ,EAAE,GAAGA,EAAE,GAAG,CAAC,CAAC,IAAI,CAAC,GAAG,IAAI;QACxD,CAAC,MACCD,EAAE,GAAG,CAACK,QAAQ,CAACJ,EAAE,GAAGA,EAAE,GAAG,CAAC,CAAC,GAAG,EAAE,IAAI,CAAC,GAAG,IAAI;MAChD;MACA5C,GAAG,CAAC0C,QAAQ,CAACL,MAAM,EAAE,aAAa,EAAE7B,UAAU,CAACb,KAAK,CAAC,aAAa,CAAC,IAAIa,UAAU,CAACb,KAAK,CAAC,aAAa,CAAC,IAAI,KAAK,GAAGa,UAAU,CAACb,KAAK,CAAC,aAAa,CAAC,GAAGgD,EAAE,GAAGA,EAAE,GAAG,KAAK,CAAC;IACtK;EACF,CAAC;EACD;EACA;EACA;EACA;EACAzC,QAAQ,CAAC+C,IAAI,CAAChD,MAAM,EAAE,UAACiD,KAAK,EAAK;IAC/B;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,CAAC1B,SAAS,CAAC0B,KAAK,CAAC,GAAGnB,WAAW,CAACmB,KAAK,EAAE1C,UAAU,CAACb,KAAK,CAAC,GAAG,EAAE;IAC7Da,UAAU,CAACf,UAAU,GAAG2C,aAAa,CAACc,KAAK,CAAC,GAAG,EAAE;EACnD,CAAC,CAAC;AACJ,CAAC;AACD,IAAMC,MAAM,GAAG,SAATA,MAAMA,CAAI7D,MAAM,EAAK;EACzBA,MAAM,CAAC8D,WAAW,CAACC,QAAQ,CAAC,YAAM;IAChC/D,MAAM,CAACgE,KAAK,CAAC,CAAC;IACdvD,KAAK,CAACT,MAAM,CAAC;EACf,CAAC,CAAC;AACJ,CAAC;AACD,IAAMiE,KAAK,GAAG,SAARA,KAAKA,CAAIjE,MAAM,EAAK;EACxBA,MAAM,CAACkE,EAAE,CAACC,QAAQ,CAACC,eAAe,CAAC,UAAU,EAAE;IAAEC,IAAI,EAAE,UAAU;IAAEC,OAAO,EAAE,MAAM;IAAEC,QAAQ,EAAE,SAAVA,QAAQA,CAAA;MAAA,OAAQV,MAAM,CAAC7D,MAAM,CAAC;IAAA;EAAC,CAAC,CAAC;EACrHA,MAAM,CAACkE,EAAE,CAACC,QAAQ,CAACK,WAAW,CAAC,UAAU,EAAE;IAAEH,IAAI,EAAE,UAAU;IAAEI,IAAI,EAAE,MAAM;IAAEF,QAAQ,EAAE,SAAVA,QAAQA,CAAA;MAAA,OAAQV,MAAM,CAAC7D,MAAM,CAAC;IAAA;EAAC,CAAC,CAAC;AAChH,CAAC;AACD,IAAM0E,QAAQ,GAAG,SAAXA,QAAQA,CAAI1E,MAAM;EAAA,OAAKA,MAAM,CAAC2E,UAAU,CAAC,aAAa,EAAE,YAAM;IAAEd,MAAM,CAAC7D,MAAM,CAAC;EAAC,CAAC,CAAC;AAAA;AACvF,OAAO,IAAM4E,YAAY,GAAG,SAAfA,YAAYA,CAAI5E,MAAM,EAAK;EACtCiE,KAAK,CAACjE,MAAM,CAAC;EACb0E,QAAQ,CAAC1E,MAAM,CAAC;AAClB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
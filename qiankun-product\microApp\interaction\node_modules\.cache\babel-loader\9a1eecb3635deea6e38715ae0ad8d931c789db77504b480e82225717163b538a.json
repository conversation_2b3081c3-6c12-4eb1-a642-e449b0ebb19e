{"ast": null, "code": "import { toDisplayString as _toDisplayString, createElementVNode as _createElementVNode, createCommentVNode as _createCommentVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, createTextVNode as _createTextVNode, resolveComponent as _resolveComponent, withCtx as _withCtx, createVNode as _createVNode, renderList as _renderList, Fragment as _Fragment } from \"vue\";\nvar _hoisted_1 = {\n  class: \"LiveManagementDetails\"\n};\nvar _hoisted_2 = {\n  class: \"VideoMeetinDetailsTitle\"\n};\nvar _hoisted_3 = {\n  class: \"VideoMeetinDetailsTime\"\n};\nvar _hoisted_4 = {\n  class: \"VideoMeetinDetailsTimeLeft\"\n};\nvar _hoisted_5 = {\n  class: \"VideoMeetinDetailsTimeHours\"\n};\nvar _hoisted_6 = {\n  class: \"VideoMeetinDetailsTimeDate\"\n};\nvar _hoisted_7 = {\n  class: \"VideoMeetinDetailsTimeCenter\"\n};\nvar _hoisted_8 = {\n  class: \"VideoMeetinDetailsDuration\"\n};\nvar _hoisted_9 = {\n  class: \"VideoMeetinDetailsTimeRight\"\n};\nvar _hoisted_10 = {\n  class: \"VideoMeetinDetailsTimeHours\"\n};\nvar _hoisted_11 = {\n  class: \"VideoMeetinDetailsTimeDate\"\n};\nvar _hoisted_12 = {\n  class: \"LiveList\"\n};\nvar _hoisted_13 = {\n  class: \"LiveContent\"\n};\nvar _hoisted_14 = {\n  key: 0,\n  class: \"LiveList\"\n};\nvar _hoisted_15 = {\n  class: \"player-container\"\n};\nvar _hoisted_16 = [\"src\"];\nvar _hoisted_17 = {\n  class: \"LiveList\"\n};\nvar _hoisted_18 = {\n  class: \"LiveInteractionBox\"\n};\nvar _hoisted_19 = {\n  class: \"LiveInteraction\"\n};\nvar _hoisted_20 = {\n  class: \"LiveInteractionTotal\"\n};\nvar _hoisted_21 = {\n  key: 0,\n  class: \"comment-container\"\n};\nvar _hoisted_22 = {\n  class: \"comment-main\"\n};\nvar _hoisted_23 = {\n  class: \"comment-avatar\"\n};\nvar _hoisted_24 = {\n  class: \"comment-content\"\n};\nvar _hoisted_25 = {\n  class: \"comment-header\"\n};\nvar _hoisted_26 = {\n  class: \"comment-username\"\n};\nvar _hoisted_27 = {\n  class: \"comment-text\"\n};\nvar _hoisted_28 = {\n  class: \"comment-footer\"\n};\nvar _hoisted_29 = {\n  class: \"comment-time\"\n};\nvar _hoisted_30 = [\"onClick\"];\nvar _hoisted_31 = {\n  class: \"action-btn like-btn\"\n};\nvar _hoisted_32 = {\n  key: 0,\n  class: \"reply-list\"\n};\nvar _hoisted_33 = {\n  class: \"reply-avatar\"\n};\nvar _hoisted_34 = {\n  class: \"reply-content\"\n};\nvar _hoisted_35 = {\n  class: \"reply-header\"\n};\nvar _hoisted_36 = {\n  class: \"reply-username\"\n};\nvar _hoisted_37 = {\n  key: 0,\n  class: \"reply-role\"\n};\nvar _hoisted_38 = {\n  class: \"reply-text\"\n};\nvar _hoisted_39 = {\n  key: 0\n};\nvar _hoisted_40 = {\n  class: \"reply-footer\"\n};\nvar _hoisted_41 = {\n  class: \"reply-time\"\n};\nvar _hoisted_42 = [\"onClick\"];\nvar _hoisted_43 = {\n  class: \"action-btn like-btn\"\n};\nvar _hoisted_44 = {\n  key: 1,\n  class: \"no-comments\"\n};\nvar _hoisted_45 = {\n  class: \"globalPagination\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  var _component_el_button = _resolveComponent(\"el-button\");\n  var _component_el_image = _resolveComponent(\"el-image\");\n  var _component_el_scrollbar = _resolveComponent(\"el-scrollbar\");\n  var _component_el_pagination = _resolveComponent(\"el-pagination\");\n  var _component_anchor_location = _resolveComponent(\"anchor-location\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createVNode(_component_anchor_location, null, {\n    default: _withCtx(function () {\n      var _$setup$details;\n      return [_createElementVNode(\"div\", _hoisted_2, _toDisplayString((_$setup$details = $setup.details) === null || _$setup$details === void 0 ? void 0 : _$setup$details.theme), 1 /* TEXT */), _createElementVNode(\"div\", _hoisted_3, [_createElementVNode(\"div\", _hoisted_4, [_createElementVNode(\"div\", _hoisted_5, _toDisplayString($setup.format($setup.details.startTime, 'HH:mm')), 1 /* TEXT */), _createElementVNode(\"div\", _hoisted_6, _toDisplayString($setup.format($setup.details.startTime, 'YYYY年MM月DD日')), 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_7, [_createElementVNode(\"div\", _hoisted_8, _toDisplayString($setup.details.during) + \"分钟\", 1 /* TEXT */), _createCommentVNode(\" <div class=\\\"VideoMeetinDetailsStatus\\\">{{ details.meetingStatus }}</div> \")]), _createElementVNode(\"div\", _hoisted_9, [_createElementVNode(\"div\", _hoisted_10, _toDisplayString($setup.format($setup.details.endTime, 'HH:mm')), 1 /* TEXT */), _createElementVNode(\"div\", _hoisted_11, _toDisplayString($setup.format($setup.details.endTime, 'YYYY年MM月DD日')), 1 /* TEXT */)])]), _createElementVNode(\"div\", _hoisted_12, [_cache[3] || (_cache[3] = _createElementVNode(\"div\", {\n        class: \"LiveName\"\n      }, \"直播简介\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_13, _toDisplayString($setup.details.liveDescribes), 1 /* TEXT */)]), $setup.details.isReplay == 1 && $setup.details.liveReplayUrl ? (_openBlock(), _createElementBlock(\"div\", _hoisted_14, [_cache[4] || (_cache[4] = _createElementVNode(\"div\", {\n        class: \"LiveName\"\n      }, \"直播回放\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_15, [_createElementVNode(\"video\", {\n        ref: \"videoPlayer\",\n        id: \"video-player\",\n        src: $setup.details.liveReplayUrl,\n        controls: \"\"\n      }, null, 8 /* PROPS */, _hoisted_16)])])) : _createCommentVNode(\"v-if\", true), _createElementVNode(\"div\", _hoisted_17, [_createElementVNode(\"div\", _hoisted_18, [_cache[6] || (_cache[6] = _createElementVNode(\"div\", {\n        class: \"LiveName\",\n        style: {\n          \"margin-top\": \"0\"\n        }\n      }, \"直播互动\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_19, [_createElementVNode(\"div\", _hoisted_20, \"(共\" + _toDisplayString($setup.totals) + \"条互动消息)\", 1 /* TEXT */), _createVNode(_component_el_button, {\n        type: \"primary\",\n        onClick: _cache[0] || (_cache[0] = function ($event) {\n          return $setup.excelMsg();\n        })\n      }, {\n        default: _withCtx(function () {\n          return _cache[5] || (_cache[5] = [_createTextVNode(\"导出互动消息\")]);\n        }),\n        _: 1 /* STABLE */\n      })])]), _createVNode(_component_el_scrollbar, {\n        class: \"LiveCommentList\"\n      }, {\n        default: _withCtx(function () {\n          return [$setup.commentList.length ? (_openBlock(), _createElementBlock(\"div\", _hoisted_21, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.commentList, function (item) {\n            return _openBlock(), _createElementBlock(\"div\", {\n              class: \"comment-item\",\n              key: item.id\n            }, [_createCommentVNode(\" 主评论 \"), _createElementVNode(\"div\", _hoisted_22, [_createElementVNode(\"div\", _hoisted_23, [_createVNode(_component_el_image, {\n              src: $setup.getAvatarUrl(item.headImg),\n              fit: \"cover\"\n            }, null, 8 /* PROPS */, [\"src\"])]), _createElementVNode(\"div\", _hoisted_24, [_createElementVNode(\"div\", _hoisted_25, [_createElementVNode(\"span\", _hoisted_26, _toDisplayString(item.commentUserName), 1 /* TEXT */), _createCommentVNode(\" <span class=\\\"comment-role\\\" v-if=\\\"item.roles && item.roles.length\\\">（{{ item.roles[0].roleName\\r\\n                    }}）</span> \")]), _createElementVNode(\"div\", _hoisted_27, _toDisplayString(item.commentContent), 1 /* TEXT */), _createElementVNode(\"div\", _hoisted_28, [_createElementVNode(\"span\", _hoisted_29, _toDisplayString($setup.formatTime(item.createDate)), 1 /* TEXT */), _createElementVNode(\"span\", {\n              class: \"action-btn delete-btn\",\n              onClick: function onClick($event) {\n                return $setup.deleteComment(item);\n              }\n            }, \"删除\", 8 /* PROPS */, _hoisted_30), _createElementVNode(\"span\", _hoisted_31, [_createVNode(_component_el_image, {\n              src: $setup.getLikeIcon(item.isLiked),\n              class: \"like-icon\"\n            }, null, 8 /* PROPS */, [\"src\"]), _createElementVNode(\"span\", null, \"(\" + _toDisplayString(item.likeCount || 20) + \")\", 1 /* TEXT */)])])])]), _createCommentVNode(\" 回复列表 \"), item.children && item.children.length ? (_openBlock(), _createElementBlock(\"div\", _hoisted_32, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList(item.children, function (reply) {\n              return _openBlock(), _createElementBlock(\"div\", {\n                class: \"reply-item\",\n                key: reply.id\n              }, [_createElementVNode(\"div\", _hoisted_33, [_createVNode(_component_el_image, {\n                src: $setup.getAvatarUrl(reply.headImg),\n                fit: \"cover\"\n              }, null, 8 /* PROPS */, [\"src\"])]), _createElementVNode(\"div\", _hoisted_34, [_createElementVNode(\"div\", _hoisted_35, [_createElementVNode(\"span\", _hoisted_36, _toDisplayString(reply.commentUserName), 1 /* TEXT */), reply.roles && reply.roles.length ? (_openBlock(), _createElementBlock(\"span\", _hoisted_37, \"（\" + _toDisplayString(reply.roles[0].roleName) + \"）\", 1 /* TEXT */)) : _createCommentVNode(\"v-if\", true)]), _createElementVNode(\"div\", _hoisted_38, [reply.toCommenter && reply.parentId !== item.id ? (_openBlock(), _createElementBlock(\"span\", _hoisted_39, \"回复\" + _toDisplayString(reply.toCommenter) + \"：\", 1 /* TEXT */)) : _createCommentVNode(\"v-if\", true), _createTextVNode(\" \" + _toDisplayString(reply.commentContent), 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_40, [_createElementVNode(\"span\", _hoisted_41, _toDisplayString($setup.formatTime(reply.createDate)), 1 /* TEXT */), _createElementVNode(\"span\", {\n                class: \"action-btn delete-btn\",\n                onClick: function onClick($event) {\n                  return $setup.deleteComment(reply);\n                }\n              }, \"删除\", 8 /* PROPS */, _hoisted_42), _createElementVNode(\"span\", _hoisted_43, [_createVNode(_component_el_image, {\n                src: $setup.getLikeIcon(reply.hasClickPraises),\n                class: \"like-icon\"\n              }, null, 8 /* PROPS */, [\"src\"]), _createElementVNode(\"span\", null, \"(\" + _toDisplayString(reply.likeCount || 20) + \")\", 1 /* TEXT */)])])])]);\n            }), 128 /* KEYED_FRAGMENT */))])) : _createCommentVNode(\"v-if\", true)]);\n          }), 128 /* KEYED_FRAGMENT */))])) : (_openBlock(), _createElementBlock(\"div\", _hoisted_44, \" 暂无互动消息 \"))];\n        }),\n        _: 1 /* STABLE */\n      }), _createElementVNode(\"div\", _hoisted_45, [_createVNode(_component_el_pagination, {\n        currentPage: $setup.pageNo,\n        \"onUpdate:currentPage\": _cache[1] || (_cache[1] = function ($event) {\n          return $setup.pageNo = $event;\n        }),\n        \"page-size\": $setup.pageSize,\n        \"onUpdate:pageSize\": _cache[2] || (_cache[2] = function ($event) {\n          return $setup.pageSize = $event;\n        }),\n        \"page-sizes\": [10, 20, 50, 100, 200],\n        layout: \"total, sizes, prev, pager, next, jumper\",\n        onSizeChange: $setup.handleQuery,\n        onCurrentChange: $setup.handleQuery,\n        total: $setup.totals,\n        background: \"\"\n      }, null, 8 /* PROPS */, [\"currentPage\", \"page-size\", \"total\"])])])];\n    }),\n    _: 1 /* STABLE */\n  })]);\n}", "map": {"version": 3, "names": ["class", "key", "_createElementBlock", "_hoisted_1", "_createVNode", "_component_anchor_location", "default", "_withCtx", "_$setup$details", "_createElementVNode", "_hoisted_2", "_toDisplayString", "$setup", "details", "theme", "_hoisted_3", "_hoisted_4", "_hoisted_5", "format", "startTime", "_hoisted_6", "_hoisted_7", "_hoisted_8", "during", "_createCommentVNode", "_hoisted_9", "_hoisted_10", "endTime", "_hoisted_11", "_hoisted_12", "_hoisted_13", "liveDescribes", "isReplay", "liveReplayUrl", "_hoisted_14", "_hoisted_15", "ref", "id", "src", "controls", "_hoisted_16", "_hoisted_17", "_hoisted_18", "style", "_hoisted_19", "_hoisted_20", "totals", "_component_el_button", "type", "onClick", "_cache", "$event", "excelMsg", "_createTextVNode", "_", "_component_el_scrollbar", "commentList", "length", "_hoisted_21", "_Fragment", "_renderList", "item", "_hoisted_22", "_hoisted_23", "_component_el_image", "getAvatarUrl", "headImg", "fit", "_hoisted_24", "_hoisted_25", "_hoisted_26", "commentUserName", "_hoisted_27", "commentContent", "_hoisted_28", "_hoisted_29", "formatTime", "createDate", "deleteComment", "_hoisted_30", "_hoisted_31", "getLikeIcon", "isLiked", "likeCount", "children", "_hoisted_32", "reply", "_hoisted_33", "_hoisted_34", "_hoisted_35", "_hoisted_36", "roles", "_hoisted_37", "<PERSON><PERSON><PERSON>", "_hoisted_38", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "parentId", "_hoisted_39", "_hoisted_40", "_hoisted_41", "_hoisted_42", "_hoisted_43", "hasClickPraises", "_hoisted_44", "_hoisted_45", "_component_el_pagination", "currentPage", "pageNo", "pageSize", "layout", "onSizeChange", "handleQuery", "onCurrentChange", "total", "background"], "sources": ["D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\interaction\\src\\views\\LiveManagement\\LiveManagementDetails.vue"], "sourcesContent": ["<template>\r\n  <div class=\"LiveManagementDetails\">\r\n    <anchor-location>\r\n      <div class=\"VideoMeetinDetailsTitle\">{{ details?.theme }}</div>\r\n      <div class=\"VideoMeetinDetailsTime\">\r\n        <div class=\"VideoMeetinDetailsTimeLeft\">\r\n          <div class=\"VideoMeetinDetailsTimeHours\">{{ format(details.startTime, 'HH:mm') }}</div>\r\n          <div class=\"VideoMeetinDetailsTimeDate\">{{ format(details.startTime, 'YYYY年MM月DD日') }}</div>\r\n        </div>\r\n        <div class=\"VideoMeetinDetailsTimeCenter\">\r\n          <div class=\"VideoMeetinDetailsDuration\">{{ details.during }}分钟</div>\r\n          <!-- <div class=\"VideoMeetinDetailsStatus\">{{ details.meetingStatus }}</div> -->\r\n        </div>\r\n        <div class=\"VideoMeetinDetailsTimeRight\">\r\n          <div class=\"VideoMeetinDetailsTimeHours\">{{ format(details.endTime, 'HH:mm') }}</div>\r\n          <div class=\"VideoMeetinDetailsTimeDate\">{{ format(details.endTime, 'YYYY年MM月DD日') }}</div>\r\n        </div>\r\n      </div>\r\n      <div class=\"LiveList\">\r\n        <div class=\"LiveName\">直播简介</div>\r\n        <div class=\"LiveContent\">{{ details.liveDescribes }}</div>\r\n      </div>\r\n      <div class=\"LiveList\" v-if=\"details.isReplay == 1 && details.liveReplayUrl\">\r\n        <div class=\"LiveName\">直播回放</div>\r\n        <div class=\"player-container\">\r\n          <video ref=\"videoPlayer\" id=\"video-player\" :src=\"details.liveReplayUrl\" controls></video>\r\n        </div>\r\n      </div>\r\n      <div class=\"LiveList\">\r\n        <div class=\"LiveInteractionBox\">\r\n          <div class=\"LiveName\" style=\"margin-top: 0;\">直播互动</div>\r\n          <div class=\"LiveInteraction\">\r\n            <div class=\"LiveInteractionTotal\">(共{{ totals }}条互动消息)</div>\r\n            <el-button type=\"primary\" @click=\"excelMsg()\">导出互动消息</el-button>\r\n          </div>\r\n        </div>\r\n        <el-scrollbar class=\"LiveCommentList\">\r\n          <div class=\"comment-container\" v-if=\"commentList.length\">\r\n            <div class=\"comment-item\" v-for=\"item in commentList\" :key=\"item.id\">\r\n              <!-- 主评论 -->\r\n              <div class=\"comment-main\">\r\n                <div class=\"comment-avatar\">\r\n                  <el-image :src=\"getAvatarUrl(item.headImg)\" fit=\"cover\" />\r\n                </div>\r\n                <div class=\"comment-content\">\r\n                  <div class=\"comment-header\">\r\n                    <span class=\"comment-username\">{{ item.commentUserName }}</span>\r\n                    <!-- <span class=\"comment-role\" v-if=\"item.roles && item.roles.length\">（{{ item.roles[0].roleName\r\n                    }}）</span> -->\r\n                  </div>\r\n                  <div class=\"comment-text\">{{ item.commentContent }}</div>\r\n                  <div class=\"comment-footer\">\r\n                    <span class=\"comment-time\">{{ formatTime(item.createDate) }}</span>\r\n                    <span class=\"action-btn delete-btn\" @click=\"deleteComment(item)\">删除</span>\r\n                    <span class=\"action-btn like-btn\">\r\n                      <el-image :src=\"getLikeIcon(item.isLiked)\" class=\"like-icon\" />\r\n                      <span>({{ item.likeCount || 20 }})</span>\r\n                    </span>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n\r\n              <!-- 回复列表 -->\r\n              <div class=\"reply-list\" v-if=\"item.children && item.children.length\">\r\n                <div class=\"reply-item\" v-for=\"reply in item.children\" :key=\"reply.id\">\r\n                  <div class=\"reply-avatar\">\r\n                    <el-image :src=\"getAvatarUrl(reply.headImg)\" fit=\"cover\" />\r\n                  </div>\r\n                  <div class=\"reply-content\">\r\n                    <div class=\"reply-header\">\r\n                      <span class=\"reply-username\">{{ reply.commentUserName }}</span>\r\n                      <span class=\"reply-role\" v-if=\"reply.roles && reply.roles.length\">（{{ reply.roles[0].roleName\r\n                      }}）</span>\r\n                    </div>\r\n                    <div class=\"reply-text\">\r\n                      <span v-if=\"reply.toCommenter && reply.parentId !== item.id\">回复{{ reply.toCommenter }}：</span>\r\n                      {{ reply.commentContent }}\r\n                    </div>\r\n                    <div class=\"reply-footer\">\r\n                      <span class=\"reply-time\">{{ formatTime(reply.createDate) }}</span>\r\n                      <span class=\"action-btn delete-btn\" @click=\"deleteComment(reply)\">删除</span>\r\n                      <span class=\"action-btn like-btn\">\r\n                        <el-image :src=\"getLikeIcon(reply.hasClickPraises)\" class=\"like-icon\" />\r\n                        <span>({{ reply.likeCount || 20 }})</span>\r\n                      </span>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n          <div class=\"no-comments\" v-else>\r\n            暂无互动消息\r\n          </div>\r\n        </el-scrollbar>\r\n        <div class=\"globalPagination\">\r\n          <el-pagination v-model:currentPage=\"pageNo\" v-model:page-size=\"pageSize\" :page-sizes=\"[10, 20, 50, 100, 200]\"\r\n            layout=\"total, sizes, prev, pager, next, jumper\" @size-change=\"handleQuery\" @current-change=\"handleQuery\"\r\n            :total=\"totals\" background />\r\n        </div>\r\n      </div>\r\n    </anchor-location>\r\n  </div>\r\n</template>\r\n<script>\r\nexport default { name: 'LiveManagementDetails' }\r\n</script>\r\n<script setup>\r\nimport api from '@/api'\r\nimport { ref, onMounted } from 'vue'\r\nimport { useRoute } from 'vue-router'\r\nimport { format } from 'common/js/time.js'\r\nimport { ElMessage, ElMessageBox } from 'element-plus'\r\n\r\nconst route = useRoute()\r\nconst details = ref({})\r\nconst pageNo = ref(1)\r\nconst pageSize = ref(10)\r\nconst totals = ref(0)\r\nconst commentList = ref([])\r\n\r\nonMounted(() => {\r\n  if (route.query.id) { videoConnectionInfo() }\r\n})\r\n\r\nconst videoConnectionInfo = async () => {\r\n  const res = await api.videoConnectionInfo({ detailId: route.query.id })\r\n  var { data } = res\r\n  details.value = data\r\n  handleQuery()\r\n}\r\n\r\nconst excelMsg = () => {\r\n  console.log('导出互动消息')\r\n}\r\n\r\nconst handleQuery = () => {\r\n  twoLevelTree()\r\n}\r\n\r\nconst twoLevelTree = async () => {\r\n  const { data, total } = await api.twoLevelTree({\r\n    businessCode: 'liveBroadcast',\r\n    businessId: route.query.id,\r\n    pageNo: pageNo.value,\r\n    pageSize: pageSize.value,\r\n    isAscByTime: 1\r\n  })\r\n  commentList.value = data\r\n  totals.value = total\r\n}\r\n\r\n// 获取头像URL\r\nconst getAvatarUrl = (url) => {\r\n  return url ? api.fileURL(url) : api.defaultImgURL('default_user_head.jpg')\r\n}\r\n\r\n// 格式化时间\r\nconst formatTime = (time) => {\r\n  return format(time, 'YYYY-MM-DD HH:mm')\r\n}\r\n\r\n// 获取点赞图标\r\nconst getLikeIcon = (isLiked) => {\r\n  return isLiked ? '../../assets/img/fabulous_o.png' : api.defaultImgURL('fabulous.png')\r\n}\r\n\r\n// 删除功能\r\nconst deleteComment = (item) => {\r\n  ElMessageBox.confirm(`此操作将删除当前选中的评论, 是否继续?`, '提示', {\r\n    confirmButtonText: '确定',\r\n    cancelButtonText: '取消',\r\n    type: 'warning'\r\n  })\r\n    .then(() => {\r\n      commentDel(item.id)\r\n    })\r\n    .catch(() => {\r\n      ElMessage({ type: 'info', message: '已取消删除' })\r\n    })\r\n}\r\n\r\n// 删除评论API调用\r\nconst commentDel = async (id) => {\r\n  try {\r\n    const { code } = await api.commentDel({ ids: [id] })\r\n    if (code === 200) {\r\n      ElMessage({ type: 'success', message: '删除成功' })\r\n      // 重新获取评论列表\r\n      handleQuery()\r\n    }\r\n  } catch (error) {\r\n    ElMessage({ type: 'error', message: '删除失败' })\r\n  }\r\n}\r\n</script>\r\n<style lang=\"scss\">\r\n.LiveManagementDetails {\r\n  width: 100%;\r\n  height: 100%;\r\n  padding: 0 20px;\r\n\r\n  .anchor-location-body {\r\n    padding: var(--zy-distance-one);\r\n  }\r\n\r\n  .VideoMeetinDetailsTitle {\r\n    text-align: center;\r\n    font-weight: bold;\r\n    font-size: var(--zy-title-font-size);\r\n    padding: var(--zy-distance-two) var(--zy-distance-one);\r\n    line-height: var(--zy-line-height);\r\n  }\r\n\r\n  .VideoMeetinDetailsTime {\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: space-evenly;\r\n    padding: var(--zy-distance-two) 0;\r\n\r\n    .VideoMeetinDetailsTimeLeft,\r\n    .VideoMeetinDetailsTimeRight {\r\n      text-align: center;\r\n      padding: 0 var(--zy-distance-one);\r\n    }\r\n\r\n    .VideoMeetinDetailsTimeCenter {\r\n      width: 99px;\r\n      height: 99px;\r\n      display: flex;\r\n      align-items: center;\r\n      flex-direction: column;\r\n      justify-content: center;\r\n      border-radius: 50%;\r\n      border: 1px solid var(--zy-el-color-primary);\r\n      background: var(--zy-el-color-primary-light-9);\r\n      position: relative;\r\n\r\n      &::after {\r\n        content: '';\r\n        position: absolute;\r\n        width: 99px;\r\n        height: 1px;\r\n        background: linear-gradient(90deg, var(--zy-el-color-primary), #fff);\r\n        top: 50%;\r\n        right: 0;\r\n        transform: translate(100%, -50%);\r\n      }\r\n\r\n      &::before {\r\n        content: '';\r\n        position: absolute;\r\n        width: 99px;\r\n        height: 1px;\r\n        background: linear-gradient(90deg, #fff, var(--zy-el-color-primary));\r\n        top: 50%;\r\n        left: 0;\r\n        transform: translate(-100%, -50%);\r\n      }\r\n\r\n      .VideoMeetinDetailsDuration {\r\n        font-weight: bold;\r\n        color: var(--zy-el-color-primary);\r\n        font-size: calc(var(--zy-name-font-size) + 2px);\r\n      }\r\n\r\n      .VideoMeetinDetailsStatus {\r\n        color: var(--zy-el-color-primary);\r\n        font-size: var(--zy-name-font-size);\r\n      }\r\n    }\r\n\r\n    .VideoMeetinDetailsTimeHours {\r\n      font-weight: bold;\r\n      font-size: calc(var(--zy-name-font-size) + 6px);\r\n    }\r\n\r\n    .VideoMeetinDetailsTimeDate {\r\n      font-size: var(--zy-name-font-size);\r\n    }\r\n  }\r\n\r\n  .LiveList {\r\n    width: 100%;\r\n    // display: flex;\r\n    // flex-wrap: wrap;\r\n    padding: 0 var(--zy-distance-one);\r\n\r\n    .LiveName {\r\n      font-weight: bold;\r\n      font-size: 20px;\r\n      line-height: var(--zy-line-height);\r\n      margin-top: 10px;\r\n    }\r\n\r\n    .LiveContent {\r\n      font-size: 16px;\r\n      line-height: var(--zy-line-height);\r\n      text-indent: 2em;\r\n      margin-top: 5px\r\n    }\r\n\r\n    .player-container {\r\n      width: 100%;\r\n      height: 100%;\r\n      margin-top: 5px;\r\n\r\n      #video-player {\r\n        width: 100%;\r\n        height: 100%;\r\n      }\r\n    }\r\n\r\n    .LiveInteractionBox {\r\n      display: flex;\r\n      align-items: center;\r\n      margin-top: 10px;\r\n      justify-content: space-between;\r\n      width: 100%;\r\n    }\r\n\r\n    .LiveInteraction {\r\n      display: flex;\r\n      align-items: center;\r\n\r\n      .LiveInteractionTotal {\r\n        margin-right: 10px;\r\n      }\r\n    }\r\n\r\n    .LiveCommentList {\r\n      height: 600px;\r\n      margin-top: 10px;\r\n      border: 1px solid var(--zy-el-border-color-lighter);\r\n\r\n      .comment-container {\r\n        width: 100%;\r\n        padding: 0 var(--zy-distance-two);\r\n      }\r\n\r\n      .comment-item {\r\n        width: 100%;\r\n        padding: 20px 0 10px 0;\r\n        border-bottom: 1px solid var(--zy-el-border-color-lighter);\r\n\r\n        &:last-child {\r\n          border-bottom: none;\r\n        }\r\n      }\r\n\r\n      .comment-main {\r\n        display: flex;\r\n        align-items: flex-start;\r\n        gap: 12px;\r\n      }\r\n\r\n      .comment-avatar {\r\n        flex-shrink: 0;\r\n\r\n        .zy-el-image {\r\n          width: 40px;\r\n          height: 40px;\r\n          border-radius: 50%;\r\n          overflow: hidden;\r\n        }\r\n      }\r\n\r\n      .comment-content {\r\n        flex: 1;\r\n      }\r\n\r\n      .comment-header {\r\n        display: flex;\r\n        align-items: center;\r\n        margin-bottom: 8px;\r\n      }\r\n\r\n      .comment-username {\r\n        font-weight: bold;\r\n        font-size: 14px;\r\n        color: #333;\r\n      }\r\n\r\n      .comment-role {\r\n        font-size: 12px;\r\n        color: #666;\r\n        margin-left: 4px;\r\n      }\r\n\r\n      .comment-text {\r\n        font-size: 14px;\r\n        line-height: 1.5;\r\n        color: #333;\r\n        margin-bottom: 8px;\r\n      }\r\n\r\n      .comment-footer {\r\n        display: flex;\r\n        align-items: center;\r\n        gap: 16px;\r\n      }\r\n\r\n      .comment-time {\r\n        font-size: 12px;\r\n        color: #999;\r\n      }\r\n\r\n      .action-btn {\r\n        display: flex;\r\n        align-items: center;\r\n        gap: 4px;\r\n        cursor: pointer;\r\n        font-size: 12px;\r\n        color: #666;\r\n        transition: color 0.3s;\r\n\r\n        &:hover {\r\n          color: #409eff;\r\n        }\r\n\r\n        &.delete-btn {\r\n          color: #f56c6c;\r\n\r\n          &:hover {\r\n            color: #f78989;\r\n          }\r\n        }\r\n\r\n        &.like-btn {\r\n          cursor: default;\r\n\r\n          .like-icon {\r\n            width: 16px;\r\n            height: 16px;\r\n          }\r\n        }\r\n      }\r\n\r\n      // 回复样式\r\n      .reply-list {\r\n        margin-top: 12px;\r\n        margin-left: 52px;\r\n        background-color: var(--zy-el-color-info-light-9);\r\n        border-radius: 8px;\r\n        padding: 12px;\r\n        border-left: 3px solid var(--zy-el-color-primary-light-5);\r\n      }\r\n\r\n      .reply-item {\r\n        display: flex;\r\n        align-items: flex-start;\r\n        gap: 8px;\r\n        margin-bottom: 12px;\r\n\r\n        &:last-child {\r\n          margin-bottom: 0;\r\n        }\r\n      }\r\n\r\n      .reply-avatar {\r\n        flex-shrink: 0;\r\n\r\n        .zy-el-image {\r\n          width: 28px;\r\n          height: 28px;\r\n          border-radius: 50%;\r\n          overflow: hidden;\r\n        }\r\n      }\r\n\r\n      .reply-content {\r\n        flex: 1;\r\n      }\r\n\r\n      .reply-header {\r\n        display: flex;\r\n        align-items: center;\r\n        margin-bottom: 4px;\r\n      }\r\n\r\n      .reply-username {\r\n        font-weight: bold;\r\n        font-size: 13px;\r\n        color: #333;\r\n      }\r\n\r\n      .reply-role {\r\n        font-size: 11px;\r\n        color: #666;\r\n        margin-left: 4px;\r\n      }\r\n\r\n      .reply-text {\r\n        font-size: 13px;\r\n        line-height: 1.4;\r\n        color: #333;\r\n        margin-bottom: 6px;\r\n      }\r\n\r\n      .reply-footer {\r\n        display: flex;\r\n        align-items: center;\r\n        gap: 12px;\r\n      }\r\n\r\n      .reply-time {\r\n        font-size: 12px;\r\n        color: #999;\r\n      }\r\n\r\n      .no-comments {\r\n        text-align: center;\r\n        color: #999;\r\n        padding: 40px 0;\r\n        font-size: 14px;\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;EACOA,KAAK,EAAC;AAAuB;;EAEzBA,KAAK,EAAC;AAAyB;;EAC/BA,KAAK,EAAC;AAAwB;;EAC5BA,KAAK,EAAC;AAA4B;;EAChCA,KAAK,EAAC;AAA6B;;EACnCA,KAAK,EAAC;AAA4B;;EAEpCA,KAAK,EAAC;AAA8B;;EAClCA,KAAK,EAAC;AAA4B;;EAGpCA,KAAK,EAAC;AAA6B;;EACjCA,KAAK,EAAC;AAA6B;;EACnCA,KAAK,EAAC;AAA4B;;EAGtCA,KAAK,EAAC;AAAU;;EAEdA,KAAK,EAAC;AAAa;;EApBhCC,GAAA;EAsBWD,KAAK,EAAC;;;EAEJA,KAAK,EAAC;AAAkB;kBAxBrC;;EA4BWA,KAAK,EAAC;AAAU;;EACdA,KAAK,EAAC;AAAoB;;EAExBA,KAAK,EAAC;AAAiB;;EACrBA,KAAK,EAAC;AAAsB;;EAhC7CC,GAAA;EAqCeD,KAAK,EAAC;;;EAGFA,KAAK,EAAC;AAAc;;EAClBA,KAAK,EAAC;AAAgB;;EAGtBA,KAAK,EAAC;AAAiB;;EACrBA,KAAK,EAAC;AAAgB;;EACnBA,KAAK,EAAC;AAAkB;;EAI3BA,KAAK,EAAC;AAAc;;EACpBA,KAAK,EAAC;AAAgB;;EACnBA,KAAK,EAAC;AAAc;kBApD9C;;EAsD0BA,KAAK,EAAC;AAAqB;;EAtDrDC,GAAA;EA+DmBD,KAAK,EAAC;;;EAEFA,KAAK,EAAC;AAAc;;EAGpBA,KAAK,EAAC;AAAe;;EACnBA,KAAK,EAAC;AAAc;;EACjBA,KAAK,EAAC;AAAgB;;EAtElDC,GAAA;EAuE4BD,KAAK,EAAC;;;EAGTA,KAAK,EAAC;AAAY;;EA1E3CC,GAAA;AAAA;;EA8EyBD,KAAK,EAAC;AAAc;;EACjBA,KAAK,EAAC;AAAY;kBA/E9C;;EAiF4BA,KAAK,EAAC;AAAqB;;EAjFvDC,GAAA;EA2FeD,KAAK,EAAC;;;EAIRA,KAAK,EAAC;AAAkB;;;;;;;uBA9FnCE,mBAAA,CAqGM,OArGNC,UAqGM,GApGJC,YAAA,CAmGkBC,0BAAA;IArGtBC,OAAA,EAAAC,QAAA,CAGM;MAAA,IAAAC,eAAA;MAAA,OAA+D,CAA/DC,mBAAA,CAA+D,OAA/DC,UAA+D,EAAAC,gBAAA,EAAAH,eAAA,GAAvBI,MAAA,CAAAC,OAAO,cAAAL,eAAA,uBAAPA,eAAA,CAASM,KAAK,kBACtDL,mBAAA,CAaM,OAbNM,UAaM,GAZJN,mBAAA,CAGM,OAHNO,UAGM,GAFJP,mBAAA,CAAuF,OAAvFQ,UAAuF,EAAAN,gBAAA,CAA3CC,MAAA,CAAAM,MAAM,CAACN,MAAA,CAAAC,OAAO,CAACM,SAAS,4BACpEV,mBAAA,CAA4F,OAA5FW,UAA4F,EAAAT,gBAAA,CAAjDC,MAAA,CAAAM,MAAM,CAACN,MAAA,CAAAC,OAAO,CAACM,SAAS,iC,GAErEV,mBAAA,CAGM,OAHNY,UAGM,GAFJZ,mBAAA,CAAoE,OAApEa,UAAoE,EAAAX,gBAAA,CAAzBC,MAAA,CAAAC,OAAO,CAACU,MAAM,IAAG,IAAE,iBAC9DC,mBAAA,+EAAgF,C,GAElFf,mBAAA,CAGM,OAHNgB,UAGM,GAFJhB,mBAAA,CAAqF,OAArFiB,WAAqF,EAAAf,gBAAA,CAAzCC,MAAA,CAAAM,MAAM,CAACN,MAAA,CAAAC,OAAO,CAACc,OAAO,4BAClElB,mBAAA,CAA0F,OAA1FmB,WAA0F,EAAAjB,gBAAA,CAA/CC,MAAA,CAAAM,MAAM,CAACN,MAAA,CAAAC,OAAO,CAACc,OAAO,iC,KAGrElB,mBAAA,CAGM,OAHNoB,WAGM,G,0BAFJpB,mBAAA,CAAgC;QAA3BT,KAAK,EAAC;MAAU,GAAC,MAAI,sBAC1BS,mBAAA,CAA0D,OAA1DqB,WAA0D,EAAAnB,gBAAA,CAA9BC,MAAA,CAAAC,OAAO,CAACkB,aAAa,iB,GAEvBnB,MAAA,CAAAC,OAAO,CAACmB,QAAQ,SAASpB,MAAA,CAAAC,OAAO,CAACoB,aAAa,I,cAA1E/B,mBAAA,CAKM,OALNgC,WAKM,G,0BAJJzB,mBAAA,CAAgC;QAA3BT,KAAK,EAAC;MAAU,GAAC,MAAI,sBAC1BS,mBAAA,CAEM,OAFN0B,WAEM,GADJ1B,mBAAA,CAAyF;QAAlF2B,GAAG,EAAC,aAAa;QAACC,EAAE,EAAC,cAAc;QAAEC,GAAG,EAAE1B,MAAA,CAAAC,OAAO,CAACoB,aAAa;QAAEM,QAAQ,EAAR;8BAzBlFC,WAAA,E,OAAAhB,mBAAA,gBA4BMf,mBAAA,CAwEM,OAxENgC,WAwEM,GAvEJhC,mBAAA,CAMM,OANNiC,WAMM,G,0BALJjC,mBAAA,CAAuD;QAAlDT,KAAK,EAAC,UAAU;QAAC2C,KAAsB,EAAtB;UAAA;QAAA;SAAuB,MAAI,sBACjDlC,mBAAA,CAGM,OAHNmC,WAGM,GAFJnC,mBAAA,CAA4D,OAA5DoC,WAA4D,EAA1B,IAAE,GAAAlC,gBAAA,CAAGC,MAAA,CAAAkC,MAAM,IAAG,QAAM,iBACtD1C,YAAA,CAAgE2C,oBAAA;QAArDC,IAAI,EAAC,SAAS;QAAEC,OAAK,EAAAC,MAAA,QAAAA,MAAA,gBAAAC,MAAA;UAAA,OAAEvC,MAAA,CAAAwC,QAAQ;QAAA;;QAjCtD9C,OAAA,EAAAC,QAAA,CAiC0D;UAAA,OAAM2C,MAAA,QAAAA,MAAA,OAjChEG,gBAAA,CAiC0D,QAAM,E;;QAjChEC,CAAA;cAoCQlD,YAAA,CA0DemD,uBAAA;QA1DDvD,KAAK,EAAC;MAAiB;QApC7CM,OAAA,EAAAC,QAAA,CAqCU;UAAA,OAqDM,CArD+BK,MAAA,CAAA4C,WAAW,CAACC,MAAM,I,cAAvDvD,mBAAA,CAqDM,OArDNwD,WAqDM,I,kBApDJxD,mBAAA,CAmDMyD,SAAA,QAzFlBC,WAAA,CAsCqDhD,MAAA,CAAA4C,WAAW,EAtChE,UAsC6CK,IAAI;iCAArC3D,mBAAA,CAmDM;cAnDDF,KAAK,EAAC,cAAc;cAA8BC,GAAG,EAAE4D,IAAI,CAACxB;gBAC/Db,mBAAA,SAAY,EACZf,mBAAA,CAoBM,OApBNqD,WAoBM,GAnBJrD,mBAAA,CAEM,OAFNsD,WAEM,GADJ3D,YAAA,CAA0D4D,mBAAA;cAA/C1B,GAAG,EAAE1B,MAAA,CAAAqD,YAAY,CAACJ,IAAI,CAACK,OAAO;cAAGC,GAAG,EAAC;gDAElD1D,mBAAA,CAeM,OAfN2D,WAeM,GAdJ3D,mBAAA,CAIM,OAJN4D,WAIM,GAHJ5D,mBAAA,CAAgE,QAAhE6D,WAAgE,EAAA3D,gBAAA,CAA9BkD,IAAI,CAACU,eAAe,kBACtD/C,mBAAA,wIACc,C,GAEhBf,mBAAA,CAAyD,OAAzD+D,WAAyD,EAAA7D,gBAAA,CAA5BkD,IAAI,CAACY,cAAc,kBAChDhE,mBAAA,CAOM,OAPNiE,WAOM,GANJjE,mBAAA,CAAmE,QAAnEkE,WAAmE,EAAAhE,gBAAA,CAArCC,MAAA,CAAAgE,UAAU,CAACf,IAAI,CAACgB,UAAU,mBACxDpE,mBAAA,CAA0E;cAApET,KAAK,EAAC,uBAAuB;cAAEiD,OAAK,WAALA,OAAKA,CAAAE,MAAA;gBAAA,OAAEvC,MAAA,CAAAkE,aAAa,CAACjB,IAAI;cAAA;eAAG,IAAE,iBArDvFkB,WAAA,GAsDoBtE,mBAAA,CAGO,QAHPuE,WAGO,GAFL5E,YAAA,CAA+D4D,mBAAA;cAApD1B,GAAG,EAAE1B,MAAA,CAAAqE,WAAW,CAACpB,IAAI,CAACqB,OAAO;cAAGlF,KAAK,EAAC;8CACjDS,mBAAA,CAAyC,cAAnC,GAAC,GAAAE,gBAAA,CAAGkD,IAAI,CAACsB,SAAS,UAAS,GAAC,gB,SAM1C3D,mBAAA,UAAa,EACiBqC,IAAI,CAACuB,QAAQ,IAAIvB,IAAI,CAACuB,QAAQ,CAAC3B,MAAM,I,cAAnEvD,mBAAA,CAyBM,OAzBNmF,WAyBM,I,kBAxBJnF,mBAAA,CAuBMyD,SAAA,QAvFtBC,WAAA,CAgEwDC,IAAI,CAACuB,QAAQ,EAhErE,UAgE+CE,KAAK;mCAApCpF,mBAAA,CAuBM;gBAvBDF,KAAK,EAAC,YAAY;gBAAiCC,GAAG,EAAEqF,KAAK,CAACjD;kBACjE5B,mBAAA,CAEM,OAFN8E,WAEM,GADJnF,YAAA,CAA2D4D,mBAAA;gBAAhD1B,GAAG,EAAE1B,MAAA,CAAAqD,YAAY,CAACqB,KAAK,CAACpB,OAAO;gBAAGC,GAAG,EAAC;kDAEnD1D,mBAAA,CAkBM,OAlBN+E,WAkBM,GAjBJ/E,mBAAA,CAIM,OAJNgF,WAIM,GAHJhF,mBAAA,CAA+D,QAA/DiF,WAA+D,EAAA/E,gBAAA,CAA/B2E,KAAK,CAACf,eAAe,kBACtBe,KAAK,CAACK,KAAK,IAAIL,KAAK,CAACK,KAAK,CAAClC,MAAM,I,cAAhEvD,mBAAA,CACU,QADV0F,WACU,EADwD,GAAC,GAAAjF,gBAAA,CAAG2E,KAAK,CAACK,KAAK,IAAIE,QAAQ,IAC3F,GAAC,mBAxEzBrE,mBAAA,e,GA0EoBf,mBAAA,CAGM,OAHNqF,WAGM,GAFQR,KAAK,CAACS,WAAW,IAAIT,KAAK,CAACU,QAAQ,KAAKnC,IAAI,CAACxB,EAAE,I,cAA3DnC,mBAAA,CAA8F,QA3EpH+F,WAAA,EA2EmF,IAAE,GAAAtF,gBAAA,CAAG2E,KAAK,CAACS,WAAW,IAAG,GAAC,mBA3E7GvE,mBAAA,gBAAA6B,gBAAA,CA2EoH,GAC9F,GAAA1C,gBAAA,CAAG2E,KAAK,CAACb,cAAc,iB,GAEzBhE,mBAAA,CAOM,OAPNyF,WAOM,GANJzF,mBAAA,CAAkE,QAAlE0F,WAAkE,EAAAxF,gBAAA,CAAtCC,MAAA,CAAAgE,UAAU,CAACU,KAAK,CAACT,UAAU,mBACvDpE,mBAAA,CAA2E;gBAArET,KAAK,EAAC,uBAAuB;gBAAEiD,OAAK,WAALA,OAAKA,CAAAE,MAAA;kBAAA,OAAEvC,MAAA,CAAAkE,aAAa,CAACQ,KAAK;gBAAA;iBAAG,IAAE,iBAhF1Fc,WAAA,GAiFsB3F,mBAAA,CAGO,QAHP4F,WAGO,GAFLjG,YAAA,CAAwE4D,mBAAA;gBAA7D1B,GAAG,EAAE1B,MAAA,CAAAqE,WAAW,CAACK,KAAK,CAACgB,eAAe;gBAAGtG,KAAK,EAAC;gDAC1DS,mBAAA,CAA0C,cAApC,GAAC,GAAAE,gBAAA,CAAG2E,KAAK,CAACH,SAAS,UAAS,GAAC,gB;gDAnF3D3D,mBAAA,e;6DA2FUtB,mBAAA,CAEM,OAFNqG,WAEM,EAF0B,UAEhC,G;;QA7FVjD,CAAA;UA+FQ7C,mBAAA,CAIM,OAJN+F,WAIM,GAHJpG,YAAA,CAE+BqG,wBAAA;QAFRC,WAAW,EAAE9F,MAAA,CAAA+F,MAAM;QAhGpD,wBAAAzD,MAAA,QAAAA,MAAA,gBAAAC,MAAA;UAAA,OAgG8CvC,MAAA,CAAA+F,MAAM,GAAAxD,MAAA;QAAA;QAAU,WAAS,EAAEvC,MAAA,CAAAgG,QAAQ;QAhGjF,qBAAA1D,MAAA,QAAAA,MAAA,gBAAAC,MAAA;UAAA,OAgGyEvC,MAAA,CAAAgG,QAAQ,GAAAzD,MAAA;QAAA;QAAG,YAAU,EAAE,sBAAsB;QAC1G0D,MAAM,EAAC,yCAAyC;QAAEC,YAAW,EAAElG,MAAA,CAAAmG,WAAW;QAAGC,eAAc,EAAEpG,MAAA,CAAAmG,WAAW;QACvGE,KAAK,EAAErG,MAAA,CAAAkC,MAAM;QAAEoE,UAAU,EAAV;;;IAlG5B5D,CAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
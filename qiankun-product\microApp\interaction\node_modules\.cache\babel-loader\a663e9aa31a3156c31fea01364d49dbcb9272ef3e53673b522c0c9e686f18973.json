{"ast": null, "code": "import { inject, computed } from 'vue';\nimport '../../../form/index.mjs';\nimport '../../../../utils/index.mjs';\nimport { checkboxGroupContextKey } from '../constants.mjs';\nimport { isUndefined } from '../../../../utils/types.mjs';\nimport { useFormDisabled } from '../../../form/src/hooks/use-form-common-props.mjs';\nvar useCheckboxDisabled = function useCheckboxDisabled(_ref) {\n  var model = _ref.model,\n    isChecked = _ref.isChecked;\n  var checkboxGroup = inject(checkboxGroupContextKey, void 0);\n  var isLimitDisabled = computed(function () {\n    var _a, _b;\n    var max = (_a = checkboxGroup == null ? void 0 : checkboxGroup.max) == null ? void 0 : _a.value;\n    var min = (_b = checkboxGroup == null ? void 0 : checkboxGroup.min) == null ? void 0 : _b.value;\n    return !isUndefined(max) && model.value.length >= max && !isChecked.value || !isUndefined(min) && model.value.length <= min && isChecked.value;\n  });\n  var isDisabled = useFormDisabled(computed(function () {\n    return (checkboxGroup == null ? void 0 : checkboxGroup.disabled.value) || isLimitDisabled.value;\n  }));\n  return {\n    isDisabled,\n    isLimitDisabled\n  };\n};\nexport { useCheckboxDisabled };", "map": {"version": 3, "names": ["useCheckboxDisabled", "_ref", "model", "isChecked", "checkboxGroup", "inject", "checkboxGroupContextKey", "isLimitDisabled", "computed", "_a", "_b", "max", "value", "min", "isUndefined", "length", "isDisabled", "useFormDisabled", "disabled"], "sources": ["../../../../../../../packages/components/checkbox/src/composables/use-checkbox-disabled.ts"], "sourcesContent": ["import { computed, inject } from 'vue'\nimport { useFormDisabled } from '@element-plus/components/form'\nimport { isUndefined } from '@element-plus/utils'\nimport { checkboxGroupContextKey } from '../constants'\n\nimport type { CheckboxModel, CheckboxStatus } from '../composables'\n\nexport const useCheckboxDisabled = ({\n  model,\n  isChecked,\n}: Pick<CheckboxModel, 'model'> & Pick<CheckboxStatus, 'isChecked'>) => {\n  const checkboxGroup = inject(checkboxGroupContextKey, undefined)\n\n  const isLimitDisabled = computed(() => {\n    const max = checkboxGroup?.max?.value\n    const min = checkboxGroup?.min?.value\n    return (\n      (!isUndefined(max) && model.value.length >= max && !isChecked.value) ||\n      (!isUndefined(min) && model.value.length <= min && isChecked.value)\n    )\n  })\n\n  const isDisabled = useFormDisabled(\n    computed(() => checkboxGroup?.disabled.value || isLimitDisabled.value)\n  )\n\n  return {\n    isDisabled,\n    isLimitDisabled,\n  }\n}\n\nexport type CheckboxDisabled = ReturnType<typeof useCheckboxDisabled>\n"], "mappings": ";;;;;;AAIY,IAACA,mBAAmB,GAAG,SAAtBA,mBAAmBA,CAAAC,IAAA,EAG1B;EAAA,IAFJC,KAAK,GAAAD,IAAA,CAALC,KAAK;IACLC,SAAS,GAAAF,IAAA,CAATE,SAAS;EAET,IAAMC,aAAa,GAAGC,MAAM,CAACC,uBAAuB,EAAE,KAAK,CAAC,CAAC;EAC7D,IAAMC,eAAe,GAAGC,QAAQ,CAAC,YAAM;IACrC,IAAIC,EAAE,EAAEC,EAAE;IACV,IAAMC,GAAG,GAAG,CAACF,EAAE,GAAGL,aAAa,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,aAAa,CAACO,GAAG,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGF,EAAE,CAACG,KAAK;IACjG,IAAMC,GAAG,GAAG,CAACH,EAAE,GAAGN,aAAa,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,aAAa,CAACS,GAAG,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGH,EAAE,CAACE,KAAK;IACjG,OAAO,CAACE,WAAW,CAACH,GAAG,CAAC,IAAIT,KAAK,CAACU,KAAK,CAACG,MAAM,IAAIJ,GAAG,IAAI,CAACR,SAAS,CAACS,KAAK,IAAI,CAACE,WAAW,CAACD,GAAG,CAAC,IAAIX,KAAK,CAACU,KAAK,CAACG,MAAM,IAAIF,GAAG,IAAIV,SAAS,CAACS,KAAK;EAClJ,CAAG,CAAC;EACF,IAAMI,UAAU,GAAGC,eAAe,CAACT,QAAQ,CAAC;IAAA,OAAM,CAACJ,aAAa,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,aAAa,CAACc,QAAQ,CAACN,KAAK,KAAKL,eAAe,CAACK,KAAK;EAAA,EAAC,CAAC;EAC5I,OAAO;IACLI,UAAU;IACVT;EACJ,CAAG;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "import baseIteratee from './_baseIteratee.js';\nimport baseSortedIndexBy from './_baseSortedIndexBy.js';\n\n/**\n * This method is like `_.sortedLastIndex` except that it accepts `iteratee`\n * which is invoked for `value` and each element of `array` to compute their\n * sort ranking. The iteratee is invoked with one argument: (value).\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category Array\n * @param {Array} array The sorted array to inspect.\n * @param {*} value The value to evaluate.\n * @param {Function} [iteratee=_.identity] The iteratee invoked per element.\n * @returns {number} Returns the index at which `value` should be inserted\n *  into `array`.\n * @example\n *\n * var objects = [{ 'x': 4 }, { 'x': 5 }];\n *\n * _.sortedLastIndexBy(objects, { 'x': 4 }, function(o) { return o.x; });\n * // => 1\n *\n * // The `_.property` iteratee shorthand.\n * _.sortedLastIndexBy(objects, { 'x': 4 }, 'x');\n * // => 1\n */\nfunction sortedLastIndexBy(array, value, iteratee) {\n  return baseSortedIndexBy(array, value, baseIteratee(iteratee, 2), true);\n}\nexport default sortedLastIndexBy;", "map": {"version": 3, "names": ["baseIteratee", "baseSortedIndexBy", "sortedLastIndexBy", "array", "value", "iteratee"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/node_modules/.pnpm/lodash-es@4.17.21/node_modules/lodash-es/sortedLastIndexBy.js"], "sourcesContent": ["import baseIteratee from './_baseIteratee.js';\nimport baseSortedIndexBy from './_baseSortedIndexBy.js';\n\n/**\n * This method is like `_.sortedLastIndex` except that it accepts `iteratee`\n * which is invoked for `value` and each element of `array` to compute their\n * sort ranking. The iteratee is invoked with one argument: (value).\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category Array\n * @param {Array} array The sorted array to inspect.\n * @param {*} value The value to evaluate.\n * @param {Function} [iteratee=_.identity] The iteratee invoked per element.\n * @returns {number} Returns the index at which `value` should be inserted\n *  into `array`.\n * @example\n *\n * var objects = [{ 'x': 4 }, { 'x': 5 }];\n *\n * _.sortedLastIndexBy(objects, { 'x': 4 }, function(o) { return o.x; });\n * // => 1\n *\n * // The `_.property` iteratee shorthand.\n * _.sortedLastIndexBy(objects, { 'x': 4 }, 'x');\n * // => 1\n */\nfunction sortedLastIndexBy(array, value, iteratee) {\n  return baseSortedIndexBy(array, value, baseIteratee(iteratee, 2), true);\n}\n\nexport default sortedLastIndexBy;\n"], "mappings": "AAAA,OAAOA,YAAY,MAAM,oBAAoB;AAC7C,OAAOC,iBAAiB,MAAM,yBAAyB;;AAEvD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,iBAAiBA,CAACC,KAAK,EAAEC,KAAK,EAAEC,QAAQ,EAAE;EACjD,OAAOJ,iBAAiB,CAACE,KAAK,EAAEC,KAAK,EAAEJ,YAAY,CAACK,QAAQ,EAAE,CAAC,CAAC,EAAE,IAAI,CAAC;AACzE;AAEA,eAAeH,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "import { createVNode, render } from 'vue';\nimport XylFilePreview from './xyl-file-preview.vue';\nexport default (function (_ref) {\n  var fileObj = _ref.fileObj;\n  // 点击关闭按钮，销毁组件\n  var closeCallback = function closeCallback() {\n    render(null, document.body);\n  };\n  // 创建 XylFilePreview 组件\n  var VNode = createVNode(XylFilePreview, {\n    fileObj,\n    closeCallback\n  });\n  render(VNode, document.body);\n});", "map": {"version": 3, "names": ["createVNode", "render", "XylFilePreview", "_ref", "fileObj", "closeCallback", "document", "body", "VNode"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/common/components/xyl-file-preview/index.js"], "sourcesContent": ["import { createVNode, render } from 'vue'\r\nimport XylFilePreview from './xyl-file-preview.vue'\r\n\r\nexport default ({ fileObj }) => {\r\n  // 点击关闭按钮，销毁组件\r\n  const closeCallback = () => {\r\n    render(null, document.body)\r\n  }\r\n  // 创建 XylFilePreview 组件\r\n  const VNode = createVNode(XylFilePreview, { fileObj, closeCallback })\r\n  render(VNode, document.body)\r\n}\r\n"], "mappings": "AAAA,SAASA,WAAW,EAAEC,MAAM,QAAQ,KAAK;AACzC,OAAOC,cAAc,MAAM,wBAAwB;AAEnD,gBAAe,UAAAC,IAAA,EAAiB;EAAA,IAAdC,OAAO,GAAAD,IAAA,CAAPC,OAAO;EACvB;EACA,IAAMC,aAAa,GAAG,SAAhBA,aAAaA,CAAA,EAAS;IAC1BJ,MAAM,CAAC,IAAI,EAAEK,QAAQ,CAACC,IAAI,CAAC;EAC7B,CAAC;EACD;EACA,IAAMC,KAAK,GAAGR,WAAW,CAACE,cAAc,EAAE;IAAEE,OAAO;IAAEC;EAAc,CAAC,CAAC;EACrEJ,MAAM,CAACO,KAAK,EAAEF,QAAQ,CAACC,IAAI,CAAC;AAC9B,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "function _slicedToArray(r, e) { return _arrayWithHoles(r) || _iterableToArrayLimit(r, e) || _unsupportedIterableToArray(r, e) || _nonIterableRest(); }\nfunction _nonIterableRest() { throw new TypeError(\"Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); }\nfunction _unsupportedIterableToArray(r, a) { if (r) { if (\"string\" == typeof r) return _arrayLikeToArray(r, a); var t = {}.toString.call(r).slice(8, -1); return \"Object\" === t && r.constructor && (t = r.constructor.name), \"Map\" === t || \"Set\" === t ? Array.from(r) : \"Arguments\" === t || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t) ? _arrayLikeToArray(r, a) : void 0; } }\nfunction _arrayLikeToArray(r, a) { (null == a || a > r.length) && (a = r.length); for (var e = 0, n = Array(a); e < a; e++) n[e] = r[e]; return n; }\nfunction _iterableToArrayLimit(r, l) { var t = null == r ? null : \"undefined\" != typeof Symbol && r[Symbol.iterator] || r[\"@@iterator\"]; if (null != t) { var e, n, i, u, a = [], f = !0, o = !1; try { if (i = (t = t.call(r)).next, 0 === l) { if (Object(t) !== t) return; f = !1; } else for (; !(f = (e = i.call(t)).done) && (a.push(e.value), a.length !== l); f = !0); } catch (r) { o = !0, n = r; } finally { try { if (!f && null != t.return && (u = t.return(), Object(u) !== u)) return; } finally { if (o) throw n; } } return a; } }\nfunction _arrayWithHoles(r) { if (Array.isArray(r)) return r; }\nimport dayjs from 'dayjs';\nimport '../../../utils/index.mjs';\nimport { isArray } from '@vue/shared';\nvar isValidRange = function isValidRange(range) {\n  if (!isArray(range)) return false;\n  var _range = _slicedToArray(range, 2),\n    left = _range[0],\n    right = _range[1];\n  return dayjs.isDayjs(left) && dayjs.isDayjs(right) && left.isSameOrBefore(right);\n};\nvar getDefaultValue = function getDefaultValue(defaultValue, _ref) {\n  var lang = _ref.lang,\n    unit = _ref.unit,\n    unlinkPanels = _ref.unlinkPanels;\n  var start;\n  if (isArray(defaultValue)) {\n    var _defaultValue$map = defaultValue.map(function (d) {\n        return dayjs(d).locale(lang);\n      }),\n      _defaultValue$map2 = _slicedToArray(_defaultValue$map, 2),\n      left = _defaultValue$map2[0],\n      right = _defaultValue$map2[1];\n    if (!unlinkPanels) {\n      right = left.add(1, unit);\n    }\n    return [left, right];\n  } else if (defaultValue) {\n    start = dayjs(defaultValue);\n  } else {\n    start = dayjs();\n  }\n  start = start.locale(lang);\n  return [start, start.add(1, unit)];\n};\nvar buildPickerTable = function buildPickerTable(dimension, rows, _ref2) {\n  var columnIndexOffset = _ref2.columnIndexOffset,\n    startDate = _ref2.startDate,\n    nextEndDate = _ref2.nextEndDate,\n    now = _ref2.now,\n    unit = _ref2.unit,\n    relativeDateGetter = _ref2.relativeDateGetter,\n    setCellMetadata = _ref2.setCellMetadata,\n    setRowMetadata = _ref2.setRowMetadata;\n  for (var rowIndex = 0; rowIndex < dimension.row; rowIndex++) {\n    var row = rows[rowIndex];\n    for (var columnIndex = 0; columnIndex < dimension.column; columnIndex++) {\n      var cell = row[columnIndex + columnIndexOffset];\n      if (!cell) {\n        cell = {\n          row: rowIndex,\n          column: columnIndex,\n          type: \"normal\",\n          inRange: false,\n          start: false,\n          end: false\n        };\n      }\n      var index = rowIndex * dimension.column + columnIndex;\n      var nextStartDate = relativeDateGetter(index);\n      cell.dayjs = nextStartDate;\n      cell.date = nextStartDate.toDate();\n      cell.timestamp = nextStartDate.valueOf();\n      cell.type = \"normal\";\n      cell.inRange = !!(startDate && nextStartDate.isSameOrAfter(startDate, unit) && nextEndDate && nextStartDate.isSameOrBefore(nextEndDate, unit)) || !!(startDate && nextStartDate.isSameOrBefore(startDate, unit) && nextEndDate && nextStartDate.isSameOrAfter(nextEndDate, unit));\n      if (startDate == null ? void 0 : startDate.isSameOrAfter(nextEndDate)) {\n        cell.start = !!nextEndDate && nextStartDate.isSame(nextEndDate, unit);\n        cell.end = startDate && nextStartDate.isSame(startDate, unit);\n      } else {\n        cell.start = !!startDate && nextStartDate.isSame(startDate, unit);\n        cell.end = !!nextEndDate && nextStartDate.isSame(nextEndDate, unit);\n      }\n      var isToday = nextStartDate.isSame(now, unit);\n      if (isToday) {\n        cell.type = \"today\";\n      }\n      setCellMetadata == null ? void 0 : setCellMetadata(cell, {\n        rowIndex,\n        columnIndex\n      });\n      row[columnIndex + columnIndexOffset] = cell;\n    }\n    setRowMetadata == null ? void 0 : setRowMetadata(row);\n  }\n};\nexport { buildPickerTable, getDefaultValue, isValidRange };", "map": {"version": 3, "names": ["isValidRange", "range", "isArray", "_range", "_slicedToArray", "left", "right", "dayjs", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "isSameOrBefore", "getDefaultValue", "defaultValue", "_ref", "lang", "unit", "unlinkPanels", "start", "_defaultValue$map", "map", "d", "locale", "_defaultValue$map2", "add", "buildPickerTable", "dimension", "rows", "_ref2", "columnIndexOffset", "startDate", "nextEndDate", "now", "relativeDateGetter", "setCellMetadata", "setRowMetadata", "rowIndex", "row", "columnIndex", "column", "cell", "type", "inRange", "end", "index", "nextStartDate", "date", "toDate", "timestamp", "valueOf", "isSameOrAfter", "isSame", "isToday"], "sources": ["../../../../../../packages/components/date-picker/src/utils.ts"], "sourcesContent": ["import dayjs from 'dayjs'\nimport { isArray } from '@element-plus/utils'\n\nimport type { Dayjs } from 'dayjs'\nimport type { DateCell } from './date-picker.type'\n\ntype DayRange = [Dayjs | undefined, Dayjs | undefined]\n\nexport const isValidRange = (range: DayRange): boolean => {\n  if (!isArray(range)) return false\n\n  const [left, right] = range\n\n  return (\n    dayjs.isDayjs(left) && dayjs.isDayjs(right) && left.isSameOrBefore(right)\n  )\n}\n\ntype GetDefaultValueParams = {\n  lang: string\n  unit: 'month' | 'year'\n  unlinkPanels: boolean\n}\n\nexport type DefaultValue = [Date, Date] | Date | undefined\n\nexport const getDefaultValue = (\n  defaultValue: DefaultValue,\n  { lang, unit, unlinkPanels }: GetDefaultValueParams\n) => {\n  let start: Dayjs\n\n  if (isArray(defaultValue)) {\n    let [left, right] = defaultValue.map((d) => dayjs(d).locale(lang))\n    if (!unlinkPanels) {\n      right = left.add(1, unit)\n    }\n    return [left, right]\n  } else if (defaultValue) {\n    start = dayjs(defaultValue)\n  } else {\n    start = dayjs()\n  }\n  start = start.locale(lang)\n  return [start, start.add(1, unit)]\n}\n\ntype Dimension = {\n  row: number\n  column: number\n}\n\ntype BuildPickerTableMetadata = {\n  startDate?: Dayjs | null\n  unit: 'month' | 'day'\n  columnIndexOffset: number\n  now: Dayjs\n  nextEndDate: Dayjs | null\n  relativeDateGetter: (index: number) => Dayjs\n  setCellMetadata?: (\n    cell: DateCell,\n    dimension: { rowIndex: number; columnIndex: number }\n  ) => void\n  setRowMetadata?: (row: DateCell[]) => void\n}\n\nexport const buildPickerTable = (\n  dimension: Dimension,\n  rows: DateCell[][],\n  {\n    columnIndexOffset,\n    startDate,\n    nextEndDate,\n    now,\n    unit,\n    relativeDateGetter,\n    setCellMetadata,\n    setRowMetadata,\n  }: BuildPickerTableMetadata\n) => {\n  for (let rowIndex = 0; rowIndex < dimension.row; rowIndex++) {\n    const row = rows[rowIndex]\n    for (let columnIndex = 0; columnIndex < dimension.column; columnIndex++) {\n      let cell = row[columnIndex + columnIndexOffset]\n      if (!cell) {\n        cell = {\n          row: rowIndex,\n          column: columnIndex,\n          type: 'normal',\n          inRange: false,\n          start: false,\n          end: false,\n        }\n      }\n      const index = rowIndex * dimension.column + columnIndex\n      const nextStartDate = relativeDateGetter(index)\n      cell.dayjs = nextStartDate\n      cell.date = nextStartDate.toDate()\n      cell.timestamp = nextStartDate.valueOf()\n      cell.type = 'normal'\n\n      cell.inRange =\n        !!(\n          startDate &&\n          nextStartDate.isSameOrAfter(startDate, unit) &&\n          nextEndDate &&\n          nextStartDate.isSameOrBefore(nextEndDate, unit)\n        ) ||\n        !!(\n          startDate &&\n          nextStartDate.isSameOrBefore(startDate, unit) &&\n          nextEndDate &&\n          nextStartDate.isSameOrAfter(nextEndDate, unit)\n        )\n\n      if (startDate?.isSameOrAfter(nextEndDate)) {\n        cell.start = !!nextEndDate && nextStartDate.isSame(nextEndDate, unit)\n        cell.end = startDate && nextStartDate.isSame(startDate, unit)\n      } else {\n        cell.start = !!startDate && nextStartDate.isSame(startDate, unit)\n        cell.end = !!nextEndDate && nextStartDate.isSame(nextEndDate, unit)\n      }\n\n      const isToday = nextStartDate.isSame(now, unit)\n\n      if (isToday) {\n        cell.type = 'today'\n      }\n      setCellMetadata?.(cell, { rowIndex, columnIndex })\n      row[columnIndex + columnIndexOffset] = cell\n    }\n    setRowMetadata?.(row)\n  }\n}\n"], "mappings": ";;;;;;;;;AAEY,IAACA,YAAY,GAAG,SAAfA,YAAYA,CAAIC,KAAK,EAAK;EACrC,IAAI,CAACC,OAAO,CAACD,KAAK,CAAC,EACjB,OAAO,KAAK;EACd,IAAAE,MAAA,GAAAC,cAAA,CAAsBH,KAAK;IAApBI,IAAI,GAAAF,MAAA;IAAEG,KAAK,GAAAH,MAAA;EAClB,OAAOI,KAAK,CAACC,OAAO,CAACH,IAAI,CAAC,IAAIE,KAAK,CAACC,OAAO,CAACF,KAAK,CAAC,IAAID,IAAI,CAACI,cAAc,CAACH,KAAK,CAAC;AAClF;AACY,IAACI,eAAe,GAAG,SAAlBA,eAAeA,CAAIC,YAAY,EAAAC,IAAA,EAAmC;EAAA,IAA/BC,IAAI,GAAAD,IAAA,CAAJC,IAAI;IAAEC,IAAI,GAAAF,IAAA,CAAJE,IAAI;IAAEC,YAAY,GAAAH,IAAA,CAAZG,YAAY;EACtE,IAAIC,KAAK;EACT,IAAId,OAAO,CAACS,YAAY,CAAC,EAAE;IACzB,IAAAM,iBAAA,GAAoBN,YAAY,CAACO,GAAG,CAAC,UAACC,CAAC;QAAA,OAAKZ,KAAK,CAACY,CAAC,CAAC,CAACC,MAAM,CAACP,IAAI,CAAC;MAAA,EAAC;MAAAQ,kBAAA,GAAAjB,cAAA,CAAAa,iBAAA;MAA7DZ,IAAI,GAAAgB,kBAAA;MAAEf,KAAK,GAAAe,kBAAA;IAChB,IAAI,CAACN,YAAY,EAAE;MACjBT,KAAK,GAAGD,IAAI,CAACiB,GAAG,CAAC,CAAC,EAAER,IAAI,CAAC;IAC/B;IACI,OAAO,CAACT,IAAI,EAAEC,KAAK,CAAC;EACxB,CAAG,MAAM,IAAIK,YAAY,EAAE;IACvBK,KAAK,GAAGT,KAAK,CAACI,YAAY,CAAC;EAC/B,CAAG,MAAM;IACLK,KAAK,GAAGT,KAAK,EAAE;EACnB;EACES,KAAK,GAAGA,KAAK,CAACI,MAAM,CAACP,IAAI,CAAC;EAC1B,OAAO,CAACG,KAAK,EAAEA,KAAK,CAACM,GAAG,CAAC,CAAC,EAAER,IAAI,CAAC,CAAC;AACpC;AACY,IAACS,gBAAgB,GAAG,SAAnBA,gBAAgBA,CAAIC,SAAS,EAAEC,IAAI,EAAAC,KAAA,EAS1C;EAAA,IARJC,iBAAiB,GAAAD,KAAA,CAAjBC,iBAAiB;IACjBC,SAAS,GAAAF,KAAA,CAATE,SAAS;IACTC,WAAW,GAAAH,KAAA,CAAXG,WAAW;IACXC,GAAG,GAAAJ,KAAA,CAAHI,GAAG;IACHhB,IAAI,GAAAY,KAAA,CAAJZ,IAAI;IACJiB,kBAAkB,GAAAL,KAAA,CAAlBK,kBAAkB;IAClBC,eAAe,GAAAN,KAAA,CAAfM,eAAe;IACfC,cAAc,GAAAP,KAAA,CAAdO,cAAc;EAEd,KAAK,IAAIC,QAAQ,GAAG,CAAC,EAAEA,QAAQ,GAAGV,SAAS,CAACW,GAAG,EAAED,QAAQ,EAAE,EAAE;IAC3D,IAAMC,GAAG,GAAGV,IAAI,CAACS,QAAQ,CAAC;IAC1B,KAAK,IAAIE,WAAW,GAAG,CAAC,EAAEA,WAAW,GAAGZ,SAAS,CAACa,MAAM,EAAED,WAAW,EAAE,EAAE;MACvE,IAAIE,IAAI,GAAGH,GAAG,CAACC,WAAW,GAAGT,iBAAiB,CAAC;MAC/C,IAAI,CAACW,IAAI,EAAE;QACTA,IAAI,GAAG;UACLH,GAAG,EAAED,QAAQ;UACbG,MAAM,EAAED,WAAW;UACnBG,IAAI,EAAE,QAAQ;UACdC,OAAO,EAAE,KAAK;UACdxB,KAAK,EAAE,KAAK;UACZyB,GAAG,EAAE;QACf,CAAS;MACT;MACM,IAAMC,KAAK,GAAGR,QAAQ,GAAGV,SAAS,CAACa,MAAM,GAAGD,WAAW;MACvD,IAAMO,aAAa,GAAGZ,kBAAkB,CAACW,KAAK,CAAC;MAC/CJ,IAAI,CAAC/B,KAAK,GAAGoC,aAAa;MAC1BL,IAAI,CAACM,IAAI,GAAGD,aAAa,CAACE,MAAM,EAAE;MAClCP,IAAI,CAACQ,SAAS,GAAGH,aAAa,CAACI,OAAO,EAAE;MACxCT,IAAI,CAACC,IAAI,GAAG,QAAQ;MACpBD,IAAI,CAACE,OAAO,GAAG,CAAC,EAAEZ,SAAS,IAAIe,aAAa,CAACK,aAAa,CAACpB,SAAS,EAAEd,IAAI,CAAC,IAAIe,WAAW,IAAIc,aAAa,CAAClC,cAAc,CAACoB,WAAW,EAAEf,IAAI,CAAC,CAAC,IAAI,CAAC,EAAEc,SAAS,IAAIe,aAAa,CAAClC,cAAc,CAACmB,SAAS,EAAEd,IAAI,CAAC,IAAIe,WAAW,IAAIc,aAAa,CAACK,aAAa,CAACnB,WAAW,EAAEf,IAAI,CAAC,CAAC;MACjR,IAAIc,SAAS,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,SAAS,CAACoB,aAAa,CAACnB,WAAW,CAAC,EAAE;QACrES,IAAI,CAACtB,KAAK,GAAG,CAAC,CAACa,WAAW,IAAIc,aAAa,CAACM,MAAM,CAACpB,WAAW,EAAEf,IAAI,CAAC;QACrEwB,IAAI,CAACG,GAAG,GAAGb,SAAS,IAAIe,aAAa,CAACM,MAAM,CAACrB,SAAS,EAAEd,IAAI,CAAC;MACrE,CAAO,MAAM;QACLwB,IAAI,CAACtB,KAAK,GAAG,CAAC,CAACY,SAAS,IAAIe,aAAa,CAACM,MAAM,CAACrB,SAAS,EAAEd,IAAI,CAAC;QACjEwB,IAAI,CAACG,GAAG,GAAG,CAAC,CAACZ,WAAW,IAAIc,aAAa,CAACM,MAAM,CAACpB,WAAW,EAAEf,IAAI,CAAC;MAC3E;MACM,IAAMoC,OAAO,GAAGP,aAAa,CAACM,MAAM,CAACnB,GAAG,EAAEhB,IAAI,CAAC;MAC/C,IAAIoC,OAAO,EAAE;QACXZ,IAAI,CAACC,IAAI,GAAG,OAAO;MAC3B;MACMP,eAAe,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,eAAe,CAACM,IAAI,EAAE;QAAEJ,QAAQ;QAAEE;MAAW,CAAE,CAAC;MACnFD,GAAG,CAACC,WAAW,GAAGT,iBAAiB,CAAC,GAAGW,IAAI;IACjD;IACIL,cAAc,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,cAAc,CAACE,GAAG,CAAC;EACzD;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
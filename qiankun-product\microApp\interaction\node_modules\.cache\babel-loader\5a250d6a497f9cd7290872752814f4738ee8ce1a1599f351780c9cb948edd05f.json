{"ast": null, "code": "import { createCommentVNode as _createCommentVNode, createElementVNode as _createElementVNode, toDisplayString as _toDisplayString, openBlock as _openBlock, createElementBlock as _createElementBlock, createBlock as _createBlock } from \"vue\";\nvar _hoisted_1 = {\n  class: \"LiveShare\"\n};\nvar _hoisted_2 = {\n  key: 0,\n  class: \"debug-info\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createCommentVNode(\" 调试信息 \"), !$setup.liveId ? (_openBlock(), _createElementBlock(\"div\", _hoisted_2, [_cache[0] || (_cache[0] = _createElementVNode(\"p\", null, \"正在加载直播信息...\", -1 /* HOISTED */)), _createElementVNode(\"p\", null, \"当前路由: \" + _toDisplayString(_ctx.$route.path), 1 /* TEXT */), _createElementVNode(\"p\", null, \"查询参数: \" + _toDisplayString(JSON.stringify(_ctx.$route.query)), 1 /* TEXT */), _createElementVNode(\"p\", null, \"路径参数: \" + _toDisplayString(JSON.stringify(_ctx.$route.params)), 1 /* TEXT */)])) : _createCommentVNode(\"v-if\", true), _createCommentVNode(\" 直播详情组件，只有当有ID时才显示 \"), $setup.liveId ? (_openBlock(), _createBlock($setup[\"LiveBroadcastDetails\"], {\n    key: 1,\n    \"model-value\": true,\n    id: $setup.liveId,\n    \"is-share\": _ctx.isShare,\n    onCallback: $setup.handleCallback,\n    \"onUpdate:modelValue\": $setup.handleModelValueUpdate\n  }, null, 8 /* PROPS */, [\"id\", \"is-share\"])) : _createCommentVNode(\"v-if\", true)]);\n}", "map": {"version": 3, "names": ["class", "key", "_createElementBlock", "_hoisted_1", "_createCommentVNode", "$setup", "liveId", "_hoisted_2", "_createElementVNode", "_toDisplayString", "_ctx", "$route", "path", "JSON", "stringify", "query", "params", "_createBlock", "id", "isShare", "onCallback", "handleCallback", "handleModelValueUpdate"], "sources": ["D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\interaction\\src\\views\\LiveManagement\\LiveShare.vue"], "sourcesContent": ["<template>\n  <div class=\"LiveShare\">\n    <!-- 调试信息 -->\n    <div v-if=\"!liveId\" class=\"debug-info\">\n      <p>正在加载直播信息...</p>\n      <p>当前路由: {{ $route.path }}</p>\n      <p>查询参数: {{ JSON.stringify($route.query) }}</p>\n      <p>路径参数: {{ JSON.stringify($route.params) }}</p>\n    </div>\n\n    <!-- 直播详情组件，只有当有ID时才显示 -->\n    <LiveBroadcastDetails v-if=\"liveId\" :model-value=\"true\" :id=\"liveId\" :is-share=\"isShare\" @callback=\"handleCallback\"\n      @update:modelValue=\"handleModelValueUpdate\" />\n  </div>\n</template>\n\n<script>\nexport default { name: 'LiveShare' }\n</script>\n\n<script setup>\nimport { ref, onMounted } from 'vue'\nimport { useRoute } from 'vue-router'\nimport { ElMessage } from 'element-plus'\nimport LiveBroadcastDetails from './LiveBroadcastDetails.vue'\n\nconst route = useRoute()\n// const router = useRouter()\nconst liveId = ref('')\n\nonMounted(() => {\n  // 从查询参数获取直播ID (因为现在使用 ?id= 格式)\n  liveId.value = route.query.id || route.params.id\n\n  console.log('LiveShare页面加载，直播ID:', liveId.value)\n  console.log('路由参数:', route.params)\n  console.log('查询参数:', route.query)\n\n  if (!liveId.value) {\n    ElMessage.error('直播ID不能为空')\n    console.error('未找到直播ID，路由信息:', route)\n    return\n  }\n})\n\n// 处理关闭回调 - 分享页面关闭时跳转到首页或显示提示\nconst handleCallback = () => {\n  // 可以根据需要跳转到首页或其他页面\n  ElMessage.info('感谢观看，您可以通过浏览器返回按钮离开此页面')\n}\n\n// 处理模型值更新 - 分享页面允许关闭\nconst handleModelValueUpdate = (value) => {\n  if (!value) {\n    // 用户点击关闭按钮时的处理\n    handleCallback()\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.LiveShare {\n  width: 100%;\n  height: 100vh;\n  overflow: hidden;\n\n  .debug-info {\n    padding: 20px;\n    background: #f5f5f5;\n    border: 1px solid #ddd;\n    margin: 20px;\n    border-radius: 8px;\n\n    p {\n      margin: 8px 0;\n      font-size: 14px;\n      color: #666;\n    }\n  }\n}\n</style>\n"], "mappings": ";;EACOA,KAAK,EAAC;AAAW;;EADxBC,GAAA;EAGwBD,KAAK,EAAC;;;uBAF5BE,mBAAA,CAYM,OAZNC,UAYM,GAXJC,mBAAA,UAAa,E,CACDC,MAAA,CAAAC,MAAM,I,cAAlBJ,mBAAA,CAKM,OALNK,UAKM,G,0BAJJC,mBAAA,CAAkB,WAAf,aAAW,sBACdA,mBAAA,CAA8B,WAA3B,QAAM,GAAAC,gBAAA,CAAGC,IAAA,CAAAC,MAAM,CAACC,IAAI,kBACvBJ,mBAAA,CAA+C,WAA5C,QAAM,GAAAC,gBAAA,CAAGI,IAAI,CAACC,SAAS,CAACJ,IAAA,CAAAC,MAAM,CAACI,KAAK,mBACvCP,mBAAA,CAAgD,WAA7C,QAAM,GAAAC,gBAAA,CAAGI,IAAI,CAACC,SAAS,CAACJ,IAAA,CAAAC,MAAM,CAACK,MAAM,kB,KAP9CZ,mBAAA,gBAUIA,mBAAA,uBAA0B,EACEC,MAAA,CAAAC,MAAM,I,cAAlCW,YAAA,CACgDZ,MAAA;IAZpDJ,GAAA;IAWyC,aAAW,EAAE,IAAI;IAAGiB,EAAE,EAAEb,MAAA,CAAAC,MAAM;IAAG,UAAQ,EAAEI,IAAA,CAAAS,OAAO;IAAGC,UAAQ,EAAEf,MAAA,CAAAgB,cAAc;IAC/G,qBAAiB,EAAEhB,MAAA,CAAAiB;iDAZ1BlB,mBAAA,e", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
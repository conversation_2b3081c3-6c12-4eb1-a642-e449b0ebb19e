{"ast": null, "code": "import baseIteratee from './_baseIteratee.js';\nimport baseWhile from './_baseWhile.js';\n\n/**\n * Creates a slice of `array` with elements taken from the beginning. Elements\n * are taken until `predicate` returns falsey. The predicate is invoked with\n * three arguments: (value, index, array).\n *\n * @static\n * @memberOf _\n * @since 3.0.0\n * @category Array\n * @param {Array} array The array to query.\n * @param {Function} [predicate=_.identity] The function invoked per iteration.\n * @returns {Array} Returns the slice of `array`.\n * @example\n *\n * var users = [\n *   { 'user': 'barney',  'active': false },\n *   { 'user': 'fred',    'active': false },\n *   { 'user': 'pebbles', 'active': true }\n * ];\n *\n * _.takeWhile(users, function(o) { return !o.active; });\n * // => objects for ['barney', 'fred']\n *\n * // The `_.matches` iteratee shorthand.\n * _.takeWhile(users, { 'user': 'barney', 'active': false });\n * // => objects for ['barney']\n *\n * // The `_.matchesProperty` iteratee shorthand.\n * _.takeWhile(users, ['active', false]);\n * // => objects for ['barney', 'fred']\n *\n * // The `_.property` iteratee shorthand.\n * _.takeWhile(users, 'active');\n * // => []\n */\nfunction takeWhile(array, predicate) {\n  return array && array.length ? baseWhile(array, baseIteratee(predicate, 3)) : [];\n}\nexport default takeWhile;", "map": {"version": 3, "names": ["baseIteratee", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "array", "predicate", "length"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/node_modules/.pnpm/lodash-es@4.17.21/node_modules/lodash-es/takeWhile.js"], "sourcesContent": ["import baseIteratee from './_baseIteratee.js';\nimport baseWhile from './_baseWhile.js';\n\n/**\n * Creates a slice of `array` with elements taken from the beginning. Elements\n * are taken until `predicate` returns falsey. The predicate is invoked with\n * three arguments: (value, index, array).\n *\n * @static\n * @memberOf _\n * @since 3.0.0\n * @category Array\n * @param {Array} array The array to query.\n * @param {Function} [predicate=_.identity] The function invoked per iteration.\n * @returns {Array} Returns the slice of `array`.\n * @example\n *\n * var users = [\n *   { 'user': 'barney',  'active': false },\n *   { 'user': 'fred',    'active': false },\n *   { 'user': 'pebbles', 'active': true }\n * ];\n *\n * _.takeWhile(users, function(o) { return !o.active; });\n * // => objects for ['barney', 'fred']\n *\n * // The `_.matches` iteratee shorthand.\n * _.takeWhile(users, { 'user': 'barney', 'active': false });\n * // => objects for ['barney']\n *\n * // The `_.matchesProperty` iteratee shorthand.\n * _.takeWhile(users, ['active', false]);\n * // => objects for ['barney', 'fred']\n *\n * // The `_.property` iteratee shorthand.\n * _.takeWhile(users, 'active');\n * // => []\n */\nfunction takeWhile(array, predicate) {\n  return (array && array.length)\n    ? baseWhile(array, baseIteratee(predicate, 3))\n    : [];\n}\n\nexport default takeWhile;\n"], "mappings": "AAAA,OAAOA,YAAY,MAAM,oBAAoB;AAC7C,OAAOC,SAAS,MAAM,iBAAiB;;AAEvC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,SAASA,CAACC,KAAK,EAAEC,SAAS,EAAE;EACnC,OAAQD,KAAK,IAAIA,KAAK,CAACE,MAAM,GACzBJ,SAAS,CAACE,KAAK,EAAEH,YAAY,CAACI,SAAS,EAAE,CAAC,CAAC,CAAC,GAC5C,EAAE;AACR;AAEA,eAAeF,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
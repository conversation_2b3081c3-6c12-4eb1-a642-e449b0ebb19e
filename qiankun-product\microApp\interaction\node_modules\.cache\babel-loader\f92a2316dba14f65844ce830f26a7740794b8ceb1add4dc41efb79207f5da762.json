{"ast": null, "code": "function _regeneratorRuntime() { \"use strict\"; /*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */ _regeneratorRuntime = function _regeneratorRuntime() { return e; }; var t, e = {}, r = Object.prototype, n = r.hasOwnProperty, o = Object.defineProperty || function (t, e, r) { t[e] = r.value; }, i = \"function\" == typeof Symbol ? Symbol : {}, a = i.iterator || \"@@iterator\", c = i.asyncIterator || \"@@asyncIterator\", u = i.toStringTag || \"@@toStringTag\"; function define(t, e, r) { return Object.defineProperty(t, e, { value: r, enumerable: !0, configurable: !0, writable: !0 }), t[e]; } try { define({}, \"\"); } catch (t) { define = function define(t, e, r) { return t[e] = r; }; } function wrap(t, e, r, n) { var i = e && e.prototype instanceof Generator ? e : Generator, a = Object.create(i.prototype), c = new Context(n || []); return o(a, \"_invoke\", { value: makeInvokeMethod(t, r, c) }), a; } function tryCatch(t, e, r) { try { return { type: \"normal\", arg: t.call(e, r) }; } catch (t) { return { type: \"throw\", arg: t }; } } e.wrap = wrap; var h = \"suspendedStart\", l = \"suspendedYield\", f = \"executing\", s = \"completed\", y = {}; function Generator() {} function GeneratorFunction() {} function GeneratorFunctionPrototype() {} var p = {}; define(p, a, function () { return this; }); var d = Object.getPrototypeOf, v = d && d(d(values([]))); v && v !== r && n.call(v, a) && (p = v); var g = GeneratorFunctionPrototype.prototype = Generator.prototype = Object.create(p); function defineIteratorMethods(t) { [\"next\", \"throw\", \"return\"].forEach(function (e) { define(t, e, function (t) { return this._invoke(e, t); }); }); } function AsyncIterator(t, e) { function invoke(r, o, i, a) { var c = tryCatch(t[r], t, o); if (\"throw\" !== c.type) { var u = c.arg, h = u.value; return h && \"object\" == typeof h && n.call(h, \"__await\") ? e.resolve(h.__await).then(function (t) { invoke(\"next\", t, i, a); }, function (t) { invoke(\"throw\", t, i, a); }) : e.resolve(h).then(function (t) { u.value = t, i(u); }, function (t) { return invoke(\"throw\", t, i, a); }); } a(c.arg); } var r; o(this, \"_invoke\", { value: function value(t, n) { function callInvokeWithMethodAndArg() { return new e(function (e, r) { invoke(t, n, e, r); }); } return r = r ? r.then(callInvokeWithMethodAndArg, callInvokeWithMethodAndArg) : callInvokeWithMethodAndArg(); } }); } function makeInvokeMethod(e, r, n) { var o = h; return function (i, a) { if (o === f) throw Error(\"Generator is already running\"); if (o === s) { if (\"throw\" === i) throw a; return { value: t, done: !0 }; } for (n.method = i, n.arg = a;;) { var c = n.delegate; if (c) { var u = maybeInvokeDelegate(c, n); if (u) { if (u === y) continue; return u; } } if (\"next\" === n.method) n.sent = n._sent = n.arg;else if (\"throw\" === n.method) { if (o === h) throw o = s, n.arg; n.dispatchException(n.arg); } else \"return\" === n.method && n.abrupt(\"return\", n.arg); o = f; var p = tryCatch(e, r, n); if (\"normal\" === p.type) { if (o = n.done ? s : l, p.arg === y) continue; return { value: p.arg, done: n.done }; } \"throw\" === p.type && (o = s, n.method = \"throw\", n.arg = p.arg); } }; } function maybeInvokeDelegate(e, r) { var n = r.method, o = e.iterator[n]; if (o === t) return r.delegate = null, \"throw\" === n && e.iterator.return && (r.method = \"return\", r.arg = t, maybeInvokeDelegate(e, r), \"throw\" === r.method) || \"return\" !== n && (r.method = \"throw\", r.arg = new TypeError(\"The iterator does not provide a '\" + n + \"' method\")), y; var i = tryCatch(o, e.iterator, r.arg); if (\"throw\" === i.type) return r.method = \"throw\", r.arg = i.arg, r.delegate = null, y; var a = i.arg; return a ? a.done ? (r[e.resultName] = a.value, r.next = e.nextLoc, \"return\" !== r.method && (r.method = \"next\", r.arg = t), r.delegate = null, y) : a : (r.method = \"throw\", r.arg = new TypeError(\"iterator result is not an object\"), r.delegate = null, y); } function pushTryEntry(t) { var e = { tryLoc: t[0] }; 1 in t && (e.catchLoc = t[1]), 2 in t && (e.finallyLoc = t[2], e.afterLoc = t[3]), this.tryEntries.push(e); } function resetTryEntry(t) { var e = t.completion || {}; e.type = \"normal\", delete e.arg, t.completion = e; } function Context(t) { this.tryEntries = [{ tryLoc: \"root\" }], t.forEach(pushTryEntry, this), this.reset(!0); } function values(e) { if (e || \"\" === e) { var r = e[a]; if (r) return r.call(e); if (\"function\" == typeof e.next) return e; if (!isNaN(e.length)) { var o = -1, i = function next() { for (; ++o < e.length;) if (n.call(e, o)) return next.value = e[o], next.done = !1, next; return next.value = t, next.done = !0, next; }; return i.next = i; } } throw new TypeError(typeof e + \" is not iterable\"); } return GeneratorFunction.prototype = GeneratorFunctionPrototype, o(g, \"constructor\", { value: GeneratorFunctionPrototype, configurable: !0 }), o(GeneratorFunctionPrototype, \"constructor\", { value: GeneratorFunction, configurable: !0 }), GeneratorFunction.displayName = define(GeneratorFunctionPrototype, u, \"GeneratorFunction\"), e.isGeneratorFunction = function (t) { var e = \"function\" == typeof t && t.constructor; return !!e && (e === GeneratorFunction || \"GeneratorFunction\" === (e.displayName || e.name)); }, e.mark = function (t) { return Object.setPrototypeOf ? Object.setPrototypeOf(t, GeneratorFunctionPrototype) : (t.__proto__ = GeneratorFunctionPrototype, define(t, u, \"GeneratorFunction\")), t.prototype = Object.create(g), t; }, e.awrap = function (t) { return { __await: t }; }, defineIteratorMethods(AsyncIterator.prototype), define(AsyncIterator.prototype, c, function () { return this; }), e.AsyncIterator = AsyncIterator, e.async = function (t, r, n, o, i) { void 0 === i && (i = Promise); var a = new AsyncIterator(wrap(t, r, n, o), i); return e.isGeneratorFunction(r) ? a : a.next().then(function (t) { return t.done ? t.value : a.next(); }); }, defineIteratorMethods(g), define(g, u, \"Generator\"), define(g, a, function () { return this; }), define(g, \"toString\", function () { return \"[object Generator]\"; }), e.keys = function (t) { var e = Object(t), r = []; for (var n in e) r.push(n); return r.reverse(), function next() { for (; r.length;) { var t = r.pop(); if (t in e) return next.value = t, next.done = !1, next; } return next.done = !0, next; }; }, e.values = values, Context.prototype = { constructor: Context, reset: function reset(e) { if (this.prev = 0, this.next = 0, this.sent = this._sent = t, this.done = !1, this.delegate = null, this.method = \"next\", this.arg = t, this.tryEntries.forEach(resetTryEntry), !e) for (var r in this) \"t\" === r.charAt(0) && n.call(this, r) && !isNaN(+r.slice(1)) && (this[r] = t); }, stop: function stop() { this.done = !0; var t = this.tryEntries[0].completion; if (\"throw\" === t.type) throw t.arg; return this.rval; }, dispatchException: function dispatchException(e) { if (this.done) throw e; var r = this; function handle(n, o) { return a.type = \"throw\", a.arg = e, r.next = n, o && (r.method = \"next\", r.arg = t), !!o; } for (var o = this.tryEntries.length - 1; o >= 0; --o) { var i = this.tryEntries[o], a = i.completion; if (\"root\" === i.tryLoc) return handle(\"end\"); if (i.tryLoc <= this.prev) { var c = n.call(i, \"catchLoc\"), u = n.call(i, \"finallyLoc\"); if (c && u) { if (this.prev < i.catchLoc) return handle(i.catchLoc, !0); if (this.prev < i.finallyLoc) return handle(i.finallyLoc); } else if (c) { if (this.prev < i.catchLoc) return handle(i.catchLoc, !0); } else { if (!u) throw Error(\"try statement without catch or finally\"); if (this.prev < i.finallyLoc) return handle(i.finallyLoc); } } } }, abrupt: function abrupt(t, e) { for (var r = this.tryEntries.length - 1; r >= 0; --r) { var o = this.tryEntries[r]; if (o.tryLoc <= this.prev && n.call(o, \"finallyLoc\") && this.prev < o.finallyLoc) { var i = o; break; } } i && (\"break\" === t || \"continue\" === t) && i.tryLoc <= e && e <= i.finallyLoc && (i = null); var a = i ? i.completion : {}; return a.type = t, a.arg = e, i ? (this.method = \"next\", this.next = i.finallyLoc, y) : this.complete(a); }, complete: function complete(t, e) { if (\"throw\" === t.type) throw t.arg; return \"break\" === t.type || \"continue\" === t.type ? this.next = t.arg : \"return\" === t.type ? (this.rval = this.arg = t.arg, this.method = \"return\", this.next = \"end\") : \"normal\" === t.type && e && (this.next = e), y; }, finish: function finish(t) { for (var e = this.tryEntries.length - 1; e >= 0; --e) { var r = this.tryEntries[e]; if (r.finallyLoc === t) return this.complete(r.completion, r.afterLoc), resetTryEntry(r), y; } }, catch: function _catch(t) { for (var e = this.tryEntries.length - 1; e >= 0; --e) { var r = this.tryEntries[e]; if (r.tryLoc === t) { var n = r.completion; if (\"throw\" === n.type) { var o = n.arg; resetTryEntry(r); } return o; } } throw Error(\"illegal catch attempt\"); }, delegateYield: function delegateYield(e, r, n) { return this.delegate = { iterator: values(e), resultName: r, nextLoc: n }, \"next\" === this.method && (this.arg = t), y; } }, e; }\nfunction asyncGeneratorStep(n, t, e, r, o, a, c) { try { var i = n[a](c), u = i.value; } catch (n) { return void e(n); } i.done ? t(u) : Promise.resolve(u).then(r, o); }\nfunction _asyncToGenerator(n) { return function () { var t = this, e = arguments; return new Promise(function (r, o) { var a = n.apply(t, e); function _next(n) { asyncGeneratorStep(a, r, o, _next, _throw, \"next\", n); } function _throw(n) { asyncGeneratorStep(a, r, o, _next, _throw, \"throw\", n); } _next(void 0); }); }; }\nimport { defineComponent, ref, computed, watch, nextTick, openBlock, createElementBlock, unref, normalizeClass, createElementVNode, Fragment, renderList, withKeys, withModifiers, toDisplayString } from 'vue';\nimport dayjs from 'dayjs';\nimport '../../../../hooks/index.mjs';\nimport '../../../time-picker/index.mjs';\nimport '../../../../utils/index.mjs';\nimport { basicMonthTableProps } from '../props/basic-month-table.mjs';\nimport _export_sfc from '../../../../_virtual/plugin-vue_export-helper.mjs';\nimport { rangeArr } from '../../../time-picker/src/utils.mjs';\nimport { useNamespace } from '../../../../hooks/use-namespace/index.mjs';\nimport { useLocale } from '../../../../hooks/use-locale/index.mjs';\nimport { castArray } from '../../../../utils/arrays.mjs';\nimport { hasClass } from '../../../../utils/dom/style.mjs';\nvar _hoisted_1 = [\"aria-label\"];\nvar _hoisted_2 = [\"aria-selected\", \"aria-label\", \"tabindex\", \"onKeydown\"];\nvar _hoisted_3 = {\n  class: \"cell\"\n};\nvar _sfc_main = /* @__PURE__ */defineComponent({\n  __name: \"basic-month-table\",\n  props: basicMonthTableProps,\n  emits: [\"changerange\", \"pick\", \"select\"],\n  setup(__props, _ref) {\n    var expose = _ref.expose,\n      emit = _ref.emit;\n    var props = __props;\n    var datesInMonth = function datesInMonth(year, month, lang2) {\n      var firstDay = dayjs().locale(lang2).startOf(\"month\").month(month).year(year);\n      var numOfDays = firstDay.daysInMonth();\n      return rangeArr(numOfDays).map(function (n) {\n        return firstDay.add(n, \"day\").toDate();\n      });\n    };\n    var ns = useNamespace(\"month-table\");\n    var _useLocale = useLocale(),\n      t = _useLocale.t,\n      lang = _useLocale.lang;\n    var tbodyRef = ref();\n    var currentCellRef = ref();\n    var months = ref(props.date.locale(\"en\").localeData().monthsShort().map(function (_) {\n      return _.toLowerCase();\n    }));\n    var tableRows = ref([[], [], []]);\n    var lastRow = ref();\n    var lastColumn = ref();\n    var rows = computed(function () {\n      var _a, _b;\n      var rows2 = tableRows.value;\n      var now = dayjs().locale(lang.value).startOf(\"month\");\n      for (var i = 0; i < 3; i++) {\n        var row = rows2[i];\n        for (var j = 0; j < 4; j++) {\n          var cell = row[j] || (row[j] = {\n            row: i,\n            column: j,\n            type: \"normal\",\n            inRange: false,\n            start: false,\n            end: false,\n            text: -1,\n            disabled: false\n          });\n          cell.type = \"normal\";\n          var index = i * 4 + j;\n          var calTime = props.date.startOf(\"year\").month(index);\n          var calEndDate = props.rangeState.endDate || props.maxDate || props.rangeState.selecting && props.minDate || null;\n          cell.inRange = !!(props.minDate && calTime.isSameOrAfter(props.minDate, \"month\") && calEndDate && calTime.isSameOrBefore(calEndDate, \"month\")) || !!(props.minDate && calTime.isSameOrBefore(props.minDate, \"month\") && calEndDate && calTime.isSameOrAfter(calEndDate, \"month\"));\n          if ((_a = props.minDate) == null ? void 0 : _a.isSameOrAfter(calEndDate)) {\n            cell.start = !!(calEndDate && calTime.isSame(calEndDate, \"month\"));\n            cell.end = props.minDate && calTime.isSame(props.minDate, \"month\");\n          } else {\n            cell.start = !!(props.minDate && calTime.isSame(props.minDate, \"month\"));\n            cell.end = !!(calEndDate && calTime.isSame(calEndDate, \"month\"));\n          }\n          var isToday = now.isSame(calTime);\n          if (isToday) {\n            cell.type = \"today\";\n          }\n          cell.text = index;\n          cell.disabled = ((_b = props.disabledDate) == null ? void 0 : _b.call(props, calTime.toDate())) || false;\n        }\n      }\n      return rows2;\n    });\n    var focus = function focus() {\n      var _a;\n      (_a = currentCellRef.value) == null ? void 0 : _a.focus();\n    };\n    var getCellStyle = function getCellStyle(cell) {\n      var style = {};\n      var year = props.date.year();\n      var today = new Date();\n      var month = cell.text;\n      style.disabled = props.disabledDate ? datesInMonth(year, month, lang.value).every(props.disabledDate) : false;\n      style.current = castArray(props.parsedValue).findIndex(function (date) {\n        return dayjs.isDayjs(date) && date.year() === year && date.month() === month;\n      }) >= 0;\n      style.today = today.getFullYear() === year && today.getMonth() === month;\n      if (cell.inRange) {\n        style[\"in-range\"] = true;\n        if (cell.start) {\n          style[\"start-date\"] = true;\n        }\n        if (cell.end) {\n          style[\"end-date\"] = true;\n        }\n      }\n      return style;\n    };\n    var isSelectedCell = function isSelectedCell(cell) {\n      var year = props.date.year();\n      var month = cell.text;\n      return castArray(props.date).findIndex(function (date) {\n        return date.year() === year && date.month() === month;\n      }) >= 0;\n    };\n    var handleMouseMove = function handleMouseMove(event) {\n      var _a;\n      if (!props.rangeState.selecting) return;\n      var target = event.target;\n      if (target.tagName === \"SPAN\") {\n        target = (_a = target.parentNode) == null ? void 0 : _a.parentNode;\n      }\n      if (target.tagName === \"DIV\") {\n        target = target.parentNode;\n      }\n      if (target.tagName !== \"TD\") return;\n      var row = target.parentNode.rowIndex;\n      var column = target.cellIndex;\n      if (rows.value[row][column].disabled) return;\n      if (row !== lastRow.value || column !== lastColumn.value) {\n        lastRow.value = row;\n        lastColumn.value = column;\n        emit(\"changerange\", {\n          selecting: true,\n          endDate: props.date.startOf(\"year\").month(row * 4 + column)\n        });\n      }\n    };\n    var handleMonthTableClick = function handleMonthTableClick(event) {\n      var _a;\n      var target = (_a = event.target) == null ? void 0 : _a.closest(\"td\");\n      if ((target == null ? void 0 : target.tagName) !== \"TD\") return;\n      if (hasClass(target, \"disabled\")) return;\n      var column = target.cellIndex;\n      var row = target.parentNode.rowIndex;\n      var month = row * 4 + column;\n      var newDate = props.date.startOf(\"year\").month(month);\n      if (props.selectionMode === \"range\") {\n        if (!props.rangeState.selecting) {\n          emit(\"pick\", {\n            minDate: newDate,\n            maxDate: null\n          });\n          emit(\"select\", true);\n        } else {\n          if (props.minDate && newDate >= props.minDate) {\n            emit(\"pick\", {\n              minDate: props.minDate,\n              maxDate: newDate\n            });\n          } else {\n            emit(\"pick\", {\n              minDate: newDate,\n              maxDate: props.minDate\n            });\n          }\n          emit(\"select\", false);\n        }\n      } else {\n        emit(\"pick\", month);\n      }\n    };\n    watch(function () {\n      return props.date;\n    }, /*#__PURE__*/_asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee() {\n      var _a, _b;\n      return _regeneratorRuntime().wrap(function _callee$(_context) {\n        while (1) switch (_context.prev = _context.next) {\n          case 0:\n            if (!((_a = tbodyRef.value) == null ? void 0 : _a.contains(document.activeElement))) {\n              _context.next = 4;\n              break;\n            }\n            _context.next = 3;\n            return nextTick();\n          case 3:\n            (_b = currentCellRef.value) == null ? void 0 : _b.focus();\n          case 4:\n          case \"end\":\n            return _context.stop();\n        }\n      }, _callee);\n    })));\n    expose({\n      focus\n    });\n    return function (_ctx, _cache) {\n      return openBlock(), createElementBlock(\"table\", {\n        role: \"grid\",\n        \"aria-label\": unref(t)(\"el.datepicker.monthTablePrompt\"),\n        class: normalizeClass(unref(ns).b()),\n        onClick: handleMonthTableClick,\n        onMousemove: handleMouseMove\n      }, [createElementVNode(\"tbody\", {\n        ref_key: \"tbodyRef\",\n        ref: tbodyRef\n      }, [(openBlock(true), createElementBlock(Fragment, null, renderList(unref(rows), function (row, key) {\n        return openBlock(), createElementBlock(\"tr\", {\n          key\n        }, [(openBlock(true), createElementBlock(Fragment, null, renderList(row, function (cell, key_) {\n          return openBlock(), createElementBlock(\"td\", {\n            key: key_,\n            ref_for: true,\n            ref: function ref(el) {\n              return isSelectedCell(cell) && (currentCellRef.value = el);\n            },\n            class: normalizeClass(getCellStyle(cell)),\n            \"aria-selected\": `${isSelectedCell(cell)}`,\n            \"aria-label\": unref(t)(`el.datepicker.month${+cell.text + 1}`),\n            tabindex: isSelectedCell(cell) ? 0 : -1,\n            onKeydown: [withKeys(withModifiers(handleMonthTableClick, [\"prevent\", \"stop\"]), [\"space\"]), withKeys(withModifiers(handleMonthTableClick, [\"prevent\", \"stop\"]), [\"enter\"])]\n          }, [createElementVNode(\"div\", null, [createElementVNode(\"span\", _hoisted_3, toDisplayString(unref(t)(\"el.datepicker.months.\" + months.value[cell.text])), 1)])], 42, _hoisted_2);\n        }), 128))]);\n      }), 128))], 512)], 42, _hoisted_1);\n    };\n  }\n});\nvar MonthTable = /* @__PURE__ */_export_sfc(_sfc_main, [[\"__file\", \"basic-month-table.vue\"]]);\nexport { MonthTable as default };", "map": {"version": 3, "names": ["datesInMonth", "year", "month", "lang2", "firstDay", "dayjs", "locale", "startOf", "numOfDays", "daysInMonth", "rangeArr", "map", "n", "add", "toDate", "ns", "useNamespace", "_useLocale", "useLocale", "t", "lang", "tbodyRef", "ref", "currentCellRef", "months", "props", "date", "localeData", "monthsShort", "_", "toLowerCase", "tableRows", "lastRow", "lastColumn", "rows", "computed", "_a", "_b", "rows2", "value", "now", "i", "row", "j", "cell", "column", "type", "inRange", "start", "end", "text", "disabled", "index", "calTime", "calEndDate", "rangeState", "endDate", "maxDate", "selecting", "minDate", "isSameOrAfter", "isSameOrBefore", "isSame", "isToday", "disabledDate", "call", "focus", "getCellStyle", "style", "today", "Date", "every", "current", "<PERSON><PERSON><PERSON><PERSON>", "parsedValue", "findIndex", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "getFullYear", "getMonth", "isSelectedCell", "handleMouseMove", "event", "target", "tagName", "parentNode", "rowIndex", "cellIndex", "emit", "handleMonthTableClick", "closest", "hasClass", "newDate", "selectionMode", "watch", "_asyncToGenerator", "_regeneratorRuntime", "mark", "_callee", "wrap", "_callee$", "_context", "prev", "next", "contains", "document", "activeElement", "nextTick", "stop"], "sources": ["../../../../../../../packages/components/date-picker/src/date-picker-com/basic-month-table.vue"], "sourcesContent": ["<template>\n  <table\n    role=\"grid\"\n    :aria-label=\"t('el.datepicker.monthTablePrompt')\"\n    :class=\"ns.b()\"\n    @click=\"handleMonthTableClick\"\n    @mousemove=\"handleMouseMove\"\n  >\n    <tbody ref=\"tbodyRef\">\n      <tr v-for=\"(row, key) in rows\" :key=\"key\">\n        <td\n          v-for=\"(cell, key_) in row\"\n          :key=\"key_\"\n          :ref=\"(el) => isSelectedCell(cell) && (currentCellRef = el as HTMLElement)\"\n          :class=\"getCellStyle(cell)\"\n          :aria-selected=\"`${isSelectedCell(cell)}`\"\n          :aria-label=\"t(`el.datepicker.month${+cell.text + 1}`)\"\n          :tabindex=\"isSelectedCell(cell) ? 0 : -1\"\n          @keydown.space.prevent.stop=\"handleMonthTableClick\"\n          @keydown.enter.prevent.stop=\"handleMonthTableClick\"\n        >\n          <div>\n            <span class=\"cell\">\n              {{ t('el.datepicker.months.' + months[cell.text]) }}\n            </span>\n          </div>\n        </td>\n      </tr>\n    </tbody>\n  </table>\n</template>\n\n<script lang=\"ts\" setup>\nimport { computed, nextTick, ref, watch } from 'vue'\nimport dayjs from 'dayjs'\nimport { useLocale, useNamespace } from '@element-plus/hooks'\nimport { rangeArr } from '@element-plus/components/time-picker'\nimport { castArray, hasClass } from '@element-plus/utils'\nimport { basicMonthTableProps } from '../props/basic-month-table'\n\ntype MonthCell = {\n  column: number\n  row: number\n  disabled: boolean\n  start: boolean\n  end: boolean\n  text: number\n  type: 'normal' | 'today'\n  inRange: boolean\n}\n\nconst datesInMonth = (year: number, month: number, lang: string) => {\n  const firstDay = dayjs().locale(lang).startOf('month').month(month).year(year)\n  const numOfDays = firstDay.daysInMonth()\n  return rangeArr(numOfDays).map((n) => firstDay.add(n, 'day').toDate())\n}\n\nconst props = defineProps(basicMonthTableProps)\nconst emit = defineEmits(['changerange', 'pick', 'select'])\n\nconst ns = useNamespace('month-table')\n\nconst { t, lang } = useLocale()\nconst tbodyRef = ref<HTMLElement>()\nconst currentCellRef = ref<HTMLElement>()\nconst months = ref(\n  props.date\n    .locale('en')\n    .localeData()\n    .monthsShort()\n    .map((_) => _.toLowerCase())\n)\nconst tableRows = ref<MonthCell[][]>([\n  [] as MonthCell[],\n  [] as MonthCell[],\n  [] as MonthCell[],\n])\nconst lastRow = ref<number>()\nconst lastColumn = ref<number>()\nconst rows = computed<MonthCell[][]>(() => {\n  const rows = tableRows.value\n\n  const now = dayjs().locale(lang.value).startOf('month')\n\n  for (let i = 0; i < 3; i++) {\n    const row = rows[i]\n    for (let j = 0; j < 4; j++) {\n      const cell = (row[j] ||= {\n        row: i,\n        column: j,\n        type: 'normal',\n        inRange: false,\n        start: false,\n        end: false,\n        text: -1,\n        disabled: false,\n      })\n\n      cell.type = 'normal'\n\n      const index = i * 4 + j\n      const calTime = props.date.startOf('year').month(index)\n\n      const calEndDate =\n        props.rangeState.endDate ||\n        props.maxDate ||\n        (props.rangeState.selecting && props.minDate) ||\n        null\n\n      cell.inRange =\n        !!(\n          props.minDate &&\n          calTime.isSameOrAfter(props.minDate, 'month') &&\n          calEndDate &&\n          calTime.isSameOrBefore(calEndDate, 'month')\n        ) ||\n        !!(\n          props.minDate &&\n          calTime.isSameOrBefore(props.minDate, 'month') &&\n          calEndDate &&\n          calTime.isSameOrAfter(calEndDate, 'month')\n        )\n\n      if (props.minDate?.isSameOrAfter(calEndDate)) {\n        cell.start = !!(calEndDate && calTime.isSame(calEndDate, 'month'))\n        cell.end = props.minDate && calTime.isSame(props.minDate, 'month')\n      } else {\n        cell.start = !!(props.minDate && calTime.isSame(props.minDate, 'month'))\n        cell.end = !!(calEndDate && calTime.isSame(calEndDate, 'month'))\n      }\n\n      const isToday = now.isSame(calTime)\n      if (isToday) {\n        cell.type = 'today'\n      }\n\n      cell.text = index\n      cell.disabled = props.disabledDate?.(calTime.toDate()) || false\n    }\n  }\n  return rows\n})\n\nconst focus = () => {\n  currentCellRef.value?.focus()\n}\n\nconst getCellStyle = (cell: MonthCell) => {\n  const style = {} as any\n  const year = props.date.year()\n  const today = new Date()\n  const month = cell.text\n\n  style.disabled = props.disabledDate\n    ? datesInMonth(year, month, lang.value).every(props.disabledDate)\n    : false\n  style.current =\n    castArray(props.parsedValue).findIndex(\n      (date) =>\n        dayjs.isDayjs(date) && date.year() === year && date.month() === month\n    ) >= 0\n  style.today = today.getFullYear() === year && today.getMonth() === month\n\n  if (cell.inRange) {\n    style['in-range'] = true\n\n    if (cell.start) {\n      style['start-date'] = true\n    }\n\n    if (cell.end) {\n      style['end-date'] = true\n    }\n  }\n  return style\n}\n\nconst isSelectedCell = (cell: MonthCell) => {\n  const year = props.date.year()\n  const month = cell.text\n  return (\n    castArray(props.date).findIndex(\n      (date) => date.year() === year && date.month() === month\n    ) >= 0\n  )\n}\n\nconst handleMouseMove = (event: MouseEvent) => {\n  if (!props.rangeState.selecting) return\n\n  let target = event.target as HTMLElement\n  if (target.tagName === 'SPAN') {\n    target = target.parentNode?.parentNode as HTMLElement\n  }\n  if (target.tagName === 'DIV') {\n    target = target.parentNode as HTMLElement\n  }\n  if (target.tagName !== 'TD') return\n\n  const row = (target.parentNode as HTMLTableRowElement).rowIndex\n  const column = (target as HTMLTableCellElement).cellIndex\n  // can not select disabled date\n  if (rows.value[row][column].disabled) return\n\n  // only update rangeState when mouse moves to a new cell\n  // this avoids frequent Date object creation and improves performance\n  if (row !== lastRow.value || column !== lastColumn.value) {\n    lastRow.value = row\n    lastColumn.value = column\n    emit('changerange', {\n      selecting: true,\n      endDate: props.date.startOf('year').month(row * 4 + column),\n    })\n  }\n}\nconst handleMonthTableClick = (event: MouseEvent | KeyboardEvent) => {\n  const target = (event.target as HTMLElement)?.closest(\n    'td'\n  ) as HTMLTableCellElement\n  if (target?.tagName !== 'TD') return\n  if (hasClass(target, 'disabled')) return\n  const column = target.cellIndex\n  const row = (target.parentNode as HTMLTableRowElement).rowIndex\n  const month = row * 4 + column\n  const newDate = props.date.startOf('year').month(month)\n  if (props.selectionMode === 'range') {\n    if (!props.rangeState.selecting) {\n      emit('pick', { minDate: newDate, maxDate: null })\n      emit('select', true)\n    } else {\n      if (props.minDate && newDate >= props.minDate) {\n        emit('pick', { minDate: props.minDate, maxDate: newDate })\n      } else {\n        emit('pick', { minDate: newDate, maxDate: props.minDate })\n      }\n      emit('select', false)\n    }\n  } else {\n    emit('pick', month)\n  }\n}\n\nwatch(\n  () => props.date,\n  async () => {\n    if (tbodyRef.value?.contains(document.activeElement)) {\n      await nextTick()\n      currentCellRef.value?.focus()\n    }\n  }\n)\n\ndefineExpose({\n  /**\n   * @description focus current cell\n   */\n  focus,\n})\n</script>\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;IAmDA,IAAMA,YAAe,YAAfA,YAAeA,CAACC,IAAc,EAAAC,KAAA,EAAeC,KAAiB;MAClE,IAAMC,QAAW,GAAAC,KAAA,EAAQ,CAAAC,MAAA,CAAOH,KAAI,EAAEI,OAAQ,QAAO,CAAE,CAAAL,KAAA,CAAMA,KAAK,EAAED,IAAA,CAAKA,IAAI;MACvE,IAAAO,SAAA,GAAYJ,QAAA,CAASK,WAAY;MACvC,OAAOC,QAAS,CAAAF,SAAS,CAAE,CAAAG,GAAA,CAAI,UAACC,CAAA;QAAA,OAAMR,QAAS,CAAAS,GAAA,CAAID,CAAG,OAAK,CAAE,CAAAE,MAAA,EAAQ;MAAA;IAAA,CACvE;IAKM,IAAAC,EAAA,GAAKC,YAAA,CAAa,aAAa;IAE/B,IAAAC,UAAA,GAAcC,SAAU;MAAtBC,CAAG,GAAAF,UAAA,CAAHE,CAAG;MAAAC,IAAA,GAAAH,UAAA,CAAAG,IAAA;IACX,IAAMC,QAAA,GAAWC,GAAiB;IAClC,IAAMC,cAAA,GAAiBD,GAAiB;IACxC,IAAME,MAAA,GAASF,GACb,CAAAG,KAAA,CAAMC,IACH,CAAApB,MAAA,CAAO,IAAI,CACX,CAAAqB,UAAA,EACA,CAAAC,WAAA,GACAjB,GAAI,WAACkB,CAAA;MAAA,OAAMA,CAAE,CAAAC,WAAA,EAAa;IAAA,EAC/B;IACA,IAAMC,SAAA,GAAYT,GAAmB,EACnC,EAAC,EACD,EAAC,EACD,EAAC,CACF;IACD,IAAMU,OAAA,GAAUV,GAAY;IAC5B,IAAMW,UAAA,GAAaX,GAAY;IACzB,IAAAY,IAAA,GAAOC,QAAA,CAAwB,YAAM;MACzC,IAAAC,EAAM,EAAAC,EAAA;MAEA,IAAAC,KAAA,GAAAP,SAAc,CAAAQ,KAAA;MAEpB,IAAAC,GAAS,GAAInC,KAAO,GAAAC,MAAQ,CAAAc,IAAA,CAAAmB,KAAA,EAAAhC,OAAA;MAC1B,SAAAkC,CAAA,IAAY,EAAKA,CAAA,MAAAA,CAAA;QACjB,IAAAC,GAAS,GAAIJ,KAAO,CAAAG,CAAA;QACZ,SAAAE,CAAA,MAAAA,CAAA,GAAmB,GAAAA,CAAA;UAAA,IAClBC,IAAA,GAAAF,GAAA,CAAAC,CAAA,MAAAD,GAAA,CAAAC,CAAA;YACLD,GAAQ,EAAAD,CAAA;YACRI,MAAM,EAAAF,CAAA;YACNG,IAAS;YACTC,OAAO;YACPC,KAAK;YACLC,GAAM;YACNC,IAAU;YACZC,QAAA;UAEA;UAEMP,IAAA,CAAAE,IAAA,WAAgB;UACtB,IAAMM,KAAA,GAAAX,CAAA,GAAgB,IAAAE,CAAA;UAEhB,IAAAU,OAAA,GAAA5B,KACE,CAAAC,IAAA,CAAAnB,OAAA,OACN,EAAAL,KAAA,CAAAkD,KAAA;UAIF,IACEE,UAAC,GACC7B,KAAM,CACN8B,UAAA,CAAAC,OAAQ,IAAc/B,KAAA,CAAAgC,OAAA,IAAehC,KAAA,CAAA8B,UACrC,CAAAG,SAAA,IAAAjC,KAAA,CAAAkC,OACuB;UAS3Bf,IAAI,CAAMG,OAAA,MAAAtB,KAAuB,CAAAkC,OAAA,IAAAN,OAAA,CAAUO,aAAG,CAAAnC,KAAA,CAAAkC,OAAA,cAAAL,UAAA,IAAAD,OAAA,CAAAQ,cAAA,CAAAP,UAAA,kBAAA7B,KAAA,CAAAkC,OAAA,IAAAN,OAAA,CAAAQ,cAAA,CAAApC,KAAA,CAAAkC,OAAA,cAAAL,UAAA,IAAAD,OAAA,CAAAO,aAAA,CAAAN,UAAA;UAC5C,KAAAlB,EAAK,GAAAX,KAAQ,CAACkC,OAAA,SAAgB,GAAQ,SAAAvB,EAAA,CAAOwB,aAAmB,CAAAN,UAAA;YAChEV,IAAA,CAAKI,KAAA,GAAY,GAAAM,UAAA,IAAAD,OAA0B,CAAAS,MAAA,CAAAR,UAAA,SAAsB;YAC5DV,IAAA,CAAAK,GAAA,GAAAxB,KAAA,CAAAkC,OAAA,IAAAN,OAAA,CAAAS,MAAA,CAAArC,KAAA,CAAAkC,OAAA;UACL,CAAK;YACLf,IAAA,CAAKI,KAAA,GAAS,GAAAvB,KAAA,CAAAkC,OAAsB,IAAAN,OAAA,CAAAS,MAAA,CAAArC,KAAA,CAAAkC,OAA0B;YAChEf,IAAA,CAAAK,GAAA,MAAAK,UAAA,IAAAD,OAAA,CAAAS,MAAA,CAAAR,UAAA;UAEA;UACA,IAAaS,OAAA,GAAAvB,GAAA,CAAAsB,MAAA,CAAAT,OAAA;UACX,IAAAU,OAAY;YACdnB,IAAA,CAAAE,IAAA;UAEA;UACAF,IAAA,CAAKM,IAAA,GAAAE,KAAiB;UACxBR,IAAA,CAAAO,QAAA,KAAAd,EAAA,GAAAZ,KAAA,CAAAuC,YAAA,qBAAA3B,EAAA,CAAA4B,IAAA,CAAAxC,KAAA,EAAA4B,OAAA,CAAAvC,MAAA;QAAA;MAEF;MACD,OAAAwB,KAAA;IAED;IACE,IAAA4B,KAAA,YAAAA,MAAA;MACF,IAAA9B,EAAA;MAEM,CAAAA,EAAA,GAAAb,cAAgB,CAAoBgB,KAAA,qBAAAH,EAAA,CAAA8B,KAAA;IACxC;IACM,IAAAC,YAAa,YAAbA,YAAaA,CAAAvB,IAAK,EAAK;MACvB,IAAAwB,KAAA,GAAQ;MACd,IAAMnE,IAAA,GAAAwB,KAAa,CAAAC,IAAA,CAAAzB,IAAA;MAEnB,IAAMoE,KAAW,OAAAC,IAAA,EACb;MAEE,IAAApE,KAAA,GAAA0C,IAAA,CAAAM,IAAA;MAKNkB,KAAA,CAAMjB,QAAQ,GAAM1B,KAAA,CAAAuC,YAAA,GAAkBhE,YAAQ,CAAAC,IAAA,EAAAC,KAAqB,EAAAkB,IAAA,CAAAmB,KAAA,EAAAgC,KAAA,CAAA9C,KAAA,CAAAuC,YAAA;MAEnEI,KAAA,CAAAI,OAAkB,GAAAC,SAAA,CAAAhD,KAAA,CAAAiD,WAAA,EAAAC,SAAA,WAAAjD,IAAA;QAAA,OAAArB,KAAA,CAAAuE,OAAA,CAAAlD,IAAA,KAAAA,IAAA,CAAAzB,IAAA,OAAAA,IAAA,IAAAyB,IAAA,CAAAxB,KAAA,OAAAA,KAAA;MAAA;MAChBkE,KAAA,CAAAC,KAAoB,GAAAA,KAAA,CAAAQ,WAAA,OAAA5E,IAAA,IAAAoE,KAAA,CAAAS,QAAA,OAAA5E,KAAA;MAEpB,IAAA0C,IAAA,CAAAG,OAAgB;QACdqB,KAAA,WAAsB;QACxB,IAAAxB,IAAA,CAAAI,KAAA;UAEAoB,KAAA,aAAc;QACZ;QACF,IAAAxB,IAAA,CAAAK,GAAA;UACFmB,KAAA;QACA;MAAO;MAGH,OAAAA,KAAA;IACJ,CAAM;IACN,IAAAW,cAAmB,YAAnBA,cAAmBA,CAAAnC,IAAA;MACnB,IAAA3C,IACY,GAAAwB,KAAA,CAAAC,IAAM,CAAIzB,IAAA;MAIxB,IAAAC,KAAA,GAAA0C,IAAA,CAAAM,IAAA;MAEM,OAAAuB,SAAA,CAAAhD,KAAkB,CAACC,IAAsB,EAAAiD,SAAA,WAAAjD,IAAA;QAAA,OAAAA,IAAA,CAAAzB,IAAA,OAAAA,IAAA,IAAAyB,IAAA,CAAAxB,KAAA,OAAAA,KAAA;MAAA;IAC7C,CAAI;IAA6B,IAAA8E,eAAA,YAAAA,gBAAAC,KAAA;MAEjC,IAAI7C,EAAA;MACA,KAAAX,KAAA,CAAO8B,UAAA,CAAAG,SAAoB,EAC7B;MACF,IAAAwB,MAAA,GAAAD,KAAA,CAAAC,MAAA;MACI,IAAAA,MAAA,CAAOC,OAAA,KAAY,MAAO;QAC5BD,MAAA,GAAS,CAAO9C,EAAA,GAAA8C,MAAA,CAAAE,UAAA,qBAAAhD,EAAA,CAAAgD,UAAA;MAAA;MAElB,IAAIF,MAAA,CAAOC,OAAY;QAAMD,MAAA,GAAAA,MAAA,CAAAE,UAAA;MAE7B;MACA,IAAAF,MAAA,CAAAC,OAAgD,WAE5C;MAAkC,IAAAzC,GAAA,GAAAwC,MAAA,CAAAE,UAAA,CAAAC,QAAA;MAItC,IAAYxC,MAAA,GAAAqC,MAAiB,CAAAI,SAAA;MAC3B,IAAApD,IAAA,CAAAK,KAAgB,CAAAG,GAAA,EAAAG,MAAA,EAAAM,QAAA,EAChB;MACA,IAAAT,GAAK,KAAeV,OAAA,CAAAO,KAAA,IAAAM,MAAA,KAAAZ,UAAA,CAAAM,KAAA;QAAAP,OACP,CAAAO,KAAA,GAAAG,GAAA;QACXT,UAAA,CAASM,KAAA,GAAWM,MAAA;QACtB0C,IAAC;UACH7B,SAAA;UACFF,OAAA,EAAA/B,KAAA,CAAAC,IAAA,CAAAnB,OAAA,SAAAL,KAAA,CAAAwC,GAAA,OAAAG,MAAA;QACA,CAAM;MACJ;IAGA;IAA8B,IAAA2C,qBAAA,YAAAA,sBAAAP,KAAA;MAC1B,IAAA7C,EAAA;MAA8B,IAAA8C,MAAA,IAAA9C,EAAA,GAAA6C,KAAA,CAAAC,MAAA,qBAAA9C,EAAA,CAAAqD,OAAA;MAClC,KAAAP,MAAA,IAAe,IAAO,YAAAA,MAAA,CAAAC,OAAA,YAChB;MACA,IAAAO,QAAA,CAAAR,MAAA,EAAkB,aACxB;MACI,IAAArC,MAAA,GAAAqC,MAAA,CAAAI,SAAiC;MAC/B,IAAA5C,GAAO,GAAAwC,MAAA,CAAAE,UAAsB,CAAAC,QAAA;MAC/B,IAAAnF,KAAA,GAAAwC,GAAa,GAAE,IAAAG,MAAkB;MACjC,IAAA8C,OAAA,GAAAlE,KAAmB,CAAAC,IAAA,CAAAnB,OAAA,SAAAL,KAAA,CAAAA,KAAA;MAAA,IACduB,KAAA,CAAAmE,aAAA;QACL,KAAAnE,KAAU,CAAA8B,UAAA,CAAAG,SAAsB;UAC9B6B,IAAA;YAAa5B,OAAE,EAAAgC,OAAA;YAAwBlC,OAAA;UAAA;UACzC8B,IAAO;QACL;UACF,IAAA9D,KAAA,CAAAkC,OAAA,IAAAgC,OAAA,IAAAlE,KAAA,CAAAkC,OAAA;YACA4B,IAAA,SAAe;cAAK5B,OAAA,EAAAlC,KAAA,CAAAkC,OAAA;cAAAF,OAAA,EAAAkC;YAAA;UAAA,CACtB;YACKJ,IAAA;cAAA5B,OAAA,EAAAgC,OAAA;cAAAlC,OAAA,EAAAhC,KAAA,CAAAkC;YAAA;UACL;UACF4B,IAAA;QAAA;MAGF,CACE;QAEEA,IAAa,SAAArF,KAAO,CAAS;MAC3B;IACA;IACF2F,KAAA;MAAA,OAAApE,KAAA,CAAAC,IAAA;IAAA,gBAAAoE,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAAC,QAAA;MAAA,IAAA7D,EAAA,EAAAC,EAAA;MAAA,OAAA0D,mBAAA,GAAAG,IAAA,UAAAC,SAAAC,QAAA;QAAA,kBAAAA,QAAA,CAAAC,IAAA,GAAAD,QAAA,CAAAE,IAAA;UAAA;YAAA,MAIS,CAAAlE,EAAA,GAAAf,QAAA,CAAAkB,KAAA,qBAAAH,EAAA,CAAAmE,QAAA,CAAAC,QAAA,CAAAC,aAAA;cAAAL,QAAA,CAAAE,IAAA;cAAA;YAAA;YAAAF,QAAA,CAAAE,IAAA;YAAA,OAIXI,QAAA;UAAA;YACD,CAAArE,EAAA,GAAAd,cAAA,CAAAgB,KAAA,qBAAAF,EAAA,CAAA6B,KAAA;UAAA;UAAA;YAAA,OAAAkC,QAAA,CAAAO,IAAA;QAAA;MAAA,GAAAV,OAAA;IAAA,C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
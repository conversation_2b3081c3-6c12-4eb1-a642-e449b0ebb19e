{"ast": null, "code": "function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nfunction _defineProperty(e, r, t) { return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, { value: t, enumerable: !0, configurable: !0, writable: !0 }) : e[r] = t, e; }\nfunction _toPropertyKey(t) { var i = _toPrimitive(t, \"string\"); return \"symbol\" == typeof i ? i : i + \"\"; }\nfunction _toPrimitive(t, r) { if (\"object\" != typeof t || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || \"default\"); if (\"object\" != typeof i) return i; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (\"string\" === r ? String : Number)(t); }\nvar calcColumnStyle = function calcColumnStyle(column, fixedColumn, fixed) {\n  var _a;\n  var flex = _objectSpread({\n    flexGrow: 0,\n    flexShrink: 0\n  }, fixed ? {} : {\n    flexGrow: column.flexGrow || 0,\n    flexShrink: column.flexShrink || 1\n  });\n  if (!fixed) {\n    flex.flexShrink = 1;\n  }\n  var style = _objectSpread(_objectSpread(_objectSpread({}, (_a = column.style) != null ? _a : {}), flex), {}, {\n    flexBasis: \"auto\",\n    width: column.width\n  });\n  if (!fixedColumn) {\n    if (column.maxWidth) style.maxWidth = column.maxWidth;\n    if (column.minWidth) style.minWidth = column.minWidth;\n  }\n  return style;\n};\nexport { calcColumnStyle };", "map": {"version": 3, "names": ["calcColumnStyle", "column", "fixedColumn", "fixed", "_a", "flex", "_objectSpread", "flexGrow", "flexShrink", "style", "flexBasis", "width", "max<PERSON><PERSON><PERSON>", "min<PERSON><PERSON><PERSON>"], "sources": ["../../../../../../../packages/components/table-v2/src/composables/utils.ts"], "sourcesContent": ["import type { CSSProperties } from 'vue'\nimport type { AnyColumns } from '../types'\n\nexport const calcColumnStyle = (\n  column: AnyColumns[number],\n  fixedColumn: boolean,\n  fixed: boolean\n): CSSProperties => {\n  const flex = {\n    flexGrow: 0,\n    flexShrink: 0,\n    ...(fixed\n      ? {}\n      : {\n          flexGrow: column.flexGrow || 0,\n          flexShrink: column.flexShrink || 1,\n        }),\n  }\n\n  if (!fixed) {\n    flex.flexShrink = 1\n  }\n\n  const style = {\n    ...(column.style ?? {}),\n    ...flex,\n    flexBasis: 'auto',\n    width: column.width,\n  }\n\n  if (!fixedColumn) {\n    if (column.maxWidth) style.maxWidth = column.maxWidth\n    if (column.minWidth) style.minWidth = column.minWidth\n  }\n\n  return style\n}\n"], "mappings": ";;;;;AAAY,IAACA,eAAe,GAAG,SAAlBA,eAAeA,CAAIC,MAAM,EAAEC,WAAW,EAAEC,KAAK,EAAK;EAC7D,IAAIC,EAAE;EACN,IAAMC,IAAI,GAAAC,aAAA;IACRC,QAAQ,EAAE,CAAC;IACXC,UAAU,EAAE;EAAC,GACVL,KAAK,GAAG,EAAE,GAAG;IACdI,QAAQ,EAAEN,MAAM,CAACM,QAAQ,IAAI,CAAC;IAC9BC,UAAU,EAAEP,MAAM,CAACO,UAAU,IAAI;EACvC,CAAK,CACF;EACD,IAAI,CAACL,KAAK,EAAE;IACVE,IAAI,CAACG,UAAU,GAAG,CAAC;EACvB;EACE,IAAMC,KAAK,GAAAH,aAAA,CAAAA,aAAA,CAAAA,aAAA,KACN,CAACF,EAAE,GAAGH,MAAM,CAACQ,KAAK,KAAK,IAAI,GAAGL,EAAE,GAAG,EAAE,GACrCC,IAAI;IACPK,SAAS,EAAE,MAAM;IACjBC,KAAK,EAAEV,MAAM,CAACU;EAAK,EACpB;EACD,IAAI,CAACT,WAAW,EAAE;IAChB,IAAID,MAAM,CAACW,QAAQ,EACjBH,KAAK,CAACG,QAAQ,GAAGX,MAAM,CAACW,QAAQ;IAClC,IAAIX,MAAM,CAACY,QAAQ,EACjBJ,KAAK,CAACI,QAAQ,GAAGZ,MAAM,CAACY,QAAQ;EACtC;EACE,OAAOJ,KAAK;AACd", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
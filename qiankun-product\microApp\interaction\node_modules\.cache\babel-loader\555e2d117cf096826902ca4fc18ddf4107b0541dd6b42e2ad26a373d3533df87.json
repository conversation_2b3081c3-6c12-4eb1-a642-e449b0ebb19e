{"ast": null, "code": "import { resolveComponent as _resolveComponent, createVNode as _createVNode, withCtx as _withCtx, createTextVNode as _createTextVNode, createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock } from \"vue\";\nvar _hoisted_1 = {\n  class: \"submit-select-person-group\"\n};\nvar _hoisted_2 = {\n  class: \"globalFormButton\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  var _component_el_input = _resolveComponent(\"el-input\");\n  var _component_el_form_item = _resolveComponent(\"el-form-item\");\n  var _component_el_tree_select = _resolveComponent(\"el-tree-select\");\n  var _component_el_button = _resolveComponent(\"el-button\");\n  var _component_el_form = _resolveComponent(\"el-form\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createVNode(_component_el_form, {\n    ref: \"formRef\",\n    model: $setup.form,\n    rules: $setup.rules,\n    inline: \"\",\n    \"label-position\": \"top\",\n    class: \"globalForm\"\n  }, {\n    default: _withCtx(function () {\n      return [_createVNode(_component_el_form_item, {\n        label: \"分组名称\",\n        prop: \"name\"\n      }, {\n        default: _withCtx(function () {\n          return [_createVNode(_component_el_input, {\n            modelValue: $setup.form.name,\n            \"onUpdate:modelValue\": _cache[0] || (_cache[0] = function ($event) {\n              return $setup.form.name = $event;\n            }),\n            placeholder: \"请输入分组名称\",\n            clearable: \"\"\n          }, null, 8 /* PROPS */, [\"modelValue\"])];\n        }),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_form_item, {\n        label: \"上级分组\"\n      }, {\n        default: _withCtx(function () {\n          return [_createVNode(_component_el_tree_select, {\n            modelValue: $setup.form.parentId,\n            \"onUpdate:modelValue\": _cache[1] || (_cache[1] = function ($event) {\n              return $setup.form.parentId = $event;\n            }),\n            data: $setup.groupData,\n            \"check-strictly\": \"\",\n            \"node-key\": \"id\",\n            \"render-after-expand\": false,\n            props: {\n              label: 'name'\n            },\n            clearable: \"\"\n          }, null, 8 /* PROPS */, [\"modelValue\", \"data\"])];\n        }),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_form_item, {\n        label: \"序号\",\n        prop: \"sort\"\n      }, {\n        default: _withCtx(function () {\n          return [_createVNode(_component_el_input, {\n            modelValue: $setup.form.sort,\n            \"onUpdate:modelValue\": _cache[2] || (_cache[2] = function ($event) {\n              return $setup.form.sort = $event;\n            }),\n            maxlength: \"10\",\n            \"show-word-limit\": \"\",\n            onInput: _cache[3] || (_cache[3] = function ($event) {\n              return $setup.form.sort = $setup.validNum($setup.form.sort);\n            }),\n            placeholder: \"请输入序号\",\n            clearable: \"\"\n          }, null, 8 /* PROPS */, [\"modelValue\"])];\n        }),\n        _: 1 /* STABLE */\n      }), _createElementVNode(\"div\", _hoisted_2, [_createVNode(_component_el_button, {\n        type: \"primary\",\n        onClick: _cache[4] || (_cache[4] = function ($event) {\n          return $setup.submitForm($setup.formRef);\n        })\n      }, {\n        default: _withCtx(function () {\n          return _cache[5] || (_cache[5] = [_createTextVNode(\"提交\")]);\n        }),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_button, {\n        onClick: $setup.resetForm\n      }, {\n        default: _withCtx(function () {\n          return _cache[6] || (_cache[6] = [_createTextVNode(\"取消\")]);\n        }),\n        _: 1 /* STABLE */\n      })])];\n    }),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"model\", \"rules\"])]);\n}", "map": {"version": 3, "names": ["class", "_createElementBlock", "_hoisted_1", "_createVNode", "_component_el_form", "ref", "model", "$setup", "form", "rules", "inline", "default", "_withCtx", "_component_el_form_item", "label", "prop", "_component_el_input", "modelValue", "name", "_cache", "$event", "placeholder", "clearable", "_", "_component_el_tree_select", "parentId", "data", "groupData", "props", "sort", "maxlength", "onInput", "validNum", "_createElementVNode", "_hoisted_2", "_component_el_button", "type", "onClick", "submitForm", "formRef", "_createTextVNode", "resetForm"], "sources": ["D:\\zy\\xm\\pc\\5.0\\qiankun-product\\common\\components\\select-person\\submit-select-person-group.vue"], "sourcesContent": ["<template>\r\n  <div class=\"submit-select-person-group\">\r\n    <el-form ref=\"formRef\" :model=\"form\" :rules=\"rules\" inline label-position=\"top\" class=\"globalForm\">\r\n      <el-form-item label=\"分组名称\" prop=\"name\">\r\n        <el-input v-model=\"form.name\" placeholder=\"请输入分组名称\" clearable />\r\n      </el-form-item>\r\n      <el-form-item label=\"上级分组\">\r\n        <el-tree-select\r\n          v-model=\"form.parentId\"\r\n          :data=\"groupData\"\r\n          check-strictly\r\n          node-key=\"id\"\r\n          :render-after-expand=\"false\"\r\n          :props=\"{ label: 'name' }\"\r\n          clearable />\r\n      </el-form-item>\r\n      <el-form-item label=\"序号\" prop=\"sort\">\r\n        <el-input\r\n          v-model=\"form.sort\"\r\n          maxlength=\"10\"\r\n          show-word-limit\r\n          @input=\"form.sort = validNum(form.sort)\"\r\n          placeholder=\"请输入序号\"\r\n          clearable />\r\n      </el-form-item>\r\n      <div class=\"globalFormButton\">\r\n        <el-button type=\"primary\" @click=\"submitForm(formRef)\">提交</el-button>\r\n        <el-button @click=\"resetForm\">取消</el-button>\r\n      </div>\r\n    </el-form>\r\n  </div>\r\n</template>\r\n<script>\r\nexport default { name: 'SubmitSelectPersonGroup' }\r\n</script>\r\n<script setup>\r\nimport api from '@/api'\r\nimport { reactive, ref, onMounted } from 'vue'\r\nimport { validNum } from 'common/js/utils.js'\r\nimport { ElMessage } from 'element-plus'\r\nconst props = defineProps({ id: { type: String, default: '' }, typeId: { type: String, default: '' } })\r\nconst emit = defineEmits(['callback'])\r\n\r\nconst formRef = ref()\r\nconst form = reactive({\r\n  parentId: '', // 上级分组\r\n  name: '', // 分组名称\r\n  sort: '' // 排序\r\n})\r\nconst rules = reactive({\r\n  name: [{ required: true, message: '请输入分组名称', trigger: ['blur', 'change'] }],\r\n  sort: [{ required: true, message: '请输入序号', trigger: ['blur', 'change'] }]\r\n})\r\n\r\nconst groupData = ref()\r\nonMounted(() => {\r\n  chooseMineTagTree()\r\n  if (props.id) chooseMineTagInfo()\r\n})\r\n\r\nconst chooseMineTagTree = async () => {\r\n  const { data } = await api.chooseMineTagTree()\r\n  groupData.value = props.id ? filterData(data) : data\r\n}\r\nconst filterData = (list) => {\r\n  return list\r\n    .filter((item) => {\r\n      return item.id !== props.id\r\n    })\r\n    .map((item) => {\r\n      item = Object.assign({}, item)\r\n      if (item.children) {\r\n        item.children = filterData(item.children)\r\n      }\r\n      return item\r\n    })\r\n}\r\nconst chooseMineTagInfo = async () => {\r\n  const res = await api.chooseMineTagInfo({ detailId: props.id })\r\n  var { data } = res\r\n  form.parentId = data.parentId === '0' ? '' : data.parentId\r\n  form.name = data.name\r\n  form.sort = data.sort\r\n}\r\nconst submitForm = async (formEl) => {\r\n  if (!formEl) return\r\n  await formEl.validate((valid, fields) => {\r\n    if (valid) {\r\n      globalJson()\r\n    } else {\r\n      ElMessage({ type: 'warning', message: '请根据提示信息完善字段内容！' })\r\n    }\r\n  })\r\n}\r\nconst globalJson = async () => {\r\n  const { code } = await api.globalJson(props.id ? '/chooseMineTag/edit' : '/chooseMineTag/add', {\r\n    form: {\r\n      id: props.id,\r\n      typeId: props.typeId,\r\n      parentId: form.parentId || '0',\r\n      tagName: form.name,\r\n      sort: form.sort\r\n    }\r\n  })\r\n  if (code === 200) {\r\n    ElMessage({ type: 'success', message: props.id ? '编辑成功' : '新增成功' })\r\n    emit('callback')\r\n  }\r\n}\r\nconst resetForm = () => {\r\n  emit('callback')\r\n}\r\n</script>\r\n<style lang=\"scss\">\r\n.submit-select-person-group {\r\n  width: 680px;\r\n}\r\n</style>\r\n"], "mappings": ";;EACOA,KAAK,EAAC;AAA4B;;EAwB9BA,KAAK,EAAC;AAAkB;;;;;;;uBAxBjCC,mBAAA,CA6BM,OA7BNC,UA6BM,GA5BJC,YAAA,CA2BUC,kBAAA;IA3BDC,GAAG,EAAC,SAAS;IAAEC,KAAK,EAAEC,MAAA,CAAAC,IAAI;IAAGC,KAAK,EAAEF,MAAA,CAAAE,KAAK;IAAEC,MAAM,EAAN,EAAM;IAAC,gBAAc,EAAC,KAAK;IAACV,KAAK,EAAC;;IAF1FW,OAAA,EAAAC,QAAA,CAGM;MAAA,OAEe,CAFfT,YAAA,CAEeU,uBAAA;QAFDC,KAAK,EAAC,MAAM;QAACC,IAAI,EAAC;;QAHtCJ,OAAA,EAAAC,QAAA,CAIQ;UAAA,OAAgE,CAAhET,YAAA,CAAgEa,mBAAA;YAJxEC,UAAA,EAI2BV,MAAA,CAAAC,IAAI,CAACU,IAAI;YAJpC,uBAAAC,MAAA,QAAAA,MAAA,gBAAAC,MAAA;cAAA,OAI2Bb,MAAA,CAAAC,IAAI,CAACU,IAAI,GAAAE,MAAA;YAAA;YAAEC,WAAW,EAAC,SAAS;YAACC,SAAS,EAAT;;;QAJ5DC,CAAA;UAMMpB,YAAA,CASeU,uBAAA;QATDC,KAAK,EAAC;MAAM;QANhCH,OAAA,EAAAC,QAAA,CAOQ;UAAA,OAOc,CAPdT,YAAA,CAOcqB,yBAAA;YAdtBP,UAAA,EAQmBV,MAAA,CAAAC,IAAI,CAACiB,QAAQ;YARhC,uBAAAN,MAAA,QAAAA,MAAA,gBAAAC,MAAA;cAAA,OAQmBb,MAAA,CAAAC,IAAI,CAACiB,QAAQ,GAAAL,MAAA;YAAA;YACrBM,IAAI,EAAEnB,MAAA,CAAAoB,SAAS;YAChB,gBAAc,EAAd,EAAc;YACd,UAAQ,EAAC,IAAI;YACZ,qBAAmB,EAAE,KAAK;YAC1BC,KAAK,EAAE;cAAAd,KAAA;YAAA,CAAiB;YACzBQ,SAAS,EAAT;;;QAdVC,CAAA;UAgBMpB,YAAA,CAQeU,uBAAA;QARDC,KAAK,EAAC,IAAI;QAACC,IAAI,EAAC;;QAhBpCJ,OAAA,EAAAC,QAAA,CAiBQ;UAAA,OAMc,CANdT,YAAA,CAMca,mBAAA;YAvBtBC,UAAA,EAkBmBV,MAAA,CAAAC,IAAI,CAACqB,IAAI;YAlB5B,uBAAAV,MAAA,QAAAA,MAAA,gBAAAC,MAAA;cAAA,OAkBmBb,MAAA,CAAAC,IAAI,CAACqB,IAAI,GAAAT,MAAA;YAAA;YAClBU,SAAS,EAAC,IAAI;YACd,iBAAe,EAAf,EAAe;YACdC,OAAK,EAAAZ,MAAA,QAAAA,MAAA,gBAAAC,MAAA;cAAA,OAAEb,MAAA,CAAAC,IAAI,CAACqB,IAAI,GAAGtB,MAAA,CAAAyB,QAAQ,CAACzB,MAAA,CAAAC,IAAI,CAACqB,IAAI;YAAA;YACtCR,WAAW,EAAC,OAAO;YACnBC,SAAS,EAAT;;;QAvBVC,CAAA;UAyBMU,mBAAA,CAGM,OAHNC,UAGM,GAFJ/B,YAAA,CAAqEgC,oBAAA;QAA1DC,IAAI,EAAC,SAAS;QAAEC,OAAK,EAAAlB,MAAA,QAAAA,MAAA,gBAAAC,MAAA;UAAA,OAAEb,MAAA,CAAA+B,UAAU,CAAC/B,MAAA,CAAAgC,OAAO;QAAA;;QA1B5D5B,OAAA,EAAAC,QAAA,CA0B+D;UAAA,OAAEO,MAAA,QAAAA,MAAA,OA1BjEqB,gBAAA,CA0B+D,IAAE,E;;QA1BjEjB,CAAA;UA2BQpB,YAAA,CAA4CgC,oBAAA;QAAhCE,OAAK,EAAE9B,MAAA,CAAAkC;MAAS;QA3BpC9B,OAAA,EAAAC,QAAA,CA2BsC;UAAA,OAAEO,MAAA,QAAAA,MAAA,OA3BxCqB,gBAAA,CA2BsC,IAAE,E;;QA3BxCjB,CAAA;;;IAAAA,CAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
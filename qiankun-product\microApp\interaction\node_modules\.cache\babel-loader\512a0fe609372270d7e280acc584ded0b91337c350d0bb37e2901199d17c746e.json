{"ast": null, "code": "import { toDisplayString as _toDisplayString, createElementVNode as _createElementVNode, createCommentVNode as _createCommentVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, createTextVNode as _createTextVNode, resolveComponent as _resolveComponent, withCtx as _withCtx, createVNode as _createVNode, normalizeClass as _normalizeClass } from \"vue\";\nvar _hoisted_1 = {\n  class: \"LiveManagementDetails\"\n};\nvar _hoisted_2 = {\n  class: \"VideoMeetinDetailsTitle\"\n};\nvar _hoisted_3 = {\n  class: \"VideoMeetinDetailsTime\"\n};\nvar _hoisted_4 = {\n  class: \"VideoMeetinDetailsTimeLeft\"\n};\nvar _hoisted_5 = {\n  class: \"VideoMeetinDetailsTimeHours\"\n};\nvar _hoisted_6 = {\n  class: \"VideoMeetinDetailsTimeDate\"\n};\nvar _hoisted_7 = {\n  class: \"VideoMeetinDetailsTimeCenter\"\n};\nvar _hoisted_8 = {\n  class: \"VideoMeetinDetailsDuration\"\n};\nvar _hoisted_9 = {\n  class: \"VideoMeetinDetailsTimeRight\"\n};\nvar _hoisted_10 = {\n  class: \"VideoMeetinDetailsTimeHours\"\n};\nvar _hoisted_11 = {\n  class: \"VideoMeetinDetailsTimeDate\"\n};\nvar _hoisted_12 = {\n  class: \"LiveList\"\n};\nvar _hoisted_13 = {\n  class: \"LiveContent\"\n};\nvar _hoisted_14 = {\n  key: 0,\n  class: \"LiveList\"\n};\nvar _hoisted_15 = {\n  class: \"player-container\"\n};\nvar _hoisted_16 = [\"src\"];\nvar _hoisted_17 = {\n  class: \"LiveList\"\n};\nvar _hoisted_18 = {\n  class: \"LiveInteractionBox\"\n};\nvar _hoisted_19 = {\n  class: \"LiveInteraction\"\n};\nvar _hoisted_20 = {\n  class: \"LiveInteractionTotal\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  var _component_el_button = _resolveComponent(\"el-button\");\n  var _component_el_scrollbar = _resolveComponent(\"el-scrollbar\");\n  var _component_anchor_location = _resolveComponent(\"anchor-location\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createVNode(_component_anchor_location, null, {\n    default: _withCtx(function () {\n      var _$setup$details;\n      return [_createElementVNode(\"div\", _hoisted_2, _toDisplayString((_$setup$details = $setup.details) === null || _$setup$details === void 0 ? void 0 : _$setup$details.theme), 1 /* TEXT */), _createElementVNode(\"div\", _hoisted_3, [_createElementVNode(\"div\", _hoisted_4, [_createElementVNode(\"div\", _hoisted_5, _toDisplayString($setup.format($setup.details.startTime, 'HH:mm')), 1 /* TEXT */), _createElementVNode(\"div\", _hoisted_6, _toDisplayString($setup.format($setup.details.startTime, 'YYYY年MM月DD日')), 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_7, [_createElementVNode(\"div\", _hoisted_8, _toDisplayString($setup.details.during) + \"分钟\", 1 /* TEXT */), _createCommentVNode(\" <div class=\\\"VideoMeetinDetailsStatus\\\">{{ details.meetingStatus }}</div> \")]), _createElementVNode(\"div\", _hoisted_9, [_createElementVNode(\"div\", _hoisted_10, _toDisplayString($setup.format($setup.details.endTime, 'HH:mm')), 1 /* TEXT */), _createElementVNode(\"div\", _hoisted_11, _toDisplayString($setup.format($setup.details.endTime, 'YYYY年MM月DD日')), 1 /* TEXT */)])]), _createElementVNode(\"div\", _hoisted_12, [_cache[1] || (_cache[1] = _createElementVNode(\"div\", {\n        class: \"LiveName\"\n      }, \"直播简介\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_13, _toDisplayString($setup.details.liveDescribes), 1 /* TEXT */)]), $setup.details.isReplay == 1 && $setup.details.liveReplayUrl ? (_openBlock(), _createElementBlock(\"div\", _hoisted_14, [_cache[2] || (_cache[2] = _createElementVNode(\"div\", {\n        class: \"LiveName\"\n      }, \"直播回放\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_15, [_createElementVNode(\"video\", {\n        ref: \"videoPlayer\",\n        id: \"video-player\",\n        src: $setup.details.liveReplayUrl,\n        controls: \"\"\n      }, null, 8 /* PROPS */, _hoisted_16)])])) : _createCommentVNode(\"v-if\", true), _createElementVNode(\"div\", _hoisted_17, [_createElementVNode(\"div\", _hoisted_18, [_cache[4] || (_cache[4] = _createElementVNode(\"div\", {\n        class: \"LiveName\",\n        style: {\n          \"margin-top\": \"0\"\n        }\n      }, \"直播互动\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_19, [_createElementVNode(\"div\", _hoisted_20, \"(共\" + _toDisplayString(_ctx.total) + \"条互动消息)\", 1 /* TEXT */), _createVNode(_component_el_button, {\n        type: \"primary\",\n        onClick: _cache[0] || (_cache[0] = function ($event) {\n          return $setup.excelMsg();\n        })\n      }, {\n        default: _withCtx(function () {\n          return _cache[3] || (_cache[3] = [_createTextVNode(\"导出互动消息\")]);\n        }),\n        _: 1 /* STABLE */\n      })])]), _createVNode(_component_el_scrollbar, {\n        class: _normalizeClass([\"xyl-global-comment-scrollbar\", {\n          'xyl-global-comment-scroll': _ctx.props.scroll\n        }])\n      }, null, 8 /* PROPS */, [\"class\"])])];\n    }),\n    _: 1 /* STABLE */\n  })]);\n}", "map": {"version": 3, "names": ["class", "key", "_createElementBlock", "_hoisted_1", "_createVNode", "_component_anchor_location", "default", "_withCtx", "_$setup$details", "_createElementVNode", "_hoisted_2", "_toDisplayString", "$setup", "details", "theme", "_hoisted_3", "_hoisted_4", "_hoisted_5", "format", "startTime", "_hoisted_6", "_hoisted_7", "_hoisted_8", "during", "_createCommentVNode", "_hoisted_9", "_hoisted_10", "endTime", "_hoisted_11", "_hoisted_12", "_hoisted_13", "liveDescribes", "isReplay", "liveReplayUrl", "_hoisted_14", "_hoisted_15", "ref", "id", "src", "controls", "_hoisted_16", "_hoisted_17", "_hoisted_18", "style", "_hoisted_19", "_hoisted_20", "_ctx", "total", "_component_el_button", "type", "onClick", "_cache", "$event", "excelMsg", "_createTextVNode", "_", "_component_el_scrollbar", "_normalizeClass", "props", "scroll"], "sources": ["D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\interaction\\src\\views\\LiveManagement\\LiveManagementDetails.vue"], "sourcesContent": ["<template>\r\n  <div class=\"LiveManagementDetails\">\r\n    <anchor-location>\r\n      <div class=\"VideoMeetinDetailsTitle\">{{ details?.theme }}</div>\r\n      <div class=\"VideoMeetinDetailsTime\">\r\n        <div class=\"VideoMeetinDetailsTimeLeft\">\r\n          <div class=\"VideoMeetinDetailsTimeHours\">{{ format(details.startTime, 'HH:mm') }}</div>\r\n          <div class=\"VideoMeetinDetailsTimeDate\">{{ format(details.startTime, 'YYYY年MM月DD日') }}</div>\r\n        </div>\r\n        <div class=\"VideoMeetinDetailsTimeCenter\">\r\n          <div class=\"VideoMeetinDetailsDuration\">{{ details.during }}分钟</div>\r\n          <!-- <div class=\"VideoMeetinDetailsStatus\">{{ details.meetingStatus }}</div> -->\r\n        </div>\r\n        <div class=\"VideoMeetinDetailsTimeRight\">\r\n          <div class=\"VideoMeetinDetailsTimeHours\">{{ format(details.endTime, 'HH:mm') }}</div>\r\n          <div class=\"VideoMeetinDetailsTimeDate\">{{ format(details.endTime, 'YYYY年MM月DD日') }}</div>\r\n        </div>\r\n      </div>\r\n      <div class=\"LiveList\">\r\n        <div class=\"LiveName\">直播简介</div>\r\n        <div class=\"LiveContent\">{{ details.liveDescribes }}</div>\r\n      </div>\r\n      <div class=\"LiveList\" v-if=\"details.isReplay == 1 && details.liveReplayUrl\">\r\n        <div class=\"LiveName\">直播回放</div>\r\n        <div class=\"player-container\">\r\n          <video ref=\"videoPlayer\" id=\"video-player\" :src=\"details.liveReplayUrl\" controls></video>\r\n        </div>\r\n      </div>\r\n      <div class=\"LiveList\">\r\n        <div class=\"LiveInteractionBox\">\r\n          <div class=\"LiveName\" style=\"margin-top: 0;\">直播互动</div>\r\n          <div class=\"LiveInteraction\">\r\n            <div class=\"LiveInteractionTotal\">(共{{ total }}条互动消息)</div>\r\n            <el-button type=\"primary\" @click=\"excelMsg()\">导出互动消息</el-button>\r\n          </div>\r\n        </div>\r\n        <el-scrollbar class=\"xyl-global-comment-scrollbar\" :class=\"{ 'xyl-global-comment-scroll': props.scroll }\">\r\n\r\n        </el-scrollbar>\r\n\r\n      </div>\r\n    </anchor-location>\r\n  </div>\r\n</template>\r\n<script>\r\nexport default { name: 'LiveManagementDetails' }\r\n</script>\r\n<script setup>\r\nimport api from '@/api'\r\nimport { ref, onMounted } from 'vue'\r\nimport { useRoute } from 'vue-router'\r\nimport { format } from 'common/js/time.js'\r\n\r\nconst route = useRoute()\r\nconst details = ref({})\r\nonMounted(() => {\r\n  if (route.query.id) { videoConnectionInfo() }\r\n})\r\nconst videoConnectionInfo = async () => {\r\n  const res = await api.videoConnectionInfo({ detailId: route.query.id })\r\n  var { data } = res\r\n  details.value = data\r\n  getCommentList()\r\n}\r\nconst excelMsg = () => {\r\n  console.log('导出互动消息')\r\n}\r\nconst getCommentList = () => {\r\n  console.log('getCommentList')\r\n}\r\n</script>\r\n<style lang=\"scss\">\r\n.LiveManagementDetails {\r\n  width: 100%;\r\n  height: 100%;\r\n  padding: 0 20px;\r\n\r\n  .anchor-location-body {\r\n    padding: var(--zy-distance-one);\r\n  }\r\n\r\n  .VideoMeetinDetailsTitle {\r\n    text-align: center;\r\n    font-weight: bold;\r\n    font-size: var(--zy-title-font-size);\r\n    padding: var(--zy-distance-two) var(--zy-distance-one);\r\n    line-height: var(--zy-line-height);\r\n  }\r\n\r\n  .VideoMeetinDetailsTime {\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: space-evenly;\r\n    padding: var(--zy-distance-two) 0;\r\n\r\n    .VideoMeetinDetailsTimeLeft,\r\n    .VideoMeetinDetailsTimeRight {\r\n      text-align: center;\r\n      padding: 0 var(--zy-distance-one);\r\n    }\r\n\r\n    .VideoMeetinDetailsTimeCenter {\r\n      width: 99px;\r\n      height: 99px;\r\n      display: flex;\r\n      align-items: center;\r\n      flex-direction: column;\r\n      justify-content: center;\r\n      border-radius: 50%;\r\n      border: 1px solid var(--zy-el-color-primary);\r\n      background: var(--zy-el-color-primary-light-9);\r\n      position: relative;\r\n\r\n      &::after {\r\n        content: '';\r\n        position: absolute;\r\n        width: 99px;\r\n        height: 1px;\r\n        background: linear-gradient(90deg, var(--zy-el-color-primary), #fff);\r\n        top: 50%;\r\n        right: 0;\r\n        transform: translate(100%, -50%);\r\n      }\r\n\r\n      &::before {\r\n        content: '';\r\n        position: absolute;\r\n        width: 99px;\r\n        height: 1px;\r\n        background: linear-gradient(90deg, #fff, var(--zy-el-color-primary));\r\n        top: 50%;\r\n        left: 0;\r\n        transform: translate(-100%, -50%);\r\n      }\r\n\r\n      .VideoMeetinDetailsDuration {\r\n        font-weight: bold;\r\n        color: var(--zy-el-color-primary);\r\n        font-size: calc(var(--zy-name-font-size) + 2px);\r\n      }\r\n\r\n      .VideoMeetinDetailsStatus {\r\n        color: var(--zy-el-color-primary);\r\n        font-size: var(--zy-name-font-size);\r\n      }\r\n    }\r\n\r\n    .VideoMeetinDetailsTimeHours {\r\n      font-weight: bold;\r\n      font-size: calc(var(--zy-name-font-size) + 6px);\r\n    }\r\n\r\n    .VideoMeetinDetailsTimeDate {\r\n      font-size: var(--zy-name-font-size);\r\n    }\r\n  }\r\n\r\n  .LiveList {\r\n    width: 100%;\r\n    // display: flex;\r\n    // flex-wrap: wrap;\r\n    padding: 0 var(--zy-distance-one);\r\n\r\n    .LiveName {\r\n      font-weight: bold;\r\n      font-size: 20px;\r\n      line-height: var(--zy-line-height);\r\n      margin-top: 10px;\r\n    }\r\n\r\n    .LiveContent {\r\n      font-size: 16px;\r\n      line-height: var(--zy-line-height);\r\n      text-indent: 2em;\r\n      margin-top: 5px\r\n    }\r\n\r\n    .player-container {\r\n      width: 100%;\r\n      height: 100%;\r\n      margin-top: 5px;\r\n\r\n      #video-player {\r\n        width: 100%;\r\n        height: 100%;\r\n      }\r\n    }\r\n\r\n    .LiveInteractionBox {\r\n      display: flex;\r\n      align-items: center;\r\n      margin-top: 10px;\r\n      justify-content: space-between;\r\n      width: 100%;\r\n    }\r\n\r\n    .LiveInteraction {\r\n      display: flex;\r\n      align-items: center;\r\n\r\n      .LiveInteractionTotal {\r\n        margin-right: 10px;\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;EACOA,KAAK,EAAC;AAAuB;;EAEzBA,KAAK,EAAC;AAAyB;;EAC/BA,KAAK,EAAC;AAAwB;;EAC5BA,KAAK,EAAC;AAA4B;;EAChCA,KAAK,EAAC;AAA6B;;EACnCA,KAAK,EAAC;AAA4B;;EAEpCA,KAAK,EAAC;AAA8B;;EAClCA,KAAK,EAAC;AAA4B;;EAGpCA,KAAK,EAAC;AAA6B;;EACjCA,KAAK,EAAC;AAA6B;;EACnCA,KAAK,EAAC;AAA4B;;EAGtCA,KAAK,EAAC;AAAU;;EAEdA,KAAK,EAAC;AAAa;;EApBhCC,GAAA;EAsBWD,KAAK,EAAC;;;EAEJA,KAAK,EAAC;AAAkB;kBAxBrC;;EA4BWA,KAAK,EAAC;AAAU;;EACdA,KAAK,EAAC;AAAoB;;EAExBA,KAAK,EAAC;AAAiB;;EACrBA,KAAK,EAAC;AAAsB;;;;;uBA/B3CE,mBAAA,CAyCM,OAzCNC,UAyCM,GAxCJC,YAAA,CAuCkBC,0BAAA;IAzCtBC,OAAA,EAAAC,QAAA,CAGM;MAAA,IAAAC,eAAA;MAAA,OAA+D,CAA/DC,mBAAA,CAA+D,OAA/DC,UAA+D,EAAAC,gBAAA,EAAAH,eAAA,GAAvBI,MAAA,CAAAC,OAAO,cAAAL,eAAA,uBAAPA,eAAA,CAASM,KAAK,kBACtDL,mBAAA,CAaM,OAbNM,UAaM,GAZJN,mBAAA,CAGM,OAHNO,UAGM,GAFJP,mBAAA,CAAuF,OAAvFQ,UAAuF,EAAAN,gBAAA,CAA3CC,MAAA,CAAAM,MAAM,CAACN,MAAA,CAAAC,OAAO,CAACM,SAAS,4BACpEV,mBAAA,CAA4F,OAA5FW,UAA4F,EAAAT,gBAAA,CAAjDC,MAAA,CAAAM,MAAM,CAACN,MAAA,CAAAC,OAAO,CAACM,SAAS,iC,GAErEV,mBAAA,CAGM,OAHNY,UAGM,GAFJZ,mBAAA,CAAoE,OAApEa,UAAoE,EAAAX,gBAAA,CAAzBC,MAAA,CAAAC,OAAO,CAACU,MAAM,IAAG,IAAE,iBAC9DC,mBAAA,+EAAgF,C,GAElFf,mBAAA,CAGM,OAHNgB,UAGM,GAFJhB,mBAAA,CAAqF,OAArFiB,WAAqF,EAAAf,gBAAA,CAAzCC,MAAA,CAAAM,MAAM,CAACN,MAAA,CAAAC,OAAO,CAACc,OAAO,4BAClElB,mBAAA,CAA0F,OAA1FmB,WAA0F,EAAAjB,gBAAA,CAA/CC,MAAA,CAAAM,MAAM,CAACN,MAAA,CAAAC,OAAO,CAACc,OAAO,iC,KAGrElB,mBAAA,CAGM,OAHNoB,WAGM,G,0BAFJpB,mBAAA,CAAgC;QAA3BT,KAAK,EAAC;MAAU,GAAC,MAAI,sBAC1BS,mBAAA,CAA0D,OAA1DqB,WAA0D,EAAAnB,gBAAA,CAA9BC,MAAA,CAAAC,OAAO,CAACkB,aAAa,iB,GAEvBnB,MAAA,CAAAC,OAAO,CAACmB,QAAQ,SAASpB,MAAA,CAAAC,OAAO,CAACoB,aAAa,I,cAA1E/B,mBAAA,CAKM,OALNgC,WAKM,G,0BAJJzB,mBAAA,CAAgC;QAA3BT,KAAK,EAAC;MAAU,GAAC,MAAI,sBAC1BS,mBAAA,CAEM,OAFN0B,WAEM,GADJ1B,mBAAA,CAAyF;QAAlF2B,GAAG,EAAC,aAAa;QAACC,EAAE,EAAC,cAAc;QAAEC,GAAG,EAAE1B,MAAA,CAAAC,OAAO,CAACoB,aAAa;QAAEM,QAAQ,EAAR;8BAzBlFC,WAAA,E,OAAAhB,mBAAA,gBA4BMf,mBAAA,CAYM,OAZNgC,WAYM,GAXJhC,mBAAA,CAMM,OANNiC,WAMM,G,0BALJjC,mBAAA,CAAuD;QAAlDT,KAAK,EAAC,UAAU;QAAC2C,KAAsB,EAAtB;UAAA;QAAA;SAAuB,MAAI,sBACjDlC,mBAAA,CAGM,OAHNmC,WAGM,GAFJnC,mBAAA,CAA2D,OAA3DoC,WAA2D,EAAzB,IAAE,GAAAlC,gBAAA,CAAGmC,IAAA,CAAAC,KAAK,IAAG,QAAM,iBACrD3C,YAAA,CAAgE4C,oBAAA;QAArDC,IAAI,EAAC,SAAS;QAAEC,OAAK,EAAAC,MAAA,QAAAA,MAAA,gBAAAC,MAAA;UAAA,OAAExC,MAAA,CAAAyC,QAAQ;QAAA;;QAjCtD/C,OAAA,EAAAC,QAAA,CAiC0D;UAAA,OAAM4C,MAAA,QAAAA,MAAA,OAjChEG,gBAAA,CAiC0D,QAAM,E;;QAjChEC,CAAA;cAoCQnD,YAAA,CAEeoD,uBAAA;QAFDxD,KAAK,EApC3ByD,eAAA,EAoC4B,8BAA8B;UAAA,6BAAwCX,IAAA,CAAAY,KAAK,CAACC;QAAM;;;IApC9GJ,CAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "import { defineComponent, h } from 'vue';\nimport '../../../hooks/index.mjs';\nimport { useNamespace } from '../../../hooks/use-namespace/index.mjs';\nvar NodeContent = defineComponent({\n  name: \"NodeContent\",\n  setup() {\n    var ns = useNamespace(\"cascader-node\");\n    return {\n      ns\n    };\n  },\n  render() {\n    var ns = this.ns;\n    var _this$$parent = this.$parent,\n      node = _this$$parent.node,\n      panel = _this$$parent.panel;\n    var data = node.data,\n      label = node.label;\n    var renderLabelFn = panel.renderLabelFn;\n    return h(\"span\", {\n      class: ns.e(\"label\")\n    }, renderLabelFn ? renderLabelFn({\n      node,\n      data\n    }) : label);\n  }\n});\nexport { NodeContent as default };", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "defineComponent", "name", "setup", "ns", "useNamespace", "render", "_this$$parent", "$parent", "node", "panel", "data", "label", "renderLabelFn", "h", "class", "e"], "sources": ["../../../../../../packages/components/cascader-panel/src/node-content.ts"], "sourcesContent": ["// @ts-nocheck\nimport { defineComponent, h } from 'vue'\nimport { useNamespace } from '@element-plus/hooks'\nexport default defineComponent({\n  name: 'NodeContent',\n  setup() {\n    const ns = useNamespace('cascader-node')\n    return {\n      ns,\n    }\n  },\n  render() {\n    const { ns } = this\n    const { node, panel } = this.$parent\n    const { data, label } = node\n    const { renderLabelFn } = panel\n    return h(\n      'span',\n      { class: ns.e('label') },\n      renderLabelFn ? renderLabelFn({ node, data }) : label\n    )\n  },\n})\n"], "mappings": ";;;AAEA,IAAAA,WAAA,GAAeC,eAAe,CAAC;EAC7BC,IAAI,EAAE,aAAa;EACnBC,KAAKA,CAAA,EAAG;IACN,IAAMC,EAAE,GAAGC,YAAY,CAAC,eAAe,CAAC;IACxC,OAAO;MACLD;IACN,CAAK;EACL,CAAG;EACDE,MAAMA,CAAA,EAAG;IACP,IAAQF,EAAE,GAAK,IAAI,CAAXA,EAAE;IACV,IAAAG,aAAA,GAAwB,IAAI,CAACC,OAAO;MAA5BC,IAAI,GAAAF,aAAA,CAAJE,IAAI;MAAEC,KAAK,GAAAH,aAAA,CAALG,KAAK;IACnB,IAAQC,IAAI,GAAYF,IAAI,CAApBE,IAAI;MAAEC,KAAK,GAAKH,IAAI,CAAdG,KAAK;IACnB,IAAQC,aAAa,GAAKH,KAAK,CAAvBG,aAAa;IACrB,OAAOC,CAAC,CAAC,MAAM,EAAE;MAAEC,KAAK,EAAEX,EAAE,CAACY,CAAC,CAAC,OAAO;IAAC,CAAE,EAAEH,aAAa,GAAGA,aAAa,CAAC;MAAEJ,IAAI;MAAEE;IAAI,CAAE,CAAC,GAAGC,KAAK,CAAC;EACrG;AACA,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
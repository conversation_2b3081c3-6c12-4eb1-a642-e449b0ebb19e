{"ast": null, "code": "import { ref, reactive, watch, nextTick, onMounted } from 'vue';\nimport { encryptPhone } from 'common/js/utils.js';\nimport { systemMobileEncrypt } from 'common/js/system_var.js';\nimport elementResizeDetectorMaker from 'element-resize-detector';\nvar __default__ = {\n  name: 'VirtualSelectUser'\n};\nexport default /*@__PURE__*/Object.assign(__default__, {\n  props: {\n    data: {\n      type: Array,\n      default: function _default() {\n        return [];\n      }\n    }\n  },\n  emits: ['handleDel'],\n  setup(__props, _ref) {\n    var __expose = _ref.expose,\n      __emit = _ref.emit;\n    __expose();\n    var erd = elementResizeDetectorMaker();\n    var props = __props;\n    var emit = __emit;\n    var virtualList = ref();\n    var virtualScrollbar = ref();\n    var virtualPlaceholder = ref();\n    // 组件记录(默认)\n    var virtualRecord = reactive({\n      height: 400,\n      // 展示几个\n      visibleCount: 16,\n      // 刷新频率\n      timeout: 4,\n      // 行高\n      itemHeight: 90,\n      // translateY偏移量\n      offset: 0,\n      // 虚拟占位高度\n      virtualHeight: 300,\n      // 记录滚动高度\n      recordScrollTop: 0,\n      dataList: [],\n      // 可展示的数据\n      visibleData: []\n    });\n    // 合并配置\n    var mergeFn = function mergeFn() {\n      virtualRecord.dataList = JSON.parse(JSON.stringify(props.data));\n      // 虚拟高度\n      virtualRecord.virtualHeight = props.data.length * virtualRecord.itemHeight;\n      // 展示数量\n      virtualRecord.visibleCount = Math.ceil(virtualRecord.height / virtualRecord.itemHeight);\n    };\n    var lastTime = 0;\n    var handleScroll = function handleScroll(scroll) {\n      var currentTime = +new Date();\n      if (currentTime - lastTime > virtualRecord.timeout) {\n        virtualRecord.recordScrollTop = scroll.scrollTop;\n        updateVisibleData(scroll.scrollTop);\n        lastTime = currentTime;\n      }\n    };\n    var updateVisibleData = function updateVisibleData(scrollTop) {\n      var start = Math.floor(scrollTop / virtualRecord.itemHeight) - Math.floor(virtualRecord.visibleCount / 2);\n      start = start < 0 ? 0 : start;\n      var end = start + virtualRecord.visibleCount * 2;\n      virtualRecord.visibleData = virtualRecord.dataList.slice(start, end);\n      virtualRecord.offset = start * virtualRecord.itemHeight;\n      nextTick(function () {\n        virtualScrollbar.value.update();\n      });\n    };\n    var deleteclick = function deleteclick(data) {\n      emit('handleDel', data);\n    };\n    var handleMobile = function handleMobile(mobile) {\n      return systemMobileEncrypt.value ? encryptPhone(mobile) : mobile;\n    };\n    watch(function () {\n      return props.data;\n    }, function () {\n      // 合并数据\n      mergeFn();\n      // 更新视图\n      updateVisibleData(virtualRecord.recordScrollTop);\n    }, {\n      immediate: true,\n      deep: true\n    });\n    onMounted(function () {\n      nextTick(function () {\n        erd.listenTo(virtualList.value, function (e) {\n          virtualRecord.height = e.offsetHeight;\n          // 合并数据\n          mergeFn();\n          // 更新视图\n          updateVisibleData(virtualRecord.recordScrollTop);\n        });\n        erd.listenTo(virtualPlaceholder.value, function (e) {\n          virtualRecord.itemHeight = e.offsetHeight;\n          // 合并数据\n          mergeFn();\n          // 更新视图\n          updateVisibleData(virtualRecord.recordScrollTop);\n        });\n      });\n    });\n    var __returned__ = {\n      erd,\n      props,\n      emit,\n      virtualList,\n      virtualScrollbar,\n      virtualPlaceholder,\n      virtualRecord,\n      mergeFn,\n      get lastTime() {\n        return lastTime;\n      },\n      set lastTime(v) {\n        lastTime = v;\n      },\n      handleScroll,\n      updateVisibleData,\n      deleteclick,\n      handleMobile,\n      ref,\n      reactive,\n      watch,\n      nextTick,\n      onMounted,\n      get encryptPhone() {\n        return encryptPhone;\n      },\n      get systemMobileEncrypt() {\n        return systemMobileEncrypt;\n      },\n      get elementResizeDetectorMaker() {\n        return elementResizeDetectorMaker;\n      }\n    };\n    Object.defineProperty(__returned__, '__isScriptSetup', {\n      enumerable: false,\n      value: true\n    });\n    return __returned__;\n  }\n});", "map": {"version": 3, "names": ["ref", "reactive", "watch", "nextTick", "onMounted", "encryptPhone", "systemMobileEncrypt", "elementResizeDetectorMaker", "__default__", "name", "erd", "props", "__props", "emit", "__emit", "virtualList", "virtualScrollbar", "virtualPlaceholder", "virtualRecord", "height", "visibleCount", "timeout", "itemHeight", "offset", "virtualHeight", "recordScrollTop", "dataList", "visibleData", "mergeFn", "JSON", "parse", "stringify", "data", "length", "Math", "ceil", "lastTime", "handleScroll", "scroll", "currentTime", "Date", "scrollTop", "updateVisibleData", "start", "floor", "end", "slice", "value", "update", "deleteclick", "handleMobile", "mobile", "immediate", "deep", "listenTo", "e", "offsetHeight"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/common/components/virtualElement/virtual-select-user.vue"], "sourcesContent": ["<template>\r\n  <div class=\"virtual-select-user\" ref=\"virtualList\">\r\n    <div class=\"virtual-placeholder\" ref=\"virtualPlaceholder\">\r\n      <div class=\"virtual-select-user-item\">\r\n        <div class=\"virtual-select-user-name ellipsis\"></div>\r\n        <div class=\"virtual-select-user-text ellipsis\"></div>\r\n        <div class=\"virtual-select-user-del\">\r\n          <el-icon>\r\n            <Close />\r\n          </el-icon>\r\n        </div>\r\n      </div>\r\n    </div>\r\n    <el-scrollbar @scroll=\"handleScroll\" ref=\"virtualScrollbar\">\r\n      <!-- 虚拟高度 -->\r\n      <div class=\"virtualBody\" :style=\"{ height: virtualRecord.virtualHeight + 'px' }\"></div>\r\n      <!-- 真实列表 -->\r\n      <div :class=\"['realBody']\" :style=\"{ transform: `translateY(${virtualRecord.offset}px)` }\">\r\n        <div\r\n          class=\"virtual-select-user-item\"\r\n          v-for=\"(item, index) in virtualRecord.visibleData\"\r\n          :key=\"index + 'virtual-select-user_'\">\r\n          <div class=\"virtual-select-user-name ellipsis\" :title=\"`${item.userName} - ${handleMobile(item.mobile)}`\">\r\n            {{ item.userName }} - {{ handleMobile(item.mobile) }}\r\n          </div>\r\n          <div class=\"virtual-select-user-text ellipsis\">{{ item.position }}</div>\r\n          <div class=\"virtual-select-user-del\" @click=\"deleteclick(item)\">\r\n            <el-icon>\r\n              <Close />\r\n            </el-icon>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </el-scrollbar>\r\n  </div>\r\n</template>\r\n<script>\r\nexport default { name: 'VirtualSelectUser' }\r\n</script>\r\n<script setup>\r\nimport { ref, reactive, watch, nextTick, onMounted } from 'vue'\r\nimport { encryptPhone } from 'common/js/utils.js'\r\nimport { systemMobileEncrypt } from 'common/js/system_var.js'\r\nimport elementResizeDetectorMaker from 'element-resize-detector'\r\nconst erd = elementResizeDetectorMaker()\r\nconst props = defineProps({ data: { type: Array, default: () => [] } })\r\nconst emit = defineEmits(['handleDel'])\r\nconst virtualList = ref()\r\nconst virtualScrollbar = ref()\r\nconst virtualPlaceholder = ref()\r\n// 组件记录(默认)\r\nconst virtualRecord = reactive({\r\n  height: 400,\r\n  // 展示几个\r\n  visibleCount: 16,\r\n  // 刷新频率\r\n  timeout: 4,\r\n  // 行高\r\n  itemHeight: 90,\r\n  // translateY偏移量\r\n  offset: 0,\r\n  // 虚拟占位高度\r\n  virtualHeight: 300,\r\n  // 记录滚动高度\r\n  recordScrollTop: 0,\r\n  dataList: [],\r\n  // 可展示的数据\r\n  visibleData: []\r\n})\r\n// 合并配置\r\nconst mergeFn = () => {\r\n  virtualRecord.dataList = JSON.parse(JSON.stringify(props.data))\r\n  // 虚拟高度\r\n  virtualRecord.virtualHeight = props.data.length * virtualRecord.itemHeight\r\n  // 展示数量\r\n  virtualRecord.visibleCount = Math.ceil(virtualRecord.height / virtualRecord.itemHeight)\r\n}\r\nlet lastTime = 0\r\nconst handleScroll = (scroll) => {\r\n  const currentTime = +new Date()\r\n  if (currentTime - lastTime > virtualRecord.timeout) {\r\n    virtualRecord.recordScrollTop = scroll.scrollTop\r\n    updateVisibleData(scroll.scrollTop)\r\n    lastTime = currentTime\r\n  }\r\n}\r\nconst updateVisibleData = (scrollTop) => {\r\n  let start = Math.floor(scrollTop / virtualRecord.itemHeight) - Math.floor(virtualRecord.visibleCount / 2)\r\n  start = start < 0 ? 0 : start\r\n  const end = start + virtualRecord.visibleCount * 2\r\n  virtualRecord.visibleData = virtualRecord.dataList.slice(start, end)\r\n  virtualRecord.offset = start * virtualRecord.itemHeight\r\n  nextTick(() => {\r\n    virtualScrollbar.value.update()\r\n  })\r\n}\r\nconst deleteclick = (data) => {\r\n  emit('handleDel', data)\r\n}\r\nconst handleMobile = (mobile) => (systemMobileEncrypt.value ? encryptPhone(mobile) : mobile)\r\nwatch(\r\n  () => props.data,\r\n  () => {\r\n    // 合并数据\r\n    mergeFn()\r\n    // 更新视图\r\n    updateVisibleData(virtualRecord.recordScrollTop)\r\n  },\r\n  { immediate: true, deep: true }\r\n)\r\nonMounted(() => {\r\n  nextTick(() => {\r\n    erd.listenTo(virtualList.value, (e) => {\r\n      virtualRecord.height = e.offsetHeight\r\n      // 合并数据\r\n      mergeFn()\r\n      // 更新视图\r\n      updateVisibleData(virtualRecord.recordScrollTop)\r\n    })\r\n    erd.listenTo(virtualPlaceholder.value, (e) => {\r\n      virtualRecord.itemHeight = e.offsetHeight\r\n      // 合并数据\r\n      mergeFn()\r\n      // 更新视图\r\n      updateVisibleData(virtualRecord.recordScrollTop)\r\n    })\r\n  })\r\n})\r\n</script>\r\n<style lang=\"scss\">\r\n.virtual-select-user {\r\n  width: 100%;\r\n  height: calc(100% - (var(--zy-height) + var(--zy-distance-three)));\r\n\r\n  .virtual-placeholder {\r\n    position: fixed;\r\n    top: -200%;\r\n    left: -200%;\r\n  }\r\n\r\n  .zy-el-scrollbar {\r\n    height: 100%;\r\n\r\n    .zy-el-scrollbar__view {\r\n      position: relative;\r\n    }\r\n\r\n    .virtualBody {\r\n      width: 100%;\r\n      position: absolute;\r\n      z-index: -10;\r\n    }\r\n\r\n    .realBody {\r\n      width: 100%;\r\n      position: absolute;\r\n    }\r\n  }\r\n\r\n  .virtual-select-user-item + .virtual-select-user-item {\r\n    margin-top: 12px;\r\n  }\r\n\r\n  .virtual-select-user-item {\r\n    width: 290px;\r\n    background: var(--el-color-info-light-9);\r\n    padding: var(--zy-distance-five) var(--zy-distance-one);\r\n    cursor: pointer;\r\n    position: relative;\r\n    border-radius: var(--el-border-radius-small);\r\n\r\n    &::after {\r\n      content: '';\r\n      position: absolute;\r\n      top: calc(var(--zy-distance-five) + var(--zy-font-text-distance-five));\r\n      left: var(--zy-distance-two);\r\n      transform: translateX(-50%);\r\n      width: var(--zy-text-font-size);\r\n      height: calc(var(--zy-text-font-size) * var(--zy-line-height));\r\n      background: url('./img/select_person_user_icon.png') no-repeat;\r\n      background-size: var(--zy-text-font-size) var(--zy-text-font-size);\r\n      background-position: center;\r\n    }\r\n\r\n    .virtual-select-user-del {\r\n      position: absolute;\r\n      top: 0;\r\n      right: 0;\r\n      width: var(--zy-distance-one);\r\n      height: 100%;\r\n      padding-top: var(--zy-distance-five);\r\n      text-align: center;\r\n      font-size: var(--zy-navigation-font-size);\r\n      line-height: var(--zy-line-height);\r\n    }\r\n\r\n    .virtual-select-user-name {\r\n      font-size: var(--zy-text-font-size);\r\n      line-height: var(--zy-line-height);\r\n      padding: var(--zy-font-text-distance-five) 0;\r\n      height: calc(var(--zy-text-font-size) * var(--zy-line-height));\r\n      box-sizing: content-box;\r\n    }\r\n\r\n    .virtual-select-user-text {\r\n      font-size: var(--zy-text-font-size);\r\n      line-height: var(--zy-line-height);\r\n      padding: var(--zy-font-text-distance-five) 0;\r\n      height: calc(var(--zy-text-font-size) * var(--zy-line-height));\r\n      color: var(--zy-el-text-color-secondary);\r\n      box-sizing: content-box;\r\n    }\r\n  }\r\n}\r\n</style>\r\n"], "mappings": "AAwCA,SAASA,GAAG,EAAEC,QAAQ,EAAEC,KAAK,EAAEC,QAAQ,EAAEC,SAAS,QAAQ,KAAK;AAC/D,SAASC,YAAY,QAAQ,oBAAoB;AACjD,SAASC,mBAAmB,QAAQ,yBAAyB;AAC7D,OAAOC,0BAA0B,MAAM,yBAAyB;AANhE,IAAAC,WAAA,GAAe;EAAEC,IAAI,EAAE;AAAoB,CAAC;;;;;;;;;;;;;;;IAO5C,IAAMC,GAAG,GAAGH,0BAA0B,CAAC,CAAC;IACxC,IAAMI,KAAK,GAAGC,OAAyD;IACvE,IAAMC,IAAI,GAAGC,MAA0B;IACvC,IAAMC,WAAW,GAAGf,GAAG,CAAC,CAAC;IACzB,IAAMgB,gBAAgB,GAAGhB,GAAG,CAAC,CAAC;IAC9B,IAAMiB,kBAAkB,GAAGjB,GAAG,CAAC,CAAC;IAChC;IACA,IAAMkB,aAAa,GAAGjB,QAAQ,CAAC;MAC7BkB,MAAM,EAAE,GAAG;MACX;MACAC,YAAY,EAAE,EAAE;MAChB;MACAC,OAAO,EAAE,CAAC;MACV;MACAC,UAAU,EAAE,EAAE;MACd;MACAC,MAAM,EAAE,CAAC;MACT;MACAC,aAAa,EAAE,GAAG;MAClB;MACAC,eAAe,EAAE,CAAC;MAClBC,QAAQ,EAAE,EAAE;MACZ;MACAC,WAAW,EAAE;IACf,CAAC,CAAC;IACF;IACA,IAAMC,OAAO,GAAG,SAAVA,OAAOA,CAAA,EAAS;MACpBV,aAAa,CAACQ,QAAQ,GAAGG,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,SAAS,CAACpB,KAAK,CAACqB,IAAI,CAAC,CAAC;MAC/D;MACAd,aAAa,CAACM,aAAa,GAAGb,KAAK,CAACqB,IAAI,CAACC,MAAM,GAAGf,aAAa,CAACI,UAAU;MAC1E;MACAJ,aAAa,CAACE,YAAY,GAAGc,IAAI,CAACC,IAAI,CAACjB,aAAa,CAACC,MAAM,GAAGD,aAAa,CAACI,UAAU,CAAC;IACzF,CAAC;IACD,IAAIc,QAAQ,GAAG,CAAC;IAChB,IAAMC,YAAY,GAAG,SAAfA,YAAYA,CAAIC,MAAM,EAAK;MAC/B,IAAMC,WAAW,GAAG,CAAC,IAAIC,IAAI,CAAC,CAAC;MAC/B,IAAID,WAAW,GAAGH,QAAQ,GAAGlB,aAAa,CAACG,OAAO,EAAE;QAClDH,aAAa,CAACO,eAAe,GAAGa,MAAM,CAACG,SAAS;QAChDC,iBAAiB,CAACJ,MAAM,CAACG,SAAS,CAAC;QACnCL,QAAQ,GAAGG,WAAW;MACxB;IACF,CAAC;IACD,IAAMG,iBAAiB,GAAG,SAApBA,iBAAiBA,CAAID,SAAS,EAAK;MACvC,IAAIE,KAAK,GAAGT,IAAI,CAACU,KAAK,CAACH,SAAS,GAAGvB,aAAa,CAACI,UAAU,CAAC,GAAGY,IAAI,CAACU,KAAK,CAAC1B,aAAa,CAACE,YAAY,GAAG,CAAC,CAAC;MACzGuB,KAAK,GAAGA,KAAK,GAAG,CAAC,GAAG,CAAC,GAAGA,KAAK;MAC7B,IAAME,GAAG,GAAGF,KAAK,GAAGzB,aAAa,CAACE,YAAY,GAAG,CAAC;MAClDF,aAAa,CAACS,WAAW,GAAGT,aAAa,CAACQ,QAAQ,CAACoB,KAAK,CAACH,KAAK,EAAEE,GAAG,CAAC;MACpE3B,aAAa,CAACK,MAAM,GAAGoB,KAAK,GAAGzB,aAAa,CAACI,UAAU;MACvDnB,QAAQ,CAAC,YAAM;QACba,gBAAgB,CAAC+B,KAAK,CAACC,MAAM,CAAC,CAAC;MACjC,CAAC,CAAC;IACJ,CAAC;IACD,IAAMC,WAAW,GAAG,SAAdA,WAAWA,CAAIjB,IAAI,EAAK;MAC5BnB,IAAI,CAAC,WAAW,EAAEmB,IAAI,CAAC;IACzB,CAAC;IACD,IAAMkB,YAAY,GAAG,SAAfA,YAAYA,CAAIC,MAAM;MAAA,OAAM7C,mBAAmB,CAACyC,KAAK,GAAG1C,YAAY,CAAC8C,MAAM,CAAC,GAAGA,MAAM;IAAA,CAAC;IAC5FjD,KAAK,CACH;MAAA,OAAMS,KAAK,CAACqB,IAAI;IAAA,GAChB,YAAM;MACJ;MACAJ,OAAO,CAAC,CAAC;MACT;MACAc,iBAAiB,CAACxB,aAAa,CAACO,eAAe,CAAC;IAClD,CAAC,EACD;MAAE2B,SAAS,EAAE,IAAI;MAAEC,IAAI,EAAE;IAAK,CAChC,CAAC;IACDjD,SAAS,CAAC,YAAM;MACdD,QAAQ,CAAC,YAAM;QACbO,GAAG,CAAC4C,QAAQ,CAACvC,WAAW,CAACgC,KAAK,EAAE,UAACQ,CAAC,EAAK;UACrCrC,aAAa,CAACC,MAAM,GAAGoC,CAAC,CAACC,YAAY;UACrC;UACA5B,OAAO,CAAC,CAAC;UACT;UACAc,iBAAiB,CAACxB,aAAa,CAACO,eAAe,CAAC;QAClD,CAAC,CAAC;QACFf,GAAG,CAAC4C,QAAQ,CAACrC,kBAAkB,CAAC8B,KAAK,EAAE,UAACQ,CAAC,EAAK;UAC5CrC,aAAa,CAACI,UAAU,GAAGiC,CAAC,CAACC,YAAY;UACzC;UACA5B,OAAO,CAAC,CAAC;UACT;UACAc,iBAAiB,CAACxB,aAAa,CAACO,eAAe,CAAC;QAClD,CAAC,CAAC;MACJ,CAAC,CAAC;IACJ,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
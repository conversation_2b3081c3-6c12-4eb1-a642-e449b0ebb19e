{"ast": null, "code": "import { renderList as _renderList, Fragment as _Fragment, openBlock as _openBlock, createElementBlock as _createElementBlock, toDisplayString as _toDisplayString, normalizeClass as _normalizeClass, createElementVNode as _createElementVNode } from \"vue\";\nvar _hoisted_1 = {\n  class: \"anchor-location-navigation\"\n};\nvar _hoisted_2 = {\n  class: \"anchor-location-navigation-list\"\n};\nvar _hoisted_3 = [\"title\", \"onClick\"];\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createElementVNode(\"div\", _hoisted_2, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.data, function (item) {\n    return _openBlock(), _createElementBlock(\"div\", {\n      class: _normalizeClass([\"anchor-location-navigation-item\", {\n        'is-active': $setup.value === item.value\n      }]),\n      key: item.value,\n      title: item.label,\n      onClick: function onClick($event) {\n        return $setup.change(item);\n      }\n    }, _toDisplayString(item.label), 11 /* TEXT, CLASS, PROPS */, _hoisted_3);\n  }), 128 /* KEYED_FRAGMENT */))])]);\n}", "map": {"version": 3, "names": ["class", "_createElementBlock", "_hoisted_1", "_createElementVNode", "_hoisted_2", "_Fragment", "_renderList", "$setup", "data", "item", "_normalizeClass", "value", "key", "title", "label", "onClick", "$event", "change", "_hoisted_3"], "sources": ["D:\\zy\\xm\\pc\\5.0\\qiankun-product\\common\\components\\anchor-location\\anchor-location-navigation.vue"], "sourcesContent": ["<template>\r\n  <div class=\"anchor-location-navigation\">\r\n    <div class=\"anchor-location-navigation-list\">\r\n      <div\r\n        class=\"anchor-location-navigation-item\"\r\n        v-for=\"item in data\"\r\n        :key=\"item.value\"\r\n        :title=\"item.label\"\r\n        :class=\"{ 'is-active': value === item.value }\"\r\n        @click=\"change(item)\">\r\n        {{ item.label }}\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nexport default { name: 'AnchorLocationNavigation' }\r\n</script>\r\n\r\n<script setup>\r\nimport { computed } from 'vue'\r\nconst props = defineProps({ modelValue: [String, Number], data: { type: Array, default: () => [] } })\r\nconst emit = defineEmits(['update:modelValue', 'change'])\r\nconst value = computed({\r\n  get() {\r\n    return props.modelValue\r\n  },\r\n  set(value) {\r\n    emit('update:modelValue', value)\r\n  }\r\n})\r\nconst data = computed(() => props.data)\r\nconst change = (item) => {\r\n  emit('change', item)\r\n  value.value = item.value\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n.anchor-location-navigation {\r\n  width: 960px;\r\n  position: absolute;\r\n  top: 50%;\r\n  left: 50%;\r\n  transform: translate(-50%, -50%);\r\n\r\n  .anchor-location-navigation-list {\r\n    position: absolute;\r\n    top: 50%;\r\n    left: -10px;\r\n    transform: translate(-100%, -50%);\r\n\r\n    .anchor-location-navigation-item {\r\n      font-size: var(--zy-name-font-size);\r\n      line-height: var(--zy-line-height);\r\n      padding-right: 20px;\r\n      margin-bottom: 40px;\r\n      position: relative;\r\n      cursor: pointer;\r\n\r\n      &::after {\r\n        content: '';\r\n        position: absolute;\r\n        top: 50%;\r\n        right: 0;\r\n        width: 6px;\r\n        height: 6px;\r\n        border-radius: 50%;\r\n        border: 2px solid var(--zy-el-border-color-lighter);\r\n        transform: translateY(-50%);\r\n      }\r\n\r\n      &::before {\r\n        content: '';\r\n        position: absolute;\r\n        top: calc(50% + 5px);\r\n        right: 4px;\r\n        width: 2px;\r\n        height: calc((var(--zy-name-font-size) * var(--zy-line-height)) + 30px);\r\n        background-color: var(--zy-el-border-color-lighter);\r\n      }\r\n\r\n      &:last-child {\r\n        &::before {\r\n          background-color: transparent;\r\n        }\r\n      }\r\n    }\r\n\r\n    .is-active {\r\n      font-weight: bold;\r\n\r\n      &::after {\r\n        border: 2px solid var(--zy-el-color-primary);\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n@media screen and (max-width: 1580px) {\r\n  .anchor-location-navigation {\r\n    .anchor-location-navigation-list {\r\n      .anchor-location-navigation-item {\r\n        color: transparent;\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;EACOA,KAAK,EAAC;AAA4B;;EAChCA,KAAK,EAAC;AAAiC;iBAFhD;;uBACEC,mBAAA,CAYM,OAZNC,UAYM,GAXJC,mBAAA,CAUM,OAVNC,UAUM,I,kBATJH,mBAAA,CAQMI,SAAA,QAXZC,WAAA,CAKuBC,MAAA,CAAAC,IAAI,EAL3B,UAKeC,IAAI;yBAFbR,mBAAA,CAQM;MAPJD,KAAK,EAJbU,eAAA,EAIc,iCAAiC;QAAA,aAIhBH,MAAA,CAAAI,KAAK,KAAKF,IAAI,CAACE;MAAK;MAF1CC,GAAG,EAAEH,IAAI,CAACE,KAAK;MACfE,KAAK,EAAEJ,IAAI,CAACK,KAAK;MAEjBC,OAAK,WAALA,OAAKA,CAAAC,MAAA;QAAA,OAAET,MAAA,CAAAU,MAAM,CAACR,IAAI;MAAA;wBAChBA,IAAI,CAACK,KAAK,gCAVrBI,UAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "\"use strict\";\n\n/* global console: false */\n\n/**\n * Reporter that handles the reporting of logs, warnings and errors.\n * @public\n * @param {boolean} quiet Tells if the reporter should be quiet or not.\n */\nmodule.exports = function (quiet) {\n  function noop() {\n    //Does nothing.\n  }\n  var reporter = {\n    log: noop,\n    warn: noop,\n    error: noop\n  };\n  if (!quiet && window.console) {\n    var attachFunction = function attachFunction(reporter, name) {\n      //The proxy is needed to be able to call the method with the console context,\n      //since we cannot use bind.\n      reporter[name] = function reporterProxy() {\n        var f = console[name];\n        if (f.apply) {\n          //IE9 does not support console.log.apply :)\n          f.apply(console, arguments);\n        } else {\n          for (var i = 0; i < arguments.length; i++) {\n            f(arguments[i]);\n          }\n        }\n      };\n    };\n    attachFunction(reporter, \"log\");\n    attachFunction(reporter, \"warn\");\n    attachFunction(reporter, \"error\");\n  }\n  return reporter;\n};", "map": {"version": 3, "names": ["module", "exports", "quiet", "noop", "reporter", "log", "warn", "error", "window", "console", "attachFunction", "name", "reporterProxy", "f", "apply", "arguments", "i", "length"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/node_modules/.pnpm/element-resize-detector@1.2.4/node_modules/element-resize-detector/src/reporter.js"], "sourcesContent": ["\"use strict\";\n\n/* global console: false */\n\n/**\n * Reporter that handles the reporting of logs, warnings and errors.\n * @public\n * @param {boolean} quiet Tells if the reporter should be quiet or not.\n */\nmodule.exports = function(quiet) {\n    function noop() {\n        //Does nothing.\n    }\n\n    var reporter = {\n        log: noop,\n        warn: noop,\n        error: noop\n    };\n\n    if(!quiet && window.console) {\n        var attachFunction = function(reporter, name) {\n            //The proxy is needed to be able to call the method with the console context,\n            //since we cannot use bind.\n            reporter[name] = function reporterProxy() {\n                var f = console[name];\n                if (f.apply) { //IE9 does not support console.log.apply :)\n                    f.apply(console, arguments);\n                } else {\n                    for (var i = 0; i < arguments.length; i++) {\n                        f(arguments[i]);\n                    }\n                }\n            };\n        };\n\n        attachFunction(reporter, \"log\");\n        attachFunction(reporter, \"warn\");\n        attachFunction(reporter, \"error\");\n    }\n\n    return reporter;\n};"], "mappings": "AAAA,YAAY;;AAEZ;;AAEA;AACA;AACA;AACA;AACA;AACAA,MAAM,CAACC,OAAO,GAAG,UAASC,KAAK,EAAE;EAC7B,SAASC,IAAIA,CAAA,EAAG;IACZ;EAAA;EAGJ,IAAIC,QAAQ,GAAG;IACXC,GAAG,EAAEF,IAAI;IACTG,IAAI,EAAEH,IAAI;IACVI,KAAK,EAAEJ;EACX,CAAC;EAED,IAAG,CAACD,KAAK,IAAIM,MAAM,CAACC,OAAO,EAAE;IACzB,IAAIC,cAAc,GAAG,SAAjBA,cAAcA,CAAYN,QAAQ,EAAEO,IAAI,EAAE;MAC1C;MACA;MACAP,QAAQ,CAACO,IAAI,CAAC,GAAG,SAASC,aAAaA,CAAA,EAAG;QACtC,IAAIC,CAAC,GAAGJ,OAAO,CAACE,IAAI,CAAC;QACrB,IAAIE,CAAC,CAACC,KAAK,EAAE;UAAE;UACXD,CAAC,CAACC,KAAK,CAACL,OAAO,EAAEM,SAAS,CAAC;QAC/B,CAAC,MAAM;UACH,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,SAAS,CAACE,MAAM,EAAED,CAAC,EAAE,EAAE;YACvCH,CAAC,CAACE,SAAS,CAACC,CAAC,CAAC,CAAC;UACnB;QACJ;MACJ,CAAC;IACL,CAAC;IAEDN,cAAc,CAACN,QAAQ,EAAE,KAAK,CAAC;IAC/BM,cAAc,CAACN,QAAQ,EAAE,MAAM,CAAC;IAChCM,cAAc,CAACN,QAAQ,EAAE,OAAO,CAAC;EACrC;EAEA,OAAOA,QAAQ;AACnB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}
{"ast": null, "code": "\"use strict\";\n\nvar utils = module.exports = {};\n\n/**\n * Loops through the collection and calls the callback for each element. if the callback returns truthy, the loop is broken and returns the same value.\n * @public\n * @param {*} collection The collection to loop through. Needs to have a length property set and have indices set from 0 to length - 1.\n * @param {function} callback The callback to be called for each element. The element will be given as a parameter to the callback. If this callback returns truthy, the loop is broken and the same value is returned.\n * @returns {*} The value that a callback has returned (if truthy). Otherwise nothing.\n */\nutils.forEach = function (collection, callback) {\n  for (var i = 0; i < collection.length; i++) {\n    var result = callback(collection[i]);\n    if (result) {\n      return result;\n    }\n  }\n};", "map": {"version": 3, "names": ["utils", "module", "exports", "for<PERSON>ach", "collection", "callback", "i", "length", "result"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/node_modules/.pnpm/element-resize-detector@1.2.4/node_modules/element-resize-detector/src/collection-utils.js"], "sourcesContent": ["\"use strict\";\n\nvar utils = module.exports = {};\n\n/**\n * Loops through the collection and calls the callback for each element. if the callback returns truthy, the loop is broken and returns the same value.\n * @public\n * @param {*} collection The collection to loop through. Needs to have a length property set and have indices set from 0 to length - 1.\n * @param {function} callback The callback to be called for each element. The element will be given as a parameter to the callback. If this callback returns truthy, the loop is broken and the same value is returned.\n * @returns {*} The value that a callback has returned (if truthy). Otherwise nothing.\n */\nutils.forEach = function(collection, callback) {\n    for(var i = 0; i < collection.length; i++) {\n        var result = callback(collection[i]);\n        if(result) {\n            return result;\n        }\n    }\n};\n"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,KAAK,GAAGC,MAAM,CAACC,OAAO,GAAG,CAAC,CAAC;;AAE/B;AACA;AACA;AACA;AACA;AACA;AACA;AACAF,KAAK,CAACG,OAAO,GAAG,UAASC,UAAU,EAAEC,QAAQ,EAAE;EAC3C,KAAI,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,UAAU,CAACG,MAAM,EAAED,CAAC,EAAE,EAAE;IACvC,IAAIE,MAAM,GAAGH,QAAQ,CAACD,UAAU,CAACE,CAAC,CAAC,CAAC;IACpC,IAAGE,MAAM,EAAE;MACP,OAAOA,MAAM;IACjB;EACJ;AACJ,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}
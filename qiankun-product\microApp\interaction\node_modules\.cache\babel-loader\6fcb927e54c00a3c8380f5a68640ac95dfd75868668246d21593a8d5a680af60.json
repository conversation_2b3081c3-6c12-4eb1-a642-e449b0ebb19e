{"ast": null, "code": "import baseRest from './_baseRest.js';\nimport createWrap from './_createWrap.js';\nimport getHolder from './_getHolder.js';\nimport replaceHolders from './_replaceHolders.js';\n\n/** Used to compose bitmasks for function metadata. */\nvar WRAP_BIND_FLAG = 1,\n  WRAP_PARTIAL_FLAG = 32;\n\n/**\n * Creates a function that invokes `func` with the `this` binding of `thisArg`\n * and `partials` prepended to the arguments it receives.\n *\n * The `_.bind.placeholder` value, which defaults to `_` in monolithic builds,\n * may be used as a placeholder for partially applied arguments.\n *\n * **Note:** Unlike native `Function#bind`, this method doesn't set the \"length\"\n * property of bound functions.\n *\n * @static\n * @memberOf _\n * @since 0.1.0\n * @category Function\n * @param {Function} func The function to bind.\n * @param {*} thisArg The `this` binding of `func`.\n * @param {...*} [partials] The arguments to be partially applied.\n * @returns {Function} Returns the new bound function.\n * @example\n *\n * function greet(greeting, punctuation) {\n *   return greeting + ' ' + this.user + punctuation;\n * }\n *\n * var object = { 'user': 'fred' };\n *\n * var bound = _.bind(greet, object, 'hi');\n * bound('!');\n * // => 'hi fred!'\n *\n * // Bound with placeholders.\n * var bound = _.bind(greet, object, _, '!');\n * bound('hi');\n * // => 'hi fred!'\n */\nvar bind = baseRest(function (func, thisArg, partials) {\n  var bitmask = WRAP_BIND_FLAG;\n  if (partials.length) {\n    var holders = replaceHolders(partials, getHolder(bind));\n    bitmask |= WRAP_PARTIAL_FLAG;\n  }\n  return createWrap(func, bitmask, thisArg, partials, holders);\n});\n\n// Assign default placeholders.\nbind.placeholder = {};\nexport default bind;", "map": {"version": 3, "names": ["baseRest", "createWrap", "getHolder", "replaceHolders", "WRAP_BIND_FLAG", "WRAP_PARTIAL_FLAG", "bind", "func", "thisArg", "partials", "bitmask", "length", "holders", "placeholder"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/node_modules/.pnpm/lodash-es@4.17.21/node_modules/lodash-es/bind.js"], "sourcesContent": ["import baseRest from './_baseRest.js';\nimport createWrap from './_createWrap.js';\nimport getHolder from './_getHolder.js';\nimport replaceHolders from './_replaceHolders.js';\n\n/** Used to compose bitmasks for function metadata. */\nvar WRAP_BIND_FLAG = 1,\n    WRAP_PARTIAL_FLAG = 32;\n\n/**\n * Creates a function that invokes `func` with the `this` binding of `thisArg`\n * and `partials` prepended to the arguments it receives.\n *\n * The `_.bind.placeholder` value, which defaults to `_` in monolithic builds,\n * may be used as a placeholder for partially applied arguments.\n *\n * **Note:** Unlike native `Function#bind`, this method doesn't set the \"length\"\n * property of bound functions.\n *\n * @static\n * @memberOf _\n * @since 0.1.0\n * @category Function\n * @param {Function} func The function to bind.\n * @param {*} thisArg The `this` binding of `func`.\n * @param {...*} [partials] The arguments to be partially applied.\n * @returns {Function} Returns the new bound function.\n * @example\n *\n * function greet(greeting, punctuation) {\n *   return greeting + ' ' + this.user + punctuation;\n * }\n *\n * var object = { 'user': 'fred' };\n *\n * var bound = _.bind(greet, object, 'hi');\n * bound('!');\n * // => 'hi fred!'\n *\n * // Bound with placeholders.\n * var bound = _.bind(greet, object, _, '!');\n * bound('hi');\n * // => 'hi fred!'\n */\nvar bind = baseRest(function(func, thisArg, partials) {\n  var bitmask = WRAP_BIND_FLAG;\n  if (partials.length) {\n    var holders = replaceHolders(partials, getHolder(bind));\n    bitmask |= WRAP_PARTIAL_FLAG;\n  }\n  return createWrap(func, bitmask, thisArg, partials, holders);\n});\n\n// Assign default placeholders.\nbind.placeholder = {};\n\nexport default bind;\n"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,gBAAgB;AACrC,OAAOC,UAAU,MAAM,kBAAkB;AACzC,OAAOC,SAAS,MAAM,iBAAiB;AACvC,OAAOC,cAAc,MAAM,sBAAsB;;AAEjD;AACA,IAAIC,cAAc,GAAG,CAAC;EAClBC,iBAAiB,GAAG,EAAE;;AAE1B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIC,IAAI,GAAGN,QAAQ,CAAC,UAASO,IAAI,EAAEC,OAAO,EAAEC,QAAQ,EAAE;EACpD,IAAIC,OAAO,GAAGN,cAAc;EAC5B,IAAIK,QAAQ,CAACE,MAAM,EAAE;IACnB,IAAIC,OAAO,GAAGT,cAAc,CAACM,QAAQ,EAAEP,SAAS,CAACI,IAAI,CAAC,CAAC;IACvDI,OAAO,IAAIL,iBAAiB;EAC9B;EACA,OAAOJ,UAAU,CAACM,IAAI,EAAEG,OAAO,EAAEF,OAAO,EAAEC,QAAQ,EAAEG,OAAO,CAAC;AAC9D,CAAC,CAAC;;AAEF;AACAN,IAAI,CAACO,WAAW,GAAG,CAAC,CAAC;AAErB,eAAeP,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
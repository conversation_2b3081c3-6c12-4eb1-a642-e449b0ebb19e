{"ast": null, "code": "import { computed } from 'vue';\nimport '../../../../hooks/index.mjs';\nimport { useLocale } from '../../../../hooks/use-locale/index.mjs';\nvar useMonthRangeHeader = function useMonthRangeHeader(_ref) {\n  var unlinkPanels = _ref.unlinkPanels,\n    leftDate = _ref.leftDate,\n    rightDate = _ref.rightDate;\n  var _useLocale = useLocale(),\n    t = _useLocale.t;\n  var leftPrevYear = function leftPrevYear() {\n    leftDate.value = leftDate.value.subtract(1, \"year\");\n    if (!unlinkPanels.value) {\n      rightDate.value = rightDate.value.subtract(1, \"year\");\n    }\n  };\n  var rightNextYear = function rightNextYear() {\n    if (!unlinkPanels.value) {\n      leftDate.value = leftDate.value.add(1, \"year\");\n    }\n    rightDate.value = rightDate.value.add(1, \"year\");\n  };\n  var leftNextYear = function leftNextYear() {\n    leftDate.value = leftDate.value.add(1, \"year\");\n  };\n  var rightPrevYear = function rightPrevYear() {\n    rightDate.value = rightDate.value.subtract(1, \"year\");\n  };\n  var leftLabel = computed(function () {\n    return `${leftDate.value.year()} ${t(\"el.datepicker.year\")}`;\n  });\n  var rightLabel = computed(function () {\n    return `${rightDate.value.year()} ${t(\"el.datepicker.year\")}`;\n  });\n  var leftYear = computed(function () {\n    return leftDate.value.year();\n  });\n  var rightYear = computed(function () {\n    return rightDate.value.year() === leftDate.value.year() ? leftDate.value.year() + 1 : rightDate.value.year();\n  });\n  return {\n    leftPrevYear,\n    rightNextYear,\n    leftNextYear,\n    rightPrevYear,\n    leftLabel,\n    rightLabel,\n    leftYear,\n    rightYear\n  };\n};\nexport { useMonthRangeHeader };", "map": {"version": 3, "names": ["useMonthRangeHeader", "_ref", "unlinkPanels", "leftDate", "rightDate", "_useLocale", "useLocale", "t", "leftPrevYear", "value", "subtract", "rightNextYear", "add", "leftNextYear", "rightPrevYear", "leftLabel", "computed", "year", "<PERSON><PERSON><PERSON><PERSON>", "leftYear", "rightYear"], "sources": ["../../../../../../../packages/components/date-picker/src/composables/use-month-range-header.ts"], "sourcesContent": ["import { computed } from 'vue'\nimport { useLocale } from '@element-plus/hooks'\nimport type { Ref, ToRef } from 'vue'\nimport type { Dayjs } from 'dayjs'\n\nexport const useMonthRangeHeader = ({\n  unlinkPanels,\n  leftDate,\n  rightDate,\n}: {\n  unlinkPanels: ToRef<boolean>\n  leftDate: Ref<Dayjs>\n  rightDate: Ref<Dayjs>\n}) => {\n  const { t } = useLocale()\n  const leftPrevYear = () => {\n    leftDate.value = leftDate.value.subtract(1, 'year')\n    if (!unlinkPanels.value) {\n      rightDate.value = rightDate.value.subtract(1, 'year')\n    }\n  }\n\n  const rightNextYear = () => {\n    if (!unlinkPanels.value) {\n      leftDate.value = leftDate.value.add(1, 'year')\n    }\n    rightDate.value = rightDate.value.add(1, 'year')\n  }\n\n  const leftNextYear = () => {\n    leftDate.value = leftDate.value.add(1, 'year')\n  }\n\n  const rightPrevYear = () => {\n    rightDate.value = rightDate.value.subtract(1, 'year')\n  }\n  const leftLabel = computed(() => {\n    return `${leftDate.value.year()} ${t('el.datepicker.year')}`\n  })\n\n  const rightLabel = computed(() => {\n    return `${rightDate.value.year()} ${t('el.datepicker.year')}`\n  })\n\n  const leftYear = computed(() => {\n    return leftDate.value.year()\n  })\n\n  const rightYear = computed(() => {\n    return rightDate.value.year() === leftDate.value.year()\n      ? leftDate.value.year() + 1\n      : rightDate.value.year()\n  })\n\n  return {\n    leftPrevYear,\n    rightNextYear,\n    leftNextYear,\n    rightPrevYear,\n    leftLabel,\n    rightLabel,\n    leftYear,\n    rightYear,\n  }\n}\n"], "mappings": ";;;AAEY,IAACA,mBAAmB,GAAG,SAAtBA,mBAAmBA,CAAAC,IAAA,EAI1B;EAAA,IAHJC,YAAY,GAAAD,IAAA,CAAZC,YAAY;IACZC,QAAQ,GAAAF,IAAA,CAARE,QAAQ;IACRC,SAAS,GAAAH,IAAA,CAATG,SAAS;EAET,IAAAC,UAAA,GAAcC,SAAS,EAAE;IAAjBC,CAAC,GAAAF,UAAA,CAADE,CAAC;EACT,IAAMC,YAAY,GAAG,SAAfA,YAAYA,CAAA,EAAS;IACzBL,QAAQ,CAACM,KAAK,GAAGN,QAAQ,CAACM,KAAK,CAACC,QAAQ,CAAC,CAAC,EAAE,MAAM,CAAC;IACnD,IAAI,CAACR,YAAY,CAACO,KAAK,EAAE;MACvBL,SAAS,CAACK,KAAK,GAAGL,SAAS,CAACK,KAAK,CAACC,QAAQ,CAAC,CAAC,EAAE,MAAM,CAAC;IAC3D;EACA,CAAG;EACD,IAAMC,aAAa,GAAG,SAAhBA,aAAaA,CAAA,EAAS;IAC1B,IAAI,CAACT,YAAY,CAACO,KAAK,EAAE;MACvBN,QAAQ,CAACM,KAAK,GAAGN,QAAQ,CAACM,KAAK,CAACG,GAAG,CAAC,CAAC,EAAE,MAAM,CAAC;IACpD;IACIR,SAAS,CAACK,KAAK,GAAGL,SAAS,CAACK,KAAK,CAACG,GAAG,CAAC,CAAC,EAAE,MAAM,CAAC;EACpD,CAAG;EACD,IAAMC,YAAY,GAAG,SAAfA,YAAYA,CAAA,EAAS;IACzBV,QAAQ,CAACM,KAAK,GAAGN,QAAQ,CAACM,KAAK,CAACG,GAAG,CAAC,CAAC,EAAE,MAAM,CAAC;EAClD,CAAG;EACD,IAAME,aAAa,GAAG,SAAhBA,aAAaA,CAAA,EAAS;IAC1BV,SAAS,CAACK,KAAK,GAAGL,SAAS,CAACK,KAAK,CAACC,QAAQ,CAAC,CAAC,EAAE,MAAM,CAAC;EACzD,CAAG;EACD,IAAMK,SAAS,GAAGC,QAAQ,CAAC,YAAM;IAC/B,OAAO,GAAGb,QAAQ,CAACM,KAAK,CAACQ,IAAI,EAAE,IAAIV,CAAC,CAAC,oBAAoB,CAAC,EAAE;EAChE,CAAG,CAAC;EACF,IAAMW,UAAU,GAAGF,QAAQ,CAAC,YAAM;IAChC,OAAO,GAAGZ,SAAS,CAACK,KAAK,CAACQ,IAAI,EAAE,IAAIV,CAAC,CAAC,oBAAoB,CAAC,EAAE;EACjE,CAAG,CAAC;EACF,IAAMY,QAAQ,GAAGH,QAAQ,CAAC,YAAM;IAC9B,OAAOb,QAAQ,CAACM,KAAK,CAACQ,IAAI,EAAE;EAChC,CAAG,CAAC;EACF,IAAMG,SAAS,GAAGJ,QAAQ,CAAC,YAAM;IAC/B,OAAOZ,SAAS,CAACK,KAAK,CAACQ,IAAI,EAAE,KAAKd,QAAQ,CAACM,KAAK,CAACQ,IAAI,EAAE,GAAGd,QAAQ,CAACM,KAAK,CAACQ,IAAI,EAAE,GAAG,CAAC,GAAGb,SAAS,CAACK,KAAK,CAACQ,IAAI,EAAE;EAChH,CAAG,CAAC;EACF,OAAO;IACLT,YAAY;IACZG,aAAa;IACbE,YAAY;IACZC,aAAa;IACbC,SAAS;IACTG,UAAU;IACVC,QAAQ;IACRC;EACJ,CAAG;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "import { defineComponent, openBlock, createElementBlock, normalizeClass, normalizeStyle, toDisplayString } from 'vue';\nimport '../../../hooks/index.mjs';\nimport _export_sfc from '../../../_virtual/plugin-vue_export-helper.mjs';\nimport { useNamespace } from '../../../hooks/use-namespace/index.mjs';\nvar _sfc_main = defineComponent({\n  props: {\n    item: {\n      type: Object,\n      required: true\n    },\n    style: Object,\n    height: Number\n  },\n  setup() {\n    var ns = useNamespace(\"select\");\n    return {\n      ns\n    };\n  }\n});\nfunction _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {\n  return openBlock(), createElementBlock(\"div\", {\n    class: normalizeClass(_ctx.ns.be(\"group\", \"title\")),\n    style: normalizeStyle([_ctx.style, {\n      lineHeight: `${_ctx.height}px`\n    }])\n  }, toDisplayString(_ctx.item.label), 7);\n}\nvar GroupItem = /* @__PURE__ */_export_sfc(_sfc_main, [[\"render\", _sfc_render], [\"__file\", \"group-item.vue\"]]);\nexport { GroupItem as default };", "map": {"version": 3, "names": ["_sfc_main", "defineComponent", "props", "item", "type", "Object", "required", "style", "height", "Number", "setup", "ns", "useNamespace", "createElementBlock", "class", "normalizeClass", "_ctx", "be", "normalizeStyle", "lineHeight", "toDisplayString", "label"], "sources": ["../../../../../../packages/components/select-v2/src/group-item.vue"], "sourcesContent": ["<template>\n  <div\n    :class=\"ns.be('group', 'title')\"\n    :style=\"[style, { lineHeight: `${height}px` }]\"\n  >\n    {{ item.label }}\n  </div>\n</template>\n\n<script lang=\"ts\">\n// @ts-nocheck\nimport { defineComponent } from 'vue'\nimport { useNamespace } from '@element-plus/hooks'\n\nexport default defineComponent({\n  props: {\n    item: {\n      type: Object,\n      required: true,\n    },\n    style: Object,\n    height: Number,\n  },\n  setup() {\n    const ns = useNamespace('select')\n    return {\n      ns,\n    }\n  },\n})\n</script>\n"], "mappings": ";;;;AAcA,IAAKA,SAAA,GAAaC,eAAa;EAC7BC,KAAO;IACLC,IAAM;MACJC,IAAM,EAAAC,MAAA;MACNC,QAAU;IAAA,CACZ;IACAC,KAAO,EAAAF,MAAA;IACPG,MAAQ,EAAAC;EAAA,CACV;EACAC,KAAQA,CAAA;IACA,IAAAC,EAAA,GAAKC,YAAA,CAAa,QAAQ;IACzB;MACLD;IAAA,CACF;EAAA;AAEJ,CAAC;;sBA5BCE,kBAKM;IAJHC,KAAA,EAAKC,cAAE,CAAAC,IAAA,CAAAL,EAAA,CAAGM,EAAE;IACZV,KAAK,EAAAW,cAAA,EAAGF,IAAK,CAAAT,KAAA;MAAAY,UAAA,KAAmBH,IAAM,CAAAR,MAAA;IAAA;EAAA,GAAAY,eAAA,CAEpCJ,IAAA,CAAAb,IAAA,CAAKkB,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "import { TinyColor } from './index.js';\n// Readability Functions\n// ---------------------\n// <http://www.w3.org/TR/2008/REC-WCAG20-20081211/#contrast-ratiodef (WCAG Version 2)\n/**\n * AKA `contrast`\n *\n * Analyze the 2 colors and returns the color contrast defined by (WCAG Version 2)\n */\nexport function readability(color1, color2) {\n  var c1 = new TinyColor(color1);\n  var c2 = new TinyColor(color2);\n  return (Math.max(c1.getLuminance(), c2.getLuminance()) + 0.05) / (Math.min(c1.getLuminance(), c2.getLuminance()) + 0.05);\n}\n/**\n * Ensure that foreground and background color combinations meet WCAG2 guidelines.\n * The third argument is an object.\n *      the 'level' property states 'AA' or 'AAA' - if missing or invalid, it defaults to 'AA';\n *      the 'size' property states 'large' or 'small' - if missing or invalid, it defaults to 'small'.\n * If the entire object is absent, isReadable defaults to {level:\"AA\",size:\"small\"}.\n *\n * Example\n * ```ts\n * new TinyColor().isReadable('#000', '#111') => false\n * new TinyColor().isReadable('#000', '#111', { level: 'AA', size: 'large' }) => false\n * ```\n */\nexport function isReadable(color1, color2, wcag2) {\n  var _a, _b;\n  if (wcag2 === void 0) {\n    wcag2 = {\n      level: 'AA',\n      size: 'small'\n    };\n  }\n  var readabilityLevel = readability(color1, color2);\n  switch (((_a = wcag2.level) !== null && _a !== void 0 ? _a : 'AA') + ((_b = wcag2.size) !== null && _b !== void 0 ? _b : 'small')) {\n    case 'AAsmall':\n    case 'AAAlarge':\n      return readabilityLevel >= 4.5;\n    case 'AAlarge':\n      return readabilityLevel >= 3;\n    case 'AAAsmall':\n      return readabilityLevel >= 7;\n    default:\n      return false;\n  }\n}\n/**\n * Given a base color and a list of possible foreground or background\n * colors for that base, returns the most readable color.\n * Optionally returns Black or White if the most readable color is unreadable.\n *\n * @param baseColor - the base color.\n * @param colorList - array of colors to pick the most readable one from.\n * @param args - and object with extra arguments\n *\n * Example\n * ```ts\n * new TinyColor().mostReadable('#123', ['#124\", \"#125'], { includeFallbackColors: false }).toHexString(); // \"#112255\"\n * new TinyColor().mostReadable('#123', ['#124\", \"#125'],{ includeFallbackColors: true }).toHexString();  // \"#ffffff\"\n * new TinyColor().mostReadable('#a8015a', [\"#faf3f3\"], { includeFallbackColors:true, level: 'AAA', size: 'large' }).toHexString(); // \"#faf3f3\"\n * new TinyColor().mostReadable('#a8015a', [\"#faf3f3\"], { includeFallbackColors:true, level: 'AAA', size: 'small' }).toHexString(); // \"#ffffff\"\n * ```\n */\nexport function mostReadable(baseColor, colorList, args) {\n  if (args === void 0) {\n    args = {\n      includeFallbackColors: false,\n      level: 'AA',\n      size: 'small'\n    };\n  }\n  var bestColor = null;\n  var bestScore = 0;\n  var includeFallbackColors = args.includeFallbackColors,\n    level = args.level,\n    size = args.size;\n  for (var _i = 0, colorList_1 = colorList; _i < colorList_1.length; _i++) {\n    var color = colorList_1[_i];\n    var score = readability(baseColor, color);\n    if (score > bestScore) {\n      bestScore = score;\n      bestColor = new TinyColor(color);\n    }\n  }\n  if (isReadable(baseColor, bestColor, {\n    level: level,\n    size: size\n  }) || !includeFallbackColors) {\n    return bestColor;\n  }\n  args.includeFallbackColors = false;\n  return mostReadable(baseColor, ['#fff', '#000'], args);\n}", "map": {"version": 3, "names": ["TinyColor", "readability", "color1", "color2", "c1", "c2", "Math", "max", "getLuminance", "min", "isReadable", "wcag2", "_a", "_b", "level", "size", "readabilityLevel", "mostReadable", "baseColor", "colorList", "args", "includeFallbackColors", "bestColor", "bestScore", "_i", "colorList_1", "length", "color", "score"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/node_modules/.pnpm/@ctrl+tinycolor@3.6.1/node_modules/@ctrl/tinycolor/dist/module/readability.js"], "sourcesContent": ["import { TinyColor } from './index.js';\n// Readability Functions\n// ---------------------\n// <http://www.w3.org/TR/2008/REC-WCAG20-20081211/#contrast-ratiodef (WCAG Version 2)\n/**\n * AKA `contrast`\n *\n * Analyze the 2 colors and returns the color contrast defined by (WCAG Version 2)\n */\nexport function readability(color1, color2) {\n    var c1 = new TinyColor(color1);\n    var c2 = new TinyColor(color2);\n    return ((Math.max(c1.getLuminance(), c2.getLuminance()) + 0.05) /\n        (Math.min(c1.getLuminance(), c2.getLuminance()) + 0.05));\n}\n/**\n * Ensure that foreground and background color combinations meet WCAG2 guidelines.\n * The third argument is an object.\n *      the 'level' property states 'AA' or 'AAA' - if missing or invalid, it defaults to 'AA';\n *      the 'size' property states 'large' or 'small' - if missing or invalid, it defaults to 'small'.\n * If the entire object is absent, isReadable defaults to {level:\"AA\",size:\"small\"}.\n *\n * Example\n * ```ts\n * new TinyColor().isReadable('#000', '#111') => false\n * new TinyColor().isReadable('#000', '#111', { level: 'AA', size: 'large' }) => false\n * ```\n */\nexport function isReadable(color1, color2, wcag2) {\n    var _a, _b;\n    if (wcag2 === void 0) { wcag2 = { level: 'AA', size: 'small' }; }\n    var readabilityLevel = readability(color1, color2);\n    switch (((_a = wcag2.level) !== null && _a !== void 0 ? _a : 'AA') + ((_b = wcag2.size) !== null && _b !== void 0 ? _b : 'small')) {\n        case 'AAsmall':\n        case 'AAAlarge':\n            return readabilityLevel >= 4.5;\n        case 'AAlarge':\n            return readabilityLevel >= 3;\n        case 'AAAsmall':\n            return readabilityLevel >= 7;\n        default:\n            return false;\n    }\n}\n/**\n * Given a base color and a list of possible foreground or background\n * colors for that base, returns the most readable color.\n * Optionally returns Black or White if the most readable color is unreadable.\n *\n * @param baseColor - the base color.\n * @param colorList - array of colors to pick the most readable one from.\n * @param args - and object with extra arguments\n *\n * Example\n * ```ts\n * new TinyColor().mostReadable('#123', ['#124\", \"#125'], { includeFallbackColors: false }).toHexString(); // \"#112255\"\n * new TinyColor().mostReadable('#123', ['#124\", \"#125'],{ includeFallbackColors: true }).toHexString();  // \"#ffffff\"\n * new TinyColor().mostReadable('#a8015a', [\"#faf3f3\"], { includeFallbackColors:true, level: 'AAA', size: 'large' }).toHexString(); // \"#faf3f3\"\n * new TinyColor().mostReadable('#a8015a', [\"#faf3f3\"], { includeFallbackColors:true, level: 'AAA', size: 'small' }).toHexString(); // \"#ffffff\"\n * ```\n */\nexport function mostReadable(baseColor, colorList, args) {\n    if (args === void 0) { args = { includeFallbackColors: false, level: 'AA', size: 'small' }; }\n    var bestColor = null;\n    var bestScore = 0;\n    var includeFallbackColors = args.includeFallbackColors, level = args.level, size = args.size;\n    for (var _i = 0, colorList_1 = colorList; _i < colorList_1.length; _i++) {\n        var color = colorList_1[_i];\n        var score = readability(baseColor, color);\n        if (score > bestScore) {\n            bestScore = score;\n            bestColor = new TinyColor(color);\n        }\n    }\n    if (isReadable(baseColor, bestColor, { level: level, size: size }) || !includeFallbackColors) {\n        return bestColor;\n    }\n    args.includeFallbackColors = false;\n    return mostReadable(baseColor, ['#fff', '#000'], args);\n}\n"], "mappings": "AAAA,SAASA,SAAS,QAAQ,YAAY;AACtC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,WAAWA,CAACC,MAAM,EAAEC,MAAM,EAAE;EACxC,IAAIC,EAAE,GAAG,IAAIJ,SAAS,CAACE,MAAM,CAAC;EAC9B,IAAIG,EAAE,GAAG,IAAIL,SAAS,CAACG,MAAM,CAAC;EAC9B,OAAQ,CAACG,IAAI,CAACC,GAAG,CAACH,EAAE,CAACI,YAAY,CAAC,CAAC,EAAEH,EAAE,CAACG,YAAY,CAAC,CAAC,CAAC,GAAG,IAAI,KACzDF,IAAI,CAACG,GAAG,CAACL,EAAE,CAACI,YAAY,CAAC,CAAC,EAAEH,EAAE,CAACG,YAAY,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC;AAC/D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASE,UAAUA,CAACR,MAAM,EAAEC,MAAM,EAAEQ,KAAK,EAAE;EAC9C,IAAIC,EAAE,EAAEC,EAAE;EACV,IAAIF,KAAK,KAAK,KAAK,CAAC,EAAE;IAAEA,KAAK,GAAG;MAAEG,KAAK,EAAE,IAAI;MAAEC,IAAI,EAAE;IAAQ,CAAC;EAAE;EAChE,IAAIC,gBAAgB,GAAGf,WAAW,CAACC,MAAM,EAAEC,MAAM,CAAC;EAClD,QAAQ,CAAC,CAACS,EAAE,GAAGD,KAAK,CAACG,KAAK,MAAM,IAAI,IAAIF,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG,IAAI,KAAK,CAACC,EAAE,GAAGF,KAAK,CAACI,IAAI,MAAM,IAAI,IAAIF,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG,OAAO,CAAC;IAC7H,KAAK,SAAS;IACd,KAAK,UAAU;MACX,OAAOG,gBAAgB,IAAI,GAAG;IAClC,KAAK,SAAS;MACV,OAAOA,gBAAgB,IAAI,CAAC;IAChC,KAAK,UAAU;MACX,OAAOA,gBAAgB,IAAI,CAAC;IAChC;MACI,OAAO,KAAK;EACpB;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,YAAYA,CAACC,SAAS,EAAEC,SAAS,EAAEC,IAAI,EAAE;EACrD,IAAIA,IAAI,KAAK,KAAK,CAAC,EAAE;IAAEA,IAAI,GAAG;MAAEC,qBAAqB,EAAE,KAAK;MAAEP,KAAK,EAAE,IAAI;MAAEC,IAAI,EAAE;IAAQ,CAAC;EAAE;EAC5F,IAAIO,SAAS,GAAG,IAAI;EACpB,IAAIC,SAAS,GAAG,CAAC;EACjB,IAAIF,qBAAqB,GAAGD,IAAI,CAACC,qBAAqB;IAAEP,KAAK,GAAGM,IAAI,CAACN,KAAK;IAAEC,IAAI,GAAGK,IAAI,CAACL,IAAI;EAC5F,KAAK,IAAIS,EAAE,GAAG,CAAC,EAAEC,WAAW,GAAGN,SAAS,EAAEK,EAAE,GAAGC,WAAW,CAACC,MAAM,EAAEF,EAAE,EAAE,EAAE;IACrE,IAAIG,KAAK,GAAGF,WAAW,CAACD,EAAE,CAAC;IAC3B,IAAII,KAAK,GAAG3B,WAAW,CAACiB,SAAS,EAAES,KAAK,CAAC;IACzC,IAAIC,KAAK,GAAGL,SAAS,EAAE;MACnBA,SAAS,GAAGK,KAAK;MACjBN,SAAS,GAAG,IAAItB,SAAS,CAAC2B,KAAK,CAAC;IACpC;EACJ;EACA,IAAIjB,UAAU,CAACQ,SAAS,EAAEI,SAAS,EAAE;IAAER,KAAK,EAAEA,KAAK;IAAEC,IAAI,EAAEA;EAAK,CAAC,CAAC,IAAI,CAACM,qBAAqB,EAAE;IAC1F,OAAOC,SAAS;EACpB;EACAF,IAAI,CAACC,qBAAqB,GAAG,KAAK;EAClC,OAAOJ,YAAY,CAACC,SAAS,EAAE,CAAC,MAAM,EAAE,MAAM,CAAC,EAAEE,IAAI,CAAC;AAC1D", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
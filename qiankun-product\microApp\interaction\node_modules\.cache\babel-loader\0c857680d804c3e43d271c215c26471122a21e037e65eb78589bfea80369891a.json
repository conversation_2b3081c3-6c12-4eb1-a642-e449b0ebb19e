{"ast": null, "code": "function _createForOfIteratorHelper(r, e) { var t = \"undefined\" != typeof Symbol && r[Symbol.iterator] || r[\"@@iterator\"]; if (!t) { if (Array.isArray(r) || (t = _unsupportedIterableToArray(r)) || e && r && \"number\" == typeof r.length) { t && (r = t); var _n = 0, F = function F() {}; return { s: F, n: function n() { return _n >= r.length ? { done: !0 } : { done: !1, value: r[_n++] }; }, e: function e(r) { throw r; }, f: F }; } throw new TypeError(\"Invalid attempt to iterate non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); } var o, a = !0, u = !1; return { s: function s() { t = t.call(r); }, n: function n() { var r = t.next(); return a = r.done, r; }, e: function e(r) { u = !0, o = r; }, f: function f() { try { a || null == t.return || t.return(); } finally { if (u) throw o; } } }; }\nfunction _unsupportedIterableToArray(r, a) { if (r) { if (\"string\" == typeof r) return _arrayLikeToArray(r, a); var t = {}.toString.call(r).slice(8, -1); return \"Object\" === t && r.constructor && (t = r.constructor.name), \"Map\" === t || \"Set\" === t ? Array.from(r) : \"Arguments\" === t || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t) ? _arrayLikeToArray(r, a) : void 0; } }\nfunction _arrayLikeToArray(r, a) { (null == a || a > r.length) && (a = r.length); for (var e = 0, n = Array(a); e < a; e++) n[e] = r[e]; return n; }\nimport { ref, getCurrentInstance, watch, nextTick } from 'vue';\nimport { SetOperationEnum, NODE_CHECK, NODE_CHECK_CHANGE } from '../virtual-tree.mjs';\nfunction useCheck(props, tree) {\n  var checkedKeys = ref(/* @__PURE__ */new Set());\n  var indeterminateKeys = ref(/* @__PURE__ */new Set());\n  var _getCurrentInstance = getCurrentInstance(),\n    emit = _getCurrentInstance.emit;\n  watch([function () {\n    return tree.value;\n  }, function () {\n    return props.defaultCheckedKeys;\n  }], function () {\n    return nextTick(function () {\n      _setCheckedKeys(props.defaultCheckedKeys);\n    });\n  }, {\n    immediate: true\n  });\n  var updateCheckedKeys = function updateCheckedKeys() {\n    if (!tree.value || !props.showCheckbox || props.checkStrictly) {\n      return;\n    }\n    var _tree$value = tree.value,\n      levelTreeNodeMap = _tree$value.levelTreeNodeMap,\n      maxLevel = _tree$value.maxLevel;\n    var checkedKeySet = checkedKeys.value;\n    var indeterminateKeySet = /* @__PURE__ */new Set();\n    for (var level = maxLevel - 1; level >= 1; --level) {\n      var nodes = levelTreeNodeMap.get(level);\n      if (!nodes) continue;\n      nodes.forEach(function (node) {\n        var children = node.children;\n        if (children) {\n          var allChecked = true;\n          var hasChecked = false;\n          var _iterator = _createForOfIteratorHelper(children),\n            _step;\n          try {\n            for (_iterator.s(); !(_step = _iterator.n()).done;) {\n              var childNode = _step.value;\n              var key = childNode.key;\n              if (checkedKeySet.has(key)) {\n                hasChecked = true;\n              } else if (indeterminateKeySet.has(key)) {\n                allChecked = false;\n                hasChecked = true;\n                break;\n              } else {\n                allChecked = false;\n              }\n            }\n          } catch (err) {\n            _iterator.e(err);\n          } finally {\n            _iterator.f();\n          }\n          if (allChecked) {\n            checkedKeySet.add(node.key);\n          } else if (hasChecked) {\n            indeterminateKeySet.add(node.key);\n            checkedKeySet.delete(node.key);\n          } else {\n            checkedKeySet.delete(node.key);\n            indeterminateKeySet.delete(node.key);\n          }\n        }\n      });\n    }\n    indeterminateKeys.value = indeterminateKeySet;\n  };\n  var isChecked = function isChecked(node) {\n    return checkedKeys.value.has(node.key);\n  };\n  var isIndeterminate = function isIndeterminate(node) {\n    return indeterminateKeys.value.has(node.key);\n  };\n  var toggleCheckbox = function toggleCheckbox(node, isChecked2) {\n    var nodeClick = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : true;\n    var checkedKeySet = checkedKeys.value;\n    var _toggle = function toggle(node2, checked) {\n      checkedKeySet[checked ? SetOperationEnum.ADD : SetOperationEnum.DELETE](node2.key);\n      var children = node2.children;\n      if (!props.checkStrictly && children) {\n        children.forEach(function (childNode) {\n          if (!childNode.disabled) {\n            _toggle(childNode, checked);\n          }\n        });\n      }\n    };\n    _toggle(node, isChecked2);\n    updateCheckedKeys();\n    if (nodeClick) {\n      afterNodeCheck(node, isChecked2);\n    }\n  };\n  var afterNodeCheck = function afterNodeCheck(node, checked) {\n    var _getChecked = getChecked(),\n      checkedNodes = _getChecked.checkedNodes,\n      checkedKeys2 = _getChecked.checkedKeys;\n    var _getHalfChecked = getHalfChecked(),\n      halfCheckedNodes = _getHalfChecked.halfCheckedNodes,\n      halfCheckedKeys = _getHalfChecked.halfCheckedKeys;\n    emit(NODE_CHECK, node.data, {\n      checkedKeys: checkedKeys2,\n      checkedNodes,\n      halfCheckedKeys,\n      halfCheckedNodes\n    });\n    emit(NODE_CHECK_CHANGE, node.data, checked);\n  };\n  function getCheckedKeys() {\n    var leafOnly = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : false;\n    return getChecked(leafOnly).checkedKeys;\n  }\n  function getCheckedNodes() {\n    var leafOnly = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : false;\n    return getChecked(leafOnly).checkedNodes;\n  }\n  function getHalfCheckedKeys() {\n    return getHalfChecked().halfCheckedKeys;\n  }\n  function getHalfCheckedNodes() {\n    return getHalfChecked().halfCheckedNodes;\n  }\n  function getChecked() {\n    var leafOnly = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : false;\n    var checkedNodes = [];\n    var keys = [];\n    if ((tree == null ? void 0 : tree.value) && props.showCheckbox) {\n      var treeNodeMap = tree.value.treeNodeMap;\n      checkedKeys.value.forEach(function (key) {\n        var node = treeNodeMap.get(key);\n        if (node && (!leafOnly || leafOnly && node.isLeaf)) {\n          keys.push(key);\n          checkedNodes.push(node.data);\n        }\n      });\n    }\n    return {\n      checkedKeys: keys,\n      checkedNodes\n    };\n  }\n  function getHalfChecked() {\n    var halfCheckedNodes = [];\n    var halfCheckedKeys = [];\n    if ((tree == null ? void 0 : tree.value) && props.showCheckbox) {\n      var treeNodeMap = tree.value.treeNodeMap;\n      indeterminateKeys.value.forEach(function (key) {\n        var node = treeNodeMap.get(key);\n        if (node) {\n          halfCheckedKeys.push(key);\n          halfCheckedNodes.push(node.data);\n        }\n      });\n    }\n    return {\n      halfCheckedNodes,\n      halfCheckedKeys\n    };\n  }\n  function setCheckedKeys(keys) {\n    checkedKeys.value.clear();\n    indeterminateKeys.value.clear();\n    nextTick(function () {\n      _setCheckedKeys(keys);\n    });\n  }\n  function setChecked(key, isChecked2) {\n    if ((tree == null ? void 0 : tree.value) && props.showCheckbox) {\n      var node = tree.value.treeNodeMap.get(key);\n      if (node) {\n        toggleCheckbox(node, isChecked2, false);\n      }\n    }\n  }\n  function _setCheckedKeys(keys) {\n    if (tree == null ? void 0 : tree.value) {\n      var treeNodeMap = tree.value.treeNodeMap;\n      if (props.showCheckbox && treeNodeMap && keys) {\n        var _iterator2 = _createForOfIteratorHelper(keys),\n          _step2;\n        try {\n          for (_iterator2.s(); !(_step2 = _iterator2.n()).done;) {\n            var key = _step2.value;\n            var node = treeNodeMap.get(key);\n            if (node && !isChecked(node)) {\n              toggleCheckbox(node, true, false);\n            }\n          }\n        } catch (err) {\n          _iterator2.e(err);\n        } finally {\n          _iterator2.f();\n        }\n      }\n    }\n  }\n  return {\n    updateCheckedKeys,\n    toggleCheckbox,\n    isChecked,\n    isIndeterminate,\n    getCheckedKeys,\n    getCheckedNodes,\n    getHalfCheckedKeys,\n    getHalfCheckedNodes,\n    setChecked,\n    setCheckedKeys\n  };\n}\nexport { useCheck };", "map": {"version": 3, "names": ["useCheck", "props", "tree", "checked<PERSON>eys", "ref", "Set", "indeterminateKeys", "_getCurrentInstance", "getCurrentInstance", "emit", "watch", "value", "defaultCheckedKeys", "nextTick", "_setChe<PERSON><PERSON><PERSON>s", "immediate", "updateCheckedKeys", "showCheckbox", "checkStrictly", "_tree$value", "levelTreeNodeMap", "maxLevel", "checkedKeySet", "indeterminateKeySet", "level", "nodes", "get", "for<PERSON>ach", "node", "children", "allChecked", "hasChecked", "_iterator", "_createForOfIteratorHelper", "_step", "s", "n", "done", "childNode", "key", "has", "err", "e", "f", "add", "delete", "isChecked", "isIndeterminate", "toggleCheckbox", "isChecked2", "nodeClick", "arguments", "length", "undefined", "toggle", "node2", "checked", "SetOperationEnum", "ADD", "DELETE", "disabled", "after<PERSON><PERSON><PERSON><PERSON><PERSON>", "_getChecked", "getChecked", "checkedNodes", "checkedKeys2", "_getHalfChecked", "getHalfChecked", "halfCheckedNodes", "halfC<PERSON>cked<PERSON>eys", "NODE_CHECK", "data", "NODE_CHECK_CHANGE", "getChe<PERSON><PERSON>eys", "leafOnly", "getCheckedNodes", "getHalfCheckedKeys", "getHalfCheckedNodes", "keys", "treeNodeMap", "<PERSON><PERSON><PERSON><PERSON>", "push", "set<PERSON><PERSON><PERSON><PERSON>eys", "clear", "setChecked", "_iterator2", "_step2"], "sources": ["../../../../../../../packages/components/tree-v2/src/composables/useCheck.ts"], "sourcesContent": ["import { getCurrentInstance, nextTick, ref, watch } from 'vue'\nimport {\n  NODE_CHECK,\n  NODE_CHECK_CHANGE,\n  SetOperationEnum,\n} from '../virtual-tree'\nimport type { CheckboxValueType } from '@element-plus/components/checkbox'\nimport type { Ref } from 'vue'\nimport type { Tree, TreeKey, TreeNode, TreeNodeData, TreeProps } from '../types'\n\nexport function useCheck(props: TreeProps, tree: Ref<Tree | undefined>) {\n  const checkedKeys = ref<Set<TreeKey>>(new Set())\n  const indeterminateKeys = ref<Set<TreeKey>>(new Set())\n  const { emit } = getCurrentInstance()!\n\n  watch(\n    [() => tree.value, () => props.defaultCheckedKeys],\n    () => {\n      return nextTick(() => {\n        _setCheckedKeys(props.defaultCheckedKeys)\n      })\n    },\n    {\n      immediate: true,\n    }\n  )\n\n  const updateCheckedKeys = () => {\n    if (!tree.value || !props.showCheckbox || props.checkStrictly) {\n      return\n    }\n    const { levelTreeNodeMap, maxLevel } = tree.value\n    const checkedKeySet = checkedKeys.value\n    const indeterminateKeySet = new Set<TreeKey>()\n    // It is easier to determine the indeterminate state by\n    // traversing from bottom to top\n    // leaf nodes not have indeterminate status and can be skipped\n    for (let level = maxLevel - 1; level >= 1; --level) {\n      const nodes = levelTreeNodeMap.get(level)\n      if (!nodes) continue\n      nodes.forEach((node) => {\n        const children = node.children\n        if (children) {\n          // Whether all child nodes are selected\n          let allChecked = true\n          // Whether a child node is selected\n          let hasChecked = false\n          for (const childNode of children) {\n            const key = childNode.key\n            if (checkedKeySet.has(key)) {\n              hasChecked = true\n            } else if (indeterminateKeySet.has(key)) {\n              allChecked = false\n              hasChecked = true\n              break\n            } else {\n              allChecked = false\n            }\n          }\n          if (allChecked) {\n            checkedKeySet.add(node.key)\n          } else if (hasChecked) {\n            indeterminateKeySet.add(node.key)\n            checkedKeySet.delete(node.key)\n          } else {\n            checkedKeySet.delete(node.key)\n            indeterminateKeySet.delete(node.key)\n          }\n        }\n      })\n    }\n    indeterminateKeys.value = indeterminateKeySet\n  }\n\n  const isChecked = (node: TreeNode) => checkedKeys.value.has(node.key)\n\n  const isIndeterminate = (node: TreeNode) =>\n    indeterminateKeys.value.has(node.key)\n\n  const toggleCheckbox = (\n    node: TreeNode,\n    isChecked: CheckboxValueType,\n    nodeClick = true\n  ) => {\n    const checkedKeySet = checkedKeys.value\n    const toggle = (node: TreeNode, checked: CheckboxValueType) => {\n      checkedKeySet[checked ? SetOperationEnum.ADD : SetOperationEnum.DELETE](\n        node.key\n      )\n      const children = node.children\n      if (!props.checkStrictly && children) {\n        children.forEach((childNode) => {\n          if (!childNode.disabled) {\n            toggle(childNode, checked)\n          }\n        })\n      }\n    }\n    toggle(node, isChecked)\n    updateCheckedKeys()\n    if (nodeClick) {\n      afterNodeCheck(node, isChecked)\n    }\n  }\n\n  const afterNodeCheck = (node: TreeNode, checked: CheckboxValueType) => {\n    const { checkedNodes, checkedKeys } = getChecked()\n    const { halfCheckedNodes, halfCheckedKeys } = getHalfChecked()\n    emit(NODE_CHECK, node.data, {\n      checkedKeys,\n      checkedNodes,\n      halfCheckedKeys,\n      halfCheckedNodes,\n    })\n    emit(NODE_CHECK_CHANGE, node.data, checked)\n  }\n\n  // expose\n  function getCheckedKeys(leafOnly = false): TreeKey[] {\n    return getChecked(leafOnly).checkedKeys\n  }\n\n  function getCheckedNodes(leafOnly = false): TreeNodeData[] {\n    return getChecked(leafOnly).checkedNodes\n  }\n\n  function getHalfCheckedKeys(): TreeKey[] {\n    return getHalfChecked().halfCheckedKeys\n  }\n\n  function getHalfCheckedNodes(): TreeNodeData[] {\n    return getHalfChecked().halfCheckedNodes\n  }\n\n  function getChecked(leafOnly = false): {\n    checkedKeys: TreeKey[]\n    checkedNodes: TreeNodeData[]\n  } {\n    const checkedNodes: TreeNodeData[] = []\n    const keys: TreeKey[] = []\n    if (tree?.value && props.showCheckbox) {\n      const { treeNodeMap } = tree.value\n      checkedKeys.value.forEach((key) => {\n        const node = treeNodeMap.get(key)\n        if (node && (!leafOnly || (leafOnly && node.isLeaf))) {\n          keys.push(key)\n          checkedNodes.push(node.data)\n        }\n      })\n    }\n    return {\n      checkedKeys: keys,\n      checkedNodes,\n    }\n  }\n\n  function getHalfChecked(): {\n    halfCheckedKeys: TreeKey[]\n    halfCheckedNodes: TreeNodeData[]\n  } {\n    const halfCheckedNodes: TreeNodeData[] = []\n    const halfCheckedKeys: TreeKey[] = []\n    if (tree?.value && props.showCheckbox) {\n      const { treeNodeMap } = tree.value\n      indeterminateKeys.value.forEach((key) => {\n        const node = treeNodeMap.get(key)\n        if (node) {\n          halfCheckedKeys.push(key)\n          halfCheckedNodes.push(node.data)\n        }\n      })\n    }\n    return {\n      halfCheckedNodes,\n      halfCheckedKeys,\n    }\n  }\n\n  function setCheckedKeys(keys: TreeKey[]) {\n    checkedKeys.value.clear()\n    indeterminateKeys.value.clear()\n    nextTick(() => {\n      _setCheckedKeys(keys)\n    })\n  }\n\n  function setChecked(key: TreeKey, isChecked: boolean) {\n    if (tree?.value && props.showCheckbox) {\n      const node = tree.value.treeNodeMap.get(key)\n      if (node) {\n        toggleCheckbox(node, isChecked, false)\n      }\n    }\n  }\n\n  function _setCheckedKeys(keys: TreeKey[]) {\n    if (tree?.value) {\n      const { treeNodeMap } = tree.value\n      if (props.showCheckbox && treeNodeMap && keys) {\n        for (const key of keys) {\n          const node = treeNodeMap.get(key)\n          if (node && !isChecked(node)) {\n            toggleCheckbox(node, true, false)\n          }\n        }\n      }\n    }\n  }\n\n  return {\n    updateCheckedKeys,\n    toggleCheckbox,\n    isChecked,\n    isIndeterminate,\n    // expose\n    getCheckedKeys,\n    getCheckedNodes,\n    getHalfCheckedKeys,\n    getHalfCheckedNodes,\n    setChecked,\n    setCheckedKeys,\n  }\n}\n"], "mappings": ";;;;;AAMO,SAASA,QAAQA,CAACC,KAAK,EAAEC,IAAI,EAAE;EACpC,IAAMC,WAAW,GAAGC,GAAG,gBAAiB,IAAIC,GAAG,EAAE,CAAC;EAClD,IAAMC,iBAAiB,GAAGF,GAAG,gBAAiB,IAAIC,GAAG,EAAE,CAAC;EACxD,IAAAE,mBAAA,GAAiBC,kBAAkB,EAAE;IAA7BC,IAAI,GAAAF,mBAAA,CAAJE,IAAI;EACZC,KAAK,CAAC,CAAC;IAAA,OAAMR,IAAI,CAACS,KAAK;EAAA,GAAE;IAAA,OAAMV,KAAK,CAACW,kBAAkB;EAAA,EAAC,EAAE,YAAM;IAC9D,OAAOC,QAAQ,CAAC,YAAM;MACpBC,eAAe,CAACb,KAAK,CAACW,kBAAkB,CAAC;IAC/C,CAAK,CAAC;EACN,CAAG,EAAE;IACDG,SAAS,EAAE;EACf,CAAG,CAAC;EACF,IAAMC,iBAAiB,GAAG,SAApBA,iBAAiBA,CAAA,EAAS;IAC9B,IAAI,CAACd,IAAI,CAACS,KAAK,IAAI,CAACV,KAAK,CAACgB,YAAY,IAAIhB,KAAK,CAACiB,aAAa,EAAE;MAC7D;IACN;IACI,IAAAC,WAAA,GAAuCjB,IAAI,CAACS,KAAK;MAAzCS,gBAAgB,GAAAD,WAAA,CAAhBC,gBAAgB;MAAEC,QAAQ,GAAAF,WAAA,CAARE,QAAQ;IAClC,IAAMC,aAAa,GAAGnB,WAAW,CAACQ,KAAK;IACvC,IAAMY,mBAAmB,kBAAmB,IAAIlB,GAAG,EAAE;IACrD,KAAK,IAAImB,KAAK,GAAGH,QAAQ,GAAG,CAAC,EAAEG,KAAK,IAAI,CAAC,EAAE,EAAEA,KAAK,EAAE;MAClD,IAAMC,KAAK,GAAGL,gBAAgB,CAACM,GAAG,CAACF,KAAK,CAAC;MACzC,IAAI,CAACC,KAAK,EACR;MACFA,KAAK,CAACE,OAAO,CAAC,UAACC,IAAI,EAAK;QACtB,IAAMC,QAAQ,GAAGD,IAAI,CAACC,QAAQ;QAC9B,IAAIA,QAAQ,EAAE;UACZ,IAAIC,UAAU,GAAG,IAAI;UACrB,IAAIC,UAAU,GAAG,KAAK;UAAC,IAAAC,SAAA,GAAAC,0BAAA,CACCJ,QAAQ;YAAAK,KAAA;UAAA;YAAhC,KAAAF,SAAA,CAAAG,CAAA,MAAAD,KAAA,GAAAF,SAAA,CAAAI,CAAA,IAAAC,IAAA,GAAkC;cAAA,IAAvBC,SAAS,GAAAJ,KAAA,CAAAvB,KAAA;cAClB,IAAM4B,GAAG,GAAGD,SAAS,CAACC,GAAG;cACzB,IAAIjB,aAAa,CAACkB,GAAG,CAACD,GAAG,CAAC,EAAE;gBAC1BR,UAAU,GAAG,IAAI;cAC/B,CAAa,MAAM,IAAIR,mBAAmB,CAACiB,GAAG,CAACD,GAAG,CAAC,EAAE;gBACvCT,UAAU,GAAG,KAAK;gBAClBC,UAAU,GAAG,IAAI;gBACjB;cACd,CAAa,MAAM;gBACLD,UAAU,GAAG,KAAK;cAChC;YACA;UAAW,SAAAW,GAAA;YAAAT,SAAA,CAAAU,CAAA,CAAAD,GAAA;UAAA;YAAAT,SAAA,CAAAW,CAAA;UAAA;UACD,IAAIb,UAAU,EAAE;YACdR,aAAa,CAACsB,GAAG,CAAChB,IAAI,CAACW,GAAG,CAAC;UACvC,CAAW,MAAM,IAAIR,UAAU,EAAE;YACrBR,mBAAmB,CAACqB,GAAG,CAAChB,IAAI,CAACW,GAAG,CAAC;YACjCjB,aAAa,CAACuB,MAAM,CAACjB,IAAI,CAACW,GAAG,CAAC;UAC1C,CAAW,MAAM;YACLjB,aAAa,CAACuB,MAAM,CAACjB,IAAI,CAACW,GAAG,CAAC;YAC9BhB,mBAAmB,CAACsB,MAAM,CAACjB,IAAI,CAACW,GAAG,CAAC;UAChD;QACA;MACA,CAAO,CAAC;IACR;IACIjC,iBAAiB,CAACK,KAAK,GAAGY,mBAAmB;EACjD,CAAG;EACD,IAAMuB,SAAS,GAAG,SAAZA,SAASA,CAAIlB,IAAI;IAAA,OAAKzB,WAAW,CAACQ,KAAK,CAAC6B,GAAG,CAACZ,IAAI,CAACW,GAAG,CAAC;EAAA;EAC3D,IAAMQ,eAAe,GAAG,SAAlBA,eAAeA,CAAInB,IAAI;IAAA,OAAKtB,iBAAiB,CAACK,KAAK,CAAC6B,GAAG,CAACZ,IAAI,CAACW,GAAG,CAAC;EAAA;EACvE,IAAMS,cAAc,GAAG,SAAjBA,cAAcA,CAAIpB,IAAI,EAAEqB,UAAU,EAAuB;IAAA,IAArBC,SAAS,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,IAAI;IACxD,IAAM7B,aAAa,GAAGnB,WAAW,CAACQ,KAAK;IACvC,IAAM2C,OAAM,GAAG,SAATA,MAAMA,CAAIC,KAAK,EAAEC,OAAO,EAAK;MACjClC,aAAa,CAACkC,OAAO,GAAGC,gBAAgB,CAACC,GAAG,GAAGD,gBAAgB,CAACE,MAAM,CAAC,CAACJ,KAAK,CAAChB,GAAG,CAAC;MAClF,IAAMV,QAAQ,GAAG0B,KAAK,CAAC1B,QAAQ;MAC/B,IAAI,CAAC5B,KAAK,CAACiB,aAAa,IAAIW,QAAQ,EAAE;QACpCA,QAAQ,CAACF,OAAO,CAAC,UAACW,SAAS,EAAK;UAC9B,IAAI,CAACA,SAAS,CAACsB,QAAQ,EAAE;YACvBN,OAAM,CAAChB,SAAS,EAAEkB,OAAO,CAAC;UACtC;QACA,CAAS,CAAC;MACV;IACA,CAAK;IACDF,OAAM,CAAC1B,IAAI,EAAEqB,UAAU,CAAC;IACxBjC,iBAAiB,EAAE;IACnB,IAAIkC,SAAS,EAAE;MACbW,cAAc,CAACjC,IAAI,EAAEqB,UAAU,CAAC;IACtC;EACA,CAAG;EACD,IAAMY,cAAc,GAAG,SAAjBA,cAAcA,CAAIjC,IAAI,EAAE4B,OAAO,EAAK;IACxC,IAAAM,WAAA,GAAoDC,UAAU,EAAE;MAAxDC,YAAY,GAAAF,WAAA,CAAZE,YAAY;MAAeC,YAAY,GAAAH,WAAA,CAAzB3D,WAAW;IACjC,IAAA+D,eAAA,GAA8CC,cAAc,EAAE;MAAtDC,gBAAgB,GAAAF,eAAA,CAAhBE,gBAAgB;MAAEC,eAAe,GAAAH,eAAA,CAAfG,eAAe;IACzC5D,IAAI,CAAC6D,UAAU,EAAE1C,IAAI,CAAC2C,IAAI,EAAE;MAC1BpE,WAAW,EAAE8D,YAAY;MACzBD,YAAY;MACZK,eAAe;MACfD;IACN,CAAK,CAAC;IACF3D,IAAI,CAAC+D,iBAAiB,EAAE5C,IAAI,CAAC2C,IAAI,EAAEf,OAAO,CAAC;EAC/C,CAAG;EACD,SAASiB,cAAcA,CAAA,EAAmB;IAAA,IAAlBC,QAAQ,GAAAvB,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,KAAK;IACtC,OAAOY,UAAU,CAACW,QAAQ,CAAC,CAACvE,WAAW;EAC3C;EACE,SAASwE,eAAeA,CAAA,EAAmB;IAAA,IAAlBD,QAAQ,GAAAvB,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,KAAK;IACvC,OAAOY,UAAU,CAACW,QAAQ,CAAC,CAACV,YAAY;EAC5C;EACE,SAASY,kBAAkBA,CAAA,EAAG;IAC5B,OAAOT,cAAc,EAAE,CAACE,eAAe;EAC3C;EACE,SAASQ,mBAAmBA,CAAA,EAAG;IAC7B,OAAOV,cAAc,EAAE,CAACC,gBAAgB;EAC5C;EACE,SAASL,UAAUA,CAAA,EAAmB;IAAA,IAAlBW,QAAQ,GAAAvB,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,KAAK;IAClC,IAAMa,YAAY,GAAG,EAAE;IACvB,IAAMc,IAAI,GAAG,EAAE;IACf,IAAI,CAAC5E,IAAI,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,IAAI,CAACS,KAAK,KAAKV,KAAK,CAACgB,YAAY,EAAE;MAC9D,IAAQ8D,WAAW,GAAK7E,IAAI,CAACS,KAAK,CAA1BoE,WAAW;MACnB5E,WAAW,CAACQ,KAAK,CAACgB,OAAO,CAAC,UAACY,GAAG,EAAK;QACjC,IAAMX,IAAI,GAAGmD,WAAW,CAACrD,GAAG,CAACa,GAAG,CAAC;QACjC,IAAIX,IAAI,KAAK,CAAC8C,QAAQ,IAAIA,QAAQ,IAAI9C,IAAI,CAACoD,MAAM,CAAC,EAAE;UAClDF,IAAI,CAACG,IAAI,CAAC1C,GAAG,CAAC;UACdyB,YAAY,CAACiB,IAAI,CAACrD,IAAI,CAAC2C,IAAI,CAAC;QACtC;MACA,CAAO,CAAC;IACR;IACI,OAAO;MACLpE,WAAW,EAAE2E,IAAI;MACjBd;IACN,CAAK;EACL;EACE,SAASG,cAAcA,CAAA,EAAG;IACxB,IAAMC,gBAAgB,GAAG,EAAE;IAC3B,IAAMC,eAAe,GAAG,EAAE;IAC1B,IAAI,CAACnE,IAAI,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,IAAI,CAACS,KAAK,KAAKV,KAAK,CAACgB,YAAY,EAAE;MAC9D,IAAQ8D,WAAW,GAAK7E,IAAI,CAACS,KAAK,CAA1BoE,WAAW;MACnBzE,iBAAiB,CAACK,KAAK,CAACgB,OAAO,CAAC,UAACY,GAAG,EAAK;QACvC,IAAMX,IAAI,GAAGmD,WAAW,CAACrD,GAAG,CAACa,GAAG,CAAC;QACjC,IAAIX,IAAI,EAAE;UACRyC,eAAe,CAACY,IAAI,CAAC1C,GAAG,CAAC;UACzB6B,gBAAgB,CAACa,IAAI,CAACrD,IAAI,CAAC2C,IAAI,CAAC;QAC1C;MACA,CAAO,CAAC;IACR;IACI,OAAO;MACLH,gBAAgB;MAChBC;IACN,CAAK;EACL;EACE,SAASa,cAAcA,CAACJ,IAAI,EAAE;IAC5B3E,WAAW,CAACQ,KAAK,CAACwE,KAAK,EAAE;IACzB7E,iBAAiB,CAACK,KAAK,CAACwE,KAAK,EAAE;IAC/BtE,QAAQ,CAAC,YAAM;MACbC,eAAe,CAACgE,IAAI,CAAC;IAC3B,CAAK,CAAC;EACN;EACE,SAASM,UAAUA,CAAC7C,GAAG,EAAEU,UAAU,EAAE;IACnC,IAAI,CAAC/C,IAAI,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,IAAI,CAACS,KAAK,KAAKV,KAAK,CAACgB,YAAY,EAAE;MAC9D,IAAMW,IAAI,GAAG1B,IAAI,CAACS,KAAK,CAACoE,WAAW,CAACrD,GAAG,CAACa,GAAG,CAAC;MAC5C,IAAIX,IAAI,EAAE;QACRoB,cAAc,CAACpB,IAAI,EAAEqB,UAAU,EAAE,KAAK,CAAC;MAC/C;IACA;EACA;EACE,SAASnC,eAAeA,CAACgE,IAAI,EAAE;IAC7B,IAAI5E,IAAI,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,IAAI,CAACS,KAAK,EAAE;MACtC,IAAQoE,WAAW,GAAK7E,IAAI,CAACS,KAAK,CAA1BoE,WAAW;MACnB,IAAI9E,KAAK,CAACgB,YAAY,IAAI8D,WAAW,IAAID,IAAI,EAAE;QAAA,IAAAO,UAAA,GAAApD,0BAAA,CAC3B6C,IAAI;UAAAQ,MAAA;QAAA;UAAtB,KAAAD,UAAA,CAAAlD,CAAA,MAAAmD,MAAA,GAAAD,UAAA,CAAAjD,CAAA,IAAAC,IAAA,GAAwB;YAAA,IAAbE,GAAG,GAAA+C,MAAA,CAAA3E,KAAA;YACZ,IAAMiB,IAAI,GAAGmD,WAAW,CAACrD,GAAG,CAACa,GAAG,CAAC;YACjC,IAAIX,IAAI,IAAI,CAACkB,SAAS,CAAClB,IAAI,CAAC,EAAE;cAC5BoB,cAAc,CAACpB,IAAI,EAAE,IAAI,EAAE,KAAK,CAAC;YAC7C;UACA;QAAS,SAAAa,GAAA;UAAA4C,UAAA,CAAA3C,CAAA,CAAAD,GAAA;QAAA;UAAA4C,UAAA,CAAA1C,CAAA;QAAA;MACT;IACA;EACA;EACE,OAAO;IACL3B,iBAAiB;IACjBgC,cAAc;IACdF,SAAS;IACTC,eAAe;IACf0B,cAAc;IACdE,eAAe;IACfC,kBAAkB;IAClBC,mBAAmB;IACnBO,UAAU;IACVF;EACJ,CAAG;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
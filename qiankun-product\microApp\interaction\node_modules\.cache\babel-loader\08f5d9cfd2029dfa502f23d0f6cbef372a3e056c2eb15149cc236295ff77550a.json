{"ast": null, "code": "import { ref, onMounted, computed, watch, nextTick } from 'vue';\nimport Sortable from 'sortablejs'; // 引入插件\n\nvar __default__ = {\n  name: 'GlobalDragInput'\n};\nexport default /*@__PURE__*/Object.assign(__default__, {\n  props: {\n    data: {\n      type: Array,\n      default: function _default() {\n        return [];\n      }\n    },\n    disabled: {\n      type: Boolean,\n      default: false\n    }\n  },\n  emits: ['callback'],\n  setup(__props, _ref) {\n    var __expose = _ref.expose,\n      __emit = _ref.emit;\n    __expose();\n    var props = __props;\n    var emit = __emit;\n    var guid = function guid() {\n      return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {\n        var r = Math.random() * 16 | 0,\n          v = c == 'x' ? r : r & 0x3 | 0x8;\n        return v.toString(16);\n      });\n    };\n    var optionsRef = ref();\n    var options = ref([{\n      uid: guid(),\n      name: ''\n    }]);\n    var disabled = computed(function () {\n      return props.disabled;\n    });\n    onMounted(function () {\n      nextTick(function () {\n        if (!props.disabled) {\n          rowDrop();\n        }\n      });\n    });\n    var optionsNew = function optionsNew() {\n      options.value.push({\n        uid: guid(),\n        name: ''\n      });\n    };\n    var optionsChange = function optionsChange() {\n      emit('callback', options.value.filter(function (v) {\n        return v.name.replace(/(^\\s*)|(\\s*$)/g, '');\n      }).map(function (v) {\n        return v.name.replace(/(^\\s*)|(\\s*$)/g, '');\n      }));\n    };\n    var optionsDel = function optionsDel(row) {\n      options.value = options.value.filter(function (v) {\n        return v.uid !== row.uid;\n      });\n      if (row.name) {\n        optionsChange();\n      }\n    };\n    var rowDrop = function rowDrop() {\n      Sortable.create(optionsRef.value, {\n        handle: '.global-drag-input-icon',\n        animation: 150,\n        onEnd(_ref2) {\n          var newIndex = _ref2.newIndex,\n            oldIndex = _ref2.oldIndex;\n          if (disabled.value) return;\n          if (newIndex == oldIndex) return;\n          options.value.splice(newIndex, 0, options.value.splice(oldIndex, 1)[0]);\n          var newArray = options.value.slice(0);\n          options.value = [];\n          nextTick(function () {\n            options.value = newArray;\n            optionsChange();\n          });\n        }\n      });\n    };\n    watch(function () {\n      return props.data;\n    }, function () {\n      if (props.data.length) {\n        options.value = props.data.map(function (v) {\n          return {\n            uid: guid(),\n            name: v\n          };\n        });\n        optionsChange();\n      }\n    }, {\n      immediate: true\n    });\n    var __returned__ = {\n      props,\n      emit,\n      guid,\n      optionsRef,\n      options,\n      disabled,\n      optionsNew,\n      optionsChange,\n      optionsDel,\n      rowDrop,\n      ref,\n      onMounted,\n      computed,\n      watch,\n      nextTick,\n      get Sortable() {\n        return Sortable;\n      }\n    };\n    Object.defineProperty(__returned__, '__isScriptSetup', {\n      enumerable: false,\n      value: true\n    });\n    return __returned__;\n  }\n});", "map": {"version": 3, "names": ["ref", "onMounted", "computed", "watch", "nextTick", "Sortable", "__default__", "name", "props", "__props", "emit", "__emit", "guid", "replace", "c", "r", "Math", "random", "v", "toString", "optionsRef", "options", "uid", "disabled", "rowDrop", "optionsNew", "value", "push", "optionsChange", "filter", "map", "optionsDel", "row", "create", "handle", "animation", "onEnd", "_ref2", "newIndex", "oldIndex", "splice", "newArray", "slice", "data", "length", "immediate"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/common/components/global-drag-input/global-drag-input.vue"], "sourcesContent": ["<template>\r\n  <div class=\"global-drag-input\" ref=\"optionsRef\">\r\n    <div class=\"global-drag-input-item\" v-for=\"item in options\" :key=\"item.uid\">\r\n      <div class=\"global-drag-input-icon\"></div>\r\n      <el-input v-model=\"item.name\" @change=\"optionsChange\" placeholder=\"请输入\" :disabled=\"disabled\" clearable />\r\n      <div class=\"global-drag-input-button\">\r\n        <el-icon @click=\"optionsNew\" v-if=\"!disabled\">\r\n          <CirclePlus />\r\n        </el-icon>\r\n        <el-icon @click=\"optionsDel(item)\" v-if=\"options.length > 1 && !disabled\">\r\n          <Remove />\r\n        </el-icon>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n<script>\r\nexport default { name: 'GlobalDragInput' }\r\n</script>\r\n<script setup>\r\nimport { ref, onMounted, computed, watch, nextTick } from 'vue'\r\nimport Sortable from 'sortablejs' // 引入插件\r\nconst props = defineProps({\r\n  data: { type: Array, default: () => [] },\r\n  disabled: { type: Boolean, default: false }\r\n})\r\nconst emit = defineEmits(['callback'])\r\nconst guid = () => {\r\n  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, (c) => {\r\n    var r = (Math.random() * 16) | 0,\r\n      v = c == 'x' ? r : (r & 0x3) | 0x8\r\n    return v.toString(16)\r\n  })\r\n}\r\nconst optionsRef = ref()\r\nconst options = ref([{ uid: guid(), name: '' }])\r\nconst disabled = computed(() => props.disabled)\r\n\r\nonMounted(() => {\r\n  nextTick(() => {\r\n    if (!props.disabled) {\r\n      rowDrop()\r\n    }\r\n  })\r\n})\r\n\r\nconst optionsNew = () => {\r\n  options.value.push({ uid: guid(), name: '' })\r\n}\r\nconst optionsChange = () => {\r\n  emit(\r\n    'callback',\r\n    options.value.filter((v) => v.name.replace(/(^\\s*)|(\\s*$)/g, '')).map((v) => v.name.replace(/(^\\s*)|(\\s*$)/g, ''))\r\n  )\r\n}\r\nconst optionsDel = (row) => {\r\n  options.value = options.value.filter((v) => v.uid !== row.uid)\r\n  if (row.name) {\r\n    optionsChange()\r\n  }\r\n}\r\nconst rowDrop = () => {\r\n  Sortable.create(optionsRef.value, {\r\n    handle: '.global-drag-input-icon',\r\n    animation: 150,\r\n    onEnd({ newIndex, oldIndex }) {\r\n      if (disabled.value) return\r\n      if (newIndex == oldIndex) return\r\n      options.value.splice(newIndex, 0, options.value.splice(oldIndex, 1)[0])\r\n      const newArray = options.value.slice(0)\r\n      options.value = []\r\n      nextTick(() => {\r\n        options.value = newArray\r\n        optionsChange()\r\n      })\r\n    }\r\n  })\r\n}\r\nwatch(\r\n  () => props.data,\r\n  () => {\r\n    if (props.data.length) {\r\n      options.value = props.data.map((v) => ({ uid: guid(), name: v }))\r\n      optionsChange()\r\n    }\r\n  },\r\n  { immediate: true }\r\n)\r\n</script>\r\n<style lang=\"scss\">\r\n.global-drag-input {\r\n  width: 100%;\r\n  padding-right: 20px;\r\n  padding-bottom: 22px;\r\n\r\n  .global-drag-input-item + .global-drag-input-item {\r\n    margin-top: 10px;\r\n  }\r\n\r\n  .global-drag-input-item {\r\n    width: 100%;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: space-between;\r\n\r\n    .global-drag-input-icon {\r\n      width: 20px;\r\n      height: 20px;\r\n      background: url('./img/global_form_options.png') no-repeat;\r\n      background-size: 100% 100%;\r\n      cursor: pointer;\r\n    }\r\n\r\n    .zy-el-input {\r\n      width: calc(100% - 88px);\r\n    }\r\n\r\n    .global-drag-input-button {\r\n      width: 52px;\r\n      display: flex;\r\n      align-items: center;\r\n\r\n      .zy-el-icon {\r\n        color: var(--zy-el-color-primary);\r\n        font-size: 17px;\r\n        margin-left: 9px;\r\n        cursor: pointer;\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n"], "mappings": "AAoBA,SAASA,GAAG,EAAEC,SAAS,EAAEC,QAAQ,EAAEC,KAAK,EAAEC,QAAQ,QAAQ,KAAK;AAC/D,OAAOC,QAAQ,MAAM,YAAY,EAAC;;AAJlC,IAAAC,WAAA,GAAe;EAAEC,IAAI,EAAE;AAAkB,CAAC;;;;;;;;;;;;;;;;;;;IAK1C,IAAMC,KAAK,GAAGC,OAGZ;IACF,IAAMC,IAAI,GAAGC,MAAyB;IACtC,IAAMC,IAAI,GAAG,SAAPA,IAAIA,CAAA,EAAS;MACjB,OAAO,sCAAsC,CAACC,OAAO,CAAC,OAAO,EAAE,UAACC,CAAC,EAAK;QACpE,IAAIC,CAAC,GAAIC,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,EAAE,GAAI,CAAC;UAC9BC,CAAC,GAAGJ,CAAC,IAAI,GAAG,GAAGC,CAAC,GAAIA,CAAC,GAAG,GAAG,GAAI,GAAG;QACpC,OAAOG,CAAC,CAACC,QAAQ,CAAC,EAAE,CAAC;MACvB,CAAC,CAAC;IACJ,CAAC;IACD,IAAMC,UAAU,GAAGpB,GAAG,CAAC,CAAC;IACxB,IAAMqB,OAAO,GAAGrB,GAAG,CAAC,CAAC;MAAEsB,GAAG,EAAEV,IAAI,CAAC,CAAC;MAAEL,IAAI,EAAE;IAAG,CAAC,CAAC,CAAC;IAChD,IAAMgB,QAAQ,GAAGrB,QAAQ,CAAC;MAAA,OAAMM,KAAK,CAACe,QAAQ;IAAA,EAAC;IAE/CtB,SAAS,CAAC,YAAM;MACdG,QAAQ,CAAC,YAAM;QACb,IAAI,CAACI,KAAK,CAACe,QAAQ,EAAE;UACnBC,OAAO,CAAC,CAAC;QACX;MACF,CAAC,CAAC;IACJ,CAAC,CAAC;IAEF,IAAMC,UAAU,GAAG,SAAbA,UAAUA,CAAA,EAAS;MACvBJ,OAAO,CAACK,KAAK,CAACC,IAAI,CAAC;QAAEL,GAAG,EAAEV,IAAI,CAAC,CAAC;QAAEL,IAAI,EAAE;MAAG,CAAC,CAAC;IAC/C,CAAC;IACD,IAAMqB,aAAa,GAAG,SAAhBA,aAAaA,CAAA,EAAS;MAC1BlB,IAAI,CACF,UAAU,EACVW,OAAO,CAACK,KAAK,CAACG,MAAM,CAAC,UAACX,CAAC;QAAA,OAAKA,CAAC,CAACX,IAAI,CAACM,OAAO,CAAC,gBAAgB,EAAE,EAAE,CAAC;MAAA,EAAC,CAACiB,GAAG,CAAC,UAACZ,CAAC;QAAA,OAAKA,CAAC,CAACX,IAAI,CAACM,OAAO,CAAC,gBAAgB,EAAE,EAAE,CAAC;MAAA,EACnH,CAAC;IACH,CAAC;IACD,IAAMkB,UAAU,GAAG,SAAbA,UAAUA,CAAIC,GAAG,EAAK;MAC1BX,OAAO,CAACK,KAAK,GAAGL,OAAO,CAACK,KAAK,CAACG,MAAM,CAAC,UAACX,CAAC;QAAA,OAAKA,CAAC,CAACI,GAAG,KAAKU,GAAG,CAACV,GAAG;MAAA,EAAC;MAC9D,IAAIU,GAAG,CAACzB,IAAI,EAAE;QACZqB,aAAa,CAAC,CAAC;MACjB;IACF,CAAC;IACD,IAAMJ,OAAO,GAAG,SAAVA,OAAOA,CAAA,EAAS;MACpBnB,QAAQ,CAAC4B,MAAM,CAACb,UAAU,CAACM,KAAK,EAAE;QAChCQ,MAAM,EAAE,yBAAyB;QACjCC,SAAS,EAAE,GAAG;QACdC,KAAKA,CAAAC,KAAA,EAAyB;UAAA,IAAtBC,QAAQ,GAAAD,KAAA,CAARC,QAAQ;YAAEC,QAAQ,GAAAF,KAAA,CAARE,QAAQ;UACxB,IAAIhB,QAAQ,CAACG,KAAK,EAAE;UACpB,IAAIY,QAAQ,IAAIC,QAAQ,EAAE;UAC1BlB,OAAO,CAACK,KAAK,CAACc,MAAM,CAACF,QAAQ,EAAE,CAAC,EAAEjB,OAAO,CAACK,KAAK,CAACc,MAAM,CAACD,QAAQ,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACvE,IAAME,QAAQ,GAAGpB,OAAO,CAACK,KAAK,CAACgB,KAAK,CAAC,CAAC,CAAC;UACvCrB,OAAO,CAACK,KAAK,GAAG,EAAE;UAClBtB,QAAQ,CAAC,YAAM;YACbiB,OAAO,CAACK,KAAK,GAAGe,QAAQ;YACxBb,aAAa,CAAC,CAAC;UACjB,CAAC,CAAC;QACJ;MACF,CAAC,CAAC;IACJ,CAAC;IACDzB,KAAK,CACH;MAAA,OAAMK,KAAK,CAACmC,IAAI;IAAA,GAChB,YAAM;MACJ,IAAInC,KAAK,CAACmC,IAAI,CAACC,MAAM,EAAE;QACrBvB,OAAO,CAACK,KAAK,GAAGlB,KAAK,CAACmC,IAAI,CAACb,GAAG,CAAC,UAACZ,CAAC;UAAA,OAAM;YAAEI,GAAG,EAAEV,IAAI,CAAC,CAAC;YAAEL,IAAI,EAAEW;UAAE,CAAC;QAAA,CAAC,CAAC;QACjEU,aAAa,CAAC,CAAC;MACjB;IACF,CAAC,EACD;MAAEiB,SAAS,EAAE;IAAK,CACpB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
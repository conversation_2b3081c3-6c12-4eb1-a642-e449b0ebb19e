{"ast": null, "code": "var _excluded = [\"crossAxis\", \"alignment\", \"allowedPlacements\", \"autoAlignment\"],\n  _excluded2 = [\"mainAxis\", \"crossAxis\", \"fallbackPlacements\", \"fallbackStrategy\", \"fallbackAxisSideDirection\", \"flipAlignment\"],\n  _excluded3 = [\"strategy\"],\n  _excluded4 = [\"mainAxis\", \"crossAxis\", \"limiter\"],\n  _excluded5 = [\"apply\"];\nfunction _objectWithoutProperties(e, t) { if (null == e) return {}; var o, r, i = _objectWithoutPropertiesLoose(e, t); if (Object.getOwnPropertySymbols) { var s = Object.getOwnPropertySymbols(e); for (r = 0; r < s.length; r++) o = s[r], t.includes(o) || {}.propertyIsEnumerable.call(e, o) && (i[o] = e[o]); } return i; }\nfunction _objectWithoutPropertiesLoose(r, e) { if (null == r) return {}; var t = {}; for (var n in r) if ({}.hasOwnProperty.call(r, n)) { if (e.includes(n)) continue; t[n] = r[n]; } return t; }\nfunction _toConsumableArray(r) { return _arrayWithoutHoles(r) || _iterableToArray(r) || _unsupportedIterableToArray(r) || _nonIterableSpread(); }\nfunction _nonIterableSpread() { throw new TypeError(\"Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); }\nfunction _unsupportedIterableToArray(r, a) { if (r) { if (\"string\" == typeof r) return _arrayLikeToArray(r, a); var t = {}.toString.call(r).slice(8, -1); return \"Object\" === t && r.constructor && (t = r.constructor.name), \"Map\" === t || \"Set\" === t ? Array.from(r) : \"Arguments\" === t || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t) ? _arrayLikeToArray(r, a) : void 0; } }\nfunction _iterableToArray(r) { if (\"undefined\" != typeof Symbol && null != r[Symbol.iterator] || null != r[\"@@iterator\"]) return Array.from(r); }\nfunction _arrayWithoutHoles(r) { if (Array.isArray(r)) return _arrayLikeToArray(r); }\nfunction _arrayLikeToArray(r, a) { (null == a || a > r.length) && (a = r.length); for (var e = 0, n = Array(a); e < a; e++) n[e] = r[e]; return n; }\nfunction _regeneratorRuntime() { \"use strict\"; /*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */ _regeneratorRuntime = function _regeneratorRuntime() { return e; }; var t, e = {}, r = Object.prototype, n = r.hasOwnProperty, o = Object.defineProperty || function (t, e, r) { t[e] = r.value; }, i = \"function\" == typeof Symbol ? Symbol : {}, a = i.iterator || \"@@iterator\", c = i.asyncIterator || \"@@asyncIterator\", u = i.toStringTag || \"@@toStringTag\"; function define(t, e, r) { return Object.defineProperty(t, e, { value: r, enumerable: !0, configurable: !0, writable: !0 }), t[e]; } try { define({}, \"\"); } catch (t) { define = function define(t, e, r) { return t[e] = r; }; } function wrap(t, e, r, n) { var i = e && e.prototype instanceof Generator ? e : Generator, a = Object.create(i.prototype), c = new Context(n || []); return o(a, \"_invoke\", { value: makeInvokeMethod(t, r, c) }), a; } function tryCatch(t, e, r) { try { return { type: \"normal\", arg: t.call(e, r) }; } catch (t) { return { type: \"throw\", arg: t }; } } e.wrap = wrap; var h = \"suspendedStart\", l = \"suspendedYield\", f = \"executing\", s = \"completed\", y = {}; function Generator() {} function GeneratorFunction() {} function GeneratorFunctionPrototype() {} var p = {}; define(p, a, function () { return this; }); var d = Object.getPrototypeOf, v = d && d(d(values([]))); v && v !== r && n.call(v, a) && (p = v); var g = GeneratorFunctionPrototype.prototype = Generator.prototype = Object.create(p); function defineIteratorMethods(t) { [\"next\", \"throw\", \"return\"].forEach(function (e) { define(t, e, function (t) { return this._invoke(e, t); }); }); } function AsyncIterator(t, e) { function invoke(r, o, i, a) { var c = tryCatch(t[r], t, o); if (\"throw\" !== c.type) { var u = c.arg, h = u.value; return h && \"object\" == typeof h && n.call(h, \"__await\") ? e.resolve(h.__await).then(function (t) { invoke(\"next\", t, i, a); }, function (t) { invoke(\"throw\", t, i, a); }) : e.resolve(h).then(function (t) { u.value = t, i(u); }, function (t) { return invoke(\"throw\", t, i, a); }); } a(c.arg); } var r; o(this, \"_invoke\", { value: function value(t, n) { function callInvokeWithMethodAndArg() { return new e(function (e, r) { invoke(t, n, e, r); }); } return r = r ? r.then(callInvokeWithMethodAndArg, callInvokeWithMethodAndArg) : callInvokeWithMethodAndArg(); } }); } function makeInvokeMethod(e, r, n) { var o = h; return function (i, a) { if (o === f) throw Error(\"Generator is already running\"); if (o === s) { if (\"throw\" === i) throw a; return { value: t, done: !0 }; } for (n.method = i, n.arg = a;;) { var c = n.delegate; if (c) { var u = maybeInvokeDelegate(c, n); if (u) { if (u === y) continue; return u; } } if (\"next\" === n.method) n.sent = n._sent = n.arg;else if (\"throw\" === n.method) { if (o === h) throw o = s, n.arg; n.dispatchException(n.arg); } else \"return\" === n.method && n.abrupt(\"return\", n.arg); o = f; var p = tryCatch(e, r, n); if (\"normal\" === p.type) { if (o = n.done ? s : l, p.arg === y) continue; return { value: p.arg, done: n.done }; } \"throw\" === p.type && (o = s, n.method = \"throw\", n.arg = p.arg); } }; } function maybeInvokeDelegate(e, r) { var n = r.method, o = e.iterator[n]; if (o === t) return r.delegate = null, \"throw\" === n && e.iterator.return && (r.method = \"return\", r.arg = t, maybeInvokeDelegate(e, r), \"throw\" === r.method) || \"return\" !== n && (r.method = \"throw\", r.arg = new TypeError(\"The iterator does not provide a '\" + n + \"' method\")), y; var i = tryCatch(o, e.iterator, r.arg); if (\"throw\" === i.type) return r.method = \"throw\", r.arg = i.arg, r.delegate = null, y; var a = i.arg; return a ? a.done ? (r[e.resultName] = a.value, r.next = e.nextLoc, \"return\" !== r.method && (r.method = \"next\", r.arg = t), r.delegate = null, y) : a : (r.method = \"throw\", r.arg = new TypeError(\"iterator result is not an object\"), r.delegate = null, y); } function pushTryEntry(t) { var e = { tryLoc: t[0] }; 1 in t && (e.catchLoc = t[1]), 2 in t && (e.finallyLoc = t[2], e.afterLoc = t[3]), this.tryEntries.push(e); } function resetTryEntry(t) { var e = t.completion || {}; e.type = \"normal\", delete e.arg, t.completion = e; } function Context(t) { this.tryEntries = [{ tryLoc: \"root\" }], t.forEach(pushTryEntry, this), this.reset(!0); } function values(e) { if (e || \"\" === e) { var r = e[a]; if (r) return r.call(e); if (\"function\" == typeof e.next) return e; if (!isNaN(e.length)) { var o = -1, i = function next() { for (; ++o < e.length;) if (n.call(e, o)) return next.value = e[o], next.done = !1, next; return next.value = t, next.done = !0, next; }; return i.next = i; } } throw new TypeError(typeof e + \" is not iterable\"); } return GeneratorFunction.prototype = GeneratorFunctionPrototype, o(g, \"constructor\", { value: GeneratorFunctionPrototype, configurable: !0 }), o(GeneratorFunctionPrototype, \"constructor\", { value: GeneratorFunction, configurable: !0 }), GeneratorFunction.displayName = define(GeneratorFunctionPrototype, u, \"GeneratorFunction\"), e.isGeneratorFunction = function (t) { var e = \"function\" == typeof t && t.constructor; return !!e && (e === GeneratorFunction || \"GeneratorFunction\" === (e.displayName || e.name)); }, e.mark = function (t) { return Object.setPrototypeOf ? Object.setPrototypeOf(t, GeneratorFunctionPrototype) : (t.__proto__ = GeneratorFunctionPrototype, define(t, u, \"GeneratorFunction\")), t.prototype = Object.create(g), t; }, e.awrap = function (t) { return { __await: t }; }, defineIteratorMethods(AsyncIterator.prototype), define(AsyncIterator.prototype, c, function () { return this; }), e.AsyncIterator = AsyncIterator, e.async = function (t, r, n, o, i) { void 0 === i && (i = Promise); var a = new AsyncIterator(wrap(t, r, n, o), i); return e.isGeneratorFunction(r) ? a : a.next().then(function (t) { return t.done ? t.value : a.next(); }); }, defineIteratorMethods(g), define(g, u, \"Generator\"), define(g, a, function () { return this; }), define(g, \"toString\", function () { return \"[object Generator]\"; }), e.keys = function (t) { var e = Object(t), r = []; for (var n in e) r.push(n); return r.reverse(), function next() { for (; r.length;) { var t = r.pop(); if (t in e) return next.value = t, next.done = !1, next; } return next.done = !0, next; }; }, e.values = values, Context.prototype = { constructor: Context, reset: function reset(e) { if (this.prev = 0, this.next = 0, this.sent = this._sent = t, this.done = !1, this.delegate = null, this.method = \"next\", this.arg = t, this.tryEntries.forEach(resetTryEntry), !e) for (var r in this) \"t\" === r.charAt(0) && n.call(this, r) && !isNaN(+r.slice(1)) && (this[r] = t); }, stop: function stop() { this.done = !0; var t = this.tryEntries[0].completion; if (\"throw\" === t.type) throw t.arg; return this.rval; }, dispatchException: function dispatchException(e) { if (this.done) throw e; var r = this; function handle(n, o) { return a.type = \"throw\", a.arg = e, r.next = n, o && (r.method = \"next\", r.arg = t), !!o; } for (var o = this.tryEntries.length - 1; o >= 0; --o) { var i = this.tryEntries[o], a = i.completion; if (\"root\" === i.tryLoc) return handle(\"end\"); if (i.tryLoc <= this.prev) { var c = n.call(i, \"catchLoc\"), u = n.call(i, \"finallyLoc\"); if (c && u) { if (this.prev < i.catchLoc) return handle(i.catchLoc, !0); if (this.prev < i.finallyLoc) return handle(i.finallyLoc); } else if (c) { if (this.prev < i.catchLoc) return handle(i.catchLoc, !0); } else { if (!u) throw Error(\"try statement without catch or finally\"); if (this.prev < i.finallyLoc) return handle(i.finallyLoc); } } } }, abrupt: function abrupt(t, e) { for (var r = this.tryEntries.length - 1; r >= 0; --r) { var o = this.tryEntries[r]; if (o.tryLoc <= this.prev && n.call(o, \"finallyLoc\") && this.prev < o.finallyLoc) { var i = o; break; } } i && (\"break\" === t || \"continue\" === t) && i.tryLoc <= e && e <= i.finallyLoc && (i = null); var a = i ? i.completion : {}; return a.type = t, a.arg = e, i ? (this.method = \"next\", this.next = i.finallyLoc, y) : this.complete(a); }, complete: function complete(t, e) { if (\"throw\" === t.type) throw t.arg; return \"break\" === t.type || \"continue\" === t.type ? this.next = t.arg : \"return\" === t.type ? (this.rval = this.arg = t.arg, this.method = \"return\", this.next = \"end\") : \"normal\" === t.type && e && (this.next = e), y; }, finish: function finish(t) { for (var e = this.tryEntries.length - 1; e >= 0; --e) { var r = this.tryEntries[e]; if (r.finallyLoc === t) return this.complete(r.completion, r.afterLoc), resetTryEntry(r), y; } }, catch: function _catch(t) { for (var e = this.tryEntries.length - 1; e >= 0; --e) { var r = this.tryEntries[e]; if (r.tryLoc === t) { var n = r.completion; if (\"throw\" === n.type) { var o = n.arg; resetTryEntry(r); } return o; } } throw Error(\"illegal catch attempt\"); }, delegateYield: function delegateYield(e, r, n) { return this.delegate = { iterator: values(e), resultName: r, nextLoc: n }, \"next\" === this.method && (this.arg = t), y; } }, e; }\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nfunction _defineProperty(e, r, t) { return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, { value: t, enumerable: !0, configurable: !0, writable: !0 }) : e[r] = t, e; }\nfunction _toPropertyKey(t) { var i = _toPrimitive(t, \"string\"); return \"symbol\" == typeof i ? i : i + \"\"; }\nfunction _toPrimitive(t, r) { if (\"object\" != typeof t || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || \"default\"); if (\"object\" != typeof i) return i; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (\"string\" === r ? String : Number)(t); }\nfunction asyncGeneratorStep(n, t, e, r, o, a, c) { try { var i = n[a](c), u = i.value; } catch (n) { return void e(n); } i.done ? t(u) : Promise.resolve(u).then(r, o); }\nfunction _asyncToGenerator(n) { return function () { var t = this, e = arguments; return new Promise(function (r, o) { var a = n.apply(t, e); function _next(n) { asyncGeneratorStep(a, r, o, _next, _throw, \"next\", n); } function _throw(n) { asyncGeneratorStep(a, r, o, _next, _throw, \"throw\", n); } _next(void 0); }); }; }\nimport { getSideAxis, getAlignmentAxis, getAxisLength, getSide, getAlignment, evaluate, getPaddingObject, rectToClientRect, min, clamp, placements, getAlignmentSides, getOppositeAlignmentPlacement, getOppositePlacement, getExpandedPlacements, getOppositeAxisPlacements, sides, max, getOppositeAxis } from '@floating-ui/utils';\nexport { rectToClientRect } from '@floating-ui/utils';\nfunction computeCoordsFromPlacement(_ref, placement, rtl) {\n  var reference = _ref.reference,\n    floating = _ref.floating;\n  var sideAxis = getSideAxis(placement);\n  var alignmentAxis = getAlignmentAxis(placement);\n  var alignLength = getAxisLength(alignmentAxis);\n  var side = getSide(placement);\n  var isVertical = sideAxis === 'y';\n  var commonX = reference.x + reference.width / 2 - floating.width / 2;\n  var commonY = reference.y + reference.height / 2 - floating.height / 2;\n  var commonAlign = reference[alignLength] / 2 - floating[alignLength] / 2;\n  var coords;\n  switch (side) {\n    case 'top':\n      coords = {\n        x: commonX,\n        y: reference.y - floating.height\n      };\n      break;\n    case 'bottom':\n      coords = {\n        x: commonX,\n        y: reference.y + reference.height\n      };\n      break;\n    case 'right':\n      coords = {\n        x: reference.x + reference.width,\n        y: commonY\n      };\n      break;\n    case 'left':\n      coords = {\n        x: reference.x - floating.width,\n        y: commonY\n      };\n      break;\n    default:\n      coords = {\n        x: reference.x,\n        y: reference.y\n      };\n  }\n  switch (getAlignment(placement)) {\n    case 'start':\n      coords[alignmentAxis] -= commonAlign * (rtl && isVertical ? -1 : 1);\n      break;\n    case 'end':\n      coords[alignmentAxis] += commonAlign * (rtl && isVertical ? -1 : 1);\n      break;\n  }\n  return coords;\n}\n\n/**\n * Computes the `x` and `y` coordinates that will place the floating element\n * next to a given reference element.\n *\n * This export does not have any `platform` interface logic. You will need to\n * write one for the platform you are using Floating UI with.\n */\nvar computePosition = /*#__PURE__*/function () {\n  var _ref2 = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee(reference, floating, config) {\n    var _config$placement, placement, _config$strategy, strategy, _config$middleware, middleware, platform, validMiddleware, rtl, rects, _computeCoordsFromPla, x, y, statefulPlacement, middlewareData, resetCount, i, _validMiddleware$i, name, fn, _yield$fn, nextX, nextY, data, reset, _computeCoordsFromPla2;\n    return _regeneratorRuntime().wrap(function _callee$(_context) {\n      while (1) switch (_context.prev = _context.next) {\n        case 0:\n          _config$placement = config.placement, placement = _config$placement === void 0 ? 'bottom' : _config$placement, _config$strategy = config.strategy, strategy = _config$strategy === void 0 ? 'absolute' : _config$strategy, _config$middleware = config.middleware, middleware = _config$middleware === void 0 ? [] : _config$middleware, platform = config.platform;\n          validMiddleware = middleware.filter(Boolean);\n          _context.next = 4;\n          return platform.isRTL == null ? void 0 : platform.isRTL(floating);\n        case 4:\n          rtl = _context.sent;\n          _context.next = 7;\n          return platform.getElementRects({\n            reference,\n            floating,\n            strategy\n          });\n        case 7:\n          rects = _context.sent;\n          _computeCoordsFromPla = computeCoordsFromPlacement(rects, placement, rtl), x = _computeCoordsFromPla.x, y = _computeCoordsFromPla.y;\n          statefulPlacement = placement;\n          middlewareData = {};\n          resetCount = 0;\n          i = 0;\n        case 13:\n          if (!(i < validMiddleware.length)) {\n            _context.next = 45;\n            break;\n          }\n          _validMiddleware$i = validMiddleware[i], name = _validMiddleware$i.name, fn = _validMiddleware$i.fn;\n          _context.next = 17;\n          return fn({\n            x,\n            y,\n            initialPlacement: placement,\n            placement: statefulPlacement,\n            strategy,\n            middlewareData,\n            rects,\n            platform,\n            elements: {\n              reference,\n              floating\n            }\n          });\n        case 17:\n          _yield$fn = _context.sent;\n          nextX = _yield$fn.x;\n          nextY = _yield$fn.y;\n          data = _yield$fn.data;\n          reset = _yield$fn.reset;\n          x = nextX != null ? nextX : x;\n          y = nextY != null ? nextY : y;\n          middlewareData = _objectSpread(_objectSpread({}, middlewareData), {}, {\n            [name]: _objectSpread(_objectSpread({}, middlewareData[name]), data)\n          });\n          if (!(reset && resetCount <= 50)) {\n            _context.next = 42;\n            break;\n          }\n          resetCount++;\n          if (!(typeof reset === 'object')) {\n            _context.next = 41;\n            break;\n          }\n          if (reset.placement) {\n            statefulPlacement = reset.placement;\n          }\n          if (!reset.rects) {\n            _context.next = 38;\n            break;\n          }\n          if (!(reset.rects === true)) {\n            _context.next = 36;\n            break;\n          }\n          _context.next = 33;\n          return platform.getElementRects({\n            reference,\n            floating,\n            strategy\n          });\n        case 33:\n          _context.t0 = _context.sent;\n          _context.next = 37;\n          break;\n        case 36:\n          _context.t0 = reset.rects;\n        case 37:\n          rects = _context.t0;\n        case 38:\n          _computeCoordsFromPla2 = computeCoordsFromPlacement(rects, statefulPlacement, rtl);\n          x = _computeCoordsFromPla2.x;\n          y = _computeCoordsFromPla2.y;\n        case 41:\n          i = -1;\n        case 42:\n          i++;\n          _context.next = 13;\n          break;\n        case 45:\n          return _context.abrupt(\"return\", {\n            x,\n            y,\n            placement: statefulPlacement,\n            strategy,\n            middlewareData\n          });\n        case 46:\n        case \"end\":\n          return _context.stop();\n      }\n    }, _callee);\n  }));\n  return function computePosition(_x, _x2, _x3) {\n    return _ref2.apply(this, arguments);\n  };\n}();\n\n/**\n * Resolves with an object of overflow side offsets that determine how much the\n * element is overflowing a given clipping boundary on each side.\n * - positive = overflowing the boundary by that number of pixels\n * - negative = how many pixels left before it will overflow\n * - 0 = lies flush with the boundary\n * @see https://floating-ui.com/docs/detectOverflow\n */\nfunction detectOverflow(_x4, _x5) {\n  return _detectOverflow.apply(this, arguments);\n}\n/**\n * Provides data to position an inner element of the floating element so that it\n * appears centered to the reference element.\n * @see https://floating-ui.com/docs/arrow\n */\nfunction _detectOverflow() {\n  _detectOverflow = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee10(state, options) {\n    var _await$platform$isEle, x, y, platform, rects, elements, strategy, _evaluate8, _evaluate8$boundary, boundary, _evaluate8$rootBounda, rootBoundary, _evaluate8$elementCon, elementContext, _evaluate8$altBoundar, altBoundary, _evaluate8$padding, padding, paddingObject, altContext, element, clippingClientRect, rect, offsetParent, offsetScale, elementClientRect;\n    return _regeneratorRuntime().wrap(function _callee10$(_context10) {\n      while (1) switch (_context10.prev = _context10.next) {\n        case 0:\n          if (options === void 0) {\n            options = {};\n          }\n          x = state.x, y = state.y, platform = state.platform, rects = state.rects, elements = state.elements, strategy = state.strategy;\n          _evaluate8 = evaluate(options, state), _evaluate8$boundary = _evaluate8.boundary, boundary = _evaluate8$boundary === void 0 ? 'clippingAncestors' : _evaluate8$boundary, _evaluate8$rootBounda = _evaluate8.rootBoundary, rootBoundary = _evaluate8$rootBounda === void 0 ? 'viewport' : _evaluate8$rootBounda, _evaluate8$elementCon = _evaluate8.elementContext, elementContext = _evaluate8$elementCon === void 0 ? 'floating' : _evaluate8$elementCon, _evaluate8$altBoundar = _evaluate8.altBoundary, altBoundary = _evaluate8$altBoundar === void 0 ? false : _evaluate8$altBoundar, _evaluate8$padding = _evaluate8.padding, padding = _evaluate8$padding === void 0 ? 0 : _evaluate8$padding;\n          paddingObject = getPaddingObject(padding);\n          altContext = elementContext === 'floating' ? 'reference' : 'floating';\n          element = elements[altBoundary ? altContext : elementContext];\n          _context10.t0 = rectToClientRect;\n          _context10.t1 = platform;\n          _context10.next = 10;\n          return platform.isElement == null ? void 0 : platform.isElement(element);\n        case 10:\n          _context10.t2 = _await$platform$isEle = _context10.sent;\n          if (!(_context10.t2 != null)) {\n            _context10.next = 15;\n            break;\n          }\n          _context10.t3 = _await$platform$isEle;\n          _context10.next = 16;\n          break;\n        case 15:\n          _context10.t3 = true;\n        case 16:\n          if (!_context10.t3) {\n            _context10.next = 20;\n            break;\n          }\n          _context10.t4 = element;\n          _context10.next = 26;\n          break;\n        case 20:\n          _context10.t5 = element.contextElement;\n          if (_context10.t5) {\n            _context10.next = 25;\n            break;\n          }\n          _context10.next = 24;\n          return platform.getDocumentElement == null ? void 0 : platform.getDocumentElement(elements.floating);\n        case 24:\n          _context10.t5 = _context10.sent;\n        case 25:\n          _context10.t4 = _context10.t5;\n        case 26:\n          _context10.t6 = _context10.t4;\n          _context10.t7 = boundary;\n          _context10.t8 = rootBoundary;\n          _context10.t9 = strategy;\n          _context10.t10 = {\n            element: _context10.t6,\n            boundary: _context10.t7,\n            rootBoundary: _context10.t8,\n            strategy: _context10.t9\n          };\n          _context10.next = 33;\n          return _context10.t1.getClippingRect.call(_context10.t1, _context10.t10);\n        case 33:\n          _context10.t11 = _context10.sent;\n          clippingClientRect = (0, _context10.t0)(_context10.t11);\n          rect = elementContext === 'floating' ? {\n            x,\n            y,\n            width: rects.floating.width,\n            height: rects.floating.height\n          } : rects.reference;\n          _context10.next = 38;\n          return platform.getOffsetParent == null ? void 0 : platform.getOffsetParent(elements.floating);\n        case 38:\n          offsetParent = _context10.sent;\n          _context10.next = 41;\n          return platform.isElement == null ? void 0 : platform.isElement(offsetParent);\n        case 41:\n          if (!_context10.sent) {\n            _context10.next = 50;\n            break;\n          }\n          _context10.next = 44;\n          return platform.getScale == null ? void 0 : platform.getScale(offsetParent);\n        case 44:\n          _context10.t13 = _context10.sent;\n          if (_context10.t13) {\n            _context10.next = 47;\n            break;\n          }\n          _context10.t13 = {\n            x: 1,\n            y: 1\n          };\n        case 47:\n          _context10.t12 = _context10.t13;\n          _context10.next = 51;\n          break;\n        case 50:\n          _context10.t12 = {\n            x: 1,\n            y: 1\n          };\n        case 51:\n          offsetScale = _context10.t12;\n          _context10.t14 = rectToClientRect;\n          if (!platform.convertOffsetParentRelativeRectToViewportRelativeRect) {\n            _context10.next = 59;\n            break;\n          }\n          _context10.next = 56;\n          return platform.convertOffsetParentRelativeRectToViewportRelativeRect({\n            elements,\n            rect,\n            offsetParent,\n            strategy\n          });\n        case 56:\n          _context10.t15 = _context10.sent;\n          _context10.next = 60;\n          break;\n        case 59:\n          _context10.t15 = rect;\n        case 60:\n          _context10.t16 = _context10.t15;\n          elementClientRect = (0, _context10.t14)(_context10.t16);\n          return _context10.abrupt(\"return\", {\n            top: (clippingClientRect.top - elementClientRect.top + paddingObject.top) / offsetScale.y,\n            bottom: (elementClientRect.bottom - clippingClientRect.bottom + paddingObject.bottom) / offsetScale.y,\n            left: (clippingClientRect.left - elementClientRect.left + paddingObject.left) / offsetScale.x,\n            right: (elementClientRect.right - clippingClientRect.right + paddingObject.right) / offsetScale.x\n          });\n        case 63:\n        case \"end\":\n          return _context10.stop();\n      }\n    }, _callee10);\n  }));\n  return _detectOverflow.apply(this, arguments);\n}\nvar arrow = function arrow(options) {\n  return {\n    name: 'arrow',\n    options,\n    fn(state) {\n      return _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee2() {\n        var x, y, placement, rects, platform, elements, middlewareData, _ref3, element, _ref3$padding, padding, paddingObject, coords, axis, length, arrowDimensions, isYAxis, minProp, maxProp, clientProp, endDiff, startDiff, arrowOffsetParent, clientSize, centerToReference, largestPossiblePadding, minPadding, maxPadding, min$1, max, center, offset, shouldAddOffset, alignmentOffset;\n        return _regeneratorRuntime().wrap(function _callee2$(_context2) {\n          while (1) switch (_context2.prev = _context2.next) {\n            case 0:\n              x = state.x, y = state.y, placement = state.placement, rects = state.rects, platform = state.platform, elements = state.elements, middlewareData = state.middlewareData; // Since `element` is required, we don't Partial<> the type.\n              _ref3 = evaluate(options, state) || {}, element = _ref3.element, _ref3$padding = _ref3.padding, padding = _ref3$padding === void 0 ? 0 : _ref3$padding;\n              if (!(element == null)) {\n                _context2.next = 4;\n                break;\n              }\n              return _context2.abrupt(\"return\", {});\n            case 4:\n              paddingObject = getPaddingObject(padding);\n              coords = {\n                x,\n                y\n              };\n              axis = getAlignmentAxis(placement);\n              length = getAxisLength(axis);\n              _context2.next = 10;\n              return platform.getDimensions(element);\n            case 10:\n              arrowDimensions = _context2.sent;\n              isYAxis = axis === 'y';\n              minProp = isYAxis ? 'top' : 'left';\n              maxProp = isYAxis ? 'bottom' : 'right';\n              clientProp = isYAxis ? 'clientHeight' : 'clientWidth';\n              endDiff = rects.reference[length] + rects.reference[axis] - coords[axis] - rects.floating[length];\n              startDiff = coords[axis] - rects.reference[axis];\n              _context2.next = 19;\n              return platform.getOffsetParent == null ? void 0 : platform.getOffsetParent(element);\n            case 19:\n              arrowOffsetParent = _context2.sent;\n              clientSize = arrowOffsetParent ? arrowOffsetParent[clientProp] : 0; // DOM platform can return `window` as the `offsetParent`.\n              _context2.t0 = !clientSize;\n              if (_context2.t0) {\n                _context2.next = 26;\n                break;\n              }\n              _context2.next = 25;\n              return platform.isElement == null ? void 0 : platform.isElement(arrowOffsetParent);\n            case 25:\n              _context2.t0 = !_context2.sent;\n            case 26:\n              if (!_context2.t0) {\n                _context2.next = 28;\n                break;\n              }\n              clientSize = elements.floating[clientProp] || rects.floating[length];\n            case 28:\n              centerToReference = endDiff / 2 - startDiff / 2; // If the padding is large enough that it causes the arrow to no longer be\n              // centered, modify the padding so that it is centered.\n              largestPossiblePadding = clientSize / 2 - arrowDimensions[length] / 2 - 1;\n              minPadding = min(paddingObject[minProp], largestPossiblePadding);\n              maxPadding = min(paddingObject[maxProp], largestPossiblePadding); // Make sure the arrow doesn't overflow the floating element if the center\n              // point is outside the floating element's bounds.\n              min$1 = minPadding;\n              max = clientSize - arrowDimensions[length] - maxPadding;\n              center = clientSize / 2 - arrowDimensions[length] / 2 + centerToReference;\n              offset = clamp(min$1, center, max); // If the reference is small enough that the arrow's padding causes it to\n              // to point to nothing for an aligned placement, adjust the offset of the\n              // floating element itself. To ensure `shift()` continues to take action,\n              // a single reset is performed when this is true.\n              shouldAddOffset = !middlewareData.arrow && getAlignment(placement) != null && center !== offset && rects.reference[length] / 2 - (center < min$1 ? minPadding : maxPadding) - arrowDimensions[length] / 2 < 0;\n              alignmentOffset = shouldAddOffset ? center < min$1 ? center - min$1 : center - max : 0;\n              return _context2.abrupt(\"return\", {\n                [axis]: coords[axis] + alignmentOffset,\n                data: _objectSpread({\n                  [axis]: offset,\n                  centerOffset: center - offset - alignmentOffset\n                }, shouldAddOffset && {\n                  alignmentOffset\n                }),\n                reset: shouldAddOffset\n              });\n            case 39:\n            case \"end\":\n              return _context2.stop();\n          }\n        }, _callee2);\n      }))();\n    }\n  };\n};\nfunction getPlacementList(alignment, autoAlignment, allowedPlacements) {\n  var allowedPlacementsSortedByAlignment = alignment ? [].concat(_toConsumableArray(allowedPlacements.filter(function (placement) {\n    return getAlignment(placement) === alignment;\n  })), _toConsumableArray(allowedPlacements.filter(function (placement) {\n    return getAlignment(placement) !== alignment;\n  }))) : allowedPlacements.filter(function (placement) {\n    return getSide(placement) === placement;\n  });\n  return allowedPlacementsSortedByAlignment.filter(function (placement) {\n    if (alignment) {\n      return getAlignment(placement) === alignment || (autoAlignment ? getOppositeAlignmentPlacement(placement) !== placement : false);\n    }\n    return true;\n  });\n}\n/**\n * Optimizes the visibility of the floating element by choosing the placement\n * that has the most space available automatically, without needing to specify a\n * preferred placement. Alternative to `flip`.\n * @see https://floating-ui.com/docs/autoPlacement\n */\nvar autoPlacement = function autoPlacement(options) {\n  if (options === void 0) {\n    options = {};\n  }\n  return {\n    name: 'autoPlacement',\n    options,\n    fn(state) {\n      return _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee3() {\n        var _middlewareData$autoP, _middlewareData$autoP2, _placementsThatFitOnE, rects, middlewareData, placement, platform, elements, _evaluate, _evaluate$crossAxis, crossAxis, alignment, _evaluate$allowedPlac, allowedPlacements, _evaluate$autoAlignme, autoAlignment, detectOverflowOptions, placements$1, overflow, currentIndex, currentPlacement, alignmentSides, currentOverflows, allOverflows, nextPlacement, placementsSortedByMostSpace, placementsThatFitOnEachSide, resetPlacement;\n        return _regeneratorRuntime().wrap(function _callee3$(_context3) {\n          while (1) switch (_context3.prev = _context3.next) {\n            case 0:\n              rects = state.rects, middlewareData = state.middlewareData, placement = state.placement, platform = state.platform, elements = state.elements;\n              _evaluate = evaluate(options, state), _evaluate$crossAxis = _evaluate.crossAxis, crossAxis = _evaluate$crossAxis === void 0 ? false : _evaluate$crossAxis, alignment = _evaluate.alignment, _evaluate$allowedPlac = _evaluate.allowedPlacements, allowedPlacements = _evaluate$allowedPlac === void 0 ? placements : _evaluate$allowedPlac, _evaluate$autoAlignme = _evaluate.autoAlignment, autoAlignment = _evaluate$autoAlignme === void 0 ? true : _evaluate$autoAlignme, detectOverflowOptions = _objectWithoutProperties(_evaluate, _excluded);\n              placements$1 = alignment !== undefined || allowedPlacements === placements ? getPlacementList(alignment || null, autoAlignment, allowedPlacements) : allowedPlacements;\n              _context3.next = 5;\n              return detectOverflow(state, detectOverflowOptions);\n            case 5:\n              overflow = _context3.sent;\n              currentIndex = ((_middlewareData$autoP = middlewareData.autoPlacement) == null ? void 0 : _middlewareData$autoP.index) || 0;\n              currentPlacement = placements$1[currentIndex];\n              if (!(currentPlacement == null)) {\n                _context3.next = 10;\n                break;\n              }\n              return _context3.abrupt(\"return\", {});\n            case 10:\n              _context3.t0 = getAlignmentSides;\n              _context3.t1 = currentPlacement;\n              _context3.t2 = rects;\n              _context3.next = 15;\n              return platform.isRTL == null ? void 0 : platform.isRTL(elements.floating);\n            case 15:\n              _context3.t3 = _context3.sent;\n              alignmentSides = (0, _context3.t0)(_context3.t1, _context3.t2, _context3.t3);\n              if (!(placement !== currentPlacement)) {\n                _context3.next = 19;\n                break;\n              }\n              return _context3.abrupt(\"return\", {\n                reset: {\n                  placement: placements$1[0]\n                }\n              });\n            case 19:\n              currentOverflows = [overflow[getSide(currentPlacement)], overflow[alignmentSides[0]], overflow[alignmentSides[1]]];\n              allOverflows = [].concat(_toConsumableArray(((_middlewareData$autoP2 = middlewareData.autoPlacement) == null ? void 0 : _middlewareData$autoP2.overflows) || []), [{\n                placement: currentPlacement,\n                overflows: currentOverflows\n              }]);\n              nextPlacement = placements$1[currentIndex + 1]; // There are more placements to check.\n              if (!nextPlacement) {\n                _context3.next = 24;\n                break;\n              }\n              return _context3.abrupt(\"return\", {\n                data: {\n                  index: currentIndex + 1,\n                  overflows: allOverflows\n                },\n                reset: {\n                  placement: nextPlacement\n                }\n              });\n            case 24:\n              placementsSortedByMostSpace = allOverflows.map(function (d) {\n                var alignment = getAlignment(d.placement);\n                return [d.placement, alignment && crossAxis ?\n                // Check along the mainAxis and main crossAxis side.\n                d.overflows.slice(0, 2).reduce(function (acc, v) {\n                  return acc + v;\n                }, 0) :\n                // Check only the mainAxis.\n                d.overflows[0], d.overflows];\n              }).sort(function (a, b) {\n                return a[1] - b[1];\n              });\n              placementsThatFitOnEachSide = placementsSortedByMostSpace.filter(function (d) {\n                return d[2].slice(0,\n                // Aligned placements should not check their opposite crossAxis\n                // side.\n                getAlignment(d[0]) ? 2 : 3).every(function (v) {\n                  return v <= 0;\n                });\n              });\n              resetPlacement = ((_placementsThatFitOnE = placementsThatFitOnEachSide[0]) == null ? void 0 : _placementsThatFitOnE[0]) || placementsSortedByMostSpace[0][0];\n              if (!(resetPlacement !== placement)) {\n                _context3.next = 29;\n                break;\n              }\n              return _context3.abrupt(\"return\", {\n                data: {\n                  index: currentIndex + 1,\n                  overflows: allOverflows\n                },\n                reset: {\n                  placement: resetPlacement\n                }\n              });\n            case 29:\n              return _context3.abrupt(\"return\", {});\n            case 30:\n            case \"end\":\n              return _context3.stop();\n          }\n        }, _callee3);\n      }))();\n    }\n  };\n};\n\n/**\n * Optimizes the visibility of the floating element by flipping the `placement`\n * in order to keep it in view when the preferred placement(s) will overflow the\n * clipping boundary. Alternative to `autoPlacement`.\n * @see https://floating-ui.com/docs/flip\n */\nvar flip = function flip(options) {\n  if (options === void 0) {\n    options = {};\n  }\n  return {\n    name: 'flip',\n    options,\n    fn(state) {\n      return _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee4() {\n        var _middlewareData$arrow, _middlewareData$flip, placement, middlewareData, rects, initialPlacement, platform, elements, _evaluate2, _evaluate2$mainAxis, checkMainAxis, _evaluate2$crossAxis, checkCrossAxis, specifiedFallbackPlacements, _evaluate2$fallbackSt, fallbackStrategy, _evaluate2$fallbackAx, fallbackAxisSideDirection, _evaluate2$flipAlignm, flipAlignment, detectOverflowOptions, side, initialSideAxis, isBasePlacement, rtl, fallbackPlacements, hasFallbackAxisSideDirection, placements, overflow, overflows, overflowsData, _sides, _middlewareData$flip2, _overflowsData$filter, nextIndex, nextPlacement, resetPlacement, _overflowsData$filter2, _placement;\n        return _regeneratorRuntime().wrap(function _callee4$(_context4) {\n          while (1) switch (_context4.prev = _context4.next) {\n            case 0:\n              placement = state.placement, middlewareData = state.middlewareData, rects = state.rects, initialPlacement = state.initialPlacement, platform = state.platform, elements = state.elements;\n              _evaluate2 = evaluate(options, state), _evaluate2$mainAxis = _evaluate2.mainAxis, checkMainAxis = _evaluate2$mainAxis === void 0 ? true : _evaluate2$mainAxis, _evaluate2$crossAxis = _evaluate2.crossAxis, checkCrossAxis = _evaluate2$crossAxis === void 0 ? true : _evaluate2$crossAxis, specifiedFallbackPlacements = _evaluate2.fallbackPlacements, _evaluate2$fallbackSt = _evaluate2.fallbackStrategy, fallbackStrategy = _evaluate2$fallbackSt === void 0 ? 'bestFit' : _evaluate2$fallbackSt, _evaluate2$fallbackAx = _evaluate2.fallbackAxisSideDirection, fallbackAxisSideDirection = _evaluate2$fallbackAx === void 0 ? 'none' : _evaluate2$fallbackAx, _evaluate2$flipAlignm = _evaluate2.flipAlignment, flipAlignment = _evaluate2$flipAlignm === void 0 ? true : _evaluate2$flipAlignm, detectOverflowOptions = _objectWithoutProperties(_evaluate2, _excluded2); // If a reset by the arrow was caused due to an alignment offset being\n              // added, we should skip any logic now since `flip()` has already done its\n              // work.\n              // https://github.com/floating-ui/floating-ui/issues/2549#issuecomment-1719601643\n              if (!((_middlewareData$arrow = middlewareData.arrow) != null && _middlewareData$arrow.alignmentOffset)) {\n                _context4.next = 4;\n                break;\n              }\n              return _context4.abrupt(\"return\", {});\n            case 4:\n              side = getSide(placement);\n              initialSideAxis = getSideAxis(initialPlacement);\n              isBasePlacement = getSide(initialPlacement) === initialPlacement;\n              _context4.next = 9;\n              return platform.isRTL == null ? void 0 : platform.isRTL(elements.floating);\n            case 9:\n              rtl = _context4.sent;\n              fallbackPlacements = specifiedFallbackPlacements || (isBasePlacement || !flipAlignment ? [getOppositePlacement(initialPlacement)] : getExpandedPlacements(initialPlacement));\n              hasFallbackAxisSideDirection = fallbackAxisSideDirection !== 'none';\n              if (!specifiedFallbackPlacements && hasFallbackAxisSideDirection) {\n                fallbackPlacements.push.apply(fallbackPlacements, _toConsumableArray(getOppositeAxisPlacements(initialPlacement, flipAlignment, fallbackAxisSideDirection, rtl)));\n              }\n              placements = [initialPlacement].concat(_toConsumableArray(fallbackPlacements));\n              _context4.next = 16;\n              return detectOverflow(state, detectOverflowOptions);\n            case 16:\n              overflow = _context4.sent;\n              overflows = [];\n              overflowsData = ((_middlewareData$flip = middlewareData.flip) == null ? void 0 : _middlewareData$flip.overflows) || [];\n              if (checkMainAxis) {\n                overflows.push(overflow[side]);\n              }\n              if (checkCrossAxis) {\n                _sides = getAlignmentSides(placement, rects, rtl);\n                overflows.push(overflow[_sides[0]], overflow[_sides[1]]);\n              }\n              overflowsData = [].concat(_toConsumableArray(overflowsData), [{\n                placement,\n                overflows\n              }]);\n\n              // One or more sides is overflowing.\n              if (overflows.every(function (side) {\n                return side <= 0;\n              })) {\n                _context4.next = 39;\n                break;\n              }\n              nextIndex = (((_middlewareData$flip2 = middlewareData.flip) == null ? void 0 : _middlewareData$flip2.index) || 0) + 1;\n              nextPlacement = placements[nextIndex];\n              if (!nextPlacement) {\n                _context4.next = 27;\n                break;\n              }\n              return _context4.abrupt(\"return\", {\n                data: {\n                  index: nextIndex,\n                  overflows: overflowsData\n                },\n                reset: {\n                  placement: nextPlacement\n                }\n              });\n            case 27:\n              // First, find the candidates that fit on the mainAxis side of overflow,\n              // then find the placement that fits the best on the main crossAxis side.\n              resetPlacement = (_overflowsData$filter = overflowsData.filter(function (d) {\n                return d.overflows[0] <= 0;\n              }).sort(function (a, b) {\n                return a.overflows[1] - b.overflows[1];\n              })[0]) == null ? void 0 : _overflowsData$filter.placement; // Otherwise fallback.\n              if (resetPlacement) {\n                _context4.next = 37;\n                break;\n              }\n              _context4.t0 = fallbackStrategy;\n              _context4.next = _context4.t0 === 'bestFit' ? 32 : _context4.t0 === 'initialPlacement' ? 35 : 37;\n              break;\n            case 32:\n              _placement = (_overflowsData$filter2 = overflowsData.filter(function (d) {\n                if (hasFallbackAxisSideDirection) {\n                  var currentSideAxis = getSideAxis(d.placement);\n                  return currentSideAxis === initialSideAxis ||\n                  // Create a bias to the `y` side axis due to horizontal\n                  // reading directions favoring greater width.\n                  currentSideAxis === 'y';\n                }\n                return true;\n              }).map(function (d) {\n                return [d.placement, d.overflows.filter(function (overflow) {\n                  return overflow > 0;\n                }).reduce(function (acc, overflow) {\n                  return acc + overflow;\n                }, 0)];\n              }).sort(function (a, b) {\n                return a[1] - b[1];\n              })[0]) == null ? void 0 : _overflowsData$filter2[0];\n              if (_placement) {\n                resetPlacement = _placement;\n              }\n              return _context4.abrupt(\"break\", 37);\n            case 35:\n              resetPlacement = initialPlacement;\n              return _context4.abrupt(\"break\", 37);\n            case 37:\n              if (!(placement !== resetPlacement)) {\n                _context4.next = 39;\n                break;\n              }\n              return _context4.abrupt(\"return\", {\n                reset: {\n                  placement: resetPlacement\n                }\n              });\n            case 39:\n              return _context4.abrupt(\"return\", {});\n            case 40:\n            case \"end\":\n              return _context4.stop();\n          }\n        }, _callee4);\n      }))();\n    }\n  };\n};\nfunction getSideOffsets(overflow, rect) {\n  return {\n    top: overflow.top - rect.height,\n    right: overflow.right - rect.width,\n    bottom: overflow.bottom - rect.height,\n    left: overflow.left - rect.width\n  };\n}\nfunction isAnySideFullyClipped(overflow) {\n  return sides.some(function (side) {\n    return overflow[side] >= 0;\n  });\n}\n/**\n * Provides data to hide the floating element in applicable situations, such as\n * when it is not in the same clipping context as the reference element.\n * @see https://floating-ui.com/docs/hide\n */\nvar hide = function hide(options) {\n  if (options === void 0) {\n    options = {};\n  }\n  return {\n    name: 'hide',\n    options,\n    fn(state) {\n      return _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee5() {\n        var rects, _evaluate3, _evaluate3$strategy, strategy, detectOverflowOptions, overflow, offsets, _overflow, _offsets;\n        return _regeneratorRuntime().wrap(function _callee5$(_context5) {\n          while (1) switch (_context5.prev = _context5.next) {\n            case 0:\n              rects = state.rects;\n              _evaluate3 = evaluate(options, state), _evaluate3$strategy = _evaluate3.strategy, strategy = _evaluate3$strategy === void 0 ? 'referenceHidden' : _evaluate3$strategy, detectOverflowOptions = _objectWithoutProperties(_evaluate3, _excluded3);\n              _context5.t0 = strategy;\n              _context5.next = _context5.t0 === 'referenceHidden' ? 5 : _context5.t0 === 'escaped' ? 10 : 15;\n              break;\n            case 5:\n              _context5.next = 7;\n              return detectOverflow(state, _objectSpread(_objectSpread({}, detectOverflowOptions), {}, {\n                elementContext: 'reference'\n              }));\n            case 7:\n              overflow = _context5.sent;\n              offsets = getSideOffsets(overflow, rects.reference);\n              return _context5.abrupt(\"return\", {\n                data: {\n                  referenceHiddenOffsets: offsets,\n                  referenceHidden: isAnySideFullyClipped(offsets)\n                }\n              });\n            case 10:\n              _context5.next = 12;\n              return detectOverflow(state, _objectSpread(_objectSpread({}, detectOverflowOptions), {}, {\n                altBoundary: true\n              }));\n            case 12:\n              _overflow = _context5.sent;\n              _offsets = getSideOffsets(_overflow, rects.floating);\n              return _context5.abrupt(\"return\", {\n                data: {\n                  escapedOffsets: _offsets,\n                  escaped: isAnySideFullyClipped(_offsets)\n                }\n              });\n            case 15:\n              return _context5.abrupt(\"return\", {});\n            case 16:\n            case \"end\":\n              return _context5.stop();\n          }\n        }, _callee5);\n      }))();\n    }\n  };\n};\nfunction getBoundingRect(rects) {\n  var minX = min.apply(void 0, _toConsumableArray(rects.map(function (rect) {\n    return rect.left;\n  })));\n  var minY = min.apply(void 0, _toConsumableArray(rects.map(function (rect) {\n    return rect.top;\n  })));\n  var maxX = max.apply(void 0, _toConsumableArray(rects.map(function (rect) {\n    return rect.right;\n  })));\n  var maxY = max.apply(void 0, _toConsumableArray(rects.map(function (rect) {\n    return rect.bottom;\n  })));\n  return {\n    x: minX,\n    y: minY,\n    width: maxX - minX,\n    height: maxY - minY\n  };\n}\nfunction getRectsByLine(rects) {\n  var sortedRects = rects.slice().sort(function (a, b) {\n    return a.y - b.y;\n  });\n  var groups = [];\n  var prevRect = null;\n  for (var i = 0; i < sortedRects.length; i++) {\n    var rect = sortedRects[i];\n    if (!prevRect || rect.y - prevRect.y > prevRect.height / 2) {\n      groups.push([rect]);\n    } else {\n      groups[groups.length - 1].push(rect);\n    }\n    prevRect = rect;\n  }\n  return groups.map(function (rect) {\n    return rectToClientRect(getBoundingRect(rect));\n  });\n}\n/**\n * Provides improved positioning for inline reference elements that can span\n * over multiple lines, such as hyperlinks or range selections.\n * @see https://floating-ui.com/docs/inline\n */\nvar inline = function inline(options) {\n  if (options === void 0) {\n    options = {};\n  }\n  return {\n    name: 'inline',\n    options,\n    fn(state) {\n      return _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee6() {\n        var placement, elements, rects, platform, strategy, _evaluate4, _evaluate4$padding, padding, x, y, nativeClientRects, clientRects, fallback, paddingObject, getBoundingClientRect, resetRects;\n        return _regeneratorRuntime().wrap(function _callee6$(_context6) {\n          while (1) switch (_context6.prev = _context6.next) {\n            case 0:\n              getBoundingClientRect = function _getBoundingClientRec() {\n                // There are two rects and they are disjoined.\n                if (clientRects.length === 2 && clientRects[0].left > clientRects[1].right && x != null && y != null) {\n                  // Find the first rect in which the point is fully inside.\n                  return clientRects.find(function (rect) {\n                    return x > rect.left - paddingObject.left && x < rect.right + paddingObject.right && y > rect.top - paddingObject.top && y < rect.bottom + paddingObject.bottom;\n                  }) || fallback;\n                }\n\n                // There are 2 or more connected rects.\n                if (clientRects.length >= 2) {\n                  if (getSideAxis(placement) === 'y') {\n                    var firstRect = clientRects[0];\n                    var lastRect = clientRects[clientRects.length - 1];\n                    var isTop = getSide(placement) === 'top';\n                    var _top = firstRect.top;\n                    var _bottom = lastRect.bottom;\n                    var _left = isTop ? firstRect.left : lastRect.left;\n                    var _right = isTop ? firstRect.right : lastRect.right;\n                    var _width = _right - _left;\n                    var _height = _bottom - _top;\n                    return {\n                      top: _top,\n                      bottom: _bottom,\n                      left: _left,\n                      right: _right,\n                      width: _width,\n                      height: _height,\n                      x: _left,\n                      y: _top\n                    };\n                  }\n                  var isLeftSide = getSide(placement) === 'left';\n                  var maxRight = max.apply(void 0, _toConsumableArray(clientRects.map(function (rect) {\n                    return rect.right;\n                  })));\n                  var minLeft = min.apply(void 0, _toConsumableArray(clientRects.map(function (rect) {\n                    return rect.left;\n                  })));\n                  var measureRects = clientRects.filter(function (rect) {\n                    return isLeftSide ? rect.left === minLeft : rect.right === maxRight;\n                  });\n                  var top = measureRects[0].top;\n                  var bottom = measureRects[measureRects.length - 1].bottom;\n                  var left = minLeft;\n                  var right = maxRight;\n                  var width = right - left;\n                  var height = bottom - top;\n                  return {\n                    top,\n                    bottom,\n                    left,\n                    right,\n                    width,\n                    height,\n                    x: left,\n                    y: top\n                  };\n                }\n                return fallback;\n              };\n              placement = state.placement, elements = state.elements, rects = state.rects, platform = state.platform, strategy = state.strategy; // A MouseEvent's client{X,Y} coords can be up to 2 pixels off a\n              // ClientRect's bounds, despite the event listener being triggered. A\n              // padding of 2 seems to handle this issue.\n              _evaluate4 = evaluate(options, state), _evaluate4$padding = _evaluate4.padding, padding = _evaluate4$padding === void 0 ? 2 : _evaluate4$padding, x = _evaluate4.x, y = _evaluate4.y;\n              _context6.t0 = Array;\n              _context6.next = 6;\n              return platform.getClientRects == null ? void 0 : platform.getClientRects(elements.reference);\n            case 6:\n              _context6.t1 = _context6.sent;\n              if (_context6.t1) {\n                _context6.next = 9;\n                break;\n              }\n              _context6.t1 = [];\n            case 9:\n              _context6.t2 = _context6.t1;\n              nativeClientRects = _context6.t0.from.call(_context6.t0, _context6.t2);\n              clientRects = getRectsByLine(nativeClientRects);\n              fallback = rectToClientRect(getBoundingRect(nativeClientRects));\n              paddingObject = getPaddingObject(padding);\n              _context6.next = 16;\n              return platform.getElementRects({\n                reference: {\n                  getBoundingClientRect\n                },\n                floating: elements.floating,\n                strategy\n              });\n            case 16:\n              resetRects = _context6.sent;\n              if (!(rects.reference.x !== resetRects.reference.x || rects.reference.y !== resetRects.reference.y || rects.reference.width !== resetRects.reference.width || rects.reference.height !== resetRects.reference.height)) {\n                _context6.next = 19;\n                break;\n              }\n              return _context6.abrupt(\"return\", {\n                reset: {\n                  rects: resetRects\n                }\n              });\n            case 19:\n              return _context6.abrupt(\"return\", {});\n            case 20:\n            case \"end\":\n              return _context6.stop();\n          }\n        }, _callee6);\n      }))();\n    }\n  };\n};\n\n// For type backwards-compatibility, the `OffsetOptions` type was also\n// Derivable.\nfunction convertValueToCoords(_x6, _x7) {\n  return _convertValueToCoords.apply(this, arguments);\n}\n/**\n * Modifies the placement by translating the floating element along the\n * specified axes.\n * A number (shorthand for `mainAxis` or distance), or an axes configuration\n * object may be passed.\n * @see https://floating-ui.com/docs/offset\n */\nfunction _convertValueToCoords() {\n  _convertValueToCoords = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee11(state, options) {\n    var placement, platform, elements, rtl, side, alignment, isVertical, mainAxisMulti, crossAxisMulti, rawValue, _ref4, mainAxis, crossAxis, alignmentAxis;\n    return _regeneratorRuntime().wrap(function _callee11$(_context11) {\n      while (1) switch (_context11.prev = _context11.next) {\n        case 0:\n          placement = state.placement, platform = state.platform, elements = state.elements;\n          _context11.next = 3;\n          return platform.isRTL == null ? void 0 : platform.isRTL(elements.floating);\n        case 3:\n          rtl = _context11.sent;\n          side = getSide(placement);\n          alignment = getAlignment(placement);\n          isVertical = getSideAxis(placement) === 'y';\n          mainAxisMulti = ['left', 'top'].includes(side) ? -1 : 1;\n          crossAxisMulti = rtl && isVertical ? -1 : 1;\n          rawValue = evaluate(options, state); // eslint-disable-next-line prefer-const\n          _ref4 = typeof rawValue === 'number' ? {\n            mainAxis: rawValue,\n            crossAxis: 0,\n            alignmentAxis: null\n          } : {\n            mainAxis: rawValue.mainAxis || 0,\n            crossAxis: rawValue.crossAxis || 0,\n            alignmentAxis: rawValue.alignmentAxis\n          }, mainAxis = _ref4.mainAxis, crossAxis = _ref4.crossAxis, alignmentAxis = _ref4.alignmentAxis;\n          if (alignment && typeof alignmentAxis === 'number') {\n            crossAxis = alignment === 'end' ? alignmentAxis * -1 : alignmentAxis;\n          }\n          return _context11.abrupt(\"return\", isVertical ? {\n            x: crossAxis * crossAxisMulti,\n            y: mainAxis * mainAxisMulti\n          } : {\n            x: mainAxis * mainAxisMulti,\n            y: crossAxis * crossAxisMulti\n          });\n        case 13:\n        case \"end\":\n          return _context11.stop();\n      }\n    }, _callee11);\n  }));\n  return _convertValueToCoords.apply(this, arguments);\n}\nvar offset = function offset(options) {\n  if (options === void 0) {\n    options = 0;\n  }\n  return {\n    name: 'offset',\n    options,\n    fn(state) {\n      return _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee7() {\n        var _middlewareData$offse, _middlewareData$arrow, x, y, placement, middlewareData, diffCoords;\n        return _regeneratorRuntime().wrap(function _callee7$(_context7) {\n          while (1) switch (_context7.prev = _context7.next) {\n            case 0:\n              x = state.x, y = state.y, placement = state.placement, middlewareData = state.middlewareData;\n              _context7.next = 3;\n              return convertValueToCoords(state, options);\n            case 3:\n              diffCoords = _context7.sent;\n              if (!(placement === ((_middlewareData$offse = middlewareData.offset) == null ? void 0 : _middlewareData$offse.placement) && (_middlewareData$arrow = middlewareData.arrow) != null && _middlewareData$arrow.alignmentOffset)) {\n                _context7.next = 6;\n                break;\n              }\n              return _context7.abrupt(\"return\", {});\n            case 6:\n              return _context7.abrupt(\"return\", {\n                x: x + diffCoords.x,\n                y: y + diffCoords.y,\n                data: _objectSpread(_objectSpread({}, diffCoords), {}, {\n                  placement\n                })\n              });\n            case 7:\n            case \"end\":\n              return _context7.stop();\n          }\n        }, _callee7);\n      }))();\n    }\n  };\n};\n\n/**\n * Optimizes the visibility of the floating element by shifting it in order to\n * keep it in view when it will overflow the clipping boundary.\n * @see https://floating-ui.com/docs/shift\n */\nvar shift = function shift(options) {\n  if (options === void 0) {\n    options = {};\n  }\n  return {\n    name: 'shift',\n    options,\n    fn(state) {\n      return _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee8() {\n        var x, y, placement, _evaluate5, _evaluate5$mainAxis, checkMainAxis, _evaluate5$crossAxis, checkCrossAxis, _evaluate5$limiter, limiter, detectOverflowOptions, coords, overflow, crossAxis, mainAxis, mainAxisCoord, crossAxisCoord, minSide, maxSide, _min, _max, _minSide, _maxSide, _min2, _max2, limitedCoords;\n        return _regeneratorRuntime().wrap(function _callee8$(_context8) {\n          while (1) switch (_context8.prev = _context8.next) {\n            case 0:\n              x = state.x, y = state.y, placement = state.placement;\n              _evaluate5 = evaluate(options, state), _evaluate5$mainAxis = _evaluate5.mainAxis, checkMainAxis = _evaluate5$mainAxis === void 0 ? true : _evaluate5$mainAxis, _evaluate5$crossAxis = _evaluate5.crossAxis, checkCrossAxis = _evaluate5$crossAxis === void 0 ? false : _evaluate5$crossAxis, _evaluate5$limiter = _evaluate5.limiter, limiter = _evaluate5$limiter === void 0 ? {\n                fn: function fn(_ref) {\n                  var x = _ref.x,\n                    y = _ref.y;\n                  return {\n                    x,\n                    y\n                  };\n                }\n              } : _evaluate5$limiter, detectOverflowOptions = _objectWithoutProperties(_evaluate5, _excluded4);\n              coords = {\n                x,\n                y\n              };\n              _context8.next = 5;\n              return detectOverflow(state, detectOverflowOptions);\n            case 5:\n              overflow = _context8.sent;\n              crossAxis = getSideAxis(getSide(placement));\n              mainAxis = getOppositeAxis(crossAxis);\n              mainAxisCoord = coords[mainAxis];\n              crossAxisCoord = coords[crossAxis];\n              if (checkMainAxis) {\n                minSide = mainAxis === 'y' ? 'top' : 'left';\n                maxSide = mainAxis === 'y' ? 'bottom' : 'right';\n                _min = mainAxisCoord + overflow[minSide];\n                _max = mainAxisCoord - overflow[maxSide];\n                mainAxisCoord = clamp(_min, mainAxisCoord, _max);\n              }\n              if (checkCrossAxis) {\n                _minSide = crossAxis === 'y' ? 'top' : 'left';\n                _maxSide = crossAxis === 'y' ? 'bottom' : 'right';\n                _min2 = crossAxisCoord + overflow[_minSide];\n                _max2 = crossAxisCoord - overflow[_maxSide];\n                crossAxisCoord = clamp(_min2, crossAxisCoord, _max2);\n              }\n              limitedCoords = limiter.fn(_objectSpread(_objectSpread({}, state), {}, {\n                [mainAxis]: mainAxisCoord,\n                [crossAxis]: crossAxisCoord\n              }));\n              return _context8.abrupt(\"return\", _objectSpread(_objectSpread({}, limitedCoords), {}, {\n                data: {\n                  x: limitedCoords.x - x,\n                  y: limitedCoords.y - y,\n                  enabled: {\n                    [mainAxis]: checkMainAxis,\n                    [crossAxis]: checkCrossAxis\n                  }\n                }\n              }));\n            case 14:\n            case \"end\":\n              return _context8.stop();\n          }\n        }, _callee8);\n      }))();\n    }\n  };\n};\n/**\n * Built-in `limiter` that will stop `shift()` at a certain point.\n */\nvar limitShift = function limitShift(options) {\n  if (options === void 0) {\n    options = {};\n  }\n  return {\n    options,\n    fn(state) {\n      var x = state.x,\n        y = state.y,\n        placement = state.placement,\n        rects = state.rects,\n        middlewareData = state.middlewareData;\n      var _evaluate6 = evaluate(options, state),\n        _evaluate6$offset = _evaluate6.offset,\n        offset = _evaluate6$offset === void 0 ? 0 : _evaluate6$offset,\n        _evaluate6$mainAxis = _evaluate6.mainAxis,\n        checkMainAxis = _evaluate6$mainAxis === void 0 ? true : _evaluate6$mainAxis,\n        _evaluate6$crossAxis = _evaluate6.crossAxis,\n        checkCrossAxis = _evaluate6$crossAxis === void 0 ? true : _evaluate6$crossAxis;\n      var coords = {\n        x,\n        y\n      };\n      var crossAxis = getSideAxis(placement);\n      var mainAxis = getOppositeAxis(crossAxis);\n      var mainAxisCoord = coords[mainAxis];\n      var crossAxisCoord = coords[crossAxis];\n      var rawOffset = evaluate(offset, state);\n      var computedOffset = typeof rawOffset === 'number' ? {\n        mainAxis: rawOffset,\n        crossAxis: 0\n      } : _objectSpread({\n        mainAxis: 0,\n        crossAxis: 0\n      }, rawOffset);\n      if (checkMainAxis) {\n        var len = mainAxis === 'y' ? 'height' : 'width';\n        var limitMin = rects.reference[mainAxis] - rects.floating[len] + computedOffset.mainAxis;\n        var limitMax = rects.reference[mainAxis] + rects.reference[len] - computedOffset.mainAxis;\n        if (mainAxisCoord < limitMin) {\n          mainAxisCoord = limitMin;\n        } else if (mainAxisCoord > limitMax) {\n          mainAxisCoord = limitMax;\n        }\n      }\n      if (checkCrossAxis) {\n        var _middlewareData$offse, _middlewareData$offse2;\n        var _len = mainAxis === 'y' ? 'width' : 'height';\n        var isOriginSide = ['top', 'left'].includes(getSide(placement));\n        var _limitMin = rects.reference[crossAxis] - rects.floating[_len] + (isOriginSide ? ((_middlewareData$offse = middlewareData.offset) == null ? void 0 : _middlewareData$offse[crossAxis]) || 0 : 0) + (isOriginSide ? 0 : computedOffset.crossAxis);\n        var _limitMax = rects.reference[crossAxis] + rects.reference[_len] + (isOriginSide ? 0 : ((_middlewareData$offse2 = middlewareData.offset) == null ? void 0 : _middlewareData$offse2[crossAxis]) || 0) - (isOriginSide ? computedOffset.crossAxis : 0);\n        if (crossAxisCoord < _limitMin) {\n          crossAxisCoord = _limitMin;\n        } else if (crossAxisCoord > _limitMax) {\n          crossAxisCoord = _limitMax;\n        }\n      }\n      return {\n        [mainAxis]: mainAxisCoord,\n        [crossAxis]: crossAxisCoord\n      };\n    }\n  };\n};\n\n/**\n * Provides data that allows you to change the size of the floating element —\n * for instance, prevent it from overflowing the clipping boundary or match the\n * width of the reference element.\n * @see https://floating-ui.com/docs/size\n */\nvar size = function size(options) {\n  if (options === void 0) {\n    options = {};\n  }\n  return {\n    name: 'size',\n    options,\n    fn(state) {\n      return _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee9() {\n        var _state$middlewareData, _state$middlewareData2, placement, rects, platform, elements, _evaluate7, _evaluate7$apply, apply, detectOverflowOptions, overflow, side, alignment, isYAxis, _rects$floating, width, height, heightSide, widthSide, maximumClippingHeight, maximumClippingWidth, overflowAvailableHeight, overflowAvailableWidth, noShift, availableHeight, availableWidth, xMin, xMax, yMin, yMax, nextDimensions;\n        return _regeneratorRuntime().wrap(function _callee9$(_context9) {\n          while (1) switch (_context9.prev = _context9.next) {\n            case 0:\n              placement = state.placement, rects = state.rects, platform = state.platform, elements = state.elements;\n              _evaluate7 = evaluate(options, state), _evaluate7$apply = _evaluate7.apply, apply = _evaluate7$apply === void 0 ? function () {} : _evaluate7$apply, detectOverflowOptions = _objectWithoutProperties(_evaluate7, _excluded5);\n              _context9.next = 4;\n              return detectOverflow(state, detectOverflowOptions);\n            case 4:\n              overflow = _context9.sent;\n              side = getSide(placement);\n              alignment = getAlignment(placement);\n              isYAxis = getSideAxis(placement) === 'y';\n              _rects$floating = rects.floating, width = _rects$floating.width, height = _rects$floating.height;\n              if (!(side === 'top' || side === 'bottom')) {\n                _context9.next = 28;\n                break;\n              }\n              heightSide = side;\n              _context9.t0 = alignment;\n              _context9.next = 14;\n              return platform.isRTL == null ? void 0 : platform.isRTL(elements.floating);\n            case 14:\n              if (!_context9.sent) {\n                _context9.next = 18;\n                break;\n              }\n              _context9.t1 = 'start';\n              _context9.next = 19;\n              break;\n            case 18:\n              _context9.t1 = 'end';\n            case 19:\n              _context9.t2 = _context9.t1;\n              if (!(_context9.t0 === _context9.t2)) {\n                _context9.next = 24;\n                break;\n              }\n              _context9.t3 = 'left';\n              _context9.next = 25;\n              break;\n            case 24:\n              _context9.t3 = 'right';\n            case 25:\n              widthSide = _context9.t3;\n              _context9.next = 30;\n              break;\n            case 28:\n              widthSide = side;\n              heightSide = alignment === 'end' ? 'top' : 'bottom';\n            case 30:\n              maximumClippingHeight = height - overflow.top - overflow.bottom;\n              maximumClippingWidth = width - overflow.left - overflow.right;\n              overflowAvailableHeight = min(height - overflow[heightSide], maximumClippingHeight);\n              overflowAvailableWidth = min(width - overflow[widthSide], maximumClippingWidth);\n              noShift = !state.middlewareData.shift;\n              availableHeight = overflowAvailableHeight;\n              availableWidth = overflowAvailableWidth;\n              if ((_state$middlewareData = state.middlewareData.shift) != null && _state$middlewareData.enabled.x) {\n                availableWidth = maximumClippingWidth;\n              }\n              if ((_state$middlewareData2 = state.middlewareData.shift) != null && _state$middlewareData2.enabled.y) {\n                availableHeight = maximumClippingHeight;\n              }\n              if (noShift && !alignment) {\n                xMin = max(overflow.left, 0);\n                xMax = max(overflow.right, 0);\n                yMin = max(overflow.top, 0);\n                yMax = max(overflow.bottom, 0);\n                if (isYAxis) {\n                  availableWidth = width - 2 * (xMin !== 0 || xMax !== 0 ? xMin + xMax : max(overflow.left, overflow.right));\n                } else {\n                  availableHeight = height - 2 * (yMin !== 0 || yMax !== 0 ? yMin + yMax : max(overflow.top, overflow.bottom));\n                }\n              }\n              _context9.next = 42;\n              return apply(_objectSpread(_objectSpread({}, state), {}, {\n                availableWidth,\n                availableHeight\n              }));\n            case 42:\n              _context9.next = 44;\n              return platform.getDimensions(elements.floating);\n            case 44:\n              nextDimensions = _context9.sent;\n              if (!(width !== nextDimensions.width || height !== nextDimensions.height)) {\n                _context9.next = 47;\n                break;\n              }\n              return _context9.abrupt(\"return\", {\n                reset: {\n                  rects: true\n                }\n              });\n            case 47:\n              return _context9.abrupt(\"return\", {});\n            case 48:\n            case \"end\":\n              return _context9.stop();\n          }\n        }, _callee9);\n      }))();\n    }\n  };\n};\nexport { arrow, autoPlacement, computePosition, detectOverflow, flip, hide, inline, limitShift, offset, shift, size };", "map": {"version": 3, "names": ["_regeneratorRuntime", "e", "t", "r", "Object", "prototype", "n", "hasOwnProperty", "o", "defineProperty", "value", "i", "Symbol", "a", "iterator", "c", "asyncIterator", "u", "toStringTag", "define", "enumerable", "configurable", "writable", "wrap", "Generator", "create", "Context", "makeInvokeMethod", "tryCatch", "type", "arg", "call", "h", "l", "f", "s", "y", "GeneratorFunction", "GeneratorFunctionPrototype", "p", "d", "getPrototypeOf", "v", "values", "g", "defineIteratorMethods", "for<PERSON>ach", "_invoke", "AsyncIterator", "invoke", "resolve", "__await", "then", "callInvokeWithMethodAndArg", "Error", "done", "method", "delegate", "maybeInvokeDelegate", "sent", "_sent", "dispatchException", "abrupt", "return", "TypeError", "resultName", "next", "nextLoc", "pushTryEntry", "tryLoc", "catchLoc", "finallyLoc", "afterLoc", "tryEntries", "push", "resetTryEntry", "completion", "reset", "isNaN", "length", "displayName", "isGeneratorFunction", "constructor", "name", "mark", "setPrototypeOf", "__proto__", "awrap", "async", "Promise", "keys", "reverse", "pop", "prev", "char<PERSON>t", "slice", "stop", "rval", "handle", "complete", "finish", "catch", "_catch", "<PERSON><PERSON><PERSON>", "ownKeys", "getOwnPropertySymbols", "filter", "getOwnPropertyDescriptor", "apply", "_objectSpread", "arguments", "_defineProperty", "getOwnPropertyDescriptors", "defineProperties", "_to<PERSON><PERSON><PERSON><PERSON><PERSON>", "_toPrimitive", "toPrimitive", "String", "Number", "asyncGeneratorStep", "_asyncToGenerator", "_next", "_throw", "getSideAxis", "getAlignmentAxis", "getAxisLength", "getSide", "getAlignment", "evaluate", "getPaddingObject", "rectToClientRect", "min", "clamp", "placements", "getAlignmentSides", "getOppositeAlignmentPlacement", "getOppositePlacement", "getExpandedPlacements", "getOppositeAxisPlacements", "sides", "max", "getOppositeAxis", "computeCoordsFromPlacement", "_ref", "placement", "rtl", "reference", "floating", "sideAxis", "alignmentAxis", "align<PERSON><PERSON><PERSON>", "side", "isVertical", "commonX", "x", "width", "commonY", "height", "commonAlign", "coords", "computePosition", "_ref2", "_callee", "config", "_config$placement", "_config$strategy", "strategy", "_config$middleware", "middleware", "platform", "validMiddleware", "rects", "_computeCoordsFromPla", "statefulPlacement", "middlewareData", "resetCount", "_validMiddleware$i", "fn", "_yield$fn", "nextX", "nextY", "data", "_computeCoordsFromPla2", "_callee$", "_context", "Boolean", "isRTL", "getElementRects", "initialPlacement", "elements", "t0", "_x", "_x2", "_x3", "detectOverflow", "_x4", "_x5", "_detectOverflow", "_callee10", "state", "options", "_await$platform$isEle", "_evaluate8", "_evaluate8$boundary", "boundary", "_evaluate8$rootBounda", "rootBoundary", "_evaluate8$elementCon", "elementContext", "_evaluate8$altBoundar", "altBoundary", "_evaluate8$padding", "padding", "paddingObject", "altContext", "element", "clippingClientRect", "rect", "offsetParent", "offsetScale", "elementClientRect", "_callee10$", "_context10", "t1", "isElement", "t2", "t3", "t4", "t5", "contextElement", "getDocumentElement", "t6", "t7", "t8", "t9", "t10", "getClippingRect", "t11", "getOffsetParent", "getScale", "t13", "t12", "t14", "convertOffsetParentRelativeRectToViewportRelativeRect", "t15", "t16", "top", "bottom", "left", "right", "arrow", "_callee2", "_ref3", "_ref3$padding", "axis", "arrowDimensions", "isYAxis", "minProp", "maxProp", "clientProp", "endDiff", "startDiff", "arrowOffsetParent", "clientSize", "centerToReference", "largestPossiblePadding", "minPadding", "maxPadding", "min$1", "center", "offset", "shouldAddOffset", "alignmentOffset", "_callee2$", "_context2", "getDimensions", "centerOffset", "getPlacementList", "alignment", "autoAlignment", "allowedPlacements", "allowedPlacementsSortedByAlignment", "concat", "_toConsumableArray", "autoPlacement", "_callee3", "_middlewareData$autoP", "_middlewareData$autoP2", "_placementsThatFitOnE", "_evaluate", "_evaluate$crossAxis", "crossAxis", "_evaluate$allowedPlac", "_evaluate$autoAlignme", "detectOverflowOptions", "placements$1", "overflow", "currentIndex", "currentPlacement", "alignmentSides", "currentOverflows", "allOverflows", "nextPlacement", "placementsSortedByMostSpace", "placementsThatFitOnEachSide", "resetPlacement", "_callee3$", "_context3", "_objectWithoutProperties", "_excluded", "undefined", "index", "overflows", "map", "reduce", "acc", "sort", "b", "every", "flip", "_callee4", "_middlewareData$arrow", "_middlewareData$flip", "_evaluate2", "_evaluate2$mainAxis", "checkMainAxis", "_evaluate2$crossAxis", "checkCrossAxis", "specifiedFallbackPlacements", "_evaluate2$fallbackSt", "fallbackStrategy", "_evaluate2$fallbackAx", "fallbackAxisSideDirection", "_evaluate2$flipAlignm", "flipAlignment", "initialSideAxis", "isBasePlacement", "fallbackPlacements", "hasFallbackAxisSideDirection", "overflowsData", "_sides", "_middlewareData$flip2", "_overflowsData$filter", "nextIndex", "_overflowsData$filter2", "_placement", "_callee4$", "_context4", "mainAxis", "_excluded2", "currentSideAxis", "getSideOffsets", "isAnySideFullyClipped", "some", "hide", "_callee5", "_evaluate3", "_evaluate3$strategy", "offsets", "_overflow", "_offsets", "_callee5$", "_context5", "_excluded3", "referenceHiddenOffsets", "referenceHidden", "escapedOffsets", "escaped", "getBoundingRect", "minX", "minY", "maxX", "maxY", "getRectsByLine", "sortedRects", "groups", "prevRect", "inline", "_callee6", "_evaluate4", "_evaluate4$padding", "nativeClientRects", "clientRects", "fallback", "getBoundingClientRect", "resetRects", "_callee6$", "_context6", "_getBoundingClientRec", "find", "firstRect", "lastRect", "isTop", "isLeftSide", "maxRight", "minLeft", "measureRects", "Array", "getClientRects", "from", "convertValueToCoords", "_x6", "_x7", "_convertValueToCoords", "_callee11", "mainAxisMulti", "crossAxisMulti", "rawValue", "_ref4", "_callee11$", "_context11", "includes", "_callee7", "_middlewareData$offse", "diffCoords", "_callee7$", "_context7", "shift", "_callee8", "_evaluate5", "_evaluate5$mainAxis", "_evaluate5$crossAxis", "_evaluate5$limiter", "limiter", "mainAxisCoord", "crossAxisCoord", "minSide", "maxSide", "_min", "_max", "_minSide", "_maxSide", "_min2", "_max2", "limitedCoords", "_callee8$", "_context8", "_excluded4", "enabled", "limitShift", "_evaluate6", "_evaluate6$offset", "_evaluate6$mainAxis", "_evaluate6$crossAxis", "rawOffset", "computedOffset", "len", "limitMin", "limitMax", "_middlewareData$offse2", "isOriginSide", "size", "_callee9", "_state$middlewareData", "_state$middlewareData2", "_evaluate7", "_evaluate7$apply", "_rects$floating", "heightSide", "widthSide", "maximumClippingHeight", "maximumClippingWidth", "overflowAvailableHeight", "overflowAvailableWidth", "noShift", "availableHeight", "availableWidth", "xMin", "xMax", "yMin", "yMax", "nextDimensions", "_callee9$", "_context9", "_excluded5"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/node_modules/.pnpm/@floating-ui+core@1.6.8/node_modules/@floating-ui/core/dist/floating-ui.core.mjs"], "sourcesContent": ["import { getSideAxis, getAlignmentAxis, getAxisLength, getSide, getAlignment, evaluate, getPaddingObject, rectToClientRect, min, clamp, placements, getAlignmentSides, getOppositeAlignmentPlacement, getOppositePlacement, getExpandedPlacements, getOppositeAxisPlacements, sides, max, getOppositeAxis } from '@floating-ui/utils';\nexport { rectToClientRect } from '@floating-ui/utils';\n\nfunction computeCoordsFromPlacement(_ref, placement, rtl) {\n  let {\n    reference,\n    floating\n  } = _ref;\n  const sideAxis = getSideAxis(placement);\n  const alignmentAxis = getAlignmentAxis(placement);\n  const alignLength = getAxisLength(alignmentAxis);\n  const side = getSide(placement);\n  const isVertical = sideAxis === 'y';\n  const commonX = reference.x + reference.width / 2 - floating.width / 2;\n  const commonY = reference.y + reference.height / 2 - floating.height / 2;\n  const commonAlign = reference[alignLength] / 2 - floating[alignLength] / 2;\n  let coords;\n  switch (side) {\n    case 'top':\n      coords = {\n        x: commonX,\n        y: reference.y - floating.height\n      };\n      break;\n    case 'bottom':\n      coords = {\n        x: commonX,\n        y: reference.y + reference.height\n      };\n      break;\n    case 'right':\n      coords = {\n        x: reference.x + reference.width,\n        y: commonY\n      };\n      break;\n    case 'left':\n      coords = {\n        x: reference.x - floating.width,\n        y: commonY\n      };\n      break;\n    default:\n      coords = {\n        x: reference.x,\n        y: reference.y\n      };\n  }\n  switch (getAlignment(placement)) {\n    case 'start':\n      coords[alignmentAxis] -= commonAlign * (rtl && isVertical ? -1 : 1);\n      break;\n    case 'end':\n      coords[alignmentAxis] += commonAlign * (rtl && isVertical ? -1 : 1);\n      break;\n  }\n  return coords;\n}\n\n/**\n * Computes the `x` and `y` coordinates that will place the floating element\n * next to a given reference element.\n *\n * This export does not have any `platform` interface logic. You will need to\n * write one for the platform you are using Floating UI with.\n */\nconst computePosition = async (reference, floating, config) => {\n  const {\n    placement = 'bottom',\n    strategy = 'absolute',\n    middleware = [],\n    platform\n  } = config;\n  const validMiddleware = middleware.filter(Boolean);\n  const rtl = await (platform.isRTL == null ? void 0 : platform.isRTL(floating));\n  let rects = await platform.getElementRects({\n    reference,\n    floating,\n    strategy\n  });\n  let {\n    x,\n    y\n  } = computeCoordsFromPlacement(rects, placement, rtl);\n  let statefulPlacement = placement;\n  let middlewareData = {};\n  let resetCount = 0;\n  for (let i = 0; i < validMiddleware.length; i++) {\n    const {\n      name,\n      fn\n    } = validMiddleware[i];\n    const {\n      x: nextX,\n      y: nextY,\n      data,\n      reset\n    } = await fn({\n      x,\n      y,\n      initialPlacement: placement,\n      placement: statefulPlacement,\n      strategy,\n      middlewareData,\n      rects,\n      platform,\n      elements: {\n        reference,\n        floating\n      }\n    });\n    x = nextX != null ? nextX : x;\n    y = nextY != null ? nextY : y;\n    middlewareData = {\n      ...middlewareData,\n      [name]: {\n        ...middlewareData[name],\n        ...data\n      }\n    };\n    if (reset && resetCount <= 50) {\n      resetCount++;\n      if (typeof reset === 'object') {\n        if (reset.placement) {\n          statefulPlacement = reset.placement;\n        }\n        if (reset.rects) {\n          rects = reset.rects === true ? await platform.getElementRects({\n            reference,\n            floating,\n            strategy\n          }) : reset.rects;\n        }\n        ({\n          x,\n          y\n        } = computeCoordsFromPlacement(rects, statefulPlacement, rtl));\n      }\n      i = -1;\n    }\n  }\n  return {\n    x,\n    y,\n    placement: statefulPlacement,\n    strategy,\n    middlewareData\n  };\n};\n\n/**\n * Resolves with an object of overflow side offsets that determine how much the\n * element is overflowing a given clipping boundary on each side.\n * - positive = overflowing the boundary by that number of pixels\n * - negative = how many pixels left before it will overflow\n * - 0 = lies flush with the boundary\n * @see https://floating-ui.com/docs/detectOverflow\n */\nasync function detectOverflow(state, options) {\n  var _await$platform$isEle;\n  if (options === void 0) {\n    options = {};\n  }\n  const {\n    x,\n    y,\n    platform,\n    rects,\n    elements,\n    strategy\n  } = state;\n  const {\n    boundary = 'clippingAncestors',\n    rootBoundary = 'viewport',\n    elementContext = 'floating',\n    altBoundary = false,\n    padding = 0\n  } = evaluate(options, state);\n  const paddingObject = getPaddingObject(padding);\n  const altContext = elementContext === 'floating' ? 'reference' : 'floating';\n  const element = elements[altBoundary ? altContext : elementContext];\n  const clippingClientRect = rectToClientRect(await platform.getClippingRect({\n    element: ((_await$platform$isEle = await (platform.isElement == null ? void 0 : platform.isElement(element))) != null ? _await$platform$isEle : true) ? element : element.contextElement || (await (platform.getDocumentElement == null ? void 0 : platform.getDocumentElement(elements.floating))),\n    boundary,\n    rootBoundary,\n    strategy\n  }));\n  const rect = elementContext === 'floating' ? {\n    x,\n    y,\n    width: rects.floating.width,\n    height: rects.floating.height\n  } : rects.reference;\n  const offsetParent = await (platform.getOffsetParent == null ? void 0 : platform.getOffsetParent(elements.floating));\n  const offsetScale = (await (platform.isElement == null ? void 0 : platform.isElement(offsetParent))) ? (await (platform.getScale == null ? void 0 : platform.getScale(offsetParent))) || {\n    x: 1,\n    y: 1\n  } : {\n    x: 1,\n    y: 1\n  };\n  const elementClientRect = rectToClientRect(platform.convertOffsetParentRelativeRectToViewportRelativeRect ? await platform.convertOffsetParentRelativeRectToViewportRelativeRect({\n    elements,\n    rect,\n    offsetParent,\n    strategy\n  }) : rect);\n  return {\n    top: (clippingClientRect.top - elementClientRect.top + paddingObject.top) / offsetScale.y,\n    bottom: (elementClientRect.bottom - clippingClientRect.bottom + paddingObject.bottom) / offsetScale.y,\n    left: (clippingClientRect.left - elementClientRect.left + paddingObject.left) / offsetScale.x,\n    right: (elementClientRect.right - clippingClientRect.right + paddingObject.right) / offsetScale.x\n  };\n}\n\n/**\n * Provides data to position an inner element of the floating element so that it\n * appears centered to the reference element.\n * @see https://floating-ui.com/docs/arrow\n */\nconst arrow = options => ({\n  name: 'arrow',\n  options,\n  async fn(state) {\n    const {\n      x,\n      y,\n      placement,\n      rects,\n      platform,\n      elements,\n      middlewareData\n    } = state;\n    // Since `element` is required, we don't Partial<> the type.\n    const {\n      element,\n      padding = 0\n    } = evaluate(options, state) || {};\n    if (element == null) {\n      return {};\n    }\n    const paddingObject = getPaddingObject(padding);\n    const coords = {\n      x,\n      y\n    };\n    const axis = getAlignmentAxis(placement);\n    const length = getAxisLength(axis);\n    const arrowDimensions = await platform.getDimensions(element);\n    const isYAxis = axis === 'y';\n    const minProp = isYAxis ? 'top' : 'left';\n    const maxProp = isYAxis ? 'bottom' : 'right';\n    const clientProp = isYAxis ? 'clientHeight' : 'clientWidth';\n    const endDiff = rects.reference[length] + rects.reference[axis] - coords[axis] - rects.floating[length];\n    const startDiff = coords[axis] - rects.reference[axis];\n    const arrowOffsetParent = await (platform.getOffsetParent == null ? void 0 : platform.getOffsetParent(element));\n    let clientSize = arrowOffsetParent ? arrowOffsetParent[clientProp] : 0;\n\n    // DOM platform can return `window` as the `offsetParent`.\n    if (!clientSize || !(await (platform.isElement == null ? void 0 : platform.isElement(arrowOffsetParent)))) {\n      clientSize = elements.floating[clientProp] || rects.floating[length];\n    }\n    const centerToReference = endDiff / 2 - startDiff / 2;\n\n    // If the padding is large enough that it causes the arrow to no longer be\n    // centered, modify the padding so that it is centered.\n    const largestPossiblePadding = clientSize / 2 - arrowDimensions[length] / 2 - 1;\n    const minPadding = min(paddingObject[minProp], largestPossiblePadding);\n    const maxPadding = min(paddingObject[maxProp], largestPossiblePadding);\n\n    // Make sure the arrow doesn't overflow the floating element if the center\n    // point is outside the floating element's bounds.\n    const min$1 = minPadding;\n    const max = clientSize - arrowDimensions[length] - maxPadding;\n    const center = clientSize / 2 - arrowDimensions[length] / 2 + centerToReference;\n    const offset = clamp(min$1, center, max);\n\n    // If the reference is small enough that the arrow's padding causes it to\n    // to point to nothing for an aligned placement, adjust the offset of the\n    // floating element itself. To ensure `shift()` continues to take action,\n    // a single reset is performed when this is true.\n    const shouldAddOffset = !middlewareData.arrow && getAlignment(placement) != null && center !== offset && rects.reference[length] / 2 - (center < min$1 ? minPadding : maxPadding) - arrowDimensions[length] / 2 < 0;\n    const alignmentOffset = shouldAddOffset ? center < min$1 ? center - min$1 : center - max : 0;\n    return {\n      [axis]: coords[axis] + alignmentOffset,\n      data: {\n        [axis]: offset,\n        centerOffset: center - offset - alignmentOffset,\n        ...(shouldAddOffset && {\n          alignmentOffset\n        })\n      },\n      reset: shouldAddOffset\n    };\n  }\n});\n\nfunction getPlacementList(alignment, autoAlignment, allowedPlacements) {\n  const allowedPlacementsSortedByAlignment = alignment ? [...allowedPlacements.filter(placement => getAlignment(placement) === alignment), ...allowedPlacements.filter(placement => getAlignment(placement) !== alignment)] : allowedPlacements.filter(placement => getSide(placement) === placement);\n  return allowedPlacementsSortedByAlignment.filter(placement => {\n    if (alignment) {\n      return getAlignment(placement) === alignment || (autoAlignment ? getOppositeAlignmentPlacement(placement) !== placement : false);\n    }\n    return true;\n  });\n}\n/**\n * Optimizes the visibility of the floating element by choosing the placement\n * that has the most space available automatically, without needing to specify a\n * preferred placement. Alternative to `flip`.\n * @see https://floating-ui.com/docs/autoPlacement\n */\nconst autoPlacement = function (options) {\n  if (options === void 0) {\n    options = {};\n  }\n  return {\n    name: 'autoPlacement',\n    options,\n    async fn(state) {\n      var _middlewareData$autoP, _middlewareData$autoP2, _placementsThatFitOnE;\n      const {\n        rects,\n        middlewareData,\n        placement,\n        platform,\n        elements\n      } = state;\n      const {\n        crossAxis = false,\n        alignment,\n        allowedPlacements = placements,\n        autoAlignment = true,\n        ...detectOverflowOptions\n      } = evaluate(options, state);\n      const placements$1 = alignment !== undefined || allowedPlacements === placements ? getPlacementList(alignment || null, autoAlignment, allowedPlacements) : allowedPlacements;\n      const overflow = await detectOverflow(state, detectOverflowOptions);\n      const currentIndex = ((_middlewareData$autoP = middlewareData.autoPlacement) == null ? void 0 : _middlewareData$autoP.index) || 0;\n      const currentPlacement = placements$1[currentIndex];\n      if (currentPlacement == null) {\n        return {};\n      }\n      const alignmentSides = getAlignmentSides(currentPlacement, rects, await (platform.isRTL == null ? void 0 : platform.isRTL(elements.floating)));\n\n      // Make `computeCoords` start from the right place.\n      if (placement !== currentPlacement) {\n        return {\n          reset: {\n            placement: placements$1[0]\n          }\n        };\n      }\n      const currentOverflows = [overflow[getSide(currentPlacement)], overflow[alignmentSides[0]], overflow[alignmentSides[1]]];\n      const allOverflows = [...(((_middlewareData$autoP2 = middlewareData.autoPlacement) == null ? void 0 : _middlewareData$autoP2.overflows) || []), {\n        placement: currentPlacement,\n        overflows: currentOverflows\n      }];\n      const nextPlacement = placements$1[currentIndex + 1];\n\n      // There are more placements to check.\n      if (nextPlacement) {\n        return {\n          data: {\n            index: currentIndex + 1,\n            overflows: allOverflows\n          },\n          reset: {\n            placement: nextPlacement\n          }\n        };\n      }\n      const placementsSortedByMostSpace = allOverflows.map(d => {\n        const alignment = getAlignment(d.placement);\n        return [d.placement, alignment && crossAxis ?\n        // Check along the mainAxis and main crossAxis side.\n        d.overflows.slice(0, 2).reduce((acc, v) => acc + v, 0) :\n        // Check only the mainAxis.\n        d.overflows[0], d.overflows];\n      }).sort((a, b) => a[1] - b[1]);\n      const placementsThatFitOnEachSide = placementsSortedByMostSpace.filter(d => d[2].slice(0,\n      // Aligned placements should not check their opposite crossAxis\n      // side.\n      getAlignment(d[0]) ? 2 : 3).every(v => v <= 0));\n      const resetPlacement = ((_placementsThatFitOnE = placementsThatFitOnEachSide[0]) == null ? void 0 : _placementsThatFitOnE[0]) || placementsSortedByMostSpace[0][0];\n      if (resetPlacement !== placement) {\n        return {\n          data: {\n            index: currentIndex + 1,\n            overflows: allOverflows\n          },\n          reset: {\n            placement: resetPlacement\n          }\n        };\n      }\n      return {};\n    }\n  };\n};\n\n/**\n * Optimizes the visibility of the floating element by flipping the `placement`\n * in order to keep it in view when the preferred placement(s) will overflow the\n * clipping boundary. Alternative to `autoPlacement`.\n * @see https://floating-ui.com/docs/flip\n */\nconst flip = function (options) {\n  if (options === void 0) {\n    options = {};\n  }\n  return {\n    name: 'flip',\n    options,\n    async fn(state) {\n      var _middlewareData$arrow, _middlewareData$flip;\n      const {\n        placement,\n        middlewareData,\n        rects,\n        initialPlacement,\n        platform,\n        elements\n      } = state;\n      const {\n        mainAxis: checkMainAxis = true,\n        crossAxis: checkCrossAxis = true,\n        fallbackPlacements: specifiedFallbackPlacements,\n        fallbackStrategy = 'bestFit',\n        fallbackAxisSideDirection = 'none',\n        flipAlignment = true,\n        ...detectOverflowOptions\n      } = evaluate(options, state);\n\n      // If a reset by the arrow was caused due to an alignment offset being\n      // added, we should skip any logic now since `flip()` has already done its\n      // work.\n      // https://github.com/floating-ui/floating-ui/issues/2549#issuecomment-1719601643\n      if ((_middlewareData$arrow = middlewareData.arrow) != null && _middlewareData$arrow.alignmentOffset) {\n        return {};\n      }\n      const side = getSide(placement);\n      const initialSideAxis = getSideAxis(initialPlacement);\n      const isBasePlacement = getSide(initialPlacement) === initialPlacement;\n      const rtl = await (platform.isRTL == null ? void 0 : platform.isRTL(elements.floating));\n      const fallbackPlacements = specifiedFallbackPlacements || (isBasePlacement || !flipAlignment ? [getOppositePlacement(initialPlacement)] : getExpandedPlacements(initialPlacement));\n      const hasFallbackAxisSideDirection = fallbackAxisSideDirection !== 'none';\n      if (!specifiedFallbackPlacements && hasFallbackAxisSideDirection) {\n        fallbackPlacements.push(...getOppositeAxisPlacements(initialPlacement, flipAlignment, fallbackAxisSideDirection, rtl));\n      }\n      const placements = [initialPlacement, ...fallbackPlacements];\n      const overflow = await detectOverflow(state, detectOverflowOptions);\n      const overflows = [];\n      let overflowsData = ((_middlewareData$flip = middlewareData.flip) == null ? void 0 : _middlewareData$flip.overflows) || [];\n      if (checkMainAxis) {\n        overflows.push(overflow[side]);\n      }\n      if (checkCrossAxis) {\n        const sides = getAlignmentSides(placement, rects, rtl);\n        overflows.push(overflow[sides[0]], overflow[sides[1]]);\n      }\n      overflowsData = [...overflowsData, {\n        placement,\n        overflows\n      }];\n\n      // One or more sides is overflowing.\n      if (!overflows.every(side => side <= 0)) {\n        var _middlewareData$flip2, _overflowsData$filter;\n        const nextIndex = (((_middlewareData$flip2 = middlewareData.flip) == null ? void 0 : _middlewareData$flip2.index) || 0) + 1;\n        const nextPlacement = placements[nextIndex];\n        if (nextPlacement) {\n          // Try next placement and re-run the lifecycle.\n          return {\n            data: {\n              index: nextIndex,\n              overflows: overflowsData\n            },\n            reset: {\n              placement: nextPlacement\n            }\n          };\n        }\n\n        // First, find the candidates that fit on the mainAxis side of overflow,\n        // then find the placement that fits the best on the main crossAxis side.\n        let resetPlacement = (_overflowsData$filter = overflowsData.filter(d => d.overflows[0] <= 0).sort((a, b) => a.overflows[1] - b.overflows[1])[0]) == null ? void 0 : _overflowsData$filter.placement;\n\n        // Otherwise fallback.\n        if (!resetPlacement) {\n          switch (fallbackStrategy) {\n            case 'bestFit':\n              {\n                var _overflowsData$filter2;\n                const placement = (_overflowsData$filter2 = overflowsData.filter(d => {\n                  if (hasFallbackAxisSideDirection) {\n                    const currentSideAxis = getSideAxis(d.placement);\n                    return currentSideAxis === initialSideAxis ||\n                    // Create a bias to the `y` side axis due to horizontal\n                    // reading directions favoring greater width.\n                    currentSideAxis === 'y';\n                  }\n                  return true;\n                }).map(d => [d.placement, d.overflows.filter(overflow => overflow > 0).reduce((acc, overflow) => acc + overflow, 0)]).sort((a, b) => a[1] - b[1])[0]) == null ? void 0 : _overflowsData$filter2[0];\n                if (placement) {\n                  resetPlacement = placement;\n                }\n                break;\n              }\n            case 'initialPlacement':\n              resetPlacement = initialPlacement;\n              break;\n          }\n        }\n        if (placement !== resetPlacement) {\n          return {\n            reset: {\n              placement: resetPlacement\n            }\n          };\n        }\n      }\n      return {};\n    }\n  };\n};\n\nfunction getSideOffsets(overflow, rect) {\n  return {\n    top: overflow.top - rect.height,\n    right: overflow.right - rect.width,\n    bottom: overflow.bottom - rect.height,\n    left: overflow.left - rect.width\n  };\n}\nfunction isAnySideFullyClipped(overflow) {\n  return sides.some(side => overflow[side] >= 0);\n}\n/**\n * Provides data to hide the floating element in applicable situations, such as\n * when it is not in the same clipping context as the reference element.\n * @see https://floating-ui.com/docs/hide\n */\nconst hide = function (options) {\n  if (options === void 0) {\n    options = {};\n  }\n  return {\n    name: 'hide',\n    options,\n    async fn(state) {\n      const {\n        rects\n      } = state;\n      const {\n        strategy = 'referenceHidden',\n        ...detectOverflowOptions\n      } = evaluate(options, state);\n      switch (strategy) {\n        case 'referenceHidden':\n          {\n            const overflow = await detectOverflow(state, {\n              ...detectOverflowOptions,\n              elementContext: 'reference'\n            });\n            const offsets = getSideOffsets(overflow, rects.reference);\n            return {\n              data: {\n                referenceHiddenOffsets: offsets,\n                referenceHidden: isAnySideFullyClipped(offsets)\n              }\n            };\n          }\n        case 'escaped':\n          {\n            const overflow = await detectOverflow(state, {\n              ...detectOverflowOptions,\n              altBoundary: true\n            });\n            const offsets = getSideOffsets(overflow, rects.floating);\n            return {\n              data: {\n                escapedOffsets: offsets,\n                escaped: isAnySideFullyClipped(offsets)\n              }\n            };\n          }\n        default:\n          {\n            return {};\n          }\n      }\n    }\n  };\n};\n\nfunction getBoundingRect(rects) {\n  const minX = min(...rects.map(rect => rect.left));\n  const minY = min(...rects.map(rect => rect.top));\n  const maxX = max(...rects.map(rect => rect.right));\n  const maxY = max(...rects.map(rect => rect.bottom));\n  return {\n    x: minX,\n    y: minY,\n    width: maxX - minX,\n    height: maxY - minY\n  };\n}\nfunction getRectsByLine(rects) {\n  const sortedRects = rects.slice().sort((a, b) => a.y - b.y);\n  const groups = [];\n  let prevRect = null;\n  for (let i = 0; i < sortedRects.length; i++) {\n    const rect = sortedRects[i];\n    if (!prevRect || rect.y - prevRect.y > prevRect.height / 2) {\n      groups.push([rect]);\n    } else {\n      groups[groups.length - 1].push(rect);\n    }\n    prevRect = rect;\n  }\n  return groups.map(rect => rectToClientRect(getBoundingRect(rect)));\n}\n/**\n * Provides improved positioning for inline reference elements that can span\n * over multiple lines, such as hyperlinks or range selections.\n * @see https://floating-ui.com/docs/inline\n */\nconst inline = function (options) {\n  if (options === void 0) {\n    options = {};\n  }\n  return {\n    name: 'inline',\n    options,\n    async fn(state) {\n      const {\n        placement,\n        elements,\n        rects,\n        platform,\n        strategy\n      } = state;\n      // A MouseEvent's client{X,Y} coords can be up to 2 pixels off a\n      // ClientRect's bounds, despite the event listener being triggered. A\n      // padding of 2 seems to handle this issue.\n      const {\n        padding = 2,\n        x,\n        y\n      } = evaluate(options, state);\n      const nativeClientRects = Array.from((await (platform.getClientRects == null ? void 0 : platform.getClientRects(elements.reference))) || []);\n      const clientRects = getRectsByLine(nativeClientRects);\n      const fallback = rectToClientRect(getBoundingRect(nativeClientRects));\n      const paddingObject = getPaddingObject(padding);\n      function getBoundingClientRect() {\n        // There are two rects and they are disjoined.\n        if (clientRects.length === 2 && clientRects[0].left > clientRects[1].right && x != null && y != null) {\n          // Find the first rect in which the point is fully inside.\n          return clientRects.find(rect => x > rect.left - paddingObject.left && x < rect.right + paddingObject.right && y > rect.top - paddingObject.top && y < rect.bottom + paddingObject.bottom) || fallback;\n        }\n\n        // There are 2 or more connected rects.\n        if (clientRects.length >= 2) {\n          if (getSideAxis(placement) === 'y') {\n            const firstRect = clientRects[0];\n            const lastRect = clientRects[clientRects.length - 1];\n            const isTop = getSide(placement) === 'top';\n            const top = firstRect.top;\n            const bottom = lastRect.bottom;\n            const left = isTop ? firstRect.left : lastRect.left;\n            const right = isTop ? firstRect.right : lastRect.right;\n            const width = right - left;\n            const height = bottom - top;\n            return {\n              top,\n              bottom,\n              left,\n              right,\n              width,\n              height,\n              x: left,\n              y: top\n            };\n          }\n          const isLeftSide = getSide(placement) === 'left';\n          const maxRight = max(...clientRects.map(rect => rect.right));\n          const minLeft = min(...clientRects.map(rect => rect.left));\n          const measureRects = clientRects.filter(rect => isLeftSide ? rect.left === minLeft : rect.right === maxRight);\n          const top = measureRects[0].top;\n          const bottom = measureRects[measureRects.length - 1].bottom;\n          const left = minLeft;\n          const right = maxRight;\n          const width = right - left;\n          const height = bottom - top;\n          return {\n            top,\n            bottom,\n            left,\n            right,\n            width,\n            height,\n            x: left,\n            y: top\n          };\n        }\n        return fallback;\n      }\n      const resetRects = await platform.getElementRects({\n        reference: {\n          getBoundingClientRect\n        },\n        floating: elements.floating,\n        strategy\n      });\n      if (rects.reference.x !== resetRects.reference.x || rects.reference.y !== resetRects.reference.y || rects.reference.width !== resetRects.reference.width || rects.reference.height !== resetRects.reference.height) {\n        return {\n          reset: {\n            rects: resetRects\n          }\n        };\n      }\n      return {};\n    }\n  };\n};\n\n// For type backwards-compatibility, the `OffsetOptions` type was also\n// Derivable.\n\nasync function convertValueToCoords(state, options) {\n  const {\n    placement,\n    platform,\n    elements\n  } = state;\n  const rtl = await (platform.isRTL == null ? void 0 : platform.isRTL(elements.floating));\n  const side = getSide(placement);\n  const alignment = getAlignment(placement);\n  const isVertical = getSideAxis(placement) === 'y';\n  const mainAxisMulti = ['left', 'top'].includes(side) ? -1 : 1;\n  const crossAxisMulti = rtl && isVertical ? -1 : 1;\n  const rawValue = evaluate(options, state);\n\n  // eslint-disable-next-line prefer-const\n  let {\n    mainAxis,\n    crossAxis,\n    alignmentAxis\n  } = typeof rawValue === 'number' ? {\n    mainAxis: rawValue,\n    crossAxis: 0,\n    alignmentAxis: null\n  } : {\n    mainAxis: rawValue.mainAxis || 0,\n    crossAxis: rawValue.crossAxis || 0,\n    alignmentAxis: rawValue.alignmentAxis\n  };\n  if (alignment && typeof alignmentAxis === 'number') {\n    crossAxis = alignment === 'end' ? alignmentAxis * -1 : alignmentAxis;\n  }\n  return isVertical ? {\n    x: crossAxis * crossAxisMulti,\n    y: mainAxis * mainAxisMulti\n  } : {\n    x: mainAxis * mainAxisMulti,\n    y: crossAxis * crossAxisMulti\n  };\n}\n\n/**\n * Modifies the placement by translating the floating element along the\n * specified axes.\n * A number (shorthand for `mainAxis` or distance), or an axes configuration\n * object may be passed.\n * @see https://floating-ui.com/docs/offset\n */\nconst offset = function (options) {\n  if (options === void 0) {\n    options = 0;\n  }\n  return {\n    name: 'offset',\n    options,\n    async fn(state) {\n      var _middlewareData$offse, _middlewareData$arrow;\n      const {\n        x,\n        y,\n        placement,\n        middlewareData\n      } = state;\n      const diffCoords = await convertValueToCoords(state, options);\n\n      // If the placement is the same and the arrow caused an alignment offset\n      // then we don't need to change the positioning coordinates.\n      if (placement === ((_middlewareData$offse = middlewareData.offset) == null ? void 0 : _middlewareData$offse.placement) && (_middlewareData$arrow = middlewareData.arrow) != null && _middlewareData$arrow.alignmentOffset) {\n        return {};\n      }\n      return {\n        x: x + diffCoords.x,\n        y: y + diffCoords.y,\n        data: {\n          ...diffCoords,\n          placement\n        }\n      };\n    }\n  };\n};\n\n/**\n * Optimizes the visibility of the floating element by shifting it in order to\n * keep it in view when it will overflow the clipping boundary.\n * @see https://floating-ui.com/docs/shift\n */\nconst shift = function (options) {\n  if (options === void 0) {\n    options = {};\n  }\n  return {\n    name: 'shift',\n    options,\n    async fn(state) {\n      const {\n        x,\n        y,\n        placement\n      } = state;\n      const {\n        mainAxis: checkMainAxis = true,\n        crossAxis: checkCrossAxis = false,\n        limiter = {\n          fn: _ref => {\n            let {\n              x,\n              y\n            } = _ref;\n            return {\n              x,\n              y\n            };\n          }\n        },\n        ...detectOverflowOptions\n      } = evaluate(options, state);\n      const coords = {\n        x,\n        y\n      };\n      const overflow = await detectOverflow(state, detectOverflowOptions);\n      const crossAxis = getSideAxis(getSide(placement));\n      const mainAxis = getOppositeAxis(crossAxis);\n      let mainAxisCoord = coords[mainAxis];\n      let crossAxisCoord = coords[crossAxis];\n      if (checkMainAxis) {\n        const minSide = mainAxis === 'y' ? 'top' : 'left';\n        const maxSide = mainAxis === 'y' ? 'bottom' : 'right';\n        const min = mainAxisCoord + overflow[minSide];\n        const max = mainAxisCoord - overflow[maxSide];\n        mainAxisCoord = clamp(min, mainAxisCoord, max);\n      }\n      if (checkCrossAxis) {\n        const minSide = crossAxis === 'y' ? 'top' : 'left';\n        const maxSide = crossAxis === 'y' ? 'bottom' : 'right';\n        const min = crossAxisCoord + overflow[minSide];\n        const max = crossAxisCoord - overflow[maxSide];\n        crossAxisCoord = clamp(min, crossAxisCoord, max);\n      }\n      const limitedCoords = limiter.fn({\n        ...state,\n        [mainAxis]: mainAxisCoord,\n        [crossAxis]: crossAxisCoord\n      });\n      return {\n        ...limitedCoords,\n        data: {\n          x: limitedCoords.x - x,\n          y: limitedCoords.y - y,\n          enabled: {\n            [mainAxis]: checkMainAxis,\n            [crossAxis]: checkCrossAxis\n          }\n        }\n      };\n    }\n  };\n};\n/**\n * Built-in `limiter` that will stop `shift()` at a certain point.\n */\nconst limitShift = function (options) {\n  if (options === void 0) {\n    options = {};\n  }\n  return {\n    options,\n    fn(state) {\n      const {\n        x,\n        y,\n        placement,\n        rects,\n        middlewareData\n      } = state;\n      const {\n        offset = 0,\n        mainAxis: checkMainAxis = true,\n        crossAxis: checkCrossAxis = true\n      } = evaluate(options, state);\n      const coords = {\n        x,\n        y\n      };\n      const crossAxis = getSideAxis(placement);\n      const mainAxis = getOppositeAxis(crossAxis);\n      let mainAxisCoord = coords[mainAxis];\n      let crossAxisCoord = coords[crossAxis];\n      const rawOffset = evaluate(offset, state);\n      const computedOffset = typeof rawOffset === 'number' ? {\n        mainAxis: rawOffset,\n        crossAxis: 0\n      } : {\n        mainAxis: 0,\n        crossAxis: 0,\n        ...rawOffset\n      };\n      if (checkMainAxis) {\n        const len = mainAxis === 'y' ? 'height' : 'width';\n        const limitMin = rects.reference[mainAxis] - rects.floating[len] + computedOffset.mainAxis;\n        const limitMax = rects.reference[mainAxis] + rects.reference[len] - computedOffset.mainAxis;\n        if (mainAxisCoord < limitMin) {\n          mainAxisCoord = limitMin;\n        } else if (mainAxisCoord > limitMax) {\n          mainAxisCoord = limitMax;\n        }\n      }\n      if (checkCrossAxis) {\n        var _middlewareData$offse, _middlewareData$offse2;\n        const len = mainAxis === 'y' ? 'width' : 'height';\n        const isOriginSide = ['top', 'left'].includes(getSide(placement));\n        const limitMin = rects.reference[crossAxis] - rects.floating[len] + (isOriginSide ? ((_middlewareData$offse = middlewareData.offset) == null ? void 0 : _middlewareData$offse[crossAxis]) || 0 : 0) + (isOriginSide ? 0 : computedOffset.crossAxis);\n        const limitMax = rects.reference[crossAxis] + rects.reference[len] + (isOriginSide ? 0 : ((_middlewareData$offse2 = middlewareData.offset) == null ? void 0 : _middlewareData$offse2[crossAxis]) || 0) - (isOriginSide ? computedOffset.crossAxis : 0);\n        if (crossAxisCoord < limitMin) {\n          crossAxisCoord = limitMin;\n        } else if (crossAxisCoord > limitMax) {\n          crossAxisCoord = limitMax;\n        }\n      }\n      return {\n        [mainAxis]: mainAxisCoord,\n        [crossAxis]: crossAxisCoord\n      };\n    }\n  };\n};\n\n/**\n * Provides data that allows you to change the size of the floating element —\n * for instance, prevent it from overflowing the clipping boundary or match the\n * width of the reference element.\n * @see https://floating-ui.com/docs/size\n */\nconst size = function (options) {\n  if (options === void 0) {\n    options = {};\n  }\n  return {\n    name: 'size',\n    options,\n    async fn(state) {\n      var _state$middlewareData, _state$middlewareData2;\n      const {\n        placement,\n        rects,\n        platform,\n        elements\n      } = state;\n      const {\n        apply = () => {},\n        ...detectOverflowOptions\n      } = evaluate(options, state);\n      const overflow = await detectOverflow(state, detectOverflowOptions);\n      const side = getSide(placement);\n      const alignment = getAlignment(placement);\n      const isYAxis = getSideAxis(placement) === 'y';\n      const {\n        width,\n        height\n      } = rects.floating;\n      let heightSide;\n      let widthSide;\n      if (side === 'top' || side === 'bottom') {\n        heightSide = side;\n        widthSide = alignment === ((await (platform.isRTL == null ? void 0 : platform.isRTL(elements.floating))) ? 'start' : 'end') ? 'left' : 'right';\n      } else {\n        widthSide = side;\n        heightSide = alignment === 'end' ? 'top' : 'bottom';\n      }\n      const maximumClippingHeight = height - overflow.top - overflow.bottom;\n      const maximumClippingWidth = width - overflow.left - overflow.right;\n      const overflowAvailableHeight = min(height - overflow[heightSide], maximumClippingHeight);\n      const overflowAvailableWidth = min(width - overflow[widthSide], maximumClippingWidth);\n      const noShift = !state.middlewareData.shift;\n      let availableHeight = overflowAvailableHeight;\n      let availableWidth = overflowAvailableWidth;\n      if ((_state$middlewareData = state.middlewareData.shift) != null && _state$middlewareData.enabled.x) {\n        availableWidth = maximumClippingWidth;\n      }\n      if ((_state$middlewareData2 = state.middlewareData.shift) != null && _state$middlewareData2.enabled.y) {\n        availableHeight = maximumClippingHeight;\n      }\n      if (noShift && !alignment) {\n        const xMin = max(overflow.left, 0);\n        const xMax = max(overflow.right, 0);\n        const yMin = max(overflow.top, 0);\n        const yMax = max(overflow.bottom, 0);\n        if (isYAxis) {\n          availableWidth = width - 2 * (xMin !== 0 || xMax !== 0 ? xMin + xMax : max(overflow.left, overflow.right));\n        } else {\n          availableHeight = height - 2 * (yMin !== 0 || yMax !== 0 ? yMin + yMax : max(overflow.top, overflow.bottom));\n        }\n      }\n      await apply({\n        ...state,\n        availableWidth,\n        availableHeight\n      });\n      const nextDimensions = await platform.getDimensions(elements.floating);\n      if (width !== nextDimensions.width || height !== nextDimensions.height) {\n        return {\n          reset: {\n            rects: true\n          }\n        };\n      }\n      return {};\n    }\n  };\n};\n\nexport { arrow, autoPlacement, computePosition, detectOverflow, flip, hide, inline, limitShift, offset, shift, size };\n"], "mappings": ";;;;;;;;;;;;;+CACA,qJAAAA,mBAAA,YAAAA,oBAAA,WAAAC,CAAA,SAAAC,CAAA,EAAAD,CAAA,OAAAE,CAAA,GAAAC,MAAA,CAAAC,SAAA,EAAAC,CAAA,GAAAH,CAAA,CAAAI,cAAA,EAAAC,CAAA,GAAAJ,MAAA,CAAAK,cAAA,cAAAP,CAAA,EAAAD,CAAA,EAAAE,CAAA,IAAAD,CAAA,CAAAD,CAAA,IAAAE,CAAA,CAAAO,KAAA,KAAAC,CAAA,wBAAAC,MAAA,GAAAA,MAAA,OAAAC,CAAA,GAAAF,CAAA,CAAAG,QAAA,kBAAAC,CAAA,GAAAJ,CAAA,CAAAK,aAAA,uBAAAC,CAAA,GAAAN,CAAA,CAAAO,WAAA,8BAAAC,OAAAjB,CAAA,EAAAD,CAAA,EAAAE,CAAA,WAAAC,MAAA,CAAAK,cAAA,CAAAP,CAAA,EAAAD,CAAA,IAAAS,KAAA,EAAAP,CAAA,EAAAiB,UAAA,MAAAC,YAAA,MAAAC,QAAA,SAAApB,CAAA,CAAAD,CAAA,WAAAkB,MAAA,mBAAAjB,CAAA,IAAAiB,MAAA,YAAAA,OAAAjB,CAAA,EAAAD,CAAA,EAAAE,CAAA,WAAAD,CAAA,CAAAD,CAAA,IAAAE,CAAA,gBAAAoB,KAAArB,CAAA,EAAAD,CAAA,EAAAE,CAAA,EAAAG,CAAA,QAAAK,CAAA,GAAAV,CAAA,IAAAA,CAAA,CAAAI,SAAA,YAAAmB,SAAA,GAAAvB,CAAA,GAAAuB,SAAA,EAAAX,CAAA,GAAAT,MAAA,CAAAqB,MAAA,CAAAd,CAAA,CAAAN,SAAA,GAAAU,CAAA,OAAAW,OAAA,CAAApB,CAAA,gBAAAE,CAAA,CAAAK,CAAA,eAAAH,KAAA,EAAAiB,gBAAA,CAAAzB,CAAA,EAAAC,CAAA,EAAAY,CAAA,MAAAF,CAAA,aAAAe,SAAA1B,CAAA,EAAAD,CAAA,EAAAE,CAAA,mBAAA0B,IAAA,YAAAC,GAAA,EAAA5B,CAAA,CAAA6B,IAAA,CAAA9B,CAAA,EAAAE,CAAA,cAAAD,CAAA,aAAA2B,IAAA,WAAAC,GAAA,EAAA5B,CAAA,QAAAD,CAAA,CAAAsB,IAAA,GAAAA,IAAA,MAAAS,CAAA,qBAAAC,CAAA,qBAAAC,CAAA,gBAAAC,CAAA,gBAAAC,CAAA,gBAAAZ,UAAA,cAAAa,kBAAA,cAAAC,2BAAA,SAAAC,CAAA,OAAApB,MAAA,CAAAoB,CAAA,EAAA1B,CAAA,qCAAA2B,CAAA,GAAApC,MAAA,CAAAqC,cAAA,EAAAC,CAAA,GAAAF,CAAA,IAAAA,CAAA,CAAAA,CAAA,CAAAG,MAAA,QAAAD,CAAA,IAAAA,CAAA,KAAAvC,CAAA,IAAAG,CAAA,CAAAyB,IAAA,CAAAW,CAAA,EAAA7B,CAAA,MAAA0B,CAAA,GAAAG,CAAA,OAAAE,CAAA,GAAAN,0BAAA,CAAAjC,SAAA,GAAAmB,SAAA,CAAAnB,SAAA,GAAAD,MAAA,CAAAqB,MAAA,CAAAc,CAAA,YAAAM,sBAAA3C,CAAA,gCAAA4C,OAAA,WAAA7C,CAAA,IAAAkB,MAAA,CAAAjB,CAAA,EAAAD,CAAA,YAAAC,CAAA,gBAAA6C,OAAA,CAAA9C,CAAA,EAAAC,CAAA,sBAAA8C,cAAA9C,CAAA,EAAAD,CAAA,aAAAgD,OAAA9C,CAAA,EAAAK,CAAA,EAAAG,CAAA,EAAAE,CAAA,QAAAE,CAAA,GAAAa,QAAA,CAAA1B,CAAA,CAAAC,CAAA,GAAAD,CAAA,EAAAM,CAAA,mBAAAO,CAAA,CAAAc,IAAA,QAAAZ,CAAA,GAAAF,CAAA,CAAAe,GAAA,EAAAE,CAAA,GAAAf,CAAA,CAAAP,KAAA,SAAAsB,CAAA,uBAAAA,CAAA,IAAA1B,CAAA,CAAAyB,IAAA,CAAAC,CAAA,eAAA/B,CAAA,CAAAiD,OAAA,CAAAlB,CAAA,CAAAmB,OAAA,EAAAC,IAAA,WAAAlD,CAAA,IAAA+C,MAAA,SAAA/C,CAAA,EAAAS,CAAA,EAAAE,CAAA,gBAAAX,CAAA,IAAA+C,MAAA,UAAA/C,CAAA,EAAAS,CAAA,EAAAE,CAAA,QAAAZ,CAAA,CAAAiD,OAAA,CAAAlB,CAAA,EAAAoB,IAAA,WAAAlD,CAAA,IAAAe,CAAA,CAAAP,KAAA,GAAAR,CAAA,EAAAS,CAAA,CAAAM,CAAA,gBAAAf,CAAA,WAAA+C,MAAA,UAAA/C,CAAA,EAAAS,CAAA,EAAAE,CAAA,SAAAA,CAAA,CAAAE,CAAA,CAAAe,GAAA,SAAA3B,CAAA,EAAAK,CAAA,oBAAAE,KAAA,WAAAA,MAAAR,CAAA,EAAAI,CAAA,aAAA+C,2BAAA,eAAApD,CAAA,WAAAA,CAAA,EAAAE,CAAA,IAAA8C,MAAA,CAAA/C,CAAA,EAAAI,CAAA,EAAAL,CAAA,EAAAE,CAAA,gBAAAA,CAAA,GAAAA,CAAA,GAAAA,CAAA,CAAAiD,IAAA,CAAAC,0BAAA,EAAAA,0BAAA,IAAAA,0BAAA,qBAAA1B,iBAAA1B,CAAA,EAAAE,CAAA,EAAAG,CAAA,QAAAE,CAAA,GAAAwB,CAAA,mBAAArB,CAAA,EAAAE,CAAA,QAAAL,CAAA,KAAA0B,CAAA,QAAAoB,KAAA,sCAAA9C,CAAA,KAAA2B,CAAA,oBAAAxB,CAAA,QAAAE,CAAA,WAAAH,KAAA,EAAAR,CAAA,EAAAqD,IAAA,eAAAjD,CAAA,CAAAkD,MAAA,GAAA7C,CAAA,EAAAL,CAAA,CAAAwB,GAAA,GAAAjB,CAAA,UAAAE,CAAA,GAAAT,CAAA,CAAAmD,QAAA,MAAA1C,CAAA,QAAAE,CAAA,GAAAyC,mBAAA,CAAA3C,CAAA,EAAAT,CAAA,OAAAW,CAAA,QAAAA,CAAA,KAAAmB,CAAA,mBAAAnB,CAAA,qBAAAX,CAAA,CAAAkD,MAAA,EAAAlD,CAAA,CAAAqD,IAAA,GAAArD,CAAA,CAAAsD,KAAA,GAAAtD,CAAA,CAAAwB,GAAA,sBAAAxB,CAAA,CAAAkD,MAAA,QAAAhD,CAAA,KAAAwB,CAAA,QAAAxB,CAAA,GAAA2B,CAAA,EAAA7B,CAAA,CAAAwB,GAAA,EAAAxB,CAAA,CAAAuD,iBAAA,CAAAvD,CAAA,CAAAwB,GAAA,uBAAAxB,CAAA,CAAAkD,MAAA,IAAAlD,CAAA,CAAAwD,MAAA,WAAAxD,CAAA,CAAAwB,GAAA,GAAAtB,CAAA,GAAA0B,CAAA,MAAAK,CAAA,GAAAX,QAAA,CAAA3B,CAAA,EAAAE,CAAA,EAAAG,CAAA,oBAAAiC,CAAA,CAAAV,IAAA,QAAArB,CAAA,GAAAF,CAAA,CAAAiD,IAAA,GAAApB,CAAA,GAAAF,CAAA,EAAAM,CAAA,CAAAT,GAAA,KAAAM,CAAA,qBAAA1B,KAAA,EAAA6B,CAAA,CAAAT,GAAA,EAAAyB,IAAA,EAAAjD,CAAA,CAAAiD,IAAA,kBAAAhB,CAAA,CAAAV,IAAA,KAAArB,CAAA,GAAA2B,CAAA,EAAA7B,CAAA,CAAAkD,MAAA,YAAAlD,CAAA,CAAAwB,GAAA,GAAAS,CAAA,CAAAT,GAAA,mBAAA4B,oBAAAzD,CAAA,EAAAE,CAAA,QAAAG,CAAA,GAAAH,CAAA,CAAAqD,MAAA,EAAAhD,CAAA,GAAAP,CAAA,CAAAa,QAAA,CAAAR,CAAA,OAAAE,CAAA,KAAAN,CAAA,SAAAC,CAAA,CAAAsD,QAAA,qBAAAnD,CAAA,IAAAL,CAAA,CAAAa,QAAA,CAAAiD,MAAA,KAAA5D,CAAA,CAAAqD,MAAA,aAAArD,CAAA,CAAA2B,GAAA,GAAA5B,CAAA,EAAAwD,mBAAA,CAAAzD,CAAA,EAAAE,CAAA,eAAAA,CAAA,CAAAqD,MAAA,kBAAAlD,CAAA,KAAAH,CAAA,CAAAqD,MAAA,YAAArD,CAAA,CAAA2B,GAAA,OAAAkC,SAAA,uCAAA1D,CAAA,iBAAA8B,CAAA,MAAAzB,CAAA,GAAAiB,QAAA,CAAApB,CAAA,EAAAP,CAAA,CAAAa,QAAA,EAAAX,CAAA,CAAA2B,GAAA,mBAAAnB,CAAA,CAAAkB,IAAA,SAAA1B,CAAA,CAAAqD,MAAA,YAAArD,CAAA,CAAA2B,GAAA,GAAAnB,CAAA,CAAAmB,GAAA,EAAA3B,CAAA,CAAAsD,QAAA,SAAArB,CAAA,MAAAvB,CAAA,GAAAF,CAAA,CAAAmB,GAAA,SAAAjB,CAAA,GAAAA,CAAA,CAAA0C,IAAA,IAAApD,CAAA,CAAAF,CAAA,CAAAgE,UAAA,IAAApD,CAAA,CAAAH,KAAA,EAAAP,CAAA,CAAA+D,IAAA,GAAAjE,CAAA,CAAAkE,OAAA,eAAAhE,CAAA,CAAAqD,MAAA,KAAArD,CAAA,CAAAqD,MAAA,WAAArD,CAAA,CAAA2B,GAAA,GAAA5B,CAAA,GAAAC,CAAA,CAAAsD,QAAA,SAAArB,CAAA,IAAAvB,CAAA,IAAAV,CAAA,CAAAqD,MAAA,YAAArD,CAAA,CAAA2B,GAAA,OAAAkC,SAAA,sCAAA7D,CAAA,CAAAsD,QAAA,SAAArB,CAAA,cAAAgC,aAAAlE,CAAA,QAAAD,CAAA,KAAAoE,MAAA,EAAAnE,CAAA,YAAAA,CAAA,KAAAD,CAAA,CAAAqE,QAAA,GAAApE,CAAA,WAAAA,CAAA,KAAAD,CAAA,CAAAsE,UAAA,GAAArE,CAAA,KAAAD,CAAA,CAAAuE,QAAA,GAAAtE,CAAA,WAAAuE,UAAA,CAAAC,IAAA,CAAAzE,CAAA,cAAA0E,cAAAzE,CAAA,QAAAD,CAAA,GAAAC,CAAA,CAAA0E,UAAA,QAAA3E,CAAA,CAAA4B,IAAA,oBAAA5B,CAAA,CAAA6B,GAAA,EAAA5B,CAAA,CAAA0E,UAAA,GAAA3E,CAAA,aAAAyB,QAAAxB,CAAA,SAAAuE,UAAA,MAAAJ,MAAA,aAAAnE,CAAA,CAAA4C,OAAA,CAAAsB,YAAA,cAAAS,KAAA,iBAAAlC,OAAA1C,CAAA,QAAAA,CAAA,WAAAA,CAAA,QAAAE,CAAA,GAAAF,CAAA,CAAAY,CAAA,OAAAV,CAAA,SAAAA,CAAA,CAAA4B,IAAA,CAAA9B,CAAA,4BAAAA,CAAA,CAAAiE,IAAA,SAAAjE,CAAA,OAAA6E,KAAA,CAAA7E,CAAA,CAAA8E,MAAA,SAAAvE,CAAA,OAAAG,CAAA,YAAAuD,KAAA,aAAA1D,CAAA,GAAAP,CAAA,CAAA8E,MAAA,OAAAzE,CAAA,CAAAyB,IAAA,CAAA9B,CAAA,EAAAO,CAAA,UAAA0D,IAAA,CAAAxD,KAAA,GAAAT,CAAA,CAAAO,CAAA,GAAA0D,IAAA,CAAAX,IAAA,OAAAW,IAAA,SAAAA,IAAA,CAAAxD,KAAA,GAAAR,CAAA,EAAAgE,IAAA,CAAAX,IAAA,OAAAW,IAAA,YAAAvD,CAAA,CAAAuD,IAAA,GAAAvD,CAAA,gBAAAqD,SAAA,QAAA/D,CAAA,iCAAAoC,iBAAA,CAAAhC,SAAA,GAAAiC,0BAAA,EAAA9B,CAAA,CAAAoC,CAAA,mBAAAlC,KAAA,EAAA4B,0BAAA,EAAAjB,YAAA,SAAAb,CAAA,CAAA8B,0BAAA,mBAAA5B,KAAA,EAAA2B,iBAAA,EAAAhB,YAAA,SAAAgB,iBAAA,CAAA2C,WAAA,GAAA7D,MAAA,CAAAmB,0BAAA,EAAArB,CAAA,wBAAAhB,CAAA,CAAAgF,mBAAA,aAAA/E,CAAA,QAAAD,CAAA,wBAAAC,CAAA,IAAAA,CAAA,CAAAgF,WAAA,WAAAjF,CAAA,KAAAA,CAAA,KAAAoC,iBAAA,6BAAApC,CAAA,CAAA+E,WAAA,IAAA/E,CAAA,CAAAkF,IAAA,OAAAlF,CAAA,CAAAmF,IAAA,aAAAlF,CAAA,WAAAE,MAAA,CAAAiF,cAAA,GAAAjF,MAAA,CAAAiF,cAAA,CAAAnF,CAAA,EAAAoC,0BAAA,KAAApC,CAAA,CAAAoF,SAAA,GAAAhD,0BAAA,EAAAnB,MAAA,CAAAjB,CAAA,EAAAe,CAAA,yBAAAf,CAAA,CAAAG,SAAA,GAAAD,MAAA,CAAAqB,MAAA,CAAAmB,CAAA,GAAA1C,CAAA,KAAAD,CAAA,CAAAsF,KAAA,aAAArF,CAAA,aAAAiD,OAAA,EAAAjD,CAAA,OAAA2C,qBAAA,CAAAG,aAAA,CAAA3C,SAAA,GAAAc,MAAA,CAAA6B,aAAA,CAAA3C,SAAA,EAAAU,CAAA,iCAAAd,CAAA,CAAA+C,aAAA,GAAAA,aAAA,EAAA/C,CAAA,CAAAuF,KAAA,aAAAtF,CAAA,EAAAC,CAAA,EAAAG,CAAA,EAAAE,CAAA,EAAAG,CAAA,eAAAA,CAAA,KAAAA,CAAA,GAAA8E,OAAA,OAAA5E,CAAA,OAAAmC,aAAA,CAAAzB,IAAA,CAAArB,CAAA,EAAAC,CAAA,EAAAG,CAAA,EAAAE,CAAA,GAAAG,CAAA,UAAAV,CAAA,CAAAgF,mBAAA,CAAA9E,CAAA,IAAAU,CAAA,GAAAA,CAAA,CAAAqD,IAAA,GAAAd,IAAA,WAAAlD,CAAA,WAAAA,CAAA,CAAAqD,IAAA,GAAArD,CAAA,CAAAQ,KAAA,GAAAG,CAAA,CAAAqD,IAAA,WAAArB,qBAAA,CAAAD,CAAA,GAAAzB,MAAA,CAAAyB,CAAA,EAAA3B,CAAA,gBAAAE,MAAA,CAAAyB,CAAA,EAAA/B,CAAA,iCAAAM,MAAA,CAAAyB,CAAA,6DAAA3C,CAAA,CAAAyF,IAAA,aAAAxF,CAAA,QAAAD,CAAA,GAAAG,MAAA,CAAAF,CAAA,GAAAC,CAAA,gBAAAG,CAAA,IAAAL,CAAA,EAAAE,CAAA,CAAAuE,IAAA,CAAApE,CAAA,UAAAH,CAAA,CAAAwF,OAAA,aAAAzB,KAAA,WAAA/D,CAAA,CAAA4E,MAAA,SAAA7E,CAAA,GAAAC,CAAA,CAAAyF,GAAA,QAAA1F,CAAA,IAAAD,CAAA,SAAAiE,IAAA,CAAAxD,KAAA,GAAAR,CAAA,EAAAgE,IAAA,CAAAX,IAAA,OAAAW,IAAA,WAAAA,IAAA,CAAAX,IAAA,OAAAW,IAAA,QAAAjE,CAAA,CAAA0C,MAAA,GAAAA,MAAA,EAAAjB,OAAA,CAAArB,SAAA,KAAA6E,WAAA,EAAAxD,OAAA,EAAAmD,KAAA,WAAAA,MAAA5E,CAAA,aAAA4F,IAAA,WAAA3B,IAAA,WAAAP,IAAA,QAAAC,KAAA,GAAA1D,CAAA,OAAAqD,IAAA,YAAAE,QAAA,cAAAD,MAAA,gBAAA1B,GAAA,GAAA5B,CAAA,OAAAuE,UAAA,CAAA3B,OAAA,CAAA6B,aAAA,IAAA1E,CAAA,WAAAE,CAAA,kBAAAA,CAAA,CAAA2F,MAAA,OAAAxF,CAAA,CAAAyB,IAAA,OAAA5B,CAAA,MAAA2E,KAAA,EAAA3E,CAAA,CAAA4F,KAAA,cAAA5F,CAAA,IAAAD,CAAA,MAAA8F,IAAA,WAAAA,KAAA,SAAAzC,IAAA,WAAArD,CAAA,QAAAuE,UAAA,IAAAG,UAAA,kBAAA1E,CAAA,CAAA2B,IAAA,QAAA3B,CAAA,CAAA4B,GAAA,cAAAmE,IAAA,KAAApC,iBAAA,WAAAA,kBAAA5D,CAAA,aAAAsD,IAAA,QAAAtD,CAAA,MAAAE,CAAA,kBAAA+F,OAAA5F,CAAA,EAAAE,CAAA,WAAAK,CAAA,CAAAgB,IAAA,YAAAhB,CAAA,CAAAiB,GAAA,GAAA7B,CAAA,EAAAE,CAAA,CAAA+D,IAAA,GAAA5D,CAAA,EAAAE,CAAA,KAAAL,CAAA,CAAAqD,MAAA,WAAArD,CAAA,CAAA2B,GAAA,GAAA5B,CAAA,KAAAM,CAAA,aAAAA,CAAA,QAAAiE,UAAA,CAAAM,MAAA,MAAAvE,CAAA,SAAAA,CAAA,QAAAG,CAAA,QAAA8D,UAAA,CAAAjE,CAAA,GAAAK,CAAA,GAAAF,CAAA,CAAAiE,UAAA,iBAAAjE,CAAA,CAAA0D,MAAA,SAAA6B,MAAA,aAAAvF,CAAA,CAAA0D,MAAA,SAAAwB,IAAA,QAAA9E,CAAA,GAAAT,CAAA,CAAAyB,IAAA,CAAApB,CAAA,eAAAM,CAAA,GAAAX,CAAA,CAAAyB,IAAA,CAAApB,CAAA,qBAAAI,CAAA,IAAAE,CAAA,aAAA4E,IAAA,GAAAlF,CAAA,CAAA2D,QAAA,SAAA4B,MAAA,CAAAvF,CAAA,CAAA2D,QAAA,gBAAAuB,IAAA,GAAAlF,CAAA,CAAA4D,UAAA,SAAA2B,MAAA,CAAAvF,CAAA,CAAA4D,UAAA,cAAAxD,CAAA,aAAA8E,IAAA,GAAAlF,CAAA,CAAA2D,QAAA,SAAA4B,MAAA,CAAAvF,CAAA,CAAA2D,QAAA,qBAAArD,CAAA,QAAAqC,KAAA,qDAAAuC,IAAA,GAAAlF,CAAA,CAAA4D,UAAA,SAAA2B,MAAA,CAAAvF,CAAA,CAAA4D,UAAA,YAAAT,MAAA,WAAAA,OAAA5D,CAAA,EAAAD,CAAA,aAAAE,CAAA,QAAAsE,UAAA,CAAAM,MAAA,MAAA5E,CAAA,SAAAA,CAAA,QAAAK,CAAA,QAAAiE,UAAA,CAAAtE,CAAA,OAAAK,CAAA,CAAA6D,MAAA,SAAAwB,IAAA,IAAAvF,CAAA,CAAAyB,IAAA,CAAAvB,CAAA,wBAAAqF,IAAA,GAAArF,CAAA,CAAA+D,UAAA,QAAA5D,CAAA,GAAAH,CAAA,aAAAG,CAAA,iBAAAT,CAAA,mBAAAA,CAAA,KAAAS,CAAA,CAAA0D,MAAA,IAAApE,CAAA,IAAAA,CAAA,IAAAU,CAAA,CAAA4D,UAAA,KAAA5D,CAAA,cAAAE,CAAA,GAAAF,CAAA,GAAAA,CAAA,CAAAiE,UAAA,cAAA/D,CAAA,CAAAgB,IAAA,GAAA3B,CAAA,EAAAW,CAAA,CAAAiB,GAAA,GAAA7B,CAAA,EAAAU,CAAA,SAAA6C,MAAA,gBAAAU,IAAA,GAAAvD,CAAA,CAAA4D,UAAA,EAAAnC,CAAA,SAAA+D,QAAA,CAAAtF,CAAA,MAAAsF,QAAA,WAAAA,SAAAjG,CAAA,EAAAD,CAAA,oBAAAC,CAAA,CAAA2B,IAAA,QAAA3B,CAAA,CAAA4B,GAAA,qBAAA5B,CAAA,CAAA2B,IAAA,mBAAA3B,CAAA,CAAA2B,IAAA,QAAAqC,IAAA,GAAAhE,CAAA,CAAA4B,GAAA,gBAAA5B,CAAA,CAAA2B,IAAA,SAAAoE,IAAA,QAAAnE,GAAA,GAAA5B,CAAA,CAAA4B,GAAA,OAAA0B,MAAA,kBAAAU,IAAA,yBAAAhE,CAAA,CAAA2B,IAAA,IAAA5B,CAAA,UAAAiE,IAAA,GAAAjE,CAAA,GAAAmC,CAAA,KAAAgE,MAAA,WAAAA,OAAAlG,CAAA,aAAAD,CAAA,QAAAwE,UAAA,CAAAM,MAAA,MAAA9E,CAAA,SAAAA,CAAA,QAAAE,CAAA,QAAAsE,UAAA,CAAAxE,CAAA,OAAAE,CAAA,CAAAoE,UAAA,KAAArE,CAAA,cAAAiG,QAAA,CAAAhG,CAAA,CAAAyE,UAAA,EAAAzE,CAAA,CAAAqE,QAAA,GAAAG,aAAA,CAAAxE,CAAA,GAAAiC,CAAA,OAAAiE,KAAA,WAAAC,OAAApG,CAAA,aAAAD,CAAA,QAAAwE,UAAA,CAAAM,MAAA,MAAA9E,CAAA,SAAAA,CAAA,QAAAE,CAAA,QAAAsE,UAAA,CAAAxE,CAAA,OAAAE,CAAA,CAAAkE,MAAA,KAAAnE,CAAA,QAAAI,CAAA,GAAAH,CAAA,CAAAyE,UAAA,kBAAAtE,CAAA,CAAAuB,IAAA,QAAArB,CAAA,GAAAF,CAAA,CAAAwB,GAAA,EAAA6C,aAAA,CAAAxE,CAAA,YAAAK,CAAA,YAAA8C,KAAA,8BAAAiD,aAAA,WAAAA,cAAAtG,CAAA,EAAAE,CAAA,EAAAG,CAAA,gBAAAmD,QAAA,KAAA3C,QAAA,EAAA6B,MAAA,CAAA1C,CAAA,GAAAgE,UAAA,EAAA9D,CAAA,EAAAgE,OAAA,EAAA7D,CAAA,oBAAAkD,MAAA,UAAA1B,GAAA,GAAA5B,CAAA,GAAAkC,CAAA,OAAAnC,CAAA;AAAA,SAAAuG,QAAAvG,CAAA,EAAAE,CAAA,QAAAD,CAAA,GAAAE,MAAA,CAAAsF,IAAA,CAAAzF,CAAA,OAAAG,MAAA,CAAAqG,qBAAA,QAAAjG,CAAA,GAAAJ,MAAA,CAAAqG,qBAAA,CAAAxG,CAAA,GAAAE,CAAA,KAAAK,CAAA,GAAAA,CAAA,CAAAkG,MAAA,WAAAvG,CAAA,WAAAC,MAAA,CAAAuG,wBAAA,CAAA1G,CAAA,EAAAE,CAAA,EAAAiB,UAAA,OAAAlB,CAAA,CAAAwE,IAAA,CAAAkC,KAAA,CAAA1G,CAAA,EAAAM,CAAA,YAAAN,CAAA;AAAA,SAAA2G,cAAA5G,CAAA,aAAAE,CAAA,MAAAA,CAAA,GAAA2G,SAAA,CAAA/B,MAAA,EAAA5E,CAAA,UAAAD,CAAA,WAAA4G,SAAA,CAAA3G,CAAA,IAAA2G,SAAA,CAAA3G,CAAA,QAAAA,CAAA,OAAAqG,OAAA,CAAApG,MAAA,CAAAF,CAAA,OAAA4C,OAAA,WAAA3C,CAAA,IAAA4G,eAAA,CAAA9G,CAAA,EAAAE,CAAA,EAAAD,CAAA,CAAAC,CAAA,SAAAC,MAAA,CAAA4G,yBAAA,GAAA5G,MAAA,CAAA6G,gBAAA,CAAAhH,CAAA,EAAAG,MAAA,CAAA4G,yBAAA,CAAA9G,CAAA,KAAAsG,OAAA,CAAApG,MAAA,CAAAF,CAAA,GAAA4C,OAAA,WAAA3C,CAAA,IAAAC,MAAA,CAAAK,cAAA,CAAAR,CAAA,EAAAE,CAAA,EAAAC,MAAA,CAAAuG,wBAAA,CAAAzG,CAAA,EAAAC,CAAA,iBAAAF,CAAA;AAAA,SAAA8G,gBAAA9G,CAAA,EAAAE,CAAA,EAAAD,CAAA,YAAAC,CAAA,GAAA+G,cAAA,CAAA/G,CAAA,MAAAF,CAAA,GAAAG,MAAA,CAAAK,cAAA,CAAAR,CAAA,EAAAE,CAAA,IAAAO,KAAA,EAAAR,CAAA,EAAAkB,UAAA,MAAAC,YAAA,MAAAC,QAAA,UAAArB,CAAA,CAAAE,CAAA,IAAAD,CAAA,EAAAD,CAAA;AAAA,SAAAiH,eAAAhH,CAAA,QAAAS,CAAA,GAAAwG,YAAA,CAAAjH,CAAA,uCAAAS,CAAA,GAAAA,CAAA,GAAAA,CAAA;AAAA,SAAAwG,aAAAjH,CAAA,EAAAC,CAAA,2BAAAD,CAAA,KAAAA,CAAA,SAAAA,CAAA,MAAAD,CAAA,GAAAC,CAAA,CAAAU,MAAA,CAAAwG,WAAA,kBAAAnH,CAAA,QAAAU,CAAA,GAAAV,CAAA,CAAA8B,IAAA,CAAA7B,CAAA,EAAAC,CAAA,uCAAAQ,CAAA,SAAAA,CAAA,YAAAqD,SAAA,yEAAA7D,CAAA,GAAAkH,MAAA,GAAAC,MAAA,EAAApH,CAAA;AAAA,SAAAqH,mBAAAjH,CAAA,EAAAJ,CAAA,EAAAD,CAAA,EAAAE,CAAA,EAAAK,CAAA,EAAAK,CAAA,EAAAE,CAAA,cAAAJ,CAAA,GAAAL,CAAA,CAAAO,CAAA,EAAAE,CAAA,GAAAE,CAAA,GAAAN,CAAA,CAAAD,KAAA,WAAAJ,CAAA,gBAAAL,CAAA,CAAAK,CAAA,KAAAK,CAAA,CAAA4C,IAAA,GAAArD,CAAA,CAAAe,CAAA,IAAAwE,OAAA,CAAAvC,OAAA,CAAAjC,CAAA,EAAAmC,IAAA,CAAAjD,CAAA,EAAAK,CAAA;AAAA,SAAAgH,kBAAAlH,CAAA,6BAAAJ,CAAA,SAAAD,CAAA,GAAA6G,SAAA,aAAArB,OAAA,WAAAtF,CAAA,EAAAK,CAAA,QAAAK,CAAA,GAAAP,CAAA,CAAAsG,KAAA,CAAA1G,CAAA,EAAAD,CAAA,YAAAwH,MAAAnH,CAAA,IAAAiH,kBAAA,CAAA1G,CAAA,EAAAV,CAAA,EAAAK,CAAA,EAAAiH,KAAA,EAAAC,MAAA,UAAApH,CAAA,cAAAoH,OAAApH,CAAA,IAAAiH,kBAAA,CAAA1G,CAAA,EAAAV,CAAA,EAAAK,CAAA,EAAAiH,KAAA,EAAAC,MAAA,WAAApH,CAAA,KAAAmH,KAAA;AADA,SAASE,WAAW,EAAEC,gBAAgB,EAAEC,aAAa,EAAEC,OAAO,EAAEC,YAAY,EAAEC,QAAQ,EAAEC,gBAAgB,EAAEC,gBAAgB,EAAEC,GAAG,EAAEC,KAAK,EAAEC,UAAU,EAAEC,iBAAiB,EAAEC,6BAA6B,EAAEC,oBAAoB,EAAEC,qBAAqB,EAAEC,yBAAyB,EAAEC,KAAK,EAAEC,GAAG,EAAEC,eAAe,QAAQ,oBAAoB;AACrU,SAASX,gBAAgB,QAAQ,oBAAoB;AAErD,SAASY,0BAA0BA,CAACC,IAAI,EAAEC,SAAS,EAAEC,GAAG,EAAE;EACxD,IACEC,SAAS,GAEPH,IAAI,CAFNG,SAAS;IACTC,QAAQ,GACNJ,IAAI,CADNI,QAAQ;EAEV,IAAMC,QAAQ,GAAGzB,WAAW,CAACqB,SAAS,CAAC;EACvC,IAAMK,aAAa,GAAGzB,gBAAgB,CAACoB,SAAS,CAAC;EACjD,IAAMM,WAAW,GAAGzB,aAAa,CAACwB,aAAa,CAAC;EAChD,IAAME,IAAI,GAAGzB,OAAO,CAACkB,SAAS,CAAC;EAC/B,IAAMQ,UAAU,GAAGJ,QAAQ,KAAK,GAAG;EACnC,IAAMK,OAAO,GAAGP,SAAS,CAACQ,CAAC,GAAGR,SAAS,CAACS,KAAK,GAAG,CAAC,GAAGR,QAAQ,CAACQ,KAAK,GAAG,CAAC;EACtE,IAAMC,OAAO,GAAGV,SAAS,CAAC9G,CAAC,GAAG8G,SAAS,CAACW,MAAM,GAAG,CAAC,GAAGV,QAAQ,CAACU,MAAM,GAAG,CAAC;EACxE,IAAMC,WAAW,GAAGZ,SAAS,CAACI,WAAW,CAAC,GAAG,CAAC,GAAGH,QAAQ,CAACG,WAAW,CAAC,GAAG,CAAC;EAC1E,IAAIS,MAAM;EACV,QAAQR,IAAI;IACV,KAAK,KAAK;MACRQ,MAAM,GAAG;QACPL,CAAC,EAAED,OAAO;QACVrH,CAAC,EAAE8G,SAAS,CAAC9G,CAAC,GAAG+G,QAAQ,CAACU;MAC5B,CAAC;MACD;IACF,KAAK,QAAQ;MACXE,MAAM,GAAG;QACPL,CAAC,EAAED,OAAO;QACVrH,CAAC,EAAE8G,SAAS,CAAC9G,CAAC,GAAG8G,SAAS,CAACW;MAC7B,CAAC;MACD;IACF,KAAK,OAAO;MACVE,MAAM,GAAG;QACPL,CAAC,EAAER,SAAS,CAACQ,CAAC,GAAGR,SAAS,CAACS,KAAK;QAChCvH,CAAC,EAAEwH;MACL,CAAC;MACD;IACF,KAAK,MAAM;MACTG,MAAM,GAAG;QACPL,CAAC,EAAER,SAAS,CAACQ,CAAC,GAAGP,QAAQ,CAACQ,KAAK;QAC/BvH,CAAC,EAAEwH;MACL,CAAC;MACD;IACF;MACEG,MAAM,GAAG;QACPL,CAAC,EAAER,SAAS,CAACQ,CAAC;QACdtH,CAAC,EAAE8G,SAAS,CAAC9G;MACf,CAAC;EACL;EACA,QAAQ2F,YAAY,CAACiB,SAAS,CAAC;IAC7B,KAAK,OAAO;MACVe,MAAM,CAACV,aAAa,CAAC,IAAIS,WAAW,IAAIb,GAAG,IAAIO,UAAU,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;MACnE;IACF,KAAK,KAAK;MACRO,MAAM,CAACV,aAAa,CAAC,IAAIS,WAAW,IAAIb,GAAG,IAAIO,UAAU,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;MACnE;EACJ;EACA,OAAOO,MAAM;AACf;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAMC,eAAe;EAAA,IAAAC,KAAA,GAAAzC,iBAAA,cAAAxH,mBAAA,GAAAoF,IAAA,CAAG,SAAA8E,QAAOhB,SAAS,EAAEC,QAAQ,EAAEgB,MAAM;IAAA,IAAAC,iBAAA,EAAApB,SAAA,EAAAqB,gBAAA,EAAAC,QAAA,EAAAC,kBAAA,EAAAC,UAAA,EAAAC,QAAA,EAAAC,eAAA,EAAAzB,GAAA,EAAA0B,KAAA,EAAAC,qBAAA,EAAAlB,CAAA,EAAAtH,CAAA,EAAAyI,iBAAA,EAAAC,cAAA,EAAAC,UAAA,EAAApK,CAAA,EAAAqK,kBAAA,EAAA7F,IAAA,EAAA8F,EAAA,EAAAC,SAAA,EAAAC,KAAA,EAAAC,KAAA,EAAAC,IAAA,EAAAxG,KAAA,EAAAyG,sBAAA;IAAA,OAAAtL,mBAAA,GAAAuB,IAAA,UAAAgK,SAAAC,QAAA;MAAA,kBAAAA,QAAA,CAAA3F,IAAA,GAAA2F,QAAA,CAAAtH,IAAA;QAAA;UAAAkG,iBAAA,GAMpDD,MAAM,CAJRnB,SAAS,EAATA,SAAS,GAAAoB,iBAAA,cAAG,QAAQ,GAAAA,iBAAA,EAAAC,gBAAA,GAIlBF,MAAM,CAHRG,QAAQ,EAARA,QAAQ,GAAAD,gBAAA,cAAG,UAAU,GAAAA,gBAAA,EAAAE,kBAAA,GAGnBJ,MAAM,CAFRK,UAAU,EAAVA,UAAU,GAAAD,kBAAA,cAAG,EAAE,GAAAA,kBAAA,EACfE,QAAQ,GACNN,MAAM,CADRM,QAAQ;UAEJC,eAAe,GAAGF,UAAU,CAAC9D,MAAM,CAAC+E,OAAO,CAAC;UAAAD,QAAA,CAAAtH,IAAA;UAAA,OAC/BuG,QAAQ,CAACiB,KAAK,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGjB,QAAQ,CAACiB,KAAK,CAACvC,QAAQ,CAAC;QAAA;UAAvEF,GAAG,GAAAuC,QAAA,CAAA7H,IAAA;UAAA6H,QAAA,CAAAtH,IAAA;UAAA,OACSuG,QAAQ,CAACkB,eAAe,CAAC;YACzCzC,SAAS;YACTC,QAAQ;YACRmB;UACF,CAAC,CAAC;QAAA;UAJEK,KAAK,GAAAa,QAAA,CAAA7H,IAAA;UAAAiH,qBAAA,GAQL9B,0BAA0B,CAAC6B,KAAK,EAAE3B,SAAS,EAAEC,GAAG,CAAC,EAFnDS,CAAC,GAAAkB,qBAAA,CAADlB,CAAC,EACDtH,CAAC,GAAAwI,qBAAA,CAADxI,CAAC;UAECyI,iBAAiB,GAAG7B,SAAS;UAC7B8B,cAAc,GAAG,CAAC,CAAC;UACnBC,UAAU,GAAG,CAAC;UACTpK,CAAC,GAAG,CAAC;QAAA;UAAA,MAAEA,CAAC,GAAG+J,eAAe,CAAC3F,MAAM;YAAAyG,QAAA,CAAAtH,IAAA;YAAA;UAAA;UAAA8G,kBAAA,GAIpCN,eAAe,CAAC/J,CAAC,CAAC,EAFpBwE,IAAI,GAAA6F,kBAAA,CAAJ7F,IAAI,EACJ8F,EAAE,GAAAD,kBAAA,CAAFC,EAAE;UAAAO,QAAA,CAAAtH,IAAA;UAAA,OAOM+G,EAAE,CAAC;YACXvB,CAAC;YACDtH,CAAC;YACDwJ,gBAAgB,EAAE5C,SAAS;YAC3BA,SAAS,EAAE6B,iBAAiB;YAC5BP,QAAQ;YACRQ,cAAc;YACdH,KAAK;YACLF,QAAQ;YACRoB,QAAQ,EAAE;cACR3C,SAAS;cACTC;YACF;UACF,CAAC,CAAC;QAAA;UAAA+B,SAAA,GAAAM,QAAA,CAAA7H,IAAA;UAjBGwH,KAAK,GAAAD,SAAA,CAARxB,CAAC;UACE0B,KAAK,GAAAF,SAAA,CAAR9I,CAAC;UACDiJ,IAAI,GAAAH,SAAA,CAAJG,IAAI;UACJxG,KAAK,GAAAqG,SAAA,CAALrG,KAAK;UAeP6E,CAAC,GAAGyB,KAAK,IAAI,IAAI,GAAGA,KAAK,GAAGzB,CAAC;UAC7BtH,CAAC,GAAGgJ,KAAK,IAAI,IAAI,GAAGA,KAAK,GAAGhJ,CAAC;UAC7B0I,cAAc,GAAAjE,aAAA,CAAAA,aAAA,KACTiE,cAAc;YACjB,CAAC3F,IAAI,GAAA0B,aAAA,CAAAA,aAAA,KACAiE,cAAc,CAAC3F,IAAI,CAAC,GACpBkG,IAAI;UACR,EACF;UAAC,MACExG,KAAK,IAAIkG,UAAU,IAAI,EAAE;YAAAS,QAAA,CAAAtH,IAAA;YAAA;UAAA;UAC3B6G,UAAU,EAAE;UAAC,MACT,OAAOlG,KAAK,KAAK,QAAQ;YAAA2G,QAAA,CAAAtH,IAAA;YAAA;UAAA;UAC3B,IAAIW,KAAK,CAACmE,SAAS,EAAE;YACnB6B,iBAAiB,GAAGhG,KAAK,CAACmE,SAAS;UACrC;UAAC,KACGnE,KAAK,CAAC8F,KAAK;YAAAa,QAAA,CAAAtH,IAAA;YAAA;UAAA;UAAA,MACLW,KAAK,CAAC8F,KAAK,KAAK,IAAI;YAAAa,QAAA,CAAAtH,IAAA;YAAA;UAAA;UAAAsH,QAAA,CAAAtH,IAAA;UAAA,OAASuG,QAAQ,CAACkB,eAAe,CAAC;YAC5DzC,SAAS;YACTC,QAAQ;YACRmB;UACF,CAAC,CAAC;QAAA;UAAAkB,QAAA,CAAAM,EAAA,GAAAN,QAAA,CAAA7H,IAAA;UAAA6H,QAAA,CAAAtH,IAAA;UAAA;QAAA;UAAAsH,QAAA,CAAAM,EAAA,GAAGjH,KAAK,CAAC8F,KAAK;QAAA;UAJhBA,KAAK,GAAAa,QAAA,CAAAM,EAAA;QAAA;UAAAR,sBAAA,GASHxC,0BAA0B,CAAC6B,KAAK,EAAEE,iBAAiB,EAAE5B,GAAG,CAAC;UAF3DS,CAAC,GAAA4B,sBAAA,CAAD5B,CAAC;UACDtH,CAAC,GAAAkJ,sBAAA,CAADlJ,CAAC;QAAA;UAGLzB,CAAC,GAAG,CAAC,CAAC;QAAC;UAnDiCA,CAAC,EAAE;UAAA6K,QAAA,CAAAtH,IAAA;UAAA;QAAA;UAAA,OAAAsH,QAAA,CAAA1H,MAAA,WAsDxC;YACL4F,CAAC;YACDtH,CAAC;YACD4G,SAAS,EAAE6B,iBAAiB;YAC5BP,QAAQ;YACRQ;UACF,CAAC;QAAA;QAAA;UAAA,OAAAU,QAAA,CAAAxF,IAAA;MAAA;IAAA,GAAAkE,OAAA;EAAA,CACF;EAAA,gBAlFKF,eAAeA,CAAA+B,EAAA,EAAAC,GAAA,EAAAC,GAAA;IAAA,OAAAhC,KAAA,CAAArD,KAAA,OAAAE,SAAA;EAAA;AAAA,GAkFpB;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAPA,SAQeoF,cAAcA,CAAAC,GAAA,EAAAC,GAAA;EAAA,OAAAC,eAAA,CAAAzF,KAAA,OAAAE,SAAA;AAAA;AAyD7B;AACA;AACA;AACA;AACA;AAJA,SAAAuF,gBAAA;EAAAA,eAAA,GAAA7E,iBAAA,cAAAxH,mBAAA,GAAAoF,IAAA,CAzDA,SAAAkH,UAA8BC,KAAK,EAAEC,OAAO;IAAA,IAAAC,qBAAA,EAAA/C,CAAA,EAAAtH,CAAA,EAAAqI,QAAA,EAAAE,KAAA,EAAAkB,QAAA,EAAAvB,QAAA,EAAAoC,UAAA,EAAAC,mBAAA,EAAAC,QAAA,EAAAC,qBAAA,EAAAC,YAAA,EAAAC,qBAAA,EAAAC,cAAA,EAAAC,qBAAA,EAAAC,WAAA,EAAAC,kBAAA,EAAAC,OAAA,EAAAC,aAAA,EAAAC,UAAA,EAAAC,OAAA,EAAAC,kBAAA,EAAAC,IAAA,EAAAC,YAAA,EAAAC,WAAA,EAAAC,iBAAA;IAAA,OAAA5N,mBAAA,GAAAuB,IAAA,UAAAsM,WAAAC,UAAA;MAAA,kBAAAA,UAAA,CAAAjI,IAAA,GAAAiI,UAAA,CAAA5J,IAAA;QAAA;UAE1C,IAAIsI,OAAO,KAAK,KAAK,CAAC,EAAE;YACtBA,OAAO,GAAG,CAAC,CAAC;UACd;UAEE9C,CAAC,GAMC6C,KAAK,CANP7C,CAAC,EACDtH,CAAC,GAKCmK,KAAK,CALPnK,CAAC,EACDqI,QAAQ,GAIN8B,KAAK,CAJP9B,QAAQ,EACRE,KAAK,GAGH4B,KAAK,CAHP5B,KAAK,EACLkB,QAAQ,GAENU,KAAK,CAFPV,QAAQ,EACRvB,QAAQ,GACNiC,KAAK,CADPjC,QAAQ;UAAAoC,UAAA,GAQN1E,QAAQ,CAACwE,OAAO,EAAED,KAAK,CAAC,EAAAI,mBAAA,GAAAD,UAAA,CAL1BE,QAAQ,EAARA,QAAQ,GAAAD,mBAAA,cAAG,mBAAmB,GAAAA,mBAAA,EAAAE,qBAAA,GAAAH,UAAA,CAC9BI,YAAY,EAAZA,YAAY,GAAAD,qBAAA,cAAG,UAAU,GAAAA,qBAAA,EAAAE,qBAAA,GAAAL,UAAA,CACzBM,cAAc,EAAdA,cAAc,GAAAD,qBAAA,cAAG,UAAU,GAAAA,qBAAA,EAAAE,qBAAA,GAAAP,UAAA,CAC3BQ,WAAW,EAAXA,WAAW,GAAAD,qBAAA,cAAG,KAAK,GAAAA,qBAAA,EAAAE,kBAAA,GAAAT,UAAA,CACnBU,OAAO,EAAPA,OAAO,GAAAD,kBAAA,cAAG,CAAC,GAAAA,kBAAA;UAEPE,aAAa,GAAGpF,gBAAgB,CAACmF,OAAO,CAAC;UACzCE,UAAU,GAAGN,cAAc,KAAK,UAAU,GAAG,WAAW,GAAG,UAAU;UACrEO,OAAO,GAAG1B,QAAQ,CAACqB,WAAW,GAAGI,UAAU,GAAGN,cAAc,CAAC;UAAAc,UAAA,CAAAhC,EAAA,GACxC5D,gBAAgB;UAAA4F,UAAA,CAAAC,EAAA,GAAOtD,QAAQ;UAAAqD,UAAA,CAAA5J,IAAA;UAAA,OACduG,QAAQ,CAACuD,SAAS,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGvD,QAAQ,CAACuD,SAAS,CAACT,OAAO,CAAC;QAAA;UAAAO,UAAA,CAAAG,EAAA,GAAhGxB,qBAAqB,GAAAqB,UAAA,CAAAnK,IAAA;UAAA,MAAAmK,UAAA,CAAAG,EAAA,IAAiF,IAAI;YAAAH,UAAA,CAAA5J,IAAA;YAAA;UAAA;UAAA4J,UAAA,CAAAI,EAAA,GAAGzB,qBAAqB;UAAAqB,UAAA,CAAA5J,IAAA;UAAA;QAAA;UAAA4J,UAAA,CAAAI,EAAA,GAAG,IAAI;QAAA;UAAA,KAAAJ,UAAA,CAAAI,EAAA;YAAAJ,UAAA,CAAA5J,IAAA;YAAA;UAAA;UAAA4J,UAAA,CAAAK,EAAA,GAAIZ,OAAO;UAAAO,UAAA,CAAA5J,IAAA;UAAA;QAAA;UAAA4J,UAAA,CAAAM,EAAA,GAAGb,OAAO,CAACc,cAAc;UAAA,IAAAP,UAAA,CAAAM,EAAA;YAAAN,UAAA,CAAA5J,IAAA;YAAA;UAAA;UAAA4J,UAAA,CAAA5J,IAAA;UAAA,OAAYuG,QAAQ,CAAC6D,kBAAkB,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG7D,QAAQ,CAAC6D,kBAAkB,CAACzC,QAAQ,CAAC1C,QAAQ,CAAC;QAAA;UAAA2E,UAAA,CAAAM,EAAA,GAAAN,UAAA,CAAAnK,IAAA;QAAA;UAAAmK,UAAA,CAAAK,EAAA,GAAAL,UAAA,CAAAM,EAAA;QAAA;UAAAN,UAAA,CAAAS,EAAA,GAAAT,UAAA,CAAAK,EAAA;UAAAL,UAAA,CAAAU,EAAA,GACjS5B,QAAQ;UAAAkB,UAAA,CAAAW,EAAA,GACR3B,YAAY;UAAAgB,UAAA,CAAAY,EAAA,GACZpE,QAAQ;UAAAwD,UAAA,CAAAa,GAAA;YAHRpB,OAAO,EAAAO,UAAA,CAAAS,EAAA;YACP3B,QAAQ,EAAAkB,UAAA,CAAAU,EAAA;YACR1B,YAAY,EAAAgB,UAAA,CAAAW,EAAA;YACZnE,QAAQ,EAAAwD,UAAA,CAAAY;UAAA;UAAAZ,UAAA,CAAA5J,IAAA;UAAA,OAAA4J,UAAA,CAAAC,EAAA,CAJiDa,eAAe,CAAA7M,IAAA,CAAA+L,UAAA,CAAAC,EAAA,EAAAD,UAAA,CAAAa,GAAA;QAAA;UAAAb,UAAA,CAAAe,GAAA,GAAAf,UAAA,CAAAnK,IAAA;UAApE6J,kBAAkB,OAAAM,UAAA,CAAAhC,EAAA,EAAAgC,UAAA,CAAAe,GAAA;UAMlBpB,IAAI,GAAGT,cAAc,KAAK,UAAU,GAAG;YAC3CtD,CAAC;YACDtH,CAAC;YACDuH,KAAK,EAAEgB,KAAK,CAACxB,QAAQ,CAACQ,KAAK;YAC3BE,MAAM,EAAEc,KAAK,CAACxB,QAAQ,CAACU;UACzB,CAAC,GAAGc,KAAK,CAACzB,SAAS;UAAA4E,UAAA,CAAA5J,IAAA;UAAA,OACSuG,QAAQ,CAACqE,eAAe,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGrE,QAAQ,CAACqE,eAAe,CAACjD,QAAQ,CAAC1C,QAAQ,CAAC;QAAA;UAA7GuE,YAAY,GAAAI,UAAA,CAAAnK,IAAA;UAAAmK,UAAA,CAAA5J,IAAA;UAAA,OACUuG,QAAQ,CAACuD,SAAS,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGvD,QAAQ,CAACuD,SAAS,CAACN,YAAY,CAAC;QAAA;UAAA,KAAAI,UAAA,CAAAnK,IAAA;YAAAmK,UAAA,CAAA5J,IAAA;YAAA;UAAA;UAAA4J,UAAA,CAAA5J,IAAA;UAAA,OAAauG,QAAQ,CAACsE,QAAQ,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGtE,QAAQ,CAACsE,QAAQ,CAACrB,YAAY,CAAC;QAAA;UAAAI,UAAA,CAAAkB,GAAA,GAAAlB,UAAA,CAAAnK,IAAA;UAAA,IAAAmK,UAAA,CAAAkB,GAAA;YAAAlB,UAAA,CAAA5J,IAAA;YAAA;UAAA;UAAA4J,UAAA,CAAAkB,GAAA,GAAM;YACvLtF,CAAC,EAAE,CAAC;YACJtH,CAAC,EAAE;UACL,CAAC;QAAA;UAAA0L,UAAA,CAAAmB,GAAA,GAAAnB,UAAA,CAAAkB,GAAA;UAAAlB,UAAA,CAAA5J,IAAA;UAAA;QAAA;UAAA4J,UAAA,CAAAmB,GAAA,GAAG;YACFvF,CAAC,EAAE,CAAC;YACJtH,CAAC,EAAE;UACL,CAAC;QAAA;UANKuL,WAAW,GAAAG,UAAA,CAAAmB,GAAA;UAAAnB,UAAA,CAAAoB,GAAA,GAOShH,gBAAgB;UAAA,KAACuC,QAAQ,CAAC0E,qDAAqD;YAAArB,UAAA,CAAA5J,IAAA;YAAA;UAAA;UAAA4J,UAAA,CAAA5J,IAAA;UAAA,OAASuG,QAAQ,CAAC0E,qDAAqD,CAAC;YAC/KtD,QAAQ;YACR4B,IAAI;YACJC,YAAY;YACZpD;UACF,CAAC,CAAC;QAAA;UAAAwD,UAAA,CAAAsB,GAAA,GAAAtB,UAAA,CAAAnK,IAAA;UAAAmK,UAAA,CAAA5J,IAAA;UAAA;QAAA;UAAA4J,UAAA,CAAAsB,GAAA,GAAG3B,IAAI;QAAA;UAAAK,UAAA,CAAAuB,GAAA,GAAAvB,UAAA,CAAAsB,GAAA;UALHxB,iBAAiB,OAAAE,UAAA,CAAAoB,GAAA,EAAApB,UAAA,CAAAuB,GAAA;UAAA,OAAAvB,UAAA,CAAAhK,MAAA,WAMhB;YACLwL,GAAG,EAAE,CAAC9B,kBAAkB,CAAC8B,GAAG,GAAG1B,iBAAiB,CAAC0B,GAAG,GAAGjC,aAAa,CAACiC,GAAG,IAAI3B,WAAW,CAACvL,CAAC;YACzFmN,MAAM,EAAE,CAAC3B,iBAAiB,CAAC2B,MAAM,GAAG/B,kBAAkB,CAAC+B,MAAM,GAAGlC,aAAa,CAACkC,MAAM,IAAI5B,WAAW,CAACvL,CAAC;YACrGoN,IAAI,EAAE,CAAChC,kBAAkB,CAACgC,IAAI,GAAG5B,iBAAiB,CAAC4B,IAAI,GAAGnC,aAAa,CAACmC,IAAI,IAAI7B,WAAW,CAACjE,CAAC;YAC7F+F,KAAK,EAAE,CAAC7B,iBAAiB,CAAC6B,KAAK,GAAGjC,kBAAkB,CAACiC,KAAK,GAAGpC,aAAa,CAACoC,KAAK,IAAI9B,WAAW,CAACjE;UAClG,CAAC;QAAA;QAAA;UAAA,OAAAoE,UAAA,CAAA9H,IAAA;MAAA;IAAA,GAAAsG,SAAA;EAAA,CACF;EAAA,OAAAD,eAAA,CAAAzF,KAAA,OAAAE,SAAA;AAAA;AAOD,IAAM4I,KAAK,GAAG,SAARA,KAAKA,CAAGlD,OAAO;EAAA,OAAK;IACxBrH,IAAI,EAAE,OAAO;IACbqH,OAAO;IACDvB,EAAEA,CAACsB,KAAK,EAAE;MAAA,OAAA/E,iBAAA,cAAAxH,mBAAA,GAAAoF,IAAA,UAAAuK,SAAA;QAAA,IAAAjG,CAAA,EAAAtH,CAAA,EAAA4G,SAAA,EAAA2B,KAAA,EAAAF,QAAA,EAAAoB,QAAA,EAAAf,cAAA,EAAA8E,KAAA,EAAArC,OAAA,EAAAsC,aAAA,EAAAzC,OAAA,EAAAC,aAAA,EAAAtD,MAAA,EAAA+F,IAAA,EAAA/K,MAAA,EAAAgL,eAAA,EAAAC,OAAA,EAAAC,OAAA,EAAAC,OAAA,EAAAC,UAAA,EAAAC,OAAA,EAAAC,SAAA,EAAAC,iBAAA,EAAAC,UAAA,EAAAC,iBAAA,EAAAC,sBAAA,EAAAC,UAAA,EAAAC,UAAA,EAAAC,KAAA,EAAAhI,GAAA,EAAAiI,MAAA,EAAAC,MAAA,EAAAC,eAAA,EAAAC,eAAA;QAAA,OAAAhR,mBAAA,GAAAuB,IAAA,UAAA0P,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAArL,IAAA,GAAAqL,SAAA,CAAAhN,IAAA;YAAA;cAEZwF,CAAC,GAOC6C,KAAK,CAPP7C,CAAC,EACDtH,CAAC,GAMCmK,KAAK,CANPnK,CAAC,EACD4G,SAAS,GAKPuD,KAAK,CALPvD,SAAS,EACT2B,KAAK,GAIH4B,KAAK,CAJP5B,KAAK,EACLF,QAAQ,GAGN8B,KAAK,CAHP9B,QAAQ,EACRoB,QAAQ,GAENU,KAAK,CAFPV,QAAQ,EACRf,cAAc,GACZyB,KAAK,CADPzB,cAAc,EAEhB;cAAA8E,KAAA,GAII5H,QAAQ,CAACwE,OAAO,EAAED,KAAK,CAAC,IAAI,CAAC,CAAC,EAFhCgB,OAAO,GAAAqC,KAAA,CAAPrC,OAAO,EAAAsC,aAAA,GAAAD,KAAA,CACPxC,OAAO,EAAPA,OAAO,GAAAyC,aAAA,cAAG,CAAC,GAAAA,aAAA;cAAA,MAETtC,OAAO,IAAI,IAAI;gBAAA2D,SAAA,CAAAhN,IAAA;gBAAA;cAAA;cAAA,OAAAgN,SAAA,CAAApN,MAAA,WACV,CAAC,CAAC;YAAA;cAELuJ,aAAa,GAAGpF,gBAAgB,CAACmF,OAAO,CAAC;cACzCrD,MAAM,GAAG;gBACbL,CAAC;gBACDtH;cACF,CAAC;cACK0N,IAAI,GAAGlI,gBAAgB,CAACoB,SAAS,CAAC;cAClCjE,MAAM,GAAG8C,aAAa,CAACiI,IAAI,CAAC;cAAAoB,SAAA,CAAAhN,IAAA;cAAA,OACJuG,QAAQ,CAAC0G,aAAa,CAAC5D,OAAO,CAAC;YAAA;cAAvDwC,eAAe,GAAAmB,SAAA,CAAAvN,IAAA;cACfqM,OAAO,GAAGF,IAAI,KAAK,GAAG;cACtBG,OAAO,GAAGD,OAAO,GAAG,KAAK,GAAG,MAAM;cAClCE,OAAO,GAAGF,OAAO,GAAG,QAAQ,GAAG,OAAO;cACtCG,UAAU,GAAGH,OAAO,GAAG,cAAc,GAAG,aAAa;cACrDI,OAAO,GAAGzF,KAAK,CAACzB,SAAS,CAACnE,MAAM,CAAC,GAAG4F,KAAK,CAACzB,SAAS,CAAC4G,IAAI,CAAC,GAAG/F,MAAM,CAAC+F,IAAI,CAAC,GAAGnF,KAAK,CAACxB,QAAQ,CAACpE,MAAM,CAAC;cACjGsL,SAAS,GAAGtG,MAAM,CAAC+F,IAAI,CAAC,GAAGnF,KAAK,CAACzB,SAAS,CAAC4G,IAAI,CAAC;cAAAoB,SAAA,CAAAhN,IAAA;cAAA,OACrBuG,QAAQ,CAACqE,eAAe,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGrE,QAAQ,CAACqE,eAAe,CAACvB,OAAO,CAAC;YAAA;cAAxG+C,iBAAiB,GAAAY,SAAA,CAAAvN,IAAA;cACnB4M,UAAU,GAAGD,iBAAiB,GAAGA,iBAAiB,CAACH,UAAU,CAAC,GAAG,CAAC,EAEtE;cAAAe,SAAA,CAAApF,EAAA,GACI,CAACyE,UAAU;cAAA,IAAAW,SAAA,CAAApF,EAAA;gBAAAoF,SAAA,CAAAhN,IAAA;gBAAA;cAAA;cAAAgN,SAAA,CAAAhN,IAAA;cAAA,OAAauG,QAAQ,CAACuD,SAAS,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGvD,QAAQ,CAACuD,SAAS,CAACsC,iBAAiB,CAAC;YAAA;cAAAY,SAAA,CAAApF,EAAA,IAAAoF,SAAA,CAAAvN,IAAA;YAAA;cAAA,KAAAuN,SAAA,CAAApF,EAAA;gBAAAoF,SAAA,CAAAhN,IAAA;gBAAA;cAAA;cACrGqM,UAAU,GAAG1E,QAAQ,CAAC1C,QAAQ,CAACgH,UAAU,CAAC,IAAIxF,KAAK,CAACxB,QAAQ,CAACpE,MAAM,CAAC;YAAC;cAEjEyL,iBAAiB,GAAGJ,OAAO,GAAG,CAAC,GAAGC,SAAS,GAAG,CAAC,EAErD;cACA;cACMI,sBAAsB,GAAGF,UAAU,GAAG,CAAC,GAAGR,eAAe,CAAChL,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC;cACzE2L,UAAU,GAAGvI,GAAG,CAACkF,aAAa,CAAC4C,OAAO,CAAC,EAAEQ,sBAAsB,CAAC;cAChEE,UAAU,GAAGxI,GAAG,CAACkF,aAAa,CAAC6C,OAAO,CAAC,EAAEO,sBAAsB,CAAC,EAEtE;cACA;cACMG,KAAK,GAAGF,UAAU;cAClB9H,GAAG,GAAG2H,UAAU,GAAGR,eAAe,CAAChL,MAAM,CAAC,GAAG4L,UAAU;cACvDE,MAAM,GAAGN,UAAU,GAAG,CAAC,GAAGR,eAAe,CAAChL,MAAM,CAAC,GAAG,CAAC,GAAGyL,iBAAiB;cACzEM,MAAM,GAAG1I,KAAK,CAACwI,KAAK,EAAEC,MAAM,EAAEjI,GAAG,CAAC,EAExC;cACA;cACA;cACA;cACMmI,eAAe,GAAG,CAACjG,cAAc,CAAC4E,KAAK,IAAI3H,YAAY,CAACiB,SAAS,CAAC,IAAI,IAAI,IAAI6H,MAAM,KAAKC,MAAM,IAAInG,KAAK,CAACzB,SAAS,CAACnE,MAAM,CAAC,GAAG,CAAC,IAAI8L,MAAM,GAAGD,KAAK,GAAGF,UAAU,GAAGC,UAAU,CAAC,GAAGZ,eAAe,CAAChL,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC;cAC7MiM,eAAe,GAAGD,eAAe,GAAGF,MAAM,GAAGD,KAAK,GAAGC,MAAM,GAAGD,KAAK,GAAGC,MAAM,GAAGjI,GAAG,GAAG,CAAC;cAAA,OAAAsI,SAAA,CAAApN,MAAA,WACrF;gBACL,CAACgM,IAAI,GAAG/F,MAAM,CAAC+F,IAAI,CAAC,GAAGkB,eAAe;gBACtC3F,IAAI,EAAAxE,aAAA;kBACF,CAACiJ,IAAI,GAAGgB,MAAM;kBACdM,YAAY,EAAEP,MAAM,GAAGC,MAAM,GAAGE;gBAAe,GAC3CD,eAAe,IAAI;kBACrBC;gBACF,CAAC,CACF;gBACDnM,KAAK,EAAEkM;cACT,CAAC;YAAA;YAAA;cAAA,OAAAG,SAAA,CAAAlL,IAAA;UAAA;QAAA,GAAA2J,QAAA;MAAA;IACH;EACF,CAAC;AAAA,CAAC;AAEF,SAAS0B,gBAAgBA,CAACC,SAAS,EAAEC,aAAa,EAAEC,iBAAiB,EAAE;EACrE,IAAMC,kCAAkC,GAAGH,SAAS,MAAAI,MAAA,CAAAC,kBAAA,CAAOH,iBAAiB,CAAC9K,MAAM,CAAC,UAAAsC,SAAS;IAAA,OAAIjB,YAAY,CAACiB,SAAS,CAAC,KAAKsI,SAAS;EAAA,EAAC,GAAAK,kBAAA,CAAKH,iBAAiB,CAAC9K,MAAM,CAAC,UAAAsC,SAAS;IAAA,OAAIjB,YAAY,CAACiB,SAAS,CAAC,KAAKsI,SAAS;EAAA,EAAC,KAAIE,iBAAiB,CAAC9K,MAAM,CAAC,UAAAsC,SAAS;IAAA,OAAIlB,OAAO,CAACkB,SAAS,CAAC,KAAKA,SAAS;EAAA,EAAC;EACnS,OAAOyI,kCAAkC,CAAC/K,MAAM,CAAC,UAAAsC,SAAS,EAAI;IAC5D,IAAIsI,SAAS,EAAE;MACb,OAAOvJ,YAAY,CAACiB,SAAS,CAAC,KAAKsI,SAAS,KAAKC,aAAa,GAAGhJ,6BAA6B,CAACS,SAAS,CAAC,KAAKA,SAAS,GAAG,KAAK,CAAC;IAClI;IACA,OAAO,IAAI;EACb,CAAC,CAAC;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAM4I,aAAa,GAAG,SAAhBA,aAAaA,CAAapF,OAAO,EAAE;EACvC,IAAIA,OAAO,KAAK,KAAK,CAAC,EAAE;IACtBA,OAAO,GAAG,CAAC,CAAC;EACd;EACA,OAAO;IACLrH,IAAI,EAAE,eAAe;IACrBqH,OAAO;IACDvB,EAAEA,CAACsB,KAAK,EAAE;MAAA,OAAA/E,iBAAA,cAAAxH,mBAAA,GAAAoF,IAAA,UAAAyM,SAAA;QAAA,IAAAC,qBAAA,EAAAC,sBAAA,EAAAC,qBAAA,EAAArH,KAAA,EAAAG,cAAA,EAAA9B,SAAA,EAAAyB,QAAA,EAAAoB,QAAA,EAAAoG,SAAA,EAAAC,mBAAA,EAAAC,SAAA,EAAAb,SAAA,EAAAc,qBAAA,EAAAZ,iBAAA,EAAAa,qBAAA,EAAAd,aAAA,EAAAe,qBAAA,EAAAC,YAAA,EAAAC,QAAA,EAAAC,YAAA,EAAAC,gBAAA,EAAAC,cAAA,EAAAC,gBAAA,EAAAC,YAAA,EAAAC,aAAA,EAAAC,2BAAA,EAAAC,2BAAA,EAAAC,cAAA;QAAA,OAAAjT,mBAAA,GAAAuB,IAAA,UAAA2R,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAtN,IAAA,GAAAsN,SAAA,CAAAjP,IAAA;YAAA;cAGZyG,KAAK,GAKH4B,KAAK,CALP5B,KAAK,EACLG,cAAc,GAIZyB,KAAK,CAJPzB,cAAc,EACd9B,SAAS,GAGPuD,KAAK,CAHPvD,SAAS,EACTyB,QAAQ,GAEN8B,KAAK,CAFP9B,QAAQ,EACRoB,QAAQ,GACNU,KAAK,CADPV,QAAQ;cAAAoG,SAAA,GAQNjK,QAAQ,CAACwE,OAAO,EAAED,KAAK,CAAC,EAAA2F,mBAAA,GAAAD,SAAA,CAL1BE,SAAS,EAATA,SAAS,GAAAD,mBAAA,cAAG,KAAK,GAAAA,mBAAA,EACjBZ,SAAS,GAAAW,SAAA,CAATX,SAAS,EAAAc,qBAAA,GAAAH,SAAA,CACTT,iBAAiB,EAAjBA,iBAAiB,GAAAY,qBAAA,cAAG/J,UAAU,GAAA+J,qBAAA,EAAAC,qBAAA,GAAAJ,SAAA,CAC9BV,aAAa,EAAbA,aAAa,GAAAc,qBAAA,cAAG,IAAI,GAAAA,qBAAA,EACjBC,qBAAqB,GAAAc,wBAAA,CAAAnB,SAAA,EAAAoB,SAAA;cAEpBd,YAAY,GAAGjB,SAAS,KAAKgC,SAAS,IAAI9B,iBAAiB,KAAKnJ,UAAU,GAAGgJ,gBAAgB,CAACC,SAAS,IAAI,IAAI,EAAEC,aAAa,EAAEC,iBAAiB,CAAC,GAAGA,iBAAiB;cAAA2B,SAAA,CAAAjP,IAAA;cAAA,OACrJgI,cAAc,CAACK,KAAK,EAAE+F,qBAAqB,CAAC;YAAA;cAA7DE,QAAQ,GAAAW,SAAA,CAAAxP,IAAA;cACR8O,YAAY,GAAG,CAAC,CAACX,qBAAqB,GAAGhH,cAAc,CAAC8G,aAAa,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGE,qBAAqB,CAACyB,KAAK,KAAK,CAAC;cAC3Hb,gBAAgB,GAAGH,YAAY,CAACE,YAAY,CAAC;cAAA,MAC/CC,gBAAgB,IAAI,IAAI;gBAAAS,SAAA,CAAAjP,IAAA;gBAAA;cAAA;cAAA,OAAAiP,SAAA,CAAArP,MAAA,WACnB,CAAC,CAAC;YAAA;cAAAqP,SAAA,CAAArH,EAAA,GAEYxD,iBAAiB;cAAA6K,SAAA,CAAApF,EAAA,GAAC2E,gBAAgB;cAAAS,SAAA,CAAAlF,EAAA,GAAEtD,KAAK;cAAAwI,SAAA,CAAAjP,IAAA;cAAA,OAASuG,QAAQ,CAACiB,KAAK,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGjB,QAAQ,CAACiB,KAAK,CAACG,QAAQ,CAAC1C,QAAQ,CAAC;YAAA;cAAAgK,SAAA,CAAAjF,EAAA,GAAAiF,SAAA,CAAAxP,IAAA;cAAtIgP,cAAc,OAAAQ,SAAA,CAAArH,EAAA,EAAAqH,SAAA,CAAApF,EAAA,EAAAoF,SAAA,CAAAlF,EAAA,EAAAkF,SAAA,CAAAjF,EAAA;cAAA,MAGhBlF,SAAS,KAAK0J,gBAAgB;gBAAAS,SAAA,CAAAjP,IAAA;gBAAA;cAAA;cAAA,OAAAiP,SAAA,CAAArP,MAAA,WACzB;gBACLe,KAAK,EAAE;kBACLmE,SAAS,EAAEuJ,YAAY,CAAC,CAAC;gBAC3B;cACF,CAAC;YAAA;cAEGK,gBAAgB,GAAG,CAACJ,QAAQ,CAAC1K,OAAO,CAAC4K,gBAAgB,CAAC,CAAC,EAAEF,QAAQ,CAACG,cAAc,CAAC,CAAC,CAAC,CAAC,EAAEH,QAAQ,CAACG,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC;cAClHE,YAAY,MAAAnB,MAAA,CAAAC,kBAAA,CAAQ,CAAC,CAACI,sBAAsB,GAAGjH,cAAc,CAAC8G,aAAa,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGG,sBAAsB,CAACyB,SAAS,KAAK,EAAE,IAAG;gBAC9IxK,SAAS,EAAE0J,gBAAgB;gBAC3Bc,SAAS,EAAEZ;cACb,CAAC;cACKE,aAAa,GAAGP,YAAY,CAACE,YAAY,GAAG,CAAC,CAAC,EAEpD;cAAA,KACIK,aAAa;gBAAAK,SAAA,CAAAjP,IAAA;gBAAA;cAAA;cAAA,OAAAiP,SAAA,CAAArP,MAAA,WACR;gBACLuH,IAAI,EAAE;kBACJkI,KAAK,EAAEd,YAAY,GAAG,CAAC;kBACvBe,SAAS,EAAEX;gBACb,CAAC;gBACDhO,KAAK,EAAE;kBACLmE,SAAS,EAAE8J;gBACb;cACF,CAAC;YAAA;cAEGC,2BAA2B,GAAGF,YAAY,CAACY,GAAG,CAAC,UAAAjR,CAAC,EAAI;gBACxD,IAAM8O,SAAS,GAAGvJ,YAAY,CAACvF,CAAC,CAACwG,SAAS,CAAC;gBAC3C,OAAO,CAACxG,CAAC,CAACwG,SAAS,EAAEsI,SAAS,IAAIa,SAAS;gBAC3C;gBACA3P,CAAC,CAACgR,SAAS,CAACzN,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC2N,MAAM,CAAC,UAACC,GAAG,EAAEjR,CAAC;kBAAA,OAAKiR,GAAG,GAAGjR,CAAC;gBAAA,GAAE,CAAC,CAAC;gBACtD;gBACAF,CAAC,CAACgR,SAAS,CAAC,CAAC,CAAC,EAAEhR,CAAC,CAACgR,SAAS,CAAC;cAC9B,CAAC,CAAC,CAACI,IAAI,CAAC,UAAC/S,CAAC,EAAEgT,CAAC;gBAAA,OAAKhT,CAAC,CAAC,CAAC,CAAC,GAAGgT,CAAC,CAAC,CAAC,CAAC;cAAA,EAAC;cACxBb,2BAA2B,GAAGD,2BAA2B,CAACrM,MAAM,CAAC,UAAAlE,CAAC;gBAAA,OAAIA,CAAC,CAAC,CAAC,CAAC,CAACuD,KAAK,CAAC,CAAC;gBACxF;gBACA;gBACAgC,YAAY,CAACvF,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAACsR,KAAK,CAAC,UAAApR,CAAC;kBAAA,OAAIA,CAAC,IAAI,CAAC;gBAAA,EAAC;cAAA,EAAC;cACzCuQ,cAAc,GAAG,CAAC,CAACjB,qBAAqB,GAAGgB,2BAA2B,CAAC,CAAC,CAAC,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGhB,qBAAqB,CAAC,CAAC,CAAC,KAAKe,2BAA2B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cAAA,MAC9JE,cAAc,KAAKjK,SAAS;gBAAAmK,SAAA,CAAAjP,IAAA;gBAAA;cAAA;cAAA,OAAAiP,SAAA,CAAArP,MAAA,WACvB;gBACLuH,IAAI,EAAE;kBACJkI,KAAK,EAAEd,YAAY,GAAG,CAAC;kBACvBe,SAAS,EAAEX;gBACb,CAAC;gBACDhO,KAAK,EAAE;kBACLmE,SAAS,EAAEiK;gBACb;cACF,CAAC;YAAA;cAAA,OAAAE,SAAA,CAAArP,MAAA,WAEI,CAAC,CAAC;YAAA;YAAA;cAAA,OAAAqP,SAAA,CAAAnN,IAAA;UAAA;QAAA,GAAA6L,QAAA;MAAA;IACX;EACF,CAAC;AACH,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA,IAAMkC,IAAI,GAAG,SAAPA,IAAIA,CAAavH,OAAO,EAAE;EAC9B,IAAIA,OAAO,KAAK,KAAK,CAAC,EAAE;IACtBA,OAAO,GAAG,CAAC,CAAC;EACd;EACA,OAAO;IACLrH,IAAI,EAAE,MAAM;IACZqH,OAAO;IACDvB,EAAEA,CAACsB,KAAK,EAAE;MAAA,OAAA/E,iBAAA,cAAAxH,mBAAA,GAAAoF,IAAA,UAAA4O,SAAA;QAAA,IAAAC,qBAAA,EAAAC,oBAAA,EAAAlL,SAAA,EAAA8B,cAAA,EAAAH,KAAA,EAAAiB,gBAAA,EAAAnB,QAAA,EAAAoB,QAAA,EAAAsI,UAAA,EAAAC,mBAAA,EAAAC,aAAA,EAAAC,oBAAA,EAAAC,cAAA,EAAAC,2BAAA,EAAAC,qBAAA,EAAAC,gBAAA,EAAAC,qBAAA,EAAAC,yBAAA,EAAAC,qBAAA,EAAAC,aAAA,EAAAxC,qBAAA,EAAA/I,IAAA,EAAAwL,eAAA,EAAAC,eAAA,EAAA/L,GAAA,EAAAgM,kBAAA,EAAAC,4BAAA,EAAA7M,UAAA,EAAAmK,QAAA,EAAAgB,SAAA,EAAA2B,aAAA,EAAAC,MAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,SAAA,EAAAzC,aAAA,EAAAG,cAAA,EAAAuC,sBAAA,EAAAC,UAAA;QAAA,OAAAzV,mBAAA,GAAAuB,IAAA,UAAAmU,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAA9P,IAAA,GAAA8P,SAAA,CAAAzR,IAAA;YAAA;cAGZ8E,SAAS,GAMPuD,KAAK,CANPvD,SAAS,EACT8B,cAAc,GAKZyB,KAAK,CALPzB,cAAc,EACdH,KAAK,GAIH4B,KAAK,CAJP5B,KAAK,EACLiB,gBAAgB,GAGdW,KAAK,CAHPX,gBAAgB,EAChBnB,QAAQ,GAEN8B,KAAK,CAFP9B,QAAQ,EACRoB,QAAQ,GACNU,KAAK,CADPV,QAAQ;cAAAsI,UAAA,GAUNnM,QAAQ,CAACwE,OAAO,EAAED,KAAK,CAAC,EAAA6H,mBAAA,GAAAD,UAAA,CAP1ByB,QAAQ,EAAEvB,aAAa,GAAAD,mBAAA,cAAG,IAAI,GAAAA,mBAAA,EAAAE,oBAAA,GAAAH,UAAA,CAC9BhC,SAAS,EAAEoC,cAAc,GAAAD,oBAAA,cAAG,IAAI,GAAAA,oBAAA,EACZE,2BAA2B,GAAAL,UAAA,CAA/Cc,kBAAkB,EAAAR,qBAAA,GAAAN,UAAA,CAClBO,gBAAgB,EAAhBA,gBAAgB,GAAAD,qBAAA,cAAG,SAAS,GAAAA,qBAAA,EAAAE,qBAAA,GAAAR,UAAA,CAC5BS,yBAAyB,EAAzBA,yBAAyB,GAAAD,qBAAA,cAAG,MAAM,GAAAA,qBAAA,EAAAE,qBAAA,GAAAV,UAAA,CAClCW,aAAa,EAAbA,aAAa,GAAAD,qBAAA,cAAG,IAAI,GAAAA,qBAAA,EACjBvC,qBAAqB,GAAAc,wBAAA,CAAAe,UAAA,EAAA0B,UAAA,GAG1B;cACA;cACA;cACA;cAAA,MACI,CAAC5B,qBAAqB,GAAGnJ,cAAc,CAAC4E,KAAK,KAAK,IAAI,IAAIuE,qBAAqB,CAACjD,eAAe;gBAAA2E,SAAA,CAAAzR,IAAA;gBAAA;cAAA;cAAA,OAAAyR,SAAA,CAAA7R,MAAA,WAC1F,CAAC,CAAC;YAAA;cAELyF,IAAI,GAAGzB,OAAO,CAACkB,SAAS,CAAC;cACzB+L,eAAe,GAAGpN,WAAW,CAACiE,gBAAgB,CAAC;cAC/CoJ,eAAe,GAAGlN,OAAO,CAAC8D,gBAAgB,CAAC,KAAKA,gBAAgB;cAAA+J,SAAA,CAAAzR,IAAA;cAAA,OACnDuG,QAAQ,CAACiB,KAAK,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGjB,QAAQ,CAACiB,KAAK,CAACG,QAAQ,CAAC1C,QAAQ,CAAC;YAAA;cAAhFF,GAAG,GAAA0M,SAAA,CAAAhS,IAAA;cACHsR,kBAAkB,GAAGT,2BAA2B,KAAKQ,eAAe,IAAI,CAACF,aAAa,GAAG,CAACtM,oBAAoB,CAACoD,gBAAgB,CAAC,CAAC,GAAGnD,qBAAqB,CAACmD,gBAAgB,CAAC,CAAC;cAC5KsJ,4BAA4B,GAAGN,yBAAyB,KAAK,MAAM;cACzE,IAAI,CAACJ,2BAA2B,IAAIU,4BAA4B,EAAE;gBAChED,kBAAkB,CAACvQ,IAAI,CAAAkC,KAAA,CAAvBqO,kBAAkB,EAAAtD,kBAAA,CAASjJ,yBAAyB,CAACkD,gBAAgB,EAAEkJ,aAAa,EAAEF,yBAAyB,EAAE3L,GAAG,CAAC,EAAC;cACxH;cACMZ,UAAU,IAAIuD,gBAAgB,EAAA8F,MAAA,CAAAC,kBAAA,CAAKsD,kBAAkB;cAAAU,SAAA,CAAAzR,IAAA;cAAA,OACpCgI,cAAc,CAACK,KAAK,EAAE+F,qBAAqB,CAAC;YAAA;cAA7DE,QAAQ,GAAAmD,SAAA,CAAAhS,IAAA;cACR6P,SAAS,GAAG,EAAE;cAChB2B,aAAa,GAAG,CAAC,CAACjB,oBAAoB,GAAGpJ,cAAc,CAACiJ,IAAI,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGG,oBAAoB,CAACV,SAAS,KAAK,EAAE;cAC1H,IAAIa,aAAa,EAAE;gBACjBb,SAAS,CAAC9O,IAAI,CAAC8N,QAAQ,CAACjJ,IAAI,CAAC,CAAC;cAChC;cACA,IAAIgL,cAAc,EAAE;gBACZ5L,MAAK,GAAGL,iBAAiB,CAACU,SAAS,EAAE2B,KAAK,EAAE1B,GAAG,CAAC;gBACtDuK,SAAS,CAAC9O,IAAI,CAAC8N,QAAQ,CAAC7J,MAAK,CAAC,CAAC,CAAC,CAAC,EAAE6J,QAAQ,CAAC7J,MAAK,CAAC,CAAC,CAAC,CAAC,CAAC;cACxD;cACAwM,aAAa,MAAAzD,MAAA,CAAAC,kBAAA,CAAOwD,aAAa,IAAE;gBACjCnM,SAAS;gBACTwK;cACF,CAAC,EAAC;;cAEF;cAAA,IACKA,SAAS,CAACM,KAAK,CAAC,UAAAvK,IAAI;gBAAA,OAAIA,IAAI,IAAI,CAAC;cAAA,EAAC;gBAAAoM,SAAA,CAAAzR,IAAA;gBAAA;cAAA;cAE/BqR,SAAS,GAAG,CAAC,CAAC,CAACF,qBAAqB,GAAGvK,cAAc,CAACiJ,IAAI,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGsB,qBAAqB,CAAC9B,KAAK,KAAK,CAAC,IAAI,CAAC;cACrHT,aAAa,GAAGzK,UAAU,CAACkN,SAAS,CAAC;cAAA,KACvCzC,aAAa;gBAAA6C,SAAA,CAAAzR,IAAA;gBAAA;cAAA;cAAA,OAAAyR,SAAA,CAAA7R,MAAA,WAER;gBACLuH,IAAI,EAAE;kBACJkI,KAAK,EAAEgC,SAAS;kBAChB/B,SAAS,EAAE2B;gBACb,CAAC;gBACDtQ,KAAK,EAAE;kBACLmE,SAAS,EAAE8J;gBACb;cACF,CAAC;YAAA;cAGH;cACA;cACIG,cAAc,GAAG,CAACqC,qBAAqB,GAAGH,aAAa,CAACzO,MAAM,CAAC,UAAAlE,CAAC;gBAAA,OAAIA,CAAC,CAACgR,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC;cAAA,EAAC,CAACI,IAAI,CAAC,UAAC/S,CAAC,EAAEgT,CAAC;gBAAA,OAAKhT,CAAC,CAAC2S,SAAS,CAAC,CAAC,CAAC,GAAGK,CAAC,CAACL,SAAS,CAAC,CAAC,CAAC;cAAA,EAAC,CAAC,CAAC,CAAC,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG8B,qBAAqB,CAACtM,SAAS,EAEnM;cAAA,IACKiK,cAAc;gBAAA0C,SAAA,CAAAzR,IAAA;gBAAA;cAAA;cAAAyR,SAAA,CAAA7J,EAAA,GACT4I,gBAAgB;cAAAiB,SAAA,CAAAzR,IAAA,GAAAyR,SAAA,CAAA7J,EAAA,KACjB,SAAS,QAAA6J,SAAA,CAAA7J,EAAA,KAkBT,kBAAkB;cAAA;YAAA;cAfb9C,UAAS,GAAG,CAACwM,sBAAsB,GAAGL,aAAa,CAACzO,MAAM,CAAC,UAAAlE,CAAC,EAAI;gBACpE,IAAI0S,4BAA4B,EAAE;kBAChC,IAAMY,eAAe,GAAGnO,WAAW,CAACnF,CAAC,CAACwG,SAAS,CAAC;kBAChD,OAAO8M,eAAe,KAAKf,eAAe;kBAC1C;kBACA;kBACAe,eAAe,KAAK,GAAG;gBACzB;gBACA,OAAO,IAAI;cACb,CAAC,CAAC,CAACrC,GAAG,CAAC,UAAAjR,CAAC;gBAAA,OAAI,CAACA,CAAC,CAACwG,SAAS,EAAExG,CAAC,CAACgR,SAAS,CAAC9M,MAAM,CAAC,UAAA8L,QAAQ;kBAAA,OAAIA,QAAQ,GAAG,CAAC;gBAAA,EAAC,CAACkB,MAAM,CAAC,UAACC,GAAG,EAAEnB,QAAQ;kBAAA,OAAKmB,GAAG,GAAGnB,QAAQ;gBAAA,GAAE,CAAC,CAAC,CAAC;cAAA,EAAC,CAACoB,IAAI,CAAC,UAAC/S,CAAC,EAAEgT,CAAC;gBAAA,OAAKhT,CAAC,CAAC,CAAC,CAAC,GAAGgT,CAAC,CAAC,CAAC,CAAC;cAAA,EAAC,CAAC,CAAC,CAAC,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG2B,sBAAsB,CAAC,CAAC,CAAC;cAClM,IAAIxM,UAAS,EAAE;gBACbiK,cAAc,GAAGjK,UAAS;cAC5B;cAAC,OAAA2M,SAAA,CAAA7R,MAAA;YAAA;cAIHmP,cAAc,GAAGrH,gBAAgB;cAAC,OAAA+J,SAAA,CAAA7R,MAAA;YAAA;cAAA,MAIpCkF,SAAS,KAAKiK,cAAc;gBAAA0C,SAAA,CAAAzR,IAAA;gBAAA;cAAA;cAAA,OAAAyR,SAAA,CAAA7R,MAAA,WACvB;gBACLe,KAAK,EAAE;kBACLmE,SAAS,EAAEiK;gBACb;cACF,CAAC;YAAA;cAAA,OAAA0C,SAAA,CAAA7R,MAAA,WAGE,CAAC,CAAC;YAAA;YAAA;cAAA,OAAA6R,SAAA,CAAA3P,IAAA;UAAA;QAAA,GAAAgO,QAAA;MAAA;IACX;EACF,CAAC;AACH,CAAC;AAED,SAAS+B,cAAcA,CAACvD,QAAQ,EAAE/E,IAAI,EAAE;EACtC,OAAO;IACL6B,GAAG,EAAEkD,QAAQ,CAAClD,GAAG,GAAG7B,IAAI,CAAC5D,MAAM;IAC/B4F,KAAK,EAAE+C,QAAQ,CAAC/C,KAAK,GAAGhC,IAAI,CAAC9D,KAAK;IAClC4F,MAAM,EAAEiD,QAAQ,CAACjD,MAAM,GAAG9B,IAAI,CAAC5D,MAAM;IACrC2F,IAAI,EAAEgD,QAAQ,CAAChD,IAAI,GAAG/B,IAAI,CAAC9D;EAC7B,CAAC;AACH;AACA,SAASqM,qBAAqBA,CAACxD,QAAQ,EAAE;EACvC,OAAO7J,KAAK,CAACsN,IAAI,CAAC,UAAA1M,IAAI;IAAA,OAAIiJ,QAAQ,CAACjJ,IAAI,CAAC,IAAI,CAAC;EAAA,EAAC;AAChD;AACA;AACA;AACA;AACA;AACA;AACA,IAAM2M,IAAI,GAAG,SAAPA,IAAIA,CAAa1J,OAAO,EAAE;EAC9B,IAAIA,OAAO,KAAK,KAAK,CAAC,EAAE;IACtBA,OAAO,GAAG,CAAC,CAAC;EACd;EACA,OAAO;IACLrH,IAAI,EAAE,MAAM;IACZqH,OAAO;IACDvB,EAAEA,CAACsB,KAAK,EAAE;MAAA,OAAA/E,iBAAA,cAAAxH,mBAAA,GAAAoF,IAAA,UAAA+Q,SAAA;QAAA,IAAAxL,KAAA,EAAAyL,UAAA,EAAAC,mBAAA,EAAA/L,QAAA,EAAAgI,qBAAA,EAAAE,QAAA,EAAA8D,OAAA,EAAAC,SAAA,EAAAC,QAAA;QAAA,OAAAxW,mBAAA,GAAAuB,IAAA,UAAAkV,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAA7Q,IAAA,GAAA6Q,SAAA,CAAAxS,IAAA;YAAA;cAEZyG,KAAK,GACH4B,KAAK,CADP5B,KAAK;cAAAyL,UAAA,GAKHpO,QAAQ,CAACwE,OAAO,EAAED,KAAK,CAAC,EAAA8J,mBAAA,GAAAD,UAAA,CAF1B9L,QAAQ,EAARA,QAAQ,GAAA+L,mBAAA,cAAG,iBAAiB,GAAAA,mBAAA,EACzB/D,qBAAqB,GAAAc,wBAAA,CAAAgD,UAAA,EAAAO,UAAA;cAAAD,SAAA,CAAA5K,EAAA,GAElBxB,QAAQ;cAAAoM,SAAA,CAAAxS,IAAA,GAAAwS,SAAA,CAAA5K,EAAA,KACT,iBAAiB,OAAA4K,SAAA,CAAA5K,EAAA,KAcjB,SAAS;cAAA;YAAA;cAAA4K,SAAA,CAAAxS,IAAA;cAAA,OAZagI,cAAc,CAACK,KAAK,EAAA1F,aAAA,CAAAA,aAAA,KACtCyL,qBAAqB;gBACxBtF,cAAc,EAAE;cAAW,EAC5B,CAAC;YAAA;cAHIwF,QAAQ,GAAAkE,SAAA,CAAA/S,IAAA;cAIR2S,OAAO,GAAGP,cAAc,CAACvD,QAAQ,EAAE7H,KAAK,CAACzB,SAAS,CAAC;cAAA,OAAAwN,SAAA,CAAA5S,MAAA,WAClD;gBACLuH,IAAI,EAAE;kBACJuL,sBAAsB,EAAEN,OAAO;kBAC/BO,eAAe,EAAEb,qBAAqB,CAACM,OAAO;gBAChD;cACF,CAAC;YAAA;cAAAI,SAAA,CAAAxS,IAAA;cAAA,OAIsBgI,cAAc,CAACK,KAAK,EAAA1F,aAAA,CAAAA,aAAA,KACtCyL,qBAAqB;gBACxBpF,WAAW,EAAE;cAAI,EAClB,CAAC;YAAA;cAHIsF,SAAQ,GAAAkE,SAAA,CAAA/S,IAAA;cAIR2S,QAAO,GAAGP,cAAc,CAACvD,SAAQ,EAAE7H,KAAK,CAACxB,QAAQ,CAAC;cAAA,OAAAuN,SAAA,CAAA5S,MAAA,WACjD;gBACLuH,IAAI,EAAE;kBACJyL,cAAc,EAAER,QAAO;kBACvBS,OAAO,EAAEf,qBAAqB,CAACM,QAAO;gBACxC;cACF,CAAC;YAAA;cAAA,OAAAI,SAAA,CAAA5S,MAAA,WAIM,CAAC,CAAC;YAAA;YAAA;cAAA,OAAA4S,SAAA,CAAA1Q,IAAA;UAAA;QAAA,GAAAmQ,QAAA;MAAA;IAGjB;EACF,CAAC;AACH,CAAC;AAED,SAASa,eAAeA,CAACrM,KAAK,EAAE;EAC9B,IAAMsM,IAAI,GAAG9O,GAAG,CAAAvB,KAAA,SAAA+K,kBAAA,CAAIhH,KAAK,CAAC8I,GAAG,CAAC,UAAAhG,IAAI;IAAA,OAAIA,IAAI,CAAC+B,IAAI;EAAA,EAAC,EAAC;EACjD,IAAM0H,IAAI,GAAG/O,GAAG,CAAAvB,KAAA,SAAA+K,kBAAA,CAAIhH,KAAK,CAAC8I,GAAG,CAAC,UAAAhG,IAAI;IAAA,OAAIA,IAAI,CAAC6B,GAAG;EAAA,EAAC,EAAC;EAChD,IAAM6H,IAAI,GAAGvO,GAAG,CAAAhC,KAAA,SAAA+K,kBAAA,CAAIhH,KAAK,CAAC8I,GAAG,CAAC,UAAAhG,IAAI;IAAA,OAAIA,IAAI,CAACgC,KAAK;EAAA,EAAC,EAAC;EAClD,IAAM2H,IAAI,GAAGxO,GAAG,CAAAhC,KAAA,SAAA+K,kBAAA,CAAIhH,KAAK,CAAC8I,GAAG,CAAC,UAAAhG,IAAI;IAAA,OAAIA,IAAI,CAAC8B,MAAM;EAAA,EAAC,EAAC;EACnD,OAAO;IACL7F,CAAC,EAAEuN,IAAI;IACP7U,CAAC,EAAE8U,IAAI;IACPvN,KAAK,EAAEwN,IAAI,GAAGF,IAAI;IAClBpN,MAAM,EAAEuN,IAAI,GAAGF;EACjB,CAAC;AACH;AACA,SAASG,cAAcA,CAAC1M,KAAK,EAAE;EAC7B,IAAM2M,WAAW,GAAG3M,KAAK,CAAC5E,KAAK,CAAC,CAAC,CAAC6N,IAAI,CAAC,UAAC/S,CAAC,EAAEgT,CAAC;IAAA,OAAKhT,CAAC,CAACuB,CAAC,GAAGyR,CAAC,CAACzR,CAAC;EAAA,EAAC;EAC3D,IAAMmV,MAAM,GAAG,EAAE;EACjB,IAAIC,QAAQ,GAAG,IAAI;EACnB,KAAK,IAAI7W,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG2W,WAAW,CAACvS,MAAM,EAAEpE,CAAC,EAAE,EAAE;IAC3C,IAAM8M,IAAI,GAAG6J,WAAW,CAAC3W,CAAC,CAAC;IAC3B,IAAI,CAAC6W,QAAQ,IAAI/J,IAAI,CAACrL,CAAC,GAAGoV,QAAQ,CAACpV,CAAC,GAAGoV,QAAQ,CAAC3N,MAAM,GAAG,CAAC,EAAE;MAC1D0N,MAAM,CAAC7S,IAAI,CAAC,CAAC+I,IAAI,CAAC,CAAC;IACrB,CAAC,MAAM;MACL8J,MAAM,CAACA,MAAM,CAACxS,MAAM,GAAG,CAAC,CAAC,CAACL,IAAI,CAAC+I,IAAI,CAAC;IACtC;IACA+J,QAAQ,GAAG/J,IAAI;EACjB;EACA,OAAO8J,MAAM,CAAC9D,GAAG,CAAC,UAAAhG,IAAI;IAAA,OAAIvF,gBAAgB,CAAC8O,eAAe,CAACvJ,IAAI,CAAC,CAAC;EAAA,EAAC;AACpE;AACA;AACA;AACA;AACA;AACA;AACA,IAAMgK,MAAM,GAAG,SAATA,MAAMA,CAAajL,OAAO,EAAE;EAChC,IAAIA,OAAO,KAAK,KAAK,CAAC,EAAE;IACtBA,OAAO,GAAG,CAAC,CAAC;EACd;EACA,OAAO;IACLrH,IAAI,EAAE,QAAQ;IACdqH,OAAO;IACDvB,EAAEA,CAACsB,KAAK,EAAE;MAAA,OAAA/E,iBAAA,cAAAxH,mBAAA,GAAAoF,IAAA,UAAAsS,SAAA;QAAA,IAAA1O,SAAA,EAAA6C,QAAA,EAAAlB,KAAA,EAAAF,QAAA,EAAAH,QAAA,EAAAqN,UAAA,EAAAC,kBAAA,EAAAxK,OAAA,EAAA1D,CAAA,EAAAtH,CAAA,EAAAyV,iBAAA,EAAAC,WAAA,EAAAC,QAAA,EAAA1K,aAAA,EAoBL2K,qBAAqB,EAAAC,UAAA;QAAA,OAAAjY,mBAAA,GAAAuB,IAAA,UAAA2W,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAtS,IAAA,GAAAsS,SAAA,CAAAjU,IAAA;YAAA;cAArB8T,qBAAqB,YAAAI,sBAAA,EAAG;gBAC/B;gBACA,IAAIN,WAAW,CAAC/S,MAAM,KAAK,CAAC,IAAI+S,WAAW,CAAC,CAAC,CAAC,CAACtI,IAAI,GAAGsI,WAAW,CAAC,CAAC,CAAC,CAACrI,KAAK,IAAI/F,CAAC,IAAI,IAAI,IAAItH,CAAC,IAAI,IAAI,EAAE;kBACpG;kBACA,OAAO0V,WAAW,CAACO,IAAI,CAAC,UAAA5K,IAAI;oBAAA,OAAI/D,CAAC,GAAG+D,IAAI,CAAC+B,IAAI,GAAGnC,aAAa,CAACmC,IAAI,IAAI9F,CAAC,GAAG+D,IAAI,CAACgC,KAAK,GAAGpC,aAAa,CAACoC,KAAK,IAAIrN,CAAC,GAAGqL,IAAI,CAAC6B,GAAG,GAAGjC,aAAa,CAACiC,GAAG,IAAIlN,CAAC,GAAGqL,IAAI,CAAC8B,MAAM,GAAGlC,aAAa,CAACkC,MAAM;kBAAA,EAAC,IAAIwI,QAAQ;gBACvM;;gBAEA;gBACA,IAAID,WAAW,CAAC/S,MAAM,IAAI,CAAC,EAAE;kBAC3B,IAAI4C,WAAW,CAACqB,SAAS,CAAC,KAAK,GAAG,EAAE;oBAClC,IAAMsP,SAAS,GAAGR,WAAW,CAAC,CAAC,CAAC;oBAChC,IAAMS,QAAQ,GAAGT,WAAW,CAACA,WAAW,CAAC/S,MAAM,GAAG,CAAC,CAAC;oBACpD,IAAMyT,KAAK,GAAG1Q,OAAO,CAACkB,SAAS,CAAC,KAAK,KAAK;oBAC1C,IAAMsG,IAAG,GAAGgJ,SAAS,CAAChJ,GAAG;oBACzB,IAAMC,OAAM,GAAGgJ,QAAQ,CAAChJ,MAAM;oBAC9B,IAAMC,KAAI,GAAGgJ,KAAK,GAAGF,SAAS,CAAC9I,IAAI,GAAG+I,QAAQ,CAAC/I,IAAI;oBACnD,IAAMC,MAAK,GAAG+I,KAAK,GAAGF,SAAS,CAAC7I,KAAK,GAAG8I,QAAQ,CAAC9I,KAAK;oBACtD,IAAM9F,MAAK,GAAG8F,MAAK,GAAGD,KAAI;oBAC1B,IAAM3F,OAAM,GAAG0F,OAAM,GAAGD,IAAG;oBAC3B,OAAO;sBACLA,GAAG,EAAHA,IAAG;sBACHC,MAAM,EAANA,OAAM;sBACNC,IAAI,EAAJA,KAAI;sBACJC,KAAK,EAALA,MAAK;sBACL9F,KAAK,EAALA,MAAK;sBACLE,MAAM,EAANA,OAAM;sBACNH,CAAC,EAAE8F,KAAI;sBACPpN,CAAC,EAAEkN;oBACL,CAAC;kBACH;kBACA,IAAMmJ,UAAU,GAAG3Q,OAAO,CAACkB,SAAS,CAAC,KAAK,MAAM;kBAChD,IAAM0P,QAAQ,GAAG9P,GAAG,CAAAhC,KAAA,SAAA+K,kBAAA,CAAImG,WAAW,CAACrE,GAAG,CAAC,UAAAhG,IAAI;oBAAA,OAAIA,IAAI,CAACgC,KAAK;kBAAA,EAAC,EAAC;kBAC5D,IAAMkJ,OAAO,GAAGxQ,GAAG,CAAAvB,KAAA,SAAA+K,kBAAA,CAAImG,WAAW,CAACrE,GAAG,CAAC,UAAAhG,IAAI;oBAAA,OAAIA,IAAI,CAAC+B,IAAI;kBAAA,EAAC,EAAC;kBAC1D,IAAMoJ,YAAY,GAAGd,WAAW,CAACpR,MAAM,CAAC,UAAA+G,IAAI;oBAAA,OAAIgL,UAAU,GAAGhL,IAAI,CAAC+B,IAAI,KAAKmJ,OAAO,GAAGlL,IAAI,CAACgC,KAAK,KAAKiJ,QAAQ;kBAAA,EAAC;kBAC7G,IAAMpJ,GAAG,GAAGsJ,YAAY,CAAC,CAAC,CAAC,CAACtJ,GAAG;kBAC/B,IAAMC,MAAM,GAAGqJ,YAAY,CAACA,YAAY,CAAC7T,MAAM,GAAG,CAAC,CAAC,CAACwK,MAAM;kBAC3D,IAAMC,IAAI,GAAGmJ,OAAO;kBACpB,IAAMlJ,KAAK,GAAGiJ,QAAQ;kBACtB,IAAM/O,KAAK,GAAG8F,KAAK,GAAGD,IAAI;kBAC1B,IAAM3F,MAAM,GAAG0F,MAAM,GAAGD,GAAG;kBAC3B,OAAO;oBACLA,GAAG;oBACHC,MAAM;oBACNC,IAAI;oBACJC,KAAK;oBACL9F,KAAK;oBACLE,MAAM;oBACNH,CAAC,EAAE8F,IAAI;oBACPpN,CAAC,EAAEkN;kBACL,CAAC;gBACH;gBACA,OAAOyI,QAAQ;cACjB,CAAC;cAtEC/O,SAAS,GAKPuD,KAAK,CALPvD,SAAS,EACT6C,QAAQ,GAINU,KAAK,CAJPV,QAAQ,EACRlB,KAAK,GAGH4B,KAAK,CAHP5B,KAAK,EACLF,QAAQ,GAEN8B,KAAK,CAFP9B,QAAQ,EACRH,QAAQ,GACNiC,KAAK,CADPjC,QAAQ,EAEV;cACA;cACA;cAAAqN,UAAA,GAKI3P,QAAQ,CAACwE,OAAO,EAAED,KAAK,CAAC,EAAAqL,kBAAA,GAAAD,UAAA,CAH1BvK,OAAO,EAAPA,OAAO,GAAAwK,kBAAA,cAAG,CAAC,GAAAA,kBAAA,EACXlO,CAAC,GAAAiO,UAAA,CAADjO,CAAC,EACDtH,CAAC,GAAAuV,UAAA,CAADvV,CAAC;cAAA+V,SAAA,CAAArM,EAAA,GAEuB+M,KAAK;cAAAV,SAAA,CAAAjU,IAAA;cAAA,OAAcuG,QAAQ,CAACqO,cAAc,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGrO,QAAQ,CAACqO,cAAc,CAACjN,QAAQ,CAAC3C,SAAS,CAAC;YAAA;cAAAiP,SAAA,CAAApK,EAAA,GAAAoK,SAAA,CAAAxU,IAAA;cAAA,IAAAwU,SAAA,CAAApK,EAAA;gBAAAoK,SAAA,CAAAjU,IAAA;gBAAA;cAAA;cAAAiU,SAAA,CAAApK,EAAA,GAAM,EAAE;YAAA;cAAAoK,SAAA,CAAAlK,EAAA,GAAAkK,SAAA,CAAApK,EAAA;cAArI8J,iBAAiB,GAAAM,SAAA,CAAArM,EAAA,CAASiN,IAAI,CAAAhX,IAAA,CAAAoW,SAAA,CAAArM,EAAA,EAAAqM,SAAA,CAAAlK,EAAA;cAC9B6J,WAAW,GAAGT,cAAc,CAACQ,iBAAiB,CAAC;cAC/CE,QAAQ,GAAG7P,gBAAgB,CAAC8O,eAAe,CAACa,iBAAiB,CAAC,CAAC;cAC/DxK,aAAa,GAAGpF,gBAAgB,CAACmF,OAAO,CAAC;cAAA+K,SAAA,CAAAjU,IAAA;cAAA,OAsDtBuG,QAAQ,CAACkB,eAAe,CAAC;gBAChDzC,SAAS,EAAE;kBACT8O;gBACF,CAAC;gBACD7O,QAAQ,EAAE0C,QAAQ,CAAC1C,QAAQ;gBAC3BmB;cACF,CAAC,CAAC;YAAA;cANI2N,UAAU,GAAAE,SAAA,CAAAxU,IAAA;cAAA,MAOZgH,KAAK,CAACzB,SAAS,CAACQ,CAAC,KAAKuO,UAAU,CAAC/O,SAAS,CAACQ,CAAC,IAAIiB,KAAK,CAACzB,SAAS,CAAC9G,CAAC,KAAK6V,UAAU,CAAC/O,SAAS,CAAC9G,CAAC,IAAIuI,KAAK,CAACzB,SAAS,CAACS,KAAK,KAAKsO,UAAU,CAAC/O,SAAS,CAACS,KAAK,IAAIgB,KAAK,CAACzB,SAAS,CAACW,MAAM,KAAKoO,UAAU,CAAC/O,SAAS,CAACW,MAAM;gBAAAsO,SAAA,CAAAjU,IAAA;gBAAA;cAAA;cAAA,OAAAiU,SAAA,CAAArU,MAAA,WACzM;gBACLe,KAAK,EAAE;kBACL8F,KAAK,EAAEsN;gBACT;cACF,CAAC;YAAA;cAAA,OAAAE,SAAA,CAAArU,MAAA,WAEI,CAAC,CAAC;YAAA;YAAA;cAAA,OAAAqU,SAAA,CAAAnS,IAAA;UAAA;QAAA,GAAA0R,QAAA;MAAA;IACX;EACF,CAAC;AACH,CAAC;;AAED;AACA;AAAA,SAEesB,oBAAoBA,CAAAC,GAAA,EAAAC,GAAA;EAAA,OAAAC,qBAAA,CAAAvS,KAAA,OAAAE,SAAA;AAAA;AAwCnC;AACA;AACA;AACA;AACA;AACA;AACA;AANA,SAAAqS,sBAAA;EAAAA,qBAAA,GAAA3R,iBAAA,cAAAxH,mBAAA,GAAAoF,IAAA,CAxCA,SAAAgU,UAAoC7M,KAAK,EAAEC,OAAO;IAAA,IAAAxD,SAAA,EAAAyB,QAAA,EAAAoB,QAAA,EAAA5C,GAAA,EAAAM,IAAA,EAAA+H,SAAA,EAAA9H,UAAA,EAAA6P,aAAA,EAAAC,cAAA,EAAAC,QAAA,EAAAC,KAAA,EAAA5D,QAAA,EAAAzD,SAAA,EAAA9I,aAAA;IAAA,OAAArJ,mBAAA,GAAAuB,IAAA,UAAAkY,WAAAC,UAAA;MAAA,kBAAAA,UAAA,CAAA7T,IAAA,GAAA6T,UAAA,CAAAxV,IAAA;QAAA;UAE9C8E,SAAS,GAGPuD,KAAK,CAHPvD,SAAS,EACTyB,QAAQ,GAEN8B,KAAK,CAFP9B,QAAQ,EACRoB,QAAQ,GACNU,KAAK,CADPV,QAAQ;UAAA6N,UAAA,CAAAxV,IAAA;UAAA,OAESuG,QAAQ,CAACiB,KAAK,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGjB,QAAQ,CAACiB,KAAK,CAACG,QAAQ,CAAC1C,QAAQ,CAAC;QAAA;UAAhFF,GAAG,GAAAyQ,UAAA,CAAA/V,IAAA;UACH4F,IAAI,GAAGzB,OAAO,CAACkB,SAAS,CAAC;UACzBsI,SAAS,GAAGvJ,YAAY,CAACiB,SAAS,CAAC;UACnCQ,UAAU,GAAG7B,WAAW,CAACqB,SAAS,CAAC,KAAK,GAAG;UAC3CqQ,aAAa,GAAG,CAAC,MAAM,EAAE,KAAK,CAAC,CAACM,QAAQ,CAACpQ,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC;UACvD+P,cAAc,GAAGrQ,GAAG,IAAIO,UAAU,GAAG,CAAC,CAAC,GAAG,CAAC;UAC3C+P,QAAQ,GAAGvR,QAAQ,CAACwE,OAAO,EAAED,KAAK,CAAC,EAEzC;UAAAiN,KAAA,GAKI,OAAOD,QAAQ,KAAK,QAAQ,GAAG;YACjC3D,QAAQ,EAAE2D,QAAQ;YAClBpH,SAAS,EAAE,CAAC;YACZ9I,aAAa,EAAE;UACjB,CAAC,GAAG;YACFuM,QAAQ,EAAE2D,QAAQ,CAAC3D,QAAQ,IAAI,CAAC;YAChCzD,SAAS,EAAEoH,QAAQ,CAACpH,SAAS,IAAI,CAAC;YAClC9I,aAAa,EAAEkQ,QAAQ,CAAClQ;UAC1B,CAAC,EAXCuM,QAAQ,GAAA4D,KAAA,CAAR5D,QAAQ,EACRzD,SAAS,GAAAqH,KAAA,CAATrH,SAAS,EACT9I,aAAa,GAAAmQ,KAAA,CAAbnQ,aAAa;UAUf,IAAIiI,SAAS,IAAI,OAAOjI,aAAa,KAAK,QAAQ,EAAE;YAClD8I,SAAS,GAAGb,SAAS,KAAK,KAAK,GAAGjI,aAAa,GAAG,CAAC,CAAC,GAAGA,aAAa;UACtE;UAAC,OAAAqQ,UAAA,CAAA5V,MAAA,WACM0F,UAAU,GAAG;YAClBE,CAAC,EAAEyI,SAAS,GAAGmH,cAAc;YAC7BlX,CAAC,EAAEwT,QAAQ,GAAGyD;UAChB,CAAC,GAAG;YACF3P,CAAC,EAAEkM,QAAQ,GAAGyD,aAAa;YAC3BjX,CAAC,EAAE+P,SAAS,GAAGmH;UACjB,CAAC;QAAA;QAAA;UAAA,OAAAI,UAAA,CAAA1T,IAAA;MAAA;IAAA,GAAAoT,SAAA;EAAA,CACF;EAAA,OAAAD,qBAAA,CAAAvS,KAAA,OAAAE,SAAA;AAAA;AASD,IAAMgK,MAAM,GAAG,SAATA,MAAMA,CAAatE,OAAO,EAAE;EAChC,IAAIA,OAAO,KAAK,KAAK,CAAC,EAAE;IACtBA,OAAO,GAAG,CAAC;EACb;EACA,OAAO;IACLrH,IAAI,EAAE,QAAQ;IACdqH,OAAO;IACDvB,EAAEA,CAACsB,KAAK,EAAE;MAAA,OAAA/E,iBAAA,cAAAxH,mBAAA,GAAAoF,IAAA,UAAAwU,SAAA;QAAA,IAAAC,qBAAA,EAAA5F,qBAAA,EAAAvK,CAAA,EAAAtH,CAAA,EAAA4G,SAAA,EAAA8B,cAAA,EAAAgP,UAAA;QAAA,OAAA9Z,mBAAA,GAAAuB,IAAA,UAAAwY,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAnU,IAAA,GAAAmU,SAAA,CAAA9V,IAAA;YAAA;cAGZwF,CAAC,GAIC6C,KAAK,CAJP7C,CAAC,EACDtH,CAAC,GAGCmK,KAAK,CAHPnK,CAAC,EACD4G,SAAS,GAEPuD,KAAK,CAFPvD,SAAS,EACT8B,cAAc,GACZyB,KAAK,CADPzB,cAAc;cAAAkP,SAAA,CAAA9V,IAAA;cAAA,OAES8U,oBAAoB,CAACzM,KAAK,EAAEC,OAAO,CAAC;YAAA;cAAvDsN,UAAU,GAAAE,SAAA,CAAArW,IAAA;cAAA,MAIZqF,SAAS,MAAM,CAAC6Q,qBAAqB,GAAG/O,cAAc,CAACgG,MAAM,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG+I,qBAAqB,CAAC7Q,SAAS,CAAC,IAAI,CAACiL,qBAAqB,GAAGnJ,cAAc,CAAC4E,KAAK,KAAK,IAAI,IAAIuE,qBAAqB,CAACjD,eAAe;gBAAAgJ,SAAA,CAAA9V,IAAA;gBAAA;cAAA;cAAA,OAAA8V,SAAA,CAAAlW,MAAA,WAChN,CAAC,CAAC;YAAA;cAAA,OAAAkW,SAAA,CAAAlW,MAAA,WAEJ;gBACL4F,CAAC,EAAEA,CAAC,GAAGoQ,UAAU,CAACpQ,CAAC;gBACnBtH,CAAC,EAAEA,CAAC,GAAG0X,UAAU,CAAC1X,CAAC;gBACnBiJ,IAAI,EAAAxE,aAAA,CAAAA,aAAA,KACCiT,UAAU;kBACb9Q;gBAAS;cAEb,CAAC;YAAA;YAAA;cAAA,OAAAgR,SAAA,CAAAhU,IAAA;UAAA;QAAA,GAAA4T,QAAA;MAAA;IACH;EACF,CAAC;AACH,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,IAAMK,KAAK,GAAG,SAARA,KAAKA,CAAazN,OAAO,EAAE;EAC/B,IAAIA,OAAO,KAAK,KAAK,CAAC,EAAE;IACtBA,OAAO,GAAG,CAAC,CAAC;EACd;EACA,OAAO;IACLrH,IAAI,EAAE,OAAO;IACbqH,OAAO;IACDvB,EAAEA,CAACsB,KAAK,EAAE;MAAA,OAAA/E,iBAAA,cAAAxH,mBAAA,GAAAoF,IAAA,UAAA8U,SAAA;QAAA,IAAAxQ,CAAA,EAAAtH,CAAA,EAAA4G,SAAA,EAAAmR,UAAA,EAAAC,mBAAA,EAAA/F,aAAA,EAAAgG,oBAAA,EAAA9F,cAAA,EAAA+F,kBAAA,EAAAC,OAAA,EAAAjI,qBAAA,EAAAvI,MAAA,EAAAyI,QAAA,EAAAL,SAAA,EAAAyD,QAAA,EAAA4E,aAAA,EAAAC,cAAA,EAAAC,OAAA,EAAAC,OAAA,EAAAC,IAAA,EAAAC,IAAA,EAAAC,QAAA,EAAAC,QAAA,EAAAC,KAAA,EAAAC,KAAA,EAAAC,aAAA;QAAA,OAAAlb,mBAAA,GAAAuB,IAAA,UAAA4Z,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAvV,IAAA,GAAAuV,SAAA,CAAAlX,IAAA;YAAA;cAEZwF,CAAC,GAGC6C,KAAK,CAHP7C,CAAC,EACDtH,CAAC,GAECmK,KAAK,CAFPnK,CAAC,EACD4G,SAAS,GACPuD,KAAK,CADPvD,SAAS;cAAAmR,UAAA,GAkBPnS,QAAQ,CAACwE,OAAO,EAAED,KAAK,CAAC,EAAA6N,mBAAA,GAAAD,UAAA,CAf1BvE,QAAQ,EAAEvB,aAAa,GAAA+F,mBAAA,cAAG,IAAI,GAAAA,mBAAA,EAAAC,oBAAA,GAAAF,UAAA,CAC9BhI,SAAS,EAAEoC,cAAc,GAAA8F,oBAAA,cAAG,KAAK,GAAAA,oBAAA,EAAAC,kBAAA,GAAAH,UAAA,CACjCI,OAAO,EAAPA,OAAO,GAAAD,kBAAA,cAAG;gBACRrP,EAAE,EAAE,SAAJA,EAAEA,CAAElC,IAAI,EAAI;kBACV,IACEW,CAAC,GAECX,IAAI,CAFNW,CAAC;oBACDtH,CAAC,GACC2G,IAAI,CADN3G,CAAC;kBAEH,OAAO;oBACLsH,CAAC;oBACDtH;kBACF,CAAC;gBACH;cACF,CAAC,GAAAkY,kBAAA,EACEhI,qBAAqB,GAAAc,wBAAA,CAAA+G,UAAA,EAAAkB,UAAA;cAEpBtR,MAAM,GAAG;gBACbL,CAAC;gBACDtH;cACF,CAAC;cAAAgZ,SAAA,CAAAlX,IAAA;cAAA,OACsBgI,cAAc,CAACK,KAAK,EAAE+F,qBAAqB,CAAC;YAAA;cAA7DE,QAAQ,GAAA4I,SAAA,CAAAzX,IAAA;cACRwO,SAAS,GAAGxK,WAAW,CAACG,OAAO,CAACkB,SAAS,CAAC,CAAC;cAC3C4M,QAAQ,GAAG/M,eAAe,CAACsJ,SAAS,CAAC;cACvCqI,aAAa,GAAGzQ,MAAM,CAAC6L,QAAQ,CAAC;cAChC6E,cAAc,GAAG1Q,MAAM,CAACoI,SAAS,CAAC;cACtC,IAAIkC,aAAa,EAAE;gBACXqG,OAAO,GAAG9E,QAAQ,KAAK,GAAG,GAAG,KAAK,GAAG,MAAM;gBAC3C+E,OAAO,GAAG/E,QAAQ,KAAK,GAAG,GAAG,QAAQ,GAAG,OAAO;gBAC/CzN,IAAG,GAAGqS,aAAa,GAAGhI,QAAQ,CAACkI,OAAO,CAAC;gBACvC9R,IAAG,GAAG4R,aAAa,GAAGhI,QAAQ,CAACmI,OAAO,CAAC;gBAC7CH,aAAa,GAAGpS,KAAK,CAACD,IAAG,EAAEqS,aAAa,EAAE5R,IAAG,CAAC;cAChD;cACA,IAAI2L,cAAc,EAAE;gBACZmG,QAAO,GAAGvI,SAAS,KAAK,GAAG,GAAG,KAAK,GAAG,MAAM;gBAC5CwI,QAAO,GAAGxI,SAAS,KAAK,GAAG,GAAG,QAAQ,GAAG,OAAO;gBAChDhK,KAAG,GAAGsS,cAAc,GAAGjI,QAAQ,CAACkI,QAAO,CAAC;gBACxC9R,KAAG,GAAG6R,cAAc,GAAGjI,QAAQ,CAACmI,QAAO,CAAC;gBAC9CF,cAAc,GAAGrS,KAAK,CAACD,KAAG,EAAEsS,cAAc,EAAE7R,KAAG,CAAC;cAClD;cACMsS,aAAa,GAAGX,OAAO,CAACtP,EAAE,CAAApE,aAAA,CAAAA,aAAA,KAC3B0F,KAAK;gBACR,CAACqJ,QAAQ,GAAG4E,aAAa;gBACzB,CAACrI,SAAS,GAAGsI;cAAc,EAC5B,CAAC;cAAA,OAAAW,SAAA,CAAAtX,MAAA,WAAA+C,aAAA,CAAAA,aAAA,KAEGqU,aAAa;gBAChB7P,IAAI,EAAE;kBACJ3B,CAAC,EAAEwR,aAAa,CAACxR,CAAC,GAAGA,CAAC;kBACtBtH,CAAC,EAAE8Y,aAAa,CAAC9Y,CAAC,GAAGA,CAAC;kBACtBkZ,OAAO,EAAE;oBACP,CAAC1F,QAAQ,GAAGvB,aAAa;oBACzB,CAAClC,SAAS,GAAGoC;kBACf;gBACF;cAAC;YAAA;YAAA;cAAA,OAAA6G,SAAA,CAAApV,IAAA;UAAA;QAAA,GAAAkU,QAAA;MAAA;IAEL;EACF,CAAC;AACH,CAAC;AACD;AACA;AACA;AACA,IAAMqB,UAAU,GAAG,SAAbA,UAAUA,CAAa/O,OAAO,EAAE;EACpC,IAAIA,OAAO,KAAK,KAAK,CAAC,EAAE;IACtBA,OAAO,GAAG,CAAC,CAAC;EACd;EACA,OAAO;IACLA,OAAO;IACPvB,EAAEA,CAACsB,KAAK,EAAE;MACR,IACE7C,CAAC,GAKC6C,KAAK,CALP7C,CAAC;QACDtH,CAAC,GAICmK,KAAK,CAJPnK,CAAC;QACD4G,SAAS,GAGPuD,KAAK,CAHPvD,SAAS;QACT2B,KAAK,GAEH4B,KAAK,CAFP5B,KAAK;QACLG,cAAc,GACZyB,KAAK,CADPzB,cAAc;MAEhB,IAAA0Q,UAAA,GAIIxT,QAAQ,CAACwE,OAAO,EAAED,KAAK,CAAC;QAAAkP,iBAAA,GAAAD,UAAA,CAH1B1K,MAAM;QAANA,MAAM,GAAA2K,iBAAA,cAAG,CAAC,GAAAA,iBAAA;QAAAC,mBAAA,GAAAF,UAAA,CACV5F,QAAQ;QAAEvB,aAAa,GAAAqH,mBAAA,cAAG,IAAI,GAAAA,mBAAA;QAAAC,oBAAA,GAAAH,UAAA,CAC9BrJ,SAAS;QAAEoC,cAAc,GAAAoH,oBAAA,cAAG,IAAI,GAAAA,oBAAA;MAElC,IAAM5R,MAAM,GAAG;QACbL,CAAC;QACDtH;MACF,CAAC;MACD,IAAM+P,SAAS,GAAGxK,WAAW,CAACqB,SAAS,CAAC;MACxC,IAAM4M,QAAQ,GAAG/M,eAAe,CAACsJ,SAAS,CAAC;MAC3C,IAAIqI,aAAa,GAAGzQ,MAAM,CAAC6L,QAAQ,CAAC;MACpC,IAAI6E,cAAc,GAAG1Q,MAAM,CAACoI,SAAS,CAAC;MACtC,IAAMyJ,SAAS,GAAG5T,QAAQ,CAAC8I,MAAM,EAAEvE,KAAK,CAAC;MACzC,IAAMsP,cAAc,GAAG,OAAOD,SAAS,KAAK,QAAQ,GAAG;QACrDhG,QAAQ,EAAEgG,SAAS;QACnBzJ,SAAS,EAAE;MACb,CAAC,GAAAtL,aAAA;QACC+O,QAAQ,EAAE,CAAC;QACXzD,SAAS,EAAE;MAAC,GACTyJ,SAAS,CACb;MACD,IAAIvH,aAAa,EAAE;QACjB,IAAMyH,GAAG,GAAGlG,QAAQ,KAAK,GAAG,GAAG,QAAQ,GAAG,OAAO;QACjD,IAAMmG,QAAQ,GAAGpR,KAAK,CAACzB,SAAS,CAAC0M,QAAQ,CAAC,GAAGjL,KAAK,CAACxB,QAAQ,CAAC2S,GAAG,CAAC,GAAGD,cAAc,CAACjG,QAAQ;QAC1F,IAAMoG,QAAQ,GAAGrR,KAAK,CAACzB,SAAS,CAAC0M,QAAQ,CAAC,GAAGjL,KAAK,CAACzB,SAAS,CAAC4S,GAAG,CAAC,GAAGD,cAAc,CAACjG,QAAQ;QAC3F,IAAI4E,aAAa,GAAGuB,QAAQ,EAAE;UAC5BvB,aAAa,GAAGuB,QAAQ;QAC1B,CAAC,MAAM,IAAIvB,aAAa,GAAGwB,QAAQ,EAAE;UACnCxB,aAAa,GAAGwB,QAAQ;QAC1B;MACF;MACA,IAAIzH,cAAc,EAAE;QAClB,IAAIsF,qBAAqB,EAAEoC,sBAAsB;QACjD,IAAMH,IAAG,GAAGlG,QAAQ,KAAK,GAAG,GAAG,OAAO,GAAG,QAAQ;QACjD,IAAMsG,YAAY,GAAG,CAAC,KAAK,EAAE,MAAM,CAAC,CAACvC,QAAQ,CAAC7R,OAAO,CAACkB,SAAS,CAAC,CAAC;QACjE,IAAM+S,SAAQ,GAAGpR,KAAK,CAACzB,SAAS,CAACiJ,SAAS,CAAC,GAAGxH,KAAK,CAACxB,QAAQ,CAAC2S,IAAG,CAAC,IAAII,YAAY,GAAG,CAAC,CAACrC,qBAAqB,GAAG/O,cAAc,CAACgG,MAAM,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG+I,qBAAqB,CAAC1H,SAAS,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,IAAI+J,YAAY,GAAG,CAAC,GAAGL,cAAc,CAAC1J,SAAS,CAAC;QACnP,IAAM6J,SAAQ,GAAGrR,KAAK,CAACzB,SAAS,CAACiJ,SAAS,CAAC,GAAGxH,KAAK,CAACzB,SAAS,CAAC4S,IAAG,CAAC,IAAII,YAAY,GAAG,CAAC,GAAG,CAAC,CAACD,sBAAsB,GAAGnR,cAAc,CAACgG,MAAM,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGmL,sBAAsB,CAAC9J,SAAS,CAAC,KAAK,CAAC,CAAC,IAAI+J,YAAY,GAAGL,cAAc,CAAC1J,SAAS,GAAG,CAAC,CAAC;QACtP,IAAIsI,cAAc,GAAGsB,SAAQ,EAAE;UAC7BtB,cAAc,GAAGsB,SAAQ;QAC3B,CAAC,MAAM,IAAItB,cAAc,GAAGuB,SAAQ,EAAE;UACpCvB,cAAc,GAAGuB,SAAQ;QAC3B;MACF;MACA,OAAO;QACL,CAACpG,QAAQ,GAAG4E,aAAa;QACzB,CAACrI,SAAS,GAAGsI;MACf,CAAC;IACH;EACF,CAAC;AACH,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA,IAAM0B,IAAI,GAAG,SAAPA,IAAIA,CAAa3P,OAAO,EAAE;EAC9B,IAAIA,OAAO,KAAK,KAAK,CAAC,EAAE;IACtBA,OAAO,GAAG,CAAC,CAAC;EACd;EACA,OAAO;IACLrH,IAAI,EAAE,MAAM;IACZqH,OAAO;IACDvB,EAAEA,CAACsB,KAAK,EAAE;MAAA,OAAA/E,iBAAA,cAAAxH,mBAAA,GAAAoF,IAAA,UAAAgX,SAAA;QAAA,IAAAC,qBAAA,EAAAC,sBAAA,EAAAtT,SAAA,EAAA2B,KAAA,EAAAF,QAAA,EAAAoB,QAAA,EAAA0Q,UAAA,EAAAC,gBAAA,EAAA5V,KAAA,EAAA0L,qBAAA,EAAAE,QAAA,EAAAjJ,IAAA,EAAA+H,SAAA,EAAAtB,OAAA,EAAAyM,eAAA,EAAA9S,KAAA,EAAAE,MAAA,EAAA6S,UAAA,EAAAC,SAAA,EAAAC,qBAAA,EAAAC,oBAAA,EAAAC,uBAAA,EAAAC,sBAAA,EAAAC,OAAA,EAAAC,eAAA,EAAAC,cAAA,EAAAC,IAAA,EAAAC,IAAA,EAAAC,IAAA,EAAAC,IAAA,EAAAC,cAAA;QAAA,OAAAvd,mBAAA,GAAAuB,IAAA,UAAAic,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAA5X,IAAA,GAAA4X,SAAA,CAAAvZ,IAAA;YAAA;cAGZ8E,SAAS,GAIPuD,KAAK,CAJPvD,SAAS,EACT2B,KAAK,GAGH4B,KAAK,CAHP5B,KAAK,EACLF,QAAQ,GAEN8B,KAAK,CAFP9B,QAAQ,EACRoB,QAAQ,GACNU,KAAK,CADPV,QAAQ;cAAA0Q,UAAA,GAKNvU,QAAQ,CAACwE,OAAO,EAAED,KAAK,CAAC,EAAAiQ,gBAAA,GAAAD,UAAA,CAF1B3V,KAAK,EAALA,KAAK,GAAA4V,gBAAA,cAAG,YAAM,CAAC,CAAC,GAAAA,gBAAA,EACblK,qBAAqB,GAAAc,wBAAA,CAAAmJ,UAAA,EAAAmB,UAAA;cAAAD,SAAA,CAAAvZ,IAAA;cAAA,OAEHgI,cAAc,CAACK,KAAK,EAAE+F,qBAAqB,CAAC;YAAA;cAA7DE,QAAQ,GAAAiL,SAAA,CAAA9Z,IAAA;cACR4F,IAAI,GAAGzB,OAAO,CAACkB,SAAS,CAAC;cACzBsI,SAAS,GAAGvJ,YAAY,CAACiB,SAAS,CAAC;cACnCgH,OAAO,GAAGrI,WAAW,CAACqB,SAAS,CAAC,KAAK,GAAG;cAAAyT,eAAA,GAI1C9R,KAAK,CAACxB,QAAQ,EAFhBQ,KAAK,GAAA8S,eAAA,CAAL9S,KAAK,EACLE,MAAM,GAAA4S,eAAA,CAAN5S,MAAM;cAAA,MAIJN,IAAI,KAAK,KAAK,IAAIA,IAAI,KAAK,QAAQ;gBAAAkU,SAAA,CAAAvZ,IAAA;gBAAA;cAAA;cACrCwY,UAAU,GAAGnT,IAAI;cAACkU,SAAA,CAAA3R,EAAA,GACNwF,SAAS;cAAAmM,SAAA,CAAAvZ,IAAA;cAAA,OAAcuG,QAAQ,CAACiB,KAAK,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGjB,QAAQ,CAACiB,KAAK,CAACG,QAAQ,CAAC1C,QAAQ,CAAC;YAAA;cAAA,KAAAsU,SAAA,CAAA9Z,IAAA;gBAAA8Z,SAAA,CAAAvZ,IAAA;gBAAA;cAAA;cAAAuZ,SAAA,CAAA1P,EAAA,GAAK,OAAO;cAAA0P,SAAA,CAAAvZ,IAAA;cAAA;YAAA;cAAAuZ,SAAA,CAAA1P,EAAA,GAAG,KAAK;YAAA;cAAA0P,SAAA,CAAAxP,EAAA,GAAAwP,SAAA,CAAA1P,EAAA;cAAA,MAAA0P,SAAA,CAAA3R,EAAA,KAAA2R,SAAA,CAAAxP,EAAA;gBAAAwP,SAAA,CAAAvZ,IAAA;gBAAA;cAAA;cAAAuZ,SAAA,CAAAvP,EAAA,GAAI,MAAM;cAAAuP,SAAA,CAAAvZ,IAAA;cAAA;YAAA;cAAAuZ,SAAA,CAAAvP,EAAA,GAAG,OAAO;YAAA;cAA9IyO,SAAS,GAAAc,SAAA,CAAAvP,EAAA;cAAAuP,SAAA,CAAAvZ,IAAA;cAAA;YAAA;cAETyY,SAAS,GAAGpT,IAAI;cAChBmT,UAAU,GAAGpL,SAAS,KAAK,KAAK,GAAG,KAAK,GAAG,QAAQ;YAAC;cAEhDsL,qBAAqB,GAAG/S,MAAM,GAAG2I,QAAQ,CAAClD,GAAG,GAAGkD,QAAQ,CAACjD,MAAM;cAC/DsN,oBAAoB,GAAGlT,KAAK,GAAG6I,QAAQ,CAAChD,IAAI,GAAGgD,QAAQ,CAAC/C,KAAK;cAC7DqN,uBAAuB,GAAG3U,GAAG,CAAC0B,MAAM,GAAG2I,QAAQ,CAACkK,UAAU,CAAC,EAAEE,qBAAqB,CAAC;cACnFG,sBAAsB,GAAG5U,GAAG,CAACwB,KAAK,GAAG6I,QAAQ,CAACmK,SAAS,CAAC,EAAEE,oBAAoB,CAAC;cAC/EG,OAAO,GAAG,CAACzQ,KAAK,CAACzB,cAAc,CAACmP,KAAK;cACvCgD,eAAe,GAAGH,uBAAuB;cACzCI,cAAc,GAAGH,sBAAsB;cAC3C,IAAI,CAACV,qBAAqB,GAAG9P,KAAK,CAACzB,cAAc,CAACmP,KAAK,KAAK,IAAI,IAAIoC,qBAAqB,CAACf,OAAO,CAAC5R,CAAC,EAAE;gBACnGwT,cAAc,GAAGL,oBAAoB;cACvC;cACA,IAAI,CAACP,sBAAsB,GAAG/P,KAAK,CAACzB,cAAc,CAACmP,KAAK,KAAK,IAAI,IAAIqC,sBAAsB,CAAChB,OAAO,CAAClZ,CAAC,EAAE;gBACrG6a,eAAe,GAAGL,qBAAqB;cACzC;cACA,IAAII,OAAO,IAAI,CAAC1L,SAAS,EAAE;gBACnB6L,IAAI,GAAGvU,GAAG,CAAC4J,QAAQ,CAAChD,IAAI,EAAE,CAAC,CAAC;gBAC5B4N,IAAI,GAAGxU,GAAG,CAAC4J,QAAQ,CAAC/C,KAAK,EAAE,CAAC,CAAC;gBAC7B4N,IAAI,GAAGzU,GAAG,CAAC4J,QAAQ,CAAClD,GAAG,EAAE,CAAC,CAAC;gBAC3BgO,IAAI,GAAG1U,GAAG,CAAC4J,QAAQ,CAACjD,MAAM,EAAE,CAAC,CAAC;gBACpC,IAAIS,OAAO,EAAE;kBACXkN,cAAc,GAAGvT,KAAK,GAAG,CAAC,IAAIwT,IAAI,KAAK,CAAC,IAAIC,IAAI,KAAK,CAAC,GAAGD,IAAI,GAAGC,IAAI,GAAGxU,GAAG,CAAC4J,QAAQ,CAAChD,IAAI,EAAEgD,QAAQ,CAAC/C,KAAK,CAAC,CAAC;gBAC5G,CAAC,MAAM;kBACLwN,eAAe,GAAGpT,MAAM,GAAG,CAAC,IAAIwT,IAAI,KAAK,CAAC,IAAIC,IAAI,KAAK,CAAC,GAAGD,IAAI,GAAGC,IAAI,GAAG1U,GAAG,CAAC4J,QAAQ,CAAClD,GAAG,EAAEkD,QAAQ,CAACjD,MAAM,CAAC,CAAC;gBAC9G;cACF;cAACkO,SAAA,CAAAvZ,IAAA;cAAA,OACK0C,KAAK,CAAAC,aAAA,CAAAA,aAAA,KACN0F,KAAK;gBACR2Q,cAAc;gBACdD;cAAe,EAChB,CAAC;YAAA;cAAAQ,SAAA,CAAAvZ,IAAA;cAAA,OAC2BuG,QAAQ,CAAC0G,aAAa,CAACtF,QAAQ,CAAC1C,QAAQ,CAAC;YAAA;cAAhEoU,cAAc,GAAAE,SAAA,CAAA9Z,IAAA;cAAA,MAChBgG,KAAK,KAAK4T,cAAc,CAAC5T,KAAK,IAAIE,MAAM,KAAK0T,cAAc,CAAC1T,MAAM;gBAAA4T,SAAA,CAAAvZ,IAAA;gBAAA;cAAA;cAAA,OAAAuZ,SAAA,CAAA3Z,MAAA,WAC7D;gBACLe,KAAK,EAAE;kBACL8F,KAAK,EAAE;gBACT;cACF,CAAC;YAAA;cAAA,OAAA8S,SAAA,CAAA3Z,MAAA,WAEI,CAAC,CAAC;YAAA;YAAA;cAAA,OAAA2Z,SAAA,CAAAzX,IAAA;UAAA;QAAA,GAAAoW,QAAA;MAAA;IACX;EACF,CAAC;AACH,CAAC;AAED,SAAS1M,KAAK,EAAEkC,aAAa,EAAE5H,eAAe,EAAEkC,cAAc,EAAE6H,IAAI,EAAEmC,IAAI,EAAEuB,MAAM,EAAE8D,UAAU,EAAEzK,MAAM,EAAEmJ,KAAK,EAAEkC,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
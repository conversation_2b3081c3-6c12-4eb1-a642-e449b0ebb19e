{"ast": null, "code": "import { ref, onMounted } from 'vue';\nimport { useRoute } from 'vue-router';\nimport { ElMessage } from 'element-plus';\nimport LiveBroadcastDetails from './LiveBroadcastDetails.vue';\nvar __default__ = {\n  name: 'LiveShare'\n};\nexport default /*@__PURE__*/Object.assign(__default__, {\n  setup(__props, _ref) {\n    var __expose = _ref.expose;\n    __expose();\n    var route = useRoute();\n    // const router = useRouter()\n    var liveId = ref('');\n    onMounted(function () {\n      // 从路由参数获取直播ID\n      liveId.value = route.params.id;\n      if (!liveId.value) {\n        ElMessage.error('直播ID不能为空');\n        // 可以跳转到错误页面或首页\n        return;\n      }\n    });\n\n    // 处理关闭回调 - 分享页面关闭时跳转到首页或显示提示\n    var handleCallback = function handleCallback() {\n      // 可以根据需要跳转到首页或其他页面\n      ElMessage.info('感谢观看，您可以通过浏览器返回按钮离开此页面');\n    };\n\n    // 处理模型值更新 - 分享页面允许关闭\n    var handleModelValueUpdate = function handleModelValueUpdate(value) {\n      if (!value) {\n        // 用户点击关闭按钮时的处理\n        handleCallback();\n      }\n    };\n    var __returned__ = {\n      route,\n      liveId,\n      handleCallback,\n      handleModelValueUpdate,\n      ref,\n      onMounted,\n      get useRoute() {\n        return useRoute;\n      },\n      get ElMessage() {\n        return ElMessage;\n      },\n      LiveBroadcastDetails\n    };\n    Object.defineProperty(__returned__, '__isScriptSetup', {\n      enumerable: false,\n      value: true\n    });\n    return __returned__;\n  }\n});", "map": {"version": 3, "names": ["ref", "onMounted", "useRoute", "ElMessage", "LiveBroadcastDetails", "__default__", "name", "route", "liveId", "value", "params", "id", "error", "handleCallback", "info", "handleModelValueUpdate"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/microApp/interaction/src/views/LiveManagement/LiveShare.vue"], "sourcesContent": ["<template>\n  <div class=\"LiveShare\">\n    <!-- 直播详情组件，始终显示 -->\n    <LiveBroadcastDetails :model-value=\"true\" :id=\"liveId\" @callback=\"handleCallback\"\n      @update:modelValue=\"handleModelValueUpdate\" />\n  </div>\n</template>\n\n<script>\nexport default { name: 'LiveShare' }\n</script>\n\n<script setup>\nimport { ref, onMounted } from 'vue'\nimport { useRoute } from 'vue-router'\nimport { ElMessage } from 'element-plus'\nimport LiveBroadcastDetails from './LiveBroadcastDetails.vue'\n\nconst route = useRoute()\n// const router = useRouter()\nconst liveId = ref('')\n\nonMounted(() => {\n  // 从路由参数获取直播ID\n  liveId.value = route.params.id\n\n  if (!liveId.value) {\n    ElMessage.error('直播ID不能为空')\n    // 可以跳转到错误页面或首页\n    return\n  }\n})\n\n// 处理关闭回调 - 分享页面关闭时跳转到首页或显示提示\nconst handleCallback = () => {\n  // 可以根据需要跳转到首页或其他页面\n  ElMessage.info('感谢观看，您可以通过浏览器返回按钮离开此页面')\n}\n\n// 处理模型值更新 - 分享页面允许关闭\nconst handleModelValueUpdate = (value) => {\n  if (!value) {\n    // 用户点击关闭按钮时的处理\n    handleCallback()\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.LiveShare {\n  width: 100%;\n  height: 100vh;\n  overflow: hidden;\n}\n</style>\n"], "mappings": "AAaA,SAASA,GAAG,EAAEC,SAAS,QAAQ,KAAI;AACnC,SAASC,QAAQ,QAAQ,YAAW;AACpC,SAASC,SAAS,QAAQ,cAAa;AACvC,OAAOC,oBAAoB,MAAM,4BAA2B;AAP5D,IAAAC,WAAA,GAAe;EAAEC,IAAI,EAAE;AAAY;;;;;IASnC,IAAMC,KAAK,GAAGL,QAAQ,CAAC;IACvB;IACA,IAAMM,MAAM,GAAGR,GAAG,CAAC,EAAE;IAErBC,SAAS,CAAC,YAAM;MACd;MACAO,MAAM,CAACC,KAAK,GAAGF,KAAK,CAACG,MAAM,CAACC,EAAC;MAE7B,IAAI,CAACH,MAAM,CAACC,KAAK,EAAE;QACjBN,SAAS,CAACS,KAAK,CAAC,UAAU;QAC1B;QACA;MACF;IACF,CAAC;;IAED;IACA,IAAMC,cAAc,GAAG,SAAjBA,cAAcA,CAAA,EAAS;MAC3B;MACAV,SAAS,CAACW,IAAI,CAAC,wBAAwB;IACzC;;IAEA;IACA,IAAMC,sBAAsB,GAAG,SAAzBA,sBAAsBA,CAAIN,KAAK,EAAK;MACxC,IAAI,CAACA,KAAK,EAAE;QACV;QACAI,cAAc,CAAC;MACjB;IACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "import { defineComponent, inject, h } from 'vue';\nimport '../../../hooks/index.mjs';\nimport { treeNodeContentProps, ROOT_TREE_INJECTION_KEY } from './virtual-tree.mjs';\nimport { useNamespace } from '../../../hooks/use-namespace/index.mjs';\nvar ElNodeContent = defineComponent({\n  name: \"ElTreeNodeContent\",\n  props: treeNodeContentProps,\n  setup(props) {\n    var tree = inject(ROOT_TREE_INJECTION_KEY);\n    var ns = useNamespace(\"tree\");\n    return function () {\n      var node = props.node;\n      var data = node.data;\n      return (tree == null ? void 0 : tree.ctx.slots.default) ? tree.ctx.slots.default({\n        node,\n        data\n      }) : h(\"span\", {\n        class: ns.be(\"node\", \"label\")\n      }, [node == null ? void 0 : node.label]);\n    };\n  }\n});\nexport { ElNodeContent as default };", "map": {"version": 3, "names": ["ElNodeContent", "defineComponent", "name", "props", "treeNodeContentProps", "setup", "tree", "inject", "ROOT_TREE_INJECTION_KEY", "ns", "useNamespace", "node", "data", "ctx", "slots", "default", "h", "class", "be", "label"], "sources": ["../../../../../../packages/components/tree-v2/src/tree-node-content.ts"], "sourcesContent": ["import { defineComponent, h, inject } from 'vue'\nimport { useNamespace } from '@element-plus/hooks'\nimport { ROOT_TREE_INJECTION_KEY, treeNodeContentProps } from './virtual-tree'\n\nexport default defineComponent({\n  name: 'ElTreeNodeContent',\n  props: treeNodeContentProps,\n  setup(props) {\n    const tree = inject(ROOT_TREE_INJECTION_KEY)\n    const ns = useNamespace('tree')\n    return () => {\n      const node = props.node\n      const { data } = node!\n      return tree?.ctx.slots.default\n        ? tree.ctx.slots.default({ node, data })\n        : h('span', { class: ns.be('node', 'label') }, [node?.label])\n    }\n  },\n})\n"], "mappings": ";;;;AAGA,IAAAA,aAAA,GAAeC,eAAe,CAAC;EAC7BC,IAAI,EAAE,mBAAmB;EACzBC,KAAK,EAAEC,oBAAoB;EAC3BC,KAAKA,CAACF,KAAK,EAAE;IACX,IAAMG,IAAI,GAAGC,MAAM,CAACC,uBAAuB,CAAC;IAC5C,IAAMC,EAAE,GAAGC,YAAY,CAAC,MAAM,CAAC;IAC/B,OAAO,YAAM;MACX,IAAMC,IAAI,GAAGR,KAAK,CAACQ,IAAI;MACvB,IAAQC,IAAI,GAAKD,IAAI,CAAbC,IAAI;MACZ,OAAO,CAACN,IAAI,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,IAAI,CAACO,GAAG,CAACC,KAAK,CAACC,OAAO,IAAIT,IAAI,CAACO,GAAG,CAACC,KAAK,CAACC,OAAO,CAAC;QAAEJ,IAAI;QAAEC;MAAI,CAAE,CAAC,GAAGI,CAAC,CAAC,MAAM,EAAE;QAAEC,KAAK,EAAER,EAAE,CAACS,EAAE,CAAC,MAAM,EAAE,OAAO;MAAC,CAAE,EAAE,CAACP,IAAI,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,IAAI,CAACQ,KAAK,CAAC,CAAC;IAC3L,CAAK;EACL;AACA,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
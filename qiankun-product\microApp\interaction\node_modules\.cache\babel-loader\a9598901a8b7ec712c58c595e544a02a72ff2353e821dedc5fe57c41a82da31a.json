{"ast": null, "code": "import { openBlock as _openBlock, createElementBlock as _createElementBlock, createCommentVNode as _createCommentVNode, toDisplayString as _toDisplayString, createElementVNode as _createElementVNode, createTextVNode as _createTextVNode, resolveComponent as _resolveComponent, withCtx as _withCtx, createVNode as _createVNode, createBlock as _createBlock, Fragment as _Fragment, normalizeClass as _normalizeClass, renderList as _renderList, vModelText as _vModelText, withKeys as _withKeys, withDirectives as _withDirectives, vShow as _vShow, Transition as _Transition } from \"vue\";\nimport _imports_0 from '../../assets/img/icon_live_time.png';\nimport _imports_1 from '../../assets/img/icon_live_send.png';\nvar _hoisted_1 = {\n  class: \"LiveBroadcastDetails\"\n};\nvar _hoisted_2 = {\n  class: \"LiveBroadcastDetailsHeader\"\n};\nvar _hoisted_3 = [\"src\"];\nvar _hoisted_4 = {\n  class: \"LiveBroadcastDetailsHeadInfo\"\n};\nvar _hoisted_5 = {\n  class: \"LiveBroadcastDetailsTitle\"\n};\nvar _hoisted_6 = {\n  class: \"LiveBroadcastDetailsTime\"\n};\nvar _hoisted_7 = {\n  class: \"LiveBroadcastDetailsActions\"\n};\nvar _hoisted_8 = {\n  class: \"LiveBroadcastDetailsBody\"\n};\nvar _hoisted_9 = {\n  class: \"LiveBroadcastDetailsCanvas\"\n};\nvar _hoisted_10 = {\n  key: 0,\n  class: \"LiveBroadcastDetailsPoster\"\n};\nvar _hoisted_11 = [\"src\"];\nvar _hoisted_12 = {\n  class: \"LiveBroadcastDetailsLiveOverlay\"\n};\nvar _hoisted_13 = {\n  class: \"LiveBroadcastDetailsNoStream\"\n};\nvar _hoisted_14 = [\"src\"];\nvar _hoisted_15 = {\n  class: \"LiveBroadcastDetailsEnded\"\n};\nvar _hoisted_16 = [\"src\"];\nvar _hoisted_17 = {\n  class: \"LiveBroadcastDetailsEndedWrap\"\n};\nvar _hoisted_18 = {\n  class: \"LiveBroadcastDetailsSidebar\"\n};\nvar _hoisted_19 = {\n  class: \"LiveBroadcastDetailsTabs\"\n};\nvar _hoisted_20 = {\n  key: 0,\n  class: \"LiveBroadcastDetailsTabPane\"\n};\nvar _hoisted_21 = {\n  class: \"detailsTitle\"\n};\nvar _hoisted_22 = {\n  class: \"detailsTimeBox\"\n};\nvar _hoisted_23 = {\n  class: \"detailsTime\"\n};\nvar _hoisted_24 = {\n  class: \"detailsDesc\"\n};\nvar _hoisted_25 = {\n  key: 1,\n  class: \"LiveBroadcastDetailsTabPane interact-pane\"\n};\nvar _hoisted_26 = {\n  class: \"comments-container\",\n  ref: \"commentsContainer\"\n};\nvar _hoisted_27 = {\n  class: \"comment-main\"\n};\nvar _hoisted_28 = {\n  class: \"comment-content\"\n};\nvar _hoisted_29 = {\n  class: \"comment-header\"\n};\nvar _hoisted_30 = {\n  class: \"comment-nickname\"\n};\nvar _hoisted_31 = {\n  class: \"comment-time\"\n};\nvar _hoisted_32 = {\n  class: \"comment-text\"\n};\nvar _hoisted_33 = {\n  class: \"comment-actions\"\n};\nvar _hoisted_34 = [\"onClick\"];\nvar _hoisted_35 = [\"onClick\"];\nvar _hoisted_36 = [\"src\"];\nvar _hoisted_37 = {\n  class: \"like-count\"\n};\nvar _hoisted_38 = {\n  key: 0,\n  class: \"replies-container\"\n};\nvar _hoisted_39 = {\n  class: \"comment-content\"\n};\nvar _hoisted_40 = {\n  class: \"comment-header\"\n};\nvar _hoisted_41 = {\n  class: \"comment-nickname\"\n};\nvar _hoisted_42 = {\n  class: \"comment-time\"\n};\nvar _hoisted_43 = {\n  class: \"comment-text\"\n};\nvar _hoisted_44 = {\n  class: \"comment-actions\"\n};\nvar _hoisted_45 = [\"onClick\"];\nvar _hoisted_46 = [\"src\"];\nvar _hoisted_47 = {\n  class: \"like-count\"\n};\nvar _hoisted_48 = {\n  key: 1,\n  class: \"reply-input-container\"\n};\nvar _hoisted_49 = {\n  class: \"reply-input-wrapper\"\n};\nvar _hoisted_50 = [\"placeholder\", \"onKeyup\"];\nvar _hoisted_51 = {\n  class: \"reply-actions\"\n};\nvar _hoisted_52 = [\"onClick\", \"disabled\"];\nvar _hoisted_53 = {\n  key: 0,\n  class: \"comments-empty\"\n};\nvar _hoisted_54 = {\n  class: \"comment-input-area\"\n};\nvar _hoisted_55 = {\n  class: \"input-wrapper\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  var _component_el_button = _resolveComponent(\"el-button\");\n  var _component_Close = _resolveComponent(\"Close\");\n  var _component_el_icon = _resolveComponent(\"el-icon\");\n  return _openBlock(), _createElementBlock(_Fragment, null, [_createVNode(_Transition, {\n    name: \"details-fade\",\n    persisted: \"\"\n  }, {\n    default: _withCtx(function () {\n      var _$setup$details, _$setup$details2, _$setup$details3;\n      return [_withDirectives(_createElementVNode(\"div\", _hoisted_1, [_createElementVNode(\"div\", _hoisted_2, [$setup.logoSrc ? (_openBlock(), _createElementBlock(\"img\", {\n        key: 0,\n        class: \"LiveBroadcastDetailsEmblem\",\n        src: $setup.logoSrc,\n        alt: \"emblem\"\n      }, null, 8 /* PROPS */, _hoisted_3)) : _createCommentVNode(\"v-if\", true), _createElementVNode(\"div\", _hoisted_4, [_createElementVNode(\"div\", _hoisted_5, _toDisplayString($setup.details.theme), 1 /* TEXT */), _createElementVNode(\"div\", _hoisted_6, \"直播时间：\" + _toDisplayString($setup.format($setup.details.startTime)) + \" 到 \" + _toDisplayString($setup.format($setup.details.endTime)), 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_7, [_createVNode(_component_el_button, {\n        type: \"primary\",\n        onClick: $setup.handleShare,\n        class: \"share-btn\"\n      }, {\n        default: _withCtx(function () {\n          return _cache[5] || (_cache[5] = [_createTextVNode(\" 分享直播 \")]);\n        }),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_icon, {\n        class: \"LiveBroadcastDetailsClose\",\n        onClick: $setup.handleClose\n      }, {\n        default: _withCtx(function () {\n          return [_createVNode(_component_Close)];\n        }),\n        _: 1 /* STABLE */\n      })])]), _createElementVNode(\"div\", _hoisted_8, [_createElementVNode(\"div\", _hoisted_9, [_createCommentVNode(\" 未开始：显示封面图 + 倒计时 \"), $setup.details.meetingStatus === '未开始' ? (_openBlock(), _createElementBlock(\"div\", _hoisted_10, [_createElementVNode(\"img\", {\n        src: $setup.imgUrl,\n        alt: \"海报/播放画面区域\",\n        style: {\n          \"width\": \"100%\",\n          \"height\": \"100%\",\n          \"object-fit\": \"contain\"\n        }\n      }, null, 8 /* PROPS */, _hoisted_11), _createVNode($setup[\"CountdownTimer\"], {\n        \"start-time\": $setup.details.startTime,\n        onCountdownEnd: $setup.handleCountdownEnd,\n        ref: \"countdownTimerRef\"\n      }, null, 8 /* PROPS */, [\"start-time\"])])) : $setup.details.meetingStatus === '进行中' ? (_openBlock(), _createElementBlock(_Fragment, {\n        key: 1\n      }, [_createCommentVNode(\" 进行中：直播播放器 \"), _createElementVNode(\"div\", _hoisted_12, [$setup.details.liveUrl ? (_openBlock(), _createBlock($setup[\"VideoPlayer\"], {\n        key: `video-player-live-${$setup.props.id}-${$setup.details.meetingStatus}-${$setup.isReplayMode}`,\n        \"live-url\": $setup.details.liveUrl,\n        \"replay-url\": $setup.details.liveReplayUrl,\n        \"is-replay\": false,\n        ref: \"videoPlayerRef\"\n      }, null, 8 /* PROPS */, [\"live-url\", \"replay-url\"])) : (_openBlock(), _createElementBlock(_Fragment, {\n        key: 1\n      }, [_createCommentVNode(\" 没有推流地址时显示提示 \"), _createElementVNode(\"div\", _hoisted_13, [_createElementVNode(\"img\", {\n        src: $setup.imgUrl,\n        alt: \"直播背景\",\n        class: \"no-stream-bg\"\n      }, null, 8 /* PROPS */, _hoisted_14), _cache[6] || (_cache[6] = _createElementVNode(\"div\", {\n        class: \"no-stream-wrap\"\n      }, [_createElementVNode(\"div\", {\n        class: \"no-stream-title\"\n      }, \"暂无配置直播地址\")], -1 /* HOISTED */))])], 2112 /* STABLE_FRAGMENT, DEV_ROOT_FRAGMENT */))])], 2112 /* STABLE_FRAGMENT, DEV_ROOT_FRAGMENT */)) : $setup.details.meetingStatus === '已结束' ? (_openBlock(), _createElementBlock(_Fragment, {\n        key: 2\n      }, [_createCommentVNode(\" 已结束：回放播放器或背景图 + 悬浮内容 \"), _createElementVNode(\"div\", _hoisted_15, [_createCommentVNode(\" 回放模式：显示回放播放器 \"), $setup.isReplayMode && $setup.details.liveReplayUrl ? (_openBlock(), _createBlock($setup[\"VideoPlayer\"], {\n        key: `video-player-replay-${$setup.props.id}-${$setup.details.meetingStatus}-${$setup.isReplayMode}`,\n        \"live-url\": $setup.details.liveUrl,\n        \"replay-url\": $setup.details.liveReplayUrl,\n        \"is-replay\": true,\n        ref: \"videoPlayerRef\"\n      }, null, 8 /* PROPS */, [\"live-url\", \"replay-url\"])) : (_openBlock(), _createElementBlock(_Fragment, {\n        key: 1\n      }, [_createCommentVNode(\" 非回放模式：显示背景图和按钮 \"), _createCommentVNode(\" 背景图 \"), _createElementVNode(\"img\", {\n        src: $setup.imgUrl,\n        alt: \"直播背景\",\n        class: \"LiveBroadcastDetailsEndedBg\"\n      }, null, 8 /* PROPS */, _hoisted_16), _createCommentVNode(\" 悬浮的结束状态内容 \"), _createElementVNode(\"div\", _hoisted_17, [_cache[8] || (_cache[8] = _createElementVNode(\"div\", {\n        class: \"endedTitle\"\n      }, \"直播已结束\", -1 /* HOISTED */)), _createCommentVNode(\" 有回放地址：显示观看回放按钮 \"), $setup.details.isReplay == 1 ? (_openBlock(), _createBlock(_component_el_button, {\n        key: 0,\n        type: \"primary\",\n        class: \"replayBtn\",\n        onClick: $setup.handleReplay\n      }, {\n        default: _withCtx(function () {\n          return _cache[7] || (_cache[7] = [_createTextVNode(\" 观看回放 \")]);\n        }),\n        _: 1 /* STABLE */\n      })) : _createCommentVNode(\"v-if\", true)])], 64 /* STABLE_FRAGMENT */))])], 2112 /* STABLE_FRAGMENT, DEV_ROOT_FRAGMENT */)) : _createCommentVNode(\"v-if\", true)]), _createElementVNode(\"div\", _hoisted_18, [_createElementVNode(\"div\", _hoisted_19, [_createElementVNode(\"div\", {\n        class: _normalizeClass(['LiveBroadcastDetailsTab', {\n          active: $setup.activeTab === 'details'\n        }]),\n        onClick: _cache[0] || (_cache[0] = function ($event) {\n          return $setup.activeTab = 'details';\n        })\n      }, \" 直播详情\", 2 /* CLASS */), _createElementVNode(\"div\", {\n        class: _normalizeClass(['LiveBroadcastDetailsTab', {\n          active: $setup.activeTab === 'interact'\n        }]),\n        onClick: $setup.handleInteractTabClick\n      }, \" 互动\", 2 /* CLASS */)]), $setup.activeTab === 'details' ? (_openBlock(), _createElementBlock(\"div\", _hoisted_20, [_createElementVNode(\"div\", _hoisted_21, _toDisplayString(((_$setup$details = $setup.details) === null || _$setup$details === void 0 ? void 0 : _$setup$details.theme) || '-'), 1 /* TEXT */), _createElementVNode(\"div\", _hoisted_22, [_cache[9] || (_cache[9] = _createElementVNode(\"img\", {\n        src: _imports_0,\n        alt: \"\",\n        class: \"detailsTimeIcon\"\n      }, null, -1 /* HOISTED */)), _createElementVNode(\"span\", _hoisted_23, \"时间：\" + _toDisplayString($setup.format((_$setup$details2 = $setup.details) === null || _$setup$details2 === void 0 ? void 0 : _$setup$details2.startTime)) + \" - \" + _toDisplayString($setup.format((_$setup$details3 = $setup.details) === null || _$setup$details3 === void 0 ? void 0 : _$setup$details3.endTime)), 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_24, _toDisplayString($setup.details.liveDescribes || '暂无简介'), 1 /* TEXT */)])) : (_openBlock(), _createElementBlock(\"div\", _hoisted_25, [_createCommentVNode(\" 评论列表 \"), _createElementVNode(\"div\", _hoisted_26, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.comments, function (comment) {\n        return _openBlock(), _createElementBlock(\"div\", {\n          key: comment.id,\n          class: \"comment-item\"\n        }, [_createCommentVNode(\" 主评论 \"), _createElementVNode(\"div\", _hoisted_27, [_createElementVNode(\"div\", _hoisted_28, [_createElementVNode(\"div\", _hoisted_29, [_createElementVNode(\"span\", _hoisted_30, _toDisplayString(comment.commentUserName), 1 /* TEXT */), _createElementVNode(\"span\", _hoisted_31, _toDisplayString($setup.format(comment.createDate)), 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_32, _toDisplayString(comment.commentContent), 1 /* TEXT */), _createElementVNode(\"div\", _hoisted_33, [_createElementVNode(\"span\", {\n          class: \"action-item\",\n          onClick: function onClick($event) {\n            return $setup.showReplyInput(comment.id);\n          }\n        }, \"跟帖互动\", 8 /* PROPS */, _hoisted_34), _createElementVNode(\"span\", {\n          class: _normalizeClass([\"action-item like-btn\", {\n            liked: comment.hasClickPraises\n          }]),\n          onClick: function onClick($event) {\n            return $setup.toggleLike(comment);\n          }\n        }, [_createElementVNode(\"img\", {\n          src: comment.hasClickPraises ? require('../../assets/img/fabulous_o.png') : require('../../assets/img/fabulous.png'),\n          alt: \"点赞\",\n          class: \"like-icon\"\n        }, null, 8 /* PROPS */, _hoisted_36), _createElementVNode(\"span\", _hoisted_37, \"(\" + _toDisplayString(comment.praisesCount || 0) + \")\", 1 /* TEXT */)], 10 /* CLASS, PROPS */, _hoisted_35)])])]), _createCommentVNode(\" 回复列表 \"), comment.children && comment.children.length > 0 ? (_openBlock(), _createElementBlock(\"div\", _hoisted_38, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList(comment.children, function (reply) {\n          return _openBlock(), _createElementBlock(\"div\", {\n            key: reply.id,\n            class: \"reply-item\"\n          }, [_createElementVNode(\"div\", _hoisted_39, [_createElementVNode(\"div\", _hoisted_40, [_createElementVNode(\"span\", _hoisted_41, _toDisplayString(reply.commentUserName), 1 /* TEXT */), _createCommentVNode(\" <span v-if=\\\"reply.replyTo\\\" class=\\\"reply-to\\\">回复 @{{ reply.replyTo }}</span> \"), _createElementVNode(\"span\", _hoisted_42, _toDisplayString($setup.format(reply.createDate)), 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_43, _toDisplayString(reply.commentContent), 1 /* TEXT */), _createElementVNode(\"div\", _hoisted_44, [_createElementVNode(\"span\", {\n            class: _normalizeClass([\"action-item like-btn\", {\n              liked: reply.hasClickPraises\n            }]),\n            onClick: function onClick($event) {\n              return $setup.toggleLike(reply);\n            }\n          }, [_createElementVNode(\"img\", {\n            src: reply.hasClickPraises ? require('../../assets/img/fabulous_o.png') : require('../../assets/img/fabulous.png'),\n            alt: \"点赞\",\n            class: \"like-icon\"\n          }, null, 8 /* PROPS */, _hoisted_46), _createElementVNode(\"span\", _hoisted_47, \"(\" + _toDisplayString(reply.praisesCount || 0) + \")\", 1 /* TEXT */)], 10 /* CLASS, PROPS */, _hoisted_45)])])]);\n        }), 128 /* KEYED_FRAGMENT */))])) : _createCommentVNode(\"v-if\", true), _createCommentVNode(\" 回复输入框 \"), $setup.replyingTo === comment.id ? (_openBlock(), _createElementBlock(\"div\", _hoisted_48, [_createElementVNode(\"div\", _hoisted_49, [_withDirectives(_createElementVNode(\"input\", {\n          \"onUpdate:modelValue\": _cache[1] || (_cache[1] = function ($event) {\n            return $setup.replyContent = $event;\n          }),\n          type: \"text\",\n          placeholder: $setup.replyToUser ? `回复 @${$setup.replyToUser}:` : '写下你的回复...',\n          class: \"reply-input\",\n          onKeyup: _withKeys(function ($event) {\n            return $setup.submitReply(comment.id);\n          }, [\"enter\"]),\n          maxlength: \"200\",\n          ref_for: true,\n          ref: \"replyInputRef\"\n        }, null, 40 /* PROPS, NEED_HYDRATION */, _hoisted_50), [[_vModelText, $setup.replyContent]]), _createElementVNode(\"div\", _hoisted_51, [_createElementVNode(\"button\", {\n          class: \"cancel-btn\",\n          onClick: $setup.cancelReply\n        }, \"取消\"), _createElementVNode(\"button\", {\n          class: \"submit-btn\",\n          onClick: function onClick($event) {\n            return $setup.submitReply(comment.id);\n          },\n          disabled: !$setup.replyContent.trim()\n        }, \" 回复 \", 8 /* PROPS */, _hoisted_52)])])])) : _createCommentVNode(\"v-if\", true)]);\n      }), 128 /* KEYED_FRAGMENT */)), _createCommentVNode(\" 空状态 \"), $setup.comments.length === 0 ? (_openBlock(), _createElementBlock(\"div\", _hoisted_53, _cache[10] || (_cache[10] = [_createElementVNode(\"div\", {\n        class: \"empty-text\"\n      }, \"暂无评论\", -1 /* HOISTED */)]))) : _createCommentVNode(\"v-if\", true)], 512 /* NEED_PATCH */), _createCommentVNode(\" 发表评论输入框 \"), _createElementVNode(\"div\", _hoisted_54, [_createElementVNode(\"div\", _hoisted_55, [_withDirectives(_createElementVNode(\"textarea\", {\n        \"onUpdate:modelValue\": _cache[2] || (_cache[2] = function ($event) {\n          return $setup.newComment = $event;\n        }),\n        placeholder: \"说点什么...\",\n        class: \"comment-input\",\n        onKeyup: _withKeys($setup.submitComment, [\"enter\"]),\n        maxlength: \"200\",\n        rows: \"3\",\n        style: {\n          \"width\": \"100%\",\n          \"height\": \"100%\"\n        }\n      }, null, 544 /* NEED_HYDRATION, NEED_PATCH */), [[_vModelText, $setup.newComment]]), _createElementVNode(\"div\", {\n        class: \"submit-comment-btn\",\n        onClick: $setup.submitComment\n      }, _cache[11] || (_cache[11] = [_createElementVNode(\"div\", {\n        class: \"comment-btn\"\n      }, \"发送\", -1 /* HOISTED */), _createElementVNode(\"img\", {\n        src: _imports_1,\n        alt: \"发送\",\n        class: \"send-icon\"\n      }, null, -1 /* HOISTED */)]))])])]))])])], 512 /* NEED_PATCH */), [[_vShow, $props.modelValue]])];\n    }),\n    _: 1 /* STABLE */\n  }), _createCommentVNode(\" 分享弹窗 \"), _createVNode($setup[\"ShareDialog\"], {\n    modelValue: $setup.shareDialogVisible,\n    \"onUpdate:modelValue\": _cache[3] || (_cache[3] = function ($event) {\n      return $setup.shareDialogVisible = $event;\n    }),\n    \"live-id\": $setup.props.id,\n    \"live-data\": $setup.details\n  }, null, 8 /* PROPS */, [\"modelValue\", \"live-id\", \"live-data\"]), _createCommentVNode(\" 手机号验证弹窗 \"), _createVNode($setup[\"PhoneVerifyDialog\"], {\n    modelValue: $setup.phoneVerifyVisible,\n    \"onUpdate:modelValue\": _cache[4] || (_cache[4] = function ($event) {\n      return $setup.phoneVerifyVisible = $event;\n    }),\n    onVerifySuccess: $setup.handleVerifySuccess\n  }, null, 8 /* PROPS */, [\"modelValue\"])], 64 /* STABLE_FRAGMENT */);\n}", "map": {"version": 3, "names": ["_imports_0", "_imports_1", "class", "key", "ref", "_createElementBlock", "_Fragment", "_createVNode", "_Transition", "name", "persisted", "default", "_withCtx", "_$setup$details", "_$setup$details2", "_$setup$details3", "_createElementVNode", "_hoisted_1", "_hoisted_2", "$setup", "logoSrc", "src", "alt", "_hoisted_3", "_createCommentVNode", "_hoisted_4", "_hoisted_5", "_toDisplayString", "details", "theme", "_hoisted_6", "format", "startTime", "endTime", "_hoisted_7", "_component_el_button", "type", "onClick", "handleShare", "_cache", "_createTextVNode", "_", "_component_el_icon", "handleClose", "_component_Close", "_hoisted_8", "_hoisted_9", "meetingStatus", "_hoisted_10", "imgUrl", "style", "_hoisted_11", "onCountdownEnd", "handleCountdownEnd", "_hoisted_12", "liveUrl", "_createBlock", "props", "id", "isReplayMode", "liveReplayUrl", "_hoisted_13", "_hoisted_14", "_hoisted_15", "_hoisted_16", "_hoisted_17", "isReplay", "handleReplay", "_hoisted_18", "_hoisted_19", "_normalizeClass", "active", "activeTab", "$event", "handleInteractTabClick", "_hoisted_20", "_hoisted_21", "_hoisted_22", "_hoisted_23", "_hoisted_24", "liveDescribes", "_hoisted_25", "_hoisted_26", "_renderList", "comments", "comment", "_hoisted_27", "_hoisted_28", "_hoisted_29", "_hoisted_30", "commentUserName", "_hoisted_31", "createDate", "_hoisted_32", "commentContent", "_hoisted_33", "showReplyInput", "_hoisted_34", "liked", "hasClickPraises", "toggleLike", "require", "_hoisted_36", "_hoisted_37", "praisesCount", "_hoisted_35", "children", "length", "_hoisted_38", "reply", "_hoisted_39", "_hoisted_40", "_hoisted_41", "_hoisted_42", "_hoisted_43", "_hoisted_44", "_hoisted_46", "_hoisted_47", "_hoisted_45", "replyingTo", "_hoisted_48", "_hoisted_49", "replyContent", "placeholder", "replyToUser", "onKeyup", "_with<PERSON><PERSON><PERSON>", "submitReply", "maxlength", "ref_for", "_hoisted_50", "_hoisted_51", "cancelReply", "disabled", "trim", "_hoisted_52", "_hoisted_53", "_hoisted_54", "_hoisted_55", "newComment", "submitComment", "rows", "$props", "modelValue", "shareDialogVisible", "phoneVerifyVisible", "onVerifySuccess", "handleVerifySuccess"], "sources": ["D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\interaction\\src\\views\\LiveManagement\\LiveBroadcastDetails.vue"], "sourcesContent": ["<template>\r\n  <transition name=\"details-fade\">\r\n    <div v-show=\"modelValue\" class=\"LiveBroadcastDetails\">\r\n      <div class=\"LiveBroadcastDetailsHeader\">\r\n        <img v-if=\"logoSrc\" class=\"LiveBroadcastDetailsEmblem\" :src=\"logoSrc\" alt=\"emblem\" />\r\n        <div class=\"LiveBroadcastDetailsHeadInfo\">\r\n          <div class=\"LiveBroadcastDetailsTitle\">{{ details.theme }}</div>\r\n          <div class=\"LiveBroadcastDetailsTime\">直播时间：{{ format(details.startTime) }} 到 {{\r\n            format(details.endTime) }}</div>\r\n        </div>\r\n        <div class=\"LiveBroadcastDetailsActions\">\r\n          <el-button type=\"primary\" @click=\"handleShare\" class=\"share-btn\">\r\n            分享直播\r\n          </el-button>\r\n          <el-icon class=\"LiveBroadcastDetailsClose\" @click=\"handleClose\">\r\n            <Close />\r\n          </el-icon>\r\n        </div>\r\n      </div>\r\n      <div class=\"LiveBroadcastDetailsBody\">\r\n        <div class=\"LiveBroadcastDetailsCanvas\">\r\n          <!-- 未开始：显示封面图 + 倒计时 -->\r\n          <div v-if=\"details.meetingStatus === '未开始'\" class=\"LiveBroadcastDetailsPoster\">\r\n            <img :src=\"imgUrl\" alt=\"海报/播放画面区域\" style=\"width: 100%;height: 100%;object-fit: contain;\">\r\n            <CountdownTimer :start-time=\"details.startTime\" @countdown-end=\"handleCountdownEnd\"\r\n              ref=\"countdownTimerRef\" />\r\n          </div>\r\n          <!-- 进行中：直播播放器 -->\r\n          <div v-else-if=\"details.meetingStatus === '进行中'\" class=\"LiveBroadcastDetailsLiveOverlay\">\r\n            <VideoPlayer v-if=\"details.liveUrl\"\r\n              :key=\"`video-player-live-${props.id}-${details.meetingStatus}-${isReplayMode}`\"\r\n              :live-url=\"details.liveUrl\" :replay-url=\"details.liveReplayUrl\" :is-replay=\"false\" ref=\"videoPlayerRef\" />\r\n            <!-- 没有推流地址时显示提示 -->\r\n            <div v-else class=\"LiveBroadcastDetailsNoStream\">\r\n              <img :src=\"imgUrl\" alt=\"直播背景\" class=\"no-stream-bg\">\r\n              <div class=\"no-stream-wrap\">\r\n                <div class=\"no-stream-title\">暂无配置直播地址</div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n          <!-- 已结束：回放播放器或背景图 + 悬浮内容 -->\r\n          <div v-else-if=\"details.meetingStatus === '已结束'\" class=\"LiveBroadcastDetailsEnded\">\r\n            <!-- 回放模式：显示回放播放器 -->\r\n            <VideoPlayer v-if=\"isReplayMode && details.liveReplayUrl\"\r\n              :key=\"`video-player-replay-${props.id}-${details.meetingStatus}-${isReplayMode}`\"\r\n              :live-url=\"details.liveUrl\" :replay-url=\"details.liveReplayUrl\" :is-replay=\"true\" ref=\"videoPlayerRef\" />\r\n            <!-- 非回放模式：显示背景图和按钮 -->\r\n            <template v-else>\r\n              <!-- 背景图 -->\r\n              <img :src=\"imgUrl\" alt=\"直播背景\" class=\"LiveBroadcastDetailsEndedBg\">\r\n              <!-- 悬浮的结束状态内容 -->\r\n              <div class=\"LiveBroadcastDetailsEndedWrap\">\r\n                <div class=\"endedTitle\">直播已结束</div>\r\n                <!-- 有回放地址：显示观看回放按钮 -->\r\n                <el-button type=\"primary\" class=\"replayBtn\" v-if=\"details.isReplay == 1\" @click=\"handleReplay\">\r\n                  观看回放\r\n                </el-button>\r\n              </div>\r\n            </template>\r\n          </div>\r\n        </div>\r\n        <div class=\"LiveBroadcastDetailsSidebar\">\r\n          <div class=\"LiveBroadcastDetailsTabs\">\r\n            <div :class=\"['LiveBroadcastDetailsTab', { active: activeTab === 'details' }]\"\r\n              @click=\"activeTab = 'details'\">\r\n              直播详情</div>\r\n            <div :class=\"['LiveBroadcastDetailsTab', { active: activeTab === 'interact' }]\"\r\n              @click=\"handleInteractTabClick\">\r\n              互动</div>\r\n          </div>\r\n          <div class=\"LiveBroadcastDetailsTabPane\" v-if=\"activeTab === 'details'\">\r\n            <div class=\"detailsTitle\">{{ details?.theme || '-' }}</div>\r\n            <div class=\"detailsTimeBox\">\r\n              <img src=\"../../assets/img/icon_live_time.png\" alt=\"\" class=\"detailsTimeIcon\">\r\n              <span class=\"detailsTime\">时间：{{ format(details?.startTime) }} - {{ format(details?.endTime) }}</span>\r\n            </div>\r\n            <div class=\"detailsDesc\">{{ details.liveDescribes || '暂无简介' }}</div>\r\n          </div>\r\n          <div class=\"LiveBroadcastDetailsTabPane interact-pane\" v-else>\r\n            <!-- 评论列表 -->\r\n            <div class=\"comments-container\" ref=\"commentsContainer\">\r\n              <div v-for=\"comment in comments\" :key=\"comment.id\" class=\"comment-item\">\r\n                <!-- 主评论 -->\r\n                <div class=\"comment-main\">\r\n                  <div class=\"comment-content\">\r\n                    <div class=\"comment-header\">\r\n                      <span class=\"comment-nickname\">{{ comment.commentUserName }}</span>\r\n                      <span class=\"comment-time\">{{ format(comment.createDate) }}</span>\r\n                    </div>\r\n                    <div class=\"comment-text\">{{ comment.commentContent }}</div>\r\n                    <div class=\"comment-actions\">\r\n                      <span class=\"action-item\" @click=\"showReplyInput(comment.id)\">跟帖互动</span>\r\n                      <span class=\"action-item like-btn\" @click=\"toggleLike(comment)\"\r\n                        :class=\"{ liked: comment.hasClickPraises }\">\r\n                        <img\r\n                          :src=\"comment.hasClickPraises ? require('../../assets/img/fabulous_o.png') : require('../../assets/img/fabulous.png')\"\r\n                          alt=\"点赞\" class=\"like-icon\" />\r\n                        <span class=\"like-count\">({{ comment.praisesCount || 0 }})</span>\r\n                      </span>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n\r\n                <!-- 回复列表 -->\r\n                <div v-if=\"comment.children && comment.children.length > 0\" class=\"replies-container\">\r\n                  <div v-for=\"reply in comment.children\" :key=\"reply.id\" class=\"reply-item\">\r\n                    <div class=\"comment-content\">\r\n                      <div class=\"comment-header\">\r\n                        <span class=\"comment-nickname\">{{ reply.commentUserName }}</span>\r\n                        <!-- <span v-if=\"reply.replyTo\" class=\"reply-to\">回复 @{{ reply.replyTo }}</span> -->\r\n                        <span class=\"comment-time\">{{ format(reply.createDate) }}</span>\r\n                      </div>\r\n                      <div class=\"comment-text\">{{ reply.commentContent }}</div>\r\n                      <div class=\"comment-actions\">\r\n                        <span class=\"action-item like-btn\" @click=\"toggleLike(reply)\"\r\n                          :class=\"{ liked: reply.hasClickPraises }\">\r\n                          <img\r\n                            :src=\"reply.hasClickPraises ? require('../../assets/img/fabulous_o.png') : require('../../assets/img/fabulous.png')\"\r\n                            alt=\"点赞\" class=\"like-icon\" />\r\n                          <span class=\"like-count\">({{ reply.praisesCount || 0 }})</span>\r\n                        </span>\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n\r\n                <!-- 回复输入框 -->\r\n                <div v-if=\"replyingTo === comment.id\" class=\"reply-input-container\">\r\n                  <div class=\"reply-input-wrapper\">\r\n                    <input v-model=\"replyContent\" type=\"text\"\r\n                      :placeholder=\"replyToUser ? `回复 @${replyToUser}:` : '写下你的回复...'\" class=\"reply-input\"\r\n                      @keyup.enter=\"submitReply(comment.id)\" maxlength=\"200\" ref=\"replyInputRef\" />\r\n                    <div class=\"reply-actions\">\r\n                      <button class=\"cancel-btn\" @click=\"cancelReply\">取消</button>\r\n                      <button class=\"submit-btn\" @click=\"submitReply(comment.id)\" :disabled=\"!replyContent.trim()\">\r\n                        回复\r\n                      </button>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n\r\n              <!-- 空状态 -->\r\n              <div v-if=\"comments.length === 0\" class=\"comments-empty\">\r\n                <div class=\"empty-text\">暂无评论</div>\r\n              </div>\r\n            </div>\r\n\r\n            <!-- 发表评论输入框 -->\r\n            <div class=\"comment-input-area\">\r\n              <div class=\"input-wrapper\">\r\n                <textarea v-model=\"newComment\" placeholder=\"说点什么...\" class=\"comment-input\" @keyup.enter=\"submitComment\"\r\n                  maxlength=\"200\" rows=\"3\" style=\"width: 100%;height: 100%;\"></textarea>\r\n                <div class=\"submit-comment-btn\" @click=\"submitComment\">\r\n                  <div class=\"comment-btn\">发送</div>\r\n                  <img src=\"../../assets/img/icon_live_send.png\" alt=\"发送\" class=\"send-icon\" />\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </transition>\r\n  <!-- 分享弹窗 -->\r\n  <ShareDialog v-model=\"shareDialogVisible\" :live-id=\"props.id\" :live-data=\"details\" />\r\n\r\n  <!-- 手机号验证弹窗 -->\r\n  <PhoneVerifyDialog v-model=\"phoneVerifyVisible\" @verify-success=\"handleVerifySuccess\" />\r\n</template>\r\n<script>\r\nexport default { name: 'LiveBroadcastDetails' }\r\n</script>\r\n<script setup>\r\nimport api from '@/api'\r\nimport { ref, onBeforeUnmount, onMounted, computed, watch, nextTick } from 'vue'\r\nimport { format } from 'common/js/time.js'\r\nimport { ElMessage } from 'element-plus'\r\nimport CountdownTimer from './components/CountdownTimer.vue'\r\nimport VideoPlayer from './components/VideoPlayer.vue'\r\nimport ShareDialog from './components/ShareDialog.vue'\r\nimport PhoneVerifyDialog from './components/PhoneVerifyDialog.vue'\r\n// import { qiankunMicro } from 'common/config/MicroGlobal'\r\nconst props = defineProps({\r\n  modelValue: { type: Boolean, default: false },\r\n  id: { type: String, default: '' }\r\n})\r\nconst emit = defineEmits(['callback', 'update:modelValue'])\r\nconst details = ref({})\r\nconst logoSrc = ref('https://xazx.cszysoft.com:8131/lzt/pageImg/open/logo?areaId=610100')\r\nconst activeTab = ref('details')\r\nconst imgUrl = computed(() => details.value.coverImg ? api.fileURL(details.value.coverImg) : '')\r\n\r\n// 组件引用\r\nconst countdownTimerRef = ref(null)\r\nconst videoPlayerRef = ref(null)\r\n\r\n// 回放模式标识\r\nconst isReplayMode = ref(false)\r\n// 分享弹窗状态\r\nconst shareDialogVisible = ref(false)\r\n// 手机验证弹窗状态\r\nconst phoneVerifyVisible = ref(false)\r\n// 评论相关数据\r\nconst comments = ref([])\r\nconst newComment = ref('')\r\nconst replyingTo = ref(null)\r\nconst replyContent = ref('')\r\nconst replyToUser = ref('')\r\nconst replyInputRef = ref(null)\r\n// 评论定时刷新\r\nlet commentTimer = null\r\n// 监听状态变化\r\nwatch(() => details.value.meetingStatus, (newStatus, oldStatus) => {\r\n  if (newStatus === '进行中' && oldStatus !== '进行中') {\r\n    // 只有在有推流地址时才初始化播放器\r\n    if (details.value.liveUrl) {\r\n      nextTick(() => {\r\n        if (videoPlayerRef.value) {\r\n          videoPlayerRef.value.initPlayer()\r\n        }\r\n      })\r\n    }\r\n  } else if (newStatus === '已结束' && oldStatus !== '已结束') {\r\n    // 如果是回放模式且有回放地址，初始化回放播放器\r\n    if (isReplayMode.value && details.value.liveReplayUrl) {\r\n      nextTick(() => {\r\n        if (videoPlayerRef.value) {\r\n          videoPlayerRef.value.initPlayer()\r\n        }\r\n      })\r\n    }\r\n  } else if ((newStatus !== '进行中' && oldStatus === '进行中') ||\r\n    (newStatus !== '已结束' && oldStatus === '已结束' && isReplayMode.value)) {\r\n    // 从进行中或回放状态变为其他状态，销毁播放器\r\n    if (videoPlayerRef.value) {\r\n      videoPlayerRef.value.destroyVideoPlayer()\r\n    }\r\n  }\r\n})\r\n\r\n// 倒计时结束处理\r\nconst handleCountdownEnd = () => {\r\n  // 倒计时结束后切换到进行中状态\r\n  if (details.value.meetingStatus === '未开始') {\r\n    isReplayMode.value = false\r\n    details.value.meetingStatus = '进行中'\r\n  }\r\n}\r\n\r\n// 初始化播放器（根据当前状态）\r\nconst initPlayerByStatus = () => {\r\n  if (details.value.meetingStatus === '进行中' && details.value.liveUrl) {\r\n    // 进行中状态且有推流地址，初始化直播播放器\r\n    setTimeout(() => {\r\n      if (videoPlayerRef.value) {\r\n        videoPlayerRef.value.initPlayer()\r\n      }\r\n    }, 100)\r\n  } else if (details.value.meetingStatus === '已结束' && isReplayMode.value && details.value.liveReplayUrl) {\r\n    // 已结束状态且是回放模式且有回放地址，初始化回放播放器\r\n    setTimeout(() => {\r\n      if (videoPlayerRef.value) {\r\n        videoPlayerRef.value.initPlayer()\r\n      }\r\n    }, 100)\r\n  }\r\n}\r\n\r\nonMounted(() => {\r\n  // 在 onMounted 中，如果组件已经显示且有 id，则加载数据\r\n  if (props.modelValue && props.id) {\r\n    // 初始化时检查token状态\r\n    initTokenStatus()\r\n    getInfo()\r\n  }\r\n})\r\n\r\n// 监听 modelValue 变化 - 当组件从隐藏变为显示时加载数据\r\nwatch(() => props.modelValue, (newVal, oldVal) => {\r\n  if (newVal && props.id && !oldVal) {\r\n    // 从 false 变为 true 且有 id 时加载数据\r\n    console.log('弹窗打开，开始加载数据')\r\n    // 重置状态，确保每次打开都是干净的状态\r\n    isReplayMode.value = false\r\n    activeTab.value = 'details'\r\n    // 初始化token状态\r\n    initTokenStatus()\r\n    getInfo()\r\n  } else if (!newVal && oldVal) {\r\n    // 从显示变为隐藏时，停止评论定时刷新\r\n    console.log('弹窗关闭，停止定时刷新')\r\n    stopCommentRefresh()\r\n  }\r\n})\r\n\r\n// 监听 id 变化 - 当 id 改变且组件显示时加载数据\r\nwatch(() => props.id, (newVal, oldVal) => {\r\n  if (newVal && props.modelValue && newVal !== oldVal) {\r\n    getInfo()\r\n  }\r\n})\r\n\r\n// 监听activeTab变化，控制评论刷新\r\nwatch(() => activeTab.value, (newTab, oldTab) => {\r\n  if (newTab === 'details' && oldTab === 'interact') {\r\n    // 从互动tab切换到详情tab，停止评论刷新\r\n    stopCommentRefresh()\r\n  }\r\n})\r\n\r\n// 初始化token状态\r\nconst initTokenStatus = () => {\r\n  // 检查会话存储中是否已有token\r\n  const sessionToken = sessionStorage.getItem('token')\r\n  if (!sessionToken) {\r\n    // 会话存储中没有token，检查本地存储\r\n    const shareToken = localStorage.getItem('shareToken')\r\n    if (shareToken) {\r\n      // 本地存储中有token，转移到会话存储（为了下次进入页面时能直接使用）\r\n      sessionStorage.setItem('token', shareToken)\r\n      console.log('Token已从本地存储转移到会话存储')\r\n    }\r\n  }\r\n}\r\n\r\nconst getInfo = async () => {\r\n  const res = await api.videoConnectionInfo({ detailId: props.id })\r\n  var { data } = res\r\n  details.value = data\r\n  // 只有在会话存储中存在token的情况下才调用getLiveBrowseCount\r\n  const sessionToken = sessionStorage.getItem('token')\r\n  if (sessionToken) {\r\n    getLiveBrowseCount()\r\n  }\r\n  // 根据状态初始化播放器\r\n  initPlayerByStatus()\r\n}\r\n\r\nconst getLiveBrowseCount = async () => {\r\n  const res = await api.liveWatchCountAdd({ form: { dataId: props.id } })\r\n  var { data } = res\r\n  console.log('data===>', data)\r\n}\r\n\r\n// 获取评论数据\r\nconst getCommentData = async () => {\r\n  const res = await api.twoLevelTree({\r\n    businessCode: 'liveBroadcast',\r\n    businessId: props.id,\r\n    pageNo: 1,\r\n    pageSize: 99,\r\n  })\r\n  console.log('评论数据:', res)\r\n  comments.value = res.data || []\r\n}\r\n\r\n// 开始评论定时刷新（只在互动tab时刷新）\r\nconst startCommentRefresh = () => {\r\n  if (commentTimer) clearInterval(commentTimer)\r\n  commentTimer = setInterval(() => {\r\n    // 只有在互动tab时才刷新评论\r\n    if (activeTab.value === 'interact') {\r\n      getCommentData()\r\n    }\r\n  }, 3000) // 每3秒刷新一次\r\n}\r\n\r\n// 停止评论定时刷新\r\nconst stopCommentRefresh = () => {\r\n  if (commentTimer) {\r\n    clearInterval(commentTimer)\r\n    commentTimer = null\r\n  }\r\n}\r\n\r\n// 打开回放\r\nconst handleReplay = () => {\r\n  if (!details.value.liveReplayUrl) {\r\n    ElMessage({ type: 'error', message: '没有回放地址' })\r\n    return\r\n  }\r\n  // 先销毁现有的播放器\r\n  if (videoPlayerRef.value) {\r\n    videoPlayerRef.value.destroyVideoPlayer()\r\n  }\r\n  // 设置回放模式\r\n  isReplayMode.value = true\r\n  // 确保VideoPlayer组件能够正确初始化\r\n  nextTick(() => {\r\n    // 由于key变化，组件会重新创建，需要重新获取ref\r\n    setTimeout(() => {\r\n      if (videoPlayerRef.value) {\r\n        videoPlayerRef.value.initPlayer()\r\n      }\r\n    }, 100)\r\n  })\r\n}\r\n\r\n// 提交新评论\r\nconst submitComment = async () => {\r\n  if (!newComment.value.trim()) return\r\n  const res = await api.commentNew({\r\n    form: {\r\n      businessCode: 'liveBroadcast',\r\n      businessId: props.id,\r\n      checkedStatus: 1,\r\n      commentContent: newComment.value,\r\n      parentId: \"\",\r\n      terminalName: 'PC'\r\n    }\r\n  })\r\n\r\n  if (res.code == 200) {\r\n    ElMessage({ type: 'success', message: '评论发表成功' })\r\n    getCommentData()\r\n  } else {\r\n    ElMessage({ type: 'error', message: res.message || '评论发表失败' })\r\n  }\r\n  newComment.value = ''\r\n}\r\n\r\n// 点赞功能\r\nconst toggleLike = async (item) => {\r\n  if (item.hasClickPraises) {\r\n    // 取消点赞\r\n    const res = await api.praisesDels({\r\n      businessCode: \"comment\",\r\n      businessId: item.id\r\n    })\r\n\r\n    if (res.code == 200) {\r\n      item.hasClickPraises = false\r\n      item.praisesCount = Math.max(0, (item.praisesCount || 0) - 1)\r\n      ElMessage({\r\n        message: '取消点赞成功',\r\n        type: 'success'\r\n      })\r\n    } else {\r\n      ElMessage({\r\n        message: res.message || '取消点赞失败',\r\n        type: 'error'\r\n      })\r\n    }\r\n  } else {\r\n    // 点赞\r\n    const res = await api.praisesAdd({\r\n      form: {\r\n        businessCode: \"comment\",\r\n        businessId: item.id\r\n      }\r\n    })\r\n\r\n    if (res.code == 200) {\r\n      item.hasClickPraises = true\r\n      item.praisesCount = (item.praisesCount || 0) + 1\r\n      ElMessage({\r\n        message: '点赞成功',\r\n        type: 'success'\r\n      })\r\n    } else {\r\n      ElMessage({\r\n        message: res.message || '点赞失败',\r\n        type: 'error'\r\n      })\r\n    }\r\n  }\r\n}\r\n\r\n// 显示回复输入框\r\nconst showReplyInput = (commentId, replyToUsername = '') => {\r\n  replyingTo.value = commentId\r\n  replyToUser.value = replyToUsername\r\n  replyContent.value = ''\r\n\r\n  nextTick(() => {\r\n    if (replyInputRef.value) {\r\n      replyInputRef.value.focus()\r\n    }\r\n  })\r\n}\r\n\r\n// 取消回复\r\nconst cancelReply = () => {\r\n  replyingTo.value = null\r\n  replyToUser.value = ''\r\n  replyContent.value = ''\r\n}\r\n\r\n// 提交回复\r\nconst submitReply = async (commentId) => {\r\n  if (!replyContent.value.trim()) return\r\n  const res = await api.commentNew({\r\n    form: {\r\n      businessCode: \"liveBroadcast\",\r\n      businessId: props.id,\r\n      checkedStatus: 1,\r\n      commentContent: replyContent.value.trim(),\r\n      parentId: commentId,\r\n      terminalName: \"PC\"\r\n    }\r\n  })\r\n  if (res.code == 200) {\r\n    ElMessage({\r\n      message: '回复发表成功',\r\n      type: 'success'\r\n    })\r\n    getCommentData()\r\n    cancelReply()\r\n  } else {\r\n    ElMessage({\r\n      message: res.message || '回复发表失败',\r\n      type: 'error'\r\n    })\r\n  }\r\n}\r\n\r\nonBeforeUnmount(() => {\r\n  // 清理评论定时器\r\n  stopCommentRefresh()\r\n  // 销毁视频播放器\r\n  if (videoPlayerRef.value) {\r\n    videoPlayerRef.value.destroyVideoPlayer()\r\n  }\r\n})\r\n\r\n// 处理互动tab点击\r\nconst handleInteractTabClick = () => {\r\n  // 1. 优先检查会话存储中是否已存在token\r\n  const sessionToken = sessionStorage.getItem('token')\r\n  if (sessionToken) {\r\n    // 会话存储中已有token，直接切换到互动tab\r\n    activeTab.value = 'interact'\r\n    getCommentData()\r\n    startCommentRefresh()\r\n    return\r\n  }\r\n  // 2. 会话存储中没有token，检查本地存储\r\n  const shareToken = localStorage.getItem('shareToken')\r\n  if (shareToken) {\r\n    // 本地存储中有token，转移到会话存储\r\n    sessionStorage.setItem('token', shareToken)\r\n    // 切换到互动tab\r\n    activeTab.value = 'interact'\r\n    getCommentData()\r\n    startCommentRefresh()\r\n  } else {\r\n    // 本地存储中也没有token，弹出手机验证弹窗\r\n    phoneVerifyVisible.value = true\r\n  }\r\n}\r\n\r\n// 手机验证成功回调\r\nconst handleVerifySuccess = (data) => {\r\n  console.log('验证成功:', data)\r\n\r\n  // 1. 先确保token存储到本地存储（PhoneVerifyDialog中已经存储了，这里再确认一次）\r\n  localStorage.setItem('shareToken', data.token)\r\n\r\n  // 2. 检查本地存储，如果有token则转移到会话存储\r\n  const shareToken = localStorage.getItem('shareToken')\r\n  if (shareToken) {\r\n    sessionStorage.setItem('token', shareToken)\r\n  }\r\n\r\n  // 3. 切换到互动tab\r\n  activeTab.value = 'interact'\r\n\r\n  // 4. 调用评论接口\r\n  getCommentData()\r\n  startCommentRefresh()\r\n}\r\n\r\n// 分享直播功能\r\nconst handleShare = () => {\r\n  // qiankunMicro.setGlobalState({ openRoute: { name: '视频会议详情', path: '/interaction/LiveShare', query: { id: '1958437043231023105' } } })\r\n  if (!props.id) {\r\n    ElMessage.error('直播ID不能为空')\r\n    return\r\n  }\r\n\r\n  // 打开分享弹窗\r\n  shareDialogVisible.value = true\r\n}\r\n\r\nconst handleClose = () => {\r\n  // 停止评论定时刷新\r\n  stopCommentRefresh()\r\n  // 销毁视频播放器\r\n  if (videoPlayerRef.value) {\r\n    videoPlayerRef.value.destroyVideoPlayer()\r\n  }\r\n  // 重置状态\r\n  isReplayMode.value = false\r\n  activeTab.value = 'details'\r\n  // 清空数据引用，确保下次打开时重新加载\r\n  details.value = {}\r\n  emit('update:modelValue', false)\r\n  emit('callback')\r\n}\r\n</script>\r\n<style lang=\"scss\">\r\n.LiveBroadcastDetails {\r\n  position: fixed;\r\n  inset: 0;\r\n  width: 100vw;\r\n  height: 100vh;\r\n  background: #0F0F0F;\r\n  display: flex;\r\n  flex-direction: column;\r\n  z-index: 1000;\r\n\r\n  .LiveBroadcastDetailsHeader {\r\n    position: relative;\r\n    height: 80px;\r\n    display: flex;\r\n    align-items: center;\r\n    padding: 0 16px;\r\n    color: #fff;\r\n    background: #2B2B2B;\r\n    border-bottom: 1px solid rgba(255, 255, 255, 0.08);\r\n\r\n    .LiveBroadcastDetailsEmblem {\r\n      width: 58px;\r\n      height: 58px;\r\n      margin-right: 14px;\r\n    }\r\n\r\n    .LiveBroadcastDetailsHeadInfo {\r\n      display: flex;\r\n      flex-direction: column;\r\n    }\r\n\r\n    .LiveBroadcastDetailsTitle {\r\n      font-family: Microsoft YaHei, Microsoft YaHei;\r\n      font-weight: bold;\r\n      font-size: 20px;\r\n      color: #FFFFFF;\r\n      margin-bottom: 12px;\r\n    }\r\n\r\n    .LiveBroadcastDetailsTime {\r\n      font-weight: 400;\r\n      font-size: 14px;\r\n      color: #D9D9D9;\r\n    }\r\n\r\n    .LiveBroadcastDetailsActions {\r\n      position: absolute;\r\n      right: 16px;\r\n      top: 50%;\r\n      transform: translateY(-50%);\r\n      display: flex;\r\n      align-items: center;\r\n      gap: 12px;\r\n\r\n      .share-btn {\r\n        background: linear-gradient(90deg, #2F04FF 0%, #00F0FE 100%);\r\n        border: none;\r\n        color: #FFFFFF;\r\n        font-size: 14px;\r\n        padding: 8px 16px;\r\n        border-radius: 6px;\r\n\r\n        &:hover {\r\n          opacity: 0.9;\r\n        }\r\n      }\r\n    }\r\n\r\n    .LiveBroadcastDetailsClose {\r\n      color: #fff;\r\n      cursor: pointer;\r\n      font-size: 18px;\r\n      opacity: .85;\r\n\r\n      &:hover {\r\n        opacity: 1;\r\n      }\r\n    }\r\n  }\r\n\r\n  .LiveBroadcastDetailsBody {\r\n    flex: 1;\r\n    display: grid;\r\n    grid-template-columns: 1fr 360px;\r\n    gap: 16px;\r\n    padding: 16px;\r\n    overflow: hidden;\r\n  }\r\n\r\n  .LiveBroadcastDetailsCanvas {\r\n    position: relative;\r\n    height: 100%;\r\n    background: #111;\r\n    overflow: hidden;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    z-index: 0;\r\n\r\n    .LiveBroadcastDetailsPoster {\r\n      width: 100%;\r\n      height: 100%;\r\n      background: #000;\r\n      display: flex;\r\n      align-items: center;\r\n      justify-content: center;\r\n      color: #d23a2e;\r\n      font-weight: bold;\r\n      font-size: 22px;\r\n    }\r\n\r\n    .LiveBroadcastDetailsLiveOverlay {\r\n      position: absolute;\r\n      left: 0;\r\n      right: 0;\r\n      top: 0;\r\n      bottom: 0;\r\n      width: 100%;\r\n      height: 100%;\r\n      z-index: 1;\r\n    }\r\n\r\n    .LiveBroadcastDetailsNoStream {\r\n      position: absolute;\r\n      inset: 0;\r\n      display: flex;\r\n      align-items: center;\r\n      justify-content: center;\r\n\r\n      .no-stream-bg {\r\n        position: absolute;\r\n        inset: 0;\r\n        width: 100%;\r\n        height: 100%;\r\n        object-fit: contain;\r\n        z-index: 1;\r\n      }\r\n\r\n      // 遮罩层覆盖整个图片\r\n      &::before {\r\n        content: '';\r\n        position: absolute;\r\n        inset: 0;\r\n        background: rgba(0, 0, 0, 0.45);\r\n        z-index: 2;\r\n      }\r\n\r\n      .no-stream-wrap {\r\n        position: relative;\r\n        z-index: 3;\r\n        text-align: center;\r\n\r\n        .no-stream-title {\r\n          font-weight: 400;\r\n          font-size: 16px;\r\n          color: #FFFFFF;\r\n          text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);\r\n        }\r\n      }\r\n    }\r\n\r\n    .LiveBroadcastDetailsEnded {\r\n      position: absolute;\r\n      inset: 0;\r\n      display: flex;\r\n      align-items: center;\r\n      justify-content: center;\r\n\r\n      .LiveBroadcastDetailsEndedBg {\r\n        position: absolute;\r\n        inset: 0;\r\n        width: 100%;\r\n        height: 100%;\r\n        object-fit: contain;\r\n        z-index: 1;\r\n      }\r\n\r\n      // 遮罩层覆盖整个图片\r\n      &::before {\r\n        content: '';\r\n        position: absolute;\r\n        inset: 0;\r\n        background: rgba(0, 0, 0, 0.45);\r\n        z-index: 2;\r\n      }\r\n\r\n      .LiveBroadcastDetailsEndedWrap {\r\n        position: relative;\r\n        z-index: 3;\r\n        text-align: center;\r\n\r\n        .endedTitle {\r\n          font-weight: 400;\r\n          font-size: 16px;\r\n          color: #FFFFFF;\r\n          margin-bottom: 20px;\r\n          text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);\r\n        }\r\n\r\n        .replayBtn {\r\n          background: linear-gradient(90deg, #2F04FF 0%, #00F0FE 100%);\r\n          border-radius: 6px;\r\n          border: none;\r\n          padding: 12px 0;\r\n          font-size: 18px;\r\n          color: #FFFFFF;\r\n          width: 175px;\r\n          height: 44px;\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .LiveBroadcastDetailsSidebar {\r\n    height: 100%;\r\n    background: #191919;\r\n    border-left: 1px solid rgba(255, 255, 255, 0.05);\r\n    color: #e8e8e8;\r\n    // padding: 14px 16px 16px 16px;\r\n    overflow: auto;\r\n\r\n    .LiveBroadcastDetailsTabs {\r\n      display: flex;\r\n      align-items: center;\r\n      justify-content: space-around;\r\n      gap: 40px;\r\n      margin-bottom: 10px;\r\n      background: #2B2B2B;\r\n      padding: 14px 16px;\r\n\r\n      .LiveBroadcastDetailsTab {\r\n        cursor: pointer;\r\n        color: #999999;\r\n        position: relative;\r\n        font-size: 16px;\r\n        line-height: 1;\r\n        transition: color .2s ease;\r\n        font-weight: 500;\r\n\r\n        &.active {\r\n          color: #ffffff;\r\n          font-weight: 700;\r\n        }\r\n\r\n        &.active::after {\r\n          content: '';\r\n          position: absolute;\r\n          left: 0;\r\n          right: 0;\r\n          bottom: -8px;\r\n          height: 3px;\r\n          background: #54BDFF;\r\n          border-radius: 3px;\r\n        }\r\n      }\r\n    }\r\n\r\n    .LiveBroadcastDetailsTabPane {\r\n      padding: 12px 15px;\r\n    }\r\n\r\n    .LiveBroadcastDetailsPanelTitle {\r\n      font-weight: bold;\r\n      margin-bottom: 10px;\r\n      font-size: 15px;\r\n    }\r\n\r\n    .LiveBroadcastDetailsPanelText {\r\n      font-size: 13px;\r\n      line-height: 1.8;\r\n      color: #cfcfcf;\r\n    }\r\n\r\n    /* 详情tab内样式 */\r\n    .detailsTitle {\r\n      font-weight: 400;\r\n      font-size: 18px;\r\n      color: #FFFFFF;\r\n    }\r\n\r\n    .detailsTimeBox {\r\n      display: flex;\r\n      align-items: center;\r\n      margin-top: 14px;\r\n\r\n      .detailsTime {\r\n        font-weight: 400;\r\n        font-size: 14px;\r\n        color: #666666;\r\n      }\r\n\r\n      .detailsTimeIcon {\r\n        width: 16px;\r\n        height: 16px;\r\n        margin-right: 5px;\r\n      }\r\n    }\r\n\r\n\r\n    .detailsDesc {\r\n      font-weight: 400;\r\n      font-size: 14px;\r\n      color: #FFFFFF;\r\n      line-height: 24px;\r\n      margin-top: 24px;\r\n    }\r\n\r\n    /* 互动评论样式 */\r\n    .interact-pane {\r\n      display: flex;\r\n      flex-direction: column;\r\n      height: calc(100vh - 80px - 60px - 32px);\r\n      padding: 0 !important;\r\n    }\r\n\r\n    .comments-container {\r\n      flex: 1;\r\n      overflow-y: auto;\r\n      padding: 12px 15px;\r\n\r\n      &::-webkit-scrollbar {\r\n        width: 4px;\r\n      }\r\n\r\n      &::-webkit-scrollbar-track {\r\n        background: #2B2B2B;\r\n      }\r\n\r\n      &::-webkit-scrollbar-thumb {\r\n        background: #666;\r\n        border-radius: 2px;\r\n      }\r\n    }\r\n\r\n    .comment-item {\r\n      margin-bottom: 20px;\r\n\r\n      .comment-main {\r\n        display: flex;\r\n        margin-bottom: 8px;\r\n      }\r\n\r\n      .comment-content {\r\n        flex: 1;\r\n        min-width: 0;\r\n\r\n        .comment-header {\r\n          display: flex;\r\n          align-items: center;\r\n          margin-bottom: 6px;\r\n\r\n          .comment-nickname {\r\n            font-size: 13px;\r\n            color: #54BDFF;\r\n            margin-right: 8px;\r\n            font-weight: 500;\r\n          }\r\n\r\n          .reply-to {\r\n            font-size: 12px;\r\n            color: #999999;\r\n            margin-right: 8px;\r\n          }\r\n\r\n          .comment-time {\r\n            font-size: 11px;\r\n            color: #666666;\r\n          }\r\n        }\r\n\r\n        .comment-text {\r\n          font-size: 14px;\r\n          color: #FFFFFF;\r\n          line-height: 20px;\r\n          word-wrap: break-word;\r\n          margin-bottom: 8px;\r\n        }\r\n\r\n        .comment-actions {\r\n          display: flex;\r\n          align-items: center;\r\n          gap: 16px;\r\n          margin-bottom: 8px;\r\n\r\n          .action-item {\r\n            display: flex;\r\n            align-items: center;\r\n            gap: 4px;\r\n            font-size: 12px;\r\n            color: #999999;\r\n            cursor: pointer;\r\n            transition: color 0.2s;\r\n\r\n            &:hover {\r\n              color: #54BDFF;\r\n            }\r\n\r\n            &.like-btn {\r\n              &.liked {\r\n                color: #54BDFF;\r\n              }\r\n            }\r\n\r\n            .like-icon {\r\n              width: 14px;\r\n              height: 14px;\r\n            }\r\n\r\n            .like-count {\r\n              font-size: 11px;\r\n            }\r\n          }\r\n        }\r\n      }\r\n    }\r\n\r\n    // 回复列表样式\r\n    .replies-container {\r\n      margin-left: 42px;\r\n      border-left: 2px solid rgba(255, 255, 255, 0.05);\r\n      padding-left: 12px;\r\n\r\n      .reply-item {\r\n        margin-bottom: 12px;\r\n\r\n        .comment-content {\r\n          .comment-header {\r\n            .comment-nickname {\r\n              font-size: 12px;\r\n            }\r\n          }\r\n\r\n          .comment-text {\r\n            font-size: 13px;\r\n          }\r\n        }\r\n      }\r\n    }\r\n\r\n    // 回复输入框样式\r\n    .reply-input-container {\r\n      margin-left: 42px;\r\n      margin-top: 8px;\r\n\r\n      .reply-input-wrapper {\r\n        background: #2B2B2B;\r\n        border-radius: 6px;\r\n        padding: 8px;\r\n\r\n        .reply-input {\r\n          width: 100%;\r\n          height: 32px;\r\n          padding: 0 8px;\r\n          background: #191919;\r\n          border: 1px solid rgba(255, 255, 255, 0.1);\r\n          border-radius: 4px;\r\n          color: #FFFFFF;\r\n          font-size: 13px;\r\n          outline: none;\r\n          margin-bottom: 8px;\r\n\r\n          &::placeholder {\r\n            color: #666666;\r\n          }\r\n\r\n          &:focus {\r\n            border-color: #54BDFF;\r\n          }\r\n        }\r\n\r\n        .reply-actions {\r\n          display: flex;\r\n          justify-content: flex-end;\r\n          gap: 8px;\r\n\r\n          .cancel-btn,\r\n          .submit-btn {\r\n            height: 28px;\r\n            padding: 0 12px;\r\n            border: none;\r\n            border-radius: 4px;\r\n            font-size: 12px;\r\n            cursor: pointer;\r\n            transition: background-color 0.2s;\r\n          }\r\n\r\n          .cancel-btn {\r\n            background: transparent;\r\n            color: #999999;\r\n\r\n            &:hover {\r\n              background: rgba(255, 255, 255, 0.05);\r\n            }\r\n          }\r\n\r\n          .submit-btn {\r\n            background: #54BDFF;\r\n            color: #FFFFFF;\r\n\r\n            &:hover:not(:disabled) {\r\n              background: #4AA8E8;\r\n            }\r\n\r\n            &:disabled {\r\n              background: #333333;\r\n              color: #666666;\r\n              cursor: not-allowed;\r\n            }\r\n          }\r\n        }\r\n      }\r\n    }\r\n\r\n    .comments-empty {\r\n      display: flex;\r\n      align-items: center;\r\n      justify-content: center;\r\n      height: 200px;\r\n\r\n      .empty-text {\r\n        font-size: 14px;\r\n        color: #666666;\r\n      }\r\n    }\r\n\r\n    .comment-input-area {\r\n      border-top: 1px solid rgba(255, 255, 255, 0.08);\r\n      padding: 12px;\r\n\r\n      .input-wrapper {\r\n        background: #4A4A4A;\r\n        border-radius: 8px;\r\n        padding: 12px;\r\n        height: 90px;\r\n        position: relative;\r\n\r\n        .comment-input {\r\n          width: 100%;\r\n          height: calc(100% - 40px);\r\n          padding: 0;\r\n          padding-right: 80px;\r\n          background: transparent;\r\n          border: none;\r\n          color: #FFFFFF;\r\n          font-size: 14px;\r\n          outline: none;\r\n          resize: none;\r\n          font-family: inherit;\r\n          line-height: 1.4;\r\n\r\n          &::placeholder {\r\n            color: #999999;\r\n          }\r\n        }\r\n\r\n        .submit-comment-btn {\r\n          position: absolute;\r\n          bottom: 8px;\r\n          right: 8px;\r\n          cursor: pointer;\r\n          display: flex;\r\n          align-items: center;\r\n          gap: 4px;\r\n\r\n          .comment-btn {\r\n            color: #54BDFF;\r\n            font-size: 17px;\r\n            font-weight: 500;\r\n          }\r\n\r\n          .send-icon {\r\n            width: 18px;\r\n            height: 18px;\r\n          }\r\n\r\n          &:disabled {\r\n            background: #666666;\r\n            color: #999999;\r\n            cursor: not-allowed;\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.details-fade-enter-active,\r\n.details-fade-leave-active {\r\n  transition: opacity .2s ease;\r\n}\r\n\r\n.details-fade-enter-from,\r\n.details-fade-leave-to {\r\n  opacity: 0;\r\n}\r\n</style>\r\n"], "mappings": ";OAyEmBA,UAAyC;OAkFrCC,UAAyC;;EAzJnCC,KAAK,EAAC;AAAsB;;EAC9CA,KAAK,EAAC;AAA4B;iBAH7C;;EAKaA,KAAK,EAAC;AAA8B;;EAClCA,KAAK,EAAC;AAA2B;;EACjCA,KAAK,EAAC;AAA0B;;EAGlCA,KAAK,EAAC;AAA6B;;EASrCA,KAAK,EAAC;AAA0B;;EAC9BA,KAAK,EAAC;AAA4B;;EApB/CC,GAAA;EAsBsDD,KAAK,EAAC;;kBAtB5D;;EA4B2DA,KAAK,EAAC;AAAiC;;EAK1EA,KAAK,EAAC;AAA8B;kBAjC5D;;EAyC2DA,KAAK,EAAC;AAA2B;kBAzC5F;;EAmDmBA,KAAK,EAAC;AAA+B;;EAU3CA,KAAK,EAAC;AAA6B;;EACjCA,KAAK,EAAC;AAA0B;;EA9D/CC,GAAA;EAsEeD,KAAK,EAAC;;;EACJA,KAAK,EAAC;AAAc;;EACpBA,KAAK,EAAC;AAAgB;;EAEnBA,KAAK,EAAC;AAAa;;EAEtBA,KAAK,EAAC;AAAa;;EA5EpCC,GAAA;EA8EeD,KAAK,EAAC;;;EAEJA,KAAK,EAAC,oBAAoB;EAACE,GAAG,EAAC;;;EAG3BF,KAAK,EAAC;AAAc;;EAClBA,KAAK,EAAC;AAAiB;;EACrBA,KAAK,EAAC;AAAgB;;EACnBA,KAAK,EAAC;AAAkB;;EACxBA,KAAK,EAAC;AAAc;;EAEvBA,KAAK,EAAC;AAAc;;EACpBA,KAAK,EAAC;AAAiB;kBA1FhD;kBAAA;kBAAA;;EAiG8BA,KAAK,EAAC;AAAY;;EAjGhDC,GAAA;EAwG4ED,KAAK,EAAC;;;EAEzDA,KAAK,EAAC;AAAiB;;EACrBA,KAAK,EAAC;AAAgB;;EACnBA,KAAK,EAAC;AAAkB;;EAExBA,KAAK,EAAC;AAAc;;EAEvBA,KAAK,EAAC;AAAc;;EACpBA,KAAK,EAAC;AAAiB;kBAjHlD;kBAAA;;EAuHgCA,KAAK,EAAC;AAAY;;EAvHlDC,GAAA;EA+HsDD,KAAK,EAAC;;;EACrCA,KAAK,EAAC;AAAqB;kBAhIlD;;EAoIyBA,KAAK,EAAC;AAAe;kBApI9C;;EAAAC,GAAA;EA+IgDD,KAAK,EAAC;;;EAMrCA,KAAK,EAAC;AAAoB;;EACxBA,KAAK,EAAC;AAAe;;;;;uBAtJxCG,mBAAA,CAAAC,SAAA,SACEC,YAAA,CAkKaC,WAAA;IAlKDC,IAAI,EAAC,cAAc;IAA/BC,SAkKa,EAlKb;;IADFC,OAAA,EAAAC,QAAA,CAEI;MAAA,IAAAC,eAAA,EAAAC,gBAAA,EAAAC,gBAAA;MAAA,OAgKM,C,gBAhKNC,mBAAA,CAgKM,OAhKNC,UAgKM,GA/JJD,mBAAA,CAeM,OAfNE,UAeM,GAdOC,MAAA,CAAAC,OAAO,I,cAAlBf,mBAAA,CAAqF;QAJ7FF,GAAA;QAI4BD,KAAK,EAAC,4BAA4B;QAAEmB,GAAG,EAAEF,MAAA,CAAAC,OAAO;QAAEE,GAAG,EAAC;8BAJlFC,UAAA,KAAAC,mBAAA,gBAKQR,mBAAA,CAIM,OAJNS,UAIM,GAHJT,mBAAA,CAAgE,OAAhEU,UAAgE,EAAAC,gBAAA,CAAtBR,MAAA,CAAAS,OAAO,CAACC,KAAK,kBACvDb,mBAAA,CACkC,OADlCc,UACkC,EADI,OAAK,GAAAH,gBAAA,CAAGR,MAAA,CAAAY,MAAM,CAACZ,MAAA,CAAAS,OAAO,CAACI,SAAS,KAAI,KAAG,GAAAL,gBAAA,CAC3ER,MAAA,CAAAY,MAAM,CAACZ,MAAA,CAAAS,OAAO,CAACK,OAAO,kB,GAE1BjB,mBAAA,CAOM,OAPNkB,UAOM,GANJ3B,YAAA,CAEY4B,oBAAA;QAFDC,IAAI,EAAC,SAAS;QAAEC,OAAK,EAAElB,MAAA,CAAAmB,WAAW;QAAEpC,KAAK,EAAC;;QAX/DS,OAAA,EAAAC,QAAA,CAW2E;UAAA,OAEjE2B,MAAA,QAAAA,MAAA,OAbVC,gBAAA,CAW2E,QAEjE,E;;QAbVC,CAAA;UAcUlC,YAAA,CAEUmC,kBAAA;QAFDxC,KAAK,EAAC,2BAA2B;QAAEmC,OAAK,EAAElB,MAAA,CAAAwB;;QAd7DhC,OAAA,EAAAC,QAAA,CAeY;UAAA,OAAS,CAATL,YAAA,CAASqC,gBAAA,E;;QAfrBH,CAAA;cAmBMzB,mBAAA,CA8IM,OA9IN6B,UA8IM,GA7IJ7B,mBAAA,CAwCM,OAxCN8B,UAwCM,GAvCJtB,mBAAA,qBAAwB,EACbL,MAAA,CAAAS,OAAO,CAACmB,aAAa,c,cAAhC1C,mBAAA,CAIM,OAJN2C,WAIM,GAHJhC,mBAAA,CAAyF;QAAnFK,GAAG,EAAEF,MAAA,CAAA8B,MAAM;QAAE3B,GAAG,EAAC,WAAW;QAAC4B,KAAqD,EAArD;UAAA;UAAA;UAAA;QAAA;8BAvB/CC,WAAA,GAwBY5C,YAAA,CAC4BY,MAAA;QADX,YAAU,EAAEA,MAAA,CAAAS,OAAO,CAACI,SAAS;QAAGoB,cAAa,EAAEjC,MAAA,CAAAkC,kBAAkB;QAChFjD,GAAG,EAAC;mDAGQe,MAAA,CAAAS,OAAO,CAACmB,aAAa,c,cAArC1C,mBAAA,CAWMC,SAAA;QAvChBH,GAAA;MAAA,IA2BUqB,mBAAA,eAAkB,EAClBR,mBAAA,CAWM,OAXNsC,WAWM,GAVenC,MAAA,CAAAS,OAAO,CAAC2B,OAAO,I,cAAlCC,YAAA,CAE4GrC,MAAA;QADzGhB,GAAG,uBAAuBgB,MAAA,CAAAsC,KAAK,CAACC,EAAE,IAAIvC,MAAA,CAAAS,OAAO,CAACmB,aAAa,IAAI5B,MAAA,CAAAwC,YAAY;QAC3E,UAAQ,EAAExC,MAAA,CAAAS,OAAO,CAAC2B,OAAO;QAAG,YAAU,EAAEpC,MAAA,CAAAS,OAAO,CAACgC,aAAa;QAAG,WAAS,EAAE,KAAK;QAAExD,GAAG,EAAC;4EAEzFC,mBAAA,CAKMC,SAAA;QAtClBH,GAAA;MAAA,IAgCYqB,mBAAA,iBAAoB,EACpBR,mBAAA,CAKM,OALN6C,WAKM,GAJJ7C,mBAAA,CAAmD;QAA7CK,GAAG,EAAEF,MAAA,CAAA8B,MAAM;QAAE3B,GAAG,EAAC,MAAM;QAACpB,KAAK,EAAC;8BAlClD4D,WAAA,G,0BAmCc9C,mBAAA,CAEM;QAFDd,KAAK,EAAC;MAAgB,IACzBc,mBAAA,CAA2C;QAAtCd,KAAK,EAAC;MAAiB,GAAC,UAAQ,E,+HAK3BiB,MAAA,CAAAS,OAAO,CAACmB,aAAa,c,cAArC1C,mBAAA,CAkBMC,SAAA;QA3DhBH,GAAA;MAAA,IAwCUqB,mBAAA,0BAA6B,EAC7BR,mBAAA,CAkBM,OAlBN+C,WAkBM,GAjBJvC,mBAAA,kBAAqB,EACFL,MAAA,CAAAwC,YAAY,IAAIxC,MAAA,CAAAS,OAAO,CAACgC,aAAa,I,cAAxDJ,YAAA,CAE2GrC,MAAA;QADxGhB,GAAG,yBAAyBgB,MAAA,CAAAsC,KAAK,CAACC,EAAE,IAAIvC,MAAA,CAAAS,OAAO,CAACmB,aAAa,IAAI5B,MAAA,CAAAwC,YAAY;QAC7E,UAAQ,EAAExC,MAAA,CAAAS,OAAO,CAAC2B,OAAO;QAAG,YAAU,EAAEpC,MAAA,CAAAS,OAAO,CAACgC,aAAa;QAAG,WAAS,EAAE,IAAI;QAAExD,GAAG,EAAC;4EAExFC,mBAAA,CAWWC,SAAA;QA1DvBH,GAAA;MAAA,IA8CYqB,mBAAA,oBAAuB,EAErBA,mBAAA,SAAY,EACZR,mBAAA,CAAkE;QAA5DK,GAAG,EAAEF,MAAA,CAAA8B,MAAM;QAAE3B,GAAG,EAAC,MAAM;QAACpB,KAAK,EAAC;8BAjDlD8D,WAAA,GAkDcxC,mBAAA,eAAkB,EAClBR,mBAAA,CAMM,OANNiD,WAMM,G,0BALJjD,mBAAA,CAAmC;QAA9Bd,KAAK,EAAC;MAAY,GAAC,OAAK,sBAC7BsB,mBAAA,oBAAuB,EAC2BL,MAAA,CAAAS,OAAO,CAACsC,QAAQ,S,cAAlEV,YAAA,CAEYrB,oBAAA;QAxD5BhC,GAAA;QAsD2BiC,IAAI,EAAC,SAAS;QAAClC,KAAK,EAAC,WAAW;QAA+BmC,OAAK,EAAElB,MAAA,CAAAgD;;QAtDjGxD,OAAA,EAAAC,QAAA,CAsD+G;UAAA,OAE/F2B,MAAA,QAAAA,MAAA,OAxDhBC,gBAAA,CAsD+G,QAE/F,E;;QAxDhBC,CAAA;YAAAjB,mBAAA,e,qFAAAA,mBAAA,e,GA6DQR,mBAAA,CAmGM,OAnGNoD,WAmGM,GAlGJpD,mBAAA,CAOM,OAPNqD,WAOM,GANJrD,mBAAA,CAEY;QAFNd,KAAK,EA/DvBoE,eAAA;UAAAC,MAAA,EA+D+DpD,MAAA,CAAAqD,SAAS;QAAA;QACzDnC,OAAK,EAAAE,MAAA,QAAAA,MAAA,gBAAAkC,MAAA;UAAA,OAAEtD,MAAA,CAAAqD,SAAS;QAAA;SAAc,OAC3B,kBACNxD,mBAAA,CAEU;QAFJd,KAAK,EAlEvBoE,eAAA;UAAAC,MAAA,EAkE+DpD,MAAA,CAAAqD,SAAS;QAAA;QACzDnC,OAAK,EAAElB,MAAA,CAAAuD;SAAwB,KAC9B,iB,GAEyCvD,MAAA,CAAAqD,SAAS,kB,cAAxDnE,mBAAA,CAOM,OAPNsE,WAOM,GANJ3D,mBAAA,CAA2D,OAA3D4D,WAA2D,EAAAjD,gBAAA,CAA9B,EAAAd,eAAA,GAAAM,MAAA,CAAAS,OAAO,cAAAf,eAAA,uBAAPA,eAAA,CAASgB,KAAK,0BAC3Cb,mBAAA,CAGM,OAHN6D,WAGM,G,0BAFJ7D,mBAAA,CAA8E;QAAzEK,GAAyC,EAAzCrB,UAAyC;QAACsB,GAAG,EAAC,EAAE;QAACpB,KAAK,EAAC;mCAC5Dc,mBAAA,CAAqG,QAArG8D,WAAqG,EAA3E,KAAG,GAAAnD,gBAAA,CAAGR,MAAA,CAAAY,MAAM,EAAAjB,gBAAA,GAACK,MAAA,CAAAS,OAAO,cAAAd,gBAAA,uBAAPA,gBAAA,CAASkB,SAAS,KAAI,KAAG,GAAAL,gBAAA,CAAGR,MAAA,CAAAY,MAAM,EAAAhB,gBAAA,GAACI,MAAA,CAAAS,OAAO,cAAAb,gBAAA,uBAAPA,gBAAA,CAASkB,OAAO,kB,GAE5FjB,mBAAA,CAAoE,OAApE+D,WAAoE,EAAApD,gBAAA,CAAxCR,MAAA,CAAAS,OAAO,CAACoD,aAAa,2B,oBAEnD3E,mBAAA,CAiFM,OAjFN4E,WAiFM,GAhFJzD,mBAAA,UAAa,EACbR,mBAAA,CAkEM,OAlENkE,WAkEM,I,kBAjEJ7E,mBAAA,CA2DMC,SAAA,QA5IpB6E,WAAA,CAiFqChE,MAAA,CAAAiE,QAAQ,EAjF7C,UAiF0BC,OAAO;6BAAnBhF,mBAAA,CA2DM;UA3D4BF,GAAG,EAAEkF,OAAO,CAAC3B,EAAE;UAAExD,KAAK,EAAC;YACvDsB,mBAAA,SAAY,EACZR,mBAAA,CAkBM,OAlBNsE,WAkBM,GAjBJtE,mBAAA,CAgBM,OAhBNuE,WAgBM,GAfJvE,mBAAA,CAGM,OAHNwE,WAGM,GAFJxE,mBAAA,CAAmE,QAAnEyE,WAAmE,EAAA9D,gBAAA,CAAjC0D,OAAO,CAACK,eAAe,kBACzD1E,mBAAA,CAAkE,QAAlE2E,WAAkE,EAAAhE,gBAAA,CAApCR,MAAA,CAAAY,MAAM,CAACsD,OAAO,CAACO,UAAU,kB,GAEzD5E,mBAAA,CAA4D,OAA5D6E,WAA4D,EAAAlE,gBAAA,CAA/B0D,OAAO,CAACS,cAAc,kBACnD9E,mBAAA,CASM,OATN+E,WASM,GARJ/E,mBAAA,CAAyE;UAAnEd,KAAK,EAAC,aAAa;UAAEmC,OAAK,WAALA,OAAKA,CAAAoC,MAAA;YAAA,OAAEtD,MAAA,CAAA6E,cAAc,CAACX,OAAO,CAAC3B,EAAE;UAAA;WAAG,MAAI,iBA3FxFuC,WAAA,GA4FsBjF,mBAAA,CAMO;UANDd,KAAK,EA5FjCoE,eAAA,EA4FkC,sBAAsB;YAAA4B,KAAA,EACfb,OAAO,CAACc;UAAe;UADN9D,OAAK,WAALA,OAAKA,CAAAoC,MAAA;YAAA,OAAEtD,MAAA,CAAAiF,UAAU,CAACf,OAAO;UAAA;YAE3DrE,mBAAA,CAE+B;UAD5BK,GAAG,EAAEgE,OAAO,CAACc,eAAe,GAAGE,OAAO,sCAAsCA,OAAO;UACpF/E,GAAG,EAAC,IAAI;UAACpB,KAAK,EAAC;gCAhGzCoG,WAAA,GAiGwBtF,mBAAA,CAAiE,QAAjEuF,WAAiE,EAAxC,GAAC,GAAA5E,gBAAA,CAAG0D,OAAO,CAACmB,YAAY,SAAQ,GAAC,gB,yBAjGlFC,WAAA,E,OAuGgBjF,mBAAA,UAAa,EACF6D,OAAO,CAACqB,QAAQ,IAAIrB,OAAO,CAACqB,QAAQ,CAACC,MAAM,Q,cAAtDtG,mBAAA,CAoBM,OApBNuG,WAoBM,I,kBAnBJvG,mBAAA,CAkBMC,SAAA,QA3HxB6E,WAAA,CAyGuCE,OAAO,CAACqB,QAAQ,EAzGvD,UAyG8BG,KAAK;+BAAjBxG,mBAAA,CAkBM;YAlBkCF,GAAG,EAAE0G,KAAK,CAACnD,EAAE;YAAExD,KAAK,EAAC;cAC3Dc,mBAAA,CAgBM,OAhBN8F,WAgBM,GAfJ9F,mBAAA,CAIM,OAJN+F,WAIM,GAHJ/F,mBAAA,CAAiE,QAAjEgG,WAAiE,EAAArF,gBAAA,CAA/BkF,KAAK,CAACnB,eAAe,kBACvDlE,mBAAA,oFAAmF,EACnFR,mBAAA,CAAgE,QAAhEiG,WAAgE,EAAAtF,gBAAA,CAAlCR,MAAA,CAAAY,MAAM,CAAC8E,KAAK,CAACjB,UAAU,kB,GAEvD5E,mBAAA,CAA0D,OAA1DkG,WAA0D,EAAAvF,gBAAA,CAA7BkF,KAAK,CAACf,cAAc,kBACjD9E,mBAAA,CAQM,OARNmG,WAQM,GAPJnG,mBAAA,CAMO;YANDd,KAAK,EAlHnCoE,eAAA,EAkHoC,sBAAsB;cAAA4B,KAAA,EACfW,KAAK,CAACV;YAAe;YADJ9D,OAAK,WAALA,OAAKA,CAAAoC,MAAA;cAAA,OAAEtD,MAAA,CAAAiF,UAAU,CAACS,KAAK;YAAA;cAEzD7F,mBAAA,CAE+B;YAD5BK,GAAG,EAAEwF,KAAK,CAACV,eAAe,GAAGE,OAAO,sCAAsCA,OAAO;YAClF/E,GAAG,EAAC,IAAI;YAACpB,KAAK,EAAC;kCAtH3CkH,WAAA,GAuH0BpG,mBAAA,CAA+D,QAA/DqG,WAA+D,EAAtC,GAAC,GAAA1F,gBAAA,CAAGkF,KAAK,CAACL,YAAY,SAAQ,GAAC,gB,yBAvHlFc,WAAA,E;4CAAA9F,mBAAA,gBA8HgBA,mBAAA,WAAc,EACHL,MAAA,CAAAoG,UAAU,KAAKlC,OAAO,CAAC3B,EAAE,I,cAApCrD,mBAAA,CAYM,OAZNmH,WAYM,GAXJxG,mBAAA,CAUM,OAVNyG,WAUM,G,gBATJzG,mBAAA,CAE+E;UAnInG,uBAAAuB,MAAA,QAAAA,MAAA,gBAAAkC,MAAA;YAAA,OAiIoCtD,MAAA,CAAAuG,YAAY,GAAAjD,MAAA;UAAA;UAAErC,IAAI,EAAC,MAAM;UACtCuF,WAAW,EAAExG,MAAA,CAAAyG,WAAW,UAAUzG,MAAA,CAAAyG,WAAW;UAAmB1H,KAAK,EAAC,aAAa;UACnF2H,OAAK,EAnI5BC,SAAA,WAAArD,MAAA;YAAA,OAmIoCtD,MAAA,CAAA4G,WAAW,CAAC1C,OAAO,CAAC3B,EAAE;UAAA;UAAGsE,SAAS,EAAC,KAAK;UAnI5EC,OAAA;UAmI6E7H,GAAG,EAAC;iDAnIjF8H,WAAA,I,cAiIoC/G,MAAA,CAAAuG,YAAY,E,GAG5B1G,mBAAA,CAKM,OALNmH,WAKM,GAJJnH,mBAAA,CAA2D;UAAnDd,KAAK,EAAC,YAAY;UAAEmC,OAAK,EAAElB,MAAA,CAAAiH;WAAa,IAAE,GAClDpH,mBAAA,CAES;UAFDd,KAAK,EAAC,YAAY;UAAEmC,OAAK,WAALA,OAAKA,CAAAoC,MAAA;YAAA,OAAEtD,MAAA,CAAA4G,WAAW,CAAC1C,OAAO,CAAC3B,EAAE;UAAA;UAAI2E,QAAQ,GAAGlH,MAAA,CAAAuG,YAAY,CAACY,IAAI;WAAI,MAE7F,iBAxItBC,WAAA,E,SAAA/G,mBAAA,e;sCA8IcA,mBAAA,SAAY,EACDL,MAAA,CAAAiE,QAAQ,CAACuB,MAAM,U,cAA1BtG,mBAAA,CAEM,OAFNmI,WAEM,EAAAjG,MAAA,SAAAA,MAAA,QADJvB,mBAAA,CAAkC;QAA7Bd,KAAK,EAAC;MAAY,GAAC,MAAI,oB,MAhJ5CsB,mBAAA,e,yBAoJYA,mBAAA,aAAgB,EAChBR,mBAAA,CASM,OATNyH,WASM,GARJzH,mBAAA,CAOM,OAPN0H,WAOM,G,gBANJ1H,mBAAA,CACwE;QAxJxF,uBAAAuB,MAAA,QAAAA,MAAA,gBAAAkC,MAAA;UAAA,OAuJmCtD,MAAA,CAAAwH,UAAU,GAAAlE,MAAA;QAAA;QAAEkD,WAAW,EAAC,SAAS;QAACzH,KAAK,EAAC,eAAe;QAAE2H,OAAK,EAvJjGC,SAAA,CAuJyG3G,MAAA,CAAAyH,aAAa;QACpGZ,SAAS,EAAC,KAAK;QAACa,IAAI,EAAC,GAAG;QAAC3F,KAAiC,EAAjC;UAAA;UAAA;QAAA;qEADR/B,MAAA,CAAAwH,UAAU,E,GAE7B3H,mBAAA,CAGM;QAHDd,KAAK,EAAC,oBAAoB;QAAEmC,OAAK,EAAElB,MAAA,CAAAyH;sCACtC5H,mBAAA,CAAiC;QAA5Bd,KAAK,EAAC;MAAa,GAAC,IAAE,qBAC3Bc,mBAAA,CAA4E;QAAvEK,GAAyC,EAAzCpB,UAAyC;QAACqB,GAAG,EAAC,IAAI;QAACpB,KAAK,EAAC;kFAzJ/D4I,MAAA,CAAAC,UAAU,E;;IAF3BtG,CAAA;MAoKEjB,mBAAA,UAAa,EACbjB,YAAA,CAAqFY,MAAA;IArKvF4H,UAAA,EAqKwB5H,MAAA,CAAA6H,kBAAkB;IArK1C,uBAAAzG,MAAA,QAAAA,MAAA,gBAAAkC,MAAA;MAAA,OAqKwBtD,MAAA,CAAA6H,kBAAkB,GAAAvE,MAAA;IAAA;IAAG,SAAO,EAAEtD,MAAA,CAAAsC,KAAK,CAACC,EAAE;IAAG,WAAS,EAAEvC,MAAA,CAAAS;mEAE1EJ,mBAAA,aAAgB,EAChBjB,YAAA,CAAwFY,MAAA;IAxK1F4H,UAAA,EAwK8B5H,MAAA,CAAA8H,kBAAkB;IAxKhD,uBAAA1G,MAAA,QAAAA,MAAA,gBAAAkC,MAAA;MAAA,OAwK8BtD,MAAA,CAAA8H,kBAAkB,GAAAxE,MAAA;IAAA;IAAGyE,eAAc,EAAE/H,MAAA,CAAAgI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
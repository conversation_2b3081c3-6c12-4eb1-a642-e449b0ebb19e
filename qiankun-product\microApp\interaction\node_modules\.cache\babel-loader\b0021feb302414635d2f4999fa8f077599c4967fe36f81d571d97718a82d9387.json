{"ast": null, "code": "function _slicedToArray(r, e) { return _arrayWithHoles(r) || _iterableToArrayLimit(r, e) || _unsupportedIterableToArray(r, e) || _nonIterableRest(); }\nfunction _nonIterableRest() { throw new TypeError(\"Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); }\nfunction _unsupportedIterableToArray(r, a) { if (r) { if (\"string\" == typeof r) return _arrayLikeToArray(r, a); var t = {}.toString.call(r).slice(8, -1); return \"Object\" === t && r.constructor && (t = r.constructor.name), \"Map\" === t || \"Set\" === t ? Array.from(r) : \"Arguments\" === t || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t) ? _arrayLikeToArray(r, a) : void 0; } }\nfunction _arrayLikeToArray(r, a) { (null == a || a > r.length) && (a = r.length); for (var e = 0, n = Array(a); e < a; e++) n[e] = r[e]; return n; }\nfunction _iterableToArrayLimit(r, l) { var t = null == r ? null : \"undefined\" != typeof Symbol && r[Symbol.iterator] || r[\"@@iterator\"]; if (null != t) { var e, n, i, u, a = [], f = !0, o = !1; try { if (i = (t = t.call(r)).next, 0 === l) { if (Object(t) !== t) return; f = !1; } else for (; !(f = (e = i.call(t)).done) && (a.push(e.value), a.length !== l); f = !0); } catch (r) { o = !0, n = r; } finally { try { if (!f && null != t.return && (u = t.return(), Object(u) !== u)) return; } finally { if (o) throw n; } } return a; } }\nfunction _arrayWithHoles(r) { if (Array.isArray(r)) return r; }\nimport { defineComponent, ref, createVNode, Fragment } from 'vue';\nimport '../../../utils/index.mjs';\nimport { buildProps, definePropType } from '../../../utils/vue/props/runtime.mjs';\nimport { composeRefs } from '../../../utils/vue/refs.mjs';\nimport { ensureOnlyChild } from '../../../utils/vue/vnode.mjs';\nvar forwardRefProps = buildProps({\n  setRef: {\n    type: definePropType(Function),\n    required: true\n  },\n  onlyChild: Boolean\n});\nvar ForwardRef = defineComponent({\n  props: forwardRefProps,\n  setup(props, _ref) {\n    var slots = _ref.slots;\n    var fragmentRef = ref();\n    var setRef = composeRefs(fragmentRef, function (el) {\n      if (el) {\n        props.setRef(el.nextElementSibling);\n      } else {\n        props.setRef(null);\n      }\n    });\n    return function () {\n      var _a;\n      var _ref2 = ((_a = slots.default) == null ? void 0 : _a.call(slots)) || [],\n        _ref3 = _slicedToArray(_ref2, 1),\n        firstChild = _ref3[0];\n      var child = props.onlyChild ? ensureOnlyChild(firstChild.children) : firstChild.children;\n      return createVNode(Fragment, {\n        \"ref\": setRef\n      }, [child]);\n    };\n  }\n});\nexport { ForwardRef as default, forwardRefProps };", "map": {"version": 3, "names": ["forwardRefProps", "buildProps", "setRef", "type", "definePropType", "Function", "required", "<PERSON><PERSON><PERSON><PERSON>", "Boolean", "ForwardRef", "defineComponent", "props", "setup", "_ref", "fragmentRef", "ref", "composeRefs", "el", "nextElement<PERSON><PERSON>ling", "_a", "slots", "default", "call", "_ref3", "_slicedToArray", "_ref2", "<PERSON><PERSON><PERSON><PERSON>", "child", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "children", "createVNode", "Fragment"], "sources": ["../../../../../../packages/components/tooltip-v2/src/forward-ref.tsx"], "sourcesContent": ["import { Fragment, defineComponent, ref } from 'vue'\nimport {\n  buildProps,\n  composeRefs,\n  definePropType,\n  ensureOnlyChild,\n} from '@element-plus/utils'\n\nimport type { ExtractPropTypes, VNodeArrayChildren } from 'vue'\n\nexport type RefSetter = (el: HTMLElement | null) => void\n\nexport const forwardRefProps = buildProps({\n  setRef: { type: definePropType<RefSetter>(Function), required: true },\n  onlyChild: <PERSON>olean,\n} as const)\n\nexport type ForwardRefProps = ExtractPropTypes<typeof forwardRefProps>\n\n// TODO: consider make this component a reusable component without the only child feature.\nexport default defineComponent({\n  props: forwardRefProps,\n  setup(props, { slots }) {\n    const fragmentRef = ref()\n    const setRef = composeRefs(fragmentRef, (el) => {\n      // vue fragments is represented as a text element.\n      // The first element sibling should be the first element children of fragment.\n      // This is how we get the element.\n      if (el) {\n        props.setRef(\n          (el as HTMLElement).nextElementSibling as HTMLElement | null\n        )\n      } else {\n        props.setRef(null)\n      }\n    })\n    return () => {\n      const [firstChild] = slots.default?.() || []\n      const child = props.onlyChild\n        ? ensureOnlyChild(firstChild.children as VNodeArrayChildren)\n        : firstChild.children\n      // Dunno why the ref for jsx complains about the typing issue which was not\n      // in template\n      return <Fragment ref={setRef as any}>{child}</Fragment>\n    }\n  },\n})\n"], "mappings": ";;;;;;;;;;;AAYa,IAAAA,eAAe,GAAGC,UAAU,CAAC;EACxCC,MAAM,EAAE;IAAEC,IAAI,EAAEC,cAAc,CAAYC,QAAZ,CAAtB;IAA6CC,QAAQ,EAAE;GADvB;EAExCC,SAAS,EAAEC;AAF6B,CAAD;AAOzC,IAAAC,UAAA,GAAAC,eAAA;EACAC,KAAA,EAAAX,eAAA;EACEY,KAAKD,MADwB,EAAAE,IAAA,EAExB;IAAA,I;IAAU,IAAAC,WAAA,GAAAC,GAAA;IAAS,IAAAb,MAAA,GAAAc,WAAA,CAAAF,WAAA,YAAAG,EAAA;MAChB,IAAAA,EAAA;QACNN,KAAA,CAAAT,MAAe,CAAAe,EAAA,CAAAC,kBAAW;MACxB;QACAP,KAAA,CAAAT,MAAA;MACA;IACA;IACE,mBAAM;MAGP,IAAMiB,EAAA;kBACL,EAAAA,EAAA,GAAAC,KAAA,CAAAC,OAAA,qBAAAF,EAAA,CAAAG,IAAA,CAAAF,KAAA;QAAAG,KAAA,GAAAC,cAAA,CAAAC,KAAA;QAAKC,UAAL,GAAAH,KAAA;MACD,IAAAI,KAAA,GAAAhB,KAAA,CAAAJ,SAAA,GAAAqB,eAAA,CAAAF,UAAA,CAAAG,QAAA,IAAAH,UAAA,CAAAG,QAAA;MAVH,OAAAC,WAAA,CAAAC,QAAA;QAYA,KAAa,EAAA7B;MACX,IAAAyB,KAAM;IACN;EAIA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
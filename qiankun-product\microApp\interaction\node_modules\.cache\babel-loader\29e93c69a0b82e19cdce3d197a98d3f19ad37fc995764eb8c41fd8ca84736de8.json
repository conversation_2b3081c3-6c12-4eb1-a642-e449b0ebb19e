{"ast": null, "code": "import { resolveComponent as _resolveComponent, createVNode as _createVNode, withCtx as _withCtx, createTextVNode as _createTextVNode, createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock } from \"vue\";\nvar _hoisted_1 = {\n  class: \"JoinVideoMeeting\"\n};\nvar _hoisted_2 = {\n  class: \"globalFormButton\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  var _component_el_input = _resolveComponent(\"el-input\");\n  var _component_el_form_item = _resolveComponent(\"el-form-item\");\n  var _component_el_button = _resolveComponent(\"el-button\");\n  var _component_el_form = _resolveComponent(\"el-form\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createVNode(_component_el_form, {\n    ref: \"formRef\",\n    model: $setup.form,\n    rules: $setup.rules,\n    inline: \"\",\n    \"label-position\": \"top\",\n    class: \"globalForm\"\n  }, {\n    default: _withCtx(function () {\n      return [_createVNode(_component_el_form_item, {\n        label: \"会议室号\",\n        prop: \"meetingNumber\",\n        class: \"globalFormTitle\"\n      }, {\n        default: _withCtx(function () {\n          return [_createVNode(_component_el_input, {\n            modelValue: $setup.form.meetingNumber,\n            \"onUpdate:modelValue\": _cache[0] || (_cache[0] = function ($event) {\n              return $setup.form.meetingNumber = $event;\n            }),\n            placeholder: \"请输入会议室号\",\n            clearable: \"\"\n          }, null, 8 /* PROPS */, [\"modelValue\"])];\n        }),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_form_item, {\n        label: \"入会密码\",\n        class: \"globalFormTitle\"\n      }, {\n        default: _withCtx(function () {\n          return [_createVNode(_component_el_input, {\n            modelValue: $setup.form.meetingPassword,\n            \"onUpdate:modelValue\": _cache[1] || (_cache[1] = function ($event) {\n              return $setup.form.meetingPassword = $event;\n            }),\n            placeholder: \"请输入入会密码\",\n            clearable: \"\"\n          }, null, 8 /* PROPS */, [\"modelValue\"])];\n        }),\n        _: 1 /* STABLE */\n      }), _createElementVNode(\"div\", _hoisted_2, [_createVNode(_component_el_button, {\n        type: \"primary\",\n        onClick: _cache[2] || (_cache[2] = function ($event) {\n          return $setup.submitForm($setup.formRef);\n        })\n      }, {\n        default: _withCtx(function () {\n          return _cache[3] || (_cache[3] = [_createTextVNode(\"提交\")]);\n        }),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_button, {\n        onClick: $setup.resetForm\n      }, {\n        default: _withCtx(function () {\n          return _cache[4] || (_cache[4] = [_createTextVNode(\"取消\")]);\n        }),\n        _: 1 /* STABLE */\n      })])];\n    }),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"model\", \"rules\"])]);\n}", "map": {"version": 3, "names": ["class", "_createElementBlock", "_hoisted_1", "_createVNode", "_component_el_form", "ref", "model", "$setup", "form", "rules", "inline", "default", "_withCtx", "_component_el_form_item", "label", "prop", "_component_el_input", "modelValue", "meetingNumber", "_cache", "$event", "placeholder", "clearable", "_", "meetingPassword", "_createElementVNode", "_hoisted_2", "_component_el_button", "type", "onClick", "submitForm", "formRef", "_createTextVNode", "resetForm"], "sources": ["D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\interaction\\src\\views\\VideoMeeting\\component\\JoinVideoMeeting.vue"], "sourcesContent": ["<template>\r\n  <div class=\"JoinVideoMeeting\">\r\n    <el-form ref=\"formRef\" :model=\"form\" :rules=\"rules\" inline label-position=\"top\" class=\"globalForm\">\r\n      <el-form-item label=\"会议室号\" prop=\"meetingNumber\" class=\"globalFormTitle\">\r\n        <el-input v-model=\"form.meetingNumber\" placeholder=\"请输入会议室号\" clearable />\r\n      </el-form-item>\r\n      <el-form-item label=\"入会密码\" class=\"globalFormTitle\">\r\n        <el-input v-model=\"form.meetingPassword\" placeholder=\"请输入入会密码\" clearable />\r\n      </el-form-item>\r\n      <div class=\"globalFormButton\">\r\n        <el-button type=\"primary\" @click=\"submitForm(formRef)\">提交</el-button>\r\n        <el-button @click=\"resetForm\">取消</el-button>\r\n      </div>\r\n    </el-form>\r\n  </div>\r\n</template>\r\n<script>\r\nexport default { name: 'JoinVideoMeeting' }\r\n</script>\r\n<script setup>\r\nimport api from '@/api'\r\nimport { reactive, ref, onMounted } from 'vue'\r\nimport { user } from 'common/js/system_var.js'\r\nimport { getVideoMeetinConfig, openMeetingYL, openMeetingXY } from 'common/js/VideoMeeting.js'\r\nimport { ElMessage } from 'element-plus'\r\nconst emit = defineEmits(['callback'])\r\n\r\nconst { VideoMeetinVender, yl_client_id, xy_wss_server, xy_http_server, xy_log_server, xy_ext_id, xy_client_id, xy_client_secret } = getVideoMeetinConfig()\r\n\r\nconst formRef = ref()\r\nconst form = reactive({\r\n  meetingNumber: '',\r\n  meetingPassword: ''\r\n})\r\nconst rules = reactive({ meetingNumber: [{ required: true, message: '请输入会议室号', trigger: ['blur', 'change'] }] })\r\n\r\nonMounted(() => {\r\n  globalReadConfig()\r\n})\r\nconst globalReadConfig = async () => {\r\n  const { data } = await api.globalReadConfig({ codes: ['VideoMeetinVender'] })\r\n  VideoMeetinVender.value = data.VideoMeetinVender\r\n}\r\nconst submitForm = async (formEl) => {\r\n  if (!formEl) return\r\n  await formEl.validate((valid, fields) => {\r\n    if (valid) { handleVideoConf() } else { ElMessage({ type: 'warning', message: '请根据提示信息完善字段内容！' }) }\r\n  })\r\n}\r\nconst handleVideoConf = () => {\r\n  if (VideoMeetinVender.value === 'yl') {\r\n    genMeetingToken()\r\n  } else if (VideoMeetinVender.value === 'xy') {\r\n    openMeetingXY({\r\n      wssServer: xy_wss_server.value,\r\n      httpServer: xy_http_server.value,\r\n      logServer: xy_log_server.value,\r\n      extId: xy_ext_id.value,\r\n      clientId: xy_client_id.value,\r\n      clientSecret: xy_client_secret.value,\r\n      meeting: form.meetingNumber,\r\n      meetingPassword: form.meetingPassword,\r\n      meetingName: user.value.userName\r\n    })\r\n    emit('callback')\r\n  }\r\n}\r\nconst genMeetingToken = async () => {\r\n  const { data } = await api.genMeetingToken()\r\n  openMeetingYL({\r\n    clientId: yl_client_id.value,\r\n    accessToken: data,\r\n    meetingNumber: form.meetingNumber,\r\n    meetingPassword: form.meetingPassword,\r\n    userName: user.value.userName\r\n  })\r\n  emit('callback')\r\n}\r\nconst resetForm = () => { emit('callback') }\r\n</script>\r\n<style lang=\"scss\">\r\n.JoinVideoMeeting {\r\n  width: 680px;\r\n\r\n  .InitVideoMeetingTime {\r\n    width: calc(580px + var(--zy-distance-two));\r\n\r\n    .zy-el-form-item__content {\r\n      display: flex;\r\n      justify-content: space-between;\r\n    }\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;EACOA,KAAK,EAAC;AAAkB;;EAQpBA,KAAK,EAAC;AAAkB;;;;;;uBARjCC,mBAAA,CAaM,OAbNC,UAaM,GAZJC,YAAA,CAWUC,kBAAA;IAXDC,GAAG,EAAC,SAAS;IAAEC,KAAK,EAAEC,MAAA,CAAAC,IAAI;IAAGC,KAAK,EAAEF,MAAA,CAAAE,KAAK;IAAEC,MAAM,EAAN,EAAM;IAAC,gBAAc,EAAC,KAAK;IAACV,KAAK,EAAC;;IAF1FW,OAAA,EAAAC,QAAA,CAGM;MAAA,OAEe,CAFfT,YAAA,CAEeU,uBAAA;QAFDC,KAAK,EAAC,MAAM;QAACC,IAAI,EAAC,eAAe;QAACf,KAAK,EAAC;;QAH5DW,OAAA,EAAAC,QAAA,CAIQ;UAAA,OAAyE,CAAzET,YAAA,CAAyEa,mBAAA;YAJjFC,UAAA,EAI2BV,MAAA,CAAAC,IAAI,CAACU,aAAa;YAJ7C,uBAAAC,MAAA,QAAAA,MAAA,gBAAAC,MAAA;cAAA,OAI2Bb,MAAA,CAAAC,IAAI,CAACU,aAAa,GAAAE,MAAA;YAAA;YAAEC,WAAW,EAAC,SAAS;YAACC,SAAS,EAAT;;;QAJrEC,CAAA;UAMMpB,YAAA,CAEeU,uBAAA;QAFDC,KAAK,EAAC,MAAM;QAACd,KAAK,EAAC;;QANvCW,OAAA,EAAAC,QAAA,CAOQ;UAAA,OAA2E,CAA3ET,YAAA,CAA2Ea,mBAAA;YAPnFC,UAAA,EAO2BV,MAAA,CAAAC,IAAI,CAACgB,eAAe;YAP/C,uBAAAL,MAAA,QAAAA,MAAA,gBAAAC,MAAA;cAAA,OAO2Bb,MAAA,CAAAC,IAAI,CAACgB,eAAe,GAAAJ,MAAA;YAAA;YAAEC,WAAW,EAAC,SAAS;YAACC,SAAS,EAAT;;;QAPvEC,CAAA;UASME,mBAAA,CAGM,OAHNC,UAGM,GAFJvB,YAAA,CAAqEwB,oBAAA;QAA1DC,IAAI,EAAC,SAAS;QAAEC,OAAK,EAAAV,MAAA,QAAAA,MAAA,gBAAAC,MAAA;UAAA,OAAEb,MAAA,CAAAuB,UAAU,CAACvB,MAAA,CAAAwB,OAAO;QAAA;;QAV5DpB,OAAA,EAAAC,QAAA,CAU+D;UAAA,OAAEO,MAAA,QAAAA,MAAA,OAVjEa,gBAAA,CAU+D,IAAE,E;;QAVjET,CAAA;UAWQpB,YAAA,CAA4CwB,oBAAA;QAAhCE,OAAK,EAAEtB,MAAA,CAAA0B;MAAS;QAXpCtB,OAAA,EAAAC,QAAA,CAWsC;UAAA,OAAEO,MAAA,QAAAA,MAAA,OAXxCa,gBAAA,CAWsC,IAAE,E;;QAXxCT,CAAA;;;IAAAA,CAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "import { onMounted } from 'vue';\nimport { GlobalTable } from 'common/js/GlobalTableTree.js';\nvar __default__ = {\n  name: 'EditRecordInfo'\n};\nexport default /*@__PURE__*/Object.assign(__default__, {\n  props: {\n    id: {\n      type: String,\n      default: ''\n    }\n  },\n  setup(__props, _ref) {\n    var __expose = _ref.expose;\n    __expose();\n    var props = __props;\n    var _GlobalTable = GlobalTable({\n        tableApi: 'editRecordInfo',\n        tableDataObj: {\n          detailId: props.id\n        }\n      }),\n      tableData = _GlobalTable.tableData,\n      handleQuery = _GlobalTable.handleQuery;\n    onMounted(function () {\n      handleQuery();\n    });\n    var __returned__ = {\n      props,\n      tableData,\n      handleQuery,\n      onMounted,\n      get GlobalTable() {\n        return GlobalTable;\n      }\n    };\n    Object.defineProperty(__returned__, '__isScriptSetup', {\n      enumerable: false,\n      value: true\n    });\n    return __returned__;\n  }\n});", "map": {"version": 3, "names": ["onMounted", "GlobalTable", "__default__", "name", "props", "__props", "_GlobalTable", "tableApi", "tableDataObj", "detailId", "id", "tableData", "handleQuery"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/common/components/edit-record/edit-record-info.vue"], "sourcesContent": ["<template>\r\n  <div class=\"edit-record-info\">\r\n    <div class=\"globalTable\">\r\n      <el-table :data=\"tableData\">\r\n        <el-table-column label=\"字段名称\" prop=\"editFieldName\" show-overflow-tooltip />\r\n        <el-table-column label=\"修改前\" prop=\"oldValue\" show-overflow-tooltip />\r\n        <el-table-column label=\"修改后\" prop=\"newValue\" show-overflow-tooltip />\r\n      </el-table>\r\n    </div>\r\n  </div>\r\n</template>\r\n<script>\r\nexport default { name: 'EditRecordInfo' }\r\n</script>\r\n<script setup>\r\nimport { onMounted } from 'vue'\r\nimport { GlobalTable } from 'common/js/GlobalTableTree.js'\r\nconst props = defineProps({ id: { type: String, default: '' } })\r\nconst { tableData, handleQuery } = GlobalTable({ tableApi: 'editRecordInfo', tableDataObj: { detailId: props.id } })\r\n\r\nonMounted(() => {\r\n  handleQuery()\r\n})\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n.edit-record-info {\r\n  width: 990px;\r\n  height: calc(85vh - 52px);\r\n  padding: 20px;\r\n\r\n  .globalTable {\r\n    width: 100%;\r\n    height: 100%;\r\n  }\r\n}\r\n</style>\r\n"], "mappings": "AAeA,SAASA,SAAS,QAAQ,KAAK;AAC/B,SAASC,WAAW,QAAQ,8BAA8B;AAJ1D,IAAAC,WAAA,GAAe;EAAEC,IAAI,EAAE;AAAiB,CAAC;;;;;;;;;;;IAKzC,IAAMC,KAAK,GAAGC,OAAkD;IAChE,IAAAC,YAAA,GAAmCL,WAAW,CAAC;QAAEM,QAAQ,EAAE,gBAAgB;QAAEC,YAAY,EAAE;UAAEC,QAAQ,EAAEL,KAAK,CAACM;QAAG;MAAE,CAAC,CAAC;MAA5GC,SAAS,GAAAL,YAAA,CAATK,SAAS;MAAEC,WAAW,GAAAN,YAAA,CAAXM,WAAW;IAE9BZ,SAAS,CAAC,YAAM;MACdY,WAAW,CAAC,CAAC;IACf,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
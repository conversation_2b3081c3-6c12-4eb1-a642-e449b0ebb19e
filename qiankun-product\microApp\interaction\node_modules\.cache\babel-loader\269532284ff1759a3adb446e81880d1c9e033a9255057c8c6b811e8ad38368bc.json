{"ast": null, "code": "function _toConsumableArray(r) { return _arrayWithoutHoles(r) || _iterableToArray(r) || _unsupportedIterableToArray(r) || _nonIterableSpread(); }\nfunction _nonIterableSpread() { throw new TypeError(\"Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); }\nfunction _unsupportedIterableToArray(r, a) { if (r) { if (\"string\" == typeof r) return _arrayLikeToArray(r, a); var t = {}.toString.call(r).slice(8, -1); return \"Object\" === t && r.constructor && (t = r.constructor.name), \"Map\" === t || \"Set\" === t ? Array.from(r) : \"Arguments\" === t || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t) ? _arrayLikeToArray(r, a) : void 0; } }\nfunction _iterableToArray(r) { if (\"undefined\" != typeof Symbol && null != r[Symbol.iterator] || null != r[\"@@iterator\"]) return Array.from(r); }\nfunction _arrayWithoutHoles(r) { if (Array.isArray(r)) return _arrayLikeToArray(r); }\nfunction _arrayLikeToArray(r, a) { (null == a || a > r.length) && (a = r.length); for (var e = 0, n = Array(a); e < a; e++) n[e] = r[e]; return n; }\nimport '../../../../hooks/index.mjs';\nimport { getFixedColumnsClass, getFixedColumnOffset, ensurePosition } from '../util.mjs';\nimport useMapState from './mapState-helper.mjs';\nimport { useNamespace } from '../../../../hooks/use-namespace/index.mjs';\nfunction useStyle(props) {\n  var _useMapState = useMapState(),\n    columns = _useMapState.columns;\n  var ns = useNamespace(\"table\");\n  var getCellClasses = function getCellClasses(columns2, cellIndex) {\n    var column = columns2[cellIndex];\n    var classes = [ns.e(\"cell\"), column.id, column.align, column.labelClassName].concat(_toConsumableArray(getFixedColumnsClass(ns.b(), cellIndex, column.fixed, props.store)));\n    if (column.className) {\n      classes.push(column.className);\n    }\n    if (!column.children) {\n      classes.push(ns.is(\"leaf\"));\n    }\n    return classes;\n  };\n  var getCellStyles = function getCellStyles(column, cellIndex) {\n    var fixedStyle = getFixedColumnOffset(cellIndex, column.fixed, props.store);\n    ensurePosition(fixedStyle, \"left\");\n    ensurePosition(fixedStyle, \"right\");\n    return fixedStyle;\n  };\n  return {\n    getCellClasses,\n    getCellStyles,\n    columns\n  };\n}\nexport { useStyle as default };", "map": {"version": 3, "names": ["useStyle", "props", "_useMapState", "useMapState", "columns", "ns", "useNamespace", "getCellClasses", "columns2", "cellIndex", "column", "classes", "e", "id", "align", "labelClassName", "concat", "_toConsumableArray", "getFixedColumnsClass", "b", "fixed", "store", "className", "push", "children", "is", "getCellStyles", "fixedStyle", "getFixedColumnOffset", "ensurePosition"], "sources": ["../../../../../../../packages/components/table/src/table-footer/style-helper.ts"], "sourcesContent": ["import { useNamespace } from '@element-plus/hooks'\nimport {\n  ensurePosition,\n  getFixedColumnOffset,\n  getFixedColumnsClass,\n} from '../util'\nimport useMapState from './mapState-helper'\nimport type { TableColumnCtx } from '../table-column/defaults'\nimport type { TableFooter } from '.'\n\nfunction useStyle<T>(props: TableFooter<T>) {\n  const { columns } = useMapState()\n  const ns = useNamespace('table')\n\n  const getCellClasses = (columns: TableColumnCtx<T>[], cellIndex: number) => {\n    const column = columns[cellIndex]\n    const classes = [\n      ns.e('cell'),\n      column.id,\n      column.align,\n      column.labelClassName,\n      ...getFixedColumnsClass(ns.b(), cellIndex, column.fixed, props.store),\n    ]\n    if (column.className) {\n      classes.push(column.className)\n    }\n    if (!column.children) {\n      classes.push(ns.is('leaf'))\n    }\n    return classes\n  }\n\n  const getCellStyles = (column: TableColumnCtx<T>, cellIndex: number) => {\n    const fixedStyle = getFixedColumnOffset(\n      cellIndex,\n      column.fixed,\n      props.store\n    )\n    ensurePosition(fixedStyle, 'left')\n    ensurePosition(fixedStyle, 'right')\n    return fixedStyle\n  }\n\n  return {\n    getCellClasses,\n    getCellStyles,\n    columns,\n  }\n}\n\nexport default useStyle\n"], "mappings": ";;;;;;;;;;AAOA,SAASA,QAAQA,CAACC,KAAK,EAAE;EACvB,IAAAC,YAAA,GAAoBC,WAAW,EAAE;IAAzBC,OAAO,GAAAF,YAAA,CAAPE,OAAO;EACf,IAAMC,EAAE,GAAGC,YAAY,CAAC,OAAO,CAAC;EAChC,IAAMC,cAAc,GAAG,SAAjBA,cAAcA,CAAIC,QAAQ,EAAEC,SAAS,EAAK;IAC9C,IAAMC,MAAM,GAAGF,QAAQ,CAACC,SAAS,CAAC;IAClC,IAAME,OAAO,IACXN,EAAE,CAACO,CAAC,CAAC,MAAM,CAAC,EACZF,MAAM,CAACG,EAAE,EACTH,MAAM,CAACI,KAAK,EACZJ,MAAM,CAACK,cAAc,EAAAC,MAAA,CAAAC,kBAAA,CAClBC,oBAAoB,CAACb,EAAE,CAACc,CAAC,EAAE,EAAEV,SAAS,EAAEC,MAAM,CAACU,KAAK,EAAEnB,KAAK,CAACoB,KAAK,CAAC,EACtE;IACD,IAAIX,MAAM,CAACY,SAAS,EAAE;MACpBX,OAAO,CAACY,IAAI,CAACb,MAAM,CAACY,SAAS,CAAC;IACpC;IACI,IAAI,CAACZ,MAAM,CAACc,QAAQ,EAAE;MACpBb,OAAO,CAACY,IAAI,CAAClB,EAAE,CAACoB,EAAE,CAAC,MAAM,CAAC,CAAC;IACjC;IACI,OAAOd,OAAO;EAClB,CAAG;EACD,IAAMe,aAAa,GAAG,SAAhBA,aAAaA,CAAIhB,MAAM,EAAED,SAAS,EAAK;IAC3C,IAAMkB,UAAU,GAAGC,oBAAoB,CAACnB,SAAS,EAAEC,MAAM,CAACU,KAAK,EAAEnB,KAAK,CAACoB,KAAK,CAAC;IAC7EQ,cAAc,CAACF,UAAU,EAAE,MAAM,CAAC;IAClCE,cAAc,CAACF,UAAU,EAAE,OAAO,CAAC;IACnC,OAAOA,UAAU;EACrB,CAAG;EACD,OAAO;IACLpB,cAAc;IACdmB,aAAa;IACbtB;EACJ,CAAG;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
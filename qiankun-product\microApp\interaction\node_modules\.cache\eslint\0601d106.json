[{"D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\interaction\\src\\views\\LiveManagement\\LiveManagementDetails.vue": "1", "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\interaction\\src\\router\\index.js": "2", "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\interaction\\src\\views\\LiveManagement\\LiveBroadcastDetails.vue": "3"}, {"size": 15344, "mtime": 1756863380916, "results": "4", "hashOfConfig": "5"}, {"size": 8685, "mtime": 1756885820444, "results": "6", "hashOfConfig": "5"}, {"size": 31991, "mtime": 1756887037862, "results": "7", "hashOfConfig": "5"}, {"filePath": "8", "messages": "9", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1bf5quz", {"filePath": "10", "messages": "11", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "12", "messages": "13", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\interaction\\src\\views\\LiveManagement\\LiveManagementDetails.vue", [], "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\interaction\\src\\router\\index.js", [], "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\interaction\\src\\views\\LiveManagement\\LiveBroadcastDetails.vue", ["14", "15"], {"ruleId": "16", "severity": 2, "message": "17", "line": 312, "column": 7, "nodeType": "18", "messageId": "19", "endLine": 312, "endColumn": 25}, {"ruleId": "16", "severity": 2, "message": "20", "line": 331, "column": 7, "nodeType": "18", "messageId": "19", "endLine": 331, "endColumn": 26}, "no-unused-vars", "'getLiveBrowseCount' is assigned a value but never used.", "Identifier", "unusedVar", "'startCommentRefresh' is assigned a value but never used."]
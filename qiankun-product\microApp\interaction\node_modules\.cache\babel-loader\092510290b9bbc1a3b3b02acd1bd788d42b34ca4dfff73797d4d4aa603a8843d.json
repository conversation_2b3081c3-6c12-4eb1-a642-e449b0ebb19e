{"ast": null, "code": "import { resolveComponent as _resolveComponent, createVNode as _createVNode, withCtx as _withCtx, createTextVNode as _createTextVNode, createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock } from \"vue\";\nvar _hoisted_1 = {\n  class: \"GlobalCommentManageEdit\"\n};\nvar _hoisted_2 = {\n  class: \"globalFormButton\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  var _component_el_input = _resolveComponent(\"el-input\");\n  var _component_el_form_item = _resolveComponent(\"el-form-item\");\n  var _component_xyl_upload_file = _resolveComponent(\"xyl-upload-file\");\n  var _component_el_button = _resolveComponent(\"el-button\");\n  var _component_el_form = _resolveComponent(\"el-form\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createVNode(_component_el_form, {\n    ref: \"formRef\",\n    model: $setup.form,\n    rules: $setup.rules,\n    inline: \"\",\n    \"label-position\": \"top\",\n    class: \"globalForm\"\n  }, {\n    default: _withCtx(function () {\n      return [_createVNode(_component_el_form_item, {\n        label: \"内容\",\n        prop: \"commentContent\",\n        class: \"globalFormTitle\"\n      }, {\n        default: _withCtx(function () {\n          return [_createVNode(_component_el_input, {\n            modelValue: $setup.form.commentContent,\n            \"onUpdate:modelValue\": _cache[0] || (_cache[0] = function ($event) {\n              return $setup.form.commentContent = $event;\n            }),\n            type: \"textarea\",\n            placeholder: \"请输入内容\",\n            rows: \"6\"\n          }, null, 8 /* PROPS */, [\"modelValue\"])];\n        }),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_form_item, {\n        label: \"附件\",\n        class: \"globalFormTitle\"\n      }, {\n        default: _withCtx(function () {\n          return [_createVNode(_component_xyl_upload_file, {\n            fileData: $setup.fileData,\n            onFileUpload: $setup.fileUpload\n          }, null, 8 /* PROPS */, [\"fileData\"])];\n        }),\n        _: 1 /* STABLE */\n      }), _createElementVNode(\"div\", _hoisted_2, [_createVNode(_component_el_button, {\n        type: \"primary\",\n        onClick: _cache[1] || (_cache[1] = function ($event) {\n          return $setup.submitForm($setup.formRef);\n        })\n      }, {\n        default: _withCtx(function () {\n          return _cache[2] || (_cache[2] = [_createTextVNode(\"提交\")]);\n        }),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_button, {\n        onClick: $setup.resetForm\n      }, {\n        default: _withCtx(function () {\n          return _cache[3] || (_cache[3] = [_createTextVNode(\"取消\")]);\n        }),\n        _: 1 /* STABLE */\n      })])];\n    }),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"model\", \"rules\"])]);\n}", "map": {"version": 3, "names": ["class", "_createElementBlock", "_hoisted_1", "_createVNode", "_component_el_form", "ref", "model", "$setup", "form", "rules", "inline", "default", "_withCtx", "_component_el_form_item", "label", "prop", "_component_el_input", "modelValue", "commentContent", "_cache", "$event", "type", "placeholder", "rows", "_", "_component_xyl_upload_file", "fileData", "onFileUpload", "fileUpload", "_createElementVNode", "_hoisted_2", "_component_el_button", "onClick", "submitForm", "formRef", "_createTextVNode", "resetForm"], "sources": ["D:\\zy\\xm\\pc\\5.0\\qiankun-product\\common\\components\\global-comment-manage\\global-comment-manage-edit.vue"], "sourcesContent": ["<template>\r\n  <div class=\"GlobalCommentManageEdit\">\r\n    <el-form ref=\"formRef\" :model=\"form\" :rules=\"rules\" inline label-position=\"top\" class=\"globalForm\">\r\n      <el-form-item label=\"内容\" prop=\"commentContent\" class=\"globalFormTitle\">\r\n        <el-input v-model=\"form.commentContent\" type=\"textarea\" placeholder=\"请输入内容\" rows=\"6\" />\r\n      </el-form-item>\r\n      <el-form-item label=\"附件\" class=\"globalFormTitle\">\r\n        <xyl-upload-file :fileData=\"fileData\" @fileUpload=\"fileUpload\" />\r\n      </el-form-item>\r\n      <div class=\"globalFormButton\">\r\n        <el-button type=\"primary\" @click=\"submitForm(formRef)\">提交</el-button>\r\n        <el-button @click=\"resetForm\">取消</el-button>\r\n      </div>\r\n    </el-form>\r\n  </div>\r\n</template>\r\n<script>\r\nexport default { name: 'GlobalCommentManageEdit' }\r\n</script>\r\n<script setup>\r\nimport api from '@/api'\r\nimport { reactive, ref, onMounted } from 'vue'\r\nimport { ElMessage } from 'element-plus'\r\nconst props = defineProps({ id: { type: String, default: '' } })\r\nconst emit = defineEmits(['callback'])\r\n\r\nconst formRef = ref()\r\nconst form = reactive({\r\n  businessId: '',\r\n  commentContent: '', // 内容\r\n  terminalName: '', // 来源\r\n  commentContent: '' // 内容\r\n})\r\nconst rules = reactive({ commentContent: [{ required: true, message: '请输入内容', trigger: ['blur', 'change'] }] })\r\nconst fileData = ref([])\r\nonMounted(() => {\r\n  if (props.id) {\r\n    commentInfo()\r\n  }\r\n})\r\n\r\nconst commentInfo = async () => {\r\n  const res = await api.commentInfo({ detailId: props.id })\r\n  var { data } = res\r\n  form.businessId = data.businessId\r\n  form.commentContent = data.commentContent // 内容\r\n  form.terminalName = data.terminalName // 来源\r\n  fileData.value = data.attachments\r\n}\r\nconst fileUpload = (file) => {\r\n  fileData.value = file\r\n}\r\nconst submitForm = async (formEl) => {\r\n  if (!formEl) return\r\n  await formEl.validate((valid, fields) => {\r\n    if (valid) {\r\n      commentEdit()\r\n    } else {\r\n      ElMessage({ type: 'warning', message: '请根据提示信息完善字段内容！' })\r\n    }\r\n  })\r\n}\r\nconst commentEdit = async () => {\r\n  const { code } = await api.commentEdit({\r\n    form: {\r\n      id: props.id,\r\n      businessId: form.businessId,\r\n      commentContent: form.commentContent, // 内容\r\n      terminalName: form.terminalName || 'PC', // 来源\r\n      attachmentIds: fileData.value.map((v) => v.id).join(',')\r\n    }\r\n  })\r\n  if (code === 200) {\r\n    ElMessage({ type: 'success', message: props.id ? '编辑成功' : '新增成功' })\r\n    emit('callback')\r\n  }\r\n}\r\nconst resetForm = () => {\r\n  emit('callback')\r\n}\r\n</script>\r\n<style lang=\"scss\">\r\n.GlobalCommentManageEdit {\r\n  width: 680px;\r\n}\r\n</style>\r\n"], "mappings": ";;EACOA,KAAK,EAAC;AAAyB;;EAQ3BA,KAAK,EAAC;AAAkB;;;;;;;uBARjCC,mBAAA,CAaM,OAbNC,UAaM,GAZJC,YAAA,CAWUC,kBAAA;IAXDC,GAAG,EAAC,SAAS;IAAEC,KAAK,EAAEC,MAAA,CAAAC,IAAI;IAAGC,KAAK,EAAEF,MAAA,CAAAE,KAAK;IAAEC,MAAM,EAAN,EAAM;IAAC,gBAAc,EAAC,KAAK;IAACV,KAAK,EAAC;;IAF1FW,OAAA,EAAAC,QAAA,CAGM;MAAA,OAEe,CAFfT,YAAA,CAEeU,uBAAA;QAFDC,KAAK,EAAC,IAAI;QAACC,IAAI,EAAC,gBAAgB;QAACf,KAAK,EAAC;;QAH3DW,OAAA,EAAAC,QAAA,CAIQ;UAAA,OAAuF,CAAvFT,YAAA,CAAuFa,mBAAA;YAJ/FC,UAAA,EAI2BV,MAAA,CAAAC,IAAI,CAACU,cAAc;YAJ9C,uBAAAC,MAAA,QAAAA,MAAA,gBAAAC,MAAA;cAAA,OAI2Bb,MAAA,CAAAC,IAAI,CAACU,cAAc,GAAAE,MAAA;YAAA;YAAEC,IAAI,EAAC,UAAU;YAACC,WAAW,EAAC,OAAO;YAACC,IAAI,EAAC;;;QAJzFC,CAAA;UAMMrB,YAAA,CAEeU,uBAAA;QAFDC,KAAK,EAAC,IAAI;QAACd,KAAK,EAAC;;QANrCW,OAAA,EAAAC,QAAA,CAOQ;UAAA,OAAiE,CAAjET,YAAA,CAAiEsB,0BAAA;YAA/CC,QAAQ,EAAEnB,MAAA,CAAAmB,QAAQ;YAAGC,YAAU,EAAEpB,MAAA,CAAAqB;;;QAP3DJ,CAAA;UASMK,mBAAA,CAGM,OAHNC,UAGM,GAFJ3B,YAAA,CAAqE4B,oBAAA;QAA1DV,IAAI,EAAC,SAAS;QAAEW,OAAK,EAAAb,MAAA,QAAAA,MAAA,gBAAAC,MAAA;UAAA,OAAEb,MAAA,CAAA0B,UAAU,CAAC1B,MAAA,CAAA2B,OAAO;QAAA;;QAV5DvB,OAAA,EAAAC,QAAA,CAU+D;UAAA,OAAEO,MAAA,QAAAA,MAAA,OAVjEgB,gBAAA,CAU+D,IAAE,E;;QAVjEX,CAAA;UAWQrB,YAAA,CAA4C4B,oBAAA;QAAhCC,OAAK,EAAEzB,MAAA,CAAA6B;MAAS;QAXpCzB,OAAA,EAAAC,QAAA,CAWsC;UAAA,OAAEO,MAAA,QAAAA,MAAA,OAXxCgB,gBAAA,CAWsC,IAAE,E;;QAXxCX,CAAA;;;IAAAA,CAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
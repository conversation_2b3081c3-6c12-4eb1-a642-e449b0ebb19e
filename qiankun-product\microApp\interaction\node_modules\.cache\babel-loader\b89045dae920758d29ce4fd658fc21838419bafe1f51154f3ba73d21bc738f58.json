{"ast": null, "code": "import { resolveComponent as _resolveComponent, with<PERSON><PERSON><PERSON> as _withKeys, createVNode as _createVNode, renderList as _renderList, Fragment as _Fragment, openBlock as _openBlock, createElementBlock as _createElementBlock, createBlock as _createBlock, withCtx as _withCtx, createElementVNode as _createElementVNode } from \"vue\";\nvar _hoisted_1 = {\n  class: \"FrequentContactUser\"\n};\nvar _hoisted_2 = {\n  class: \"globalTable\"\n};\nvar _hoisted_3 = {\n  class: \"globalPagination\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  var _component_el_input = _resolveComponent(\"el-input\");\n  var _component_el_option = _resolveComponent(\"el-option\");\n  var _component_el_select = _resolveComponent(\"el-select\");\n  var _component_xyl_search_button = _resolveComponent(\"xyl-search-button\");\n  var _component_el_table_column = _resolveComponent(\"el-table-column\");\n  var _component_el_table = _resolveComponent(\"el-table\");\n  var _component_el_pagination = _resolveComponent(\"el-pagination\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createVNode(_component_xyl_search_button, {\n    onQueryClick: $setup.handleQuery,\n    onResetClick: $setup.handleReset,\n    onHandleButton: $setup.handleButton,\n    buttonList: $setup.buttonList\n  }, {\n    search: _withCtx(function () {\n      return [_createVNode(_component_el_input, {\n        modelValue: $setup.keyword,\n        \"onUpdate:modelValue\": _cache[0] || (_cache[0] = function ($event) {\n          return $setup.keyword = $event;\n        }),\n        placeholder: \"请输入关键词\",\n        onKeyup: _withKeys($setup.handleQuery, [\"enter\"]),\n        clearable: \"\"\n      }, null, 8 /* PROPS */, [\"modelValue\", \"onKeyup\"]), _createVNode(_component_el_select, {\n        modelValue: $setup.roleId,\n        \"onUpdate:modelValue\": _cache[1] || (_cache[1] = function ($event) {\n          return $setup.roleId = $event;\n        }),\n        onChange: $setup.queryChange,\n        placeholder: \"请选择角色\",\n        clearable: \"\"\n      }, {\n        default: _withCtx(function () {\n          return [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.roleData, function (item) {\n            return _openBlock(), _createBlock(_component_el_option, {\n              key: item.key,\n              label: item.name,\n              value: item.key\n            }, null, 8 /* PROPS */, [\"label\", \"value\"]);\n          }), 128 /* KEYED_FRAGMENT */))];\n        }),\n        _: 1 /* STABLE */\n      }, 8 /* PROPS */, [\"modelValue\"])];\n    }),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"onQueryClick\", \"buttonList\"]), _createElementVNode(\"div\", _hoisted_2, [_createVNode(_component_el_table, {\n    ref: \"tableRef\",\n    \"row-key\": \"id\",\n    data: $setup.tableData,\n    onSelect: $setup.handleTableSelect,\n    onSelectAll: $setup.handleTableSelect\n  }, {\n    default: _withCtx(function () {\n      return [_createVNode(_component_el_table_column, {\n        type: \"selection\",\n        \"reserve-selection\": \"\",\n        width: \"60\",\n        fixed: \"\"\n      }), _createVNode(_component_el_table_column, {\n        label: \"姓名\",\n        \"min-width\": \"120\",\n        prop: \"userName\"\n      }), _createVNode(_component_el_table_column, {\n        label: \"性别\",\n        width: \"80\",\n        prop: \"sex.label\"\n      }), _createVNode(_component_el_table_column, {\n        label: \"民族\",\n        width: \"100\",\n        prop: \"nation.label\"\n      }), _createVNode(_component_el_table_column, {\n        label: \"联系电话\",\n        width: \"160\",\n        prop: \"mobile\"\n      }), _createVNode(_component_el_table_column, {\n        label: \"单位及职务\",\n        \"min-width\": \"260\",\n        \"show-overflow-tooltip\": \"\",\n        prop: \"position\"\n      })];\n    }),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"data\", \"onSelect\", \"onSelectAll\"])]), _createElementVNode(\"div\", _hoisted_3, [_createVNode(_component_el_pagination, {\n    currentPage: $setup.pageNo,\n    \"onUpdate:currentPage\": _cache[2] || (_cache[2] = function ($event) {\n      return $setup.pageNo = $event;\n    }),\n    \"page-size\": $setup.pageSize,\n    \"onUpdate:pageSize\": _cache[3] || (_cache[3] = function ($event) {\n      return $setup.pageSize = $event;\n    }),\n    \"page-sizes\": $setup.pageSizes,\n    layout: \"total, sizes, prev, pager, next, jumper\",\n    onSizeChange: $setup.handleQuery,\n    onCurrentChange: $setup.handleQuery,\n    total: $setup.totals,\n    background: \"\"\n  }, null, 8 /* PROPS */, [\"currentPage\", \"page-size\", \"page-sizes\", \"onSizeChange\", \"onCurrentChange\", \"total\"])])]);\n}", "map": {"version": 3, "names": ["class", "_createElementBlock", "_hoisted_1", "_createVNode", "_component_xyl_search_button", "onQueryClick", "$setup", "handleQuery", "onResetClick", "handleReset", "onHandleButton", "handleButton", "buttonList", "search", "_withCtx", "_component_el_input", "modelValue", "keyword", "_cache", "$event", "placeholder", "onKeyup", "_with<PERSON><PERSON><PERSON>", "clearable", "_component_el_select", "roleId", "onChange", "query<PERSON>hange", "default", "_Fragment", "_renderList", "roleData", "item", "_createBlock", "_component_el_option", "key", "label", "name", "value", "_", "_createElementVNode", "_hoisted_2", "_component_el_table", "ref", "data", "tableData", "onSelect", "handleTableSelect", "onSelectAll", "_component_el_table_column", "type", "width", "fixed", "prop", "_hoisted_3", "_component_el_pagination", "currentPage", "pageNo", "pageSize", "pageSizes", "layout", "onSizeChange", "onCurrentChange", "total", "totals", "background"], "sources": ["D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\interaction\\src\\views\\FrequentContact\\component\\FrequentContactUser.vue"], "sourcesContent": ["<template>\r\n  <div class=\"FrequentContactUser\">\r\n    <xyl-search-button @queryClick=\"handleQuery\" @resetClick=\"handleReset\" @handleButton=\"handleButton\"\r\n      :buttonList=\"buttonList\">\r\n      <template #search>\r\n        <el-input v-model=\"keyword\" placeholder=\"请输入关键词\" @keyup.enter=\"handleQuery\" clearable />\r\n        <el-select v-model=\"roleId\" @change=\"queryChange\" placeholder=\"请选择角色\" clearable>\r\n          <el-option v-for=\"item in roleData\" :key=\"item.key\" :label=\"item.name\" :value=\"item.key\" />\r\n        </el-select>\r\n      </template>\r\n    </xyl-search-button>\r\n    <div class=\"globalTable\">\r\n      <el-table ref=\"tableRef\" row-key=\"id\" :data=\"tableData\" @select=\"handleTableSelect\"\r\n        @select-all=\"handleTableSelect\">\r\n        <el-table-column type=\"selection\" reserve-selection width=\"60\" fixed />\r\n        <el-table-column label=\"姓名\" min-width=\"120\" prop=\"userName\" />\r\n        <el-table-column label=\"性别\" width=\"80\" prop=\"sex.label\" />\r\n        <el-table-column label=\"民族\" width=\"100\" prop=\"nation.label\" />\r\n        <el-table-column label=\"联系电话\" width=\"160\" prop=\"mobile\" />\r\n        <el-table-column label=\"单位及职务\" min-width=\"260\" show-overflow-tooltip prop=\"position\" />\r\n      </el-table>\r\n    </div>\r\n    <div class=\"globalPagination\">\r\n      <el-pagination v-model:currentPage=\"pageNo\" v-model:page-size=\"pageSize\" :page-sizes=\"pageSizes\"\r\n        layout=\"total, sizes, prev, pager, next, jumper\" @size-change=\"handleQuery\" @current-change=\"handleQuery\"\r\n        :total=\"totals\" background />\r\n    </div>\r\n  </div>\r\n</template>\r\n<script>\r\nexport default { name: 'FrequentContactUser' }\r\n</script>\r\n<script setup>\r\nimport api from '@/api'\r\nimport { ref, onMounted } from 'vue'\r\nimport { GlobalTable } from 'common/js/GlobalTable.js'\r\nimport { ElMessage } from 'element-plus'\r\nconst props = defineProps({ id: { type: String, default: '' } })\r\nconst emit = defineEmits(['callback'])\r\nconst buttonList = ref([\r\n  { id: 'submit', name: '确定', type: 'primary', has: '' },\r\n  { id: 'reset', name: '取消', type: '', has: '' }\r\n])\r\nconst roleId = ref('')\r\nconst roleData = ref([])\r\nconst {\r\n  keyword,\r\n  tableRef,\r\n  totals,\r\n  pageNo,\r\n  pageSize,\r\n  pageSizes,\r\n  tableData,\r\n  handleQuery,\r\n  tableDataArray,\r\n  handleTableSelect,\r\n  tableQuery\r\n} = GlobalTable({ tableApi: 'chooseUserTagFindPrepareUser' })\r\n\r\nonMounted(() => {\r\n  chooseUserTagInfo()\r\n  chooseUserTagRoleSelector()\r\n})\r\nconst chooseUserTagInfo = async () => {\r\n  const { data } = await api.chooseUserTagInfo({ detailId: props.id })\r\n  for (let index = 0; index < (data?.users?.length || 0); index++) {\r\n    const item = data?.users[index]\r\n    tableRef.value.toggleRowSelection({ ...item, id: item.userId }, true)\r\n  }\r\n  handleQuery()\r\n}\r\n\r\nconst chooseUserTagRoleSelector = async () => {\r\n  const { data } = await api.chooseUserTagRoleSelector({})\r\n  roleData.value = data\r\n}\r\n\r\nconst handleButton = (isType, params) => {\r\n  switch (isType) {\r\n    case 'submit':\r\n      handleUser()\r\n      break\r\n    case 'reset':\r\n      emit('callback')\r\n      break\r\n    default:\r\n      break\r\n  }\r\n}\r\nconst handleUser = async () => {\r\n  const { code } = await api.chooseUserTagJoinUser({ chooseTagId: props.id, userIds: tableDataArray.value.map(v => v.id) })\r\n  if (code === 200) {\r\n    ElMessage({ type: 'success', message: '关联用户成功' })\r\n    emit('callback')\r\n  }\r\n}\r\n\r\nconst queryChange = () => {\r\n  tableQuery.value = { roleId: roleId.value || null }\r\n}\r\nconst handleReset = () => {\r\n  keyword.value = ''\r\n  roleId.value = ''\r\n  tableQuery.value = { roleId: roleId.value || null }\r\n  handleQuery()\r\n}\r\n</script>\r\n<style lang=\"scss\">\r\n.FrequentContactUser {\r\n  width: 990px;\r\n  height: calc(85vh - 52px);\r\n  padding: 0 20px;\r\n\r\n  .xyl-search-button {\r\n    .xyl-button {\r\n      width: calc(100% - 660px);\r\n    }\r\n\r\n    .xyl-search {\r\n      width: 660px;\r\n\r\n      .zy-el-select {\r\n        margin-left: 20px;\r\n      }\r\n    }\r\n  }\r\n\r\n  .globalTable {\r\n    width: 100%;\r\n    height: calc(100% - (var(--zy-height) + (var(--zy-distance-four) * 2) + 42px));\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;EACOA,KAAK,EAAC;AAAqB;;EAUzBA,KAAK,EAAC;AAAa;;EAWnBA,KAAK,EAAC;AAAkB;;;;;;;;;uBArB/BC,mBAAA,CA0BM,OA1BNC,UA0BM,GAzBJC,YAAA,CAQoBC,4BAAA;IARAC,YAAU,EAAEC,MAAA,CAAAC,WAAW;IAAGC,YAAU,EAAEF,MAAA,CAAAG,WAAW;IAAGC,cAAY,EAAEJ,MAAA,CAAAK,YAAY;IAC/FC,UAAU,EAAEN,MAAA,CAAAM;;IACFC,MAAM,EAAAC,QAAA,CACf;MAAA,OAAwF,CAAxFX,YAAA,CAAwFY,mBAAA;QALhGC,UAAA,EAK2BV,MAAA,CAAAW,OAAO;QALlC,uBAAAC,MAAA,QAAAA,MAAA,gBAAAC,MAAA;UAAA,OAK2Bb,MAAA,CAAAW,OAAO,GAAAE,MAAA;QAAA;QAAEC,WAAW,EAAC,QAAQ;QAAEC,OAAK,EAL/DC,SAAA,CAKuEhB,MAAA,CAAAC,WAAW;QAAEgB,SAAS,EAAT;0DAC5EpB,YAAA,CAEYqB,oBAAA;QARpBR,UAAA,EAM4BV,MAAA,CAAAmB,MAAM;QANlC,uBAAAP,MAAA,QAAAA,MAAA,gBAAAC,MAAA;UAAA,OAM4Bb,MAAA,CAAAmB,MAAM,GAAAN,MAAA;QAAA;QAAGO,QAAM,EAAEpB,MAAA,CAAAqB,WAAW;QAAEP,WAAW,EAAC,OAAO;QAACG,SAAS,EAAT;;QAN9EK,OAAA,EAAAd,QAAA,CAOqB;UAAA,OAAwB,E,kBAAnCb,mBAAA,CAA2F4B,SAAA,QAPrGC,WAAA,CAOoCxB,MAAA,CAAAyB,QAAQ,EAP5C,UAO4BC,IAAI;iCAAtBC,YAAA,CAA2FC,oBAAA;cAAtDC,GAAG,EAAEH,IAAI,CAACG,GAAG;cAAGC,KAAK,EAAEJ,IAAI,CAACK,IAAI;cAAGC,KAAK,EAAEN,IAAI,CAACG;;;;QAP9FI,CAAA;;;IAAAA,CAAA;qDAWIC,mBAAA,CAUM,OAVNC,UAUM,GATJtC,YAAA,CAQWuC,mBAAA;IARDC,GAAG,EAAC,UAAU;IAAC,SAAO,EAAC,IAAI;IAAEC,IAAI,EAAEtC,MAAA,CAAAuC,SAAS;IAAGC,QAAM,EAAExC,MAAA,CAAAyC,iBAAiB;IAC/EC,WAAU,EAAE1C,MAAA,CAAAyC;;IAbrBnB,OAAA,EAAAd,QAAA,CAcQ;MAAA,OAAuE,CAAvEX,YAAA,CAAuE8C,0BAAA;QAAtDC,IAAI,EAAC,WAAW;QAAC,mBAAiB,EAAjB,EAAiB;QAACC,KAAK,EAAC,IAAI;QAACC,KAAK,EAAL;UAC/DjD,YAAA,CAA8D8C,0BAAA;QAA7Cb,KAAK,EAAC,IAAI;QAAC,WAAS,EAAC,KAAK;QAACiB,IAAI,EAAC;UACjDlD,YAAA,CAA0D8C,0BAAA;QAAzCb,KAAK,EAAC,IAAI;QAACe,KAAK,EAAC,IAAI;QAACE,IAAI,EAAC;UAC5ClD,YAAA,CAA8D8C,0BAAA;QAA7Cb,KAAK,EAAC,IAAI;QAACe,KAAK,EAAC,KAAK;QAACE,IAAI,EAAC;UAC7ClD,YAAA,CAA0D8C,0BAAA;QAAzCb,KAAK,EAAC,MAAM;QAACe,KAAK,EAAC,KAAK;QAACE,IAAI,EAAC;UAC/ClD,YAAA,CAAuF8C,0BAAA;QAAtEb,KAAK,EAAC,OAAO;QAAC,WAAS,EAAC,KAAK;QAAC,uBAAqB,EAArB,EAAqB;QAACiB,IAAI,EAAC;;;IAnBlFd,CAAA;4DAsBIC,mBAAA,CAIM,OAJNc,UAIM,GAHJnD,YAAA,CAE+BoD,wBAAA;IAFRC,WAAW,EAAElD,MAAA,CAAAmD,MAAM;IAvBhD,wBAAAvC,MAAA,QAAAA,MAAA,gBAAAC,MAAA;MAAA,OAuB0Cb,MAAA,CAAAmD,MAAM,GAAAtC,MAAA;IAAA;IAAU,WAAS,EAAEb,MAAA,CAAAoD,QAAQ;IAvB7E,qBAAAxC,MAAA,QAAAA,MAAA,gBAAAC,MAAA;MAAA,OAuBqEb,MAAA,CAAAoD,QAAQ,GAAAvC,MAAA;IAAA;IAAG,YAAU,EAAEb,MAAA,CAAAqD,SAAS;IAC7FC,MAAM,EAAC,yCAAyC;IAAEC,YAAW,EAAEvD,MAAA,CAAAC,WAAW;IAAGuD,eAAc,EAAExD,MAAA,CAAAC,WAAW;IACvGwD,KAAK,EAAEzD,MAAA,CAAA0D,MAAM;IAAEC,UAAU,EAAV", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
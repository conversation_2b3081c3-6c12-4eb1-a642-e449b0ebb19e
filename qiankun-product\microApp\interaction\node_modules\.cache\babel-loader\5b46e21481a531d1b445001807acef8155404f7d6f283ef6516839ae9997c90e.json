{"ast": null, "code": "import { createTextVNode as _createTextVNode, resolveComponent as _resolveComponent, withCtx as _withCtx, createVNode as _createVNode, openBlock as _openBlock, createBlock as _createBlock, createCommentVNode as _createCommentVNode, createElementVNode as _createElementVNode, createElementBlock as _createElementBlock } from \"vue\";\nvar _hoisted_1 = {\n  class: \"SendTextMessageAdd\"\n};\nvar _hoisted_2 = {\n  class: \"globalFormButton\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  var _component_el_radio = _resolveComponent(\"el-radio\");\n  var _component_el_radio_group = _resolveComponent(\"el-radio-group\");\n  var _component_el_form_item = _resolveComponent(\"el-form-item\");\n  var _component_business_select_person = _resolveComponent(\"business-select-person\");\n  var _component_el_button = _resolveComponent(\"el-button\");\n  var _component_el_form = _resolveComponent(\"el-form\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createVNode(_component_el_form, {\n    ref: \"formRef\",\n    model: $setup.form,\n    rules: $setup.rules,\n    inline: \"\",\n    \"label-position\": \"top\",\n    class: \"globalForm\"\n  }, {\n    default: _withCtx(function () {\n      return [_createVNode(_component_el_form_item, {\n        label: \"接收对象类型\",\n        prop: \"type\",\n        class: \"globalFormTitle\"\n      }, {\n        default: _withCtx(function () {\n          return [_createVNode(_component_el_radio_group, {\n            modelValue: $setup.form.type,\n            \"onUpdate:modelValue\": _cache[0] || (_cache[0] = function ($event) {\n              return $setup.form.type = $event;\n            }),\n            onChange: $setup.typeChange,\n            disabled: $setup.isShow\n          }, {\n            default: _withCtx(function () {\n              return [_createVNode(_component_el_radio, {\n                label: \"relationBookSystemTemp\"\n              }, {\n                default: _withCtx(function () {\n                  return _cache[3] || (_cache[3] = [_createTextVNode(\"系统通讯录\")]);\n                }),\n                _: 1 /* STABLE */\n              }), _createVNode(_component_el_radio, {\n                label: \"1\"\n              }, {\n                default: _withCtx(function () {\n                  return _cache[4] || (_cache[4] = [_createTextVNode(\"系统内用户\")]);\n                }),\n                _: 1 /* STABLE */\n              })];\n            }),\n            _: 1 /* STABLE */\n          }, 8 /* PROPS */, [\"modelValue\", \"disabled\"])];\n        }),\n        _: 1 /* STABLE */\n      }), $setup.form.type !== '4' ? (_openBlock(), _createBlock(_component_el_form_item, {\n        key: 0,\n        label: \"接收对象\",\n        prop: \"memberUserIds\",\n        class: \"globalFormTitle\"\n      }, {\n        default: _withCtx(function () {\n          return [_createVNode(_component_business_select_person, {\n            modelValue: $setup.form.memberUserIds,\n            \"onUpdate:modelValue\": _cache[1] || (_cache[1] = function ($event) {\n              return $setup.form.memberUserIds = $event;\n            }),\n            onCallback: $setup.userCallback,\n            disabled: $setup.isShow,\n            tabCode: $setup.form.type === '1' ? $setup.tabCode : [$setup.form.type]\n          }, null, 8 /* PROPS */, [\"modelValue\", \"disabled\", \"tabCode\"])];\n        }),\n        _: 1 /* STABLE */\n      })) : _createCommentVNode(\"v-if\", true), _createElementVNode(\"div\", _hoisted_2, [_createVNode(_component_el_button, {\n        type: \"primary\",\n        onClick: _cache[2] || (_cache[2] = function ($event) {\n          return $setup.submitForm($setup.formRef);\n        }),\n        disabled: $setup.isShow\n      }, {\n        default: _withCtx(function () {\n          return _cache[5] || (_cache[5] = [_createTextVNode(\"提交\")]);\n        }),\n        _: 1 /* STABLE */\n      }, 8 /* PROPS */, [\"disabled\"]), _createVNode(_component_el_button, {\n        onClick: $setup.resetForm\n      }, {\n        default: _withCtx(function () {\n          return _cache[6] || (_cache[6] = [_createTextVNode(\"取消\")]);\n        }),\n        _: 1 /* STABLE */\n      })])];\n    }),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"model\", \"rules\"])]);\n}", "map": {"version": 3, "names": ["class", "_createElementBlock", "_hoisted_1", "_createVNode", "_component_el_form", "ref", "model", "$setup", "form", "rules", "inline", "default", "_withCtx", "_component_el_form_item", "label", "prop", "_component_el_radio_group", "modelValue", "type", "_cache", "$event", "onChange", "typeChange", "disabled", "isShow", "_component_el_radio", "_createTextVNode", "_", "_createBlock", "key", "_component_business_select_person", "memberUserIds", "onCallback", "userCallback", "tabCode", "_createCommentVNode", "_createElementVNode", "_hoisted_2", "_component_el_button", "onClick", "submitForm", "formRef", "resetForm"], "sources": ["D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\interaction\\src\\views\\TextMessageManage\\component\\SendTextMessageAdd.vue"], "sourcesContent": ["<template>\r\n  <div class=\"SendTextMessageAdd\">\r\n    <el-form ref=\"formRef\"\r\n             :model=\"form\"\r\n             :rules=\"rules\"\r\n             inline\r\n             label-position=\"top\"\r\n             class=\"globalForm\">\r\n      <el-form-item label=\"接收对象类型\"\r\n                    prop=\"type\"\r\n                    class=\"globalFormTitle\">\r\n        <el-radio-group v-model=\"form.type\"\r\n                        @change=\"typeChange\"\r\n                        :disabled=\"isShow\">\r\n          <el-radio label=\"relationBookSystemTemp\">系统通讯录</el-radio>\r\n          <el-radio label=\"1\">系统内用户</el-radio>\r\n        </el-radio-group>\r\n      </el-form-item>\r\n      <el-form-item label=\"接收对象\"\r\n                    v-if=\"form.type !== '4'\"\r\n                    prop=\"memberUserIds\"\r\n                    class=\"globalFormTitle\">\r\n        <business-select-person v-model=\"form.memberUserIds\"\r\n                                @callback=\"userCallback\"\r\n                                :disabled=\"isShow\"\r\n                                :tabCode=\"form.type === '1' ? tabCode : [form.type]\"></business-select-person>\r\n      </el-form-item>\r\n      <div class=\"globalFormButton\">\r\n        <el-button type=\"primary\"\r\n                   @click=\"submitForm(formRef)\"\r\n                   :disabled=\"isShow\">提交</el-button>\r\n        <el-button @click=\"resetForm\">取消</el-button>\r\n      </div>\r\n    </el-form>\r\n  </div>\r\n</template>\r\n<script>\r\nexport default { name: 'SendTextMessageAdd' }\r\n</script>\r\n<script setup>\r\nimport api from '@/api'\r\nimport { ElMessage } from 'element-plus'\r\nimport { reactive, ref, onMounted, computed } from 'vue'\r\nimport { selectUser } from 'common/js/system_var.js'\r\nconst tabCode = computed(() => selectUser.value.textMessage)\r\nconst emit = defineEmits(['callback'])\r\nconst props = defineProps({\r\n  id: { type: String, default: '' }\r\n})\r\n\r\nconst formRef = ref()\r\nconst isShow = ref(true)\r\nconst form = reactive({\r\n  type: 'relationBookSystemTemp', // 接收对象类型\r\n  memberUserIds: []\r\n})\r\nconst rules = reactive({\r\n  type: [{ required: true, message: '请选择接收对象类型', trigger: ['blur', 'change'] }],\r\n  memberUserIds: [{ required: true, message: '请选择系统内用户', trigger: ['blur', 'change'] }],\r\n  phone: [{ required: false, message: '请输入号码', trigger: ['blur', 'change'] }]\r\n})\r\nconst userCallback = (data) => {\r\n  formRef.value?.validateField('memberUserIds')\r\n}\r\n\r\nonMounted(() => {\r\n  textMessageLimit()\r\n})\r\n\r\nconst textMessageLimit = async () => {\r\n  const { data } = await api.textMessageLimit()\r\n  isShow.value = data.remaining === 0\r\n}\r\n\r\nconst submitForm = async (formEl) => {\r\n  if (!formEl) return\r\n  await formEl.validate((valid, fields) => {\r\n    if (valid) { globalJson() } else { ElMessage({ type: 'warning', message: '请根据提示信息完善字段内容！' }) }\r\n  })\r\n}\r\nconst globalJson = async () => {\r\n  const { code } = await api.globalJson('/delayTextMessage/add', {\r\n    batchId: props.id,\r\n    userIds: form.type === '1' ? form.memberUserIds : [],\r\n    relationBookMemberIds: ['relationBookSystemTemp', 'relationBookMineTemp'].includes(form.type) ? form.memberUserIds : []\r\n  })\r\n  if (code === 200) {\r\n    ElMessage({ type: 'success', message: '新增人员成功' })\r\n    emit('callback')\r\n  }\r\n}\r\nconst typeChange = () => {\r\n  form.memberUserIds = []\r\n  form.phone = ''\r\n  if (form.type !== '4') {\r\n    rules.memberUserIds = [{ required: true, message: '请选择系统内用户', trigger: ['blur', 'change'] }]\r\n    rules.phone = [{ required: false, message: '请输入号码', trigger: ['blur', 'change'] }]\r\n  } else {\r\n    rules.memberUserIds = [{ required: false, message: '请选择系统内用户', trigger: ['blur', 'change'] }]\r\n    rules.phone = [{ required: true, message: '请输入号码', trigger: ['blur', 'change'] }]\r\n  }\r\n}\r\nconst resetForm = () => { emit('callback') }\r\n</script>\r\n<style lang=\"scss\">\r\n.SendTextMessageAdd {\r\n  width: 680px;\r\n}\r\n</style>\r\n"], "mappings": ";;EACOA,KAAK,EAAC;AAAoB;;EA0BtBA,KAAK,EAAC;AAAkB;;;;;;;;uBA1BjCC,mBAAA,CAiCM,OAjCNC,UAiCM,GAhCJC,YAAA,CA+BUC,kBAAA;IA/BDC,GAAG,EAAC,SAAS;IACZC,KAAK,EAAEC,MAAA,CAAAC,IAAI;IACXC,KAAK,EAAEF,MAAA,CAAAE,KAAK;IACbC,MAAM,EAAN,EAAM;IACN,gBAAc,EAAC,KAAK;IACpBV,KAAK,EAAC;;IAPnBW,OAAA,EAAAC,QAAA,CAQM;MAAA,OASe,CATfT,YAAA,CASeU,uBAAA;QATDC,KAAK,EAAC,QAAQ;QACdC,IAAI,EAAC,MAAM;QACXf,KAAK,EAAC;;QAV1BW,OAAA,EAAAC,QAAA,CAWQ;UAAA,OAKiB,CALjBT,YAAA,CAKiBa,yBAAA;YAhBzBC,UAAA,EAWiCV,MAAA,CAAAC,IAAI,CAACU,IAAI;YAX1C,uBAAAC,MAAA,QAAAA,MAAA,gBAAAC,MAAA;cAAA,OAWiCb,MAAA,CAAAC,IAAI,CAACU,IAAI,GAAAE,MAAA;YAAA;YACjBC,QAAM,EAAEd,MAAA,CAAAe,UAAU;YAClBC,QAAQ,EAAEhB,MAAA,CAAAiB;;YAbnCb,OAAA,EAAAC,QAAA,CAcU;cAAA,OAAyD,CAAzDT,YAAA,CAAyDsB,mBAAA;gBAA/CX,KAAK,EAAC;cAAwB;gBAdlDH,OAAA,EAAAC,QAAA,CAcmD;kBAAA,OAAKO,MAAA,QAAAA,MAAA,OAdxDO,gBAAA,CAcmD,OAAK,E;;gBAdxDC,CAAA;kBAeUxB,YAAA,CAAoCsB,mBAAA;gBAA1BX,KAAK,EAAC;cAAG;gBAf7BH,OAAA,EAAAC,QAAA,CAe8B;kBAAA,OAAKO,MAAA,QAAAA,MAAA,OAfnCO,gBAAA,CAe8B,OAAK,E;;gBAfnCC,CAAA;;;YAAAA,CAAA;;;QAAAA,CAAA;UAmB0BpB,MAAA,CAAAC,IAAI,CAACU,IAAI,Y,cAD7BU,YAAA,CAQef,uBAAA;QA1BrBgB,GAAA;QAkBoBf,KAAK,EAAC,MAAM;QAEZC,IAAI,EAAC,eAAe;QACpBf,KAAK,EAAC;;QArB1BW,OAAA,EAAAC,QAAA,CAsBQ;UAAA,OAGsG,CAHtGT,YAAA,CAGsG2B,iCAAA;YAzB9Gb,UAAA,EAsByCV,MAAA,CAAAC,IAAI,CAACuB,aAAa;YAtB3D,uBAAAZ,MAAA,QAAAA,MAAA,gBAAAC,MAAA;cAAA,OAsByCb,MAAA,CAAAC,IAAI,CAACuB,aAAa,GAAAX,MAAA;YAAA;YAC1BY,UAAQ,EAAEzB,MAAA,CAAA0B,YAAY;YACtBV,QAAQ,EAAEhB,MAAA,CAAAiB,MAAM;YAChBU,OAAO,EAAE3B,MAAA,CAAAC,IAAI,CAACU,IAAI,WAAWX,MAAA,CAAA2B,OAAO,IAAI3B,MAAA,CAAAC,IAAI,CAACU,IAAI;;;QAzBlFS,CAAA;YAAAQ,mBAAA,gBA2BMC,mBAAA,CAKM,OALNC,UAKM,GAJJlC,YAAA,CAE4CmC,oBAAA;QAFjCpB,IAAI,EAAC,SAAS;QACbqB,OAAK,EAAApB,MAAA,QAAAA,MAAA,gBAAAC,MAAA;UAAA,OAAEb,MAAA,CAAAiC,UAAU,CAACjC,MAAA,CAAAkC,OAAO;QAAA;QACzBlB,QAAQ,EAAEhB,MAAA,CAAAiB;;QA9B9Bb,OAAA,EAAAC,QAAA,CA8BsC;UAAA,OAAEO,MAAA,QAAAA,MAAA,OA9BxCO,gBAAA,CA8BsC,IAAE,E;;QA9BxCC,CAAA;uCA+BQxB,YAAA,CAA4CmC,oBAAA;QAAhCC,OAAK,EAAEhC,MAAA,CAAAmC;MAAS;QA/BpC/B,OAAA,EAAAC,QAAA,CA+BsC;UAAA,OAAEO,MAAA,QAAAA,MAAA,OA/BxCO,gBAAA,CA+BsC,IAAE,E;;QA/BxCC,CAAA;;;IAAAA,CAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
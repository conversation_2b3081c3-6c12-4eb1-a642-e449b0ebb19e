{"ast": null, "code": "function _regeneratorRuntime() { \"use strict\"; /*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */ _regeneratorRuntime = function _regeneratorRuntime() { return e; }; var t, e = {}, r = Object.prototype, n = r.hasOwnProperty, o = Object.defineProperty || function (t, e, r) { t[e] = r.value; }, i = \"function\" == typeof Symbol ? Symbol : {}, a = i.iterator || \"@@iterator\", c = i.asyncIterator || \"@@asyncIterator\", u = i.toStringTag || \"@@toStringTag\"; function define(t, e, r) { return Object.defineProperty(t, e, { value: r, enumerable: !0, configurable: !0, writable: !0 }), t[e]; } try { define({}, \"\"); } catch (t) { define = function define(t, e, r) { return t[e] = r; }; } function wrap(t, e, r, n) { var i = e && e.prototype instanceof Generator ? e : Generator, a = Object.create(i.prototype), c = new Context(n || []); return o(a, \"_invoke\", { value: makeInvokeMethod(t, r, c) }), a; } function tryCatch(t, e, r) { try { return { type: \"normal\", arg: t.call(e, r) }; } catch (t) { return { type: \"throw\", arg: t }; } } e.wrap = wrap; var h = \"suspendedStart\", l = \"suspendedYield\", f = \"executing\", s = \"completed\", y = {}; function Generator() {} function GeneratorFunction() {} function GeneratorFunctionPrototype() {} var p = {}; define(p, a, function () { return this; }); var d = Object.getPrototypeOf, v = d && d(d(values([]))); v && v !== r && n.call(v, a) && (p = v); var g = GeneratorFunctionPrototype.prototype = Generator.prototype = Object.create(p); function defineIteratorMethods(t) { [\"next\", \"throw\", \"return\"].forEach(function (e) { define(t, e, function (t) { return this._invoke(e, t); }); }); } function AsyncIterator(t, e) { function invoke(r, o, i, a) { var c = tryCatch(t[r], t, o); if (\"throw\" !== c.type) { var u = c.arg, h = u.value; return h && \"object\" == typeof h && n.call(h, \"__await\") ? e.resolve(h.__await).then(function (t) { invoke(\"next\", t, i, a); }, function (t) { invoke(\"throw\", t, i, a); }) : e.resolve(h).then(function (t) { u.value = t, i(u); }, function (t) { return invoke(\"throw\", t, i, a); }); } a(c.arg); } var r; o(this, \"_invoke\", { value: function value(t, n) { function callInvokeWithMethodAndArg() { return new e(function (e, r) { invoke(t, n, e, r); }); } return r = r ? r.then(callInvokeWithMethodAndArg, callInvokeWithMethodAndArg) : callInvokeWithMethodAndArg(); } }); } function makeInvokeMethod(e, r, n) { var o = h; return function (i, a) { if (o === f) throw Error(\"Generator is already running\"); if (o === s) { if (\"throw\" === i) throw a; return { value: t, done: !0 }; } for (n.method = i, n.arg = a;;) { var c = n.delegate; if (c) { var u = maybeInvokeDelegate(c, n); if (u) { if (u === y) continue; return u; } } if (\"next\" === n.method) n.sent = n._sent = n.arg;else if (\"throw\" === n.method) { if (o === h) throw o = s, n.arg; n.dispatchException(n.arg); } else \"return\" === n.method && n.abrupt(\"return\", n.arg); o = f; var p = tryCatch(e, r, n); if (\"normal\" === p.type) { if (o = n.done ? s : l, p.arg === y) continue; return { value: p.arg, done: n.done }; } \"throw\" === p.type && (o = s, n.method = \"throw\", n.arg = p.arg); } }; } function maybeInvokeDelegate(e, r) { var n = r.method, o = e.iterator[n]; if (o === t) return r.delegate = null, \"throw\" === n && e.iterator.return && (r.method = \"return\", r.arg = t, maybeInvokeDelegate(e, r), \"throw\" === r.method) || \"return\" !== n && (r.method = \"throw\", r.arg = new TypeError(\"The iterator does not provide a '\" + n + \"' method\")), y; var i = tryCatch(o, e.iterator, r.arg); if (\"throw\" === i.type) return r.method = \"throw\", r.arg = i.arg, r.delegate = null, y; var a = i.arg; return a ? a.done ? (r[e.resultName] = a.value, r.next = e.nextLoc, \"return\" !== r.method && (r.method = \"next\", r.arg = t), r.delegate = null, y) : a : (r.method = \"throw\", r.arg = new TypeError(\"iterator result is not an object\"), r.delegate = null, y); } function pushTryEntry(t) { var e = { tryLoc: t[0] }; 1 in t && (e.catchLoc = t[1]), 2 in t && (e.finallyLoc = t[2], e.afterLoc = t[3]), this.tryEntries.push(e); } function resetTryEntry(t) { var e = t.completion || {}; e.type = \"normal\", delete e.arg, t.completion = e; } function Context(t) { this.tryEntries = [{ tryLoc: \"root\" }], t.forEach(pushTryEntry, this), this.reset(!0); } function values(e) { if (e || \"\" === e) { var r = e[a]; if (r) return r.call(e); if (\"function\" == typeof e.next) return e; if (!isNaN(e.length)) { var o = -1, i = function next() { for (; ++o < e.length;) if (n.call(e, o)) return next.value = e[o], next.done = !1, next; return next.value = t, next.done = !0, next; }; return i.next = i; } } throw new TypeError(typeof e + \" is not iterable\"); } return GeneratorFunction.prototype = GeneratorFunctionPrototype, o(g, \"constructor\", { value: GeneratorFunctionPrototype, configurable: !0 }), o(GeneratorFunctionPrototype, \"constructor\", { value: GeneratorFunction, configurable: !0 }), GeneratorFunction.displayName = define(GeneratorFunctionPrototype, u, \"GeneratorFunction\"), e.isGeneratorFunction = function (t) { var e = \"function\" == typeof t && t.constructor; return !!e && (e === GeneratorFunction || \"GeneratorFunction\" === (e.displayName || e.name)); }, e.mark = function (t) { return Object.setPrototypeOf ? Object.setPrototypeOf(t, GeneratorFunctionPrototype) : (t.__proto__ = GeneratorFunctionPrototype, define(t, u, \"GeneratorFunction\")), t.prototype = Object.create(g), t; }, e.awrap = function (t) { return { __await: t }; }, defineIteratorMethods(AsyncIterator.prototype), define(AsyncIterator.prototype, c, function () { return this; }), e.AsyncIterator = AsyncIterator, e.async = function (t, r, n, o, i) { void 0 === i && (i = Promise); var a = new AsyncIterator(wrap(t, r, n, o), i); return e.isGeneratorFunction(r) ? a : a.next().then(function (t) { return t.done ? t.value : a.next(); }); }, defineIteratorMethods(g), define(g, u, \"Generator\"), define(g, a, function () { return this; }), define(g, \"toString\", function () { return \"[object Generator]\"; }), e.keys = function (t) { var e = Object(t), r = []; for (var n in e) r.push(n); return r.reverse(), function next() { for (; r.length;) { var t = r.pop(); if (t in e) return next.value = t, next.done = !1, next; } return next.done = !0, next; }; }, e.values = values, Context.prototype = { constructor: Context, reset: function reset(e) { if (this.prev = 0, this.next = 0, this.sent = this._sent = t, this.done = !1, this.delegate = null, this.method = \"next\", this.arg = t, this.tryEntries.forEach(resetTryEntry), !e) for (var r in this) \"t\" === r.charAt(0) && n.call(this, r) && !isNaN(+r.slice(1)) && (this[r] = t); }, stop: function stop() { this.done = !0; var t = this.tryEntries[0].completion; if (\"throw\" === t.type) throw t.arg; return this.rval; }, dispatchException: function dispatchException(e) { if (this.done) throw e; var r = this; function handle(n, o) { return a.type = \"throw\", a.arg = e, r.next = n, o && (r.method = \"next\", r.arg = t), !!o; } for (var o = this.tryEntries.length - 1; o >= 0; --o) { var i = this.tryEntries[o], a = i.completion; if (\"root\" === i.tryLoc) return handle(\"end\"); if (i.tryLoc <= this.prev) { var c = n.call(i, \"catchLoc\"), u = n.call(i, \"finallyLoc\"); if (c && u) { if (this.prev < i.catchLoc) return handle(i.catchLoc, !0); if (this.prev < i.finallyLoc) return handle(i.finallyLoc); } else if (c) { if (this.prev < i.catchLoc) return handle(i.catchLoc, !0); } else { if (!u) throw Error(\"try statement without catch or finally\"); if (this.prev < i.finallyLoc) return handle(i.finallyLoc); } } } }, abrupt: function abrupt(t, e) { for (var r = this.tryEntries.length - 1; r >= 0; --r) { var o = this.tryEntries[r]; if (o.tryLoc <= this.prev && n.call(o, \"finallyLoc\") && this.prev < o.finallyLoc) { var i = o; break; } } i && (\"break\" === t || \"continue\" === t) && i.tryLoc <= e && e <= i.finallyLoc && (i = null); var a = i ? i.completion : {}; return a.type = t, a.arg = e, i ? (this.method = \"next\", this.next = i.finallyLoc, y) : this.complete(a); }, complete: function complete(t, e) { if (\"throw\" === t.type) throw t.arg; return \"break\" === t.type || \"continue\" === t.type ? this.next = t.arg : \"return\" === t.type ? (this.rval = this.arg = t.arg, this.method = \"return\", this.next = \"end\") : \"normal\" === t.type && e && (this.next = e), y; }, finish: function finish(t) { for (var e = this.tryEntries.length - 1; e >= 0; --e) { var r = this.tryEntries[e]; if (r.finallyLoc === t) return this.complete(r.completion, r.afterLoc), resetTryEntry(r), y; } }, catch: function _catch(t) { for (var e = this.tryEntries.length - 1; e >= 0; --e) { var r = this.tryEntries[e]; if (r.tryLoc === t) { var n = r.completion; if (\"throw\" === n.type) { var o = n.arg; resetTryEntry(r); } return o; } } throw Error(\"illegal catch attempt\"); }, delegateYield: function delegateYield(e, r, n) { return this.delegate = { iterator: values(e), resultName: r, nextLoc: n }, \"next\" === this.method && (this.arg = t), y; } }, e; }\nfunction asyncGeneratorStep(n, t, e, r, o, a, c) { try { var i = n[a](c), u = i.value; } catch (n) { return void e(n); } i.done ? t(u) : Promise.resolve(u).then(r, o); }\nfunction _asyncToGenerator(n) { return function () { var t = this, e = arguments; return new Promise(function (r, o) { var a = n.apply(t, e); function _next(n) { asyncGeneratorStep(a, r, o, _next, _throw, \"next\", n); } function _throw(n) { asyncGeneratorStep(a, r, o, _next, _throw, \"throw\", n); } _next(void 0); }); }; }\nimport api from '@/api';\nimport config from '../../config';\nimport { ref, computed, onMounted } from 'vue';\nimport store from '@/store';\nimport { downloadFile } from '../../config/MicroGlobal';\nimport { getFileInfo } from '../../js/file_preview.js';\nimport { ElMessage } from 'element-plus';\nvar __default__ = {\n  name: 'XylFilePreview'\n};\nexport default /*@__PURE__*/Object.assign(__default__, {\n  props: {\n    fileObj: {\n      type: Object,\n      default: function _default() {\n        return {};\n      }\n    },\n    closeCallback: {\n      type: Function\n    }\n  },\n  setup(__props, _ref) {\n    var __expose = _ref.expose;\n    __expose();\n    var props = __props;\n    var svg = `<path class=\"path\" d=\"M 30 15 L 28 17 M 25.61 25.61 A 15 15, 0, 0, 1, 15 30 A 15 15, 0, 1, 1, 27.99 7.5 L 15 15\" style=\"stroke-width: 4px; fill: rgba(0, 0, 0, 0)\"/>`;\n    var fileObj = computed(function () {\n      return props.fileObj;\n    });\n    var elIsShow = ref(false);\n    var loading = ref(true);\n    var fileUrl = ref('');\n    onMounted(function () {\n      file_preview(config.API_URL + '/file/preview/' + fileObj.value.fileId);\n      setTimeout(function () {\n        elIsShow.value = true;\n      }, 168);\n    });\n    var handleClose = function handleClose() {\n      elIsShow.value = false;\n      setTimeout(function () {\n        props.closeCallback();\n      }, 168);\n    };\n    var file_preview = /*#__PURE__*/function () {\n      var _ref2 = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee(url) {\n        var _res$data;\n        var res, data;\n        return _regeneratorRuntime().wrap(function _callee$(_context) {\n          while (1) switch (_context.prev = _context.next) {\n            case 0:\n              _context.next = 2;\n              return api.file_preview({\n                fileUrl: url\n              });\n            case 2:\n              res = _context.sent;\n              data = res === null || res === void 0 || (_res$data = res.data) === null || _res$data === void 0 ? void 0 : _res$data.data;\n              if (data) {\n                file_preview_url(url, data);\n              } else {\n                handleClose();\n                ElMessage({\n                  type: 'error',\n                  message: '打开失败，请重试'\n                });\n              }\n            case 5:\n            case \"end\":\n              return _context.stop();\n          }\n        }, _callee);\n      }));\n      return function file_preview(_x) {\n        return _ref2.apply(this, arguments);\n      };\n    }();\n    var file_preview_url = /*#__PURE__*/function () {\n      var _ref3 = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee2(url, path) {\n        var _res$data2;\n        var res, viewUrl;\n        return _regeneratorRuntime().wrap(function _callee2$(_context2) {\n          while (1) switch (_context2.prev = _context2.next) {\n            case 0:\n              _context2.next = 2;\n              return api.file_preview_url({\n                srcRelativePath: path,\n                convertType: getFileInfo(path.substring(path.lastIndexOf('.'))) || '0',\n                isDccAsync: 1,\n                isCopy: 0,\n                noCache: 0,\n                fileUrl: url,\n                showFooter: 0,\n                isHeaderBar: 0,\n                htmlTitle: '详情',\n                acceptTracks: 0\n              });\n            case 2:\n              res = _context2.sent;\n              viewUrl = res === null || res === void 0 || (_res$data2 = res.data) === null || _res$data2 === void 0 ? void 0 : _res$data2.viewUrl;\n              if (viewUrl) {\n                fileUrl.value = viewUrl;\n                loading.value = false;\n              } else {\n                handleClose();\n                ElMessage({\n                  type: 'error',\n                  message: '打开失败，请重试'\n                });\n              }\n            case 5:\n            case \"end\":\n              return _context2.stop();\n          }\n        }, _callee2);\n      }));\n      return function file_preview_url(_x2, _x3) {\n        return _ref3.apply(this, arguments);\n      };\n    }();\n    var handleDownload = function handleDownload() {\n      if (window.__POWERED_BY_QIANKUN__) {\n        downloadFile({\n          fileId: fileObj.value.fileId,\n          fileType: fileObj.value.fileType,\n          fileName: fileObj.value.fileName,\n          fileSize: fileObj.value.fileSize\n        });\n      } else {\n        console.log(window.__PUBLIC__);\n        if (window.__PUBLIC__) {\n          api.globalFileDownload(fileObj.value.fileId, fileObj.value.fileName);\n        } else {\n          store.commit('setDownloadFile', {\n            fileId: fileObj.value.fileId,\n            fileType: fileObj.value.fileType,\n            fileName: fileObj.value.fileName,\n            fileSize: fileObj.value.fileSize\n          });\n        }\n      }\n    };\n    var __returned__ = {\n      props,\n      svg,\n      fileObj,\n      elIsShow,\n      loading,\n      fileUrl,\n      handleClose,\n      file_preview,\n      file_preview_url,\n      handleDownload,\n      get api() {\n        return api;\n      },\n      get config() {\n        return config;\n      },\n      ref,\n      computed,\n      onMounted,\n      get store() {\n        return store;\n      },\n      get downloadFile() {\n        return downloadFile;\n      },\n      get getFileInfo() {\n        return getFileInfo;\n      },\n      get ElMessage() {\n        return ElMessage;\n      }\n    };\n    Object.defineProperty(__returned__, '__isScriptSetup', {\n      enumerable: false,\n      value: true\n    });\n    return __returned__;\n  }\n});", "map": {"version": 3, "names": ["_regeneratorRuntime", "e", "t", "r", "Object", "prototype", "n", "hasOwnProperty", "o", "defineProperty", "value", "i", "Symbol", "a", "iterator", "c", "asyncIterator", "u", "toStringTag", "define", "enumerable", "configurable", "writable", "wrap", "Generator", "create", "Context", "makeInvokeMethod", "tryCatch", "type", "arg", "call", "h", "l", "f", "s", "y", "GeneratorFunction", "GeneratorFunctionPrototype", "p", "d", "getPrototypeOf", "v", "values", "g", "defineIteratorMethods", "for<PERSON>ach", "_invoke", "AsyncIterator", "invoke", "resolve", "__await", "then", "callInvokeWithMethodAndArg", "Error", "done", "method", "delegate", "maybeInvokeDelegate", "sent", "_sent", "dispatchException", "abrupt", "return", "TypeError", "resultName", "next", "nextLoc", "pushTryEntry", "tryLoc", "catchLoc", "finallyLoc", "afterLoc", "tryEntries", "push", "resetTryEntry", "completion", "reset", "isNaN", "length", "displayName", "isGeneratorFunction", "constructor", "name", "mark", "setPrototypeOf", "__proto__", "awrap", "async", "Promise", "keys", "reverse", "pop", "prev", "char<PERSON>t", "slice", "stop", "rval", "handle", "complete", "finish", "catch", "_catch", "<PERSON><PERSON><PERSON>", "asyncGeneratorStep", "_asyncToGenerator", "arguments", "apply", "_next", "_throw", "api", "config", "ref", "computed", "onMounted", "store", "downloadFile", "getFileInfo", "ElMessage", "__default__", "props", "__props", "svg", "fileObj", "elIsShow", "loading", "fileUrl", "file_preview", "API_URL", "fileId", "setTimeout", "handleClose", "closeCallback", "_ref2", "_callee", "url", "_res$data", "res", "data", "_callee$", "_context", "file_preview_url", "message", "_x", "_ref3", "_callee2", "path", "_res$data2", "viewUrl", "_callee2$", "_context2", "srcRelativePath", "convertType", "substring", "lastIndexOf", "isDccAsync", "isCopy", "noCache", "showFooter", "isHeaderBar", "htmlTitle", "acceptTracks", "_x2", "_x3", "handleDownload", "window", "__POWERED_BY_QIANKUN__", "fileType", "fileName", "fileSize", "console", "log", "__PUBLIC__", "globalFileDownload", "commit"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/common/components/xyl-file-preview/xyl-file-preview.vue"], "sourcesContent": ["<template>\r\n  <div class=\"xyl-file-preview-wrapper\" @click=\"handleClose\">\r\n    <transition name=\"el-zoom-in-bottom\">\r\n      <div class=\"xyl-file-preview\" v-show=\"elIsShow\" @click.stop>\r\n        <div class=\"xyl-file-preview-head\">\r\n          <div class=\"xyl-file-preview-name ellipsis\" :title=\"fileObj.fileName\">{{ fileObj.fileName }}</div>\r\n          <div class=\"xyl-file-preview-button\">\r\n            <div class=\"xyl-file-preview-icon\" @click=\"handleDownload\">\r\n              <svg\r\n                t=\"1717642874928\"\r\n                viewBox=\"0 0 1024 1024\"\r\n                version=\"1.1\"\r\n                xmlns=\"http://www.w3.org/2000/svg\"\r\n                p-id=\"10967\"\r\n                width=\"32\"\r\n                height=\"32\">\r\n                <path\r\n                  d=\"M502 662.3c3.1 3 7.1 4.4 11.1 4.4s8-1.5 11.1-4.4l211.2-201.6c2.6-2.5 4.1-5.5 4.7-8.6 1.8-9.2-5-19.2-15.8-19.2H595.5V165.8c0-8.8-7.1-16-16-16H446.6c-8.8 0-16 7.1-16 16V433H301.8c-10.9 0-17.6 10-15.8 19.2 0.6 3.1 2.1 6 4.7 8.6L502 662.3z\"\r\n                  fill=\"#000000\"\r\n                  p-id=\"10968\"></path>\r\n                <path\r\n                  d=\"M859.6 653H626.7l-58.3 55.6c-15 14.3-34.6 22.2-55.3 22.2-20.7 0-40.4-7.9-55.3-22.2L399.5 653H166.6c-8.9 0-16.1 7.2-16.1 16v190.1c0 8.9 7.2 16.1 16.1 16.1h693c8.9 0 16-7.2 16-16.1v-190c0-8.9-7.2-16.1-16-16.1zM674.8 834.6c-19.5 0-35.2-15.8-35.2-35.2 0-19.5 15.8-35.2 35.2-35.2 19.5 0 35.2 15.8 35.2 35.2 0 19.4-15.8 35.2-35.2 35.2z m122.8 0c-19.5 0-35.2-15.8-35.2-35.2 0-19.5 15.8-35.2 35.2-35.2 19.5 0 35.2 15.8 35.2 35.2 0.1 19.4-15.7 35.2-35.2 35.2z\"\r\n                  fill=\"#000000\"\r\n                  p-id=\"10969\"></path>\r\n              </svg>\r\n            </div>\r\n            <div class=\"xyl-file-preview-icon\" @click=\"handleClose\">\r\n              <svg\r\n                t=\"1717642327842\"\r\n                viewBox=\"0 0 1024 1024\"\r\n                version=\"1.1\"\r\n                xmlns=\"http://www.w3.org/2000/svg\"\r\n                p-id=\"5911\"\r\n                width=\"28\"\r\n                height=\"28\">\r\n                <path\r\n                  d=\"M512.922 63.583c248.335 0 449.712 201.384 449.712 449.71 0 248.333-201.377 449.702-449.712 449.702-248.333 0-449.71-201.369-449.71-449.702 0-248.326 201.377-449.71 449.71-449.71z m148.683 213.634l-35.351 61.247a206.8 206.8 0 0 1 34.37 27.739c37.359 37.351 60.475 88.96 60.475 145.955 0 57.004-23.117 108.607-60.475 145.965-37.344 37.352-88.945 60.469-145.949 60.469-56.987 0-108.606-23.117-145.965-60.469-37.359-37.359-60.461-88.962-60.461-145.965 0-56.995 23.117-108.605 60.461-145.955a209.04 209.04 0 0 1 27.762-23.286l-35.43-61.359a278.69 278.69 0 0 0-42.279 34.69c-50.156 50.148-81.18 119.417-81.18 195.91 0 76.504 31.041 145.773 81.18 195.929 50.139 50.139 119.408 81.166 195.912 81.166 76.502 0 145.771-31.026 195.91-81.166 50.156-50.156 81.182-119.425 81.182-195.929 0-76.494-31.026-145.763-81.182-195.91a277.704 277.704 0 0 0-48.98-39.031zM473.618 128.865v337.849h75.458V128.865h-75.458z\"\r\n                  fill=\"#000000\"\r\n                  p-id=\"5912\"></path>\r\n              </svg>\r\n            </div>\r\n          </div>\r\n        </div>\r\n        <div\r\n          class=\"xyl-file-preview-body\"\r\n          v-loading=\"loading\"\r\n          :element-loading-spinner=\"svg\"\r\n          element-loading-svg-view-box=\"-10, -10, 50, 50\"\r\n          element-loading-text=\"文件加载中...\">\r\n          <iframe class=\"xyl-file-preview-iframe\" frameborder=\"0\" :src=\"fileUrl\"></iframe>\r\n        </div>\r\n      </div>\r\n    </transition>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nexport default { name: 'XylFilePreview' }\r\n</script>\r\n<script setup>\r\nimport api from '@/api'\r\nimport config from '../../config'\r\nimport { ref, computed, onMounted } from 'vue'\r\nimport store from '@/store'\r\nimport { downloadFile } from '../../config/MicroGlobal'\r\nimport { getFileInfo } from '../../js/file_preview.js'\r\nimport { ElMessage } from 'element-plus'\r\nconst props = defineProps({\r\n  fileObj: { type: Object, default: () => ({}) },\r\n  closeCallback: { type: Function }\r\n})\r\nconst svg = `<path class=\"path\" d=\"M 30 15 L 28 17 M 25.61 25.61 A 15 15, 0, 0, 1, 15 30 A 15 15, 0, 1, 1, 27.99 7.5 L 15 15\" style=\"stroke-width: 4px; fill: rgba(0, 0, 0, 0)\"/>`\r\nconst fileObj = computed(() => props.fileObj)\r\nconst elIsShow = ref(false)\r\nconst loading = ref(true)\r\nconst fileUrl = ref('')\r\nonMounted(() => {\r\n  file_preview(config.API_URL + '/file/preview/' + fileObj.value.fileId)\r\n  setTimeout(() => {\r\n    elIsShow.value = true\r\n  }, 168)\r\n})\r\nconst handleClose = () => {\r\n  elIsShow.value = false\r\n  setTimeout(() => {\r\n    props.closeCallback()\r\n  }, 168)\r\n}\r\nconst file_preview = async (url) => {\r\n  const res = await api.file_preview({ fileUrl: url })\r\n  const data = res?.data?.data\r\n  if (data) {\r\n    file_preview_url(url, data)\r\n  } else {\r\n    handleClose()\r\n    ElMessage({ type: 'error', message: '打开失败，请重试' })\r\n  }\r\n}\r\nconst file_preview_url = async (url, path) => {\r\n  const res = await api.file_preview_url({\r\n    srcRelativePath: path,\r\n    convertType: getFileInfo(path.substring(path.lastIndexOf('.'))) || '0',\r\n    isDccAsync: 1,\r\n    isCopy: 0,\r\n    noCache: 0,\r\n    fileUrl: url,\r\n    showFooter: 0,\r\n    isHeaderBar: 0,\r\n    htmlTitle: '详情',\r\n    acceptTracks: 0\r\n  })\r\n  const viewUrl = res?.data?.viewUrl\r\n  if (viewUrl) {\r\n    fileUrl.value = viewUrl\r\n    loading.value = false\r\n  } else {\r\n    handleClose()\r\n    ElMessage({ type: 'error', message: '打开失败，请重试' })\r\n  }\r\n}\r\nconst handleDownload = () => {\r\n  if (window.__POWERED_BY_QIANKUN__) {\r\n    downloadFile({\r\n      fileId: fileObj.value.fileId,\r\n      fileType: fileObj.value.fileType,\r\n      fileName: fileObj.value.fileName,\r\n      fileSize: fileObj.value.fileSize\r\n    })\r\n  } else {\r\n    console.log(window.__PUBLIC__)\r\n    if (window.__PUBLIC__) {\r\n      api.globalFileDownload(fileObj.value.fileId, fileObj.value.fileName)\r\n    } else {\r\n      store.commit('setDownloadFile', {\r\n        fileId: fileObj.value.fileId,\r\n        fileType: fileObj.value.fileType,\r\n        fileName: fileObj.value.fileName,\r\n        fileSize: fileObj.value.fileSize\r\n      })\r\n    }\r\n  }\r\n}\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n.xyl-file-preview-wrapper {\r\n  position: fixed;\r\n  top: 0;\r\n  bottom: 0;\r\n  right: 0;\r\n  left: 0;\r\n  background: rgba(0, 0, 0, 0.5);\r\n  display: flex;\r\n  align-items: flex-end;\r\n  justify-content: center;\r\n  z-index: 999;\r\n\r\n  .xyl-file-preview {\r\n    width: 100%;\r\n    height: 90%;\r\n    background: #fff;\r\n    border-radius: 10px 10px 0px 0px;\r\n\r\n    .xyl-file-preview-head {\r\n      width: 100%;\r\n      height: 52px;\r\n      box-shadow: 0px 2px 4px 1px rgba(0, 0, 0, 0.05);\r\n      padding: 0 32px 0 22px;\r\n      z-index: 999;\r\n      display: flex;\r\n      align-items: center;\r\n      justify-content: space-between;\r\n      border-bottom: 1px solid #eeeeee;\r\n\r\n      .xyl-file-preview-name {\r\n        cursor: pointer;\r\n        font-weight: bold;\r\n        font-size: var(--zy-name-font-size);\r\n      }\r\n\r\n      .xyl-file-preview-button {\r\n        display: flex;\r\n        align-items: center;\r\n\r\n        .xyl-file-preview-icon {\r\n          width: 36px;\r\n          height: 36px;\r\n          display: flex;\r\n          align-items: center;\r\n          justify-content: center;\r\n          cursor: pointer;\r\n          margin-left: 12px;\r\n\r\n          path {\r\n            fill: var(--zy-el-text-color-regular);\r\n          }\r\n        }\r\n      }\r\n    }\r\n\r\n    .xyl-file-preview-body {\r\n      width: 100%;\r\n      height: calc(100% - 52px);\r\n\r\n      .xyl-file-preview-iframe {\r\n        width: 100%;\r\n        height: 100%;\r\n        overflow: auto;\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n"], "mappings": "+CA6DA,qJAAAA,mBAAA,YAAAA,oBAAA,WAAAC,CAAA,SAAAC,CAAA,EAAAD,CAAA,OAAAE,CAAA,GAAAC,MAAA,CAAAC,SAAA,EAAAC,CAAA,GAAAH,CAAA,CAAAI,cAAA,EAAAC,CAAA,GAAAJ,MAAA,CAAAK,cAAA,cAAAP,CAAA,EAAAD,CAAA,EAAAE,CAAA,IAAAD,CAAA,CAAAD,CAAA,IAAAE,CAAA,CAAAO,KAAA,KAAAC,CAAA,wBAAAC,MAAA,GAAAA,MAAA,OAAAC,CAAA,GAAAF,CAAA,CAAAG,QAAA,kBAAAC,CAAA,GAAAJ,CAAA,CAAAK,aAAA,uBAAAC,CAAA,GAAAN,CAAA,CAAAO,WAAA,8BAAAC,OAAAjB,CAAA,EAAAD,CAAA,EAAAE,CAAA,WAAAC,MAAA,CAAAK,cAAA,CAAAP,CAAA,EAAAD,CAAA,IAAAS,KAAA,EAAAP,CAAA,EAAAiB,UAAA,MAAAC,YAAA,MAAAC,QAAA,SAAApB,CAAA,CAAAD,CAAA,WAAAkB,MAAA,mBAAAjB,CAAA,IAAAiB,MAAA,YAAAA,OAAAjB,CAAA,EAAAD,CAAA,EAAAE,CAAA,WAAAD,CAAA,CAAAD,CAAA,IAAAE,CAAA,gBAAAoB,KAAArB,CAAA,EAAAD,CAAA,EAAAE,CAAA,EAAAG,CAAA,QAAAK,CAAA,GAAAV,CAAA,IAAAA,CAAA,CAAAI,SAAA,YAAAmB,SAAA,GAAAvB,CAAA,GAAAuB,SAAA,EAAAX,CAAA,GAAAT,MAAA,CAAAqB,MAAA,CAAAd,CAAA,CAAAN,SAAA,GAAAU,CAAA,OAAAW,OAAA,CAAApB,CAAA,gBAAAE,CAAA,CAAAK,CAAA,eAAAH,KAAA,EAAAiB,gBAAA,CAAAzB,CAAA,EAAAC,CAAA,EAAAY,CAAA,MAAAF,CAAA,aAAAe,SAAA1B,CAAA,EAAAD,CAAA,EAAAE,CAAA,mBAAA0B,IAAA,YAAAC,GAAA,EAAA5B,CAAA,CAAA6B,IAAA,CAAA9B,CAAA,EAAAE,CAAA,cAAAD,CAAA,aAAA2B,IAAA,WAAAC,GAAA,EAAA5B,CAAA,QAAAD,CAAA,CAAAsB,IAAA,GAAAA,IAAA,MAAAS,CAAA,qBAAAC,CAAA,qBAAAC,CAAA,gBAAAC,CAAA,gBAAAC,CAAA,gBAAAZ,UAAA,cAAAa,kBAAA,cAAAC,2BAAA,SAAAC,CAAA,OAAApB,MAAA,CAAAoB,CAAA,EAAA1B,CAAA,qCAAA2B,CAAA,GAAApC,MAAA,CAAAqC,cAAA,EAAAC,CAAA,GAAAF,CAAA,IAAAA,CAAA,CAAAA,CAAA,CAAAG,MAAA,QAAAD,CAAA,IAAAA,CAAA,KAAAvC,CAAA,IAAAG,CAAA,CAAAyB,IAAA,CAAAW,CAAA,EAAA7B,CAAA,MAAA0B,CAAA,GAAAG,CAAA,OAAAE,CAAA,GAAAN,0BAAA,CAAAjC,SAAA,GAAAmB,SAAA,CAAAnB,SAAA,GAAAD,MAAA,CAAAqB,MAAA,CAAAc,CAAA,YAAAM,sBAAA3C,CAAA,gCAAA4C,OAAA,WAAA7C,CAAA,IAAAkB,MAAA,CAAAjB,CAAA,EAAAD,CAAA,YAAAC,CAAA,gBAAA6C,OAAA,CAAA9C,CAAA,EAAAC,CAAA,sBAAA8C,cAAA9C,CAAA,EAAAD,CAAA,aAAAgD,OAAA9C,CAAA,EAAAK,CAAA,EAAAG,CAAA,EAAAE,CAAA,QAAAE,CAAA,GAAAa,QAAA,CAAA1B,CAAA,CAAAC,CAAA,GAAAD,CAAA,EAAAM,CAAA,mBAAAO,CAAA,CAAAc,IAAA,QAAAZ,CAAA,GAAAF,CAAA,CAAAe,GAAA,EAAAE,CAAA,GAAAf,CAAA,CAAAP,KAAA,SAAAsB,CAAA,uBAAAA,CAAA,IAAA1B,CAAA,CAAAyB,IAAA,CAAAC,CAAA,eAAA/B,CAAA,CAAAiD,OAAA,CAAAlB,CAAA,CAAAmB,OAAA,EAAAC,IAAA,WAAAlD,CAAA,IAAA+C,MAAA,SAAA/C,CAAA,EAAAS,CAAA,EAAAE,CAAA,gBAAAX,CAAA,IAAA+C,MAAA,UAAA/C,CAAA,EAAAS,CAAA,EAAAE,CAAA,QAAAZ,CAAA,CAAAiD,OAAA,CAAAlB,CAAA,EAAAoB,IAAA,WAAAlD,CAAA,IAAAe,CAAA,CAAAP,KAAA,GAAAR,CAAA,EAAAS,CAAA,CAAAM,CAAA,gBAAAf,CAAA,WAAA+C,MAAA,UAAA/C,CAAA,EAAAS,CAAA,EAAAE,CAAA,SAAAA,CAAA,CAAAE,CAAA,CAAAe,GAAA,SAAA3B,CAAA,EAAAK,CAAA,oBAAAE,KAAA,WAAAA,MAAAR,CAAA,EAAAI,CAAA,aAAA+C,2BAAA,eAAApD,CAAA,WAAAA,CAAA,EAAAE,CAAA,IAAA8C,MAAA,CAAA/C,CAAA,EAAAI,CAAA,EAAAL,CAAA,EAAAE,CAAA,gBAAAA,CAAA,GAAAA,CAAA,GAAAA,CAAA,CAAAiD,IAAA,CAAAC,0BAAA,EAAAA,0BAAA,IAAAA,0BAAA,qBAAA1B,iBAAA1B,CAAA,EAAAE,CAAA,EAAAG,CAAA,QAAAE,CAAA,GAAAwB,CAAA,mBAAArB,CAAA,EAAAE,CAAA,QAAAL,CAAA,KAAA0B,CAAA,QAAAoB,KAAA,sCAAA9C,CAAA,KAAA2B,CAAA,oBAAAxB,CAAA,QAAAE,CAAA,WAAAH,KAAA,EAAAR,CAAA,EAAAqD,IAAA,eAAAjD,CAAA,CAAAkD,MAAA,GAAA7C,CAAA,EAAAL,CAAA,CAAAwB,GAAA,GAAAjB,CAAA,UAAAE,CAAA,GAAAT,CAAA,CAAAmD,QAAA,MAAA1C,CAAA,QAAAE,CAAA,GAAAyC,mBAAA,CAAA3C,CAAA,EAAAT,CAAA,OAAAW,CAAA,QAAAA,CAAA,KAAAmB,CAAA,mBAAAnB,CAAA,qBAAAX,CAAA,CAAAkD,MAAA,EAAAlD,CAAA,CAAAqD,IAAA,GAAArD,CAAA,CAAAsD,KAAA,GAAAtD,CAAA,CAAAwB,GAAA,sBAAAxB,CAAA,CAAAkD,MAAA,QAAAhD,CAAA,KAAAwB,CAAA,QAAAxB,CAAA,GAAA2B,CAAA,EAAA7B,CAAA,CAAAwB,GAAA,EAAAxB,CAAA,CAAAuD,iBAAA,CAAAvD,CAAA,CAAAwB,GAAA,uBAAAxB,CAAA,CAAAkD,MAAA,IAAAlD,CAAA,CAAAwD,MAAA,WAAAxD,CAAA,CAAAwB,GAAA,GAAAtB,CAAA,GAAA0B,CAAA,MAAAK,CAAA,GAAAX,QAAA,CAAA3B,CAAA,EAAAE,CAAA,EAAAG,CAAA,oBAAAiC,CAAA,CAAAV,IAAA,QAAArB,CAAA,GAAAF,CAAA,CAAAiD,IAAA,GAAApB,CAAA,GAAAF,CAAA,EAAAM,CAAA,CAAAT,GAAA,KAAAM,CAAA,qBAAA1B,KAAA,EAAA6B,CAAA,CAAAT,GAAA,EAAAyB,IAAA,EAAAjD,CAAA,CAAAiD,IAAA,kBAAAhB,CAAA,CAAAV,IAAA,KAAArB,CAAA,GAAA2B,CAAA,EAAA7B,CAAA,CAAAkD,MAAA,YAAAlD,CAAA,CAAAwB,GAAA,GAAAS,CAAA,CAAAT,GAAA,mBAAA4B,oBAAAzD,CAAA,EAAAE,CAAA,QAAAG,CAAA,GAAAH,CAAA,CAAAqD,MAAA,EAAAhD,CAAA,GAAAP,CAAA,CAAAa,QAAA,CAAAR,CAAA,OAAAE,CAAA,KAAAN,CAAA,SAAAC,CAAA,CAAAsD,QAAA,qBAAAnD,CAAA,IAAAL,CAAA,CAAAa,QAAA,CAAAiD,MAAA,KAAA5D,CAAA,CAAAqD,MAAA,aAAArD,CAAA,CAAA2B,GAAA,GAAA5B,CAAA,EAAAwD,mBAAA,CAAAzD,CAAA,EAAAE,CAAA,eAAAA,CAAA,CAAAqD,MAAA,kBAAAlD,CAAA,KAAAH,CAAA,CAAAqD,MAAA,YAAArD,CAAA,CAAA2B,GAAA,OAAAkC,SAAA,uCAAA1D,CAAA,iBAAA8B,CAAA,MAAAzB,CAAA,GAAAiB,QAAA,CAAApB,CAAA,EAAAP,CAAA,CAAAa,QAAA,EAAAX,CAAA,CAAA2B,GAAA,mBAAAnB,CAAA,CAAAkB,IAAA,SAAA1B,CAAA,CAAAqD,MAAA,YAAArD,CAAA,CAAA2B,GAAA,GAAAnB,CAAA,CAAAmB,GAAA,EAAA3B,CAAA,CAAAsD,QAAA,SAAArB,CAAA,MAAAvB,CAAA,GAAAF,CAAA,CAAAmB,GAAA,SAAAjB,CAAA,GAAAA,CAAA,CAAA0C,IAAA,IAAApD,CAAA,CAAAF,CAAA,CAAAgE,UAAA,IAAApD,CAAA,CAAAH,KAAA,EAAAP,CAAA,CAAA+D,IAAA,GAAAjE,CAAA,CAAAkE,OAAA,eAAAhE,CAAA,CAAAqD,MAAA,KAAArD,CAAA,CAAAqD,MAAA,WAAArD,CAAA,CAAA2B,GAAA,GAAA5B,CAAA,GAAAC,CAAA,CAAAsD,QAAA,SAAArB,CAAA,IAAAvB,CAAA,IAAAV,CAAA,CAAAqD,MAAA,YAAArD,CAAA,CAAA2B,GAAA,OAAAkC,SAAA,sCAAA7D,CAAA,CAAAsD,QAAA,SAAArB,CAAA,cAAAgC,aAAAlE,CAAA,QAAAD,CAAA,KAAAoE,MAAA,EAAAnE,CAAA,YAAAA,CAAA,KAAAD,CAAA,CAAAqE,QAAA,GAAApE,CAAA,WAAAA,CAAA,KAAAD,CAAA,CAAAsE,UAAA,GAAArE,CAAA,KAAAD,CAAA,CAAAuE,QAAA,GAAAtE,CAAA,WAAAuE,UAAA,CAAAC,IAAA,CAAAzE,CAAA,cAAA0E,cAAAzE,CAAA,QAAAD,CAAA,GAAAC,CAAA,CAAA0E,UAAA,QAAA3E,CAAA,CAAA4B,IAAA,oBAAA5B,CAAA,CAAA6B,GAAA,EAAA5B,CAAA,CAAA0E,UAAA,GAAA3E,CAAA,aAAAyB,QAAAxB,CAAA,SAAAuE,UAAA,MAAAJ,MAAA,aAAAnE,CAAA,CAAA4C,OAAA,CAAAsB,YAAA,cAAAS,KAAA,iBAAAlC,OAAA1C,CAAA,QAAAA,CAAA,WAAAA,CAAA,QAAAE,CAAA,GAAAF,CAAA,CAAAY,CAAA,OAAAV,CAAA,SAAAA,CAAA,CAAA4B,IAAA,CAAA9B,CAAA,4BAAAA,CAAA,CAAAiE,IAAA,SAAAjE,CAAA,OAAA6E,KAAA,CAAA7E,CAAA,CAAA8E,MAAA,SAAAvE,CAAA,OAAAG,CAAA,YAAAuD,KAAA,aAAA1D,CAAA,GAAAP,CAAA,CAAA8E,MAAA,OAAAzE,CAAA,CAAAyB,IAAA,CAAA9B,CAAA,EAAAO,CAAA,UAAA0D,IAAA,CAAAxD,KAAA,GAAAT,CAAA,CAAAO,CAAA,GAAA0D,IAAA,CAAAX,IAAA,OAAAW,IAAA,SAAAA,IAAA,CAAAxD,KAAA,GAAAR,CAAA,EAAAgE,IAAA,CAAAX,IAAA,OAAAW,IAAA,YAAAvD,CAAA,CAAAuD,IAAA,GAAAvD,CAAA,gBAAAqD,SAAA,QAAA/D,CAAA,iCAAAoC,iBAAA,CAAAhC,SAAA,GAAAiC,0BAAA,EAAA9B,CAAA,CAAAoC,CAAA,mBAAAlC,KAAA,EAAA4B,0BAAA,EAAAjB,YAAA,SAAAb,CAAA,CAAA8B,0BAAA,mBAAA5B,KAAA,EAAA2B,iBAAA,EAAAhB,YAAA,SAAAgB,iBAAA,CAAA2C,WAAA,GAAA7D,MAAA,CAAAmB,0BAAA,EAAArB,CAAA,wBAAAhB,CAAA,CAAAgF,mBAAA,aAAA/E,CAAA,QAAAD,CAAA,wBAAAC,CAAA,IAAAA,CAAA,CAAAgF,WAAA,WAAAjF,CAAA,KAAAA,CAAA,KAAAoC,iBAAA,6BAAApC,CAAA,CAAA+E,WAAA,IAAA/E,CAAA,CAAAkF,IAAA,OAAAlF,CAAA,CAAAmF,IAAA,aAAAlF,CAAA,WAAAE,MAAA,CAAAiF,cAAA,GAAAjF,MAAA,CAAAiF,cAAA,CAAAnF,CAAA,EAAAoC,0BAAA,KAAApC,CAAA,CAAAoF,SAAA,GAAAhD,0BAAA,EAAAnB,MAAA,CAAAjB,CAAA,EAAAe,CAAA,yBAAAf,CAAA,CAAAG,SAAA,GAAAD,MAAA,CAAAqB,MAAA,CAAAmB,CAAA,GAAA1C,CAAA,KAAAD,CAAA,CAAAsF,KAAA,aAAArF,CAAA,aAAAiD,OAAA,EAAAjD,CAAA,OAAA2C,qBAAA,CAAAG,aAAA,CAAA3C,SAAA,GAAAc,MAAA,CAAA6B,aAAA,CAAA3C,SAAA,EAAAU,CAAA,iCAAAd,CAAA,CAAA+C,aAAA,GAAAA,aAAA,EAAA/C,CAAA,CAAAuF,KAAA,aAAAtF,CAAA,EAAAC,CAAA,EAAAG,CAAA,EAAAE,CAAA,EAAAG,CAAA,eAAAA,CAAA,KAAAA,CAAA,GAAA8E,OAAA,OAAA5E,CAAA,OAAAmC,aAAA,CAAAzB,IAAA,CAAArB,CAAA,EAAAC,CAAA,EAAAG,CAAA,EAAAE,CAAA,GAAAG,CAAA,UAAAV,CAAA,CAAAgF,mBAAA,CAAA9E,CAAA,IAAAU,CAAA,GAAAA,CAAA,CAAAqD,IAAA,GAAAd,IAAA,WAAAlD,CAAA,WAAAA,CAAA,CAAAqD,IAAA,GAAArD,CAAA,CAAAQ,KAAA,GAAAG,CAAA,CAAAqD,IAAA,WAAArB,qBAAA,CAAAD,CAAA,GAAAzB,MAAA,CAAAyB,CAAA,EAAA3B,CAAA,gBAAAE,MAAA,CAAAyB,CAAA,EAAA/B,CAAA,iCAAAM,MAAA,CAAAyB,CAAA,6DAAA3C,CAAA,CAAAyF,IAAA,aAAAxF,CAAA,QAAAD,CAAA,GAAAG,MAAA,CAAAF,CAAA,GAAAC,CAAA,gBAAAG,CAAA,IAAAL,CAAA,EAAAE,CAAA,CAAAuE,IAAA,CAAApE,CAAA,UAAAH,CAAA,CAAAwF,OAAA,aAAAzB,KAAA,WAAA/D,CAAA,CAAA4E,MAAA,SAAA7E,CAAA,GAAAC,CAAA,CAAAyF,GAAA,QAAA1F,CAAA,IAAAD,CAAA,SAAAiE,IAAA,CAAAxD,KAAA,GAAAR,CAAA,EAAAgE,IAAA,CAAAX,IAAA,OAAAW,IAAA,WAAAA,IAAA,CAAAX,IAAA,OAAAW,IAAA,QAAAjE,CAAA,CAAA0C,MAAA,GAAAA,MAAA,EAAAjB,OAAA,CAAArB,SAAA,KAAA6E,WAAA,EAAAxD,OAAA,EAAAmD,KAAA,WAAAA,MAAA5E,CAAA,aAAA4F,IAAA,WAAA3B,IAAA,WAAAP,IAAA,QAAAC,KAAA,GAAA1D,CAAA,OAAAqD,IAAA,YAAAE,QAAA,cAAAD,MAAA,gBAAA1B,GAAA,GAAA5B,CAAA,OAAAuE,UAAA,CAAA3B,OAAA,CAAA6B,aAAA,IAAA1E,CAAA,WAAAE,CAAA,kBAAAA,CAAA,CAAA2F,MAAA,OAAAxF,CAAA,CAAAyB,IAAA,OAAA5B,CAAA,MAAA2E,KAAA,EAAA3E,CAAA,CAAA4F,KAAA,cAAA5F,CAAA,IAAAD,CAAA,MAAA8F,IAAA,WAAAA,KAAA,SAAAzC,IAAA,WAAArD,CAAA,QAAAuE,UAAA,IAAAG,UAAA,kBAAA1E,CAAA,CAAA2B,IAAA,QAAA3B,CAAA,CAAA4B,GAAA,cAAAmE,IAAA,KAAApC,iBAAA,WAAAA,kBAAA5D,CAAA,aAAAsD,IAAA,QAAAtD,CAAA,MAAAE,CAAA,kBAAA+F,OAAA5F,CAAA,EAAAE,CAAA,WAAAK,CAAA,CAAAgB,IAAA,YAAAhB,CAAA,CAAAiB,GAAA,GAAA7B,CAAA,EAAAE,CAAA,CAAA+D,IAAA,GAAA5D,CAAA,EAAAE,CAAA,KAAAL,CAAA,CAAAqD,MAAA,WAAArD,CAAA,CAAA2B,GAAA,GAAA5B,CAAA,KAAAM,CAAA,aAAAA,CAAA,QAAAiE,UAAA,CAAAM,MAAA,MAAAvE,CAAA,SAAAA,CAAA,QAAAG,CAAA,QAAA8D,UAAA,CAAAjE,CAAA,GAAAK,CAAA,GAAAF,CAAA,CAAAiE,UAAA,iBAAAjE,CAAA,CAAA0D,MAAA,SAAA6B,MAAA,aAAAvF,CAAA,CAAA0D,MAAA,SAAAwB,IAAA,QAAA9E,CAAA,GAAAT,CAAA,CAAAyB,IAAA,CAAApB,CAAA,eAAAM,CAAA,GAAAX,CAAA,CAAAyB,IAAA,CAAApB,CAAA,qBAAAI,CAAA,IAAAE,CAAA,aAAA4E,IAAA,GAAAlF,CAAA,CAAA2D,QAAA,SAAA4B,MAAA,CAAAvF,CAAA,CAAA2D,QAAA,gBAAAuB,IAAA,GAAAlF,CAAA,CAAA4D,UAAA,SAAA2B,MAAA,CAAAvF,CAAA,CAAA4D,UAAA,cAAAxD,CAAA,aAAA8E,IAAA,GAAAlF,CAAA,CAAA2D,QAAA,SAAA4B,MAAA,CAAAvF,CAAA,CAAA2D,QAAA,qBAAArD,CAAA,QAAAqC,KAAA,qDAAAuC,IAAA,GAAAlF,CAAA,CAAA4D,UAAA,SAAA2B,MAAA,CAAAvF,CAAA,CAAA4D,UAAA,YAAAT,MAAA,WAAAA,OAAA5D,CAAA,EAAAD,CAAA,aAAAE,CAAA,QAAAsE,UAAA,CAAAM,MAAA,MAAA5E,CAAA,SAAAA,CAAA,QAAAK,CAAA,QAAAiE,UAAA,CAAAtE,CAAA,OAAAK,CAAA,CAAA6D,MAAA,SAAAwB,IAAA,IAAAvF,CAAA,CAAAyB,IAAA,CAAAvB,CAAA,wBAAAqF,IAAA,GAAArF,CAAA,CAAA+D,UAAA,QAAA5D,CAAA,GAAAH,CAAA,aAAAG,CAAA,iBAAAT,CAAA,mBAAAA,CAAA,KAAAS,CAAA,CAAA0D,MAAA,IAAApE,CAAA,IAAAA,CAAA,IAAAU,CAAA,CAAA4D,UAAA,KAAA5D,CAAA,cAAAE,CAAA,GAAAF,CAAA,GAAAA,CAAA,CAAAiE,UAAA,cAAA/D,CAAA,CAAAgB,IAAA,GAAA3B,CAAA,EAAAW,CAAA,CAAAiB,GAAA,GAAA7B,CAAA,EAAAU,CAAA,SAAA6C,MAAA,gBAAAU,IAAA,GAAAvD,CAAA,CAAA4D,UAAA,EAAAnC,CAAA,SAAA+D,QAAA,CAAAtF,CAAA,MAAAsF,QAAA,WAAAA,SAAAjG,CAAA,EAAAD,CAAA,oBAAAC,CAAA,CAAA2B,IAAA,QAAA3B,CAAA,CAAA4B,GAAA,qBAAA5B,CAAA,CAAA2B,IAAA,mBAAA3B,CAAA,CAAA2B,IAAA,QAAAqC,IAAA,GAAAhE,CAAA,CAAA4B,GAAA,gBAAA5B,CAAA,CAAA2B,IAAA,SAAAoE,IAAA,QAAAnE,GAAA,GAAA5B,CAAA,CAAA4B,GAAA,OAAA0B,MAAA,kBAAAU,IAAA,yBAAAhE,CAAA,CAAA2B,IAAA,IAAA5B,CAAA,UAAAiE,IAAA,GAAAjE,CAAA,GAAAmC,CAAA,KAAAgE,MAAA,WAAAA,OAAAlG,CAAA,aAAAD,CAAA,QAAAwE,UAAA,CAAAM,MAAA,MAAA9E,CAAA,SAAAA,CAAA,QAAAE,CAAA,QAAAsE,UAAA,CAAAxE,CAAA,OAAAE,CAAA,CAAAoE,UAAA,KAAArE,CAAA,cAAAiG,QAAA,CAAAhG,CAAA,CAAAyE,UAAA,EAAAzE,CAAA,CAAAqE,QAAA,GAAAG,aAAA,CAAAxE,CAAA,GAAAiC,CAAA,OAAAiE,KAAA,WAAAC,OAAApG,CAAA,aAAAD,CAAA,QAAAwE,UAAA,CAAAM,MAAA,MAAA9E,CAAA,SAAAA,CAAA,QAAAE,CAAA,QAAAsE,UAAA,CAAAxE,CAAA,OAAAE,CAAA,CAAAkE,MAAA,KAAAnE,CAAA,QAAAI,CAAA,GAAAH,CAAA,CAAAyE,UAAA,kBAAAtE,CAAA,CAAAuB,IAAA,QAAArB,CAAA,GAAAF,CAAA,CAAAwB,GAAA,EAAA6C,aAAA,CAAAxE,CAAA,YAAAK,CAAA,YAAA8C,KAAA,8BAAAiD,aAAA,WAAAA,cAAAtG,CAAA,EAAAE,CAAA,EAAAG,CAAA,gBAAAmD,QAAA,KAAA3C,QAAA,EAAA6B,MAAA,CAAA1C,CAAA,GAAAgE,UAAA,EAAA9D,CAAA,EAAAgE,OAAA,EAAA7D,CAAA,oBAAAkD,MAAA,UAAA1B,GAAA,GAAA5B,CAAA,GAAAkC,CAAA,OAAAnC,CAAA;AAAA,SAAAuG,mBAAAlG,CAAA,EAAAJ,CAAA,EAAAD,CAAA,EAAAE,CAAA,EAAAK,CAAA,EAAAK,CAAA,EAAAE,CAAA,cAAAJ,CAAA,GAAAL,CAAA,CAAAO,CAAA,EAAAE,CAAA,GAAAE,CAAA,GAAAN,CAAA,CAAAD,KAAA,WAAAJ,CAAA,gBAAAL,CAAA,CAAAK,CAAA,KAAAK,CAAA,CAAA4C,IAAA,GAAArD,CAAA,CAAAe,CAAA,IAAAwE,OAAA,CAAAvC,OAAA,CAAAjC,CAAA,EAAAmC,IAAA,CAAAjD,CAAA,EAAAK,CAAA;AAAA,SAAAiG,kBAAAnG,CAAA,6BAAAJ,CAAA,SAAAD,CAAA,GAAAyG,SAAA,aAAAjB,OAAA,WAAAtF,CAAA,EAAAK,CAAA,QAAAK,CAAA,GAAAP,CAAA,CAAAqG,KAAA,CAAAzG,CAAA,EAAAD,CAAA,YAAA2G,MAAAtG,CAAA,IAAAkG,kBAAA,CAAA3F,CAAA,EAAAV,CAAA,EAAAK,CAAA,EAAAoG,KAAA,EAAAC,MAAA,UAAAvG,CAAA,cAAAuG,OAAAvG,CAAA,IAAAkG,kBAAA,CAAA3F,CAAA,EAAAV,CAAA,EAAAK,CAAA,EAAAoG,KAAA,EAAAC,MAAA,WAAAvG,CAAA,KAAAsG,KAAA;AADA,OAAOE,GAAG,MAAM,OAAO;AACvB,OAAOC,MAAM,MAAM,cAAc;AACjC,SAASC,GAAG,EAAEC,QAAQ,EAAEC,SAAS,QAAQ,KAAK;AAC9C,OAAOC,KAAK,MAAM,SAAS;AAC3B,SAASC,YAAY,QAAQ,0BAA0B;AACvD,SAASC,WAAW,QAAQ,0BAA0B;AACtD,SAASC,SAAS,QAAQ,cAAc;AATxC,IAAAC,WAAA,GAAe;EAAEpC,IAAI,EAAE;AAAiB,CAAC;;;;;;;;;;;;;;;;IAUzC,IAAMqC,KAAK,GAAGC,OAGZ;IACF,IAAMC,GAAG,GAAG,sKAAsK;IAClL,IAAMC,OAAO,GAAGV,QAAQ,CAAC;MAAA,OAAMO,KAAK,CAACG,OAAO;IAAA,EAAC;IAC7C,IAAMC,QAAQ,GAAGZ,GAAG,CAAC,KAAK,CAAC;IAC3B,IAAMa,OAAO,GAAGb,GAAG,CAAC,IAAI,CAAC;IACzB,IAAMc,OAAO,GAAGd,GAAG,CAAC,EAAE,CAAC;IACvBE,SAAS,CAAC,YAAM;MACda,YAAY,CAAChB,MAAM,CAACiB,OAAO,GAAG,gBAAgB,GAAGL,OAAO,CAACjH,KAAK,CAACuH,MAAM,CAAC;MACtEC,UAAU,CAAC,YAAM;QACfN,QAAQ,CAAClH,KAAK,GAAG,IAAI;MACvB,CAAC,EAAE,GAAG,CAAC;IACT,CAAC,CAAC;IACF,IAAMyH,WAAW,GAAG,SAAdA,WAAWA,CAAA,EAAS;MACxBP,QAAQ,CAAClH,KAAK,GAAG,KAAK;MACtBwH,UAAU,CAAC,YAAM;QACfV,KAAK,CAACY,aAAa,CAAC,CAAC;MACvB,CAAC,EAAE,GAAG,CAAC;IACT,CAAC;IACD,IAAML,YAAY;MAAA,IAAAM,KAAA,GAAA5B,iBAAA,cAAAzG,mBAAA,GAAAoF,IAAA,CAAG,SAAAkD,QAAOC,GAAG;QAAA,IAAAC,SAAA;QAAA,IAAAC,GAAA,EAAAC,IAAA;QAAA,OAAA1I,mBAAA,GAAAuB,IAAA,UAAAoH,SAAAC,QAAA;UAAA,kBAAAA,QAAA,CAAA/C,IAAA,GAAA+C,QAAA,CAAA1E,IAAA;YAAA;cAAA0E,QAAA,CAAA1E,IAAA;cAAA,OACX4C,GAAG,CAACiB,YAAY,CAAC;gBAAED,OAAO,EAAES;cAAI,CAAC,CAAC;YAAA;cAA9CE,GAAG,GAAAG,QAAA,CAAAjF,IAAA;cACH+E,IAAI,GAAGD,GAAG,aAAHA,GAAG,gBAAAD,SAAA,GAAHC,GAAG,CAAEC,IAAI,cAAAF,SAAA,uBAATA,SAAA,CAAWE,IAAI;cAC5B,IAAIA,IAAI,EAAE;gBACRG,gBAAgB,CAACN,GAAG,EAAEG,IAAI,CAAC;cAC7B,CAAC,MAAM;gBACLP,WAAW,CAAC,CAAC;gBACbb,SAAS,CAAC;kBAAEzF,IAAI,EAAE,OAAO;kBAAEiH,OAAO,EAAE;gBAAW,CAAC,CAAC;cACnD;YAAC;YAAA;cAAA,OAAAF,QAAA,CAAA5C,IAAA;UAAA;QAAA,GAAAsC,OAAA;MAAA,CACF;MAAA,gBATKP,YAAYA,CAAAgB,EAAA;QAAA,OAAAV,KAAA,CAAA1B,KAAA,OAAAD,SAAA;MAAA;IAAA,GASjB;IACD,IAAMmC,gBAAgB;MAAA,IAAAG,KAAA,GAAAvC,iBAAA,cAAAzG,mBAAA,GAAAoF,IAAA,CAAG,SAAA6D,SAAOV,GAAG,EAAEW,IAAI;QAAA,IAAAC,UAAA;QAAA,IAAAV,GAAA,EAAAW,OAAA;QAAA,OAAApJ,mBAAA,GAAAuB,IAAA,UAAA8H,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAzD,IAAA,GAAAyD,SAAA,CAAApF,IAAA;YAAA;cAAAoF,SAAA,CAAApF,IAAA;cAAA,OACrB4C,GAAG,CAAC+B,gBAAgB,CAAC;gBACrCU,eAAe,EAAEL,IAAI;gBACrBM,WAAW,EAAEnC,WAAW,CAAC6B,IAAI,CAACO,SAAS,CAACP,IAAI,CAACQ,WAAW,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,GAAG;gBACtEC,UAAU,EAAE,CAAC;gBACbC,MAAM,EAAE,CAAC;gBACTC,OAAO,EAAE,CAAC;gBACV/B,OAAO,EAAES,GAAG;gBACZuB,UAAU,EAAE,CAAC;gBACbC,WAAW,EAAE,CAAC;gBACdC,SAAS,EAAE,IAAI;gBACfC,YAAY,EAAE;cAChB,CAAC,CAAC;YAAA;cAXIxB,GAAG,GAAAa,SAAA,CAAA3F,IAAA;cAYHyF,OAAO,GAAGX,GAAG,aAAHA,GAAG,gBAAAU,UAAA,GAAHV,GAAG,CAAEC,IAAI,cAAAS,UAAA,uBAATA,UAAA,CAAWC,OAAO;cAClC,IAAIA,OAAO,EAAE;gBACXtB,OAAO,CAACpH,KAAK,GAAG0I,OAAO;gBACvBvB,OAAO,CAACnH,KAAK,GAAG,KAAK;cACvB,CAAC,MAAM;gBACLyH,WAAW,CAAC,CAAC;gBACbb,SAAS,CAAC;kBAAEzF,IAAI,EAAE,OAAO;kBAAEiH,OAAO,EAAE;gBAAW,CAAC,CAAC;cACnD;YAAC;YAAA;cAAA,OAAAQ,SAAA,CAAAtD,IAAA;UAAA;QAAA,GAAAiD,QAAA;MAAA,CACF;MAAA,gBArBKJ,gBAAgBA,CAAAqB,GAAA,EAAAC,GAAA;QAAA,OAAAnB,KAAA,CAAArC,KAAA,OAAAD,SAAA;MAAA;IAAA,GAqBrB;IACD,IAAM0D,cAAc,GAAG,SAAjBA,cAAcA,CAAA,EAAS;MAC3B,IAAIC,MAAM,CAACC,sBAAsB,EAAE;QACjClD,YAAY,CAAC;UACXa,MAAM,EAAEN,OAAO,CAACjH,KAAK,CAACuH,MAAM;UAC5BsC,QAAQ,EAAE5C,OAAO,CAACjH,KAAK,CAAC6J,QAAQ;UAChCC,QAAQ,EAAE7C,OAAO,CAACjH,KAAK,CAAC8J,QAAQ;UAChCC,QAAQ,EAAE9C,OAAO,CAACjH,KAAK,CAAC+J;QAC1B,CAAC,CAAC;MACJ,CAAC,MAAM;QACLC,OAAO,CAACC,GAAG,CAACN,MAAM,CAACO,UAAU,CAAC;QAC9B,IAAIP,MAAM,CAACO,UAAU,EAAE;UACrB9D,GAAG,CAAC+D,kBAAkB,CAAClD,OAAO,CAACjH,KAAK,CAACuH,MAAM,EAAEN,OAAO,CAACjH,KAAK,CAAC8J,QAAQ,CAAC;QACtE,CAAC,MAAM;UACLrD,KAAK,CAAC2D,MAAM,CAAC,iBAAiB,EAAE;YAC9B7C,MAAM,EAAEN,OAAO,CAACjH,KAAK,CAACuH,MAAM;YAC5BsC,QAAQ,EAAE5C,OAAO,CAACjH,KAAK,CAAC6J,QAAQ;YAChCC,QAAQ,EAAE7C,OAAO,CAACjH,KAAK,CAAC8J,QAAQ;YAChCC,QAAQ,EAAE9C,OAAO,CAACjH,KAAK,CAAC+J;UAC1B,CAAC,CAAC;QACJ;MACF;IACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "import '../../../utils/index.mjs';\nimport { buildProps, definePropType } from '../../../utils/vue/props/runtime.mjs';\nvar tooltipV2RootProps = buildProps({\n  delayDuration: {\n    type: Number,\n    default: 300\n  },\n  defaultOpen: Boolean,\n  open: {\n    type: <PERSON>olean,\n    default: void 0\n  },\n  onOpenChange: {\n    type: definePropType(Function)\n  },\n  \"onUpdate:open\": {\n    type: definePropType(Function)\n  }\n});\nexport { tooltipV2RootProps };", "map": {"version": 3, "names": ["tooltipV2RootProps", "buildProps", "delayDuration", "type", "Number", "default", "defaultOpen", "Boolean", "open", "onOpenChange", "definePropType", "Function"], "sources": ["../../../../../../packages/components/tooltip-v2/src/root.ts"], "sourcesContent": ["import { buildProps, definePropType } from '@element-plus/utils'\n\nimport type { ExtractPropTypes } from 'vue'\n\ntype StateUpdater = (state: boolean) => void\n\nexport const tooltipV2RootProps = buildProps({\n  delayDuration: {\n    type: Number,\n    default: 300,\n  },\n  defaultOpen: Boolean,\n  open: {\n    type: Boolean,\n    default: undefined,\n  },\n  onOpenChange: {\n    type: definePropType<StateUpdater>(Function),\n  },\n  'onUpdate:open': {\n    type: definePropType<StateUpdater>(Function),\n  },\n} as const)\n\nexport type TooltipV2RootProps = ExtractPropTypes<typeof tooltipV2RootProps>\n"], "mappings": ";;AACY,IAACA,kBAAkB,GAAGC,UAAU,CAAC;EAC3CC,aAAa,EAAE;IACbC,IAAI,EAAEC,MAAM;IACZC,OAAO,EAAE;EACb,CAAG;EACDC,WAAW,EAAEC,OAAO;EACpBC,IAAI,EAAE;IACJL,IAAI,EAAEI,OAAO;IACbF,OAAO,EAAE,KAAK;EAClB,CAAG;EACDI,YAAY,EAAE;IACZN,IAAI,EAAEO,cAAc,CAACC,QAAQ;EACjC,CAAG;EACD,eAAe,EAAE;IACfR,IAAI,EAAEO,cAAc,CAACC,QAAQ;EACjC;AACA,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
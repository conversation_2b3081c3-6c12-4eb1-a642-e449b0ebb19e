{"ast": null, "code": "\"use strict\";\n\nvar utils = module.exports = {};\nutils.getOption = getOption;\nfunction getOption(options, name, defaultValue) {\n  var value = options[name];\n  if ((value === undefined || value === null) && defaultValue !== undefined) {\n    return defaultValue;\n  }\n  return value;\n}", "map": {"version": 3, "names": ["utils", "module", "exports", "getOption", "options", "name", "defaultValue", "value", "undefined"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/node_modules/.pnpm/batch-processor@1.0.0/node_modules/batch-processor/src/utils.js"], "sourcesContent": ["\"use strict\";\n\nvar utils = module.exports = {};\n\nutils.getOption = getOption;\n\nfunction getOption(options, name, defaultValue) {\n    var value = options[name];\n\n    if((value === undefined || value === null) && defaultValue !== undefined) {\n        return defaultValue;\n    }\n\n    return value;\n}\n"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,KAAK,GAAGC,MAAM,CAACC,OAAO,GAAG,CAAC,CAAC;AAE/BF,KAAK,CAACG,SAAS,GAAGA,SAAS;AAE3B,SAASA,SAASA,CAACC,OAAO,EAAEC,IAAI,EAAEC,YAAY,EAAE;EAC5C,IAAIC,KAAK,GAAGH,OAAO,CAACC,IAAI,CAAC;EAEzB,IAAG,CAACE,KAAK,KAAKC,SAAS,IAAID,KAAK,KAAK,IAAI,KAAKD,YAAY,KAAKE,SAAS,EAAE;IACtE,OAAOF,YAAY;EACvB;EAEA,OAAOC,KAAK;AAChB", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}
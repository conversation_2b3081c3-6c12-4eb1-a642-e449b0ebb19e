{"ast": null, "code": "import { resolveComponent as _resolveComponent, createVNode as _createVNode, withCtx as _withCtx, renderList as _renderList, Fragment as _Fragment, openBlock as _openBlock, createElementBlock as _createElementBlock, createBlock as _createBlock, createTextVNode as _createTextVNode, createElementVNode as _createElementVNode, createCommentVNode as _createCommentVNode } from \"vue\";\nvar _hoisted_1 = {\n  class: \"CreateVideoMeeting\"\n};\nvar _hoisted_2 = {\n  class: \"informationDetailsImgBox\"\n};\nvar _hoisted_3 = {\n  class: \"globalFormButton\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  var _component_el_input = _resolveComponent(\"el-input\");\n  var _component_el_form_item = _resolveComponent(\"el-form-item\");\n  var _component_xyl_date_picker = _resolveComponent(\"xyl-date-picker\");\n  var _component_el_time_select = _resolveComponent(\"el-time-select\");\n  var _component_el_option = _resolveComponent(\"el-option\");\n  var _component_el_select = _resolveComponent(\"el-select\");\n  var _component_el_radio = _resolveComponent(\"el-radio\");\n  var _component_el_radio_group = _resolveComponent(\"el-radio-group\");\n  var _component_business_select_person = _resolveComponent(\"business-select-person\");\n  var _component_xyl_upload_img = _resolveComponent(\"xyl-upload-img\");\n  var _component_el_button = _resolveComponent(\"el-button\");\n  var _component_el_form = _resolveComponent(\"el-form\");\n  var _component_xyl_popup_window = _resolveComponent(\"xyl-popup-window\");\n  return _openBlock(), _createElementBlock(_Fragment, null, [_createElementVNode(\"div\", _hoisted_1, [_createVNode(_component_el_form, {\n    ref: \"formRef\",\n    model: $setup.form,\n    rules: $setup.rules,\n    inline: \"\",\n    \"label-position\": \"top\",\n    class: \"globalForm\"\n  }, {\n    default: _withCtx(function () {\n      return [_createVNode(_component_el_form_item, {\n        label: \"会议主题\",\n        prop: \"theme\",\n        class: \"globalFormTitle\"\n      }, {\n        default: _withCtx(function () {\n          return [_createVNode(_component_el_input, {\n            modelValue: $setup.form.theme,\n            \"onUpdate:modelValue\": _cache[0] || (_cache[0] = function ($event) {\n              return $setup.form.theme = $event;\n            }),\n            placeholder: \"请输入会议主题\",\n            clearable: \"\"\n          }, null, 8 /* PROPS */, [\"modelValue\"])];\n        }),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_form_item, {\n        label: \"会议开始时间\",\n        prop: \"startTime\",\n        class: \"InitVideoMeetingTime\"\n      }, {\n        default: _withCtx(function () {\n          return [_createVNode(_component_xyl_date_picker, {\n            modelValue: $setup.form.startDate,\n            \"onUpdate:modelValue\": _cache[1] || (_cache[1] = function ($event) {\n              return $setup.form.startDate = $event;\n            }),\n            type: \"date\",\n            \"disabled-date\": $setup.disabledDate,\n            placeholder: \"请选择\",\n            \"value-format\": \"YYYY-MM-DD\",\n            format: \"YYYY-MM-DD\",\n            teleported: false,\n            clearable: false\n          }, null, 8 /* PROPS */, [\"modelValue\"]), _createVNode(_component_el_time_select, {\n            modelValue: $setup.form.startTime,\n            \"onUpdate:modelValue\": _cache[2] || (_cache[2] = function ($event) {\n              return $setup.form.startTime = $event;\n            }),\n            start: \"00:00\",\n            step: \"00:15\",\n            end: \"23:45\",\n            placeholder: \"请选择\",\n            teleported: false,\n            clearable: false\n          }, null, 8 /* PROPS */, [\"modelValue\"])];\n        }),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_form_item, {\n        label: \"会议时长\",\n        prop: \"duration\"\n      }, {\n        default: _withCtx(function () {\n          return [_createVNode(_component_el_select, {\n            modelValue: $setup.form.duration,\n            \"onUpdate:modelValue\": _cache[3] || (_cache[3] = function ($event) {\n              return $setup.form.duration = $event;\n            }),\n            placeholder: \"请选择会议时长\",\n            teleported: false,\n            clearable: \"\"\n          }, {\n            default: _withCtx(function () {\n              return [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.VideoMeetingTime, function (item) {\n                return _openBlock(), _createBlock(_component_el_option, {\n                  key: item.key,\n                  label: item.name,\n                  value: item.key\n                }, null, 8 /* PROPS */, [\"label\", \"value\"]);\n              }), 128 /* KEYED_FRAGMENT */))];\n            }),\n            _: 1 /* STABLE */\n          }, 8 /* PROPS */, [\"modelValue\"])];\n        }),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_form_item, {\n        label: \"是否开启直播\"\n      }, {\n        default: _withCtx(function () {\n          return [_createVNode(_component_el_radio_group, {\n            modelValue: $setup.form.isLive,\n            \"onUpdate:modelValue\": _cache[4] || (_cache[4] = function ($event) {\n              return $setup.form.isLive = $event;\n            })\n          }, {\n            default: _withCtx(function () {\n              return [_createVNode(_component_el_radio, {\n                label: 1\n              }, {\n                default: _withCtx(function () {\n                  return _cache[15] || (_cache[15] = [_createTextVNode(\"是\")]);\n                }),\n                _: 1 /* STABLE */\n              }), _createVNode(_component_el_radio, {\n                label: 0\n              }, {\n                default: _withCtx(function () {\n                  return _cache[16] || (_cache[16] = [_createTextVNode(\"否\")]);\n                }),\n                _: 1 /* STABLE */\n              })];\n            }),\n            _: 1 /* STABLE */\n          }, 8 /* PROPS */, [\"modelValue\"])];\n        }),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_form_item, {\n        label: \"参会人员\",\n        prop: \"joinUserIds\",\n        class: \"globalFormTitle\"\n      }, {\n        default: _withCtx(function () {\n          return [_createVNode(_component_business_select_person, {\n            modelValue: $setup.form.joinUserIds,\n            \"onUpdate:modelValue\": _cache[5] || (_cache[5] = function ($event) {\n              return $setup.form.joinUserIds = $event;\n            }),\n            onCallback: $setup.userCallback\n          }, null, 8 /* PROPS */, [\"modelValue\"])];\n        }),\n        _: 1 /* STABLE */\n      }), $setup.form.isLive === 1 ? (_openBlock(), _createElementBlock(_Fragment, {\n        key: 0\n      }, [_cache[24] || (_cache[24] = _createElementVNode(\"div\", {\n        class: \"globalFormName\"\n      }, \"直播相关配置\", -1 /* HOISTED */)), _createVNode(_component_el_form_item, {\n        label: \"直播简介\",\n        prop: \"liveDescribes\",\n        class: \"globalFormTitle\"\n      }, {\n        default: _withCtx(function () {\n          return [_createVNode(_component_el_input, {\n            modelValue: $setup.form.liveDescribes,\n            \"onUpdate:modelValue\": _cache[6] || (_cache[6] = function ($event) {\n              return $setup.form.liveDescribes = $event;\n            }),\n            type: \"textarea\",\n            placeholder: \"请输入直播简介\",\n            clearable: \"\",\n            rows: \"4\",\n            disabled: $setup.props.id != ''\n          }, null, 8 /* PROPS */, [\"modelValue\", \"disabled\"])];\n        }),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_form_item, {\n        label: \"直播推流地址\",\n        class: \"globalFormTitle\"\n      }, {\n        default: _withCtx(function () {\n          return [_createVNode(_component_el_input, {\n            modelValue: $setup.form.liveUrl,\n            \"onUpdate:modelValue\": _cache[7] || (_cache[7] = function ($event) {\n              return $setup.form.liveUrl = $event;\n            }),\n            placeholder: \"请输入直播推流地址\",\n            clearable: \"\",\n            disabled: $setup.props.id != ''\n          }, null, 8 /* PROPS */, [\"modelValue\", \"disabled\"])];\n        }),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_form_item, {\n        label: \"直播回放地址\",\n        class: \"globalFormTitle\"\n      }, {\n        default: _withCtx(function () {\n          return [_createVNode(_component_el_input, {\n            modelValue: $setup.form.liveReplayUrl,\n            \"onUpdate:modelValue\": _cache[8] || (_cache[8] = function ($event) {\n              return $setup.form.liveReplayUrl = $event;\n            }),\n            placeholder: \"请输入直播回放地址\",\n            clearable: \"\",\n            disabled: $setup.props.id != ''\n          }, null, 8 /* PROPS */, [\"modelValue\", \"disabled\"])];\n        }),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_form_item, {\n        label: \"是否开启直播回放\"\n      }, {\n        default: _withCtx(function () {\n          return [_createVNode(_component_el_radio_group, {\n            modelValue: $setup.form.isReplay,\n            \"onUpdate:modelValue\": _cache[9] || (_cache[9] = function ($event) {\n              return $setup.form.isReplay = $event;\n            }),\n            disabled: $setup.props.id != ''\n          }, {\n            default: _withCtx(function () {\n              return [_createVNode(_component_el_radio, {\n                label: 1\n              }, {\n                default: _withCtx(function () {\n                  return _cache[17] || (_cache[17] = [_createTextVNode(\"是\")]);\n                }),\n                _: 1 /* STABLE */\n              }), _createVNode(_component_el_radio, {\n                label: 0\n              }, {\n                default: _withCtx(function () {\n                  return _cache[18] || (_cache[18] = [_createTextVNode(\"否\")]);\n                }),\n                _: 1 /* STABLE */\n              })];\n            }),\n            _: 1 /* STABLE */\n          }, 8 /* PROPS */, [\"modelValue\", \"disabled\"])];\n        }),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_form_item, {\n        label: \"是否推送群众端查看\"\n      }, {\n        default: _withCtx(function () {\n          return [_createVNode(_component_el_radio_group, {\n            modelValue: $setup.form.isPublic,\n            \"onUpdate:modelValue\": _cache[10] || (_cache[10] = function ($event) {\n              return $setup.form.isPublic = $event;\n            }),\n            disabled: $setup.props.id != ''\n          }, {\n            default: _withCtx(function () {\n              return [_createVNode(_component_el_radio, {\n                label: 1\n              }, {\n                default: _withCtx(function () {\n                  return _cache[19] || (_cache[19] = [_createTextVNode(\"是\")]);\n                }),\n                _: 1 /* STABLE */\n              }), _createVNode(_component_el_radio, {\n                label: 0\n              }, {\n                default: _withCtx(function () {\n                  return _cache[20] || (_cache[20] = [_createTextVNode(\"否\")]);\n                }),\n                _: 1 /* STABLE */\n              })];\n            }),\n            _: 1 /* STABLE */\n          }, 8 /* PROPS */, [\"modelValue\", \"disabled\"])];\n        }),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_form_item, {\n        label: \"是否开启直播互动\"\n      }, {\n        default: _withCtx(function () {\n          return [_createVNode(_component_el_radio_group, {\n            modelValue: $setup.form.isInteraction,\n            \"onUpdate:modelValue\": _cache[11] || (_cache[11] = function ($event) {\n              return $setup.form.isInteraction = $event;\n            }),\n            disabled: $setup.props.id != ''\n          }, {\n            default: _withCtx(function () {\n              return [_createVNode(_component_el_radio, {\n                label: 1\n              }, {\n                default: _withCtx(function () {\n                  return _cache[21] || (_cache[21] = [_createTextVNode(\"是\")]);\n                }),\n                _: 1 /* STABLE */\n              }), _createVNode(_component_el_radio, {\n                label: 0\n              }, {\n                default: _withCtx(function () {\n                  return _cache[22] || (_cache[22] = [_createTextVNode(\"否\")]);\n                }),\n                _: 1 /* STABLE */\n              })];\n            }),\n            _: 1 /* STABLE */\n          }, 8 /* PROPS */, [\"modelValue\", \"disabled\"])];\n        }),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_form_item, {\n        prop: \"coverImg\"\n      }, {\n        label: _withCtx(function () {\n          return _cache[23] || (_cache[23] = [_createTextVNode(\" 上传封面 \"), _createElementVNode(\"span\", {\n            class: \"label-gary-tips\"\n          }, \" (建议宽高尺寸比为16:9) \", -1 /* HOISTED */)]);\n        }),\n        default: _withCtx(function () {\n          return [_createElementVNode(\"div\", null, [_createElementVNode(\"div\", _hoisted_2, [_createVNode(_component_xyl_upload_img, {\n            max: 1,\n            ref: \"imgFileRef\",\n            fileId: $setup.imgParams.imgFileName,\n            fileData: $setup.imgParams.imgFileData,\n            beforeUpload: $setup.handleBeforeUpload,\n            onFileUpload: $setup.imgGlobalUpload,\n            height: \"90px\",\n            disabled: $setup.props.id != ''\n          }, null, 8 /* PROPS */, [\"fileId\", \"fileData\", \"disabled\"])])])];\n        }),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_form_item, {\n        label: \"直播设备号\"\n      }, {\n        default: _withCtx(function () {\n          return [_createVNode(_component_el_input, {\n            modelValue: $setup.form.liveNumber,\n            \"onUpdate:modelValue\": _cache[12] || (_cache[12] = function ($event) {\n              return $setup.form.liveNumber = $event;\n            }),\n            placeholder: \"请输入直播设备号\",\n            clearable: \"\",\n            disabled: $setup.props.id != ''\n          }, null, 8 /* PROPS */, [\"modelValue\", \"disabled\"])];\n        }),\n        _: 1 /* STABLE */\n      })], 64 /* STABLE_FRAGMENT */)) : _createCommentVNode(\"v-if\", true), _createElementVNode(\"div\", _hoisted_3, [_createVNode(_component_el_button, {\n        type: \"primary\",\n        onClick: _cache[13] || (_cache[13] = function ($event) {\n          return $setup.submitForm($setup.formRef);\n        })\n      }, {\n        default: _withCtx(function () {\n          return _cache[25] || (_cache[25] = [_createTextVNode(\"提交\")]);\n        }),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_button, {\n        onClick: $setup.resetForm\n      }, {\n        default: _withCtx(function () {\n          return _cache[26] || (_cache[26] = [_createTextVNode(\"取消\")]);\n        }),\n        _: 1 /* STABLE */\n      })])];\n    }),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"model\", \"rules\"])]), _createVNode(_component_xyl_popup_window, {\n    modelValue: $setup.cropperShow,\n    \"onUpdate:modelValue\": _cache[14] || (_cache[14] = function ($event) {\n      return $setup.cropperShow = $event;\n    }),\n    name: \"封面\"\n  }, {\n    default: _withCtx(function () {\n      return [_createVNode($setup[\"GlobalCropper\"], {\n        enlarge: 3,\n        width: 375,\n        height: 210,\n        cropperStyle: {\n          width: '520px',\n          height: '360px'\n        },\n        file: $setup.cropperFile,\n        onCallback: $setup.handleCropper\n      }, null, 8 /* PROPS */, [\"file\"])];\n    }),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"modelValue\"])], 64 /* STABLE_FRAGMENT */);\n}", "map": {"version": 3, "names": ["class", "_createElementBlock", "_Fragment", "_createElementVNode", "_hoisted_1", "_createVNode", "_component_el_form", "ref", "model", "$setup", "form", "rules", "inline", "default", "_withCtx", "_component_el_form_item", "label", "prop", "_component_el_input", "modelValue", "theme", "_cache", "$event", "placeholder", "clearable", "_", "_component_xyl_date_picker", "startDate", "type", "disabledDate", "format", "teleported", "_component_el_time_select", "startTime", "start", "step", "end", "_component_el_select", "duration", "_renderList", "VideoMeetingTime", "item", "_createBlock", "_component_el_option", "key", "name", "value", "_component_el_radio_group", "isLive", "_component_el_radio", "_createTextVNode", "_component_business_select_person", "joinUserIds", "onCallback", "userCallback", "liveDescribes", "rows", "disabled", "props", "id", "liveUrl", "liveReplayUrl", "isReplay", "isPublic", "isInteraction", "_hoisted_2", "_component_xyl_upload_img", "max", "fileId", "imgParams", "imgFileName", "fileData", "imgFileData", "beforeUpload", "handleBeforeUpload", "onFileUpload", "imgGlobalUpload", "height", "liveNumber", "_createCommentVNode", "_hoisted_3", "_component_el_button", "onClick", "submitForm", "formRef", "resetForm", "_component_xyl_popup_window", "cropperShow", "enlarge", "width", "cropperStyle", "file", "cropperFile", "handleCropper"], "sources": ["D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\interaction\\src\\views\\VideoMeeting\\component\\CreateVideoMeeting.vue"], "sourcesContent": ["<template>\r\n  <div class=\"CreateVideoMeeting\">\r\n    <el-form ref=\"formRef\" :model=\"form\" :rules=\"rules\" inline label-position=\"top\" class=\"globalForm\">\r\n      <el-form-item label=\"会议主题\" prop=\"theme\" class=\"globalFormTitle\">\r\n        <el-input v-model=\"form.theme\" placeholder=\"请输入会议主题\" clearable />\r\n      </el-form-item>\r\n      <el-form-item label=\"会议开始时间\" prop=\"startTime\" class=\"InitVideoMeetingTime\">\r\n        <xyl-date-picker v-model=\"form.startDate\" type=\"date\" :disabled-date=\"disabledDate\" placeholder=\"请选择\"\r\n          value-format=\"YYYY-MM-DD\" format=\"YYYY-MM-DD\" :teleported=\"false\" :clearable=\"false\" />\r\n        <el-time-select v-model=\"form.startTime\" start=\"00:00\" step=\"00:15\" end=\"23:45\" placeholder=\"请选择\"\r\n          :teleported=\"false\" :clearable=\"false\" />\r\n      </el-form-item>\r\n      <el-form-item label=\"会议时长\" prop=\"duration\">\r\n        <el-select v-model=\"form.duration\" placeholder=\"请选择会议时长\" :teleported=\"false\" clearable>\r\n          <el-option v-for=\"item in VideoMeetingTime\" :key=\"item.key\" :label=\"item.name\" :value=\"item.key\" />\r\n        </el-select>\r\n      </el-form-item>\r\n      <el-form-item label=\"是否开启直播\">\r\n        <el-radio-group v-model=\"form.isLive\">\r\n          <el-radio :label=\"1\">是</el-radio>\r\n          <el-radio :label=\"0\">否</el-radio>\r\n        </el-radio-group>\r\n      </el-form-item>\r\n      <el-form-item label=\"参会人员\" prop=\"joinUserIds\" class=\"globalFormTitle\">\r\n        <business-select-person v-model=\"form.joinUserIds\" @callback=\"userCallback\"></business-select-person>\r\n      </el-form-item>\r\n      <template v-if=\"form.isLive === 1\">\r\n        <div class=\"globalFormName\">直播相关配置</div>\r\n        <el-form-item label=\"直播简介\" prop=\"liveDescribes\" class=\"globalFormTitle\">\r\n          <el-input v-model=\"form.liveDescribes\" type=\"textarea\" placeholder=\"请输入直播简介\" clearable rows=\"4\"\r\n            :disabled=\"props.id != ''\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"直播推流地址\" class=\"globalFormTitle\">\r\n          <el-input v-model=\"form.liveUrl\" placeholder=\"请输入直播推流地址\" clearable :disabled=\"props.id != ''\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"直播回放地址\" class=\"globalFormTitle\">\r\n          <el-input v-model=\"form.liveReplayUrl\" placeholder=\"请输入直播回放地址\" clearable :disabled=\"props.id != ''\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"是否开启直播回放\">\r\n          <el-radio-group v-model=\"form.isReplay\" :disabled=\"props.id != ''\">\r\n            <el-radio :label=\"1\">是</el-radio>\r\n            <el-radio :label=\"0\">否</el-radio>\r\n          </el-radio-group>\r\n        </el-form-item>\r\n        <el-form-item label=\"是否推送群众端查看\">\r\n          <el-radio-group v-model=\"form.isPublic\" :disabled=\"props.id != ''\">\r\n            <el-radio :label=\"1\">是</el-radio>\r\n            <el-radio :label=\"0\">否</el-radio>\r\n          </el-radio-group>\r\n        </el-form-item>\r\n        <el-form-item label=\"是否开启直播互动\">\r\n          <el-radio-group v-model=\"form.isInteraction\" :disabled=\"props.id != ''\">\r\n            <el-radio :label=\"1\">是</el-radio>\r\n            <el-radio :label=\"0\">否</el-radio>\r\n          </el-radio-group>\r\n        </el-form-item>\r\n        <el-form-item prop=\"coverImg\">\r\n          <template #label>\r\n            上传封面\r\n            <span class=\"label-gary-tips\"> (建议宽高尺寸比为16:9) </span>\r\n          </template>\r\n          <div>\r\n            <div class=\"informationDetailsImgBox\">\r\n              <xyl-upload-img :max=\"1\" ref=\"imgFileRef\" :fileId=\"imgParams.imgFileName\"\r\n                :fileData=\"imgParams.imgFileData\" :beforeUpload=\"handleBeforeUpload\" @fileUpload=\"imgGlobalUpload\"\r\n                height=\"90px\" :disabled=\"props.id != ''\" />\r\n            </div>\r\n          </div>\r\n        </el-form-item>\r\n        <el-form-item label=\"直播设备号\">\r\n          <el-input v-model=\"form.liveNumber\" placeholder=\"请输入直播设备号\" clearable :disabled=\"props.id != ''\" />\r\n        </el-form-item>\r\n      </template>\r\n      <div class=\"globalFormButton\">\r\n        <el-button type=\"primary\" @click=\"submitForm(formRef)\">提交</el-button>\r\n        <el-button @click=\"resetForm\">取消</el-button>\r\n      </div>\r\n    </el-form>\r\n  </div>\r\n  <xyl-popup-window v-model=\"cropperShow\" name=\"封面\">\r\n    <global-cropper :enlarge=\"3\" :width=\"375\" :height=\"210\" :cropperStyle=\"{ width: '520px', height: '360px' }\"\r\n      :file=\"cropperFile\" @callback=\"handleCropper\"></global-cropper>\r\n  </xyl-popup-window>\r\n</template>\r\n<script>\r\nexport default { name: 'CreateVideoMeeting' }\r\n</script>\r\n<script setup>\r\nimport api from '@/api'\r\nimport { reactive, ref, onMounted } from 'vue'\r\nimport { format } from 'common/js/time.js'\r\nimport { ElMessage } from 'element-plus'\r\nimport GlobalCropper from 'common/components/global-cropper/global-cropper.vue'\r\nconst props = defineProps({ id: { type: String, default: '' } })\r\nconst emit = defineEmits(['callback'])\r\n\r\nconst formRef = ref()\r\nconst form = reactive({\r\n  theme: '',\r\n  startDate: '',\r\n  startTime: '',\r\n  duration: '',\r\n  isLive: 0, // 是否开启直播\r\n  joinUserIds: [],\r\n  liveDescribes: '', // 直播简介\r\n  liveUrl: '', // 直播推流地址\r\n  liveReplayUrl: '', // 直播回放地址\r\n  isReplay: 0, // 是否开启直播回放\r\n  isPublic: 0, // 是否推送群众端查看\r\n  isInteraction: 1, // 是否开启直播互动\r\n  coverImg: '', // 封面必填校验\r\n  liveNumber: '', // 直播设备号\r\n})\r\nconst imgFileRef = ref()\r\nconst imgParams = ref({\r\n  imgFileName: '',\r\n  imgFileData: []\r\n})\r\nconst cropperFile = ref({})\r\nconst cropperShow = ref(false)\r\nconst cropperCallback = ref()\r\nconst rules = reactive({\r\n  theme: [{ required: true, message: '请输入会议主题', trigger: ['blur', 'change'] }],\r\n  startTime: [{ required: true, message: '请选择会议开始时间', trigger: ['blur', 'change'] }],\r\n  duration: [{ required: true, message: '请选择会议时长', trigger: ['blur', 'change'] }],\r\n  joinUserIds: [{ required: true, message: '请选择参会人员', trigger: ['blur', 'change'] }],\r\n  liveDescribes: [{ required: true, message: '请输入直播简介', trigger: ['blur', 'change'] }],\r\n  coverImg: [{ required: true, message: '请上传封面', trigger: ['blur', 'change'] }],\r\n})\r\nconst disabledDate = (time) => time.getTime() < Date.now() - 8.64e7\r\nconst VideoMeetingTime = ref([])\r\n/**\r\n * 处理自动生成的推荐日期\r\n */\r\nconst getStartTime = () => {\r\n  var newDate = new Date().getTime()\r\n  var [hours, minutes] = format(newDate, 'HH:mm').split(':') // 处理时分\r\n  if (parseInt(minutes) >= 0 && parseInt(minutes) < 15) {\r\n    minutes = '15'\r\n  } else if (parseInt(minutes) >= 15 && parseInt(minutes) < 30) {\r\n    minutes = '30'\r\n  } else if (parseInt(minutes) >= 30 && parseInt(minutes) < 45) {\r\n    minutes = '45'\r\n  } else if (parseInt(minutes) >= 45 && parseInt(minutes) < 60) {\r\n    minutes = '00'\r\n    hours = (parseInt(hours) + 1) > 23 ? '00' : (parseInt(hours) + 1).toString()\r\n    if ((parseInt(hours) + 1) > 23) {\r\n      newDate = newDate + 24 * 60 * 60 * 1000\r\n    }\r\n  }\r\n  form.startDate = format(newDate, 'YYYY-MM-DD') // 处理日期\r\n  form.startTime = hours + ':' + minutes // 处理时间\r\n}\r\nonMounted(() => {\r\n  getStartTime()\r\n  dictionaryData()\r\n  if (props.id) { videoConnectionInfo() }\r\n})\r\nconst dictionaryData = async () => {\r\n  const { data } = await api.dictionaryData({ dictCodes: ['video_meeting_time'] })\r\n  VideoMeetingTime.value = data.video_meeting_time || []\r\n}\r\nconst videoConnectionInfo = async () => {\r\n  const res = await api.videoConnectionInfo({ detailId: props.id })\r\n  var { data } = res\r\n  form.theme = data.theme\r\n  form.duration = String(data.during)\r\n  form.isLive = data.isLive\r\n  form.startDate = format(data.startTime, 'YYYY-MM-DD')\r\n  form.startTime = format(data.startTime, 'HH:mm')\r\n  form.joinUserIds = data.joinUsers?.map(v => v.userId)\r\n  form.liveDescribes = data.liveDescribes\r\n  form.liveUrl = data.liveUrl\r\n  form.liveReplayUrl = data.liveReplayUrl\r\n  form.isReplay = data.isReplay\r\n  form.isPublic = data.isPublic\r\n  form.isInteraction = data.isInteraction\r\n  imgParams.value.imgFileName = data.coverImg\r\n  form.coverImg = data.coverImg  // 编辑模式下同步设置form.coverImg\r\n  form.liveNumber = data.liveNumber\r\n}\r\nconst userCallback = () => {\r\n  if (formRef.value) {\r\n    formRef.value.validateField('memberUserIds')\r\n  }\r\n}\r\nconst submitForm = async (formEl) => {\r\n  if (!formEl) return\r\n  await formEl.validate((valid, fields) => {\r\n    if (valid) { globalJson() } else { ElMessage({ type: 'warning', message: '请根据提示信息完善字段内容！' }) }\r\n  })\r\n}\r\nconst globalJson = async () => {\r\n  const { code } = await api.globalJson(props.id ? '/videoConnection/edit' : '/videoConnection/add', {\r\n    form: {\r\n      id: props.id,\r\n      theme: form.theme,\r\n      during: form.duration,\r\n      isLive: form.isLive,\r\n      startTime: new Date(form.startDate + ' ' + form.startTime).getTime(),\r\n      endTime: new Date(form.startDate + ' ' + form.startTime).getTime() + (60000 * Number(form.duration)),\r\n      liveDescribes: form.liveDescribes,\r\n      liveUrl: form.liveUrl,\r\n      liveReplayUrl: form.liveReplayUrl,\r\n      isReplay: form.isReplay,\r\n      isPublic: form.isPublic,\r\n      isInteraction: form.isInteraction,\r\n      coverImg: imgParams.value.imgFileName,\r\n      liveNumber: form.liveNumber\r\n    },\r\n    joinUserIds: form.joinUserIds\r\n  })\r\n  if (code === 200) {\r\n    ElMessage({ type: 'success', message: props.id ? '编辑成功' : '新增成功' })\r\n    emit('callback')\r\n  }\r\n}\r\nconst resetForm = () => { emit('callback') }\r\nconst handleBeforeUpload = (file, callbackFn) => {\r\n  cropperFile.value = file\r\n  cropperShow.value = true\r\n  cropperCallback.value = callbackFn\r\n}\r\nconst handleCropper = (file) => {\r\n  cropperShow.value = false\r\n  if (file) cropperCallback.value(file)\r\n}\r\nconst imgGlobalUpload = (file) => {\r\n  form.imgFileFlag = ''\r\n  if (file.newFileName) {\r\n    imgParams.value.imgFileName = file.newFileName\r\n    form.coverImg = file.newFileName  // 同步更新表单的coverImg字段\r\n    form.imgFileFlag = '1'\r\n  } else {\r\n    imgParams.value.imgFileName = ''\r\n    form.coverImg = ''  // 清空表单的coverImg字段\r\n  }\r\n  formRef.value.validateField('coverImg')  // 验证coverImg字段而不是imgFileFlag\r\n}\r\n</script>\r\n<style lang=\"scss\">\r\n.CreateVideoMeeting {\r\n  width: 990px;\r\n\r\n  .InitVideoMeetingTime {\r\n    width: calc(580px + var(--zy-distance-two));\r\n\r\n    .zy-el-form-item__content {\r\n      display: flex;\r\n      justify-content: space-between;\r\n    }\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;EACOA,KAAK,EAAC;AAAoB;;EA6DhBA,KAAK,EAAC;AAA0B;;EAWtCA,KAAK,EAAC;AAAkB;;;;;;;;;;;;;;;uBAzEnCC,mBAAA,CAAAC,SAAA,SACEC,mBAAA,CA6EM,OA7ENC,UA6EM,GA5EJC,YAAA,CA2EUC,kBAAA;IA3EDC,GAAG,EAAC,SAAS;IAAEC,KAAK,EAAEC,MAAA,CAAAC,IAAI;IAAGC,KAAK,EAAEF,MAAA,CAAAE,KAAK;IAAEC,MAAM,EAAN,EAAM;IAAC,gBAAc,EAAC,KAAK;IAACZ,KAAK,EAAC;;IAF1Fa,OAAA,EAAAC,QAAA,CAGM;MAAA,OAEe,CAFfT,YAAA,CAEeU,uBAAA;QAFDC,KAAK,EAAC,MAAM;QAACC,IAAI,EAAC,OAAO;QAACjB,KAAK,EAAC;;QAHpDa,OAAA,EAAAC,QAAA,CAIQ;UAAA,OAAiE,CAAjET,YAAA,CAAiEa,mBAAA;YAJzEC,UAAA,EAI2BV,MAAA,CAAAC,IAAI,CAACU,KAAK;YAJrC,uBAAAC,MAAA,QAAAA,MAAA,gBAAAC,MAAA;cAAA,OAI2Bb,MAAA,CAAAC,IAAI,CAACU,KAAK,GAAAE,MAAA;YAAA;YAAEC,WAAW,EAAC,SAAS;YAACC,SAAS,EAAT;;;QAJ7DC,CAAA;UAMMpB,YAAA,CAKeU,uBAAA;QALDC,KAAK,EAAC,QAAQ;QAACC,IAAI,EAAC,WAAW;QAACjB,KAAK,EAAC;;QAN1Da,OAAA,EAAAC,QAAA,CAOQ;UAAA,OACyF,CADzFT,YAAA,CACyFqB,0BAAA;YARjGP,UAAA,EAOkCV,MAAA,CAAAC,IAAI,CAACiB,SAAS;YAPhD,uBAAAN,MAAA,QAAAA,MAAA,gBAAAC,MAAA;cAAA,OAOkCb,MAAA,CAAAC,IAAI,CAACiB,SAAS,GAAAL,MAAA;YAAA;YAAEM,IAAI,EAAC,MAAM;YAAE,eAAa,EAAEnB,MAAA,CAAAoB,YAAY;YAAEN,WAAW,EAAC,KAAK;YACnG,cAAY,EAAC,YAAY;YAACO,MAAM,EAAC,YAAY;YAAEC,UAAU,EAAE,KAAK;YAAGP,SAAS,EAAE;mDAChFnB,YAAA,CAC2C2B,yBAAA;YAVnDb,UAAA,EASiCV,MAAA,CAAAC,IAAI,CAACuB,SAAS;YAT/C,uBAAAZ,MAAA,QAAAA,MAAA,gBAAAC,MAAA;cAAA,OASiCb,MAAA,CAAAC,IAAI,CAACuB,SAAS,GAAAX,MAAA;YAAA;YAAEY,KAAK,EAAC,OAAO;YAACC,IAAI,EAAC,OAAO;YAACC,GAAG,EAAC,OAAO;YAACb,WAAW,EAAC,KAAK;YAC9FQ,UAAU,EAAE,KAAK;YAAGP,SAAS,EAAE;;;QAV1CC,CAAA;UAYMpB,YAAA,CAIeU,uBAAA;QAJDC,KAAK,EAAC,MAAM;QAACC,IAAI,EAAC;;QAZtCJ,OAAA,EAAAC,QAAA,CAaQ;UAAA,OAEY,CAFZT,YAAA,CAEYgC,oBAAA;YAfpBlB,UAAA,EAa4BV,MAAA,CAAAC,IAAI,CAAC4B,QAAQ;YAbzC,uBAAAjB,MAAA,QAAAA,MAAA,gBAAAC,MAAA;cAAA,OAa4Bb,MAAA,CAAAC,IAAI,CAAC4B,QAAQ,GAAAhB,MAAA;YAAA;YAAEC,WAAW,EAAC,SAAS;YAAEQ,UAAU,EAAE,KAAK;YAAEP,SAAS,EAAT;;YAbrFX,OAAA,EAAAC,QAAA,CAcqB;cAAA,OAAgC,E,kBAA3Cb,mBAAA,CAAmGC,SAAA,QAd7GqC,WAAA,CAcoC9B,MAAA,CAAA+B,gBAAgB,EAdpD,UAc4BC,IAAI;qCAAtBC,YAAA,CAAmGC,oBAAA;kBAAtDC,GAAG,EAAEH,IAAI,CAACG,GAAG;kBAAG5B,KAAK,EAAEyB,IAAI,CAACI,IAAI;kBAAGC,KAAK,EAAEL,IAAI,CAACG;;;;YAdtGnB,CAAA;;;QAAAA,CAAA;UAiBMpB,YAAA,CAKeU,uBAAA;QALDC,KAAK,EAAC;MAAQ;QAjBlCH,OAAA,EAAAC,QAAA,CAkBQ;UAAA,OAGiB,CAHjBT,YAAA,CAGiB0C,yBAAA;YArBzB5B,UAAA,EAkBiCV,MAAA,CAAAC,IAAI,CAACsC,MAAM;YAlB5C,uBAAA3B,MAAA,QAAAA,MAAA,gBAAAC,MAAA;cAAA,OAkBiCb,MAAA,CAAAC,IAAI,CAACsC,MAAM,GAAA1B,MAAA;YAAA;;YAlB5CT,OAAA,EAAAC,QAAA,CAmBU;cAAA,OAAiC,CAAjCT,YAAA,CAAiC4C,mBAAA;gBAAtBjC,KAAK,EAAE;cAAC;gBAnB7BH,OAAA,EAAAC,QAAA,CAmB+B;kBAAA,OAACO,MAAA,SAAAA,MAAA,QAnBhC6B,gBAAA,CAmB+B,GAAC,E;;gBAnBhCzB,CAAA;kBAoBUpB,YAAA,CAAiC4C,mBAAA;gBAAtBjC,KAAK,EAAE;cAAC;gBApB7BH,OAAA,EAAAC,QAAA,CAoB+B;kBAAA,OAACO,MAAA,SAAAA,MAAA,QApBhC6B,gBAAA,CAoB+B,GAAC,E;;gBApBhCzB,CAAA;;;YAAAA,CAAA;;;QAAAA,CAAA;UAuBMpB,YAAA,CAEeU,uBAAA;QAFDC,KAAK,EAAC,MAAM;QAACC,IAAI,EAAC,aAAa;QAACjB,KAAK,EAAC;;QAvB1Da,OAAA,EAAAC,QAAA,CAwBQ;UAAA,OAAqG,CAArGT,YAAA,CAAqG8C,iCAAA;YAxB7GhC,UAAA,EAwByCV,MAAA,CAAAC,IAAI,CAAC0C,WAAW;YAxBzD,uBAAA/B,MAAA,QAAAA,MAAA,gBAAAC,MAAA;cAAA,OAwByCb,MAAA,CAAAC,IAAI,CAAC0C,WAAW,GAAA9B,MAAA;YAAA;YAAG+B,UAAQ,EAAE5C,MAAA,CAAA6C;;;QAxBtE7B,CAAA;UA0BsBhB,MAAA,CAAAC,IAAI,CAACsC,MAAM,U,cAA3B/C,mBAAA,CA8CWC,SAAA;QAxEjB0C,GAAA;MAAA,I,4BA2BQzC,mBAAA,CAAwC;QAAnCH,KAAK,EAAC;MAAgB,GAAC,QAAM,sBAClCK,YAAA,CAGeU,uBAAA;QAHDC,KAAK,EAAC,MAAM;QAACC,IAAI,EAAC,eAAe;QAACjB,KAAK,EAAC;;QA5B9Da,OAAA,EAAAC,QAAA,CA6BU;UAAA,OAC+B,CAD/BT,YAAA,CAC+Ba,mBAAA;YA9BzCC,UAAA,EA6B6BV,MAAA,CAAAC,IAAI,CAAC6C,aAAa;YA7B/C,uBAAAlC,MAAA,QAAAA,MAAA,gBAAAC,MAAA;cAAA,OA6B6Bb,MAAA,CAAAC,IAAI,CAAC6C,aAAa,GAAAjC,MAAA;YAAA;YAAEM,IAAI,EAAC,UAAU;YAACL,WAAW,EAAC,SAAS;YAACC,SAAS,EAAT,EAAS;YAACgC,IAAI,EAAC,GAAG;YAC5FC,QAAQ,EAAEhD,MAAA,CAAAiD,KAAK,CAACC,EAAE;;;QA9B/BlC,CAAA;UAgCQpB,YAAA,CAEeU,uBAAA;QAFDC,KAAK,EAAC,QAAQ;QAAChB,KAAK,EAAC;;QAhC3Ca,OAAA,EAAAC,QAAA,CAiCU;UAAA,OAAgG,CAAhGT,YAAA,CAAgGa,mBAAA;YAjC1GC,UAAA,EAiC6BV,MAAA,CAAAC,IAAI,CAACkD,OAAO;YAjCzC,uBAAAvC,MAAA,QAAAA,MAAA,gBAAAC,MAAA;cAAA,OAiC6Bb,MAAA,CAAAC,IAAI,CAACkD,OAAO,GAAAtC,MAAA;YAAA;YAAEC,WAAW,EAAC,WAAW;YAACC,SAAS,EAAT,EAAS;YAAEiC,QAAQ,EAAEhD,MAAA,CAAAiD,KAAK,CAACC,EAAE;;;QAjChGlC,CAAA;UAmCQpB,YAAA,CAEeU,uBAAA;QAFDC,KAAK,EAAC,QAAQ;QAAChB,KAAK,EAAC;;QAnC3Ca,OAAA,EAAAC,QAAA,CAoCU;UAAA,OAAsG,CAAtGT,YAAA,CAAsGa,mBAAA;YApChHC,UAAA,EAoC6BV,MAAA,CAAAC,IAAI,CAACmD,aAAa;YApC/C,uBAAAxC,MAAA,QAAAA,MAAA,gBAAAC,MAAA;cAAA,OAoC6Bb,MAAA,CAAAC,IAAI,CAACmD,aAAa,GAAAvC,MAAA;YAAA;YAAEC,WAAW,EAAC,WAAW;YAACC,SAAS,EAAT,EAAS;YAAEiC,QAAQ,EAAEhD,MAAA,CAAAiD,KAAK,CAACC,EAAE;;;QApCtGlC,CAAA;UAsCQpB,YAAA,CAKeU,uBAAA;QALDC,KAAK,EAAC;MAAU;QAtCtCH,OAAA,EAAAC,QAAA,CAuCU;UAAA,OAGiB,CAHjBT,YAAA,CAGiB0C,yBAAA;YA1C3B5B,UAAA,EAuCmCV,MAAA,CAAAC,IAAI,CAACoD,QAAQ;YAvChD,uBAAAzC,MAAA,QAAAA,MAAA,gBAAAC,MAAA;cAAA,OAuCmCb,MAAA,CAAAC,IAAI,CAACoD,QAAQ,GAAAxC,MAAA;YAAA;YAAGmC,QAAQ,EAAEhD,MAAA,CAAAiD,KAAK,CAACC,EAAE;;YAvCrE9C,OAAA,EAAAC,QAAA,CAwCY;cAAA,OAAiC,CAAjCT,YAAA,CAAiC4C,mBAAA;gBAAtBjC,KAAK,EAAE;cAAC;gBAxC/BH,OAAA,EAAAC,QAAA,CAwCiC;kBAAA,OAACO,MAAA,SAAAA,MAAA,QAxClC6B,gBAAA,CAwCiC,GAAC,E;;gBAxClCzB,CAAA;kBAyCYpB,YAAA,CAAiC4C,mBAAA;gBAAtBjC,KAAK,EAAE;cAAC;gBAzC/BH,OAAA,EAAAC,QAAA,CAyCiC;kBAAA,OAACO,MAAA,SAAAA,MAAA,QAzClC6B,gBAAA,CAyCiC,GAAC,E;;gBAzClCzB,CAAA;;;YAAAA,CAAA;;;QAAAA,CAAA;UA4CQpB,YAAA,CAKeU,uBAAA;QALDC,KAAK,EAAC;MAAW;QA5CvCH,OAAA,EAAAC,QAAA,CA6CU;UAAA,OAGiB,CAHjBT,YAAA,CAGiB0C,yBAAA;YAhD3B5B,UAAA,EA6CmCV,MAAA,CAAAC,IAAI,CAACqD,QAAQ;YA7ChD,uBAAA1C,MAAA,SAAAA,MAAA,iBAAAC,MAAA;cAAA,OA6CmCb,MAAA,CAAAC,IAAI,CAACqD,QAAQ,GAAAzC,MAAA;YAAA;YAAGmC,QAAQ,EAAEhD,MAAA,CAAAiD,KAAK,CAACC,EAAE;;YA7CrE9C,OAAA,EAAAC,QAAA,CA8CY;cAAA,OAAiC,CAAjCT,YAAA,CAAiC4C,mBAAA;gBAAtBjC,KAAK,EAAE;cAAC;gBA9C/BH,OAAA,EAAAC,QAAA,CA8CiC;kBAAA,OAACO,MAAA,SAAAA,MAAA,QA9ClC6B,gBAAA,CA8CiC,GAAC,E;;gBA9ClCzB,CAAA;kBA+CYpB,YAAA,CAAiC4C,mBAAA;gBAAtBjC,KAAK,EAAE;cAAC;gBA/C/BH,OAAA,EAAAC,QAAA,CA+CiC;kBAAA,OAACO,MAAA,SAAAA,MAAA,QA/ClC6B,gBAAA,CA+CiC,GAAC,E;;gBA/ClCzB,CAAA;;;YAAAA,CAAA;;;QAAAA,CAAA;UAkDQpB,YAAA,CAKeU,uBAAA;QALDC,KAAK,EAAC;MAAU;QAlDtCH,OAAA,EAAAC,QAAA,CAmDU;UAAA,OAGiB,CAHjBT,YAAA,CAGiB0C,yBAAA;YAtD3B5B,UAAA,EAmDmCV,MAAA,CAAAC,IAAI,CAACsD,aAAa;YAnDrD,uBAAA3C,MAAA,SAAAA,MAAA,iBAAAC,MAAA;cAAA,OAmDmCb,MAAA,CAAAC,IAAI,CAACsD,aAAa,GAAA1C,MAAA;YAAA;YAAGmC,QAAQ,EAAEhD,MAAA,CAAAiD,KAAK,CAACC,EAAE;;YAnD1E9C,OAAA,EAAAC,QAAA,CAoDY;cAAA,OAAiC,CAAjCT,YAAA,CAAiC4C,mBAAA;gBAAtBjC,KAAK,EAAE;cAAC;gBApD/BH,OAAA,EAAAC,QAAA,CAoDiC;kBAAA,OAACO,MAAA,SAAAA,MAAA,QApDlC6B,gBAAA,CAoDiC,GAAC,E;;gBApDlCzB,CAAA;kBAqDYpB,YAAA,CAAiC4C,mBAAA;gBAAtBjC,KAAK,EAAE;cAAC;gBArD/BH,OAAA,EAAAC,QAAA,CAqDiC;kBAAA,OAACO,MAAA,SAAAA,MAAA,QArDlC6B,gBAAA,CAqDiC,GAAC,E;;gBArDlCzB,CAAA;;;YAAAA,CAAA;;;QAAAA,CAAA;UAwDQpB,YAAA,CAYeU,uBAAA;QAZDE,IAAI,EAAC;MAAU;QAChBD,KAAK,EAAAF,QAAA,CAAC;UAAA,OAEfO,MAAA,SAAAA,MAAA,QA3DZ6B,gBAAA,CAyD2B,QAEf,GAAA/C,mBAAA,CAAqD;YAA/CH,KAAK,EAAC;UAAiB,GAAC,kBAAgB,oB;;QA3D1Da,OAAA,EAAAC,QAAA,CA6DU;UAAA,OAMM,CANNX,mBAAA,CAMM,cALJA,mBAAA,CAIM,OAJN8D,UAIM,GAHJ5D,YAAA,CAE6C6D,yBAAA;YAF5BC,GAAG,EAAE,CAAC;YAAE5D,GAAG,EAAC,YAAY;YAAE6D,MAAM,EAAE3D,MAAA,CAAA4D,SAAS,CAACC,WAAW;YACrEC,QAAQ,EAAE9D,MAAA,CAAA4D,SAAS,CAACG,WAAW;YAAGC,YAAY,EAAEhE,MAAA,CAAAiE,kBAAkB;YAAGC,YAAU,EAAElE,MAAA,CAAAmE,eAAe;YACjGC,MAAM,EAAC,MAAM;YAAEpB,QAAQ,EAAEhD,MAAA,CAAAiD,KAAK,CAACC,EAAE;;;QAjEjDlC,CAAA;UAqEQpB,YAAA,CAEeU,uBAAA;QAFDC,KAAK,EAAC;MAAO;QArEnCH,OAAA,EAAAC,QAAA,CAsEU;UAAA,OAAkG,CAAlGT,YAAA,CAAkGa,mBAAA;YAtE5GC,UAAA,EAsE6BV,MAAA,CAAAC,IAAI,CAACoE,UAAU;YAtE5C,uBAAAzD,MAAA,SAAAA,MAAA,iBAAAC,MAAA;cAAA,OAsE6Bb,MAAA,CAAAC,IAAI,CAACoE,UAAU,GAAAxD,MAAA;YAAA;YAAEC,WAAW,EAAC,UAAU;YAACC,SAAS,EAAT,EAAS;YAAEiC,QAAQ,EAAEhD,MAAA,CAAAiD,KAAK,CAACC,EAAE;;;QAtElGlC,CAAA;wCAAAsD,mBAAA,gBAyEM5E,mBAAA,CAGM,OAHN6E,UAGM,GAFJ3E,YAAA,CAAqE4E,oBAAA;QAA1DrD,IAAI,EAAC,SAAS;QAAEsD,OAAK,EAAA7D,MAAA,SAAAA,MAAA,iBAAAC,MAAA;UAAA,OAAEb,MAAA,CAAA0E,UAAU,CAAC1E,MAAA,CAAA2E,OAAO;QAAA;;QA1E5DvE,OAAA,EAAAC,QAAA,CA0E+D;UAAA,OAAEO,MAAA,SAAAA,MAAA,QA1EjE6B,gBAAA,CA0E+D,IAAE,E;;QA1EjEzB,CAAA;UA2EQpB,YAAA,CAA4C4E,oBAAA;QAAhCC,OAAK,EAAEzE,MAAA,CAAA4E;MAAS;QA3EpCxE,OAAA,EAAAC,QAAA,CA2EsC;UAAA,OAAEO,MAAA,SAAAA,MAAA,QA3ExC6B,gBAAA,CA2EsC,IAAE,E;;QA3ExCzB,CAAA;;;IAAAA,CAAA;2CA+EEpB,YAAA,CAGmBiF,2BAAA;IAlFrBnE,UAAA,EA+E6BV,MAAA,CAAA8E,WAAW;IA/ExC,uBAAAlE,MAAA,SAAAA,MAAA,iBAAAC,MAAA;MAAA,OA+E6Bb,MAAA,CAAA8E,WAAW,GAAAjE,MAAA;IAAA;IAAEuB,IAAI,EAAC;;IA/E/ChC,OAAA,EAAAC,QAAA,CAgFI;MAAA,OACiE,CADjET,YAAA,CACiEI,MAAA;QADhD+E,OAAO,EAAE,CAAC;QAAGC,KAAK,EAAE,GAAG;QAAGZ,MAAM,EAAE,GAAG;QAAGa,YAAY,EAAE;UAAAD,KAAA;UAAAZ,MAAA;QAAA,CAAmC;QACvGc,IAAI,EAAElF,MAAA,CAAAmF,WAAW;QAAGvC,UAAQ,EAAE5C,MAAA,CAAAoF;;;IAjFrCpE,CAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
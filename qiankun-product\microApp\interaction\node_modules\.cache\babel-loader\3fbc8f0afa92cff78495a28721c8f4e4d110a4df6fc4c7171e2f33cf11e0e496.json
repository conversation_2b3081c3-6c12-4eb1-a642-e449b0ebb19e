{"ast": null, "code": "import identity from './identity.js';\nimport overRest from './_overRest.js';\nimport setToString from './_setToString.js';\n\n/**\n * The base implementation of `_.rest` which doesn't validate or coerce arguments.\n *\n * @private\n * @param {Function} func The function to apply a rest parameter to.\n * @param {number} [start=func.length-1] The start position of the rest parameter.\n * @returns {Function} Returns the new function.\n */\nfunction baseRest(func, start) {\n  return setToString(overRest(func, start, identity), func + '');\n}\nexport default baseRest;", "map": {"version": 3, "names": ["identity", "overRest", "setToString", "baseRest", "func", "start"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/node_modules/.pnpm/lodash-es@4.17.21/node_modules/lodash-es/_baseRest.js"], "sourcesContent": ["import identity from './identity.js';\nimport overRest from './_overRest.js';\nimport setToString from './_setToString.js';\n\n/**\n * The base implementation of `_.rest` which doesn't validate or coerce arguments.\n *\n * @private\n * @param {Function} func The function to apply a rest parameter to.\n * @param {number} [start=func.length-1] The start position of the rest parameter.\n * @returns {Function} Returns the new function.\n */\nfunction baseRest(func, start) {\n  return setToString(overRest(func, start, identity), func + '');\n}\n\nexport default baseRest;\n"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,eAAe;AACpC,OAAOC,QAAQ,MAAM,gBAAgB;AACrC,OAAOC,WAAW,MAAM,mBAAmB;;AAE3C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,QAAQA,CAACC,IAAI,EAAEC,KAAK,EAAE;EAC7B,OAAOH,WAAW,CAACD,QAAQ,CAACG,IAAI,EAAEC,KAAK,EAAEL,QAAQ,CAAC,EAAEI,IAAI,GAAG,EAAE,CAAC;AAChE;AAEA,eAAeD,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
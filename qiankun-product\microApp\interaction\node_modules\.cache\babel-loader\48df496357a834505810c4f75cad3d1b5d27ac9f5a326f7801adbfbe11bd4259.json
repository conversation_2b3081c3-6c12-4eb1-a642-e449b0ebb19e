{"ast": null, "code": "import baseTimes from './_baseTimes.js';\nimport isArguments from './isArguments.js';\nimport isArray from './isArray.js';\nimport isBuffer from './isBuffer.js';\nimport isIndex from './_isIndex.js';\nimport isTypedArray from './isTypedArray.js';\n\n/** Used for built-in method references. */\nvar objectProto = Object.prototype;\n\n/** Used to check objects for own properties. */\nvar hasOwnProperty = objectProto.hasOwnProperty;\n\n/**\n * Creates an array of the enumerable property names of the array-like `value`.\n *\n * @private\n * @param {*} value The value to query.\n * @param {boolean} inherited Specify returning inherited property names.\n * @returns {Array} Returns the array of property names.\n */\nfunction arrayLikeKeys(value, inherited) {\n  var isArr = isArray(value),\n    isArg = !isArr && isArguments(value),\n    isBuff = !isArr && !isArg && isBuffer(value),\n    isType = !isArr && !isArg && !isBuff && isTypedArray(value),\n    skipIndexes = isArr || isArg || isBuff || isType,\n    result = skipIndexes ? baseTimes(value.length, String) : [],\n    length = result.length;\n  for (var key in value) {\n    if ((inherited || hasOwnProperty.call(value, key)) && !(skipIndexes && (\n    // Safari 9 has enumerable `arguments.length` in strict mode.\n    key == 'length' ||\n    // Node.js 0.10 has enumerable non-index properties on buffers.\n    isBuff && (key == 'offset' || key == 'parent') ||\n    // PhantomJS 2 has enumerable non-index properties on typed arrays.\n    isType && (key == 'buffer' || key == 'byteLength' || key == 'byteOffset') ||\n    // Skip index properties.\n    isIndex(key, length)))) {\n      result.push(key);\n    }\n  }\n  return result;\n}\nexport default arrayLikeKeys;", "map": {"version": 3, "names": ["baseTimes", "isArguments", "isArray", "<PERSON><PERSON><PERSON><PERSON>", "isIndex", "isTypedArray", "objectProto", "Object", "prototype", "hasOwnProperty", "arrayLikeKeys", "value", "inherited", "isArr", "isArg", "isBuff", "isType", "skipIndexes", "result", "length", "String", "key", "call", "push"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/node_modules/.pnpm/lodash-es@4.17.21/node_modules/lodash-es/_arrayLikeKeys.js"], "sourcesContent": ["import baseTimes from './_baseTimes.js';\nimport isArguments from './isArguments.js';\nimport isArray from './isArray.js';\nimport isBuffer from './isBuffer.js';\nimport isIndex from './_isIndex.js';\nimport isTypedArray from './isTypedArray.js';\n\n/** Used for built-in method references. */\nvar objectProto = Object.prototype;\n\n/** Used to check objects for own properties. */\nvar hasOwnProperty = objectProto.hasOwnProperty;\n\n/**\n * Creates an array of the enumerable property names of the array-like `value`.\n *\n * @private\n * @param {*} value The value to query.\n * @param {boolean} inherited Specify returning inherited property names.\n * @returns {Array} Returns the array of property names.\n */\nfunction arrayLikeKeys(value, inherited) {\n  var isArr = isArray(value),\n      isArg = !isArr && isArguments(value),\n      isBuff = !isArr && !isArg && isBuffer(value),\n      isType = !isArr && !isArg && !isBuff && isTypedArray(value),\n      skipIndexes = isArr || isArg || isBuff || isType,\n      result = skipIndexes ? baseTimes(value.length, String) : [],\n      length = result.length;\n\n  for (var key in value) {\n    if ((inherited || hasOwnProperty.call(value, key)) &&\n        !(skipIndexes && (\n           // Safari 9 has enumerable `arguments.length` in strict mode.\n           key == 'length' ||\n           // Node.js 0.10 has enumerable non-index properties on buffers.\n           (isBuff && (key == 'offset' || key == 'parent')) ||\n           // PhantomJS 2 has enumerable non-index properties on typed arrays.\n           (isType && (key == 'buffer' || key == 'byteLength' || key == 'byteOffset')) ||\n           // Skip index properties.\n           isIndex(key, length)\n        ))) {\n      result.push(key);\n    }\n  }\n  return result;\n}\n\nexport default arrayLikeKeys;\n"], "mappings": "AAAA,OAAOA,SAAS,MAAM,iBAAiB;AACvC,OAAOC,WAAW,MAAM,kBAAkB;AAC1C,OAAOC,OAAO,MAAM,cAAc;AAClC,OAAOC,QAAQ,MAAM,eAAe;AACpC,OAAOC,OAAO,MAAM,eAAe;AACnC,OAAOC,YAAY,MAAM,mBAAmB;;AAE5C;AACA,IAAIC,WAAW,GAAGC,MAAM,CAACC,SAAS;;AAElC;AACA,IAAIC,cAAc,GAAGH,WAAW,CAACG,cAAc;;AAE/C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,aAAaA,CAACC,KAAK,EAAEC,SAAS,EAAE;EACvC,IAAIC,KAAK,GAAGX,OAAO,CAACS,KAAK,CAAC;IACtBG,KAAK,GAAG,CAACD,KAAK,IAAIZ,WAAW,CAACU,KAAK,CAAC;IACpCI,MAAM,GAAG,CAACF,KAAK,IAAI,CAACC,KAAK,IAAIX,QAAQ,CAACQ,KAAK,CAAC;IAC5CK,MAAM,GAAG,CAACH,KAAK,IAAI,CAACC,KAAK,IAAI,CAACC,MAAM,IAAIV,YAAY,CAACM,KAAK,CAAC;IAC3DM,WAAW,GAAGJ,KAAK,IAAIC,KAAK,IAAIC,MAAM,IAAIC,MAAM;IAChDE,MAAM,GAAGD,WAAW,GAAGjB,SAAS,CAACW,KAAK,CAACQ,MAAM,EAAEC,MAAM,CAAC,GAAG,EAAE;IAC3DD,MAAM,GAAGD,MAAM,CAACC,MAAM;EAE1B,KAAK,IAAIE,GAAG,IAAIV,KAAK,EAAE;IACrB,IAAI,CAACC,SAAS,IAAIH,cAAc,CAACa,IAAI,CAACX,KAAK,EAAEU,GAAG,CAAC,KAC7C,EAAEJ,WAAW;IACV;IACAI,GAAG,IAAI,QAAQ;IACf;IACCN,MAAM,KAAKM,GAAG,IAAI,QAAQ,IAAIA,GAAG,IAAI,QAAQ,CAAE;IAChD;IACCL,MAAM,KAAKK,GAAG,IAAI,QAAQ,IAAIA,GAAG,IAAI,YAAY,IAAIA,GAAG,IAAI,YAAY,CAAE;IAC3E;IACAjB,OAAO,CAACiB,GAAG,EAAEF,MAAM,CAAC,CACtB,CAAC,EAAE;MACND,MAAM,CAACK,IAAI,CAACF,GAAG,CAAC;IAClB;EACF;EACA,OAAOH,MAAM;AACf;AAEA,eAAeR,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "function hasWindow() {\n  return typeof window !== 'undefined';\n}\nfunction getNodeName(node) {\n  if (isNode(node)) {\n    return (node.nodeName || '').toLowerCase();\n  }\n  // Mocked nodes in testing environments may not be instances of Node. By\n  // returning `#document` an infinite loop won't occur.\n  // https://github.com/floating-ui/floating-ui/issues/2317\n  return '#document';\n}\nfunction getWindow(node) {\n  var _node$ownerDocument;\n  return (node == null || (_node$ownerDocument = node.ownerDocument) == null ? void 0 : _node$ownerDocument.defaultView) || window;\n}\nfunction getDocumentElement(node) {\n  var _ref;\n  return (_ref = (isNode(node) ? node.ownerDocument : node.document) || window.document) == null ? void 0 : _ref.documentElement;\n}\nfunction isNode(value) {\n  if (!hasWindow()) {\n    return false;\n  }\n  return value instanceof Node || value instanceof getWindow(value).Node;\n}\nfunction isElement(value) {\n  if (!hasWindow()) {\n    return false;\n  }\n  return value instanceof Element || value instanceof getWindow(value).Element;\n}\nfunction isHTMLElement(value) {\n  if (!hasWindow()) {\n    return false;\n  }\n  return value instanceof HTMLElement || value instanceof getWindow(value).HTMLElement;\n}\nfunction isShadowRoot(value) {\n  if (!hasWindow() || typeof ShadowRoot === 'undefined') {\n    return false;\n  }\n  return value instanceof ShadowRoot || value instanceof getWindow(value).ShadowRoot;\n}\nfunction isOverflowElement(element) {\n  var _getComputedStyle = getComputedStyle(element),\n    overflow = _getComputedStyle.overflow,\n    overflowX = _getComputedStyle.overflowX,\n    overflowY = _getComputedStyle.overflowY,\n    display = _getComputedStyle.display;\n  return /auto|scroll|overlay|hidden|clip/.test(overflow + overflowY + overflowX) && !['inline', 'contents'].includes(display);\n}\nfunction isTableElement(element) {\n  return ['table', 'td', 'th'].includes(getNodeName(element));\n}\nfunction isTopLayer(element) {\n  return [':popover-open', ':modal'].some(function (selector) {\n    try {\n      return element.matches(selector);\n    } catch (e) {\n      return false;\n    }\n  });\n}\nfunction isContainingBlock(elementOrCss) {\n  var webkit = isWebKit();\n  var css = isElement(elementOrCss) ? getComputedStyle(elementOrCss) : elementOrCss;\n\n  // https://developer.mozilla.org/en-US/docs/Web/CSS/Containing_block#identifying_the_containing_block\n  return css.transform !== 'none' || css.perspective !== 'none' || (css.containerType ? css.containerType !== 'normal' : false) || !webkit && (css.backdropFilter ? css.backdropFilter !== 'none' : false) || !webkit && (css.filter ? css.filter !== 'none' : false) || ['transform', 'perspective', 'filter'].some(function (value) {\n    return (css.willChange || '').includes(value);\n  }) || ['paint', 'layout', 'strict', 'content'].some(function (value) {\n    return (css.contain || '').includes(value);\n  });\n}\nfunction getContainingBlock(element) {\n  var currentNode = getParentNode(element);\n  while (isHTMLElement(currentNode) && !isLastTraversableNode(currentNode)) {\n    if (isContainingBlock(currentNode)) {\n      return currentNode;\n    } else if (isTopLayer(currentNode)) {\n      return null;\n    }\n    currentNode = getParentNode(currentNode);\n  }\n  return null;\n}\nfunction isWebKit() {\n  if (typeof CSS === 'undefined' || !CSS.supports) return false;\n  return CSS.supports('-webkit-backdrop-filter', 'none');\n}\nfunction isLastTraversableNode(node) {\n  return ['html', 'body', '#document'].includes(getNodeName(node));\n}\nfunction getComputedStyle(element) {\n  return getWindow(element).getComputedStyle(element);\n}\nfunction getNodeScroll(element) {\n  if (isElement(element)) {\n    return {\n      scrollLeft: element.scrollLeft,\n      scrollTop: element.scrollTop\n    };\n  }\n  return {\n    scrollLeft: element.scrollX,\n    scrollTop: element.scrollY\n  };\n}\nfunction getParentNode(node) {\n  if (getNodeName(node) === 'html') {\n    return node;\n  }\n  var result =\n  // Step into the shadow DOM of the parent of a slotted node.\n  node.assignedSlot ||\n  // DOM Element detected.\n  node.parentNode ||\n  // ShadowRoot detected.\n  isShadowRoot(node) && node.host ||\n  // Fallback.\n  getDocumentElement(node);\n  return isShadowRoot(result) ? result.host : result;\n}\nfunction getNearestOverflowAncestor(node) {\n  var parentNode = getParentNode(node);\n  if (isLastTraversableNode(parentNode)) {\n    return node.ownerDocument ? node.ownerDocument.body : node.body;\n  }\n  if (isHTMLElement(parentNode) && isOverflowElement(parentNode)) {\n    return parentNode;\n  }\n  return getNearestOverflowAncestor(parentNode);\n}\nfunction getOverflowAncestors(node, list, traverseIframes) {\n  var _node$ownerDocument2;\n  if (list === void 0) {\n    list = [];\n  }\n  if (traverseIframes === void 0) {\n    traverseIframes = true;\n  }\n  var scrollableAncestor = getNearestOverflowAncestor(node);\n  var isBody = scrollableAncestor === ((_node$ownerDocument2 = node.ownerDocument) == null ? void 0 : _node$ownerDocument2.body);\n  var win = getWindow(scrollableAncestor);\n  if (isBody) {\n    var frameElement = getFrameElement(win);\n    return list.concat(win, win.visualViewport || [], isOverflowElement(scrollableAncestor) ? scrollableAncestor : [], frameElement && traverseIframes ? getOverflowAncestors(frameElement) : []);\n  }\n  return list.concat(scrollableAncestor, getOverflowAncestors(scrollableAncestor, [], traverseIframes));\n}\nfunction getFrameElement(win) {\n  return win.parent && Object.getPrototypeOf(win.parent) ? win.frameElement : null;\n}\nexport { getComputedStyle, getContainingBlock, getDocumentElement, getFrameElement, getNearestOverflowAncestor, getNodeName, getNodeScroll, getOverflowAncestors, getParentNode, getWindow, isContainingBlock, isElement, isHTMLElement, isLastTraversableNode, isNode, isOverflowElement, isShadowRoot, isTableElement, isTopLayer, isWebKit };", "map": {"version": 3, "names": ["hasW<PERSON>ow", "window", "getNodeName", "node", "isNode", "nodeName", "toLowerCase", "getWindow", "_node$ownerDocument", "ownerDocument", "defaultView", "getDocumentElement", "_ref", "document", "documentElement", "value", "Node", "isElement", "Element", "isHTMLElement", "HTMLElement", "isShadowRoot", "ShadowRoot", "isOverflowElement", "element", "_getComputedStyle", "getComputedStyle", "overflow", "overflowX", "overflowY", "display", "test", "includes", "isTableElement", "isTop<PERSON><PERSON>er", "some", "selector", "matches", "e", "isContainingBlock", "elementOrCss", "webkit", "isWebKit", "css", "transform", "perspective", "containerType", "<PERSON><PERSON>ilter", "filter", "<PERSON><PERSON><PERSON><PERSON>", "contain", "getContainingBlock", "currentNode", "getParentNode", "isLastTraversableNode", "CSS", "supports", "getNodeScroll", "scrollLeft", "scrollTop", "scrollX", "scrollY", "result", "assignedSlot", "parentNode", "host", "getNearestOverflowAncestor", "body", "getOverflowAncestors", "list", "traverseIframes", "_node$ownerDocument2", "scrollableAncestor", "isBody", "win", "frameElement", "getFrameElement", "concat", "visualViewport", "parent", "Object", "getPrototypeOf"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/node_modules/.pnpm/@floating-ui+utils@0.2.8/node_modules/@floating-ui/utils/dist/floating-ui.utils.dom.mjs"], "sourcesContent": ["function hasWindow() {\n  return typeof window !== 'undefined';\n}\nfunction getNodeName(node) {\n  if (isNode(node)) {\n    return (node.nodeName || '').toLowerCase();\n  }\n  // Mocked nodes in testing environments may not be instances of Node. By\n  // returning `#document` an infinite loop won't occur.\n  // https://github.com/floating-ui/floating-ui/issues/2317\n  return '#document';\n}\nfunction getWindow(node) {\n  var _node$ownerDocument;\n  return (node == null || (_node$ownerDocument = node.ownerDocument) == null ? void 0 : _node$ownerDocument.defaultView) || window;\n}\nfunction getDocumentElement(node) {\n  var _ref;\n  return (_ref = (isNode(node) ? node.ownerDocument : node.document) || window.document) == null ? void 0 : _ref.documentElement;\n}\nfunction isNode(value) {\n  if (!hasWindow()) {\n    return false;\n  }\n  return value instanceof Node || value instanceof getWindow(value).Node;\n}\nfunction isElement(value) {\n  if (!hasWindow()) {\n    return false;\n  }\n  return value instanceof Element || value instanceof getWindow(value).Element;\n}\nfunction isHTMLElement(value) {\n  if (!hasWindow()) {\n    return false;\n  }\n  return value instanceof HTMLElement || value instanceof getWindow(value).HTMLElement;\n}\nfunction isShadowRoot(value) {\n  if (!hasWindow() || typeof ShadowRoot === 'undefined') {\n    return false;\n  }\n  return value instanceof ShadowRoot || value instanceof getWindow(value).ShadowRoot;\n}\nfunction isOverflowElement(element) {\n  const {\n    overflow,\n    overflowX,\n    overflowY,\n    display\n  } = getComputedStyle(element);\n  return /auto|scroll|overlay|hidden|clip/.test(overflow + overflowY + overflowX) && !['inline', 'contents'].includes(display);\n}\nfunction isTableElement(element) {\n  return ['table', 'td', 'th'].includes(getNodeName(element));\n}\nfunction isTopLayer(element) {\n  return [':popover-open', ':modal'].some(selector => {\n    try {\n      return element.matches(selector);\n    } catch (e) {\n      return false;\n    }\n  });\n}\nfunction isContainingBlock(elementOrCss) {\n  const webkit = isWebKit();\n  const css = isElement(elementOrCss) ? getComputedStyle(elementOrCss) : elementOrCss;\n\n  // https://developer.mozilla.org/en-US/docs/Web/CSS/Containing_block#identifying_the_containing_block\n  return css.transform !== 'none' || css.perspective !== 'none' || (css.containerType ? css.containerType !== 'normal' : false) || !webkit && (css.backdropFilter ? css.backdropFilter !== 'none' : false) || !webkit && (css.filter ? css.filter !== 'none' : false) || ['transform', 'perspective', 'filter'].some(value => (css.willChange || '').includes(value)) || ['paint', 'layout', 'strict', 'content'].some(value => (css.contain || '').includes(value));\n}\nfunction getContainingBlock(element) {\n  let currentNode = getParentNode(element);\n  while (isHTMLElement(currentNode) && !isLastTraversableNode(currentNode)) {\n    if (isContainingBlock(currentNode)) {\n      return currentNode;\n    } else if (isTopLayer(currentNode)) {\n      return null;\n    }\n    currentNode = getParentNode(currentNode);\n  }\n  return null;\n}\nfunction isWebKit() {\n  if (typeof CSS === 'undefined' || !CSS.supports) return false;\n  return CSS.supports('-webkit-backdrop-filter', 'none');\n}\nfunction isLastTraversableNode(node) {\n  return ['html', 'body', '#document'].includes(getNodeName(node));\n}\nfunction getComputedStyle(element) {\n  return getWindow(element).getComputedStyle(element);\n}\nfunction getNodeScroll(element) {\n  if (isElement(element)) {\n    return {\n      scrollLeft: element.scrollLeft,\n      scrollTop: element.scrollTop\n    };\n  }\n  return {\n    scrollLeft: element.scrollX,\n    scrollTop: element.scrollY\n  };\n}\nfunction getParentNode(node) {\n  if (getNodeName(node) === 'html') {\n    return node;\n  }\n  const result =\n  // Step into the shadow DOM of the parent of a slotted node.\n  node.assignedSlot ||\n  // DOM Element detected.\n  node.parentNode ||\n  // ShadowRoot detected.\n  isShadowRoot(node) && node.host ||\n  // Fallback.\n  getDocumentElement(node);\n  return isShadowRoot(result) ? result.host : result;\n}\nfunction getNearestOverflowAncestor(node) {\n  const parentNode = getParentNode(node);\n  if (isLastTraversableNode(parentNode)) {\n    return node.ownerDocument ? node.ownerDocument.body : node.body;\n  }\n  if (isHTMLElement(parentNode) && isOverflowElement(parentNode)) {\n    return parentNode;\n  }\n  return getNearestOverflowAncestor(parentNode);\n}\nfunction getOverflowAncestors(node, list, traverseIframes) {\n  var _node$ownerDocument2;\n  if (list === void 0) {\n    list = [];\n  }\n  if (traverseIframes === void 0) {\n    traverseIframes = true;\n  }\n  const scrollableAncestor = getNearestOverflowAncestor(node);\n  const isBody = scrollableAncestor === ((_node$ownerDocument2 = node.ownerDocument) == null ? void 0 : _node$ownerDocument2.body);\n  const win = getWindow(scrollableAncestor);\n  if (isBody) {\n    const frameElement = getFrameElement(win);\n    return list.concat(win, win.visualViewport || [], isOverflowElement(scrollableAncestor) ? scrollableAncestor : [], frameElement && traverseIframes ? getOverflowAncestors(frameElement) : []);\n  }\n  return list.concat(scrollableAncestor, getOverflowAncestors(scrollableAncestor, [], traverseIframes));\n}\nfunction getFrameElement(win) {\n  return win.parent && Object.getPrototypeOf(win.parent) ? win.frameElement : null;\n}\n\nexport { getComputedStyle, getContainingBlock, getDocumentElement, getFrameElement, getNearestOverflowAncestor, getNodeName, getNodeScroll, getOverflowAncestors, getParentNode, getWindow, isContainingBlock, isElement, isHTMLElement, isLastTraversableNode, isNode, isOverflowElement, isShadowRoot, isTableElement, isTopLayer, isWebKit };\n"], "mappings": "AAAA,SAASA,SAASA,CAAA,EAAG;EACnB,OAAO,OAAOC,MAAM,KAAK,WAAW;AACtC;AACA,SAASC,WAAWA,CAACC,IAAI,EAAE;EACzB,IAAIC,MAAM,CAACD,IAAI,CAAC,EAAE;IAChB,OAAO,CAACA,IAAI,CAACE,QAAQ,IAAI,EAAE,EAAEC,WAAW,CAAC,CAAC;EAC5C;EACA;EACA;EACA;EACA,OAAO,WAAW;AACpB;AACA,SAASC,SAASA,CAACJ,IAAI,EAAE;EACvB,IAAIK,mBAAmB;EACvB,OAAO,CAACL,IAAI,IAAI,IAAI,IAAI,CAACK,mBAAmB,GAAGL,IAAI,CAACM,aAAa,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGD,mBAAmB,CAACE,WAAW,KAAKT,MAAM;AAClI;AACA,SAASU,kBAAkBA,CAACR,IAAI,EAAE;EAChC,IAAIS,IAAI;EACR,OAAO,CAACA,IAAI,GAAG,CAACR,MAAM,CAACD,IAAI,CAAC,GAAGA,IAAI,CAACM,aAAa,GAAGN,IAAI,CAACU,QAAQ,KAAKZ,MAAM,CAACY,QAAQ,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGD,IAAI,CAACE,eAAe;AAChI;AACA,SAASV,MAAMA,CAACW,KAAK,EAAE;EACrB,IAAI,CAACf,SAAS,CAAC,CAAC,EAAE;IAChB,OAAO,KAAK;EACd;EACA,OAAOe,KAAK,YAAYC,IAAI,IAAID,KAAK,YAAYR,SAAS,CAACQ,KAAK,CAAC,CAACC,IAAI;AACxE;AACA,SAASC,SAASA,CAACF,KAAK,EAAE;EACxB,IAAI,CAACf,SAAS,CAAC,CAAC,EAAE;IAChB,OAAO,KAAK;EACd;EACA,OAAOe,KAAK,YAAYG,OAAO,IAAIH,KAAK,YAAYR,SAAS,CAACQ,KAAK,CAAC,CAACG,OAAO;AAC9E;AACA,SAASC,aAAaA,CAACJ,KAAK,EAAE;EAC5B,IAAI,CAACf,SAAS,CAAC,CAAC,EAAE;IAChB,OAAO,KAAK;EACd;EACA,OAAOe,KAAK,YAAYK,WAAW,IAAIL,KAAK,YAAYR,SAAS,CAACQ,KAAK,CAAC,CAACK,WAAW;AACtF;AACA,SAASC,YAAYA,CAACN,KAAK,EAAE;EAC3B,IAAI,CAACf,SAAS,CAAC,CAAC,IAAI,OAAOsB,UAAU,KAAK,WAAW,EAAE;IACrD,OAAO,KAAK;EACd;EACA,OAAOP,KAAK,YAAYO,UAAU,IAAIP,KAAK,YAAYR,SAAS,CAACQ,KAAK,CAAC,CAACO,UAAU;AACpF;AACA,SAASC,iBAAiBA,CAACC,OAAO,EAAE;EAClC,IAAAC,iBAAA,GAKIC,gBAAgB,CAACF,OAAO,CAAC;IAJ3BG,QAAQ,GAAAF,iBAAA,CAARE,QAAQ;IACRC,SAAS,GAAAH,iBAAA,CAATG,SAAS;IACTC,SAAS,GAAAJ,iBAAA,CAATI,SAAS;IACTC,OAAO,GAAAL,iBAAA,CAAPK,OAAO;EAET,OAAO,iCAAiC,CAACC,IAAI,CAACJ,QAAQ,GAAGE,SAAS,GAAGD,SAAS,CAAC,IAAI,CAAC,CAAC,QAAQ,EAAE,UAAU,CAAC,CAACI,QAAQ,CAACF,OAAO,CAAC;AAC9H;AACA,SAASG,cAAcA,CAACT,OAAO,EAAE;EAC/B,OAAO,CAAC,OAAO,EAAE,IAAI,EAAE,IAAI,CAAC,CAACQ,QAAQ,CAAC9B,WAAW,CAACsB,OAAO,CAAC,CAAC;AAC7D;AACA,SAASU,UAAUA,CAACV,OAAO,EAAE;EAC3B,OAAO,CAAC,eAAe,EAAE,QAAQ,CAAC,CAACW,IAAI,CAAC,UAAAC,QAAQ,EAAI;IAClD,IAAI;MACF,OAAOZ,OAAO,CAACa,OAAO,CAACD,QAAQ,CAAC;IAClC,CAAC,CAAC,OAAOE,CAAC,EAAE;MACV,OAAO,KAAK;IACd;EACF,CAAC,CAAC;AACJ;AACA,SAASC,iBAAiBA,CAACC,YAAY,EAAE;EACvC,IAAMC,MAAM,GAAGC,QAAQ,CAAC,CAAC;EACzB,IAAMC,GAAG,GAAG1B,SAAS,CAACuB,YAAY,CAAC,GAAGd,gBAAgB,CAACc,YAAY,CAAC,GAAGA,YAAY;;EAEnF;EACA,OAAOG,GAAG,CAACC,SAAS,KAAK,MAAM,IAAID,GAAG,CAACE,WAAW,KAAK,MAAM,KAAKF,GAAG,CAACG,aAAa,GAAGH,GAAG,CAACG,aAAa,KAAK,QAAQ,GAAG,KAAK,CAAC,IAAI,CAACL,MAAM,KAAKE,GAAG,CAACI,cAAc,GAAGJ,GAAG,CAACI,cAAc,KAAK,MAAM,GAAG,KAAK,CAAC,IAAI,CAACN,MAAM,KAAKE,GAAG,CAACK,MAAM,GAAGL,GAAG,CAACK,MAAM,KAAK,MAAM,GAAG,KAAK,CAAC,IAAI,CAAC,WAAW,EAAE,aAAa,EAAE,QAAQ,CAAC,CAACb,IAAI,CAAC,UAAApB,KAAK;IAAA,OAAI,CAAC4B,GAAG,CAACM,UAAU,IAAI,EAAE,EAAEjB,QAAQ,CAACjB,KAAK,CAAC;EAAA,EAAC,IAAI,CAAC,OAAO,EAAE,QAAQ,EAAE,QAAQ,EAAE,SAAS,CAAC,CAACoB,IAAI,CAAC,UAAApB,KAAK;IAAA,OAAI,CAAC4B,GAAG,CAACO,OAAO,IAAI,EAAE,EAAElB,QAAQ,CAACjB,KAAK,CAAC;EAAA,EAAC;AACpc;AACA,SAASoC,kBAAkBA,CAAC3B,OAAO,EAAE;EACnC,IAAI4B,WAAW,GAAGC,aAAa,CAAC7B,OAAO,CAAC;EACxC,OAAOL,aAAa,CAACiC,WAAW,CAAC,IAAI,CAACE,qBAAqB,CAACF,WAAW,CAAC,EAAE;IACxE,IAAIb,iBAAiB,CAACa,WAAW,CAAC,EAAE;MAClC,OAAOA,WAAW;IACpB,CAAC,MAAM,IAAIlB,UAAU,CAACkB,WAAW,CAAC,EAAE;MAClC,OAAO,IAAI;IACb;IACAA,WAAW,GAAGC,aAAa,CAACD,WAAW,CAAC;EAC1C;EACA,OAAO,IAAI;AACb;AACA,SAASV,QAAQA,CAAA,EAAG;EAClB,IAAI,OAAOa,GAAG,KAAK,WAAW,IAAI,CAACA,GAAG,CAACC,QAAQ,EAAE,OAAO,KAAK;EAC7D,OAAOD,GAAG,CAACC,QAAQ,CAAC,yBAAyB,EAAE,MAAM,CAAC;AACxD;AACA,SAASF,qBAAqBA,CAACnD,IAAI,EAAE;EACnC,OAAO,CAAC,MAAM,EAAE,MAAM,EAAE,WAAW,CAAC,CAAC6B,QAAQ,CAAC9B,WAAW,CAACC,IAAI,CAAC,CAAC;AAClE;AACA,SAASuB,gBAAgBA,CAACF,OAAO,EAAE;EACjC,OAAOjB,SAAS,CAACiB,OAAO,CAAC,CAACE,gBAAgB,CAACF,OAAO,CAAC;AACrD;AACA,SAASiC,aAAaA,CAACjC,OAAO,EAAE;EAC9B,IAAIP,SAAS,CAACO,OAAO,CAAC,EAAE;IACtB,OAAO;MACLkC,UAAU,EAAElC,OAAO,CAACkC,UAAU;MAC9BC,SAAS,EAAEnC,OAAO,CAACmC;IACrB,CAAC;EACH;EACA,OAAO;IACLD,UAAU,EAAElC,OAAO,CAACoC,OAAO;IAC3BD,SAAS,EAAEnC,OAAO,CAACqC;EACrB,CAAC;AACH;AACA,SAASR,aAAaA,CAAClD,IAAI,EAAE;EAC3B,IAAID,WAAW,CAACC,IAAI,CAAC,KAAK,MAAM,EAAE;IAChC,OAAOA,IAAI;EACb;EACA,IAAM2D,MAAM;EACZ;EACA3D,IAAI,CAAC4D,YAAY;EACjB;EACA5D,IAAI,CAAC6D,UAAU;EACf;EACA3C,YAAY,CAAClB,IAAI,CAAC,IAAIA,IAAI,CAAC8D,IAAI;EAC/B;EACAtD,kBAAkB,CAACR,IAAI,CAAC;EACxB,OAAOkB,YAAY,CAACyC,MAAM,CAAC,GAAGA,MAAM,CAACG,IAAI,GAAGH,MAAM;AACpD;AACA,SAASI,0BAA0BA,CAAC/D,IAAI,EAAE;EACxC,IAAM6D,UAAU,GAAGX,aAAa,CAAClD,IAAI,CAAC;EACtC,IAAImD,qBAAqB,CAACU,UAAU,CAAC,EAAE;IACrC,OAAO7D,IAAI,CAACM,aAAa,GAAGN,IAAI,CAACM,aAAa,CAAC0D,IAAI,GAAGhE,IAAI,CAACgE,IAAI;EACjE;EACA,IAAIhD,aAAa,CAAC6C,UAAU,CAAC,IAAIzC,iBAAiB,CAACyC,UAAU,CAAC,EAAE;IAC9D,OAAOA,UAAU;EACnB;EACA,OAAOE,0BAA0B,CAACF,UAAU,CAAC;AAC/C;AACA,SAASI,oBAAoBA,CAACjE,IAAI,EAAEkE,IAAI,EAAEC,eAAe,EAAE;EACzD,IAAIC,oBAAoB;EACxB,IAAIF,IAAI,KAAK,KAAK,CAAC,EAAE;IACnBA,IAAI,GAAG,EAAE;EACX;EACA,IAAIC,eAAe,KAAK,KAAK,CAAC,EAAE;IAC9BA,eAAe,GAAG,IAAI;EACxB;EACA,IAAME,kBAAkB,GAAGN,0BAA0B,CAAC/D,IAAI,CAAC;EAC3D,IAAMsE,MAAM,GAAGD,kBAAkB,MAAM,CAACD,oBAAoB,GAAGpE,IAAI,CAACM,aAAa,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG8D,oBAAoB,CAACJ,IAAI,CAAC;EAChI,IAAMO,GAAG,GAAGnE,SAAS,CAACiE,kBAAkB,CAAC;EACzC,IAAIC,MAAM,EAAE;IACV,IAAME,YAAY,GAAGC,eAAe,CAACF,GAAG,CAAC;IACzC,OAAOL,IAAI,CAACQ,MAAM,CAACH,GAAG,EAAEA,GAAG,CAACI,cAAc,IAAI,EAAE,EAAEvD,iBAAiB,CAACiD,kBAAkB,CAAC,GAAGA,kBAAkB,GAAG,EAAE,EAAEG,YAAY,IAAIL,eAAe,GAAGF,oBAAoB,CAACO,YAAY,CAAC,GAAG,EAAE,CAAC;EAC/L;EACA,OAAON,IAAI,CAACQ,MAAM,CAACL,kBAAkB,EAAEJ,oBAAoB,CAACI,kBAAkB,EAAE,EAAE,EAAEF,eAAe,CAAC,CAAC;AACvG;AACA,SAASM,eAAeA,CAACF,GAAG,EAAE;EAC5B,OAAOA,GAAG,CAACK,MAAM,IAAIC,MAAM,CAACC,cAAc,CAACP,GAAG,CAACK,MAAM,CAAC,GAAGL,GAAG,CAACC,YAAY,GAAG,IAAI;AAClF;AAEA,SAASjD,gBAAgB,EAAEyB,kBAAkB,EAAExC,kBAAkB,EAAEiE,eAAe,EAAEV,0BAA0B,EAAEhE,WAAW,EAAEuD,aAAa,EAAEW,oBAAoB,EAAEf,aAAa,EAAE9C,SAAS,EAAEgC,iBAAiB,EAAEtB,SAAS,EAAEE,aAAa,EAAEmC,qBAAqB,EAAElD,MAAM,EAAEmB,iBAAiB,EAAEF,YAAY,EAAEY,cAAc,EAAEC,UAAU,EAAEQ,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
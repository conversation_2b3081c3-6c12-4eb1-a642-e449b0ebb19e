{"ast": null, "code": "import { resolveComponent as _resolveComponent, createVNode as _createVNode, withCtx as _withCtx, createTextVNode as _createTextVNode, createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock } from \"vue\";\nvar _hoisted_1 = {\n  class: \"CircleOfFriendsNew\"\n};\nvar _hoisted_2 = {\n  class: \"globalFormButton\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  var _component_el_input = _resolveComponent(\"el-input\");\n  var _component_el_form_item = _resolveComponent(\"el-form-item\");\n  var _component_xyl_upload_img = _resolveComponent(\"xyl-upload-img\");\n  var _component_el_button = _resolveComponent(\"el-button\");\n  var _component_el_form = _resolveComponent(\"el-form\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createVNode(_component_el_form, {\n    ref: \"formRef\",\n    model: $setup.form,\n    rules: $setup.rules,\n    inline: \"\",\n    \"label-position\": \"top\",\n    class: \"globalForm\"\n  }, {\n    default: _withCtx(function () {\n      return [_createVNode(_component_el_form_item, {\n        label: \"内容\",\n        prop: \"content\",\n        class: \"globalFormTitle\"\n      }, {\n        default: _withCtx(function () {\n          return [_createVNode(_component_el_input, {\n            modelValue: $setup.form.content,\n            \"onUpdate:modelValue\": _cache[0] || (_cache[0] = function ($event) {\n              return $setup.form.content = $event;\n            }),\n            type: \"textarea\",\n            placeholder: \"请输入内容\",\n            rows: \"6\"\n          }, null, 8 /* PROPS */, [\"modelValue\"])];\n        }),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_form_item, {\n        label: \"照片\",\n        class: \"globalFormTitle\"\n      }, {\n        default: _withCtx(function () {\n          return [_createVNode(_component_xyl_upload_img, {\n            onFileUpload: $setup.fileUpload,\n            fileData: $setup.fileData\n          }, null, 8 /* PROPS */, [\"fileData\"])];\n        }),\n        _: 1 /* STABLE */\n      }), _createElementVNode(\"div\", _hoisted_2, [_createVNode(_component_el_button, {\n        type: \"primary\",\n        onClick: _cache[1] || (_cache[1] = function ($event) {\n          return $setup.submitForm($setup.formRef);\n        })\n      }, {\n        default: _withCtx(function () {\n          return _cache[2] || (_cache[2] = [_createTextVNode(\"提交\")]);\n        }),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_button, {\n        onClick: $setup.resetForm\n      }, {\n        default: _withCtx(function () {\n          return _cache[3] || (_cache[3] = [_createTextVNode(\"取消\")]);\n        }),\n        _: 1 /* STABLE */\n      })])];\n    }),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"model\", \"rules\"])]);\n}", "map": {"version": 3, "names": ["class", "_createElementBlock", "_hoisted_1", "_createVNode", "_component_el_form", "ref", "model", "$setup", "form", "rules", "inline", "default", "_withCtx", "_component_el_form_item", "label", "prop", "_component_el_input", "modelValue", "content", "_cache", "$event", "type", "placeholder", "rows", "_", "_component_xyl_upload_img", "onFileUpload", "fileUpload", "fileData", "_createElementVNode", "_hoisted_2", "_component_el_button", "onClick", "submitForm", "formRef", "_createTextVNode", "resetForm"], "sources": ["D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\interaction\\src\\views\\CircleOfFriends\\CircleOfFriendsNew.vue"], "sourcesContent": ["<template>\r\n  <div class=\"CircleOfFriendsNew\">\r\n    <el-form ref=\"formRef\"\r\n             :model=\"form\"\r\n             :rules=\"rules\"\r\n             inline\r\n             label-position=\"top\"\r\n             class=\"globalForm\">\r\n      <el-form-item label=\"内容\"\r\n                    prop=\"content\"\r\n                    class=\"globalFormTitle\">\r\n        <el-input v-model=\"form.content\"\r\n                  type=\"textarea\"\r\n                  placeholder=\"请输入内容\"\r\n                  rows=\"6\" />\r\n      </el-form-item>\r\n      <el-form-item label=\"照片\"\r\n                    class=\"globalFormTitle\">\r\n        <xyl-upload-img @fileUpload=\"fileUpload\"\r\n                        :fileData=\"fileData\"></xyl-upload-img>\r\n      </el-form-item>\r\n      <div class=\"globalFormButton\">\r\n        <el-button type=\"primary\"\r\n                   @click=\"submitForm(formRef)\">提交</el-button>\r\n        <el-button @click=\"resetForm\">取消</el-button>\r\n      </div>\r\n    </el-form>\r\n  </div>\r\n</template>\r\n<script>\r\nexport default { name: 'CircleOfFriendsNew' }\r\n</script>\r\n<script setup>\r\nimport api from '@/api'\r\nimport { reactive, ref, onMounted } from 'vue'\r\nimport { user } from 'common/js/system_var.js'\r\nimport { ElMessage } from 'element-plus'\r\nconst props = defineProps({ id: { type: String, default: '' } })\r\nconst emit = defineEmits(['callback'])\r\n\r\nconst formRef = ref()\r\nconst form = reactive({\r\n  accountId: '', // 用户id\r\n  content: '' // 内容\r\n})\r\nconst rules = reactive({ content: [{ required: true, message: '请输入内容', trigger: ['blur', 'change'] }] })\r\nconst fileData = ref([])\r\nonMounted(() => { if (props.id) { styleCircleInfo() } })\r\n\r\nconst styleCircleInfo = async () => {\r\n  const res = await api.styleCircleInfo({ detailId: props.id })\r\n  var { data } = res\r\n  form.accountId = data.accountId // 用户id\r\n  form.content = data.content // 内容\r\n  form.content = data.content // 内容\r\n  fileData.value = data.imgs\r\n}\r\nconst fileUpload = (file) => {\r\n  fileData.value = file.map(v => v.newFileName)\r\n}\r\nconst submitForm = async (formEl) => {\r\n  if (!formEl) return\r\n  await formEl.validate((valid, fields) => {\r\n    if (valid) { globalJson() } else { ElMessage({ type: 'warning', message: '请根据提示信息完善字段内容！' }) }\r\n  })\r\n}\r\nconst globalJson = async () => {\r\n  const { code } = await api.globalJson(props.id ? '/styleCircle/edit' : '/styleCircle/add', {\r\n    form: {\r\n      id: props.id,\r\n      accountId: form.accountId || user.value.accountId, // 用户id\r\n      content: form.content,\r\n      imgs: fileData.value\r\n    }\r\n  })\r\n  if (code === 200) {\r\n    ElMessage({ type: 'success', message: props.id ? '编辑成功' : '新增成功' })\r\n    emit('callback')\r\n  }\r\n}\r\nconst resetForm = () => { emit('callback') }\r\n</script>\r\n<style lang=\"scss\">\r\n.CircleOfFriendsNew {\r\n  width: 680px;\r\n}\r\n</style>\r\n"], "mappings": ";;EACOA,KAAK,EAAC;AAAoB;;EAoBtBA,KAAK,EAAC;AAAkB;;;;;;;uBApBjCC,mBAAA,CA0BM,OA1BNC,UA0BM,GAzBJC,YAAA,CAwBUC,kBAAA;IAxBDC,GAAG,EAAC,SAAS;IACZC,KAAK,EAAEC,MAAA,CAAAC,IAAI;IACXC,KAAK,EAAEF,MAAA,CAAAE,KAAK;IACbC,MAAM,EAAN,EAAM;IACN,gBAAc,EAAC,KAAK;IACpBV,KAAK,EAAC;;IAPnBW,OAAA,EAAAC,QAAA,CAQM;MAAA,OAOe,CAPfT,YAAA,CAOeU,uBAAA;QAPDC,KAAK,EAAC,IAAI;QACVC,IAAI,EAAC,SAAS;QACdf,KAAK,EAAC;;QAV1BW,OAAA,EAAAC,QAAA,CAWQ;UAAA,OAGqB,CAHrBT,YAAA,CAGqBa,mBAAA;YAd7BC,UAAA,EAW2BV,MAAA,CAAAC,IAAI,CAACU,OAAO;YAXvC,uBAAAC,MAAA,QAAAA,MAAA,gBAAAC,MAAA;cAAA,OAW2Bb,MAAA,CAAAC,IAAI,CAACU,OAAO,GAAAE,MAAA;YAAA;YACrBC,IAAI,EAAC,UAAU;YACfC,WAAW,EAAC,OAAO;YACnBC,IAAI,EAAC;;;QAdvBC,CAAA;UAgBMrB,YAAA,CAIeU,uBAAA;QAJDC,KAAK,EAAC,IAAI;QACVd,KAAK,EAAC;;QAjB1BW,OAAA,EAAAC,QAAA,CAkBQ;UAAA,OACsD,CADtDT,YAAA,CACsDsB,yBAAA;YADrCC,YAAU,EAAEnB,MAAA,CAAAoB,UAAU;YACtBC,QAAQ,EAAErB,MAAA,CAAAqB;;;QAnBnCJ,CAAA;UAqBMK,mBAAA,CAIM,OAJNC,UAIM,GAHJ3B,YAAA,CACsD4B,oBAAA;QAD3CV,IAAI,EAAC,SAAS;QACbW,OAAK,EAAAb,MAAA,QAAAA,MAAA,gBAAAC,MAAA;UAAA,OAAEb,MAAA,CAAA0B,UAAU,CAAC1B,MAAA,CAAA2B,OAAO;QAAA;;QAvB7CvB,OAAA,EAAAC,QAAA,CAuBgD;UAAA,OAAEO,MAAA,QAAAA,MAAA,OAvBlDgB,gBAAA,CAuBgD,IAAE,E;;QAvBlDX,CAAA;UAwBQrB,YAAA,CAA4C4B,oBAAA;QAAhCC,OAAK,EAAEzB,MAAA,CAAA6B;MAAS;QAxBpCzB,OAAA,EAAAC,QAAA,CAwBsC;UAAA,OAAEO,MAAA,QAAAA,MAAA,OAxBxCgB,gBAAA,CAwBsC,IAAE,E;;QAxBxCX,CAAA;;;IAAAA,CAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
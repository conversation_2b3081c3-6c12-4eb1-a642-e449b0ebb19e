{"ast": null, "code": "import createWrap from './_createWrap.js';\nimport flatRest from './_flatRest.js';\n\n/** Used to compose bitmasks for function metadata. */\nvar WRAP_REARG_FLAG = 256;\n\n/**\n * Creates a function that invokes `func` with arguments arranged according\n * to the specified `indexes` where the argument value at the first index is\n * provided as the first argument, the argument value at the second index is\n * provided as the second argument, and so on.\n *\n * @static\n * @memberOf _\n * @since 3.0.0\n * @category Function\n * @param {Function} func The function to rearrange arguments for.\n * @param {...(number|number[])} indexes The arranged argument indexes.\n * @returns {Function} Returns the new function.\n * @example\n *\n * var rearged = _.rearg(function(a, b, c) {\n *   return [a, b, c];\n * }, [2, 0, 1]);\n *\n * rearged('b', 'c', 'a')\n * // => ['a', 'b', 'c']\n */\nvar rearg = flatRest(function (func, indexes) {\n  return createWrap(func, WRAP_REARG_FLAG, undefined, undefined, undefined, indexes);\n});\nexport default rearg;", "map": {"version": 3, "names": ["createWrap", "flatRest", "WRAP_REARG_FLAG", "rearg", "func", "indexes", "undefined"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/node_modules/.pnpm/lodash-es@4.17.21/node_modules/lodash-es/rearg.js"], "sourcesContent": ["import createWrap from './_createWrap.js';\nimport flatRest from './_flatRest.js';\n\n/** Used to compose bitmasks for function metadata. */\nvar WRAP_REARG_FLAG = 256;\n\n/**\n * Creates a function that invokes `func` with arguments arranged according\n * to the specified `indexes` where the argument value at the first index is\n * provided as the first argument, the argument value at the second index is\n * provided as the second argument, and so on.\n *\n * @static\n * @memberOf _\n * @since 3.0.0\n * @category Function\n * @param {Function} func The function to rearrange arguments for.\n * @param {...(number|number[])} indexes The arranged argument indexes.\n * @returns {Function} Returns the new function.\n * @example\n *\n * var rearged = _.rearg(function(a, b, c) {\n *   return [a, b, c];\n * }, [2, 0, 1]);\n *\n * rearged('b', 'c', 'a')\n * // => ['a', 'b', 'c']\n */\nvar rearg = flatRest(function(func, indexes) {\n  return createWrap(func, WRAP_REARG_FLAG, undefined, undefined, undefined, indexes);\n});\n\nexport default rearg;\n"], "mappings": "AAAA,OAAOA,UAAU,MAAM,kBAAkB;AACzC,OAAOC,QAAQ,MAAM,gBAAgB;;AAErC;AACA,IAAIC,eAAe,GAAG,GAAG;;AAEzB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIC,KAAK,GAAGF,QAAQ,CAAC,UAASG,IAAI,EAAEC,OAAO,EAAE;EAC3C,OAAOL,UAAU,CAACI,IAAI,EAAEF,eAAe,EAAEI,SAAS,EAAEA,SAAS,EAAEA,SAAS,EAAED,OAAO,CAAC;AACpF,CAAC,CAAC;AAEF,eAAeF,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
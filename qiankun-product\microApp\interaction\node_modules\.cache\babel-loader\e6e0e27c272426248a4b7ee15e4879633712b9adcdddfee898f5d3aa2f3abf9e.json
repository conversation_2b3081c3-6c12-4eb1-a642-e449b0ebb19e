{"ast": null, "code": "function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nfunction _defineProperty(e, r, t) { return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, { value: t, enumerable: !0, configurable: !0, writable: !0 }) : e[r] = t, e; }\nfunction _toPropertyKey(t) { var i = _toPrimitive(t, \"string\"); return \"symbol\" == typeof i ? i : i + \"\"; }\nfunction _toPrimitive(t, r) { if (\"object\" != typeof t || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || \"default\"); if (\"object\" != typeof i) return i; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (\"string\" === r ? String : Number)(t); }\nimport { computed } from 'vue';\nimport { get } from 'lodash-unified';\nvar defaultProps = {\n  label: \"label\",\n  value: \"value\",\n  disabled: \"disabled\",\n  options: \"options\"\n};\nfunction useProps(props) {\n  var aliasProps = computed(function () {\n    return _objectSpread(_objectSpread({}, defaultProps), props.props);\n  });\n  var getLabel = function getLabel(option) {\n    return get(option, aliasProps.value.label);\n  };\n  var getValue = function getValue(option) {\n    return get(option, aliasProps.value.value);\n  };\n  var getDisabled = function getDisabled(option) {\n    return get(option, aliasProps.value.disabled);\n  };\n  var getOptions = function getOptions(option) {\n    return get(option, aliasProps.value.options);\n  };\n  return {\n    aliasProps,\n    getLabel,\n    getValue,\n    getDisabled,\n    getOptions\n  };\n}\nexport { defaultProps, useProps };", "map": {"version": 3, "names": ["defaultProps", "label", "value", "disabled", "options", "useProps", "props", "aliasProps", "computed", "_objectSpread", "get<PERSON><PERSON><PERSON>", "option", "get", "getValue", "getDisabled", "getOptions"], "sources": ["../../../../../../packages/components/select-v2/src/useProps.ts"], "sourcesContent": ["import { computed } from 'vue'\nimport { get } from 'lodash-unified'\n\nimport type { ISelectV2Props } from './token'\nimport type { Option } from './select.types'\n\nexport interface Props {\n  label?: string\n  value?: string\n  disabled?: string\n  options?: string\n}\n\nexport const defaultProps: Required<Props> = {\n  label: 'label',\n  value: 'value',\n  disabled: 'disabled',\n  options: 'options',\n}\n\nexport function useProps(props: Pick<ISelectV2Props, 'props'>) {\n  const aliasProps = computed(() => ({ ...defaultProps, ...props.props }))\n\n  const getLabel = (option: Option) => get(option, aliasProps.value.label)\n  const getValue = (option: Option) => get(option, aliasProps.value.value)\n  const getDisabled = (option: Option) => get(option, aliasProps.value.disabled)\n  const getOptions = (option: Option) => get(option, aliasProps.value.options)\n\n  return {\n    aliasProps,\n    getLabel,\n    getValue,\n    getDisabled,\n    getOptions,\n  }\n}\n"], "mappings": ";;;;;;;AAEY,IAACA,YAAY,GAAG;EAC1BC,KAAK,EAAE,OAAO;EACdC,KAAK,EAAE,OAAO;EACdC,QAAQ,EAAE,UAAU;EACpBC,OAAO,EAAE;AACX;AACO,SAASC,QAAQA,CAACC,KAAK,EAAE;EAC9B,IAAMC,UAAU,GAAGC,QAAQ,CAAC;IAAA,OAAAC,aAAA,CAAAA,aAAA,KAAYT,YAAY,GAAKM,KAAK,CAACA,KAAK;EAAA,CAAG,CAAC;EACxE,IAAMI,QAAQ,GAAG,SAAXA,QAAQA,CAAIC,MAAM;IAAA,OAAKC,GAAG,CAACD,MAAM,EAAEJ,UAAU,CAACL,KAAK,CAACD,KAAK,CAAC;EAAA;EAChE,IAAMY,QAAQ,GAAG,SAAXA,QAAQA,CAAIF,MAAM;IAAA,OAAKC,GAAG,CAACD,MAAM,EAAEJ,UAAU,CAACL,KAAK,CAACA,KAAK,CAAC;EAAA;EAChE,IAAMY,WAAW,GAAG,SAAdA,WAAWA,CAAIH,MAAM;IAAA,OAAKC,GAAG,CAACD,MAAM,EAAEJ,UAAU,CAACL,KAAK,CAACC,QAAQ,CAAC;EAAA;EACtE,IAAMY,UAAU,GAAG,SAAbA,UAAUA,CAAIJ,MAAM;IAAA,OAAKC,GAAG,CAACD,MAAM,EAAEJ,UAAU,CAACL,KAAK,CAACE,OAAO,CAAC;EAAA;EACpE,OAAO;IACLG,UAAU;IACVG,QAAQ;IACRG,QAAQ;IACRC,WAAW;IACXC;EACJ,CAAG;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "import coreJsData from './_coreJsData.js';\n\n/** Used to detect methods masquerading as native. */\nvar maskSrcKey = function () {\n  var uid = /[^.]+$/.exec(coreJsData && coreJsData.keys && coreJsData.keys.IE_PROTO || '');\n  return uid ? 'Symbol(src)_1.' + uid : '';\n}();\n\n/**\n * Checks if `func` has its source masked.\n *\n * @private\n * @param {Function} func The function to check.\n * @returns {boolean} Returns `true` if `func` is masked, else `false`.\n */\nfunction isMasked(func) {\n  return !!maskSrcKey && maskSrcKey in func;\n}\nexport default isMasked;", "map": {"version": 3, "names": ["coreJsData", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "uid", "exec", "keys", "IE_PROTO", "isMasked", "func"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/node_modules/.pnpm/lodash-es@4.17.21/node_modules/lodash-es/_isMasked.js"], "sourcesContent": ["import coreJsData from './_coreJsData.js';\n\n/** Used to detect methods masquerading as native. */\nvar maskSrcKey = (function() {\n  var uid = /[^.]+$/.exec(coreJsData && coreJsData.keys && coreJsData.keys.IE_PROTO || '');\n  return uid ? ('Symbol(src)_1.' + uid) : '';\n}());\n\n/**\n * Checks if `func` has its source masked.\n *\n * @private\n * @param {Function} func The function to check.\n * @returns {boolean} Returns `true` if `func` is masked, else `false`.\n */\nfunction isMasked(func) {\n  return !!maskSrcKey && (maskSrcKey in func);\n}\n\nexport default isMasked;\n"], "mappings": "AAAA,OAAOA,UAAU,MAAM,kBAAkB;;AAEzC;AACA,IAAIC,UAAU,GAAI,YAAW;EAC3B,IAAIC,GAAG,GAAG,QAAQ,CAACC,IAAI,CAACH,UAAU,IAAIA,UAAU,CAACI,IAAI,IAAIJ,UAAU,CAACI,IAAI,CAACC,QAAQ,IAAI,EAAE,CAAC;EACxF,OAAOH,GAAG,GAAI,gBAAgB,GAAGA,GAAG,GAAI,EAAE;AAC5C,CAAC,CAAC,CAAE;;AAEJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASI,QAAQA,CAACC,IAAI,EAAE;EACtB,OAAO,CAAC,CAACN,UAAU,IAAKA,UAAU,IAAIM,IAAK;AAC7C;AAEA,eAAeD,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
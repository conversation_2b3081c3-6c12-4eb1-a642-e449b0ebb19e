{"ast": null, "code": "import ElVisuallyHidden from './src/visual-hidden.mjs';\nexport { default as ElVisuallyHidden, default } from './src/visual-hidden.mjs';\nexport { visualHiddenProps } from './src/visual-hidden2.mjs';", "map": {"version": 3, "names": [], "sources": [], "sourcesContent": ["import ElVisuallyHidden from './src/visual-hidden.mjs';\nexport { default as ElVisuallyHidden, default } from './src/visual-hidden.mjs';\nexport { visualHiddenProps } from './src/visual-hidden2.mjs';\n//# sourceMappingURL=index.mjs.map\n"], "mappings": "", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
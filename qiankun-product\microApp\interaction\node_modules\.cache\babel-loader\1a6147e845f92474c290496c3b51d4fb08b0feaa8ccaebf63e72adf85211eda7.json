{"ast": null, "code": "import '../../../utils/index.mjs';\nimport { buildProps, definePropType } from '../../../utils/vue/props/runtime.mjs';\nvar descriptionsRowProps = buildProps({\n  row: {\n    type: definePropType(Array),\n    default: function _default() {\n      return [];\n    }\n  }\n});\nexport { descriptionsRowProps };", "map": {"version": 3, "names": ["descriptionsRowProps", "buildProps", "row", "type", "definePropType", "Array", "default"], "sources": ["../../../../../../packages/components/descriptions/src/descriptions-row.ts"], "sourcesContent": ["import { buildProps, definePropType } from '@element-plus/utils'\n\nimport type { DescriptionItemVNode } from './description-item'\n\nexport const descriptionsRowProps = buildProps({\n  row: {\n    type: definePropType<DescriptionItemVNode[]>(Array),\n    default: () => [],\n  },\n} as const)\n"], "mappings": ";;AACY,IAACA,oBAAoB,GAAGC,UAAU,CAAC;EAC7CC,GAAG,EAAE;IACHC,IAAI,EAAEC,cAAc,CAACC,KAAK,CAAC;IAC3BC,OAAO,EAAE,SAATA,QAAOA,CAAA;MAAA,OAAQ,EAAE;IAAA;EACrB;AACA,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
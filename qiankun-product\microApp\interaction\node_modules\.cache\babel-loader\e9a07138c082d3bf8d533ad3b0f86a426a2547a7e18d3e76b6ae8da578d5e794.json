{"ast": null, "code": "import isSymbol from './isSymbol.js';\n\n/** Used as references for the maximum length and index of an array. */\nvar MAX_ARRAY_LENGTH = 4294967295,\n  MAX_ARRAY_INDEX = MAX_ARRAY_LENGTH - 1;\n\n/* Built-in method references for those with the same name as other `lodash` methods. */\nvar nativeFloor = Math.floor,\n  nativeMin = Math.min;\n\n/**\n * The base implementation of `_.sortedIndexBy` and `_.sortedLastIndexBy`\n * which invokes `iteratee` for `value` and each element of `array` to compute\n * their sort ranking. The iteratee is invoked with one argument; (value).\n *\n * @private\n * @param {Array} array The sorted array to inspect.\n * @param {*} value The value to evaluate.\n * @param {Function} iteratee The iteratee invoked per element.\n * @param {boolean} [retHighest] Specify returning the highest qualified index.\n * @returns {number} Returns the index at which `value` should be inserted\n *  into `array`.\n */\nfunction baseSortedIndexBy(array, value, iteratee, retHighest) {\n  var low = 0,\n    high = array == null ? 0 : array.length;\n  if (high === 0) {\n    return 0;\n  }\n  value = iteratee(value);\n  var valIsNaN = value !== value,\n    valIsNull = value === null,\n    valIsSymbol = isSymbol(value),\n    valIsUndefined = value === undefined;\n  while (low < high) {\n    var mid = nativeFloor((low + high) / 2),\n      computed = iteratee(array[mid]),\n      othIsDefined = computed !== undefined,\n      othIsNull = computed === null,\n      othIsReflexive = computed === computed,\n      othIsSymbol = isSymbol(computed);\n    if (valIsNaN) {\n      var setLow = retHighest || othIsReflexive;\n    } else if (valIsUndefined) {\n      setLow = othIsReflexive && (retHighest || othIsDefined);\n    } else if (valIsNull) {\n      setLow = othIsReflexive && othIsDefined && (retHighest || !othIsNull);\n    } else if (valIsSymbol) {\n      setLow = othIsReflexive && othIsDefined && !othIsNull && (retHighest || !othIsSymbol);\n    } else if (othIsNull || othIsSymbol) {\n      setLow = false;\n    } else {\n      setLow = retHighest ? computed <= value : computed < value;\n    }\n    if (setLow) {\n      low = mid + 1;\n    } else {\n      high = mid;\n    }\n  }\n  return nativeMin(high, MAX_ARRAY_INDEX);\n}\nexport default baseSortedIndexBy;", "map": {"version": 3, "names": ["isSymbol", "MAX_ARRAY_LENGTH", "MAX_ARRAY_INDEX", "nativeFloor", "Math", "floor", "nativeMin", "min", "baseSortedIndexBy", "array", "value", "iteratee", "retHighest", "low", "high", "length", "valIsNaN", "valIsNull", "valIsSymbol", "valIsUndefined", "undefined", "mid", "computed", "othIsDefined", "othIsNull", "othIsReflexive", "othIsSymbol", "setLow"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/node_modules/.pnpm/lodash-es@4.17.21/node_modules/lodash-es/_baseSortedIndexBy.js"], "sourcesContent": ["import isSymbol from './isSymbol.js';\n\n/** Used as references for the maximum length and index of an array. */\nvar MAX_ARRAY_LENGTH = 4294967295,\n    MAX_ARRAY_INDEX = MAX_ARRAY_LENGTH - 1;\n\n/* Built-in method references for those with the same name as other `lodash` methods. */\nvar nativeFloor = Math.floor,\n    nativeMin = Math.min;\n\n/**\n * The base implementation of `_.sortedIndexBy` and `_.sortedLastIndexBy`\n * which invokes `iteratee` for `value` and each element of `array` to compute\n * their sort ranking. The iteratee is invoked with one argument; (value).\n *\n * @private\n * @param {Array} array The sorted array to inspect.\n * @param {*} value The value to evaluate.\n * @param {Function} iteratee The iteratee invoked per element.\n * @param {boolean} [retHighest] Specify returning the highest qualified index.\n * @returns {number} Returns the index at which `value` should be inserted\n *  into `array`.\n */\nfunction baseSortedIndexBy(array, value, iteratee, retHighest) {\n  var low = 0,\n      high = array == null ? 0 : array.length;\n  if (high === 0) {\n    return 0;\n  }\n\n  value = iteratee(value);\n  var valIsNaN = value !== value,\n      valIsNull = value === null,\n      valIsSymbol = isSymbol(value),\n      valIsUndefined = value === undefined;\n\n  while (low < high) {\n    var mid = nativeFloor((low + high) / 2),\n        computed = iteratee(array[mid]),\n        othIsDefined = computed !== undefined,\n        othIsNull = computed === null,\n        othIsReflexive = computed === computed,\n        othIsSymbol = isSymbol(computed);\n\n    if (valIsNaN) {\n      var setLow = retHighest || othIsReflexive;\n    } else if (valIsUndefined) {\n      setLow = othIsReflexive && (retHighest || othIsDefined);\n    } else if (valIsNull) {\n      setLow = othIsReflexive && othIsDefined && (retHighest || !othIsNull);\n    } else if (valIsSymbol) {\n      setLow = othIsReflexive && othIsDefined && !othIsNull && (retHighest || !othIsSymbol);\n    } else if (othIsNull || othIsSymbol) {\n      setLow = false;\n    } else {\n      setLow = retHighest ? (computed <= value) : (computed < value);\n    }\n    if (setLow) {\n      low = mid + 1;\n    } else {\n      high = mid;\n    }\n  }\n  return nativeMin(high, MAX_ARRAY_INDEX);\n}\n\nexport default baseSortedIndexBy;\n"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,eAAe;;AAEpC;AACA,IAAIC,gBAAgB,GAAG,UAAU;EAC7BC,eAAe,GAAGD,gBAAgB,GAAG,CAAC;;AAE1C;AACA,IAAIE,WAAW,GAAGC,IAAI,CAACC,KAAK;EACxBC,SAAS,GAAGF,IAAI,CAACG,GAAG;;AAExB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,iBAAiBA,CAACC,KAAK,EAAEC,KAAK,EAAEC,QAAQ,EAAEC,UAAU,EAAE;EAC7D,IAAIC,GAAG,GAAG,CAAC;IACPC,IAAI,GAAGL,KAAK,IAAI,IAAI,GAAG,CAAC,GAAGA,KAAK,CAACM,MAAM;EAC3C,IAAID,IAAI,KAAK,CAAC,EAAE;IACd,OAAO,CAAC;EACV;EAEAJ,KAAK,GAAGC,QAAQ,CAACD,KAAK,CAAC;EACvB,IAAIM,QAAQ,GAAGN,KAAK,KAAKA,KAAK;IAC1BO,SAAS,GAAGP,KAAK,KAAK,IAAI;IAC1BQ,WAAW,GAAGlB,QAAQ,CAACU,KAAK,CAAC;IAC7BS,cAAc,GAAGT,KAAK,KAAKU,SAAS;EAExC,OAAOP,GAAG,GAAGC,IAAI,EAAE;IACjB,IAAIO,GAAG,GAAGlB,WAAW,CAAC,CAACU,GAAG,GAAGC,IAAI,IAAI,CAAC,CAAC;MACnCQ,QAAQ,GAAGX,QAAQ,CAACF,KAAK,CAACY,GAAG,CAAC,CAAC;MAC/BE,YAAY,GAAGD,QAAQ,KAAKF,SAAS;MACrCI,SAAS,GAAGF,QAAQ,KAAK,IAAI;MAC7BG,cAAc,GAAGH,QAAQ,KAAKA,QAAQ;MACtCI,WAAW,GAAG1B,QAAQ,CAACsB,QAAQ,CAAC;IAEpC,IAAIN,QAAQ,EAAE;MACZ,IAAIW,MAAM,GAAGf,UAAU,IAAIa,cAAc;IAC3C,CAAC,MAAM,IAAIN,cAAc,EAAE;MACzBQ,MAAM,GAAGF,cAAc,KAAKb,UAAU,IAAIW,YAAY,CAAC;IACzD,CAAC,MAAM,IAAIN,SAAS,EAAE;MACpBU,MAAM,GAAGF,cAAc,IAAIF,YAAY,KAAKX,UAAU,IAAI,CAACY,SAAS,CAAC;IACvE,CAAC,MAAM,IAAIN,WAAW,EAAE;MACtBS,MAAM,GAAGF,cAAc,IAAIF,YAAY,IAAI,CAACC,SAAS,KAAKZ,UAAU,IAAI,CAACc,WAAW,CAAC;IACvF,CAAC,MAAM,IAAIF,SAAS,IAAIE,WAAW,EAAE;MACnCC,MAAM,GAAG,KAAK;IAChB,CAAC,MAAM;MACLA,MAAM,GAAGf,UAAU,GAAIU,QAAQ,IAAIZ,KAAK,GAAKY,QAAQ,GAAGZ,KAAM;IAChE;IACA,IAAIiB,MAAM,EAAE;MACVd,GAAG,GAAGQ,GAAG,GAAG,CAAC;IACf,CAAC,MAAM;MACLP,IAAI,GAAGO,GAAG;IACZ;EACF;EACA,OAAOf,SAAS,CAACQ,IAAI,EAAEZ,eAAe,CAAC;AACzC;AAEA,eAAeM,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
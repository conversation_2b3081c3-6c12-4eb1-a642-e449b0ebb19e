{"ast": null, "code": "import baseDelay from './_baseDelay.js';\nimport baseRest from './_baseRest.js';\n\n/**\n * Defers invoking the `func` until the current call stack has cleared. Any\n * additional arguments are provided to `func` when it's invoked.\n *\n * @static\n * @memberOf _\n * @since 0.1.0\n * @category Function\n * @param {Function} func The function to defer.\n * @param {...*} [args] The arguments to invoke `func` with.\n * @returns {number} Returns the timer id.\n * @example\n *\n * _.defer(function(text) {\n *   console.log(text);\n * }, 'deferred');\n * // => Logs 'deferred' after one millisecond.\n */\nvar defer = baseRest(function (func, args) {\n  return baseDelay(func, 1, args);\n});\nexport default defer;", "map": {"version": 3, "names": ["baseDelay", "baseRest", "defer", "func", "args"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/node_modules/.pnpm/lodash-es@4.17.21/node_modules/lodash-es/defer.js"], "sourcesContent": ["import baseDelay from './_baseDelay.js';\nimport baseRest from './_baseRest.js';\n\n/**\n * Defers invoking the `func` until the current call stack has cleared. Any\n * additional arguments are provided to `func` when it's invoked.\n *\n * @static\n * @memberOf _\n * @since 0.1.0\n * @category Function\n * @param {Function} func The function to defer.\n * @param {...*} [args] The arguments to invoke `func` with.\n * @returns {number} Returns the timer id.\n * @example\n *\n * _.defer(function(text) {\n *   console.log(text);\n * }, 'deferred');\n * // => Logs 'deferred' after one millisecond.\n */\nvar defer = baseRest(function(func, args) {\n  return baseDelay(func, 1, args);\n});\n\nexport default defer;\n"], "mappings": "AAAA,OAAOA,SAAS,MAAM,iBAAiB;AACvC,OAAOC,QAAQ,MAAM,gBAAgB;;AAErC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIC,KAAK,GAAGD,QAAQ,CAAC,UAASE,IAAI,EAAEC,IAAI,EAAE;EACxC,OAAOJ,SAAS,CAACG,IAAI,EAAE,CAAC,EAAEC,IAAI,CAAC;AACjC,CAAC,CAAC;AAEF,eAAeF,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
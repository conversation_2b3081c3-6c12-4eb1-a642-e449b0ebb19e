{"ast": null, "code": "import { resolveComponent as _resolveComponent, createVNode as _createVNode, withCtx as _withCtx, createTextVNode as _createTextVNode, openBlock as _openBlock, createBlock as _createBlock, createCommentVNode as _createCommentVNode, createElementVNode as _createElementVNode, withModifiers as _withModifiers, createElementBlock as _createElementBlock } from \"vue\";\nvar _hoisted_1 = {\n  class: \"NoticeAnnouncementType\"\n};\nvar _hoisted_2 = {\n  class: \"globalFormButton\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  var _component_el_input = _resolveComponent(\"el-input\");\n  var _component_el_form_item = _resolveComponent(\"el-form-item\");\n  var _component_el_radio = _resolveComponent(\"el-radio\");\n  var _component_el_radio_group = _resolveComponent(\"el-radio-group\");\n  var _component_business_select_role = _resolveComponent(\"business-select-role\");\n  var _component_el_button = _resolveComponent(\"el-button\");\n  var _component_el_form = _resolveComponent(\"el-form\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createVNode(_component_el_form, {\n    ref: \"formRef\",\n    model: $setup.form,\n    rules: $setup.rules,\n    inline: \"\",\n    \"label-position\": \"top\",\n    class: \"globalForm\",\n    onSubmit: _cache[4] || (_cache[4] = _withModifiers(function () {}, [\"prevent\"]))\n  }, {\n    default: _withCtx(function () {\n      return [_createVNode(_component_el_form_item, {\n        label: \"类型\",\n        prop: \"channelName\",\n        class: \"globalFormTitle\"\n      }, {\n        default: _withCtx(function () {\n          return [_createVNode(_component_el_input, {\n            modelValue: $setup.form.channelName,\n            \"onUpdate:modelValue\": _cache[0] || (_cache[0] = function ($event) {\n              return $setup.form.channelName = $event;\n            }),\n            placeholder: \"请输入类型\",\n            clearable: \"\"\n          }, null, 8 /* PROPS */, [\"modelValue\"])];\n        }),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_form_item, {\n        label: \"角色范围\",\n        prop: \"isRole\",\n        class: \"globalFormTitle\"\n      }, {\n        default: _withCtx(function () {\n          return [_createVNode(_component_el_radio_group, {\n            modelValue: $setup.form.isPublic,\n            \"onUpdate:modelValue\": _cache[1] || (_cache[1] = function ($event) {\n              return $setup.form.isPublic = $event;\n            }),\n            onChange: $setup.publicChange\n          }, {\n            default: _withCtx(function () {\n              return [_createVNode(_component_el_radio, {\n                label: 1\n              }, {\n                default: _withCtx(function () {\n                  return _cache[5] || (_cache[5] = [_createTextVNode(\"所有角色可用\")]);\n                }),\n                _: 1 /* STABLE */\n              }), _createVNode(_component_el_radio, {\n                label: 0\n              }, {\n                default: _withCtx(function () {\n                  return _cache[6] || (_cache[6] = [_createTextVNode(\"指定角色可用\")]);\n                }),\n                _: 1 /* STABLE */\n              })];\n            }),\n            _: 1 /* STABLE */\n          }, 8 /* PROPS */, [\"modelValue\"]), !$setup.form.isPublic ? (_openBlock(), _createBlock(_component_business_select_role, {\n            key: 0,\n            modelValue: $setup.form.roleData,\n            \"onUpdate:modelValue\": _cache[2] || (_cache[2] = function ($event) {\n              return $setup.form.roleData = $event;\n            }),\n            onCallback: $setup.callback\n          }, null, 8 /* PROPS */, [\"modelValue\"])) : _createCommentVNode(\"v-if\", true)];\n        }),\n        _: 1 /* STABLE */\n      }), _createElementVNode(\"div\", _hoisted_2, [_createVNode(_component_el_button, {\n        type: \"primary\",\n        onClick: _cache[3] || (_cache[3] = function ($event) {\n          return $setup.submitForm($setup.formRef);\n        })\n      }, {\n        default: _withCtx(function () {\n          return _cache[7] || (_cache[7] = [_createTextVNode(\"提交\")]);\n        }),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_button, {\n        onClick: $setup.resetForm\n      }, {\n        default: _withCtx(function () {\n          return _cache[8] || (_cache[8] = [_createTextVNode(\"取消\")]);\n        }),\n        _: 1 /* STABLE */\n      })])];\n    }),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"model\", \"rules\"])]);\n}", "map": {"version": 3, "names": ["class", "_createElementBlock", "_hoisted_1", "_createVNode", "_component_el_form", "ref", "model", "$setup", "form", "rules", "inline", "onSubmit", "_cache", "_withModifiers", "default", "_withCtx", "_component_el_form_item", "label", "prop", "_component_el_input", "modelValue", "channelName", "$event", "placeholder", "clearable", "_", "_component_el_radio_group", "isPublic", "onChange", "publicChange", "_component_el_radio", "_createTextVNode", "_createBlock", "_component_business_select_role", "key", "roleData", "onCallback", "callback", "_createCommentVNode", "_createElementVNode", "_hoisted_2", "_component_el_button", "type", "onClick", "submitForm", "formRef", "resetForm"], "sources": ["D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\interaction\\src\\views\\NoticeAnnouncement\\component\\NoticeAnnouncementType.vue"], "sourcesContent": ["<template>\r\n  <div class=\"NoticeAnnouncementType\">\r\n    <el-form ref=\"formRef\"\r\n             :model=\"form\"\r\n             :rules=\"rules\"\r\n             inline\r\n             label-position=\"top\"\r\n             class=\"globalForm\"\r\n             @submit.enter.prevent>\r\n      <el-form-item label=\"类型\"\r\n                    prop=\"channelName\"\r\n                    class=\"globalFormTitle\">\r\n        <el-input v-model=\"form.channelName\"\r\n                  placeholder=\"请输入类型\"\r\n                  clearable />\r\n      </el-form-item>\r\n      <el-form-item label=\"角色范围\"\r\n                    prop=\"isRole\"\r\n                    class=\"globalFormTitle\">\r\n        <el-radio-group v-model=\"form.isPublic\"\r\n                        @change=\"publicChange\">\r\n          <el-radio :label=\"1\">所有角色可用</el-radio>\r\n          <el-radio :label=\"0\">指定角色可用</el-radio>\r\n        </el-radio-group>\r\n        <template v-if=\"!form.isPublic\">\r\n          <business-select-role v-model=\"form.roleData\"\r\n                                @callback=\"callback\"></business-select-role>\r\n        </template>\r\n      </el-form-item>\r\n      <div class=\"globalFormButton\">\r\n        <el-button type=\"primary\"\r\n                   @click=\"submitForm(formRef)\">提交</el-button>\r\n        <el-button @click=\"resetForm\">取消</el-button>\r\n      </div>\r\n    </el-form>\r\n  </div>\r\n</template>\r\n<script>\r\nexport default { name: 'NoticeAnnouncementType' }\r\n</script>\r\n<script setup>\r\nimport api from '@/api'\r\nimport { reactive, ref, onMounted } from 'vue'\r\nimport { ElMessage } from 'element-plus'\r\nconst props = defineProps({ id: { type: String, default: '' } })\r\nconst emit = defineEmits(['callback'])\r\n\r\nconst formRef = ref()\r\nconst form = reactive({\r\n  channelName: '', // 类型\r\n  isPublic: 1, // 字典标识名称\r\n  roleData: [], // 选择角色\r\n  isRole: '1' // 选择角色\r\n})\r\nconst rules = reactive({\r\n  channelName: [{ required: true, message: '请输入类型', trigger: ['blur', 'change'] }],\r\n  isRole: [{ required: true, message: '请选择角色', trigger: ['blur', 'change'] }]\r\n})\r\n\r\nonMounted(() => { if (props.id) { NoticeAnnouncementTypeInfo() } })\r\n\r\nconst NoticeAnnouncementTypeInfo = async () => {\r\n  const res = await api.NoticeAnnouncementTypeInfo({ detailId: props.id })\r\n  var { data } = res\r\n  form.channelName = data.channelName\r\n  form.isPublic = data.roleIds.length ? 0 : 1\r\n  form.isRole = form.isPublic ? '1' : ''\r\n  form.roleData = data.roleIds\r\n}\r\nconst publicChange = () => {\r\n  form.isRole = form.isPublic ? '1' : ''\r\n  if (form.isPublic) {\r\n    formRef.value.validateField('isRole')\r\n  }\r\n}\r\nconst callback = (data) => {\r\n  form.isRole = data.length ? '1' : ''\r\n  formRef.value.validateField('isRole')\r\n}\r\nconst submitForm = async (formEl) => {\r\n  if (!formEl) return\r\n  await formEl.validate((valid, fields) => {\r\n    if (valid) { globalJson() } else { ElMessage({ type: 'warning', message: '请根据提示信息完善字段内容！' }) }\r\n  })\r\n}\r\nconst globalJson = async () => {\r\n  const { code } = await api.globalJson(props.id ? '/notificationChannel/edit' : '/notificationChannel/add', {\r\n    form: {\r\n      id: props.id,\r\n      channelName: form.channelName\r\n    },\r\n    roleIds: form.isPublic ? [] : form.roleData\r\n  })\r\n  if (code === 200) {\r\n    ElMessage({ type: 'success', message: props.id ? '编辑成功' : '新增成功' })\r\n    emit('callback')\r\n  }\r\n}\r\nconst resetForm = () => { emit('callback') }\r\n</script>\r\n<style lang=\"scss\">\r\n.NoticeAnnouncementType {\r\n  width: 680px;\r\n}\r\n</style>\r\n"], "mappings": ";;EACOA,KAAK,EAAC;AAAwB;;EA4B1BA,KAAK,EAAC;AAAkB;;;;;;;;;uBA5BjCC,mBAAA,CAkCM,OAlCNC,UAkCM,GAjCJC,YAAA,CAgCUC,kBAAA;IAhCDC,GAAG,EAAC,SAAS;IACZC,KAAK,EAAEC,MAAA,CAAAC,IAAI;IACXC,KAAK,EAAEF,MAAA,CAAAE,KAAK;IACbC,MAAM,EAAN,EAAM;IACN,gBAAc,EAAC,KAAK;IACpBV,KAAK,EAAC,YAAY;IACjBW,QAAM,EAAAC,MAAA,QAAAA,MAAA,MARpBC,cAAA,CAQa,cAAqB;;IARlCC,OAAA,EAAAC,QAAA,CASM;MAAA,OAMe,CANfZ,YAAA,CAMea,uBAAA;QANDC,KAAK,EAAC,IAAI;QACVC,IAAI,EAAC,aAAa;QAClBlB,KAAK,EAAC;;QAX1Bc,OAAA,EAAAC,QAAA,CAYQ;UAAA,OAEsB,CAFtBZ,YAAA,CAEsBgB,mBAAA;YAd9BC,UAAA,EAY2Bb,MAAA,CAAAC,IAAI,CAACa,WAAW;YAZ3C,uBAAAT,MAAA,QAAAA,MAAA,gBAAAU,MAAA;cAAA,OAY2Bf,MAAA,CAAAC,IAAI,CAACa,WAAW,GAAAC,MAAA;YAAA;YACzBC,WAAW,EAAC,OAAO;YACnBC,SAAS,EAAT;;;QAdlBC,CAAA;UAgBMtB,YAAA,CAYea,uBAAA;QAZDC,KAAK,EAAC,MAAM;QACZC,IAAI,EAAC,QAAQ;QACblB,KAAK,EAAC;;QAlB1Bc,OAAA,EAAAC,QAAA,CAmBQ;UAAA,OAIiB,CAJjBZ,YAAA,CAIiBuB,yBAAA;YAvBzBN,UAAA,EAmBiCb,MAAA,CAAAC,IAAI,CAACmB,QAAQ;YAnB9C,uBAAAf,MAAA,QAAAA,MAAA,gBAAAU,MAAA;cAAA,OAmBiCf,MAAA,CAAAC,IAAI,CAACmB,QAAQ,GAAAL,MAAA;YAAA;YACrBM,QAAM,EAAErB,MAAA,CAAAsB;;YApBjCf,OAAA,EAAAC,QAAA,CAqBU;cAAA,OAAsC,CAAtCZ,YAAA,CAAsC2B,mBAAA;gBAA3Bb,KAAK,EAAE;cAAC;gBArB7BH,OAAA,EAAAC,QAAA,CAqB+B;kBAAA,OAAMH,MAAA,QAAAA,MAAA,OArBrCmB,gBAAA,CAqB+B,QAAM,E;;gBArBrCN,CAAA;kBAsBUtB,YAAA,CAAsC2B,mBAAA;gBAA3Bb,KAAK,EAAE;cAAC;gBAtB7BH,OAAA,EAAAC,QAAA,CAsB+B;kBAAA,OAAMH,MAAA,QAAAA,MAAA,OAtBrCmB,gBAAA,CAsB+B,QAAM,E;;gBAtBrCN,CAAA;;;YAAAA,CAAA;8CAwByBlB,MAAA,CAAAC,IAAI,CAACmB,QAAQ,I,cAC5BK,YAAA,CACkEC,+BAAA;YA1B5EC,GAAA;YAAAd,UAAA,EAyByCb,MAAA,CAAAC,IAAI,CAAC2B,QAAQ;YAzBtD,uBAAAvB,MAAA,QAAAA,MAAA,gBAAAU,MAAA;cAAA,OAyByCf,MAAA,CAAAC,IAAI,CAAC2B,QAAQ,GAAAb,MAAA;YAAA;YACrBc,UAAQ,EAAE7B,MAAA,CAAA8B;qDA1B3CC,mBAAA,e;;QAAAb,CAAA;UA6BMc,mBAAA,CAIM,OAJNC,UAIM,GAHJrC,YAAA,CACsDsC,oBAAA;QAD3CC,IAAI,EAAC,SAAS;QACbC,OAAK,EAAA/B,MAAA,QAAAA,MAAA,gBAAAU,MAAA;UAAA,OAAEf,MAAA,CAAAqC,UAAU,CAACrC,MAAA,CAAAsC,OAAO;QAAA;;QA/B7C/B,OAAA,EAAAC,QAAA,CA+BgD;UAAA,OAAEH,MAAA,QAAAA,MAAA,OA/BlDmB,gBAAA,CA+BgD,IAAE,E;;QA/BlDN,CAAA;UAgCQtB,YAAA,CAA4CsC,oBAAA;QAAhCE,OAAK,EAAEpC,MAAA,CAAAuC;MAAS;QAhCpChC,OAAA,EAAAC,QAAA,CAgCsC;UAAA,OAAEH,MAAA,QAAAA,MAAA,OAhCxCmB,gBAAA,CAgCsC,IAAE,E;;QAhCxCN,CAAA;;;IAAAA,CAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
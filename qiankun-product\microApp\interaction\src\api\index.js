// 导入封装的方法
import HTTP from 'common/http'
import GlobalApi from 'common/http/GlobalApi'
const api = {
  ...GlobalApi,
  themeTemplateList (params) { // 模板列表
    return HTTP.json('/themeTemplate/list', params)
  },
  themeTemplateInfo (params) { // 模板详情
    return HTTP.json('/themeTemplate/info', params)
  },
  themeTemplateDel (params) { // 模板删除
    return HTTP.json('/themeTemplate/dels', params)
  },
  messageSwitchList (params) { // 消息开关列表
    return HTTP.json('/messageSwitch/list', params)
  },
  boxMessageReceiverDel (params) { // 消息盒子删除接收人
    return HTTP.json('/boxMessageReceiver/dels', params)
  },
  boxMessageList (params) { // 消息盒子列表
    return HTTP.json('/boxMessage/list', params)
  },
  boxMessageInfo (params) { // 消息盒子详情
    return HTTP.json('/boxMessage/info', params)
  },
  boxMessageDelUser (params) { // 消息盒子删除
    return HTTP.json('/boxMessage/delUser', params)
  },
  boxMessageDel (params) { // 消息盒子删除
    return HTTP.json('/boxMessage/dels', params)
  },
  boxMessageType (params) { // 消息盒子类型筛选下拉选
    return HTTP.json('/boxMessage/businesses', params)
  },
  boxMessageSign (params) { // 消息盒子标记已读
    return HTTP.json('/redDot/sign', params)
  },
  boxMessageRead (params) { // 消息盒子全部标记已读
    return HTTP.json('/redDot/signBatch', params)
  },
  boxMessageBatchList (params) { // 系统消息盒子批次列表
    return HTTP.json('/boxMessage/batchList', params)
  },
  boxMessageBatchInfo (params) { // 系统消息盒子批次详情
    return HTTP.json('/boxMessage/batchInfo', params)
  },
  boxMessageBatchReminder (params) { // 系统消息盒子短信提醒
    return HTTP.json('/boxMessage/textMessageNotice', params)
  },
  personalDoList (params) { // 个人待办列表
    return HTTP.json('/pendingMessage/list', params)
  },
  pendingMessageSelect (params) { // 个人待办类型筛选下拉选
    return HTTP.json('/pendingMessage/select', params)
  },
  personalDoInfo (params) { // 个人待办详情
    return HTTP.json('/pendingMessage/info', params)
  },
  personalDoDel (params) { // 个人待办删除
    return HTTP.json('/pendingMessage/dels', params)
  },
  textMessageLimit (params) { // 短信额度统计
    return HTTP.json('/textMessage/limit', params)
  },
  textMessageList (params) { // 短信列表
    return HTTP.json('/textMessage/list', params)
  },
  textMessageBatchList (params) { // 短信集合列表
    return HTTP.json('/textMessage/batchList', params)
  },
  textMessageBatchInfo (params) { // 短信集合详情
    return HTTP.json('/textMessage/batchInfo', params)
  },
  textMessageInfo (params) { // 短信详情
    return HTTP.json('/textMessage/info', params)
  },
  textMessageDel (params) { // 短信删除
    return HTTP.json('/textMessage/dels', params)
  },
  UplinkTextMessageList (params) { // 上行短信列表
    return HTTP.json('/textMessageReceived/list', params)
  },
  UplinkTextMessageInfo (params) { // 上行短信详情
    return HTTP.json('/textMessageReceived/info', params)
  },
  UplinkTextMessageDel (params) { // 上行短信删除
    return HTTP.json('/textMessageReceived/dels', params)
  },
  DoNotDisturbUserList (params) { // 短信勿扰用户列表
    return HTTP.json('/textMessageShield/users', params)
  },
  DoNotDisturbUserNew (params) { // 短信勿扰用户新增
    return HTTP.json('/textMessageShield/add', params)
  },
  DoNotDisturbUserDel (params) { // 短信勿扰用户删除
    return HTTP.json('/textMessageShield/remove', params)
  },
  ClusterList (params) { // 群组列表
    return HTTP.json('/chatGroup/list', params)
  },
  ClusterInfo (params) { // 群组详情
    return HTTP.json('/chatGroup/info', params)
  },
  ClusterDel (params) { // 群组删除
    return HTTP.json('/chatGroup/dels', params)
  },
  ClusterAutoBuildSelector (params) { // 群组操作按钮
    return HTTP.json('/chatGroup/autoBuildSelector', params)
  },
  ClusterAutoBuild (params) { // 群组操作按钮点击
    return HTTP.json('/chatGroup/autoBuild', params)
  },
  chatGroupMemberList (params) { // 群组成员详情
    return HTTP.json('/chatGroupMember/list', params)
  },
  ClusterFileList (params) { // 群文件列表
    return HTTP.json('/chatGroupFile/list', params)
  },
  ClusterFileDel (params) { // 群文件删除
    return HTTP.json('/chatGroupFile/dels', params)
  },
  AddressBookTypeList (params) { // 通讯录分类列表
    return HTTP.json('/relationBookType/list', params)
  },
  AddressBookTypeInfo (params) { // 通讯录分类详情
    return HTTP.json('/relationBookType/info', params)
  },
  AddressBookTypeSort (params) { // 通讯录分类排序
    return HTTP.json('/relationBookType/sort', params)
  },
  AddressBookTypeDel (params) { // 通讯录分类删除
    return HTTP.json('/relationBookType/dels', params)
  },
  AddressBookGroupList (params) { // 通讯录分组列表
    return HTTP.json('/relationBook/list', params)
  },
  AddressBookGroupInfo (params) { // 通讯录分组详情
    return HTTP.json('/relationBook/info', params)
  },
  flushUserNameSort (params) { // 通讯录分组排序
    return HTTP.json('/relationBookMember/flushUserNameSort', params)
  },
  AddressBookGroupSort (params) { // 通讯录分组排序
    return HTTP.json('/relationBook/sort', params)
  },
  AddressBookGroupDel (params) { // 通讯录分组删除
    return HTTP.json('/relationBook/dels', params)
  },
  AddressBookPullSelect (params) { // 通讯录操作按钮
    return HTTP.json('/relationBook/pullSelect', params)
  },
  AddressBookPull (code) { // 通讯录操作按钮点击
    return HTTP.json(`/relationBook/pull/${code}`)
  },
  AddressBookUserList (params) { // 通讯录成员列表
    return HTTP.json('/relationBookMember/list', params)
  },
  AddressBookUserInfo (params) { // 通讯录成员详情
    return HTTP.json('/relationBookMember/info', params)
  },
  AddressBookUserDel (params) { // 通讯录成员删除
    return HTTP.json('/relationBookMember/dels', params)
  },
  AddressBookUserFind (params) { // 查找通讯录已存在的用户
    return HTTP.json('/relationBookMember/userIds', params)
  },
  AddressBookUserJoin (params) { // 通讯录保存关连用户
    return HTTP.json('/relationBookMember/join', params)
  },
  AddressBookUserOpen (params) { // 通讯录用户是否公开
    return HTTP.json('/relationBookMember/open', params)
  },
  styleCircleList (params) { // 圈子列表
    return HTTP.json('/styleCircle/list', params)
  },
  styleCircleInfo (params) { // 圈子详情
    return HTTP.json('/styleCircle/info', params)
  },
  styleCircleDel (params) { // 圈子删除
    return HTTP.json('/styleCircle/dels', params)
  },
  styleCirclePass (params) { // 圈子审核通过
    return HTTP.json('/styleCircle/check/pass', params)
  },
  styleCircleNoPass (params) { // 圈子审核不通过
    return HTTP.json('/styleCircle/check/nopass ', params)
  },
  NoticeAnnouncementTypeList (params) { // 通知公告类型列表
    return HTTP.json('/notificationChannel/list', params)
  },
  NoticeAnnouncementTypeInfo (params) { // 通知公告类型详情
    return HTTP.json('/notificationChannel/info', params)
  },
  NoticeAnnouncementTypeSort (params) { // 通知公告类型排序
    return HTTP.json('/notificationChannel/sort', params)
  },
  NoticeAnnouncementTypeDel (params) { // 通知公告类型删除
    return HTTP.json('/notificationChannel/dels', params)
  },
  NoticeAnnouncementList (params) { // 通知公告列表
    return HTTP.json('/notification/list', params)
  },
  NoticeAnnouncementInfo (params) { // 通知公告详情
    return HTTP.json('/notification/info', params)
  },
  NoticeAnnouncementDel (params) { // 通知公告删除
    return HTTP.json('/notification/dels', params)
  },
  NoticeAnnouncementScope (params) { // 通知公告范围
    return HTTP.json('/authorityRange/selectors', params)
  },
  NoticeAnnouncementSubmit (params) { // 通知公告批量提交草稿箱
    return HTTP.json('/notification/submit/draft', params)
  },
  NoticeAnnouncementReading (params) { // 通知公告阅读情况
    return HTTP.json('/notificationReceiver/readUserList', params)
  },
  NoticeAnnouncementRemind (params) { // 通知公告未读人员短信提醒
    return HTTP.json('/notificationReceiver/noticeNoRead', params)
  },
  NoticeAnnouncementTop (params) { // 通知公告置顶
    return HTTP.json('/notification/top', params)
  },
  ReturnReceiptList (params) { // 通知公告回执列表
    return HTTP.json('/notificationReceiver/list', params)
  },
  ReturnReceiptInfo (params) { // 通知公告回执详情
    return HTTP.json('/notificationReceiver/info', params)
  },
  ReturnReceiptDel (params) { // 通知公告回执删除
    return HTTP.json('/notificationReceiver/dels', params)
  },
  favoriteFolderInfo (params) { // 收藏文件夹详情
    return HTTP.json('/favoriteFolder/info', params)
  },
  favoriteFolderDel (params) { // 收藏文件夹删除
    return HTTP.json('/favoriteFolder/dels', params)
  },
  favoriteList (params) { // 收藏列表
    return HTTP.json('/favorite/list', params)
  },
  favoriteInfo (params) { // 收藏详情
    return HTTP.json('/favorite/info', params)
  },
  favoriteDel (params) { // 收藏删除
    return HTTP.json('/favorite/dels', params)
  },
  favoriteMove (params) { // 收藏移动
    return HTTP.json('/favorite/move', params)
  },
  videoConnectionList (params) { // 视频会议列表
    return HTTP.json('/videoConnection/list', params)
  },
  videoConnectionInfo (params) { // 视频会议详情
    return HTTP.json('/videoConnection/info', params)
  },
  videoConnectionDel (params) { // 视频会议删除
    return HTTP.json('/videoConnection/dels', params)
  },
  chooseUserTagList (params) { // 常用联系人列表
    return HTTP.json('/chooseUserTag/list', params)
  },
  chooseUserTagInfo (params) { // 常用联系人详情
    return HTTP.json('/chooseUserTag/info', params)
  },
  chooseUserTagDel (params) { // 常用联系人删除
    return HTTP.json('/chooseUserTag/dels', params)
  },
  chooseUserTagFindUsers (params) { // 常用联系人用户列表
    return HTTP.json('/chooseUserTag/findUsers', params)
  },
  chooseUserTagJoinUser (params) { // 常用联系人关联用户
    return HTTP.json('/chooseUserTag/join/user', params)
  },
  chooseUserTagCleanUser (params) { // 常用联系人取消关联用户
    return HTTP.json('/chooseUserTag/clean/user', params)
  },
  chooseUserTagRoleSelector (params) { // 常用联系人关联用户角色下拉选
    return HTTP.json('/chooseUserTag/roleSelector', params)
  },
  chooseUserTagFindPrepareUser (params) { // 常用联系人关联用户列表
    return HTTP.json('/chooseUserTag/findPrepareUser', params)
  },
  videoConnectionDelLive (params) { // 直播管理删除
    return HTTP.json('/videoConnection/delsLive', params)
  },
  praisesAdd (params) { // 新增点赞
    return HTTP.json('/praises/add', params)
  },
  praisesDels (params) { // 删除点赞
    return HTTP.json('/praises/dels', params)
  },
  LiveBroadcastList (params) { // 在线直播
    return HTTP.json('/videoConnection/listLive', params)
  },
  popUpList (params) { // 弹窗列表
    return HTTP.json('/popUp/list', params)
  },
  popUpDels (params) { // 弹窗删除
    return HTTP.json('/popUp/dels', params)
  },
  popUpInfo (params) { // 弹窗详情
    return HTTP.json('/popUp/info', params)
  },
  liveWatchCountAdd (params) { // 添加访问记录
    return HTTP.json('/liveWatchCount/add', params)
  },
  // 手机验证相关接口
  sendVerifyCode (params) { // 发送验证码
    return HTTP.json('/open_api/verifyCode/send', params)
  },
  verifyPhone (params) { // 验证手机号
    return HTTP.json('/open_api/verifyCode/verify', params)
  }
}
export default api

{"ast": null, "code": "import { resolveComponent as _resolveComponent, createVNode as _createVNode, withCtx as _withCtx, createTextVNode as _createTextVNode, createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock } from \"vue\";\nvar _hoisted_1 = {\n  class: \"GenerateCluster\"\n};\nvar _hoisted_2 = {\n  class: \"globalFormButton\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  var _component_input_select_person_account = _resolveComponent(\"input-select-person-account\");\n  var _component_el_form_item = _resolveComponent(\"el-form-item\");\n  var _component_business_select_person_account = _resolveComponent(\"business-select-person-account\");\n  var _component_el_button = _resolveComponent(\"el-button\");\n  var _component_el_form = _resolveComponent(\"el-form\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createVNode(_component_el_form, {\n    ref: \"formRef\",\n    model: $setup.form,\n    rules: $setup.rules,\n    inline: \"\",\n    \"label-position\": \"top\",\n    class: \"globalForm\"\n  }, {\n    default: _withCtx(function () {\n      return [_createVNode(_component_el_form_item, {\n        label: \"群主\",\n        prop: \"ownerUserId\"\n      }, {\n        default: _withCtx(function () {\n          return [_createVNode(_component_input_select_person_account, {\n            modelValue: $setup.form.ownerUserId,\n            \"onUpdate:modelValue\": _cache[0] || (_cache[0] = function ($event) {\n              return $setup.form.ownerUserId = $event;\n            }),\n            placeholder: \"请选择群主\"\n          }, null, 8 /* PROPS */, [\"modelValue\"])];\n        }),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_form_item, {\n        label: \"群成员\",\n        class: \"globalFormTitle\"\n      }, {\n        default: _withCtx(function () {\n          return [_createVNode(_component_business_select_person_account, {\n            modelValue: $setup.form.memberUserIds,\n            \"onUpdate:modelValue\": _cache[1] || (_cache[1] = function ($event) {\n              return $setup.form.memberUserIds = $event;\n            }),\n            onCallback: $setup.userCallback\n          }, null, 8 /* PROPS */, [\"modelValue\"])];\n        }),\n        _: 1 /* STABLE */\n      }), _createElementVNode(\"div\", _hoisted_2, [_createVNode(_component_el_button, {\n        type: \"primary\",\n        onClick: _cache[2] || (_cache[2] = function ($event) {\n          return $setup.submitForm($setup.formRef);\n        })\n      }, {\n        default: _withCtx(function () {\n          return _cache[3] || (_cache[3] = [_createTextVNode(\"提交\")]);\n        }),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_button, {\n        onClick: $setup.resetForm\n      }, {\n        default: _withCtx(function () {\n          return _cache[4] || (_cache[4] = [_createTextVNode(\"取消\")]);\n        }),\n        _: 1 /* STABLE */\n      })])];\n    }),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"model\", \"rules\"])]);\n}", "map": {"version": 3, "names": ["class", "_createElementBlock", "_hoisted_1", "_createVNode", "_component_el_form", "ref", "model", "$setup", "form", "rules", "inline", "default", "_withCtx", "_component_el_form_item", "label", "prop", "_component_input_select_person_account", "modelValue", "ownerUserId", "_cache", "$event", "placeholder", "_", "_component_business_select_person_account", "memberUserIds", "onCallback", "userCallback", "_createElementVNode", "_hoisted_2", "_component_el_button", "type", "onClick", "submitForm", "formRef", "_createTextVNode", "resetForm"], "sources": ["D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\interaction\\src\\views\\ClusterManage\\component\\GenerateCluster.vue"], "sourcesContent": ["<template>\r\n  <div class=\"GenerateCluster\">\r\n    <el-form ref=\"formRef\"\r\n             :model=\"form\"\r\n             :rules=\"rules\"\r\n             inline\r\n             label-position=\"top\"\r\n             class=\"globalForm\">\r\n      <el-form-item label=\"群主\"\r\n                    prop=\"ownerUserId\">\r\n        <input-select-person-account v-model=\"form.ownerUserId\"\r\n                                     placeholder=\"请选择群主\" />\r\n      </el-form-item>\r\n      <el-form-item label=\"群成员\"\r\n                    class=\"globalFormTitle\">\r\n        <business-select-person-account v-model=\"form.memberUserIds\"\r\n                                        @callback=\"userCallback\"></business-select-person-account>\r\n      </el-form-item>\r\n      <div class=\"globalFormButton\">\r\n        <el-button type=\"primary\"\r\n                   @click=\"submitForm(formRef)\">提交</el-button>\r\n        <el-button @click=\"resetForm\">取消</el-button>\r\n      </div>\r\n    </el-form>\r\n  </div>\r\n</template>\r\n<script>\r\nexport default { name: 'GenerateCluster' }\r\n</script>\r\n<script setup>\r\nimport api from '@/api'\r\nimport { reactive, ref } from 'vue'\r\nimport { ElMessage } from 'element-plus'\r\nconst props = defineProps({ code: { type: String, default: '' }, name: { type: String, default: '' } })\r\nconst emit = defineEmits(['callback'])\r\n\r\nconst formRef = ref()\r\nconst form = reactive({\r\n  ownerUserId: '', // 群主id\r\n  memberUserIds: [] // 群成员\r\n})\r\nconst rules = reactive({ ownerUserId: [{ required: true, message: '请选择群主', trigger: ['blur', 'change'] }] })\r\n\r\nconst userCallback = () => {\r\n  formRef.value.validateField('memberUserIds')\r\n}\r\nconst submitForm = async (formEl) => {\r\n  if (!formEl) return\r\n  await formEl.validate((valid, fields) => {\r\n    if (valid) { globalJson() } else { ElMessage({ type: 'warning', message: '请根据提示信息完善字段内容！' }) }\r\n  })\r\n}\r\nconst globalJson = async () => {\r\n  const { code } = await api.ClusterAutoBuild({\r\n    businessCode: props.code,\r\n    ownerUserId: form.ownerUserId,\r\n    memberUserIds: form.memberUserIds\r\n  })\r\n  if (code === 200) {\r\n    ElMessage({ type: 'success', message: `${props.name}成功` })\r\n    emit('callback')\r\n  }\r\n}\r\nconst resetForm = () => { emit('callback') }\r\n</script>\r\n<style lang=\"scss\">\r\n.GenerateCluster {\r\n  width: 990px;\r\n}\r\n</style>\r\n"], "mappings": ";;EACOA,KAAK,EAAC;AAAiB;;EAiBnBA,KAAK,EAAC;AAAkB;;;;;;;uBAjBjCC,mBAAA,CAuBM,OAvBNC,UAuBM,GAtBJC,YAAA,CAqBUC,kBAAA;IArBDC,GAAG,EAAC,SAAS;IACZC,KAAK,EAAEC,MAAA,CAAAC,IAAI;IACXC,KAAK,EAAEF,MAAA,CAAAE,KAAK;IACbC,MAAM,EAAN,EAAM;IACN,gBAAc,EAAC,KAAK;IACpBV,KAAK,EAAC;;IAPnBW,OAAA,EAAAC,QAAA,CAQM;MAAA,OAIe,CAJfT,YAAA,CAIeU,uBAAA;QAJDC,KAAK,EAAC,IAAI;QACVC,IAAI,EAAC;;QATzBJ,OAAA,EAAAC,QAAA,CAUQ;UAAA,OACmD,CADnDT,YAAA,CACmDa,sCAAA;YAX3DC,UAAA,EAU8CV,MAAA,CAAAC,IAAI,CAACU,WAAW;YAV9D,uBAAAC,MAAA,QAAAA,MAAA,gBAAAC,MAAA;cAAA,OAU8Cb,MAAA,CAAAC,IAAI,CAACU,WAAW,GAAAE,MAAA;YAAA;YACzBC,WAAW,EAAC;;;QAXjDC,CAAA;UAaMnB,YAAA,CAIeU,uBAAA;QAJDC,KAAK,EAAC,KAAK;QACXd,KAAK,EAAC;;QAd1BW,OAAA,EAAAC,QAAA,CAeQ;UAAA,OAC0F,CAD1FT,YAAA,CAC0FoB,yCAAA;YAhBlGN,UAAA,EAeiDV,MAAA,CAAAC,IAAI,CAACgB,aAAa;YAfnE,uBAAAL,MAAA,QAAAA,MAAA,gBAAAC,MAAA;cAAA,OAeiDb,MAAA,CAAAC,IAAI,CAACgB,aAAa,GAAAJ,MAAA;YAAA;YAC1BK,UAAQ,EAAElB,MAAA,CAAAmB;;;QAhBnDJ,CAAA;UAkBMK,mBAAA,CAIM,OAJNC,UAIM,GAHJzB,YAAA,CACsD0B,oBAAA;QAD3CC,IAAI,EAAC,SAAS;QACbC,OAAK,EAAAZ,MAAA,QAAAA,MAAA,gBAAAC,MAAA;UAAA,OAAEb,MAAA,CAAAyB,UAAU,CAACzB,MAAA,CAAA0B,OAAO;QAAA;;QApB7CtB,OAAA,EAAAC,QAAA,CAoBgD;UAAA,OAAEO,MAAA,QAAAA,MAAA,OApBlDe,gBAAA,CAoBgD,IAAE,E;;QApBlDZ,CAAA;UAqBQnB,YAAA,CAA4C0B,oBAAA;QAAhCE,OAAK,EAAExB,MAAA,CAAA4B;MAAS;QArBpCxB,OAAA,EAAAC,QAAA,CAqBsC;UAAA,OAAEO,MAAA,QAAAA,MAAA,OArBxCe,gBAAA,CAqBsC,IAAE,E;;QArBxCZ,CAAA;;;IAAAA,CAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
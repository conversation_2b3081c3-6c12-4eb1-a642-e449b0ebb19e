{"ast": null, "code": "import { ref, computed } from 'vue';\nimport { useProps } from './useProps.mjs';\nfunction useAllowCreate(props, states) {\n  var _useProps = useProps(props),\n    aliasProps = _useProps.aliasProps,\n    getLabel = _useProps.getLabel,\n    getValue = _useProps.getValue;\n  var createOptionCount = ref(0);\n  var cachedSelectedOption = ref(null);\n  var enableAllowCreateMode = computed(function () {\n    return props.allowCreate && props.filterable;\n  });\n  function hasExistingOption(query) {\n    var hasOption = function hasOption(option) {\n      return getLabel(option) === query;\n    };\n    return props.options && props.options.some(hasOption) || states.createdOptions.some(hasOption);\n  }\n  function selectNewOption(option) {\n    if (!enableAllowCreateMode.value) {\n      return;\n    }\n    if (props.multiple && option.created) {\n      createOptionCount.value++;\n    } else {\n      cachedSelectedOption.value = option;\n    }\n  }\n  function createNewOption(query) {\n    if (enableAllowCreateMode.value) {\n      if (query && query.length > 0) {\n        if (hasExistingOption(query)) {\n          return;\n        }\n        var newOption = {\n          [aliasProps.value.value]: query,\n          [aliasProps.value.label]: query,\n          created: true,\n          [aliasProps.value.disabled]: false\n        };\n        if (states.createdOptions.length >= createOptionCount.value) {\n          states.createdOptions[createOptionCount.value] = newOption;\n        } else {\n          states.createdOptions.push(newOption);\n        }\n      } else {\n        if (props.multiple) {\n          states.createdOptions.length = createOptionCount.value;\n        } else {\n          var selectedOption = cachedSelectedOption.value;\n          states.createdOptions.length = 0;\n          if (selectedOption && selectedOption.created) {\n            states.createdOptions.push(selectedOption);\n          }\n        }\n      }\n    }\n  }\n  function removeNewOption(option) {\n    if (!enableAllowCreateMode.value || !option || !option.created || option.created && props.reserveKeyword && states.inputValue === getLabel(option)) {\n      return;\n    }\n    var idx = states.createdOptions.findIndex(function (it) {\n      return getValue(it) === getValue(option);\n    });\n    if (~idx) {\n      states.createdOptions.splice(idx, 1);\n      createOptionCount.value--;\n    }\n  }\n  function clearAllNewOption() {\n    if (enableAllowCreateMode.value) {\n      states.createdOptions.length = 0;\n      createOptionCount.value = 0;\n    }\n  }\n  return {\n    createNewOption,\n    removeNewOption,\n    selectNewOption,\n    clearAllNewOption\n  };\n}\nexport { useAllowCreate };", "map": {"version": 3, "names": ["useAllowCreate", "props", "states", "_useProps", "useProps", "aliasProps", "get<PERSON><PERSON><PERSON>", "getValue", "createOptionCount", "ref", "cachedSelectedOption", "enableAllowCreateMode", "computed", "allowCreate", "filterable", "hasExistingOption", "query", "hasOption", "option", "options", "some", "createdOptions", "selectNewOption", "value", "multiple", "created", "createNewOption", "length", "newOption", "label", "disabled", "push", "selectedOption", "removeNewOption", "reserveKeyword", "inputValue", "idx", "findIndex", "it", "splice", "clearAllNewOption"], "sources": ["../../../../../../packages/components/select-v2/src/useAllowCreate.ts"], "sourcesContent": ["// @ts-nocheck\nimport { computed, ref } from 'vue'\nimport { useProps } from './useProps'\nimport type { ISelectV2Props } from './token'\nimport type { Option } from './select.types'\n\nexport function useAllowCreate(props: ISelectV2Props, states) {\n  const { aliasProps, getLabel, getValue } = useProps(props)\n\n  const createOptionCount = ref(0)\n  const cachedSelectedOption = ref<Option>(null)\n\n  const enableAllowCreateMode = computed(() => {\n    return props.allowCreate && props.filterable\n  })\n\n  function hasExistingOption(query: string) {\n    const hasOption = (option) => getLabel(option) === query\n    return (\n      (props.options && props.options.some(hasOption)) ||\n      states.createdOptions.some(hasOption)\n    )\n  }\n\n  function selectNewOption(option: Option) {\n    if (!enableAllowCreateMode.value) {\n      return\n    }\n    if (props.multiple && option.created) {\n      createOptionCount.value++\n    } else {\n      cachedSelectedOption.value = option\n    }\n  }\n\n  function createNewOption(query: string) {\n    if (enableAllowCreateMode.value) {\n      if (query && query.length > 0) {\n        if (hasExistingOption(query)) {\n          return\n        }\n        const newOption = {\n          [aliasProps.value.value]: query,\n          [aliasProps.value.label]: query,\n          created: true,\n          [aliasProps.value.disabled]: false,\n        }\n        if (states.createdOptions.length >= createOptionCount.value) {\n          states.createdOptions[createOptionCount.value] = newOption\n        } else {\n          states.createdOptions.push(newOption)\n        }\n      } else {\n        if (props.multiple) {\n          states.createdOptions.length = createOptionCount.value\n        } else {\n          const selectedOption = cachedSelectedOption.value\n          states.createdOptions.length = 0\n          if (selectedOption && selectedOption.created) {\n            states.createdOptions.push(selectedOption)\n          }\n        }\n      }\n    }\n  }\n\n  function removeNewOption(option: Option) {\n    if (\n      !enableAllowCreateMode.value ||\n      !option ||\n      !option.created ||\n      (option.created &&\n        props.reserveKeyword &&\n        states.inputValue === getLabel(option))\n    ) {\n      return\n    }\n    const idx = states.createdOptions.findIndex(\n      (it) => getValue(it) === getValue(option)\n    )\n    if (~idx) {\n      states.createdOptions.splice(idx, 1)\n      createOptionCount.value--\n    }\n  }\n\n  function clearAllNewOption() {\n    if (enableAllowCreateMode.value) {\n      states.createdOptions.length = 0\n      createOptionCount.value = 0\n    }\n  }\n\n  return {\n    createNewOption,\n    removeNewOption,\n    selectNewOption,\n    clearAllNewOption,\n  }\n}\n"], "mappings": ";;AAEO,SAASA,cAAcA,CAACC,KAAK,EAAEC,MAAM,EAAE;EAC5C,IAAAC,SAAA,GAA2CC,QAAQ,CAACH,KAAK,CAAC;IAAlDI,UAAU,GAAAF,SAAA,CAAVE,UAAU;IAAEC,QAAQ,GAAAH,SAAA,CAARG,QAAQ;IAAEC,QAAQ,GAAAJ,SAAA,CAARI,QAAQ;EACtC,IAAMC,iBAAiB,GAAGC,GAAG,CAAC,CAAC,CAAC;EAChC,IAAMC,oBAAoB,GAAGD,GAAG,CAAC,IAAI,CAAC;EACtC,IAAME,qBAAqB,GAAGC,QAAQ,CAAC,YAAM;IAC3C,OAAOX,KAAK,CAACY,WAAW,IAAIZ,KAAK,CAACa,UAAU;EAChD,CAAG,CAAC;EACF,SAASC,iBAAiBA,CAACC,KAAK,EAAE;IAChC,IAAMC,SAAS,GAAG,SAAZA,SAASA,CAAIC,MAAM;MAAA,OAAKZ,QAAQ,CAACY,MAAM,CAAC,KAAKF,KAAK;IAAA;IACxD,OAAOf,KAAK,CAACkB,OAAO,IAAIlB,KAAK,CAACkB,OAAO,CAACC,IAAI,CAACH,SAAS,CAAC,IAAIf,MAAM,CAACmB,cAAc,CAACD,IAAI,CAACH,SAAS,CAAC;EAClG;EACE,SAASK,eAAeA,CAACJ,MAAM,EAAE;IAC/B,IAAI,CAACP,qBAAqB,CAACY,KAAK,EAAE;MAChC;IACN;IACI,IAAItB,KAAK,CAACuB,QAAQ,IAAIN,MAAM,CAACO,OAAO,EAAE;MACpCjB,iBAAiB,CAACe,KAAK,EAAE;IAC/B,CAAK,MAAM;MACLb,oBAAoB,CAACa,KAAK,GAAGL,MAAM;IACzC;EACA;EACE,SAASQ,eAAeA,CAACV,KAAK,EAAE;IAC9B,IAAIL,qBAAqB,CAACY,KAAK,EAAE;MAC/B,IAAIP,KAAK,IAAIA,KAAK,CAACW,MAAM,GAAG,CAAC,EAAE;QAC7B,IAAIZ,iBAAiB,CAACC,KAAK,CAAC,EAAE;UAC5B;QACV;QACQ,IAAMY,SAAS,GAAG;UAChB,CAACvB,UAAU,CAACkB,KAAK,CAACA,KAAK,GAAGP,KAAK;UAC/B,CAACX,UAAU,CAACkB,KAAK,CAACM,KAAK,GAAGb,KAAK;UAC/BS,OAAO,EAAE,IAAI;UACb,CAACpB,UAAU,CAACkB,KAAK,CAACO,QAAQ,GAAG;QACvC,CAAS;QACD,IAAI5B,MAAM,CAACmB,cAAc,CAACM,MAAM,IAAInB,iBAAiB,CAACe,KAAK,EAAE;UAC3DrB,MAAM,CAACmB,cAAc,CAACb,iBAAiB,CAACe,KAAK,CAAC,GAAGK,SAAS;QACpE,CAAS,MAAM;UACL1B,MAAM,CAACmB,cAAc,CAACU,IAAI,CAACH,SAAS,CAAC;QAC/C;MACA,CAAO,MAAM;QACL,IAAI3B,KAAK,CAACuB,QAAQ,EAAE;UAClBtB,MAAM,CAACmB,cAAc,CAACM,MAAM,GAAGnB,iBAAiB,CAACe,KAAK;QAChE,CAAS,MAAM;UACL,IAAMS,cAAc,GAAGtB,oBAAoB,CAACa,KAAK;UACjDrB,MAAM,CAACmB,cAAc,CAACM,MAAM,GAAG,CAAC;UAChC,IAAIK,cAAc,IAAIA,cAAc,CAACP,OAAO,EAAE;YAC5CvB,MAAM,CAACmB,cAAc,CAACU,IAAI,CAACC,cAAc,CAAC;UACtD;QACA;MACA;IACA;EACA;EACE,SAASC,eAAeA,CAACf,MAAM,EAAE;IAC/B,IAAI,CAACP,qBAAqB,CAACY,KAAK,IAAI,CAACL,MAAM,IAAI,CAACA,MAAM,CAACO,OAAO,IAAIP,MAAM,CAACO,OAAO,IAAIxB,KAAK,CAACiC,cAAc,IAAIhC,MAAM,CAACiC,UAAU,KAAK7B,QAAQ,CAACY,MAAM,CAAC,EAAE;MAClJ;IACN;IACI,IAAMkB,GAAG,GAAGlC,MAAM,CAACmB,cAAc,CAACgB,SAAS,CAAC,UAACC,EAAE;MAAA,OAAK/B,QAAQ,CAAC+B,EAAE,CAAC,KAAK/B,QAAQ,CAACW,MAAM,CAAC;IAAA,EAAC;IACtF,IAAI,CAACkB,GAAG,EAAE;MACRlC,MAAM,CAACmB,cAAc,CAACkB,MAAM,CAACH,GAAG,EAAE,CAAC,CAAC;MACpC5B,iBAAiB,CAACe,KAAK,EAAE;IAC/B;EACA;EACE,SAASiB,iBAAiBA,CAAA,EAAG;IAC3B,IAAI7B,qBAAqB,CAACY,KAAK,EAAE;MAC/BrB,MAAM,CAACmB,cAAc,CAACM,MAAM,GAAG,CAAC;MAChCnB,iBAAiB,CAACe,KAAK,GAAG,CAAC;IACjC;EACA;EACE,OAAO;IACLG,eAAe;IACfO,eAAe;IACfX,eAAe;IACfkB;EACJ,CAAG;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
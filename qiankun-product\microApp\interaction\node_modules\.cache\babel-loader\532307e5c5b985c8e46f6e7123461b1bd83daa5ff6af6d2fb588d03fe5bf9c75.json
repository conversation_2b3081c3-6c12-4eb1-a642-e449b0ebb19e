{"ast": null, "code": "import { createElementVNode as _createElementVNode, toDisplayString as _toDisplayString, createTextVNode as _createTextVNode, resolveComponent as _resolveComponent, withCtx as _withCtx, createVNode as _createVNode, openBlock as _openBlock, createElementBlock as _createElementBlock } from \"vue\";\nvar _hoisted_1 = {\n  class: \"BoxMessageDetails\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  var _component_global_info_item = _resolveComponent(\"global-info-item\");\n  var _component_global_info_line = _resolveComponent(\"global-info-line\");\n  var _component_global_info = _resolveComponent(\"global-info\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_cache[0] || (_cache[0] = _createElementVNode(\"div\", {\n    class: \"BoxMessageDetailsName\"\n  }, \"系统消息\", -1 /* HOISTED */)), _createVNode(_component_global_info, null, {\n    default: _withCtx(function () {\n      return [_createVNode(_component_global_info_line, null, {\n        default: _withCtx(function () {\n          return [_createVNode(_component_global_info_item, {\n            label: \"来源模块\"\n          }, {\n            default: _withCtx(function () {\n              return [_createTextVNode(_toDisplayString($setup.details.businessName), 1 /* TEXT */)];\n            }),\n            _: 1 /* STABLE */\n          }), _createVNode(_component_global_info_item, {\n            label: \"消息时间\"\n          }, {\n            default: _withCtx(function () {\n              return [_createTextVNode(_toDisplayString($setup.format($setup.details.createDate)), 1 /* TEXT */)];\n            }),\n            _: 1 /* STABLE */\n          })];\n        }),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_global_info_item, {\n        label: \"消息内容\"\n      }, {\n        default: _withCtx(function () {\n          return [_createTextVNode(_toDisplayString($setup.details.content), 1 /* TEXT */)];\n        }),\n        _: 1 /* STABLE */\n      })];\n    }),\n    _: 1 /* STABLE */\n  })]);\n}", "map": {"version": 3, "names": ["class", "_createElementBlock", "_hoisted_1", "_createElementVNode", "_createVNode", "_component_global_info", "default", "_withCtx", "_component_global_info_line", "_component_global_info_item", "label", "_createTextVNode", "_toDisplayString", "$setup", "details", "businessName", "_", "format", "createDate", "content"], "sources": ["D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\interaction\\src\\views\\BoxMessage\\BoxMessageDetails.vue"], "sourcesContent": ["<template>\r\n  <div class=\"BoxMessageDetails\">\r\n    <div class=\"BoxMessageDetailsName\">系统消息</div>\r\n    <global-info>\r\n      <global-info-line>\r\n        <global-info-item label=\"来源模块\">{{details.businessName}}</global-info-item>\r\n        <global-info-item label=\"消息时间\">{{format(details.createDate)}}</global-info-item>\r\n      </global-info-line>\r\n      <global-info-item label=\"消息内容\">{{details.content}}</global-info-item>\r\n    </global-info>\r\n  </div>\r\n</template>\r\n<script>\r\nexport default { name: 'BoxMessageDetails' }\r\n</script>\r\n<script setup>\r\nimport api from '@/api'\r\nimport { ref, onMounted } from 'vue'\r\nimport { format } from 'common/js/time.js'\r\nconst props = defineProps({ id: { type: String, default: '' } })\r\nconst details = ref({})\r\nonMounted(() => { boxMessageInfo() })\r\nconst boxMessageInfo = async () => {\r\n  const res = await api.boxMessageInfo({ detailId: props.id })\r\n  var { data } = res\r\n  details.value = data\r\n}\r\n</script>\r\n<style lang=\"scss\">\r\n.BoxMessageDetails {\r\n  width: 990px;\r\n  padding: 40px;\r\n  padding-top: 0;\r\n  .BoxMessageDetailsName {\r\n    font-size: var(--zy-title-font-size);\r\n    font-weight: bold;\r\n    color: var(--zy-el-color-primary);\r\n    border-bottom: 1px solid var(--zy-el-color-primary);\r\n    text-align: center;\r\n    padding: 20px 0;\r\n    margin-bottom: 20px;\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;EACOA,KAAK,EAAC;AAAmB;;;;;uBAA9BC,mBAAA,CASM,OATNC,UASM,G,0BARJC,mBAAA,CAA6C;IAAxCH,KAAK,EAAC;EAAuB,GAAC,MAAI,sBACvCI,YAAA,CAMcC,sBAAA;IATlBC,OAAA,EAAAC,QAAA,CAIM;MAAA,OAGmB,CAHnBH,YAAA,CAGmBI,2BAAA;QAPzBF,OAAA,EAAAC,QAAA,CAKQ;UAAA,OAA0E,CAA1EH,YAAA,CAA0EK,2BAAA;YAAxDC,KAAK,EAAC;UAAM;YALtCJ,OAAA,EAAAC,QAAA,CAKuC;cAAA,OAAwB,CAL/DI,gBAAA,CAAAC,gBAAA,CAKyCC,MAAA,CAAAC,OAAO,CAACC,YAAY,iB;;YAL7DC,CAAA;cAMQZ,YAAA,CAAgFK,2BAAA;YAA9DC,KAAK,EAAC;UAAM;YANtCJ,OAAA,EAAAC,QAAA,CAMuC;cAAA,OAA8B,CANrEI,gBAAA,CAAAC,gBAAA,CAMyCC,MAAA,CAAAI,MAAM,CAACJ,MAAA,CAAAC,OAAO,CAACI,UAAU,kB;;YANlEF,CAAA;;;QAAAA,CAAA;UAQMZ,YAAA,CAAqEK,2BAAA;QAAnDC,KAAK,EAAC;MAAM;QARpCJ,OAAA,EAAAC,QAAA,CAQqC;UAAA,OAAmB,CARxDI,gBAAA,CAAAC,gBAAA,CAQuCC,MAAA,CAAAC,OAAO,CAACK,OAAO,iB;;QARtDH,CAAA;;;IAAAA,CAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
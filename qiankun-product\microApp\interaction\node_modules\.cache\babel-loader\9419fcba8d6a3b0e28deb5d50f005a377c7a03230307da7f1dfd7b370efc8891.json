{"ast": null, "code": "import { toDisplayString as _toDisplayString, createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, resolveDirective as _resolveDirective, withDirectives as _withDirectives, vShow as _vShow, withModifiers as _withModifiers, Transition as _Transition, withCtx as _withCtx, createVNode as _createVNode } from \"vue\";\nvar _hoisted_1 = {\n  class: \"xyl-file-preview-head\"\n};\nvar _hoisted_2 = [\"title\"];\nvar _hoisted_3 = [\"src\"];\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  var _directive_loading = _resolveDirective(\"loading\");\n  return _openBlock(), _createElementBlock(\"div\", {\n    class: \"xyl-file-preview-wrapper\",\n    onClick: $setup.handleClose\n  }, [_createVNode(_Transition, {\n    name: \"el-zoom-in-bottom\",\n    persisted: \"\"\n  }, {\n    default: _withCtx(function () {\n      return [_withDirectives(_createElementVNode(\"div\", {\n        class: \"xyl-file-preview\",\n        onClick: _cache[0] || (_cache[0] = _withModifiers(function () {}, [\"stop\"]))\n      }, [_createElementVNode(\"div\", _hoisted_1, [_createElementVNode(\"div\", {\n        class: \"xyl-file-preview-name ellipsis\",\n        title: $setup.fileObj.fileName\n      }, _toDisplayString($setup.fileObj.fileName), 9 /* TEXT, PROPS */, _hoisted_2), _createElementVNode(\"div\", {\n        class: \"xyl-file-preview-button\"\n      }, [_createElementVNode(\"div\", {\n        class: \"xyl-file-preview-icon\",\n        onClick: $setup.handleDownload\n      }, _cache[1] || (_cache[1] = [_createElementVNode(\"svg\", {\n        t: \"1717642874928\",\n        viewBox: \"0 0 1024 1024\",\n        version: \"1.1\",\n        xmlns: \"http://www.w3.org/2000/svg\",\n        \"p-id\": \"10967\",\n        width: \"32\",\n        height: \"32\"\n      }, [_createElementVNode(\"path\", {\n        d: \"M502 662.3c3.1 3 7.1 4.4 11.1 4.4s8-1.5 11.1-4.4l211.2-201.6c2.6-2.5 4.1-5.5 4.7-8.6 1.8-9.2-5-19.2-15.8-19.2H595.5V165.8c0-8.8-7.1-16-16-16H446.6c-8.8 0-16 7.1-16 16V433H301.8c-10.9 0-17.6 10-15.8 19.2 0.6 3.1 2.1 6 4.7 8.6L502 662.3z\",\n        fill: \"#000000\",\n        \"p-id\": \"10968\"\n      }), _createElementVNode(\"path\", {\n        d: \"M859.6 653H626.7l-58.3 55.6c-15 14.3-34.6 22.2-55.3 22.2-20.7 0-40.4-7.9-55.3-22.2L399.5 653H166.6c-8.9 0-16.1 7.2-16.1 16v190.1c0 8.9 7.2 16.1 16.1 16.1h693c8.9 0 16-7.2 16-16.1v-190c0-8.9-7.2-16.1-16-16.1zM674.8 834.6c-19.5 0-35.2-15.8-35.2-35.2 0-19.5 15.8-35.2 35.2-35.2 19.5 0 35.2 15.8 35.2 35.2 0 19.4-15.8 35.2-35.2 35.2z m122.8 0c-19.5 0-35.2-15.8-35.2-35.2 0-19.5 15.8-35.2 35.2-35.2 19.5 0 35.2 15.8 35.2 35.2 0.1 19.4-15.7 35.2-35.2 35.2z\",\n        fill: \"#000000\",\n        \"p-id\": \"10969\"\n      })], -1 /* HOISTED */)])), _createElementVNode(\"div\", {\n        class: \"xyl-file-preview-icon\",\n        onClick: $setup.handleClose\n      }, _cache[2] || (_cache[2] = [_createElementVNode(\"svg\", {\n        t: \"1717642327842\",\n        viewBox: \"0 0 1024 1024\",\n        version: \"1.1\",\n        xmlns: \"http://www.w3.org/2000/svg\",\n        \"p-id\": \"5911\",\n        width: \"28\",\n        height: \"28\"\n      }, [_createElementVNode(\"path\", {\n        d: \"M512.922 63.583c248.335 0 449.712 201.384 449.712 449.71 0 248.333-201.377 449.702-449.712 449.702-248.333 0-449.71-201.369-449.71-449.702 0-248.326 201.377-449.71 449.71-449.71z m148.683 213.634l-35.351 61.247a206.8 206.8 0 0 1 34.37 27.739c37.359 37.351 60.475 88.96 60.475 145.955 0 57.004-23.117 108.607-60.475 145.965-37.344 37.352-88.945 60.469-145.949 60.469-56.987 0-108.606-23.117-145.965-60.469-37.359-37.359-60.461-88.962-60.461-145.965 0-56.995 23.117-108.605 60.461-145.955a209.04 209.04 0 0 1 27.762-23.286l-35.43-61.359a278.69 278.69 0 0 0-42.279 34.69c-50.156 50.148-81.18 119.417-81.18 195.91 0 76.504 31.041 145.773 81.18 195.929 50.139 50.139 119.408 81.166 195.912 81.166 76.502 0 145.771-31.026 195.91-81.166 50.156-50.156 81.182-119.425 81.182-195.929 0-76.494-31.026-145.763-81.182-195.91a277.704 277.704 0 0 0-48.98-39.031zM473.618 128.865v337.849h75.458V128.865h-75.458z\",\n        fill: \"#000000\",\n        \"p-id\": \"5912\"\n      })], -1 /* HOISTED */)]))])]), _withDirectives((_openBlock(), _createElementBlock(\"div\", {\n        class: \"xyl-file-preview-body\",\n        \"element-loading-spinner\": $setup.svg,\n        \"element-loading-svg-view-box\": \"-10, -10, 50, 50\",\n        \"element-loading-text\": \"文件加载中...\"\n      }, [_createElementVNode(\"iframe\", {\n        class: \"xyl-file-preview-iframe\",\n        frameborder: \"0\",\n        src: $setup.fileUrl\n      }, null, 8 /* PROPS */, _hoisted_3)])), [[_directive_loading, $setup.loading]])], 512 /* NEED_PATCH */), [[_vShow, $setup.elIsShow]])];\n    }),\n    _: 1 /* STABLE */\n  })]);\n}", "map": {"version": 3, "names": ["class", "_createElementBlock", "onClick", "$setup", "handleClose", "_createVNode", "_Transition", "name", "persisted", "default", "_withCtx", "_createElementVNode", "_cache", "_withModifiers", "_hoisted_1", "title", "fileObj", "fileName", "_hoisted_2", "handleDownload", "t", "viewBox", "version", "xmlns", "width", "height", "d", "fill", "svg", "frameborder", "src", "fileUrl", "_hoisted_3", "loading", "elIsShow", "_"], "sources": ["D:\\zy\\xm\\pc\\5.0\\qiankun-product\\common\\components\\xyl-file-preview\\xyl-file-preview.vue"], "sourcesContent": ["<template>\r\n  <div class=\"xyl-file-preview-wrapper\" @click=\"handleClose\">\r\n    <transition name=\"el-zoom-in-bottom\">\r\n      <div class=\"xyl-file-preview\" v-show=\"elIsShow\" @click.stop>\r\n        <div class=\"xyl-file-preview-head\">\r\n          <div class=\"xyl-file-preview-name ellipsis\" :title=\"fileObj.fileName\">{{ fileObj.fileName }}</div>\r\n          <div class=\"xyl-file-preview-button\">\r\n            <div class=\"xyl-file-preview-icon\" @click=\"handleDownload\">\r\n              <svg\r\n                t=\"1717642874928\"\r\n                viewBox=\"0 0 1024 1024\"\r\n                version=\"1.1\"\r\n                xmlns=\"http://www.w3.org/2000/svg\"\r\n                p-id=\"10967\"\r\n                width=\"32\"\r\n                height=\"32\">\r\n                <path\r\n                  d=\"M502 662.3c3.1 3 7.1 4.4 11.1 4.4s8-1.5 11.1-4.4l211.2-201.6c2.6-2.5 4.1-5.5 4.7-8.6 1.8-9.2-5-19.2-15.8-19.2H595.5V165.8c0-8.8-7.1-16-16-16H446.6c-8.8 0-16 7.1-16 16V433H301.8c-10.9 0-17.6 10-15.8 19.2 0.6 3.1 2.1 6 4.7 8.6L502 662.3z\"\r\n                  fill=\"#000000\"\r\n                  p-id=\"10968\"></path>\r\n                <path\r\n                  d=\"M859.6 653H626.7l-58.3 55.6c-15 14.3-34.6 22.2-55.3 22.2-20.7 0-40.4-7.9-55.3-22.2L399.5 653H166.6c-8.9 0-16.1 7.2-16.1 16v190.1c0 8.9 7.2 16.1 16.1 16.1h693c8.9 0 16-7.2 16-16.1v-190c0-8.9-7.2-16.1-16-16.1zM674.8 834.6c-19.5 0-35.2-15.8-35.2-35.2 0-19.5 15.8-35.2 35.2-35.2 19.5 0 35.2 15.8 35.2 35.2 0 19.4-15.8 35.2-35.2 35.2z m122.8 0c-19.5 0-35.2-15.8-35.2-35.2 0-19.5 15.8-35.2 35.2-35.2 19.5 0 35.2 15.8 35.2 35.2 0.1 19.4-15.7 35.2-35.2 35.2z\"\r\n                  fill=\"#000000\"\r\n                  p-id=\"10969\"></path>\r\n              </svg>\r\n            </div>\r\n            <div class=\"xyl-file-preview-icon\" @click=\"handleClose\">\r\n              <svg\r\n                t=\"1717642327842\"\r\n                viewBox=\"0 0 1024 1024\"\r\n                version=\"1.1\"\r\n                xmlns=\"http://www.w3.org/2000/svg\"\r\n                p-id=\"5911\"\r\n                width=\"28\"\r\n                height=\"28\">\r\n                <path\r\n                  d=\"M512.922 63.583c248.335 0 449.712 201.384 449.712 449.71 0 248.333-201.377 449.702-449.712 449.702-248.333 0-449.71-201.369-449.71-449.702 0-248.326 201.377-449.71 449.71-449.71z m148.683 213.634l-35.351 61.247a206.8 206.8 0 0 1 34.37 27.739c37.359 37.351 60.475 88.96 60.475 145.955 0 57.004-23.117 108.607-60.475 145.965-37.344 37.352-88.945 60.469-145.949 60.469-56.987 0-108.606-23.117-145.965-60.469-37.359-37.359-60.461-88.962-60.461-145.965 0-56.995 23.117-108.605 60.461-145.955a209.04 209.04 0 0 1 27.762-23.286l-35.43-61.359a278.69 278.69 0 0 0-42.279 34.69c-50.156 50.148-81.18 119.417-81.18 195.91 0 76.504 31.041 145.773 81.18 195.929 50.139 50.139 119.408 81.166 195.912 81.166 76.502 0 145.771-31.026 195.91-81.166 50.156-50.156 81.182-119.425 81.182-195.929 0-76.494-31.026-145.763-81.182-195.91a277.704 277.704 0 0 0-48.98-39.031zM473.618 128.865v337.849h75.458V128.865h-75.458z\"\r\n                  fill=\"#000000\"\r\n                  p-id=\"5912\"></path>\r\n              </svg>\r\n            </div>\r\n          </div>\r\n        </div>\r\n        <div\r\n          class=\"xyl-file-preview-body\"\r\n          v-loading=\"loading\"\r\n          :element-loading-spinner=\"svg\"\r\n          element-loading-svg-view-box=\"-10, -10, 50, 50\"\r\n          element-loading-text=\"文件加载中...\">\r\n          <iframe class=\"xyl-file-preview-iframe\" frameborder=\"0\" :src=\"fileUrl\"></iframe>\r\n        </div>\r\n      </div>\r\n    </transition>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nexport default { name: 'XylFilePreview' }\r\n</script>\r\n<script setup>\r\nimport api from '@/api'\r\nimport config from '../../config'\r\nimport { ref, computed, onMounted } from 'vue'\r\nimport store from '@/store'\r\nimport { downloadFile } from '../../config/MicroGlobal'\r\nimport { getFileInfo } from '../../js/file_preview.js'\r\nimport { ElMessage } from 'element-plus'\r\nconst props = defineProps({\r\n  fileObj: { type: Object, default: () => ({}) },\r\n  closeCallback: { type: Function }\r\n})\r\nconst svg = `<path class=\"path\" d=\"M 30 15 L 28 17 M 25.61 25.61 A 15 15, 0, 0, 1, 15 30 A 15 15, 0, 1, 1, 27.99 7.5 L 15 15\" style=\"stroke-width: 4px; fill: rgba(0, 0, 0, 0)\"/>`\r\nconst fileObj = computed(() => props.fileObj)\r\nconst elIsShow = ref(false)\r\nconst loading = ref(true)\r\nconst fileUrl = ref('')\r\nonMounted(() => {\r\n  file_preview(config.API_URL + '/file/preview/' + fileObj.value.fileId)\r\n  setTimeout(() => {\r\n    elIsShow.value = true\r\n  }, 168)\r\n})\r\nconst handleClose = () => {\r\n  elIsShow.value = false\r\n  setTimeout(() => {\r\n    props.closeCallback()\r\n  }, 168)\r\n}\r\nconst file_preview = async (url) => {\r\n  const res = await api.file_preview({ fileUrl: url })\r\n  const data = res?.data?.data\r\n  if (data) {\r\n    file_preview_url(url, data)\r\n  } else {\r\n    handleClose()\r\n    ElMessage({ type: 'error', message: '打开失败，请重试' })\r\n  }\r\n}\r\nconst file_preview_url = async (url, path) => {\r\n  const res = await api.file_preview_url({\r\n    srcRelativePath: path,\r\n    convertType: getFileInfo(path.substring(path.lastIndexOf('.'))) || '0',\r\n    isDccAsync: 1,\r\n    isCopy: 0,\r\n    noCache: 0,\r\n    fileUrl: url,\r\n    showFooter: 0,\r\n    isHeaderBar: 0,\r\n    htmlTitle: '详情',\r\n    acceptTracks: 0\r\n  })\r\n  const viewUrl = res?.data?.viewUrl\r\n  if (viewUrl) {\r\n    fileUrl.value = viewUrl\r\n    loading.value = false\r\n  } else {\r\n    handleClose()\r\n    ElMessage({ type: 'error', message: '打开失败，请重试' })\r\n  }\r\n}\r\nconst handleDownload = () => {\r\n  if (window.__POWERED_BY_QIANKUN__) {\r\n    downloadFile({\r\n      fileId: fileObj.value.fileId,\r\n      fileType: fileObj.value.fileType,\r\n      fileName: fileObj.value.fileName,\r\n      fileSize: fileObj.value.fileSize\r\n    })\r\n  } else {\r\n    console.log(window.__PUBLIC__)\r\n    if (window.__PUBLIC__) {\r\n      api.globalFileDownload(fileObj.value.fileId, fileObj.value.fileName)\r\n    } else {\r\n      store.commit('setDownloadFile', {\r\n        fileId: fileObj.value.fileId,\r\n        fileType: fileObj.value.fileType,\r\n        fileName: fileObj.value.fileName,\r\n        fileSize: fileObj.value.fileSize\r\n      })\r\n    }\r\n  }\r\n}\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n.xyl-file-preview-wrapper {\r\n  position: fixed;\r\n  top: 0;\r\n  bottom: 0;\r\n  right: 0;\r\n  left: 0;\r\n  background: rgba(0, 0, 0, 0.5);\r\n  display: flex;\r\n  align-items: flex-end;\r\n  justify-content: center;\r\n  z-index: 999;\r\n\r\n  .xyl-file-preview {\r\n    width: 100%;\r\n    height: 90%;\r\n    background: #fff;\r\n    border-radius: 10px 10px 0px 0px;\r\n\r\n    .xyl-file-preview-head {\r\n      width: 100%;\r\n      height: 52px;\r\n      box-shadow: 0px 2px 4px 1px rgba(0, 0, 0, 0.05);\r\n      padding: 0 32px 0 22px;\r\n      z-index: 999;\r\n      display: flex;\r\n      align-items: center;\r\n      justify-content: space-between;\r\n      border-bottom: 1px solid #eeeeee;\r\n\r\n      .xyl-file-preview-name {\r\n        cursor: pointer;\r\n        font-weight: bold;\r\n        font-size: var(--zy-name-font-size);\r\n      }\r\n\r\n      .xyl-file-preview-button {\r\n        display: flex;\r\n        align-items: center;\r\n\r\n        .xyl-file-preview-icon {\r\n          width: 36px;\r\n          height: 36px;\r\n          display: flex;\r\n          align-items: center;\r\n          justify-content: center;\r\n          cursor: pointer;\r\n          margin-left: 12px;\r\n\r\n          path {\r\n            fill: var(--zy-el-text-color-regular);\r\n          }\r\n        }\r\n      }\r\n    }\r\n\r\n    .xyl-file-preview-body {\r\n      width: 100%;\r\n      height: calc(100% - 52px);\r\n\r\n      .xyl-file-preview-iframe {\r\n        width: 100%;\r\n        height: 100%;\r\n        overflow: auto;\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;EAIaA,KAAK,EAAC;AAAuB;iBAJ1C;iBAAA;;;uBACEC,mBAAA,CAoDM;IApDDD,KAAK,EAAC,0BAA0B;IAAEE,OAAK,EAAEC,MAAA,CAAAC;MAC5CC,YAAA,CAkDaC,WAAA;IAlDDC,IAAI,EAAC,mBAAmB;IAApCC,SAkDa,EAlDb;;IAFJC,OAAA,EAAAC,QAAA,CAGM;MAAA,OAgDM,C,gBAhDNC,mBAAA,CAgDM;QAhDDX,KAAK,EAAC,kBAAkB;QAAoBE,OAAK,EAAAU,MAAA,QAAAA,MAAA,MAH5DC,cAAA,CAGsD,cAAW;UACzDF,mBAAA,CAsCM,OAtCNG,UAsCM,GArCJH,mBAAA,CAAkG;QAA7FX,KAAK,EAAC,gCAAgC;QAAEe,KAAK,EAAEZ,MAAA,CAAAa,OAAO,CAACC;0BAAad,MAAA,CAAAa,OAAO,CAACC,QAAQ,wBALnGC,UAAA,GAMUP,mBAAA,CAmCM;QAnCDX,KAAK,EAAC;MAAyB,IAClCW,mBAAA,CAkBM;QAlBDX,KAAK,EAAC,uBAAuB;QAAEE,OAAK,EAAEC,MAAA,CAAAgB;oCACzCR,mBAAA,CAgBM;QAfJS,CAAC,EAAC,eAAe;QACjBC,OAAO,EAAC,eAAe;QACvBC,OAAO,EAAC,KAAK;QACbC,KAAK,EAAC,4BAA4B;QAClC,MAAI,EAAC,OAAO;QACZC,KAAK,EAAC,IAAI;QACVC,MAAM,EAAC;UACPd,mBAAA,CAGsB;QAFpBe,CAAC,EAAC,6OAA6O;QAC/OC,IAAI,EAAC,SAAS;QACd,MAAI,EAAC;UACPhB,mBAAA,CAGsB;QAFpBe,CAAC,EAAC,ocAAoc;QACtcC,IAAI,EAAC,SAAS;QACd,MAAI,EAAC;iCAGXhB,mBAAA,CAcM;QAdDX,KAAK,EAAC,uBAAuB;QAAEE,OAAK,EAAEC,MAAA,CAAAC;oCACzCO,mBAAA,CAYM;QAXJS,CAAC,EAAC,eAAe;QACjBC,OAAO,EAAC,eAAe;QACvBC,OAAO,EAAC,KAAK;QACbC,KAAK,EAAC,4BAA4B;QAClC,MAAI,EAAC,MAAM;QACXC,KAAK,EAAC,IAAI;QACVC,MAAM,EAAC;UACPd,mBAAA,CAGqB;QAFnBe,CAAC,EAAC,i4BAAi4B;QACn4BC,IAAI,EAAC,SAAS;QACd,MAAI,EAAC;oEAKf1B,mBAAA,CAOM;QANJD,KAAK,EAAC,uBAAuB;QAE5B,yBAAuB,EAAEG,MAAA,CAAAyB,GAAG;QAC7B,8BAA4B,EAAC,kBAAkB;QAC/C,sBAAoB,EAAC;UACrBjB,mBAAA,CAAgF;QAAxEX,KAAK,EAAC,yBAAyB;QAAC6B,WAAW,EAAC,GAAG;QAAEC,GAAG,EAAE3B,MAAA,CAAA4B;8BAjDxEC,UAAA,E,0BA6CqB7B,MAAA,CAAA8B,OAAO,E,qCA1CgB9B,MAAA,CAAA+B,QAAQ,E;;IAHpDC,CAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
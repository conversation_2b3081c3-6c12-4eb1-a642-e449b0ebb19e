{"ast": null, "code": "import { resolveComponent as _resolveComponent, createVNode as _createVNode, withCtx as _withCtx, createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock } from \"vue\";\nvar _hoisted_1 = {\n  class: \"edit-record-info\"\n};\nvar _hoisted_2 = {\n  class: \"globalTable\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  var _component_el_table_column = _resolveComponent(\"el-table-column\");\n  var _component_el_table = _resolveComponent(\"el-table\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createElementVNode(\"div\", _hoisted_2, [_createVNode(_component_el_table, {\n    data: $setup.tableData\n  }, {\n    default: _withCtx(function () {\n      return [_createVNode(_component_el_table_column, {\n        label: \"字段名称\",\n        prop: \"editFieldName\",\n        \"show-overflow-tooltip\": \"\"\n      }), _createVNode(_component_el_table_column, {\n        label: \"修改前\",\n        prop: \"oldValue\",\n        \"show-overflow-tooltip\": \"\"\n      }), _createVNode(_component_el_table_column, {\n        label: \"修改后\",\n        prop: \"newValue\",\n        \"show-overflow-tooltip\": \"\"\n      })];\n    }),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"data\"])])]);\n}", "map": {"version": 3, "names": ["class", "_createElementBlock", "_hoisted_1", "_createElementVNode", "_hoisted_2", "_createVNode", "_component_el_table", "data", "$setup", "tableData", "default", "_withCtx", "_component_el_table_column", "label", "prop", "_"], "sources": ["D:\\zy\\xm\\pc\\5.0\\qiankun-product\\common\\components\\edit-record\\edit-record-info.vue"], "sourcesContent": ["<template>\r\n  <div class=\"edit-record-info\">\r\n    <div class=\"globalTable\">\r\n      <el-table :data=\"tableData\">\r\n        <el-table-column label=\"字段名称\" prop=\"editFieldName\" show-overflow-tooltip />\r\n        <el-table-column label=\"修改前\" prop=\"oldValue\" show-overflow-tooltip />\r\n        <el-table-column label=\"修改后\" prop=\"newValue\" show-overflow-tooltip />\r\n      </el-table>\r\n    </div>\r\n  </div>\r\n</template>\r\n<script>\r\nexport default { name: 'EditRecordInfo' }\r\n</script>\r\n<script setup>\r\nimport { onMounted } from 'vue'\r\nimport { GlobalTable } from 'common/js/GlobalTableTree.js'\r\nconst props = defineProps({ id: { type: String, default: '' } })\r\nconst { tableData, handleQuery } = GlobalTable({ tableApi: 'editRecordInfo', tableDataObj: { detailId: props.id } })\r\n\r\nonMounted(() => {\r\n  handleQuery()\r\n})\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n.edit-record-info {\r\n  width: 990px;\r\n  height: calc(85vh - 52px);\r\n  padding: 20px;\r\n\r\n  .globalTable {\r\n    width: 100%;\r\n    height: 100%;\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;EACOA,KAAK,EAAC;AAAkB;;EACtBA,KAAK,EAAC;AAAa;;;;uBAD1BC,mBAAA,CAQM,OARNC,UAQM,GAPJC,mBAAA,CAMM,OANNC,UAMM,GALJC,YAAA,CAIWC,mBAAA;IAJAC,IAAI,EAAEC,MAAA,CAAAC;EAAS;IAHhCC,OAAA,EAAAC,QAAA,CAIQ;MAAA,OAA2E,CAA3EN,YAAA,CAA2EO,0BAAA;QAA1DC,KAAK,EAAC,MAAM;QAACC,IAAI,EAAC,eAAe;QAAC,uBAAqB,EAArB;UACnDT,YAAA,CAAqEO,0BAAA;QAApDC,KAAK,EAAC,KAAK;QAACC,IAAI,EAAC,UAAU;QAAC,uBAAqB,EAArB;UAC7CT,YAAA,CAAqEO,0BAAA;QAApDC,KAAK,EAAC,KAAK;QAACC,IAAI,EAAC,UAAU;QAAC,uBAAqB,EAArB;;;IANrDC,CAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
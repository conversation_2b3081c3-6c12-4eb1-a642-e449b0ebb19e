{"ast": null, "code": "import { resolveComponent as _resolveComponent, createVNode as _createVNode, withCtx as _withCtx, createTextVNode as _createTextVNode, createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock } from \"vue\";\nvar _hoisted_1 = {\n  class: \"new-folder\"\n};\nvar _hoisted_2 = {\n  class: \"globalFormButton\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  var _component_el_tree_select = _resolveComponent(\"el-tree-select\");\n  var _component_el_form_item = _resolveComponent(\"el-form-item\");\n  var _component_el_input = _resolveComponent(\"el-input\");\n  var _component_el_button = _resolveComponent(\"el-button\");\n  var _component_el_form = _resolveComponent(\"el-form\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createVNode(_component_el_form, {\n    ref: \"formRef\",\n    model: $setup.form,\n    rules: $setup.rules,\n    inline: \"\",\n    \"label-position\": \"top\",\n    class: \"globalForm\"\n  }, {\n    default: _withCtx(function () {\n      return [_createVNode(_component_el_form_item, {\n        label: \"上级文件夹\"\n      }, {\n        default: _withCtx(function () {\n          return [_createVNode(_component_el_tree_select, {\n            modelValue: $setup.form.parentId,\n            \"onUpdate:modelValue\": _cache[0] || (_cache[0] = function ($event) {\n              return $setup.form.parentId = $event;\n            }),\n            data: $setup.folderData,\n            \"check-strictly\": \"\",\n            \"node-key\": \"id\",\n            \"render-after-expand\": false,\n            props: {\n              label: 'fileName'\n            },\n            clearable: \"\"\n          }, null, 8 /* PROPS */, [\"modelValue\", \"data\"])];\n        }),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_form_item, {\n        label: \"文件夹名称\",\n        prop: \"fileName\",\n        class: \"globalFormTitle\"\n      }, {\n        default: _withCtx(function () {\n          return [_createVNode(_component_el_input, {\n            modelValue: $setup.form.fileName,\n            \"onUpdate:modelValue\": _cache[1] || (_cache[1] = function ($event) {\n              return $setup.form.fileName = $event;\n            }),\n            placeholder: \"请输入文件夹名称\",\n            clearable: \"\"\n          }, null, 8 /* PROPS */, [\"modelValue\"])];\n        }),\n        _: 1 /* STABLE */\n      }), _createElementVNode(\"div\", _hoisted_2, [_createVNode(_component_el_button, {\n        type: \"primary\",\n        onClick: _cache[2] || (_cache[2] = function ($event) {\n          return $setup.submitForm($setup.formRef);\n        })\n      }, {\n        default: _withCtx(function () {\n          return _cache[3] || (_cache[3] = [_createTextVNode(\"提交\")]);\n        }),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_button, {\n        onClick: $setup.resetForm\n      }, {\n        default: _withCtx(function () {\n          return _cache[4] || (_cache[4] = [_createTextVNode(\"取消\")]);\n        }),\n        _: 1 /* STABLE */\n      })])];\n    }),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"model\", \"rules\"])]);\n}", "map": {"version": 3, "names": ["class", "_createElementBlock", "_hoisted_1", "_createVNode", "_component_el_form", "ref", "model", "$setup", "form", "rules", "inline", "default", "_withCtx", "_component_el_form_item", "label", "_component_el_tree_select", "modelValue", "parentId", "_cache", "$event", "data", "folderData", "props", "clearable", "_", "prop", "_component_el_input", "fileName", "placeholder", "_createElementVNode", "_hoisted_2", "_component_el_button", "type", "onClick", "submitForm", "formRef", "_createTextVNode", "resetForm"], "sources": ["D:\\zy\\xm\\pc\\5.0\\qiankun-product\\common\\components\\transfer-cloud-disk\\new-folder.vue"], "sourcesContent": ["<template>\r\n  <div class=\"new-folder\">\r\n    <el-form ref=\"formRef\" :model=\"form\" :rules=\"rules\" inline label-position=\"top\" class=\"globalForm\">\r\n      <el-form-item label=\"上级文件夹\">\r\n        <el-tree-select\r\n          v-model=\"form.parentId\"\r\n          :data=\"folderData\"\r\n          check-strictly\r\n          node-key=\"id\"\r\n          :render-after-expand=\"false\"\r\n          :props=\"{ label: 'fileName' }\"\r\n          clearable />\r\n      </el-form-item>\r\n      <el-form-item label=\"文件夹名称\" prop=\"fileName\" class=\"globalFormTitle\">\r\n        <el-input v-model=\"form.fileName\" placeholder=\"请输入文件夹名称\" clearable />\r\n      </el-form-item>\r\n      <div class=\"globalFormButton\">\r\n        <el-button type=\"primary\" @click=\"submitForm(formRef)\">提交</el-button>\r\n        <el-button @click=\"resetForm\">取消</el-button>\r\n      </div>\r\n    </el-form>\r\n  </div>\r\n</template>\r\n<script>\r\nexport default { name: 'NewFolder' }\r\n</script>\r\n<script setup>\r\nimport api from '@/api'\r\nimport { reactive, ref, onMounted } from 'vue'\r\nimport { ElMessage } from 'element-plus'\r\nconst props = defineProps({ id: { type: String, default: '' } })\r\nconst emit = defineEmits(['callback'])\r\n\r\nconst formRef = ref()\r\nconst form = reactive({\r\n  fileName: '', // 文件夹名称\r\n  parentId: '' // 上级文件夹\r\n})\r\nconst rules = reactive({\r\n  fileName: [{ required: true, message: '请输入文件夹名称', trigger: ['blur', 'change'] }]\r\n})\r\n\r\nconst folderData = ref([])\r\n\r\nonMounted(() => {\r\n  myFolderTree()\r\n})\r\nconst myFolderTree = async () => {\r\n  const res = await api.myFolderTree()\r\n  var { data } = res\r\n  folderData.value = data\r\n}\r\nconst submitForm = async (formEl) => {\r\n  if (!formEl) return\r\n  await formEl.validate((valid, fields) => {\r\n    if (valid) {\r\n      globalJson()\r\n    } else {\r\n      ElMessage({ type: 'warning', message: '请根据提示信息完善字段内容！' })\r\n    }\r\n  })\r\n}\r\nconst globalJson = async () => {\r\n  const { code } = await api.myNewFolder({\r\n    parentId: form.parentId || 0,\r\n    fileName: form.fileName\r\n  })\r\n  if (code === 200) {\r\n    ElMessage({ type: 'success', message: '新增成功' })\r\n    emit('callback')\r\n  }\r\n}\r\nconst resetForm = () => {\r\n  emit('callback')\r\n}\r\n</script>\r\n<style lang=\"scss\">\r\n.new-folder {\r\n  width: 680px;\r\n}\r\n</style>\r\n"], "mappings": ";;EACOA,KAAK,EAAC;AAAY;;EAedA,KAAK,EAAC;AAAkB;;;;;;;uBAfjCC,mBAAA,CAoBM,OApBNC,UAoBM,GAnBJC,YAAA,CAkBUC,kBAAA;IAlBDC,GAAG,EAAC,SAAS;IAAEC,KAAK,EAAEC,MAAA,CAAAC,IAAI;IAAGC,KAAK,EAAEF,MAAA,CAAAE,KAAK;IAAEC,MAAM,EAAN,EAAM;IAAC,gBAAc,EAAC,KAAK;IAACV,KAAK,EAAC;;IAF1FW,OAAA,EAAAC,QAAA,CAGM;MAAA,OASe,CATfT,YAAA,CASeU,uBAAA;QATDC,KAAK,EAAC;MAAO;QAHjCH,OAAA,EAAAC,QAAA,CAIQ;UAAA,OAOc,CAPdT,YAAA,CAOcY,yBAAA;YAXtBC,UAAA,EAKmBT,MAAA,CAAAC,IAAI,CAACS,QAAQ;YALhC,uBAAAC,MAAA,QAAAA,MAAA,gBAAAC,MAAA;cAAA,OAKmBZ,MAAA,CAAAC,IAAI,CAACS,QAAQ,GAAAE,MAAA;YAAA;YACrBC,IAAI,EAAEb,MAAA,CAAAc,UAAU;YACjB,gBAAc,EAAd,EAAc;YACd,UAAQ,EAAC,IAAI;YACZ,qBAAmB,EAAE,KAAK;YAC1BC,KAAK,EAAE;cAAAR,KAAA;YAAA,CAAqB;YAC7BS,SAAS,EAAT;;;QAXVC,CAAA;UAaMrB,YAAA,CAEeU,uBAAA;QAFDC,KAAK,EAAC,OAAO;QAACW,IAAI,EAAC,UAAU;QAACzB,KAAK,EAAC;;QAbxDW,OAAA,EAAAC,QAAA,CAcQ;UAAA,OAAqE,CAArET,YAAA,CAAqEuB,mBAAA;YAd7EV,UAAA,EAc2BT,MAAA,CAAAC,IAAI,CAACmB,QAAQ;YAdxC,uBAAAT,MAAA,QAAAA,MAAA,gBAAAC,MAAA;cAAA,OAc2BZ,MAAA,CAAAC,IAAI,CAACmB,QAAQ,GAAAR,MAAA;YAAA;YAAES,WAAW,EAAC,UAAU;YAACL,SAAS,EAAT;;;QAdjEC,CAAA;UAgBMK,mBAAA,CAGM,OAHNC,UAGM,GAFJ3B,YAAA,CAAqE4B,oBAAA;QAA1DC,IAAI,EAAC,SAAS;QAAEC,OAAK,EAAAf,MAAA,QAAAA,MAAA,gBAAAC,MAAA;UAAA,OAAEZ,MAAA,CAAA2B,UAAU,CAAC3B,MAAA,CAAA4B,OAAO;QAAA;;QAjB5DxB,OAAA,EAAAC,QAAA,CAiB+D;UAAA,OAAEM,MAAA,QAAAA,MAAA,OAjBjEkB,gBAAA,CAiB+D,IAAE,E;;QAjBjEZ,CAAA;UAkBQrB,YAAA,CAA4C4B,oBAAA;QAAhCE,OAAK,EAAE1B,MAAA,CAAA8B;MAAS;QAlBpC1B,OAAA,EAAAC,QAAA,CAkBsC;UAAA,OAAEM,MAAA,QAAAA,MAAA,OAlBxCkB,gBAAA,CAkBsC,IAAE,E;;QAlBxCZ,CAAA;;;IAAAA,CAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
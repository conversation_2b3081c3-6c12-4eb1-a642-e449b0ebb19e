{"ast": null, "code": "/**\n * This method returns a new empty object.\n *\n * @static\n * @memberOf _\n * @since 4.13.0\n * @category Util\n * @returns {Object} Returns the new empty object.\n * @example\n *\n * var objects = _.times(2, _.stubObject);\n *\n * console.log(objects);\n * // => [{}, {}]\n *\n * console.log(objects[0] === objects[1]);\n * // => false\n */\nfunction stubObject() {\n  return {};\n}\nexport default stubObject;", "map": {"version": 3, "names": ["stubObject"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/node_modules/.pnpm/lodash-es@4.17.21/node_modules/lodash-es/stubObject.js"], "sourcesContent": ["/**\n * This method returns a new empty object.\n *\n * @static\n * @memberOf _\n * @since 4.13.0\n * @category Util\n * @returns {Object} Returns the new empty object.\n * @example\n *\n * var objects = _.times(2, _.stubObject);\n *\n * console.log(objects);\n * // => [{}, {}]\n *\n * console.log(objects[0] === objects[1]);\n * // => false\n */\nfunction stubObject() {\n  return {};\n}\n\nexport default stubObject;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,UAAUA,CAAA,EAAG;EACpB,OAAO,CAAC,CAAC;AACX;AAEA,eAAeA,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
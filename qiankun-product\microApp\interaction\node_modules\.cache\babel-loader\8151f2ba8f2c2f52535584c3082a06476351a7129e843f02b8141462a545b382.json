{"ast": null, "code": "import Symbol from './_Symbol.js';\n\n/** Used to convert symbols to primitives and strings. */\nvar symbolProto = Symbol ? Symbol.prototype : undefined,\n  symbolValueOf = symbolProto ? symbolProto.valueOf : undefined;\n\n/**\n * Creates a clone of the `symbol` object.\n *\n * @private\n * @param {Object} symbol The symbol object to clone.\n * @returns {Object} Returns the cloned symbol object.\n */\nfunction cloneSymbol(symbol) {\n  return symbolValueOf ? Object(symbolValueOf.call(symbol)) : {};\n}\nexport default cloneSymbol;", "map": {"version": 3, "names": ["Symbol", "symbol<PERSON>roto", "prototype", "undefined", "symbolValueOf", "valueOf", "cloneSymbol", "symbol", "Object", "call"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/node_modules/.pnpm/lodash-es@4.17.21/node_modules/lodash-es/_cloneSymbol.js"], "sourcesContent": ["import Symbol from './_Symbol.js';\n\n/** Used to convert symbols to primitives and strings. */\nvar symbolProto = Symbol ? Symbol.prototype : undefined,\n    symbolValueOf = symbolProto ? symbolProto.valueOf : undefined;\n\n/**\n * Creates a clone of the `symbol` object.\n *\n * @private\n * @param {Object} symbol The symbol object to clone.\n * @returns {Object} Returns the cloned symbol object.\n */\nfunction cloneSymbol(symbol) {\n  return symbolValueOf ? Object(symbolValueOf.call(symbol)) : {};\n}\n\nexport default cloneSymbol;\n"], "mappings": "AAAA,OAAOA,MAAM,MAAM,cAAc;;AAEjC;AACA,IAAIC,WAAW,GAAGD,MAAM,GAAGA,MAAM,CAACE,SAAS,GAAGC,SAAS;EACnDC,aAAa,GAAGH,WAAW,GAAGA,WAAW,CAACI,OAAO,GAAGF,SAAS;;AAEjE;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASG,WAAWA,CAACC,MAAM,EAAE;EAC3B,OAAOH,aAAa,GAAGI,MAAM,CAACJ,aAAa,CAACK,IAAI,CAACF,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC;AAChE;AAEA,eAAeD,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
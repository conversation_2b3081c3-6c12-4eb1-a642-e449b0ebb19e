{"ast": null, "code": "import { ref, watch } from 'vue';\nvar __default__ = {\n  name: 'XylGlobalTableButtonItem'\n};\nexport default /*@__PURE__*/Object.assign(__default__, {\n  props: {\n    dataRow: {\n      type: Object,\n      default: function _default() {\n        return {};\n      }\n    },\n    data: {\n      type: Array,\n      default: function _default() {\n        return [];\n      }\n    },\n    max: {\n      type: Number,\n      default: 1\n    },\n    elWhetherDisabled: Function,\n    elWhetherShow: Function\n  },\n  emits: ['click'],\n  setup(__props, _ref) {\n    var __expose = _ref.expose,\n      __emit = _ref.emit;\n    __expose();\n    var props = __props;\n    var emit = __emit;\n    var dataOne = ref([]);\n    var dataTwo = ref([]);\n\n    /**\r\n     * @description: 按钮点击事件\r\n     * @return void\r\n     */\n    var handleButton = function handleButton(id) {\n      emit('click', props.dataRow, id);\n    };\n    var handleCommand = function handleCommand(_ref2) {\n      var id = _ref2.id;\n      emit('click', props.dataRow, id);\n    };\n    /**\r\n     * @description: 按钮禁用方法\r\n     * @return true 禁用  /  false 不禁用\r\n     */\n    var handleElWhetherDisabled = function handleElWhetherDisabled(id, type) {\n      if (!type) return false;\n      if (typeof props.elWhetherDisabled === 'function') {\n        return props.elWhetherDisabled(props.dataRow, id);\n      } else {\n        return false;\n      }\n    };\n    /**\r\n     * @description: 按钮显示方法\r\n     * @return true 显示  /  false 不显示\r\n     */\n    var handleElWhetherShow = function handleElWhetherShow(id, type) {\n      if (!type) return true;\n      if (typeof props.elWhetherShow === 'function') {\n        return props.elWhetherShow(props.dataRow, id);\n      } else {\n        return true;\n      }\n    };\n    var initButton = function initButton() {\n      var newTableData = [];\n      for (var index = 0; index < props.data.length; index++) {\n        var item = props.data[index];\n        if (handleElWhetherShow(item.id, item.whetherShow)) {\n          var disabled = handleElWhetherDisabled(item.id, item.whetherDisabled);\n          newTableData.push({\n            id: item.id,\n            name: item.name,\n            disabled: disabled\n          });\n        }\n      }\n      if (newTableData.length === props.max || newTableData.length === props.max + 1) {\n        dataOne.value = newTableData;\n        dataTwo.value = [];\n      } else {\n        dataOne.value = newTableData.slice(0, props.max);\n        dataTwo.value = newTableData.slice(props.max);\n      }\n    };\n    watch([function () {\n      return props.dataRow;\n    }, function () {\n      return props.data;\n    }], function () {\n      initButton();\n    }, {\n      immediate: true\n    });\n    var __returned__ = {\n      props,\n      emit,\n      dataOne,\n      dataTwo,\n      handleButton,\n      handleCommand,\n      handleElWhetherDisabled,\n      handleElWhetherShow,\n      initButton,\n      ref,\n      watch\n    };\n    Object.defineProperty(__returned__, '__isScriptSetup', {\n      enumerable: false,\n      value: true\n    });\n    return __returned__;\n  }\n});", "map": {"version": 3, "names": ["ref", "watch", "__default__", "name", "props", "__props", "emit", "__emit", "dataOne", "dataTwo", "handleButton", "id", "dataRow", "handleCommand", "_ref2", "handleElWhetherDisabled", "type", "elWhetherDisabled", "handleElWhetherShow", "elWhetherShow", "initButton", "newTableData", "index", "data", "length", "item", "whetherShow", "disabled", "whetherDisabled", "push", "max", "value", "slice", "immediate"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/common/components/xyl-global-table/xyl-global-table-button-item.vue"], "sourcesContent": ["<!--\r\n * @FileDescription: 权限控制按钮每一行\r\n * @Author: 谢育林\r\n * @Date: 2024-4-2\r\n * @LastEditors: 谢育林\r\n * @LastEditTime: 2024-4-2\r\n -->\r\n<template>\r\n  <el-button\r\n    v-for=\"item in dataOne\"\r\n    :key=\"item.id\"\r\n    :disabled=\"item.disabled\"\r\n    @click=\"handleButton(item.id)\"\r\n    type=\"primary\"\r\n    plain>\r\n    {{ item.name }}\r\n  </el-button>\r\n  <el-dropdown v-if=\"dataTwo.length\" @command=\"handleCommand\" trigger=\"click\">\r\n    <el-button type=\"primary\" plain>管理</el-button>\r\n    <template #dropdown>\r\n      <el-dropdown-menu>\r\n        <el-dropdown-item v-for=\"item in dataTwo\" :key=\"item.id\" :disabled=\"item.disabled\" :command=\"{ id: item.id }\">\r\n          {{ item.name }}\r\n        </el-dropdown-item>\r\n      </el-dropdown-menu>\r\n    </template>\r\n  </el-dropdown>\r\n</template>\r\n<script>\r\nexport default { name: 'XylGlobalTableButtonItem' }\r\n</script>\r\n<script setup>\r\nimport { ref, watch } from 'vue'\r\nconst props = defineProps({\r\n  dataRow: { type: Object, default: () => ({}) },\r\n  data: { type: Array, default: () => [] },\r\n  max: { type: Number, default: 1 },\r\n  elWhetherDisabled: Function,\r\n  elWhetherShow: Function\r\n})\r\nconst emit = defineEmits(['click'])\r\n\r\nconst dataOne = ref([])\r\nconst dataTwo = ref([])\r\n\r\n/**\r\n * @description: 按钮点击事件\r\n * @return void\r\n */\r\nconst handleButton = (id) => {\r\n  emit('click', props.dataRow, id)\r\n}\r\nconst handleCommand = ({ id }) => {\r\n  emit('click', props.dataRow, id)\r\n}\r\n/**\r\n * @description: 按钮禁用方法\r\n * @return true 禁用  /  false 不禁用\r\n */\r\nconst handleElWhetherDisabled = (id, type) => {\r\n  if (!type) return false\r\n  if (typeof props.elWhetherDisabled === 'function') {\r\n    return props.elWhetherDisabled(props.dataRow, id)\r\n  } else {\r\n    return false\r\n  }\r\n}\r\n/**\r\n * @description: 按钮显示方法\r\n * @return true 显示  /  false 不显示\r\n */\r\nconst handleElWhetherShow = (id, type) => {\r\n  if (!type) return true\r\n  if (typeof props.elWhetherShow === 'function') {\r\n    return props.elWhetherShow(props.dataRow, id)\r\n  } else {\r\n    return true\r\n  }\r\n}\r\nconst initButton = () => {\r\n  var newTableData = []\r\n  for (let index = 0; index < props.data.length; index++) {\r\n    const item = props.data[index]\r\n    if (handleElWhetherShow(item.id, item.whetherShow)) {\r\n      const disabled = handleElWhetherDisabled(item.id, item.whetherDisabled)\r\n      newTableData.push({ id: item.id, name: item.name, disabled: disabled })\r\n    }\r\n  }\r\n  if (newTableData.length === props.max || newTableData.length === props.max + 1) {\r\n    dataOne.value = newTableData\r\n    dataTwo.value = []\r\n  } else {\r\n    dataOne.value = newTableData.slice(0, props.max)\r\n    dataTwo.value = newTableData.slice(props.max)\r\n  }\r\n}\r\nwatch(\r\n  [() => props.dataRow, () => props.data],\r\n  () => {\r\n    initButton()\r\n  },\r\n  { immediate: true }\r\n)\r\n</script>\r\n"], "mappings": "AAgCA,SAASA,GAAG,EAAEC,KAAK,QAAQ,KAAK;AAHhC,IAAAC,WAAA,GAAe;EAAEC,IAAI,EAAE;AAA2B,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;IAInD,IAAMC,KAAK,GAAGC,OAMZ;IACF,IAAMC,IAAI,GAAGC,MAAsB;IAEnC,IAAMC,OAAO,GAAGR,GAAG,CAAC,EAAE,CAAC;IACvB,IAAMS,OAAO,GAAGT,GAAG,CAAC,EAAE,CAAC;;IAEvB;AACA;AACA;AACA;IACA,IAAMU,YAAY,GAAG,SAAfA,YAAYA,CAAIC,EAAE,EAAK;MAC3BL,IAAI,CAAC,OAAO,EAAEF,KAAK,CAACQ,OAAO,EAAED,EAAE,CAAC;IAClC,CAAC;IACD,IAAME,aAAa,GAAG,SAAhBA,aAAaA,CAAAC,KAAA,EAAe;MAAA,IAATH,EAAE,GAAAG,KAAA,CAAFH,EAAE;MACzBL,IAAI,CAAC,OAAO,EAAEF,KAAK,CAACQ,OAAO,EAAED,EAAE,CAAC;IAClC,CAAC;IACD;AACA;AACA;AACA;IACA,IAAMI,uBAAuB,GAAG,SAA1BA,uBAAuBA,CAAIJ,EAAE,EAAEK,IAAI,EAAK;MAC5C,IAAI,CAACA,IAAI,EAAE,OAAO,KAAK;MACvB,IAAI,OAAOZ,KAAK,CAACa,iBAAiB,KAAK,UAAU,EAAE;QACjD,OAAOb,KAAK,CAACa,iBAAiB,CAACb,KAAK,CAACQ,OAAO,EAAED,EAAE,CAAC;MACnD,CAAC,MAAM;QACL,OAAO,KAAK;MACd;IACF,CAAC;IACD;AACA;AACA;AACA;IACA,IAAMO,mBAAmB,GAAG,SAAtBA,mBAAmBA,CAAIP,EAAE,EAAEK,IAAI,EAAK;MACxC,IAAI,CAACA,IAAI,EAAE,OAAO,IAAI;MACtB,IAAI,OAAOZ,KAAK,CAACe,aAAa,KAAK,UAAU,EAAE;QAC7C,OAAOf,KAAK,CAACe,aAAa,CAACf,KAAK,CAACQ,OAAO,EAAED,EAAE,CAAC;MAC/C,CAAC,MAAM;QACL,OAAO,IAAI;MACb;IACF,CAAC;IACD,IAAMS,UAAU,GAAG,SAAbA,UAAUA,CAAA,EAAS;MACvB,IAAIC,YAAY,GAAG,EAAE;MACrB,KAAK,IAAIC,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAGlB,KAAK,CAACmB,IAAI,CAACC,MAAM,EAAEF,KAAK,EAAE,EAAE;QACtD,IAAMG,IAAI,GAAGrB,KAAK,CAACmB,IAAI,CAACD,KAAK,CAAC;QAC9B,IAAIJ,mBAAmB,CAACO,IAAI,CAACd,EAAE,EAAEc,IAAI,CAACC,WAAW,CAAC,EAAE;UAClD,IAAMC,QAAQ,GAAGZ,uBAAuB,CAACU,IAAI,CAACd,EAAE,EAAEc,IAAI,CAACG,eAAe,CAAC;UACvEP,YAAY,CAACQ,IAAI,CAAC;YAAElB,EAAE,EAAEc,IAAI,CAACd,EAAE;YAAER,IAAI,EAAEsB,IAAI,CAACtB,IAAI;YAAEwB,QAAQ,EAAEA;UAAS,CAAC,CAAC;QACzE;MACF;MACA,IAAIN,YAAY,CAACG,MAAM,KAAKpB,KAAK,CAAC0B,GAAG,IAAIT,YAAY,CAACG,MAAM,KAAKpB,KAAK,CAAC0B,GAAG,GAAG,CAAC,EAAE;QAC9EtB,OAAO,CAACuB,KAAK,GAAGV,YAAY;QAC5BZ,OAAO,CAACsB,KAAK,GAAG,EAAE;MACpB,CAAC,MAAM;QACLvB,OAAO,CAACuB,KAAK,GAAGV,YAAY,CAACW,KAAK,CAAC,CAAC,EAAE5B,KAAK,CAAC0B,GAAG,CAAC;QAChDrB,OAAO,CAACsB,KAAK,GAAGV,YAAY,CAACW,KAAK,CAAC5B,KAAK,CAAC0B,GAAG,CAAC;MAC/C;IACF,CAAC;IACD7B,KAAK,CACH,CAAC;MAAA,OAAMG,KAAK,CAACQ,OAAO;IAAA,GAAE;MAAA,OAAMR,KAAK,CAACmB,IAAI;IAAA,EAAC,EACvC,YAAM;MACJH,UAAU,CAAC,CAAC;IACd,CAAC,EACD;MAAEa,SAAS,EAAE;IAAK,CACpB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "/**\n * A specialized version of `_.reduceRight` for arrays without support for\n * iteratee shorthands.\n *\n * @private\n * @param {Array} [array] The array to iterate over.\n * @param {Function} iteratee The function invoked per iteration.\n * @param {*} [accumulator] The initial value.\n * @param {boolean} [initAccum] Specify using the last element of `array` as\n *  the initial value.\n * @returns {*} Returns the accumulated value.\n */\nfunction arrayReduceRight(array, iteratee, accumulator, initAccum) {\n  var length = array == null ? 0 : array.length;\n  if (initAccum && length) {\n    accumulator = array[--length];\n  }\n  while (length--) {\n    accumulator = iteratee(accumulator, array[length], length, array);\n  }\n  return accumulator;\n}\nexport default arrayReduceRight;", "map": {"version": 3, "names": ["arrayReduceRight", "array", "iteratee", "accumulator", "initAccum", "length"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/node_modules/.pnpm/lodash-es@4.17.21/node_modules/lodash-es/_arrayReduceRight.js"], "sourcesContent": ["/**\n * A specialized version of `_.reduceRight` for arrays without support for\n * iteratee shorthands.\n *\n * @private\n * @param {Array} [array] The array to iterate over.\n * @param {Function} iteratee The function invoked per iteration.\n * @param {*} [accumulator] The initial value.\n * @param {boolean} [initAccum] Specify using the last element of `array` as\n *  the initial value.\n * @returns {*} Returns the accumulated value.\n */\nfunction arrayReduceRight(array, iteratee, accumulator, initAccum) {\n  var length = array == null ? 0 : array.length;\n  if (initAccum && length) {\n    accumulator = array[--length];\n  }\n  while (length--) {\n    accumulator = iteratee(accumulator, array[length], length, array);\n  }\n  return accumulator;\n}\n\nexport default arrayReduceRight;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,gBAAgBA,CAACC,KAAK,EAAEC,QAAQ,EAAEC,WAAW,EAAEC,SAAS,EAAE;EACjE,IAAIC,MAAM,GAAGJ,KAAK,IAAI,IAAI,GAAG,CAAC,GAAGA,KAAK,CAACI,MAAM;EAC7C,IAAID,SAAS,IAAIC,MAAM,EAAE;IACvBF,WAAW,GAAGF,KAAK,CAAC,EAAEI,MAAM,CAAC;EAC/B;EACA,OAAOA,MAAM,EAAE,EAAE;IACfF,WAAW,GAAGD,QAAQ,CAACC,WAAW,EAAEF,KAAK,CAACI,MAAM,CAAC,EAAEA,MAAM,EAAEJ,KAAK,CAAC;EACnE;EACA,OAAOE,WAAW;AACpB;AAEA,eAAeH,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
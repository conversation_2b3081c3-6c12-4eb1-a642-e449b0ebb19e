{"ast": null, "code": "import Lazy<PERSON>rapper from './_LazyWrapper.js';\nimport copyArray from './_copyArray.js';\n\n/**\n * Creates a clone of the lazy wrapper object.\n *\n * @private\n * @name clone\n * @memberOf LazyWrapper\n * @returns {Object} Returns the cloned `<PERSON><PERSON>Wrapper` object.\n */\nfunction lazyClone() {\n  var result = new LazyWrapper(this.__wrapped__);\n  result.__actions__ = copyArray(this.__actions__);\n  result.__dir__ = this.__dir__;\n  result.__filtered__ = this.__filtered__;\n  result.__iteratees__ = copyArray(this.__iteratees__);\n  result.__takeCount__ = this.__takeCount__;\n  result.__views__ = copyArray(this.__views__);\n  return result;\n}\nexport default lazyClone;", "map": {"version": 3, "names": ["LazyWrapper", "copyArray", "lazy<PERSON>lone", "result", "__wrapped__", "__actions__", "__dir__", "__filtered__", "__iteratees__", "__takeCount__", "__views__"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/node_modules/.pnpm/lodash-es@4.17.21/node_modules/lodash-es/_lazyClone.js"], "sourcesContent": ["import Lazy<PERSON>rapper from './_LazyWrapper.js';\nimport copyArray from './_copyArray.js';\n\n/**\n * Creates a clone of the lazy wrapper object.\n *\n * @private\n * @name clone\n * @memberOf LazyWrapper\n * @returns {Object} Returns the cloned `<PERSON><PERSON>Wrapper` object.\n */\nfunction lazyClone() {\n  var result = new LazyWrapper(this.__wrapped__);\n  result.__actions__ = copyArray(this.__actions__);\n  result.__dir__ = this.__dir__;\n  result.__filtered__ = this.__filtered__;\n  result.__iteratees__ = copyArray(this.__iteratees__);\n  result.__takeCount__ = this.__takeCount__;\n  result.__views__ = copyArray(this.__views__);\n  return result;\n}\n\nexport default lazyClone;\n"], "mappings": "AAAA,OAAOA,WAAW,MAAM,mBAAmB;AAC3C,OAAOC,SAAS,MAAM,iBAAiB;;AAEvC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,SAASA,CAAA,EAAG;EACnB,IAAIC,MAAM,GAAG,IAAIH,WAAW,CAAC,IAAI,CAACI,WAAW,CAAC;EAC9CD,MAAM,CAACE,WAAW,GAAGJ,SAAS,CAAC,IAAI,CAACI,WAAW,CAAC;EAChDF,MAAM,CAACG,OAAO,GAAG,IAAI,CAACA,OAAO;EAC7BH,MAAM,CAACI,YAAY,GAAG,IAAI,CAACA,YAAY;EACvCJ,MAAM,CAACK,aAAa,GAAGP,SAAS,CAAC,IAAI,CAACO,aAAa,CAAC;EACpDL,MAAM,CAACM,aAAa,GAAG,IAAI,CAACA,aAAa;EACzCN,MAAM,CAACO,SAAS,GAAGT,SAAS,CAAC,IAAI,CAACS,SAAS,CAAC;EAC5C,OAAOP,MAAM;AACf;AAEA,eAAeD,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
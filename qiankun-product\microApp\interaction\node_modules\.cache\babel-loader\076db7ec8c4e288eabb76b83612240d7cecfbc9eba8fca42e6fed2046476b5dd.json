{"ast": null, "code": "import arraySample from './_arraySample.js';\nimport baseSample from './_baseSample.js';\nimport isArray from './isArray.js';\n\n/**\n * Gets a random element from `collection`.\n *\n * @static\n * @memberOf _\n * @since 2.0.0\n * @category Collection\n * @param {Array|Object} collection The collection to sample.\n * @returns {*} Returns the random element.\n * @example\n *\n * _.sample([1, 2, 3, 4]);\n * // => 2\n */\nfunction sample(collection) {\n  var func = isArray(collection) ? arraySample : baseSample;\n  return func(collection);\n}\nexport default sample;", "map": {"version": 3, "names": ["arraySample", "baseSample", "isArray", "sample", "collection", "func"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/node_modules/.pnpm/lodash-es@4.17.21/node_modules/lodash-es/sample.js"], "sourcesContent": ["import arraySample from './_arraySample.js';\nimport baseSample from './_baseSample.js';\nimport isArray from './isArray.js';\n\n/**\n * Gets a random element from `collection`.\n *\n * @static\n * @memberOf _\n * @since 2.0.0\n * @category Collection\n * @param {Array|Object} collection The collection to sample.\n * @returns {*} Returns the random element.\n * @example\n *\n * _.sample([1, 2, 3, 4]);\n * // => 2\n */\nfunction sample(collection) {\n  var func = isArray(collection) ? arraySample : baseSample;\n  return func(collection);\n}\n\nexport default sample;\n"], "mappings": "AAAA,OAAOA,WAAW,MAAM,mBAAmB;AAC3C,OAAOC,UAAU,MAAM,kBAAkB;AACzC,OAAOC,OAAO,MAAM,cAAc;;AAElC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,MAAMA,CAACC,UAAU,EAAE;EAC1B,IAAIC,IAAI,GAAGH,OAAO,CAACE,UAAU,CAAC,GAAGJ,WAAW,GAAGC,UAAU;EACzD,OAAOI,IAAI,CAACD,UAAU,CAAC;AACzB;AAEA,eAAeD,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
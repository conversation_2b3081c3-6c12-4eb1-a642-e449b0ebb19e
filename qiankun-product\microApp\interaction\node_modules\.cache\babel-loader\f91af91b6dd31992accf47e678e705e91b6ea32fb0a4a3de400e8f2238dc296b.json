{"ast": null, "code": "function _regeneratorRuntime() { \"use strict\"; /*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */ _regeneratorRuntime = function _regeneratorRuntime() { return e; }; var t, e = {}, r = Object.prototype, n = r.hasOwnProperty, o = Object.defineProperty || function (t, e, r) { t[e] = r.value; }, i = \"function\" == typeof Symbol ? Symbol : {}, a = i.iterator || \"@@iterator\", c = i.asyncIterator || \"@@asyncIterator\", u = i.toStringTag || \"@@toStringTag\"; function define(t, e, r) { return Object.defineProperty(t, e, { value: r, enumerable: !0, configurable: !0, writable: !0 }), t[e]; } try { define({}, \"\"); } catch (t) { define = function define(t, e, r) { return t[e] = r; }; } function wrap(t, e, r, n) { var i = e && e.prototype instanceof Generator ? e : Generator, a = Object.create(i.prototype), c = new Context(n || []); return o(a, \"_invoke\", { value: makeInvokeMethod(t, r, c) }), a; } function tryCatch(t, e, r) { try { return { type: \"normal\", arg: t.call(e, r) }; } catch (t) { return { type: \"throw\", arg: t }; } } e.wrap = wrap; var h = \"suspendedStart\", l = \"suspendedYield\", f = \"executing\", s = \"completed\", y = {}; function Generator() {} function GeneratorFunction() {} function GeneratorFunctionPrototype() {} var p = {}; define(p, a, function () { return this; }); var d = Object.getPrototypeOf, v = d && d(d(values([]))); v && v !== r && n.call(v, a) && (p = v); var g = GeneratorFunctionPrototype.prototype = Generator.prototype = Object.create(p); function defineIteratorMethods(t) { [\"next\", \"throw\", \"return\"].forEach(function (e) { define(t, e, function (t) { return this._invoke(e, t); }); }); } function AsyncIterator(t, e) { function invoke(r, o, i, a) { var c = tryCatch(t[r], t, o); if (\"throw\" !== c.type) { var u = c.arg, h = u.value; return h && \"object\" == typeof h && n.call(h, \"__await\") ? e.resolve(h.__await).then(function (t) { invoke(\"next\", t, i, a); }, function (t) { invoke(\"throw\", t, i, a); }) : e.resolve(h).then(function (t) { u.value = t, i(u); }, function (t) { return invoke(\"throw\", t, i, a); }); } a(c.arg); } var r; o(this, \"_invoke\", { value: function value(t, n) { function callInvokeWithMethodAndArg() { return new e(function (e, r) { invoke(t, n, e, r); }); } return r = r ? r.then(callInvokeWithMethodAndArg, callInvokeWithMethodAndArg) : callInvokeWithMethodAndArg(); } }); } function makeInvokeMethod(e, r, n) { var o = h; return function (i, a) { if (o === f) throw Error(\"Generator is already running\"); if (o === s) { if (\"throw\" === i) throw a; return { value: t, done: !0 }; } for (n.method = i, n.arg = a;;) { var c = n.delegate; if (c) { var u = maybeInvokeDelegate(c, n); if (u) { if (u === y) continue; return u; } } if (\"next\" === n.method) n.sent = n._sent = n.arg;else if (\"throw\" === n.method) { if (o === h) throw o = s, n.arg; n.dispatchException(n.arg); } else \"return\" === n.method && n.abrupt(\"return\", n.arg); o = f; var p = tryCatch(e, r, n); if (\"normal\" === p.type) { if (o = n.done ? s : l, p.arg === y) continue; return { value: p.arg, done: n.done }; } \"throw\" === p.type && (o = s, n.method = \"throw\", n.arg = p.arg); } }; } function maybeInvokeDelegate(e, r) { var n = r.method, o = e.iterator[n]; if (o === t) return r.delegate = null, \"throw\" === n && e.iterator.return && (r.method = \"return\", r.arg = t, maybeInvokeDelegate(e, r), \"throw\" === r.method) || \"return\" !== n && (r.method = \"throw\", r.arg = new TypeError(\"The iterator does not provide a '\" + n + \"' method\")), y; var i = tryCatch(o, e.iterator, r.arg); if (\"throw\" === i.type) return r.method = \"throw\", r.arg = i.arg, r.delegate = null, y; var a = i.arg; return a ? a.done ? (r[e.resultName] = a.value, r.next = e.nextLoc, \"return\" !== r.method && (r.method = \"next\", r.arg = t), r.delegate = null, y) : a : (r.method = \"throw\", r.arg = new TypeError(\"iterator result is not an object\"), r.delegate = null, y); } function pushTryEntry(t) { var e = { tryLoc: t[0] }; 1 in t && (e.catchLoc = t[1]), 2 in t && (e.finallyLoc = t[2], e.afterLoc = t[3]), this.tryEntries.push(e); } function resetTryEntry(t) { var e = t.completion || {}; e.type = \"normal\", delete e.arg, t.completion = e; } function Context(t) { this.tryEntries = [{ tryLoc: \"root\" }], t.forEach(pushTryEntry, this), this.reset(!0); } function values(e) { if (e || \"\" === e) { var r = e[a]; if (r) return r.call(e); if (\"function\" == typeof e.next) return e; if (!isNaN(e.length)) { var o = -1, i = function next() { for (; ++o < e.length;) if (n.call(e, o)) return next.value = e[o], next.done = !1, next; return next.value = t, next.done = !0, next; }; return i.next = i; } } throw new TypeError(typeof e + \" is not iterable\"); } return GeneratorFunction.prototype = GeneratorFunctionPrototype, o(g, \"constructor\", { value: GeneratorFunctionPrototype, configurable: !0 }), o(GeneratorFunctionPrototype, \"constructor\", { value: GeneratorFunction, configurable: !0 }), GeneratorFunction.displayName = define(GeneratorFunctionPrototype, u, \"GeneratorFunction\"), e.isGeneratorFunction = function (t) { var e = \"function\" == typeof t && t.constructor; return !!e && (e === GeneratorFunction || \"GeneratorFunction\" === (e.displayName || e.name)); }, e.mark = function (t) { return Object.setPrototypeOf ? Object.setPrototypeOf(t, GeneratorFunctionPrototype) : (t.__proto__ = GeneratorFunctionPrototype, define(t, u, \"GeneratorFunction\")), t.prototype = Object.create(g), t; }, e.awrap = function (t) { return { __await: t }; }, defineIteratorMethods(AsyncIterator.prototype), define(AsyncIterator.prototype, c, function () { return this; }), e.AsyncIterator = AsyncIterator, e.async = function (t, r, n, o, i) { void 0 === i && (i = Promise); var a = new AsyncIterator(wrap(t, r, n, o), i); return e.isGeneratorFunction(r) ? a : a.next().then(function (t) { return t.done ? t.value : a.next(); }); }, defineIteratorMethods(g), define(g, u, \"Generator\"), define(g, a, function () { return this; }), define(g, \"toString\", function () { return \"[object Generator]\"; }), e.keys = function (t) { var e = Object(t), r = []; for (var n in e) r.push(n); return r.reverse(), function next() { for (; r.length;) { var t = r.pop(); if (t in e) return next.value = t, next.done = !1, next; } return next.done = !0, next; }; }, e.values = values, Context.prototype = { constructor: Context, reset: function reset(e) { if (this.prev = 0, this.next = 0, this.sent = this._sent = t, this.done = !1, this.delegate = null, this.method = \"next\", this.arg = t, this.tryEntries.forEach(resetTryEntry), !e) for (var r in this) \"t\" === r.charAt(0) && n.call(this, r) && !isNaN(+r.slice(1)) && (this[r] = t); }, stop: function stop() { this.done = !0; var t = this.tryEntries[0].completion; if (\"throw\" === t.type) throw t.arg; return this.rval; }, dispatchException: function dispatchException(e) { if (this.done) throw e; var r = this; function handle(n, o) { return a.type = \"throw\", a.arg = e, r.next = n, o && (r.method = \"next\", r.arg = t), !!o; } for (var o = this.tryEntries.length - 1; o >= 0; --o) { var i = this.tryEntries[o], a = i.completion; if (\"root\" === i.tryLoc) return handle(\"end\"); if (i.tryLoc <= this.prev) { var c = n.call(i, \"catchLoc\"), u = n.call(i, \"finallyLoc\"); if (c && u) { if (this.prev < i.catchLoc) return handle(i.catchLoc, !0); if (this.prev < i.finallyLoc) return handle(i.finallyLoc); } else if (c) { if (this.prev < i.catchLoc) return handle(i.catchLoc, !0); } else { if (!u) throw Error(\"try statement without catch or finally\"); if (this.prev < i.finallyLoc) return handle(i.finallyLoc); } } } }, abrupt: function abrupt(t, e) { for (var r = this.tryEntries.length - 1; r >= 0; --r) { var o = this.tryEntries[r]; if (o.tryLoc <= this.prev && n.call(o, \"finallyLoc\") && this.prev < o.finallyLoc) { var i = o; break; } } i && (\"break\" === t || \"continue\" === t) && i.tryLoc <= e && e <= i.finallyLoc && (i = null); var a = i ? i.completion : {}; return a.type = t, a.arg = e, i ? (this.method = \"next\", this.next = i.finallyLoc, y) : this.complete(a); }, complete: function complete(t, e) { if (\"throw\" === t.type) throw t.arg; return \"break\" === t.type || \"continue\" === t.type ? this.next = t.arg : \"return\" === t.type ? (this.rval = this.arg = t.arg, this.method = \"return\", this.next = \"end\") : \"normal\" === t.type && e && (this.next = e), y; }, finish: function finish(t) { for (var e = this.tryEntries.length - 1; e >= 0; --e) { var r = this.tryEntries[e]; if (r.finallyLoc === t) return this.complete(r.completion, r.afterLoc), resetTryEntry(r), y; } }, catch: function _catch(t) { for (var e = this.tryEntries.length - 1; e >= 0; --e) { var r = this.tryEntries[e]; if (r.tryLoc === t) { var n = r.completion; if (\"throw\" === n.type) { var o = n.arg; resetTryEntry(r); } return o; } } throw Error(\"illegal catch attempt\"); }, delegateYield: function delegateYield(e, r, n) { return this.delegate = { iterator: values(e), resultName: r, nextLoc: n }, \"next\" === this.method && (this.arg = t), y; } }, e; }\nfunction asyncGeneratorStep(n, t, e, r, o, a, c) { try { var i = n[a](c), u = i.value; } catch (n) { return void e(n); } i.done ? t(u) : Promise.resolve(u).then(r, o); }\nfunction _asyncToGenerator(n) { return function () { var t = this, e = arguments; return new Promise(function (r, o) { var a = n.apply(t, e); function _next(n) { asyncGeneratorStep(a, r, o, _next, _throw, \"next\", n); } function _throw(n) { asyncGeneratorStep(a, r, o, _next, _throw, \"throw\", n); } _next(void 0); }); }; }\nimport { ref, computed, inject, nextTick, watch } from 'vue';\nimport { debounce } from 'lodash-unified';\nimport '../../../../constants/index.mjs';\nimport { sliderContextKey } from '../constants.mjs';\nimport { EVENT_CODE } from '../../../../constants/aria.mjs';\nimport { UPDATE_MODEL_EVENT } from '../../../../constants/event.mjs';\nvar left = EVENT_CODE.left,\n  down = EVENT_CODE.down,\n  right = EVENT_CODE.right,\n  up = EVENT_CODE.up,\n  home = EVENT_CODE.home,\n  end = EVENT_CODE.end,\n  pageUp = EVENT_CODE.pageUp,\n  pageDown = EVENT_CODE.pageDown;\nvar useTooltip = function useTooltip(props, formatTooltip, showTooltip) {\n  var tooltip = ref();\n  var tooltipVisible = ref(false);\n  var enableFormat = computed(function () {\n    return formatTooltip.value instanceof Function;\n  });\n  var formatValue = computed(function () {\n    return enableFormat.value && formatTooltip.value(props.modelValue) || props.modelValue;\n  });\n  var displayTooltip = debounce(function () {\n    showTooltip.value && (tooltipVisible.value = true);\n  }, 50);\n  var hideTooltip = debounce(function () {\n    showTooltip.value && (tooltipVisible.value = false);\n  }, 50);\n  return {\n    tooltip,\n    tooltipVisible,\n    formatValue,\n    displayTooltip,\n    hideTooltip\n  };\n};\nvar useSliderButton = function useSliderButton(props, initData, emit) {\n  var _inject = inject(sliderContextKey),\n    disabled = _inject.disabled,\n    min = _inject.min,\n    max = _inject.max,\n    step = _inject.step,\n    showTooltip = _inject.showTooltip,\n    precision = _inject.precision,\n    sliderSize = _inject.sliderSize,\n    formatTooltip = _inject.formatTooltip,\n    emitChange = _inject.emitChange,\n    resetSize = _inject.resetSize,\n    updateDragging = _inject.updateDragging;\n  var _useTooltip = useTooltip(props, formatTooltip, showTooltip),\n    tooltip = _useTooltip.tooltip,\n    tooltipVisible = _useTooltip.tooltipVisible,\n    formatValue = _useTooltip.formatValue,\n    displayTooltip = _useTooltip.displayTooltip,\n    hideTooltip = _useTooltip.hideTooltip;\n  var button = ref();\n  var currentPosition = computed(function () {\n    return `${(props.modelValue - min.value) / (max.value - min.value) * 100}%`;\n  });\n  var wrapperStyle = computed(function () {\n    return props.vertical ? {\n      bottom: currentPosition.value\n    } : {\n      left: currentPosition.value\n    };\n  });\n  var handleMouseEnter = function handleMouseEnter() {\n    initData.hovering = true;\n    displayTooltip();\n  };\n  var handleMouseLeave = function handleMouseLeave() {\n    initData.hovering = false;\n    if (!initData.dragging) {\n      hideTooltip();\n    }\n  };\n  var onButtonDown = function onButtonDown(event) {\n    if (disabled.value) return;\n    event.preventDefault();\n    onDragStart(event);\n    window.addEventListener(\"mousemove\", onDragging);\n    window.addEventListener(\"touchmove\", onDragging);\n    window.addEventListener(\"mouseup\", _onDragEnd);\n    window.addEventListener(\"touchend\", _onDragEnd);\n    window.addEventListener(\"contextmenu\", _onDragEnd);\n    button.value.focus();\n  };\n  var incrementPosition = function incrementPosition(amount) {\n    if (disabled.value) return;\n    initData.newPosition = Number.parseFloat(currentPosition.value) + amount / (max.value - min.value) * 100;\n    setPosition(initData.newPosition);\n    emitChange();\n  };\n  var onLeftKeyDown = function onLeftKeyDown() {\n    incrementPosition(-step.value);\n  };\n  var onRightKeyDown = function onRightKeyDown() {\n    incrementPosition(step.value);\n  };\n  var onPageDownKeyDown = function onPageDownKeyDown() {\n    incrementPosition(-step.value * 4);\n  };\n  var onPageUpKeyDown = function onPageUpKeyDown() {\n    incrementPosition(step.value * 4);\n  };\n  var onHomeKeyDown = function onHomeKeyDown() {\n    if (disabled.value) return;\n    setPosition(0);\n    emitChange();\n  };\n  var onEndKeyDown = function onEndKeyDown() {\n    if (disabled.value) return;\n    setPosition(100);\n    emitChange();\n  };\n  var onKeyDown = function onKeyDown(event) {\n    var isPreventDefault = true;\n    if ([left, down].includes(event.key)) {\n      onLeftKeyDown();\n    } else if ([right, up].includes(event.key)) {\n      onRightKeyDown();\n    } else if (event.key === home) {\n      onHomeKeyDown();\n    } else if (event.key === end) {\n      onEndKeyDown();\n    } else if (event.key === pageDown) {\n      onPageDownKeyDown();\n    } else if (event.key === pageUp) {\n      onPageUpKeyDown();\n    } else {\n      isPreventDefault = false;\n    }\n    isPreventDefault && event.preventDefault();\n  };\n  var getClientXY = function getClientXY(event) {\n    var clientX;\n    var clientY;\n    if (event.type.startsWith(\"touch\")) {\n      clientY = event.touches[0].clientY;\n      clientX = event.touches[0].clientX;\n    } else {\n      clientY = event.clientY;\n      clientX = event.clientX;\n    }\n    return {\n      clientX,\n      clientY\n    };\n  };\n  var onDragStart = function onDragStart(event) {\n    initData.dragging = true;\n    initData.isClick = true;\n    var _getClientXY = getClientXY(event),\n      clientX = _getClientXY.clientX,\n      clientY = _getClientXY.clientY;\n    if (props.vertical) {\n      initData.startY = clientY;\n    } else {\n      initData.startX = clientX;\n    }\n    initData.startPosition = Number.parseFloat(currentPosition.value);\n    initData.newPosition = initData.startPosition;\n  };\n  var onDragging = function onDragging(event) {\n    if (initData.dragging) {\n      initData.isClick = false;\n      displayTooltip();\n      resetSize();\n      var diff;\n      var _getClientXY2 = getClientXY(event),\n        clientX = _getClientXY2.clientX,\n        clientY = _getClientXY2.clientY;\n      if (props.vertical) {\n        initData.currentY = clientY;\n        diff = (initData.startY - initData.currentY) / sliderSize.value * 100;\n      } else {\n        initData.currentX = clientX;\n        diff = (initData.currentX - initData.startX) / sliderSize.value * 100;\n      }\n      initData.newPosition = initData.startPosition + diff;\n      setPosition(initData.newPosition);\n    }\n  };\n  var _onDragEnd = function onDragEnd() {\n    if (initData.dragging) {\n      setTimeout(function () {\n        initData.dragging = false;\n        if (!initData.hovering) {\n          hideTooltip();\n        }\n        if (!initData.isClick) {\n          setPosition(initData.newPosition);\n        }\n        emitChange();\n      }, 0);\n      window.removeEventListener(\"mousemove\", onDragging);\n      window.removeEventListener(\"touchmove\", onDragging);\n      window.removeEventListener(\"mouseup\", _onDragEnd);\n      window.removeEventListener(\"touchend\", _onDragEnd);\n      window.removeEventListener(\"contextmenu\", _onDragEnd);\n    }\n  };\n  var setPosition = /*#__PURE__*/function () {\n    var _ref = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee(newPosition) {\n      var lengthPerStep, steps, value;\n      return _regeneratorRuntime().wrap(function _callee$(_context) {\n        while (1) switch (_context.prev = _context.next) {\n          case 0:\n            if (!(newPosition === null || Number.isNaN(+newPosition))) {\n              _context.next = 2;\n              break;\n            }\n            return _context.abrupt(\"return\");\n          case 2:\n            if (newPosition < 0) {\n              newPosition = 0;\n            } else if (newPosition > 100) {\n              newPosition = 100;\n            }\n            lengthPerStep = 100 / ((max.value - min.value) / step.value);\n            steps = Math.round(newPosition / lengthPerStep);\n            value = steps * lengthPerStep * (max.value - min.value) * 0.01 + min.value;\n            value = Number.parseFloat(value.toFixed(precision.value));\n            if (value !== props.modelValue) {\n              emit(UPDATE_MODEL_EVENT, value);\n            }\n            if (!initData.dragging && props.modelValue !== initData.oldValue) {\n              initData.oldValue = props.modelValue;\n            }\n            _context.next = 11;\n            return nextTick();\n          case 11:\n            initData.dragging && displayTooltip();\n            tooltip.value.updatePopper();\n          case 13:\n          case \"end\":\n            return _context.stop();\n        }\n      }, _callee);\n    }));\n    return function setPosition(_x) {\n      return _ref.apply(this, arguments);\n    };\n  }();\n  watch(function () {\n    return initData.dragging;\n  }, function (val) {\n    updateDragging(val);\n  });\n  return {\n    disabled,\n    button,\n    tooltip,\n    tooltipVisible,\n    showTooltip,\n    wrapperStyle,\n    formatValue,\n    handleMouseEnter,\n    handleMouseLeave,\n    onButtonDown,\n    onKeyDown,\n    setPosition\n  };\n};\nexport { useSliderButton };", "map": {"version": 3, "names": ["left", "EVENT_CODE", "down", "right", "up", "home", "end", "pageUp", "pageDown", "useTooltip", "props", "formatTooltip", "showTooltip", "tooltip", "ref", "tooltipVisible", "enableFormat", "computed", "value", "Function", "formatValue", "modelValue", "displayTooltip", "debounce", "hideTooltip", "useSliderButton", "initData", "emit", "_inject", "inject", "slider<PERSON><PERSON>xt<PERSON><PERSON>", "disabled", "min", "max", "step", "precision", "sliderSize", "emitChange", "resetSize", "updateDragging", "_useTooltip", "button", "currentPosition", "wrapperStyle", "vertical", "bottom", "handleMouseEnter", "hovering", "handleMouseLeave", "dragging", "onButtonDown", "event", "preventDefault", "onDragStart", "window", "addEventListener", "onDragging", "onDragEnd", "focus", "incrementPosition", "amount", "newPosition", "Number", "parseFloat", "setPosition", "onLeftKeyDown", "onRightKeyDown", "onPageDownKeyDown", "onPageUpKeyDown", "onHomeKeyDown", "onEndKeyDown", "onKeyDown", "isPreventDefault", "includes", "key", "getClientXY", "clientX", "clientY", "type", "startsWith", "touches", "isClick", "_getClientXY", "startY", "startX", "startPosition", "diff", "_getClientXY2", "currentY", "currentX", "setTimeout", "removeEventListener", "_ref", "_asyncToGenerator", "_regeneratorRuntime", "mark", "_callee", "lengthPerStep", "steps", "wrap", "_callee$", "_context", "prev", "next", "isNaN", "abrupt", "Math", "round", "toFixed", "UPDATE_MODEL_EVENT", "oldValue", "nextTick", "updatePopper", "stop", "_x", "apply", "arguments", "watch", "val"], "sources": ["../../../../../../../packages/components/slider/src/composables/use-slider-button.ts"], "sourcesContent": ["import { computed, inject, nextTick, ref, watch } from 'vue'\nimport { debounce } from 'lodash-unified'\nimport { EVENT_CODE, UPDATE_MODEL_EVENT } from '@element-plus/constants'\nimport { sliderContextKey } from '../constants'\n\nimport type { CSSProperties, ComputedRef, Ref, SetupContext } from 'vue'\nimport type { SliderProps } from '../slider'\nimport type {\n  SliderButtonEmits,\n  SliderButtonInitData,\n  SliderButtonProps,\n} from '../button'\n\nconst { left, down, right, up, home, end, pageUp, pageDown } = EVENT_CODE\n\nconst useTooltip = (\n  props: SliderButtonProps,\n  formatTooltip: Ref<SliderProps['formatTooltip']>,\n  showTooltip: Ref<SliderProps['showTooltip']>\n) => {\n  // TODO any is temporary, replace with `TooltipInstance` later\n  const tooltip = ref<any>()\n\n  const tooltipVisible = ref(false)\n\n  const enableFormat = computed(() => {\n    return formatTooltip.value instanceof Function\n  })\n\n  const formatValue = computed(() => {\n    return (\n      (enableFormat.value && formatTooltip.value!(props.modelValue)) ||\n      props.modelValue\n    )\n  })\n\n  const displayTooltip = debounce(() => {\n    showTooltip.value && (tooltipVisible.value = true)\n  }, 50)\n\n  const hideTooltip = debounce(() => {\n    showTooltip.value && (tooltipVisible.value = false)\n  }, 50)\n\n  return {\n    tooltip,\n    tooltipVisible,\n    formatValue,\n    displayTooltip,\n    hideTooltip,\n  }\n}\n\nexport const useSliderButton = (\n  props: SliderButtonProps,\n  initData: SliderButtonInitData,\n  emit: SetupContext<SliderButtonEmits>['emit']\n) => {\n  const {\n    disabled,\n    min,\n    max,\n    step,\n    showTooltip,\n    precision,\n    sliderSize,\n    formatTooltip,\n    emitChange,\n    resetSize,\n    updateDragging,\n  } = inject(sliderContextKey)!\n\n  const { tooltip, tooltipVisible, formatValue, displayTooltip, hideTooltip } =\n    useTooltip(props, formatTooltip!, showTooltip)\n\n  const button = ref<HTMLDivElement>()\n\n  const currentPosition = computed(() => {\n    return `${\n      ((props.modelValue - min.value) / (max.value - min.value)) * 100\n    }%`\n  })\n\n  const wrapperStyle: ComputedRef<CSSProperties> = computed(() => {\n    return props.vertical\n      ? { bottom: currentPosition.value }\n      : { left: currentPosition.value }\n  })\n\n  const handleMouseEnter = () => {\n    initData.hovering = true\n    displayTooltip()\n  }\n\n  const handleMouseLeave = () => {\n    initData.hovering = false\n    if (!initData.dragging) {\n      hideTooltip()\n    }\n  }\n\n  const onButtonDown = (event: MouseEvent | TouchEvent) => {\n    if (disabled.value) return\n    event.preventDefault()\n    onDragStart(event)\n    window.addEventListener('mousemove', onDragging)\n    window.addEventListener('touchmove', onDragging)\n    window.addEventListener('mouseup', onDragEnd)\n    window.addEventListener('touchend', onDragEnd)\n    window.addEventListener('contextmenu', onDragEnd)\n    button.value!.focus()\n  }\n\n  const incrementPosition = (amount: number) => {\n    if (disabled.value) return\n    initData.newPosition =\n      Number.parseFloat(currentPosition.value) +\n      (amount / (max.value - min.value)) * 100\n    setPosition(initData.newPosition)\n    emitChange()\n  }\n\n  const onLeftKeyDown = () => {\n    incrementPosition(-step.value)\n  }\n\n  const onRightKeyDown = () => {\n    incrementPosition(step.value)\n  }\n\n  const onPageDownKeyDown = () => {\n    incrementPosition(-step.value * 4)\n  }\n\n  const onPageUpKeyDown = () => {\n    incrementPosition(step.value * 4)\n  }\n\n  const onHomeKeyDown = () => {\n    if (disabled.value) return\n    setPosition(0)\n    emitChange()\n  }\n\n  const onEndKeyDown = () => {\n    if (disabled.value) return\n    setPosition(100)\n    emitChange()\n  }\n\n  const onKeyDown = (event: KeyboardEvent) => {\n    let isPreventDefault = true\n    if ([left, down].includes(event.key)) {\n      onLeftKeyDown()\n    } else if ([right, up].includes(event.key)) {\n      onRightKeyDown()\n    } else if (event.key === home) {\n      onHomeKeyDown()\n    } else if (event.key === end) {\n      onEndKeyDown()\n    } else if (event.key === pageDown) {\n      onPageDownKeyDown()\n    } else if (event.key === pageUp) {\n      onPageUpKeyDown()\n    } else {\n      isPreventDefault = false\n    }\n    isPreventDefault && event.preventDefault()\n  }\n\n  const getClientXY = (event: MouseEvent | TouchEvent) => {\n    let clientX: number\n    let clientY: number\n    if (event.type.startsWith('touch')) {\n      clientY = (event as TouchEvent).touches[0].clientY\n      clientX = (event as TouchEvent).touches[0].clientX\n    } else {\n      clientY = (event as MouseEvent).clientY\n      clientX = (event as MouseEvent).clientX\n    }\n    return {\n      clientX,\n      clientY,\n    }\n  }\n\n  const onDragStart = (event: MouseEvent | TouchEvent) => {\n    initData.dragging = true\n    initData.isClick = true\n    const { clientX, clientY } = getClientXY(event)\n    if (props.vertical) {\n      initData.startY = clientY\n    } else {\n      initData.startX = clientX\n    }\n    initData.startPosition = Number.parseFloat(currentPosition.value)\n    initData.newPosition = initData.startPosition\n  }\n\n  const onDragging = (event: MouseEvent | TouchEvent) => {\n    if (initData.dragging) {\n      initData.isClick = false\n      displayTooltip()\n      resetSize()\n      let diff: number\n      const { clientX, clientY } = getClientXY(event)\n      if (props.vertical) {\n        initData.currentY = clientY\n        diff = ((initData.startY - initData.currentY) / sliderSize.value) * 100\n      } else {\n        initData.currentX = clientX\n        diff = ((initData.currentX - initData.startX) / sliderSize.value) * 100\n      }\n      initData.newPosition = initData.startPosition + diff\n      setPosition(initData.newPosition)\n    }\n  }\n\n  const onDragEnd = () => {\n    if (initData.dragging) {\n      /*\n       * 防止在 mouseup 后立即触发 click，导致滑块有几率产生一小段位移\n       * 不使用 preventDefault 是因为 mouseup 和 click 没有注册在同一个 DOM 上\n       */\n      setTimeout(() => {\n        initData.dragging = false\n        if (!initData.hovering) {\n          hideTooltip()\n        }\n        if (!initData.isClick) {\n          setPosition(initData.newPosition)\n        }\n        emitChange()\n      }, 0)\n      window.removeEventListener('mousemove', onDragging)\n      window.removeEventListener('touchmove', onDragging)\n      window.removeEventListener('mouseup', onDragEnd)\n      window.removeEventListener('touchend', onDragEnd)\n      window.removeEventListener('contextmenu', onDragEnd)\n    }\n  }\n\n  const setPosition = async (newPosition: number) => {\n    if (newPosition === null || Number.isNaN(+newPosition)) return\n    if (newPosition < 0) {\n      newPosition = 0\n    } else if (newPosition > 100) {\n      newPosition = 100\n    }\n    const lengthPerStep = 100 / ((max.value - min.value) / step.value)\n    const steps = Math.round(newPosition / lengthPerStep)\n    let value =\n      steps * lengthPerStep * (max.value - min.value) * 0.01 + min.value\n    value = Number.parseFloat(value.toFixed(precision.value))\n\n    if (value !== props.modelValue) {\n      emit(UPDATE_MODEL_EVENT, value)\n    }\n\n    if (!initData.dragging && props.modelValue !== initData.oldValue) {\n      initData.oldValue = props.modelValue\n    }\n\n    await nextTick()\n    initData.dragging && displayTooltip()\n    tooltip.value!.updatePopper()\n  }\n\n  watch(\n    () => initData.dragging,\n    (val) => {\n      updateDragging(val)\n    }\n  )\n\n  return {\n    disabled,\n    button,\n    tooltip,\n    tooltipVisible,\n    showTooltip,\n    wrapperStyle,\n    formatValue,\n    handleMouseEnter,\n    handleMouseLeave,\n    onButtonDown,\n    onKeyDown,\n    setPosition,\n  }\n}\n"], "mappings": ";;;;;;;;;AAIA,IAAQA,IAAI,GAAmDC,UAAU,CAAjED,IAAI;EAAEE,IAAI,GAA6CD,UAAU,CAA3DC,IAAI;EAAEC,KAAK,GAAsCF,UAAU,CAArDE,KAAK;EAAEC,EAAE,GAAkCH,UAAU,CAA9CG,EAAE;EAAEC,IAAI,GAA4BJ,UAAU,CAA1CI,IAAI;EAAEC,GAAG,GAAuBL,UAAU,CAApCK,GAAG;EAAEC,MAAM,GAAeN,UAAU,CAA/BM,MAAM;EAAEC,QAAQ,GAAKP,UAAU,CAAvBO,QAAQ;AAC1D,IAAMC,UAAU,GAAG,SAAbA,UAAUA,CAAIC,KAAK,EAAEC,aAAa,EAAEC,WAAW,EAAK;EACxD,IAAMC,OAAO,GAAGC,GAAG,EAAE;EACrB,IAAMC,cAAc,GAAGD,GAAG,CAAC,KAAK,CAAC;EACjC,IAAME,YAAY,GAAGC,QAAQ,CAAC,YAAM;IAClC,OAAON,aAAa,CAACO,KAAK,YAAYC,QAAQ;EAClD,CAAG,CAAC;EACF,IAAMC,WAAW,GAAGH,QAAQ,CAAC,YAAM;IACjC,OAAOD,YAAY,CAACE,KAAK,IAAIP,aAAa,CAACO,KAAK,CAACR,KAAK,CAACW,UAAU,CAAC,IAAIX,KAAK,CAACW,UAAU;EAC1F,CAAG,CAAC;EACF,IAAMC,cAAc,GAAGC,QAAQ,CAAC,YAAM;IACpCX,WAAW,CAACM,KAAK,KAAKH,cAAc,CAACG,KAAK,GAAG,IAAI,CAAC;EACtD,CAAG,EAAE,EAAE,CAAC;EACN,IAAMM,WAAW,GAAGD,QAAQ,CAAC,YAAM;IACjCX,WAAW,CAACM,KAAK,KAAKH,cAAc,CAACG,KAAK,GAAG,KAAK,CAAC;EACvD,CAAG,EAAE,EAAE,CAAC;EACN,OAAO;IACLL,OAAO;IACPE,cAAc;IACdK,WAAW;IACXE,cAAc;IACdE;EACJ,CAAG;AACH,CAAC;AACW,IAACC,eAAe,GAAG,SAAlBA,eAAeA,CAAIf,KAAK,EAAEgB,QAAQ,EAAEC,IAAI,EAAK;EACxD,IAAAC,OAAA,GAYIC,MAAM,CAACC,gBAAgB,CAAC;IAX1BC,QAAQ,GAAAH,OAAA,CAARG,QAAQ;IACRC,GAAG,GAAAJ,OAAA,CAAHI,GAAG;IACHC,GAAG,GAAAL,OAAA,CAAHK,GAAG;IACHC,IAAI,GAAAN,OAAA,CAAJM,IAAI;IACJtB,WAAW,GAAAgB,OAAA,CAAXhB,WAAW;IACXuB,SAAS,GAAAP,OAAA,CAATO,SAAS;IACTC,UAAU,GAAAR,OAAA,CAAVQ,UAAU;IACVzB,aAAa,GAAAiB,OAAA,CAAbjB,aAAa;IACb0B,UAAU,GAAAT,OAAA,CAAVS,UAAU;IACVC,SAAS,GAAAV,OAAA,CAATU,SAAS;IACTC,cAAc,GAAAX,OAAA,CAAdW,cAAc;EAEhB,IAAAC,WAAA,GAA8E/B,UAAU,CAACC,KAAK,EAAEC,aAAa,EAAEC,WAAW,CAAC;IAAnHC,OAAO,GAAA2B,WAAA,CAAP3B,OAAO;IAAEE,cAAc,GAAAyB,WAAA,CAAdzB,cAAc;IAAEK,WAAW,GAAAoB,WAAA,CAAXpB,WAAW;IAAEE,cAAc,GAAAkB,WAAA,CAAdlB,cAAc;IAAEE,WAAW,GAAAgB,WAAA,CAAXhB,WAAW;EACzE,IAAMiB,MAAM,GAAG3B,GAAG,EAAE;EACpB,IAAM4B,eAAe,GAAGzB,QAAQ,CAAC,YAAM;IACrC,OAAO,GAAG,CAACP,KAAK,CAACW,UAAU,GAAGW,GAAG,CAACd,KAAK,KAAKe,GAAG,CAACf,KAAK,GAAGc,GAAG,CAACd,KAAK,CAAC,GAAG,GAAG,GAAG;EAC/E,CAAG,CAAC;EACF,IAAMyB,YAAY,GAAG1B,QAAQ,CAAC,YAAM;IAClC,OAAOP,KAAK,CAACkC,QAAQ,GAAG;MAAEC,MAAM,EAAEH,eAAe,CAACxB;IAAK,CAAE,GAAG;MAAElB,IAAI,EAAE0C,eAAe,CAACxB;IAAK,CAAE;EAC/F,CAAG,CAAC;EACF,IAAM4B,gBAAgB,GAAG,SAAnBA,gBAAgBA,CAAA,EAAS;IAC7BpB,QAAQ,CAACqB,QAAQ,GAAG,IAAI;IACxBzB,cAAc,EAAE;EACpB,CAAG;EACD,IAAM0B,gBAAgB,GAAG,SAAnBA,gBAAgBA,CAAA,EAAS;IAC7BtB,QAAQ,CAACqB,QAAQ,GAAG,KAAK;IACzB,IAAI,CAACrB,QAAQ,CAACuB,QAAQ,EAAE;MACtBzB,WAAW,EAAE;IACnB;EACA,CAAG;EACD,IAAM0B,YAAY,GAAG,SAAfA,YAAYA,CAAIC,KAAK,EAAK;IAC9B,IAAIpB,QAAQ,CAACb,KAAK,EAChB;IACFiC,KAAK,CAACC,cAAc,EAAE;IACtBC,WAAW,CAACF,KAAK,CAAC;IAClBG,MAAM,CAACC,gBAAgB,CAAC,WAAW,EAAEC,UAAU,CAAC;IAChDF,MAAM,CAACC,gBAAgB,CAAC,WAAW,EAAEC,UAAU,CAAC;IAChDF,MAAM,CAACC,gBAAgB,CAAC,SAAS,EAAEE,UAAS,CAAC;IAC7CH,MAAM,CAACC,gBAAgB,CAAC,UAAU,EAAEE,UAAS,CAAC;IAC9CH,MAAM,CAACC,gBAAgB,CAAC,aAAa,EAAEE,UAAS,CAAC;IACjDhB,MAAM,CAACvB,KAAK,CAACwC,KAAK,EAAE;EACxB,CAAG;EACD,IAAMC,iBAAiB,GAAG,SAApBA,iBAAiBA,CAAIC,MAAM,EAAK;IACpC,IAAI7B,QAAQ,CAACb,KAAK,EAChB;IACFQ,QAAQ,CAACmC,WAAW,GAAGC,MAAM,CAACC,UAAU,CAACrB,eAAe,CAACxB,KAAK,CAAC,GAAG0C,MAAM,IAAI3B,GAAG,CAACf,KAAK,GAAGc,GAAG,CAACd,KAAK,CAAC,GAAG,GAAG;IACxG8C,WAAW,CAACtC,QAAQ,CAACmC,WAAW,CAAC;IACjCxB,UAAU,EAAE;EAChB,CAAG;EACD,IAAM4B,aAAa,GAAG,SAAhBA,aAAaA,CAAA,EAAS;IAC1BN,iBAAiB,CAAC,CAACzB,IAAI,CAAChB,KAAK,CAAC;EAClC,CAAG;EACD,IAAMgD,cAAc,GAAG,SAAjBA,cAAcA,CAAA,EAAS;IAC3BP,iBAAiB,CAACzB,IAAI,CAAChB,KAAK,CAAC;EACjC,CAAG;EACD,IAAMiD,iBAAiB,GAAG,SAApBA,iBAAiBA,CAAA,EAAS;IAC9BR,iBAAiB,CAAC,CAACzB,IAAI,CAAChB,KAAK,GAAG,CAAC,CAAC;EACtC,CAAG;EACD,IAAMkD,eAAe,GAAG,SAAlBA,eAAeA,CAAA,EAAS;IAC5BT,iBAAiB,CAACzB,IAAI,CAAChB,KAAK,GAAG,CAAC,CAAC;EACrC,CAAG;EACD,IAAMmD,aAAa,GAAG,SAAhBA,aAAaA,CAAA,EAAS;IAC1B,IAAItC,QAAQ,CAACb,KAAK,EAChB;IACF8C,WAAW,CAAC,CAAC,CAAC;IACd3B,UAAU,EAAE;EAChB,CAAG;EACD,IAAMiC,YAAY,GAAG,SAAfA,YAAYA,CAAA,EAAS;IACzB,IAAIvC,QAAQ,CAACb,KAAK,EAChB;IACF8C,WAAW,CAAC,GAAG,CAAC;IAChB3B,UAAU,EAAE;EAChB,CAAG;EACD,IAAMkC,SAAS,GAAG,SAAZA,SAASA,CAAIpB,KAAK,EAAK;IAC3B,IAAIqB,gBAAgB,GAAG,IAAI;IAC3B,IAAI,CAACxE,IAAI,EAAEE,IAAI,CAAC,CAACuE,QAAQ,CAACtB,KAAK,CAACuB,GAAG,CAAC,EAAE;MACpCT,aAAa,EAAE;IACrB,CAAK,MAAM,IAAI,CAAC9D,KAAK,EAAEC,EAAE,CAAC,CAACqE,QAAQ,CAACtB,KAAK,CAACuB,GAAG,CAAC,EAAE;MAC1CR,cAAc,EAAE;IACtB,CAAK,MAAM,IAAIf,KAAK,CAACuB,GAAG,KAAKrE,IAAI,EAAE;MAC7BgE,aAAa,EAAE;IACrB,CAAK,MAAM,IAAIlB,KAAK,CAACuB,GAAG,KAAKpE,GAAG,EAAE;MAC5BgE,YAAY,EAAE;IACpB,CAAK,MAAM,IAAInB,KAAK,CAACuB,GAAG,KAAKlE,QAAQ,EAAE;MACjC2D,iBAAiB,EAAE;IACzB,CAAK,MAAM,IAAIhB,KAAK,CAACuB,GAAG,KAAKnE,MAAM,EAAE;MAC/B6D,eAAe,EAAE;IACvB,CAAK,MAAM;MACLI,gBAAgB,GAAG,KAAK;IAC9B;IACIA,gBAAgB,IAAIrB,KAAK,CAACC,cAAc,EAAE;EAC9C,CAAG;EACD,IAAMuB,WAAW,GAAG,SAAdA,WAAWA,CAAIxB,KAAK,EAAK;IAC7B,IAAIyB,OAAO;IACX,IAAIC,OAAO;IACX,IAAI1B,KAAK,CAAC2B,IAAI,CAACC,UAAU,CAAC,OAAO,CAAC,EAAE;MAClCF,OAAO,GAAG1B,KAAK,CAAC6B,OAAO,CAAC,CAAC,CAAC,CAACH,OAAO;MAClCD,OAAO,GAAGzB,KAAK,CAAC6B,OAAO,CAAC,CAAC,CAAC,CAACJ,OAAO;IACxC,CAAK,MAAM;MACLC,OAAO,GAAG1B,KAAK,CAAC0B,OAAO;MACvBD,OAAO,GAAGzB,KAAK,CAACyB,OAAO;IAC7B;IACI,OAAO;MACLA,OAAO;MACPC;IACN,CAAK;EACL,CAAG;EACD,IAAMxB,WAAW,GAAG,SAAdA,WAAWA,CAAIF,KAAK,EAAK;IAC7BzB,QAAQ,CAACuB,QAAQ,GAAG,IAAI;IACxBvB,QAAQ,CAACuD,OAAO,GAAG,IAAI;IACvB,IAAAC,YAAA,GAA6BP,WAAW,CAACxB,KAAK,CAAC;MAAvCyB,OAAO,GAAAM,YAAA,CAAPN,OAAO;MAAEC,OAAO,GAAAK,YAAA,CAAPL,OAAO;IACxB,IAAInE,KAAK,CAACkC,QAAQ,EAAE;MAClBlB,QAAQ,CAACyD,MAAM,GAAGN,OAAO;IAC/B,CAAK,MAAM;MACLnD,QAAQ,CAAC0D,MAAM,GAAGR,OAAO;IAC/B;IACIlD,QAAQ,CAAC2D,aAAa,GAAGvB,MAAM,CAACC,UAAU,CAACrB,eAAe,CAACxB,KAAK,CAAC;IACjEQ,QAAQ,CAACmC,WAAW,GAAGnC,QAAQ,CAAC2D,aAAa;EACjD,CAAG;EACD,IAAM7B,UAAU,GAAG,SAAbA,UAAUA,CAAIL,KAAK,EAAK;IAC5B,IAAIzB,QAAQ,CAACuB,QAAQ,EAAE;MACrBvB,QAAQ,CAACuD,OAAO,GAAG,KAAK;MACxB3D,cAAc,EAAE;MAChBgB,SAAS,EAAE;MACX,IAAIgD,IAAI;MACR,IAAAC,aAAA,GAA6BZ,WAAW,CAACxB,KAAK,CAAC;QAAvCyB,OAAO,GAAAW,aAAA,CAAPX,OAAO;QAAEC,OAAO,GAAAU,aAAA,CAAPV,OAAO;MACxB,IAAInE,KAAK,CAACkC,QAAQ,EAAE;QAClBlB,QAAQ,CAAC8D,QAAQ,GAAGX,OAAO;QAC3BS,IAAI,GAAG,CAAC5D,QAAQ,CAACyD,MAAM,GAAGzD,QAAQ,CAAC8D,QAAQ,IAAIpD,UAAU,CAAClB,KAAK,GAAG,GAAG;MAC7E,CAAO,MAAM;QACLQ,QAAQ,CAAC+D,QAAQ,GAAGb,OAAO;QAC3BU,IAAI,GAAG,CAAC5D,QAAQ,CAAC+D,QAAQ,GAAG/D,QAAQ,CAAC0D,MAAM,IAAIhD,UAAU,CAAClB,KAAK,GAAG,GAAG;MAC7E;MACMQ,QAAQ,CAACmC,WAAW,GAAGnC,QAAQ,CAAC2D,aAAa,GAAGC,IAAI;MACpDtB,WAAW,CAACtC,QAAQ,CAACmC,WAAW,CAAC;IACvC;EACA,CAAG;EACD,IAAMJ,UAAS,GAAG,SAAZA,SAASA,CAAA,EAAS;IACtB,IAAI/B,QAAQ,CAACuB,QAAQ,EAAE;MACrByC,UAAU,CAAC,YAAM;QACfhE,QAAQ,CAACuB,QAAQ,GAAG,KAAK;QACzB,IAAI,CAACvB,QAAQ,CAACqB,QAAQ,EAAE;UACtBvB,WAAW,EAAE;QACvB;QACQ,IAAI,CAACE,QAAQ,CAACuD,OAAO,EAAE;UACrBjB,WAAW,CAACtC,QAAQ,CAACmC,WAAW,CAAC;QAC3C;QACQxB,UAAU,EAAE;MACpB,CAAO,EAAE,CAAC,CAAC;MACLiB,MAAM,CAACqC,mBAAmB,CAAC,WAAW,EAAEnC,UAAU,CAAC;MACnDF,MAAM,CAACqC,mBAAmB,CAAC,WAAW,EAAEnC,UAAU,CAAC;MACnDF,MAAM,CAACqC,mBAAmB,CAAC,SAAS,EAAElC,UAAS,CAAC;MAChDH,MAAM,CAACqC,mBAAmB,CAAC,UAAU,EAAElC,UAAS,CAAC;MACjDH,MAAM,CAACqC,mBAAmB,CAAC,aAAa,EAAElC,UAAS,CAAC;IAC1D;EACA,CAAG;EACD,IAAMO,WAAW;IAAA,IAAA4B,IAAA,GAAAC,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,CAAG,SAAAC,QAAOnC,WAAW;MAAA,IAAAoC,aAAA,EAAAC,KAAA,EAAAhF,KAAA;MAAA,OAAA4E,mBAAA,GAAAK,IAAA,UAAAC,SAAAC,QAAA;QAAA,kBAAAA,QAAA,CAAAC,IAAA,GAAAD,QAAA,CAAAE,IAAA;UAAA;YAAA,MAChC1C,WAAW,KAAK,IAAI,IAAIC,MAAM,CAAC0C,KAAK,CAAC,CAAC3C,WAAW,CAAC;cAAAwC,QAAA,CAAAE,IAAA;cAAA;YAAA;YAAA,OAAAF,QAAA,CAAAI,MAAA;UAAA;YAEtD,IAAI5C,WAAW,GAAG,CAAC,EAAE;cACnBA,WAAW,GAAG,CAAC;YACrB,CAAK,MAAM,IAAIA,WAAW,GAAG,GAAG,EAAE;cAC5BA,WAAW,GAAG,GAAG;YACvB;YACUoC,aAAa,GAAG,GAAG,IAAI,CAAChE,GAAG,CAACf,KAAK,GAAGc,GAAG,CAACd,KAAK,IAAIgB,IAAI,CAAChB,KAAK,CAAC;YAC5DgF,KAAK,GAAGQ,IAAI,CAACC,KAAK,CAAC9C,WAAW,GAAGoC,aAAa,CAAC;YACjD/E,KAAK,GAAGgF,KAAK,GAAGD,aAAa,IAAIhE,GAAG,CAACf,KAAK,GAAGc,GAAG,CAACd,KAAK,CAAC,GAAG,IAAI,GAAGc,GAAG,CAACd,KAAK;YAC9EA,KAAK,GAAG4C,MAAM,CAACC,UAAU,CAAC7C,KAAK,CAAC0F,OAAO,CAACzE,SAAS,CAACjB,KAAK,CAAC,CAAC;YACzD,IAAIA,KAAK,KAAKR,KAAK,CAACW,UAAU,EAAE;cAC9BM,IAAI,CAACkF,kBAAkB,EAAE3F,KAAK,CAAC;YACrC;YACI,IAAI,CAACQ,QAAQ,CAACuB,QAAQ,IAAIvC,KAAK,CAACW,UAAU,KAAKK,QAAQ,CAACoF,QAAQ,EAAE;cAChEpF,QAAQ,CAACoF,QAAQ,GAAGpG,KAAK,CAACW,UAAU;YAC1C;YAAKgF,QAAA,CAAAE,IAAA;YAAA,OACKQ,QAAQ,EAAE;UAAA;YAChBrF,QAAQ,CAACuB,QAAQ,IAAI3B,cAAc,EAAE;YACrCT,OAAO,CAACK,KAAK,CAAC8F,YAAY,EAAE;UAAC;UAAA;YAAA,OAAAX,QAAA,CAAAY,IAAA;QAAA;MAAA,GAAAjB,OAAA;IAAA,CAC9B;IAAA,gBArBKhC,WAAWA,CAAAkD,EAAA;MAAA,OAAAtB,IAAA,CAAAuB,KAAA,OAAAC,SAAA;IAAA;EAAA,GAqBhB;EACDC,KAAK,CAAC;IAAA,OAAM3F,QAAQ,CAACuB,QAAQ;EAAA,GAAE,UAACqE,GAAG,EAAK;IACtC/E,cAAc,CAAC+E,GAAG,CAAC;EACvB,CAAG,CAAC;EACF,OAAO;IACLvF,QAAQ;IACRU,MAAM;IACN5B,OAAO;IACPE,cAAc;IACdH,WAAW;IACX+B,YAAY;IACZvB,WAAW;IACX0B,gBAAgB;IAChBE,gBAAgB;IAChBE,YAAY;IACZqB,SAAS;IACTP;EACJ,CAAG;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
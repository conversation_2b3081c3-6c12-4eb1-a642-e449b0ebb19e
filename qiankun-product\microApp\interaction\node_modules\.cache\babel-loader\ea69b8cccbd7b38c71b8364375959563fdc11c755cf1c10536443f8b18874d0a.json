{"ast": null, "code": "import { resolveComponent as _resolveComponent, createVNode as _createVNode, createElementVNode as _createElementVNode, toDisplayString as _toDisplayString, renderList as _renderList, Fragment as _Fragment, openBlock as _openBlock, createElementBlock as _createElementBlock, createBlock as _createBlock } from \"vue\";\nvar _hoisted_1 = {\n  class: \"CircleOfFriendsDetails\"\n};\nvar _hoisted_2 = {\n  class: \"CircleOfFriendsDetailsHeadImg\"\n};\nvar _hoisted_3 = {\n  class: \"CircleOfFriendsDetailsBody\"\n};\nvar _hoisted_4 = {\n  class: \"CircleOfFriendsDetailsName\"\n};\nvar _hoisted_5 = {\n  class: \"CircleOfFriendsDetailsText\"\n};\nvar _hoisted_6 = [\"title\"];\nvar _hoisted_7 = {\n  class: \"CircleOfFriendsDetailsContent\"\n};\nvar _hoisted_8 = {\n  class: \"CircleOfFriendsDetailsImg\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  var _component_el_image = _resolveComponent(\"el-image\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createElementVNode(\"div\", _hoisted_2, [_createVNode(_component_el_image, {\n    src: $setup.imgUrl($setup.details.headImg),\n    fit: \"cover\"\n  }, null, 8 /* PROPS */, [\"src\"])]), _createElementVNode(\"div\", _hoisted_3, [_createElementVNode(\"div\", _hoisted_4, _toDisplayString($setup.details.createUserName), 1 /* TEXT */), _createElementVNode(\"div\", _hoisted_5, [_createElementVNode(\"span\", {\n    title: $setup.details.userHonor\n  }, \"（\" + _toDisplayString($setup.userArea) + \"）\", 9 /* TEXT, PROPS */, _hoisted_6), _createElementVNode(\"span\", null, _toDisplayString($setup.format($setup.details.createDate)), 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_7, [_createElementVNode(\"pre\", null, _toDisplayString($setup.details.content), 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_8, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.imgList, function (item) {\n    return _openBlock(), _createBlock(_component_el_image, {\n      src: item,\n      key: item,\n      \"preview-src-list\": $setup.imgList,\n      fit: \"cover\"\n    }, null, 8 /* PROPS */, [\"src\", \"preview-src-list\"]);\n  }), 128 /* KEYED_FRAGMENT */))])])]);\n}", "map": {"version": 3, "names": ["class", "_createElementBlock", "_hoisted_1", "_createElementVNode", "_hoisted_2", "_createVNode", "_component_el_image", "src", "$setup", "imgUrl", "details", "headImg", "fit", "_hoisted_3", "_hoisted_4", "_toDisplayString", "createUserName", "_hoisted_5", "title", "userHonor", "userArea", "_hoisted_6", "format", "createDate", "_hoisted_7", "content", "_hoisted_8", "_Fragment", "_renderList", "imgList", "item", "_createBlock", "key"], "sources": ["D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\interaction\\src\\views\\CircleOfFriends\\CircleOfFriendsDetails.vue"], "sourcesContent": ["<template>\r\n  <div class=\"CircleOfFriendsDetails\">\r\n    <div class=\"CircleOfFriendsDetailsHeadImg\">\r\n      <el-image :src=\"imgUrl(details.headImg)\"\r\n                fit=\"cover\" />\r\n    </div>\r\n    <div class=\"CircleOfFriendsDetailsBody\">\r\n      <div class=\"CircleOfFriendsDetailsName\">{{details.createUserName}}</div>\r\n      <div class=\"CircleOfFriendsDetailsText\"><span :title=\"details.userHonor\">（{{userArea}}）</span><span>{{format(details.createDate)}}</span></div>\r\n      <div class=\"CircleOfFriendsDetailsContent\">\r\n        <pre>{{details.content}}</pre>\r\n      </div>\r\n      <div class=\"CircleOfFriendsDetailsImg\">\r\n        <el-image :src=\"item\"\r\n                  v-for=\"item in imgList\"\r\n                  :key=\"item\"\r\n                  :preview-src-list=\"imgList\"\r\n                  fit=\"cover\" />\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n<script>\r\nexport default { name: 'CircleOfFriendsDetails' }\r\n</script>\r\n<script setup>\r\nimport api from '@/api'\r\nimport { ref, onMounted } from 'vue'\r\nimport { format } from 'common/js/time.js'\r\nconst props = defineProps({ id: { type: String, default: '' } })\r\n\r\nconst details = ref({})\r\nconst imgList = ref([])\r\nconst userArea = ref('')\r\n\r\nonMounted(() => { if (props.id) { styleCircleInfo() } })\r\n\r\n// 图片地址拼接组合\r\nconst imgUrl = url => url ? api.fileURL(url) : api.defaultImgURL('default_user_head.jpg')\r\nconst styleCircleInfo = async () => {\r\n  const res = await api.styleCircleInfo({ detailId: props.id })\r\n  var { data } = res\r\n  details.value = data\r\n  imgList.value = data.imgs.map(v => imgUrl(v))\r\n  const userAreaList = data.userHonor?.split(',') || []\r\n  if (userAreaList.length > 2) {\r\n    userArea.value = userAreaList.splice(0, 2).join('，') + ' 等'\r\n  } else {\r\n    userArea.value = data.userHonor\r\n  }\r\n}\r\n</script>\r\n<style lang=\"scss\">\r\n.CircleOfFriendsDetails {\r\n  width: 990px;\r\n  padding: 40px;\r\n  display: flex;\r\n  justify-content: space-between;\r\n  .CircleOfFriendsDetailsHeadImg {\r\n    width: 66px;\r\n    .zy-el-image {\r\n      width: 50px;\r\n      height: 50px;\r\n      border-radius: 50%;\r\n      overflow: hidden;\r\n    }\r\n  }\r\n  .CircleOfFriendsDetailsBody {\r\n    width: calc(100% - 66px);\r\n    .CircleOfFriendsDetailsName {\r\n      font-weight: bold;\r\n      padding-bottom: 2px;\r\n      font-size: var(--zy-name-font-size);\r\n      line-height: var(--zy-line-height);\r\n    }\r\n    .CircleOfFriendsDetailsText {\r\n      font-size: var(--zy-text-font-size);\r\n      line-height: var(--zy-line-height);\r\n      display: flex;\r\n      justify-content: space-between;\r\n      padding-bottom: 12px;\r\n    }\r\n    .CircleOfFriendsDetailsContent {\r\n      font-size: var(--zy-name-font-size);\r\n      line-height: var(--zy-line-height);\r\n      pre {\r\n        white-space: pre-wrap;\r\n      }\r\n    }\r\n    .CircleOfFriendsDetailsImg {\r\n      padding-top: 20px;\r\n      display: flex;\r\n      flex-wrap: wrap;\r\n      .zy-el-image {\r\n        width: 128px;\r\n        height: 128px;\r\n        margin-right: 10px;\r\n        display: block;\r\n        margin-bottom: 10px;\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;EACOA,KAAK,EAAC;AAAwB;;EAC5BA,KAAK,EAAC;AAA+B;;EAIrCA,KAAK,EAAC;AAA4B;;EAChCA,KAAK,EAAC;AAA4B;;EAClCA,KAAK,EAAC;AAA4B;iBAR7C;;EASWA,KAAK,EAAC;AAA+B;;EAGrCA,KAAK,EAAC;AAA2B;;;uBAX1CC,mBAAA,CAmBM,OAnBNC,UAmBM,GAlBJC,mBAAA,CAGM,OAHNC,UAGM,GAFJC,YAAA,CACwBC,mBAAA;IADbC,GAAG,EAAEC,MAAA,CAAAC,MAAM,CAACD,MAAA,CAAAE,OAAO,CAACC,OAAO;IAC5BC,GAAG,EAAC;sCAEhBT,mBAAA,CAaM,OAbNU,UAaM,GAZJV,mBAAA,CAAwE,OAAxEW,UAAwE,EAAAC,gBAAA,CAA9BP,MAAA,CAAAE,OAAO,CAACM,cAAc,kBAChEb,mBAAA,CAA+I,OAA/Ic,UAA+I,GAAvGd,mBAAA,CAAsD;IAA/Ce,KAAK,EAAEV,MAAA,CAAAE,OAAO,CAACS;KAAW,GAAC,GAAAJ,gBAAA,CAAEP,MAAA,CAAAY,QAAQ,IAAE,GAAC,uBAR7FC,UAAA,GAQoGlB,mBAAA,CAA2C,cAAAY,gBAAA,CAAnCP,MAAA,CAAAc,MAAM,CAACd,MAAA,CAAAE,OAAO,CAACa,UAAU,kB,GAC/HpB,mBAAA,CAEM,OAFNqB,UAEM,GADJrB,mBAAA,CAA8B,aAAAY,gBAAA,CAAvBP,MAAA,CAAAE,OAAO,CAACe,OAAO,iB,GAExBtB,mBAAA,CAMM,OANNuB,UAMM,I,kBALJzB,mBAAA,CAIwB0B,SAAA,QAjBhCC,WAAA,CAciCpB,MAAA,CAAAqB,OAAO,EAdxC,UAcyBC,IAAI;yBADrBC,YAAA,CAIwBzB,mBAAA;MAJbC,GAAG,EAAEuB,IAAI;MAETE,GAAG,EAAEF,IAAI;MACT,kBAAgB,EAAEtB,MAAA,CAAAqB,OAAO;MAC1BjB,GAAG,EAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
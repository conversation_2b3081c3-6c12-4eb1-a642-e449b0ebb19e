{"ast": null, "code": "function _toConsumableArray(r) { return _arrayWithoutHoles(r) || _iterableToArray(r) || _unsupportedIterableToArray(r) || _nonIterableSpread(); }\nfunction _nonIterableSpread() { throw new TypeError(\"Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); }\nfunction _unsupportedIterableToArray(r, a) { if (r) { if (\"string\" == typeof r) return _arrayLikeToArray(r, a); var t = {}.toString.call(r).slice(8, -1); return \"Object\" === t && r.constructor && (t = r.constructor.name), \"Map\" === t || \"Set\" === t ? Array.from(r) : \"Arguments\" === t || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t) ? _arrayLikeToArray(r, a) : void 0; } }\nfunction _iterableToArray(r) { if (\"undefined\" != typeof Symbol && null != r[Symbol.iterator] || null != r[\"@@iterator\"]) return Array.from(r); }\nfunction _arrayWithoutHoles(r) { if (Array.isArray(r)) return _arrayLikeToArray(r); }\nfunction _arrayLikeToArray(r, a) { (null == a || a > r.length) && (a = r.length); for (var e = 0, n = Array(a); e < a; e++) n[e] = r[e]; return n; }\nimport { resolveComponent as _resolveComponent, createVNode as _createVNode, createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, createCommentVNode as _createCommentVNode, toDisplayString as _toDisplayString, createTextVNode as _createTextVNode, withCtx as _withCtx, renderSlot as _renderSlot, renderList as _renderList, Fragment as _Fragment } from \"vue\";\nvar _hoisted_1 = {\n  class: \"xyl-global-comment-item\"\n};\nvar _hoisted_2 = {\n  class: \"xyl-global-comment-info\"\n};\nvar _hoisted_3 = {\n  class: \"xyl-global-comment-img\"\n};\nvar _hoisted_4 = {\n  class: \"xyl-global-comment-body\"\n};\nvar _hoisted_5 = {\n  key: 0,\n  class: \"xyl-global-comment-status xyl-global-comment-centre\"\n};\nvar _hoisted_6 = {\n  key: 1,\n  class: \"xyl-global-comment-status\"\n};\nvar _hoisted_7 = {\n  class: \"xyl-global-comment-name\"\n};\nvar _hoisted_8 = {\n  key: 0\n};\nvar _hoisted_9 = {\n  key: 1,\n  class: \"xyl-global-comment-name-my\"\n};\nvar _hoisted_10 = [\"title\"];\nvar _hoisted_11 = {\n  class: \"xyl-global-comment-time\"\n};\nvar _hoisted_12 = {\n  key: 2,\n  class: \"xyl-global-comment-input\"\n};\nvar _hoisted_13 = {\n  class: \"xyl-global-comment-button\"\n};\nvar _hoisted_14 = {\n  key: 3,\n  class: \"xyl-global-comment-child-list\"\n};\nvar _hoisted_15 = {\n  class: \"xyl-global-comment-img\"\n};\nvar _hoisted_16 = {\n  class: \"xyl-global-comment-body\"\n};\nvar _hoisted_17 = {\n  key: 0,\n  class: \"xyl-global-comment-status xyl-global-comment-centre\"\n};\nvar _hoisted_18 = {\n  key: 1,\n  class: \"xyl-global-comment-status\"\n};\nvar _hoisted_19 = {\n  class: \"xyl-global-comment-name\"\n};\nvar _hoisted_20 = {\n  key: 0\n};\nvar _hoisted_21 = {\n  class: \"xyl-global-comment-time\"\n};\nvar _hoisted_22 = [\"onClick\"];\nvar _hoisted_23 = [\"onClick\"];\nvar _hoisted_24 = {\n  key: 2,\n  class: \"xyl-global-comment-input\"\n};\nvar _hoisted_25 = {\n  class: \"xyl-global-comment-button\"\n};\nvar _hoisted_26 = {\n  key: 0,\n  class: \"xyl-global-comment-more\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  var _component_el_image = _resolveComponent(\"el-image\");\n  var _component_text_expansion_collapse = _resolveComponent(\"text-expansion-collapse\");\n  var _component_xyl_global_file = _resolveComponent(\"xyl-global-file\");\n  var _component_el_input = _resolveComponent(\"el-input\");\n  var _component_el_button = _resolveComponent(\"el-button\");\n  var _component_DArrowRight = _resolveComponent(\"DArrowRight\");\n  var _component_el_icon = _resolveComponent(\"el-icon\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createElementVNode(\"div\", _hoisted_2, [_createElementVNode(\"div\", _hoisted_3, [_createVNode(_component_el_image, {\n    src: $setup.imgUrl($setup.commentObj.headImg),\n    fit: \"cover\"\n  }, null, 8 /* PROPS */, [\"src\"])]), _createElementVNode(\"div\", _hoisted_4, [!$setup.commentObj.checkedStatus ? (_openBlock(), _createElementBlock(\"div\", _hoisted_5, \"审核中\")) : _createCommentVNode(\"v-if\", true), $setup.commentObj.checkedStatus === 2 ? (_openBlock(), _createElementBlock(\"div\", _hoisted_6, \"审核不通过\")) : _createCommentVNode(\"v-if\", true), _createElementVNode(\"div\", _hoisted_7, [_createTextVNode(_toDisplayString($setup.commentObj.commentUserName) + \" \", 1 /* TEXT */), $setup.user.accountId !== $setup.commentObj.publishAccountId && $setup.roleName ? (_openBlock(), _createElementBlock(\"div\", _hoisted_8, _toDisplayString($setup.roleName), 1 /* TEXT */)) : _createCommentVNode(\"v-if\", true), $setup.user.accountId === $setup.commentObj.publishAccountId ? (_openBlock(), _createElementBlock(\"div\", _hoisted_9, \"我\")) : _createCommentVNode(\"v-if\", true), _createElementVNode(\"span\", {\n    title: $setup.commentObj.userArea\n  }, \"（\" + _toDisplayString($setup.userArea) + \"）\", 9 /* TEXT, PROPS */, _hoisted_10)]), _createVNode(_component_text_expansion_collapse, {\n    textClass: \"name\"\n  }, {\n    default: _withCtx(function () {\n      return [_createTextVNode(_toDisplayString($setup.commentObj.commentContent), 1 /* TEXT */)];\n    }),\n    _: 1 /* STABLE */\n  }), _createVNode(_component_xyl_global_file, {\n    fileData: $setup.commentObj.fileInfos\n  }, null, 8 /* PROPS */, [\"fileData\"]), _createElementVNode(\"div\", _hoisted_11, [_createTextVNode(_toDisplayString($setup.format($setup.commentObj.createDate)) + \" \", 1 /* TEXT */), $setup.user.accountId !== $setup.commentObj.publishAccountId && $setup.props.isReply ? (_openBlock(), _createElementBlock(\"span\", {\n    key: 0,\n    onClick: _cache[0] || (_cache[0] = function ($event) {\n      return $setup.handleComment($setup.commentObj);\n    })\n  }, \" 回复 \")) : _createCommentVNode(\"v-if\", true), $setup.user.accountId === $setup.commentObj.publishAccountId && $setup.props.isDel ? (_openBlock(), _createElementBlock(\"span\", {\n    key: 1,\n    onClick: _cache[1] || (_cache[1] = function ($event) {\n      return $setup.handleDel($setup.commentObj);\n    })\n  }, \" 删除 \")) : _createCommentVNode(\"v-if\", true), _renderSlot(_ctx.$slots, \"default\", {\n    row: $setup.commentObj\n  })]), $setup.commentId === $setup.commentObj.id ? (_openBlock(), _createElementBlock(\"div\", _hoisted_12, [_createVNode(_component_el_input, {\n    modelValue: $setup.content,\n    \"onUpdate:modelValue\": _cache[2] || (_cache[2] = function ($event) {\n      return $setup.content = $event;\n    }),\n    type: \"textarea\",\n    autosize: {\n      minRows: 2\n    },\n    placeholder: `请输入${$setup.props.text}`\n  }, null, 8 /* PROPS */, [\"modelValue\", \"placeholder\"]), _createElementVNode(\"div\", _hoisted_13, [_createVNode(_component_el_button, {\n    onClick: _cache[3] || (_cache[3] = function ($event) {\n      return $setup.commentId = '';\n    }),\n    link: \"\"\n  }, {\n    default: _withCtx(function () {\n      return _cache[8] || (_cache[8] = [_createTextVNode(\"取消回复\")]);\n    }),\n    _: 1 /* STABLE */\n  }), _createVNode(_component_el_button, {\n    type: \"primary\",\n    onClick: $setup.commentNew\n  }, {\n    default: _withCtx(function () {\n      return _cache[9] || (_cache[9] = [_createTextVNode(\"回复\")]);\n    }),\n    _: 1 /* STABLE */\n  })])])) : _createCommentVNode(\"v-if\", true), $setup.commentObj.children.length ? (_openBlock(), _createElementBlock(\"div\", _hoisted_14, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.commentObj.children.slice(0, $setup.commentNum), function (row) {\n    return _openBlock(), _createElementBlock(\"div\", {\n      class: \"xyl-global-comment-child\",\n      key: row.id\n    }, [_createElementVNode(\"div\", _hoisted_15, [_createVNode(_component_el_image, {\n      src: $setup.imgUrl(row.headImg),\n      fit: \"cover\"\n    }, null, 8 /* PROPS */, [\"src\"])]), _createElementVNode(\"div\", _hoisted_16, [!row.checkedStatus ? (_openBlock(), _createElementBlock(\"div\", _hoisted_17, \"审核中\")) : _createCommentVNode(\"v-if\", true), row.checkedStatus === 2 ? (_openBlock(), _createElementBlock(\"div\", _hoisted_18, \"审核不通过\")) : _createCommentVNode(\"v-if\", true), _createElementVNode(\"div\", _hoisted_19, [_createTextVNode(_toDisplayString(row.commentUserName) + \" \", 1 /* TEXT */), row.toCommenter && row.parentId !== $setup.commentObj.id ? (_openBlock(), _createElementBlock(\"span\", _hoisted_20, \"回复\")) : _createCommentVNode(\"v-if\", true), _createTextVNode(\" \" + _toDisplayString(row.parentId !== $setup.commentObj.id ? row.toCommenter : ''), 1 /* TEXT */)]), _createVNode(_component_text_expansion_collapse, {\n      textClass: \"name\"\n    }, {\n      default: _withCtx(function () {\n        return [_createTextVNode(_toDisplayString(row.commentContent), 1 /* TEXT */)];\n      }),\n      _: 2 /* DYNAMIC */\n    }, 1024 /* DYNAMIC_SLOTS */), _createVNode(_component_xyl_global_file, {\n      fileData: row.fileInfos\n    }, null, 8 /* PROPS */, [\"fileData\"]), _createElementVNode(\"div\", _hoisted_21, [_createTextVNode(_toDisplayString($setup.format(row.createDate)) + \" \", 1 /* TEXT */), $setup.user.accountId !== row.publishAccountId && $setup.props.isReply ? (_openBlock(), _createElementBlock(\"span\", {\n      key: 0,\n      onClick: function onClick($event) {\n        return $setup.handleComment(row);\n      }\n    }, \" 回复 \", 8 /* PROPS */, _hoisted_22)) : _createCommentVNode(\"v-if\", true), $setup.user.accountId === row.publishAccountId && $setup.props.isDel ? (_openBlock(), _createElementBlock(\"span\", {\n      key: 1,\n      onClick: function onClick($event) {\n        return $setup.handleDel(row);\n      }\n    }, \"删除\", 8 /* PROPS */, _hoisted_23)) : _createCommentVNode(\"v-if\", true), _renderSlot(_ctx.$slots, \"default\", {\n      row: row\n    })]), $setup.commentId === row.id ? (_openBlock(), _createElementBlock(\"div\", _hoisted_24, [_createVNode(_component_el_input, {\n      modelValue: $setup.content,\n      \"onUpdate:modelValue\": _cache[4] || (_cache[4] = function ($event) {\n        return $setup.content = $event;\n      }),\n      type: \"textarea\",\n      autosize: {\n        minRows: 2\n      },\n      placeholder: \"请输入回复\"\n    }, null, 8 /* PROPS */, [\"modelValue\"]), _createElementVNode(\"div\", _hoisted_25, [_createVNode(_component_el_button, {\n      onClick: _cache[5] || (_cache[5] = function ($event) {\n        return $setup.commentId = '';\n      }),\n      link: \"\"\n    }, {\n      default: _withCtx(function () {\n        return _toConsumableArray(_cache[10] || (_cache[10] = [_createTextVNode(\"取消回复\")]));\n      }),\n      _: 1 /* STABLE */\n    }), _createVNode(_component_el_button, {\n      type: \"primary\",\n      onClick: $setup.commentNew\n    }, {\n      default: _withCtx(function () {\n        return _toConsumableArray(_cache[11] || (_cache[11] = [_createTextVNode(\"回复\")]));\n      }),\n      _: 1 /* STABLE */\n    })])])) : _createCommentVNode(\"v-if\", true)])]);\n  }), 128 /* KEYED_FRAGMENT */)), $setup.commentObj.children.length > 2 ? (_openBlock(), _createElementBlock(\"div\", _hoisted_26, [$setup.commentObj.children.length > $setup.commentNum ? (_openBlock(), _createElementBlock(\"div\", {\n    key: 0,\n    class: \"xyl-global-comment-unfold\",\n    onClick: _cache[6] || (_cache[6] = function ($event) {\n      return $setup.moreComment(true);\n    })\n  }, [_cache[12] || (_cache[12] = _createTextVNode(\" 展开更多 \")), _createVNode(_component_el_icon, null, {\n    default: _withCtx(function () {\n      return [_createVNode(_component_DArrowRight)];\n    }),\n    _: 1 /* STABLE */\n  })])) : _createCommentVNode(\"v-if\", true), $setup.commentNum > 2 ? (_openBlock(), _createElementBlock(\"div\", {\n    key: 1,\n    class: \"xyl-global-comment-put-away\",\n    onClick: _cache[7] || (_cache[7] = function ($event) {\n      return $setup.moreComment();\n    })\n  }, [_cache[13] || (_cache[13] = _createTextVNode(\" 收起 \")), _createVNode(_component_el_icon, null, {\n    default: _withCtx(function () {\n      return [_createVNode(_component_DArrowRight)];\n    }),\n    _: 1 /* STABLE */\n  })])) : _createCommentVNode(\"v-if\", true)])) : _createCommentVNode(\"v-if\", true)])) : _createCommentVNode(\"v-if\", true)])])]);\n}", "map": {"version": 3, "names": ["class", "key", "_createElementBlock", "_hoisted_1", "_createElementVNode", "_hoisted_2", "_hoisted_3", "_createVNode", "_component_el_image", "src", "$setup", "imgUrl", "commentObj", "headImg", "fit", "_hoisted_4", "checkedStatus", "_hoisted_5", "_createCommentVNode", "_hoisted_6", "_hoisted_7", "_createTextVNode", "_toDisplayString", "commentUserName", "user", "accountId", "publishAccountId", "<PERSON><PERSON><PERSON>", "_hoisted_8", "_hoisted_9", "title", "userArea", "_hoisted_10", "_component_text_expansion_collapse", "textClass", "default", "_withCtx", "commentContent", "_", "_component_xyl_global_file", "fileData", "fileInfos", "_hoisted_11", "format", "createDate", "props", "isReply", "onClick", "_cache", "$event", "handleComment", "isDel", "handleDel", "_renderSlot", "_ctx", "$slots", "row", "commentId", "id", "_hoisted_12", "_component_el_input", "modelValue", "content", "type", "autosize", "minRows", "placeholder", "text", "_hoisted_13", "_component_el_button", "link", "commentNew", "children", "length", "_hoisted_14", "_Fragment", "_renderList", "slice", "commentNum", "_hoisted_15", "_hoisted_16", "_hoisted_17", "_hoisted_18", "_hoisted_19", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "parentId", "_hoisted_20", "_hoisted_21", "_hoisted_22", "_hoisted_23", "_hoisted_24", "_hoisted_25", "_toConsumableArray", "_hoisted_26", "moreComment", "_component_el_icon", "_component_DArrowRight"], "sources": ["D:\\zy\\xm\\pc\\5.0\\qiankun-product\\common\\components\\xyl-global-comment\\xyl-global-comment-item.vue"], "sourcesContent": ["<template>\r\n  <div class=\"xyl-global-comment-item\">\r\n    <div class=\"xyl-global-comment-info\">\r\n      <div class=\"xyl-global-comment-img\">\r\n        <el-image :src=\"imgUrl(commentObj.headImg)\" fit=\"cover\" />\r\n      </div>\r\n      <div class=\"xyl-global-comment-body\">\r\n        <div class=\"xyl-global-comment-status xyl-global-comment-centre\" v-if=\"!commentObj.checkedStatus\">审核中</div>\r\n        <div class=\"xyl-global-comment-status\" v-if=\"commentObj.checkedStatus === 2\">审核不通过</div>\r\n        <div class=\"xyl-global-comment-name\">\r\n          {{ commentObj.commentUserName }}\r\n          <div v-if=\"user.accountId !== commentObj.publishAccountId && roleName\">{{ roleName }}</div>\r\n          <div v-if=\"user.accountId === commentObj.publishAccountId\" class=\"xyl-global-comment-name-my\">我</div>\r\n          <span :title=\"commentObj.userArea\">（{{ userArea }}）</span>\r\n        </div>\r\n        <text-expansion-collapse textClass=\"name\">{{ commentObj.commentContent }}</text-expansion-collapse>\r\n        <xyl-global-file :fileData=\"commentObj.fileInfos\"></xyl-global-file>\r\n        <div class=\"xyl-global-comment-time\">\r\n          {{ format(commentObj.createDate) }}\r\n          <span\r\n            @click=\"handleComment(commentObj)\"\r\n            v-if=\"user.accountId !== commentObj.publishAccountId && props.isReply\">\r\n            回复\r\n          </span>\r\n          <span @click=\"handleDel(commentObj)\" v-if=\"user.accountId === commentObj.publishAccountId && props.isDel\">\r\n            删除\r\n          </span>\r\n          <slot :row=\"commentObj\"></slot>\r\n        </div>\r\n        <div class=\"xyl-global-comment-input\" v-if=\"commentId === commentObj.id\">\r\n          <el-input v-model=\"content\" type=\"textarea\" :autosize=\"{ minRows: 2 }\" :placeholder=\"`请输入${props.text}`\" />\r\n          <div class=\"xyl-global-comment-button\">\r\n            <el-button @click=\"commentId = ''\" link>取消回复</el-button>\r\n            <el-button type=\"primary\" @click=\"commentNew\">回复</el-button>\r\n          </div>\r\n        </div>\r\n        <div class=\"xyl-global-comment-child-list\" v-if=\"commentObj.children.length\">\r\n          <div class=\"xyl-global-comment-child\" v-for=\"row in commentObj.children.slice(0, commentNum)\" :key=\"row.id\">\r\n            <div class=\"xyl-global-comment-img\">\r\n              <el-image :src=\"imgUrl(row.headImg)\" fit=\"cover\" />\r\n            </div>\r\n            <div class=\"xyl-global-comment-body\">\r\n              <div class=\"xyl-global-comment-status xyl-global-comment-centre\" v-if=\"!row.checkedStatus\">审核中</div>\r\n              <div class=\"xyl-global-comment-status\" v-if=\"row.checkedStatus === 2\">审核不通过</div>\r\n              <div class=\"xyl-global-comment-name\">\r\n                {{ row.commentUserName }}\r\n                <span v-if=\"row.toCommenter && row.parentId !== commentObj.id\">回复</span>\r\n                {{ row.parentId !== commentObj.id ? row.toCommenter : '' }}\r\n              </div>\r\n              <text-expansion-collapse textClass=\"name\">{{ row.commentContent }}</text-expansion-collapse>\r\n              <xyl-global-file :fileData=\"row.fileInfos\"></xyl-global-file>\r\n              <div class=\"xyl-global-comment-time\">\r\n                {{ format(row.createDate) }}\r\n                <span @click=\"handleComment(row)\" v-if=\"user.accountId !== row.publishAccountId && props.isReply\">\r\n                  回复\r\n                </span>\r\n                <span @click=\"handleDel(row)\" v-if=\"user.accountId === row.publishAccountId && props.isDel\">删除</span>\r\n                <slot :row=\"row\"></slot>\r\n              </div>\r\n              <div class=\"xyl-global-comment-input\" v-if=\"commentId === row.id\">\r\n                <el-input v-model=\"content\" type=\"textarea\" :autosize=\"{ minRows: 2 }\" placeholder=\"请输入回复\" />\r\n                <div class=\"xyl-global-comment-button\">\r\n                  <el-button @click=\"commentId = ''\" link>取消回复</el-button>\r\n                  <el-button type=\"primary\" @click=\"commentNew\">回复</el-button>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n          <div class=\"xyl-global-comment-more\" v-if=\"commentObj.children.length > 2\">\r\n            <div\r\n              class=\"xyl-global-comment-unfold\"\r\n              @click=\"moreComment(true)\"\r\n              v-if=\"commentObj.children.length > commentNum\">\r\n              展开更多\r\n              <el-icon>\r\n                <DArrowRight />\r\n              </el-icon>\r\n            </div>\r\n            <div class=\"xyl-global-comment-put-away\" @click=\"moreComment()\" v-if=\"commentNum > 2\">\r\n              收起\r\n              <el-icon>\r\n                <DArrowRight />\r\n              </el-icon>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n<script>\r\nexport default { name: 'XylGlobalCommentItem' }\r\n</script>\r\n<script setup>\r\nimport api from '@/api'\r\nimport { ref, computed } from 'vue'\r\nimport { useStore } from 'vuex'\r\nimport { format } from 'common/js/time.js'\r\nimport { ElMessage, ElMessageBox } from 'element-plus'\r\nconst store = useStore()\r\nconst props = defineProps({\r\n  id: { type: String, default: '' },\r\n  type: { type: String, default: '' },\r\n  text: { type: String, default: '评论' },\r\n  prompt: { type: Boolean, default: true },\r\n  isDel: { type: Boolean, default: true },\r\n  isReply: { type: Boolean, default: true },\r\n  commentObj: { type: Object, default: () => ({}) },\r\n  checked: { type: Boolean, default: true }\r\n})\r\nconst emit = defineEmits(['refresh'])\r\n// 用户信息\r\nconst user = computed(() => store.getters.getUserFn)\r\n\r\n// 图片地址拼接组合\r\nconst imgUrl = (url) => (url ? api.fileURL(url) : api.defaultImgURL('default_user_head.jpg'))\r\n\r\nconst commentObj = computed(() => props.commentObj)\r\nconst userArea = computed(() => {\r\n  const userAreaList = props.commentObj?.userArea?.split(',') || []\r\n  if (userAreaList.length > 2) {\r\n    return userAreaList.splice(0, 2).join('，') + ' 等'\r\n  } else {\r\n    return props.commentObj.userArea\r\n  }\r\n})\r\nconst roleName = computed(() => {\r\n  if (props.commentObj.roles && props.commentObj.roles.length) {\r\n    return props.commentObj?.roles[0]?.roleName\r\n  } else {\r\n    return ''\r\n  }\r\n})\r\nconst commentNum = ref(2)\r\nconst commentId = ref('')\r\nconst content = ref('')\r\nconst handleComment = (row) => {\r\n  if (commentId.value === row.id) return\r\n  commentId.value = row.id\r\n  content.value = ''\r\n}\r\nconst commentNew = async () => {\r\n  const { code } = await api.commentNew({\r\n    form: {\r\n      businessCode: props.type,\r\n      businessId: props.id,\r\n      parentId: commentId.value,\r\n      commentContent: content.value,\r\n      terminalName: 'PC',\r\n      checkedStatus: props.checked ? 1 : 0\r\n    }\r\n  })\r\n  if (code === 200) {\r\n    ElMessage({ type: 'success', message: `${props.text}成功` })\r\n    commentId.value = ''\r\n    content.value = ''\r\n    emit('refresh', true)\r\n  }\r\n}\r\nconst moreComment = (type) => {\r\n  if (type) {\r\n    commentNum.value += 5\r\n  } else {\r\n    commentNum.value = 2\r\n  }\r\n}\r\nconst handleDel = (row) => {\r\n  if (props.prompt) {\r\n    ElMessageBox.confirm(`此操作将删除当前选中的${props.text}, 是否继续?`, '提示', {\r\n      confirmButtonText: '确定',\r\n      cancelButtonText: '取消',\r\n      type: 'warning'\r\n    })\r\n      .then(() => {\r\n        commentDel(row.id)\r\n      })\r\n      .catch(() => {\r\n        ElMessage({ type: 'info', message: '已取消删除' })\r\n      })\r\n  } else {\r\n    commentDel(row.id)\r\n  }\r\n}\r\nconst commentDel = async (id) => {\r\n  const { code } = await api.commentDel({ ids: [id] })\r\n  if (code === 200) {\r\n    ElMessage({ type: 'success', message: '删除成功' })\r\n    commentId.value = ''\r\n    content.value = ''\r\n    emit('refresh', commentObj.value.id !== id)\r\n  }\r\n}\r\n</script>\r\n<style lang=\"scss\">\r\n.xyl-global-comment-item {\r\n  width: 100%;\r\n  padding: 0 var(--zy-distance-two);\r\n\r\n  .xyl-global-comment-info {\r\n    width: 100%;\r\n    display: flex;\r\n    padding: var(--zy-distance-one) 0 var(--zy-distance-two) 0;\r\n    border-bottom: 1px solid var(--zy-el-border-color-lighter);\r\n\r\n    .xyl-global-comment-img {\r\n      width: 66px;\r\n\r\n      .zy-el-image {\r\n        width: 50px;\r\n        height: 50px;\r\n        border-radius: 50%;\r\n        overflow: hidden;\r\n      }\r\n    }\r\n\r\n    .xyl-global-comment-body {\r\n      width: calc(100% - 66px);\r\n      position: relative;\r\n\r\n      .xyl-global-comment-status {\r\n        position: absolute;\r\n        top: 0;\r\n        right: 0;\r\n        color: var(--el-color-info);\r\n        font-weight: normal;\r\n        font-size: var(--zy-text-font-size);\r\n        line-height: calc(var(--zy-name-font-size) * var(--zy-line-height));\r\n      }\r\n\r\n      .xyl-global-comment-centre {\r\n        color: var(--el-color-warning);\r\n      }\r\n\r\n      .xyl-global-comment-name {\r\n        display: flex;\r\n        align-items: center;\r\n        font-weight: bold;\r\n        padding-bottom: var(--zy-distance-four);\r\n        font-size: var(--zy-name-font-size);\r\n        line-height: var(--zy-line-height);\r\n\r\n        div {\r\n          font-weight: normal;\r\n          display: inline-block;\r\n          font-size: var(--zy-text-font-size);\r\n          line-height: var(--zy-line-height);\r\n          padding: 0 6px;\r\n          color: var(--el-color-warning);\r\n          background-color: var(--el-color-warning-light-9);\r\n          border-radius: var(--el-border-radius-small);\r\n          margin-left: var(--zy-distance-two);\r\n        }\r\n\r\n        .xyl-global-comment-name-my {\r\n          color: var(--el-color-danger);\r\n          background-color: var(--el-color-danger-light-9);\r\n        }\r\n\r\n        span {\r\n          font-weight: 400;\r\n          display: inline-block;\r\n          font-size: var(--zy-text-font-size);\r\n          line-height: var(--zy-line-height);\r\n          margin-left: var(--zy-distance-three);\r\n        }\r\n      }\r\n\r\n      .xyl-global-file {\r\n        padding-top: var(--zy-font-name-distance-five);\r\n        padding-bottom: 0;\r\n      }\r\n\r\n      .xyl-global-comment-time {\r\n        color: var(--zy-el-text-color-regular);\r\n        font-size: var(--zy-name-font-size);\r\n        line-height: var(--zy-line-height);\r\n        padding-bottom: var(--zy-distance-four);\r\n\r\n        span {\r\n          font-weight: 400;\r\n          display: inline-block;\r\n          font-size: var(--zy-name-font-size);\r\n          line-height: var(--zy-line-height);\r\n          margin-left: var(--zy-distance-three);\r\n          cursor: pointer;\r\n        }\r\n      }\r\n\r\n      .xyl-global-comment-input {\r\n        .xyl-global-comment-button {\r\n          display: flex;\r\n          justify-content: flex-end;\r\n          padding: var(--zy-distance-five) 0;\r\n        }\r\n      }\r\n\r\n      .xyl-global-comment-child-list {\r\n        width: 100%;\r\n        background: var(--zy-el-color-info-light-9);\r\n        margin-bottom: var(--zy-distance-four);\r\n        padding-top: var(--zy-distance-three);\r\n\r\n        .xyl-global-comment-child {\r\n          width: 100%;\r\n          display: flex;\r\n          padding: var(--zy-distance-four) var(--zy-distance-two) 0 var(--zy-distance-two);\r\n\r\n          .xyl-global-comment-img {\r\n            width: 46px;\r\n\r\n            .zy-el-image {\r\n              width: 32px;\r\n              height: 32px;\r\n              border-radius: 50%;\r\n              overflow: hidden;\r\n            }\r\n          }\r\n\r\n          .xyl-global-comment-body {\r\n            width: calc(100% - 46px);\r\n\r\n            .xyl-global-comment-name {\r\n              span {\r\n                margin: 0 var(--zy-distance-five);\r\n                color: var(--zy-el-text-color-regular);\r\n                font-size: var(--zy-name-font-size);\r\n                line-height: var(--zy-line-height);\r\n              }\r\n            }\r\n          }\r\n        }\r\n\r\n        .xyl-global-comment-more {\r\n          position: relative;\r\n          display: flex;\r\n          align-items: center;\r\n          cursor: pointer;\r\n          padding-left: 98px;\r\n          font-size: var(--zy-text-font-size);\r\n          height: var(--zy-height);\r\n\r\n          &::after {\r\n            content: '';\r\n            position: absolute;\r\n            top: 50%;\r\n            left: 0;\r\n            transform: translate(68px, -50%);\r\n            width: 22px;\r\n            height: 1px;\r\n            background: var(--zy-el-color-info-light-5);\r\n          }\r\n\r\n          .xyl-global-comment-unfold {\r\n            display: flex;\r\n            align-items: center;\r\n            margin-right: 32px;\r\n\r\n            .zy-el-icon {\r\n              transform: rotateZ(90deg);\r\n            }\r\n          }\r\n\r\n          .xyl-global-comment-put-away {\r\n            display: flex;\r\n            align-items: center;\r\n\r\n            .zy-el-icon {\r\n              transform: rotateZ(-90deg);\r\n            }\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;EACOA,KAAK,EAAC;AAAyB;;EAC7BA,KAAK,EAAC;AAAyB;;EAC7BA,KAAK,EAAC;AAAwB;;EAG9BA,KAAK,EAAC;AAAyB;;EAN1CC,GAAA;EAOaD,KAAK,EAAC;;;EAPnBC,GAAA;EAQaD,KAAK,EAAC;;;EACNA,KAAK,EAAC;AAAyB;;EAT5CC,GAAA;AAAA;;EAAAA,GAAA;EAYqED,KAAK,EAAC;;kBAZ3E;;EAiBaA,KAAK,EAAC;AAAyB;;EAjB5CC,GAAA;EA6BaD,KAAK,EAAC;;;EAEJA,KAAK,EAAC;AAA2B;;EA/BhDC,GAAA;EAoCaD,KAAK,EAAC;;;EAEFA,KAAK,EAAC;AAAwB;;EAG9BA,KAAK,EAAC;AAAyB;;EAzChDC,GAAA;EA0CmBD,KAAK,EAAC;;;EA1CzBC,GAAA;EA2CmBD,KAAK,EAAC;;;EACNA,KAAK,EAAC;AAAyB;;EA5ClDC,GAAA;AAAA;;EAmDmBD,KAAK,EAAC;AAAyB;kBAnDlD;kBAAA;;EAAAC,GAAA;EA2DmBD,KAAK,EAAC;;;EAEJA,KAAK,EAAC;AAA2B;;EA7DtDC,GAAA;EAoEeD,KAAK,EAAC;;;;;;;;;;uBAnEnBE,mBAAA,CAuFM,OAvFNC,UAuFM,GAtFJC,mBAAA,CAqFM,OArFNC,UAqFM,GApFJD,mBAAA,CAEM,OAFNE,UAEM,GADJC,YAAA,CAA0DC,mBAAA;IAA/CC,GAAG,EAAEC,MAAA,CAAAC,MAAM,CAACD,MAAA,CAAAE,UAAU,CAACC,OAAO;IAAGC,GAAG,EAAC;sCAElDV,mBAAA,CAgFM,OAhFNW,UAgFM,G,CA/EoEL,MAAA,CAAAE,UAAU,CAACI,aAAa,I,cAAhGd,mBAAA,CAA2G,OAA3Ge,UAA2G,EAAT,KAAG,KAP7GC,mBAAA,gBAQqDR,MAAA,CAAAE,UAAU,CAACI,aAAa,U,cAArEd,mBAAA,CAAwF,OAAxFiB,UAAwF,EAAX,OAAK,KAR1FD,mBAAA,gBASQd,mBAAA,CAKM,OALNgB,UAKM,GAddC,gBAAA,CAAAC,gBAAA,CAUaZ,MAAA,CAAAE,UAAU,CAACW,eAAe,IAAG,GAChC,iBAAWb,MAAA,CAAAc,IAAI,CAACC,SAAS,KAAKf,MAAA,CAAAE,UAAU,CAACc,gBAAgB,IAAIhB,MAAA,CAAAiB,QAAQ,I,cAArEzB,mBAAA,CAA2F,OAXrG0B,UAAA,EAAAN,gBAAA,CAWoFZ,MAAA,CAAAiB,QAAQ,oBAX5FT,mBAAA,gBAYqBR,MAAA,CAAAc,IAAI,CAACC,SAAS,KAAKf,MAAA,CAAAE,UAAU,CAACc,gBAAgB,I,cAAzDxB,mBAAA,CAAqG,OAArG2B,UAAqG,EAAP,GAAC,KAZzGX,mBAAA,gBAaUd,mBAAA,CAA0D;IAAnD0B,KAAK,EAAEpB,MAAA,CAAAE,UAAU,CAACmB;KAAU,GAAC,GAAAT,gBAAA,CAAGZ,MAAA,CAAAqB,QAAQ,IAAG,GAAC,uBAb7DC,WAAA,E,GAeQzB,YAAA,CAAmG0B,kCAAA;IAA1EC,SAAS,EAAC;EAAM;IAfjDC,OAAA,EAAAC,QAAA,CAekD;MAAA,OAA+B,CAfjFf,gBAAA,CAAAC,gBAAA,CAeqDZ,MAAA,CAAAE,UAAU,CAACyB,cAAc,iB;;IAf9EC,CAAA;MAgBQ/B,YAAA,CAAoEgC,0BAAA;IAAlDC,QAAQ,EAAE9B,MAAA,CAAAE,UAAU,CAAC6B;yCACvCrC,mBAAA,CAWM,OAXNsC,WAWM,GA5BdrB,gBAAA,CAAAC,gBAAA,CAkBaZ,MAAA,CAAAiC,MAAM,CAACjC,MAAA,CAAAE,UAAU,CAACgC,UAAU,KAAI,GACnC,iBAEQlC,MAAA,CAAAc,IAAI,CAACC,SAAS,KAAKf,MAAA,CAAAE,UAAU,CAACc,gBAAgB,IAAIhB,MAAA,CAAAmC,KAAK,CAACC,OAAO,I,cAFvE5C,mBAAA,CAIO;IAvBjBD,GAAA;IAoBa8C,OAAK,EAAAC,MAAA,QAAAA,MAAA,gBAAAC,MAAA;MAAA,OAAEvC,MAAA,CAAAwC,aAAa,CAACxC,MAAA,CAAAE,UAAU;IAAA;KACuC,MAEzE,KAvBVM,mBAAA,gBAwBqDR,MAAA,CAAAc,IAAI,CAACC,SAAS,KAAKf,MAAA,CAAAE,UAAU,CAACc,gBAAgB,IAAIhB,MAAA,CAAAmC,KAAK,CAACM,KAAK,I,cAAxGjD,mBAAA,CAEO;IA1BjBD,GAAA;IAwBiB8C,OAAK,EAAAC,MAAA,QAAAA,MAAA,gBAAAC,MAAA;MAAA,OAAEvC,MAAA,CAAA0C,SAAS,CAAC1C,MAAA,CAAAE,UAAU;IAAA;KAAwE,MAE1G,KA1BVM,mBAAA,gBA2BUmC,WAAA,CAA+BC,IAAA,CAAAC,MAAA;IAAxBC,GAAG,EAAE9C,MAAA,CAAAE;EAAU,G,GAEoBF,MAAA,CAAA+C,SAAS,KAAK/C,MAAA,CAAAE,UAAU,CAAC8C,EAAE,I,cAAvExD,mBAAA,CAMM,OANNyD,WAMM,GALJpD,YAAA,CAA2GqD,mBAAA;IA9BrHC,UAAA,EA8B6BnD,MAAA,CAAAoD,OAAO;IA9BpC,uBAAAd,MAAA,QAAAA,MAAA,gBAAAC,MAAA;MAAA,OA8B6BvC,MAAA,CAAAoD,OAAO,GAAAb,MAAA;IAAA;IAAEc,IAAI,EAAC,UAAU;IAAEC,QAAQ,EAAE;MAAAC,OAAA;IAAA,CAAc;IAAGC,WAAW,QAAQxD,MAAA,CAAAmC,KAAK,CAACsB,IAAI;0DACrG/D,mBAAA,CAGM,OAHNgE,WAGM,GAFJ7D,YAAA,CAAwD8D,oBAAA;IAA5CtB,OAAK,EAAAC,MAAA,QAAAA,MAAA,gBAAAC,MAAA;MAAA,OAAEvC,MAAA,CAAA+C,SAAS;IAAA;IAAOa,IAAI,EAAJ;;IAhC/CnC,OAAA,EAAAC,QAAA,CAgCoD;MAAA,OAAIY,MAAA,QAAAA,MAAA,OAhCxD3B,gBAAA,CAgCoD,MAAI,E;;IAhCxDiB,CAAA;MAiCY/B,YAAA,CAA4D8D,oBAAA;IAAjDN,IAAI,EAAC,SAAS;IAAEhB,OAAK,EAAErC,MAAA,CAAA6D;;IAjC9CpC,OAAA,EAAAC,QAAA,CAiC0D;MAAA,OAAEY,MAAA,QAAAA,MAAA,OAjC5D3B,gBAAA,CAiC0D,IAAE,E;;IAjC5DiB,CAAA;YAAApB,mBAAA,gBAoCyDR,MAAA,CAAAE,UAAU,CAAC4D,QAAQ,CAACC,MAAM,I,cAA3EvE,mBAAA,CAiDM,OAjDNwE,WAiDM,I,kBAhDJxE,mBAAA,CA8BMyE,SAAA,QAnEhBC,WAAA,CAqC8DlE,MAAA,CAAAE,UAAU,CAAC4D,QAAQ,CAACK,KAAK,IAAInE,MAAA,CAAAoE,UAAU,GArCrG,UAqCuDtB,GAAG;yBAAhDtD,mBAAA,CA8BM;MA9BDF,KAAK,EAAC,0BAA0B;MAA0DC,GAAG,EAAEuD,GAAG,CAACE;QACtGtD,mBAAA,CAEM,OAFN2E,WAEM,GADJxE,YAAA,CAAmDC,mBAAA;MAAxCC,GAAG,EAAEC,MAAA,CAAAC,MAAM,CAAC6C,GAAG,CAAC3C,OAAO;MAAGC,GAAG,EAAC;wCAE3CV,mBAAA,CAyBM,OAzBN4E,WAyBM,G,CAxBoExB,GAAG,CAACxC,aAAa,I,cAAzFd,mBAAA,CAAoG,OAApG+E,WAAoG,EAAT,KAAG,KA1C5G/D,mBAAA,gBA2C2DsC,GAAG,CAACxC,aAAa,U,cAA9Dd,mBAAA,CAAiF,OAAjFgF,WAAiF,EAAX,OAAK,KA3CzFhE,mBAAA,gBA4Ccd,mBAAA,CAIM,OAJN+E,WAIM,GAhDpB9D,gBAAA,CAAAC,gBAAA,CA6CmBkC,GAAG,CAACjC,eAAe,IAAG,GACzB,iBAAYiC,GAAG,CAAC4B,WAAW,IAAI5B,GAAG,CAAC6B,QAAQ,KAAK3E,MAAA,CAAAE,UAAU,CAAC8C,EAAE,I,cAA7DxD,mBAAA,CAAwE,QA9CxFoF,WAAA,EA8C+E,IAAE,KA9CjFpE,mBAAA,gBAAAG,gBAAA,CA8CwF,GACxE,GAAAC,gBAAA,CAAGkC,GAAG,CAAC6B,QAAQ,KAAK3E,MAAA,CAAAE,UAAU,CAAC8C,EAAE,GAAGF,GAAG,CAAC4B,WAAW,sB,GAErD7E,YAAA,CAA4F0B,kCAAA;MAAnEC,SAAS,EAAC;IAAM;MAjDvDC,OAAA,EAAAC,QAAA,CAiDwD;QAAA,OAAwB,CAjDhFf,gBAAA,CAAAC,gBAAA,CAiD2DkC,GAAG,CAACnB,cAAc,iB;;MAjD7EC,CAAA;kCAkDc/B,YAAA,CAA6DgC,0BAAA;MAA3CC,QAAQ,EAAEgB,GAAG,CAACf;2CAChCrC,mBAAA,CAOM,OAPNmF,WAOM,GA1DpBlE,gBAAA,CAAAC,gBAAA,CAoDmBZ,MAAA,CAAAiC,MAAM,CAACa,GAAG,CAACZ,UAAU,KAAI,GAC5B,iBAAwClC,MAAA,CAAAc,IAAI,CAACC,SAAS,KAAK+B,GAAG,CAAC9B,gBAAgB,IAAIhB,MAAA,CAAAmC,KAAK,CAACC,OAAO,I,cAAhG5C,mBAAA,CAEO;MAvDvBD,GAAA;MAqDuB8C,OAAK,WAALA,OAAKA,CAAAE,MAAA;QAAA,OAAEvC,MAAA,CAAAwC,aAAa,CAACM,GAAG;MAAA;OAAmE,MAElG,iBAvDhBgC,WAAA,KAAAtE,mBAAA,gBAwDoDR,MAAA,CAAAc,IAAI,CAACC,SAAS,KAAK+B,GAAG,CAAC9B,gBAAgB,IAAIhB,MAAA,CAAAmC,KAAK,CAACM,KAAK,I,cAA1FjD,mBAAA,CAAqG;MAxDrHD,GAAA;MAwDuB8C,OAAK,WAALA,OAAKA,CAAAE,MAAA;QAAA,OAAEvC,MAAA,CAAA0C,SAAS,CAACI,GAAG;MAAA;OAAiE,IAAE,iBAxD9GiC,WAAA,KAAAvE,mBAAA,gBAyDgBmC,WAAA,CAAwBC,IAAA,CAAAC,MAAA;MAAjBC,GAAG,EAAEA;IAAG,G,GAE2B9C,MAAA,CAAA+C,SAAS,KAAKD,GAAG,CAACE,EAAE,I,cAAhExD,mBAAA,CAMM,OANNwF,WAMM,GALJnF,YAAA,CAA6FqD,mBAAA;MA5D7GC,UAAA,EA4DmCnD,MAAA,CAAAoD,OAAO;MA5D1C,uBAAAd,MAAA,QAAAA,MAAA,gBAAAC,MAAA;QAAA,OA4DmCvC,MAAA,CAAAoD,OAAO,GAAAb,MAAA;MAAA;MAAEc,IAAI,EAAC,UAAU;MAAEC,QAAQ,EAAE;QAAAC,OAAA;MAAA,CAAc;MAAEC,WAAW,EAAC;6CACnF9D,mBAAA,CAGM,OAHNuF,WAGM,GAFJpF,YAAA,CAAwD8D,oBAAA;MAA5CtB,OAAK,EAAAC,MAAA,QAAAA,MAAA,gBAAAC,MAAA;QAAA,OAAEvC,MAAA,CAAA+C,SAAS;MAAA;MAAOa,IAAI,EAAJ;;MA9DrDnC,OAAA,EAAAC,QAAA,CA8D0D;QAAA,OAAAwD,kBAAA,CAAI5C,MAAA,SAAAA,MAAA,QA9D9D3B,gBAAA,CA8D0D,MAAI,E;;MA9D9DiB,CAAA;QA+DkB/B,YAAA,CAA4D8D,oBAAA;MAAjDN,IAAI,EAAC,SAAS;MAAEhB,OAAK,EAAErC,MAAA,CAAA6D;;MA/DpDpC,OAAA,EAAAC,QAAA,CA+DgE;QAAA,OAAAwD,kBAAA,CAAE5C,MAAA,SAAAA,MAAA,QA/DlE3B,gBAAA,CA+DgE,IAAE,E;;MA/DlEiB,CAAA;cAAApB,mBAAA,e;kCAoEqDR,MAAA,CAAAE,UAAU,CAAC4D,QAAQ,CAACC,MAAM,Q,cAArEvE,mBAAA,CAgBM,OAhBN2F,WAgBM,GAZInF,MAAA,CAAAE,UAAU,CAAC4D,QAAQ,CAACC,MAAM,GAAG/D,MAAA,CAAAoE,UAAU,I,cAH/C5E,mBAAA,CAQM;IA7ElBD,GAAA;IAsEcD,KAAK,EAAC,2BAA2B;IAChC+C,OAAK,EAAAC,MAAA,QAAAA,MAAA,gBAAAC,MAAA;MAAA,OAAEvC,MAAA,CAAAoF,WAAW;IAAA;kCAvEjCzE,gBAAA,CAwE6D,QAE/C,IAAAd,YAAA,CAEUwF,kBAAA;IA5ExB5D,OAAA,EAAAC,QAAA,CA2EgB;MAAA,OAAe,CAAf7B,YAAA,CAAeyF,sBAAA,E;;IA3E/B1D,CAAA;UAAApB,mBAAA,gBA8EkFR,MAAA,CAAAoE,UAAU,Q,cAAhF5E,mBAAA,CAKM;IAnFlBD,GAAA;IA8EiBD,KAAK,EAAC,6BAA6B;IAAE+C,OAAK,EAAAC,MAAA,QAAAA,MAAA,gBAAAC,MAAA;MAAA,OAAEvC,MAAA,CAAAoF,WAAW;IAAA;kCA9ExEzE,gBAAA,CA8EkG,MAEpF,IAAAd,YAAA,CAEUwF,kBAAA;IAlFxB5D,OAAA,EAAAC,QAAA,CAiFgB;MAAA,OAAe,CAAf7B,YAAA,CAAeyF,sBAAA,E;;IAjF/B1D,CAAA;UAAApB,mBAAA,e,KAAAA,mBAAA,e,KAAAA,mBAAA,e", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "function _toConsumableArray(r) { return _arrayWithoutHoles(r) || _iterableToArray(r) || _unsupportedIterableToArray(r) || _nonIterableSpread(); }\nfunction _nonIterableSpread() { throw new TypeError(\"Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); }\nfunction _unsupportedIterableToArray(r, a) { if (r) { if (\"string\" == typeof r) return _arrayLikeToArray(r, a); var t = {}.toString.call(r).slice(8, -1); return \"Object\" === t && r.constructor && (t = r.constructor.name), \"Map\" === t || \"Set\" === t ? Array.from(r) : \"Arguments\" === t || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t) ? _arrayLikeToArray(r, a) : void 0; } }\nfunction _iterableToArray(r) { if (\"undefined\" != typeof Symbol && null != r[Symbol.iterator] || null != r[\"@@iterator\"]) return Array.from(r); }\nfunction _arrayWithoutHoles(r) { if (Array.isArray(r)) return _arrayLikeToArray(r); }\nfunction _arrayLikeToArray(r, a) { (null == a || a > r.length) && (a = r.length); for (var e = 0, n = Array(a); e < a; e++) n[e] = r[e]; return n; }\nimport { ref, reactive, computed, watch, nextTick, onMounted } from 'vue';\nimport { encryptPhone } from 'common/js/utils.js';\nimport { systemMobileEncrypt } from 'common/js/system_var.js';\nimport elementResizeDetectorMaker from 'element-resize-detector';\nvar __default__ = {\n  name: 'VirtualUser'\n};\nexport default /*@__PURE__*/Object.assign(__default__, {\n  props: {\n    modelValue: {\n      type: Array,\n      default: function _default() {\n        return [];\n      }\n    },\n    data: {\n      type: Array,\n      default: function _default() {\n        return [];\n      }\n    },\n    disabledUser: Function,\n    nodeKey: {\n      type: String,\n      default: 'id'\n    }\n  },\n  emits: ['update:modelValue', 'handleChange'],\n  setup(__props, _ref) {\n    var __expose = _ref.expose,\n      __emit = _ref.emit;\n    __expose();\n    var erd = elementResizeDetectorMaker();\n    var props = __props;\n    var emit = __emit;\n    var virtualList = ref();\n    var virtualScrollbar = ref();\n    var virtualPlaceholder = ref();\n    var userId = computed({\n      get() {\n        return props.modelValue;\n      },\n      set(value) {\n        emit('update:modelValue', value);\n      }\n    });\n    // 组件记录(默认)\n    var virtualRecord = reactive({\n      height: 400,\n      // 展示几个\n      visibleCount: 66,\n      // 刷新频率\n      timeout: 4,\n      // 行高\n      itemHeight: 50,\n      // translateY偏移量\n      offset: 0,\n      // 虚拟占位高度\n      virtualHeight: 300,\n      // 记录滚动高度\n      recordScrollTop: 0,\n      dataList: [],\n      // 可展示的数据\n      visibleData: []\n    });\n    // 合并配置\n    var mergeFn = function mergeFn() {\n      virtualRecord.dataList = JSON.parse(JSON.stringify(props.data));\n      // 虚拟高度\n      virtualRecord.virtualHeight = props.data.length * virtualRecord.itemHeight;\n      // 展示数量\n      virtualRecord.visibleCount = Math.ceil(virtualRecord.height / virtualRecord.itemHeight);\n    };\n    var lastTime = 0;\n    var handleScroll = function handleScroll(scroll) {\n      var currentTime = +new Date();\n      if (currentTime - lastTime > virtualRecord.timeout) {\n        virtualRecord.recordScrollTop = scroll.scrollTop;\n        updateVisibleData(scroll.scrollTop);\n        lastTime = currentTime;\n      }\n    };\n    var updateVisibleData = function updateVisibleData(scrollTop) {\n      var start = Math.floor(scrollTop / virtualRecord.itemHeight) - Math.floor(virtualRecord.visibleCount / 2);\n      start = start < 0 ? 0 : start;\n      var end = start + virtualRecord.visibleCount * 2;\n      virtualRecord.visibleData = virtualRecord.dataList.slice(start, end);\n      virtualRecord.offset = start * virtualRecord.itemHeight;\n      nextTick(function () {\n        virtualScrollbar.value.update();\n      });\n    };\n    var handleClick = function handleClick(item) {\n      if (disabledUser(item[props.nodeKey])) return;\n      var newUserId = [];\n      if (userId.value.includes(item[props.nodeKey])) {\n        for (var index = 0; index < userId.value.length; index++) {\n          if (userId.value[index] !== item[props.nodeKey]) {\n            newUserId.push(userId.value[index]);\n          }\n        }\n      } else {\n        newUserId = [].concat(_toConsumableArray(userId.value), [item[props.nodeKey]]);\n      }\n      userId.value = newUserId;\n      emit('handleChange', newUserId);\n    };\n    var handleChange = function handleChange(value) {\n      emit('handleChange', value);\n    };\n    var disabledUser = function disabledUser(id) {\n      if (typeof props.disabledUser === 'function') {\n        return props.disabledUser(id);\n      } else {\n        return false;\n      }\n    };\n    var handleMobile = function handleMobile(mobile) {\n      return systemMobileEncrypt.value ? encryptPhone(mobile) : mobile;\n    };\n    watch(function () {\n      return props.data;\n    }, function () {\n      // 合并数据\n      mergeFn();\n      // 更新视图\n      updateVisibleData(virtualRecord.recordScrollTop);\n    }, {\n      immediate: true,\n      deep: true\n    });\n    onMounted(function () {\n      nextTick(function () {\n        erd.listenTo(virtualList.value, function (e) {\n          virtualRecord.height = e.offsetHeight;\n          // 合并数据\n          mergeFn();\n          // 更新视图\n          updateVisibleData(virtualRecord.recordScrollTop);\n        });\n        erd.listenTo(virtualPlaceholder.value, function (e) {\n          virtualRecord.itemHeight = e.offsetHeight;\n          // 合并数据\n          mergeFn();\n          // 更新视图\n          updateVisibleData(virtualRecord.recordScrollTop);\n        });\n      });\n    });\n    var __returned__ = {\n      erd,\n      props,\n      emit,\n      virtualList,\n      virtualScrollbar,\n      virtualPlaceholder,\n      userId,\n      virtualRecord,\n      mergeFn,\n      get lastTime() {\n        return lastTime;\n      },\n      set lastTime(v) {\n        lastTime = v;\n      },\n      handleScroll,\n      updateVisibleData,\n      handleClick,\n      handleChange,\n      disabledUser,\n      handleMobile,\n      ref,\n      reactive,\n      computed,\n      watch,\n      nextTick,\n      onMounted,\n      get encryptPhone() {\n        return encryptPhone;\n      },\n      get systemMobileEncrypt() {\n        return systemMobileEncrypt;\n      },\n      get elementResizeDetectorMaker() {\n        return elementResizeDetectorMaker;\n      }\n    };\n    Object.defineProperty(__returned__, '__isScriptSetup', {\n      enumerable: false,\n      value: true\n    });\n    return __returned__;\n  }\n});", "map": {"version": 3, "names": ["ref", "reactive", "computed", "watch", "nextTick", "onMounted", "encryptPhone", "systemMobileEncrypt", "elementResizeDetectorMaker", "__default__", "name", "erd", "props", "__props", "emit", "__emit", "virtualList", "virtualScrollbar", "virtualPlaceholder", "userId", "get", "modelValue", "set", "value", "virtualRecord", "height", "visibleCount", "timeout", "itemHeight", "offset", "virtualHeight", "recordScrollTop", "dataList", "visibleData", "mergeFn", "JSON", "parse", "stringify", "data", "length", "Math", "ceil", "lastTime", "handleScroll", "scroll", "currentTime", "Date", "scrollTop", "updateVisibleData", "start", "floor", "end", "slice", "update", "handleClick", "item", "disabledUser", "nodeKey", "newUserId", "includes", "index", "push", "concat", "_toConsumableArray", "handleChange", "id", "handleMobile", "mobile", "immediate", "deep", "listenTo", "e", "offsetHeight"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/common/components/virtualElement/virtual-user.vue"], "sourcesContent": ["<template>\r\n  <div class=\"virtual-user\" ref=\"virtualList\">\r\n    <div class=\"virtual-placeholder\" ref=\"virtualPlaceholder\">\r\n      <div class=\"virtual-user-item\">\r\n        <div class=\"virtual-user-name ellipsis\">占位</div>\r\n      </div>\r\n    </div>\r\n    <el-scrollbar @scroll=\"handleScroll\" ref=\"virtualScrollbar\">\r\n      <!-- 虚拟高度 -->\r\n      <div class=\"virtualBody\" :style=\"{ height: virtualRecord.virtualHeight + 'px' }\"></div>\r\n      <!-- 真实列表 -->\r\n      <el-checkbox-group\r\n        v-model=\"userId\"\r\n        :class=\"['realBody']\"\r\n        :style=\"{ transform: `translateY(${virtualRecord.offset}px)` }\"\r\n        @change=\"handleChange\">\r\n        <div\r\n          class=\"virtual-user-item\"\r\n          v-for=\"(item, index) in virtualRecord.visibleData\"\r\n          :key=\"index + '_virtual-user'\"\r\n          @click=\"handleClick(item)\">\r\n          <div class=\"virtual-user-name ellipsis\" :title=\"`${item.userName} - ${handleMobile(item.mobile)}`\">\r\n            {{ item.userName }} - {{ handleMobile(item.mobile) }}\r\n          </div>\r\n          <el-checkbox\r\n            @click.stop\r\n            :label=\"item[props.nodeKey]\"\r\n            :disabled=\"disabledUser(item[props.nodeKey])\"></el-checkbox>\r\n        </div>\r\n      </el-checkbox-group>\r\n    </el-scrollbar>\r\n  </div>\r\n</template>\r\n<script>\r\nexport default { name: 'VirtualUser' }\r\n</script>\r\n<script setup>\r\nimport { ref, reactive, computed, watch, nextTick, onMounted } from 'vue'\r\nimport { encryptPhone } from 'common/js/utils.js'\r\nimport { systemMobileEncrypt } from 'common/js/system_var.js'\r\nimport elementResizeDetectorMaker from 'element-resize-detector'\r\nconst erd = elementResizeDetectorMaker()\r\nconst props = defineProps({\r\n  modelValue: { type: Array, default: () => [] },\r\n  data: { type: Array, default: () => [] },\r\n  disabledUser: Function,\r\n  nodeKey: { type: String, default: 'id' }\r\n})\r\nconst emit = defineEmits(['update:modelValue', 'handleChange'])\r\nconst virtualList = ref()\r\nconst virtualScrollbar = ref()\r\nconst virtualPlaceholder = ref()\r\nconst userId = computed({\r\n  get() {\r\n    return props.modelValue\r\n  },\r\n  set(value) {\r\n    emit('update:modelValue', value)\r\n  }\r\n})\r\n// 组件记录(默认)\r\nconst virtualRecord = reactive({\r\n  height: 400,\r\n  // 展示几个\r\n  visibleCount: 66,\r\n  // 刷新频率\r\n  timeout: 4,\r\n  // 行高\r\n  itemHeight: 50,\r\n  // translateY偏移量\r\n  offset: 0,\r\n  // 虚拟占位高度\r\n  virtualHeight: 300,\r\n  // 记录滚动高度\r\n  recordScrollTop: 0,\r\n  dataList: [],\r\n  // 可展示的数据\r\n  visibleData: []\r\n})\r\n// 合并配置\r\nconst mergeFn = () => {\r\n  virtualRecord.dataList = JSON.parse(JSON.stringify(props.data))\r\n  // 虚拟高度\r\n  virtualRecord.virtualHeight = props.data.length * virtualRecord.itemHeight\r\n  // 展示数量\r\n  virtualRecord.visibleCount = Math.ceil(virtualRecord.height / virtualRecord.itemHeight)\r\n}\r\nlet lastTime = 0\r\nconst handleScroll = (scroll) => {\r\n  const currentTime = +new Date()\r\n  if (currentTime - lastTime > virtualRecord.timeout) {\r\n    virtualRecord.recordScrollTop = scroll.scrollTop\r\n    updateVisibleData(scroll.scrollTop)\r\n    lastTime = currentTime\r\n  }\r\n}\r\nconst updateVisibleData = (scrollTop) => {\r\n  let start = Math.floor(scrollTop / virtualRecord.itemHeight) - Math.floor(virtualRecord.visibleCount / 2)\r\n  start = start < 0 ? 0 : start\r\n  const end = start + virtualRecord.visibleCount * 2\r\n  virtualRecord.visibleData = virtualRecord.dataList.slice(start, end)\r\n  virtualRecord.offset = start * virtualRecord.itemHeight\r\n  nextTick(() => {\r\n    virtualScrollbar.value.update()\r\n  })\r\n}\r\nconst handleClick = (item) => {\r\n  if (disabledUser(item[props.nodeKey])) return\r\n  let newUserId = []\r\n  if (userId.value.includes(item[props.nodeKey])) {\r\n    for (let index = 0; index < userId.value.length; index++) {\r\n      if (userId.value[index] !== item[props.nodeKey]) {\r\n        newUserId.push(userId.value[index])\r\n      }\r\n    }\r\n  } else {\r\n    newUserId = [...userId.value, item[props.nodeKey]]\r\n  }\r\n  userId.value = newUserId\r\n  emit('handleChange', newUserId)\r\n}\r\nconst handleChange = (value) => {\r\n  emit('handleChange', value)\r\n}\r\nconst disabledUser = (id) => {\r\n  if (typeof props.disabledUser === 'function') {\r\n    return props.disabledUser(id)\r\n  } else {\r\n    return false\r\n  }\r\n}\r\nconst handleMobile = (mobile) => (systemMobileEncrypt.value ? encryptPhone(mobile) : mobile)\r\nwatch(\r\n  () => props.data,\r\n  () => {\r\n    // 合并数据\r\n    mergeFn()\r\n    // 更新视图\r\n    updateVisibleData(virtualRecord.recordScrollTop)\r\n  },\r\n  { immediate: true, deep: true }\r\n)\r\nonMounted(() => {\r\n  nextTick(() => {\r\n    erd.listenTo(virtualList.value, (e) => {\r\n      virtualRecord.height = e.offsetHeight\r\n      // 合并数据\r\n      mergeFn()\r\n      // 更新视图\r\n      updateVisibleData(virtualRecord.recordScrollTop)\r\n    })\r\n    erd.listenTo(virtualPlaceholder.value, (e) => {\r\n      virtualRecord.itemHeight = e.offsetHeight\r\n      // 合并数据\r\n      mergeFn()\r\n      // 更新视图\r\n      updateVisibleData(virtualRecord.recordScrollTop)\r\n    })\r\n  })\r\n})\r\n</script>\r\n<style lang=\"scss\">\r\n.virtual-user {\r\n  width: 100%;\r\n  height: calc(100% - ((var(--zy-name-font-size) * var(--zy-line-height)) + (var(--zy-distance-four) * 2)));\r\n\r\n  .virtual-placeholder {\r\n    position: fixed;\r\n    top: -200%;\r\n    left: -200%;\r\n  }\r\n\r\n  .zy-el-scrollbar {\r\n    height: 100%;\r\n\r\n    .zy-el-scrollbar__view {\r\n      position: relative;\r\n    }\r\n\r\n    .virtualBody {\r\n      width: 100%;\r\n      position: absolute;\r\n      z-index: -10;\r\n    }\r\n\r\n    .realBody {\r\n      width: 100%;\r\n      position: absolute;\r\n    }\r\n  }\r\n\r\n  .virtual-user-item {\r\n    display: flex;\r\n    align-items: center;\r\n    cursor: pointer;\r\n\r\n    .virtual-user-name {\r\n      width: calc(100% - 40px);\r\n      font-size: var(--zy-text-font-size);\r\n      line-height: var(--zy-line-height);\r\n      position: relative;\r\n      padding: var(--zy-distance-four) 0;\r\n      padding-left: var(--zy-distance-one);\r\n\r\n      &::after {\r\n        content: '';\r\n        position: absolute;\r\n        top: 50%;\r\n        left: var(--zy-distance-two);\r\n        width: var(--zy-text-font-size);\r\n        height: var(--zy-text-font-size);\r\n        transform: translate(-50%, -50%);\r\n        background: url('./img/select_person_user_icon.png') no-repeat;\r\n        background-size: 100% 100%;\r\n      }\r\n    }\r\n\r\n    .zy-el-checkbox {\r\n      width: 40px;\r\n      display: flex;\r\n      justify-content: flex-end;\r\n      padding-right: var(--zy-distance-three);\r\n\r\n      .zy-el-checkbox__label {\r\n        display: none;\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;AAqCA,SAASA,GAAG,EAAEC,QAAQ,EAAEC,QAAQ,EAAEC,KAAK,EAAEC,QAAQ,EAAEC,SAAS,QAAQ,KAAK;AACzE,SAASC,YAAY,QAAQ,oBAAoB;AACjD,SAASC,mBAAmB,QAAQ,yBAAyB;AAC7D,OAAOC,0BAA0B,MAAM,yBAAyB;AANhE,IAAAC,WAAA,GAAe;EAAEC,IAAI,EAAE;AAAc,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;IAOtC,IAAMC,GAAG,GAAGH,0BAA0B,CAAC,CAAC;IACxC,IAAMI,KAAK,GAAGC,OAKZ;IACF,IAAMC,IAAI,GAAGC,MAAkD;IAC/D,IAAMC,WAAW,GAAGhB,GAAG,CAAC,CAAC;IACzB,IAAMiB,gBAAgB,GAAGjB,GAAG,CAAC,CAAC;IAC9B,IAAMkB,kBAAkB,GAAGlB,GAAG,CAAC,CAAC;IAChC,IAAMmB,MAAM,GAAGjB,QAAQ,CAAC;MACtBkB,GAAGA,CAAA,EAAG;QACJ,OAAOR,KAAK,CAACS,UAAU;MACzB,CAAC;MACDC,GAAGA,CAACC,KAAK,EAAE;QACTT,IAAI,CAAC,mBAAmB,EAAES,KAAK,CAAC;MAClC;IACF,CAAC,CAAC;IACF;IACA,IAAMC,aAAa,GAAGvB,QAAQ,CAAC;MAC7BwB,MAAM,EAAE,GAAG;MACX;MACAC,YAAY,EAAE,EAAE;MAChB;MACAC,OAAO,EAAE,CAAC;MACV;MACAC,UAAU,EAAE,EAAE;MACd;MACAC,MAAM,EAAE,CAAC;MACT;MACAC,aAAa,EAAE,GAAG;MAClB;MACAC,eAAe,EAAE,CAAC;MAClBC,QAAQ,EAAE,EAAE;MACZ;MACAC,WAAW,EAAE;IACf,CAAC,CAAC;IACF;IACA,IAAMC,OAAO,GAAG,SAAVA,OAAOA,CAAA,EAAS;MACpBV,aAAa,CAACQ,QAAQ,GAAGG,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,SAAS,CAACzB,KAAK,CAAC0B,IAAI,CAAC,CAAC;MAC/D;MACAd,aAAa,CAACM,aAAa,GAAGlB,KAAK,CAAC0B,IAAI,CAACC,MAAM,GAAGf,aAAa,CAACI,UAAU;MAC1E;MACAJ,aAAa,CAACE,YAAY,GAAGc,IAAI,CAACC,IAAI,CAACjB,aAAa,CAACC,MAAM,GAAGD,aAAa,CAACI,UAAU,CAAC;IACzF,CAAC;IACD,IAAIc,QAAQ,GAAG,CAAC;IAChB,IAAMC,YAAY,GAAG,SAAfA,YAAYA,CAAIC,MAAM,EAAK;MAC/B,IAAMC,WAAW,GAAG,CAAC,IAAIC,IAAI,CAAC,CAAC;MAC/B,IAAID,WAAW,GAAGH,QAAQ,GAAGlB,aAAa,CAACG,OAAO,EAAE;QAClDH,aAAa,CAACO,eAAe,GAAGa,MAAM,CAACG,SAAS;QAChDC,iBAAiB,CAACJ,MAAM,CAACG,SAAS,CAAC;QACnCL,QAAQ,GAAGG,WAAW;MACxB;IACF,CAAC;IACD,IAAMG,iBAAiB,GAAG,SAApBA,iBAAiBA,CAAID,SAAS,EAAK;MACvC,IAAIE,KAAK,GAAGT,IAAI,CAACU,KAAK,CAACH,SAAS,GAAGvB,aAAa,CAACI,UAAU,CAAC,GAAGY,IAAI,CAACU,KAAK,CAAC1B,aAAa,CAACE,YAAY,GAAG,CAAC,CAAC;MACzGuB,KAAK,GAAGA,KAAK,GAAG,CAAC,GAAG,CAAC,GAAGA,KAAK;MAC7B,IAAME,GAAG,GAAGF,KAAK,GAAGzB,aAAa,CAACE,YAAY,GAAG,CAAC;MAClDF,aAAa,CAACS,WAAW,GAAGT,aAAa,CAACQ,QAAQ,CAACoB,KAAK,CAACH,KAAK,EAAEE,GAAG,CAAC;MACpE3B,aAAa,CAACK,MAAM,GAAGoB,KAAK,GAAGzB,aAAa,CAACI,UAAU;MACvDxB,QAAQ,CAAC,YAAM;QACba,gBAAgB,CAACM,KAAK,CAAC8B,MAAM,CAAC,CAAC;MACjC,CAAC,CAAC;IACJ,CAAC;IACD,IAAMC,WAAW,GAAG,SAAdA,WAAWA,CAAIC,IAAI,EAAK;MAC5B,IAAIC,YAAY,CAACD,IAAI,CAAC3C,KAAK,CAAC6C,OAAO,CAAC,CAAC,EAAE;MACvC,IAAIC,SAAS,GAAG,EAAE;MAClB,IAAIvC,MAAM,CAACI,KAAK,CAACoC,QAAQ,CAACJ,IAAI,CAAC3C,KAAK,CAAC6C,OAAO,CAAC,CAAC,EAAE;QAC9C,KAAK,IAAIG,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAGzC,MAAM,CAACI,KAAK,CAACgB,MAAM,EAAEqB,KAAK,EAAE,EAAE;UACxD,IAAIzC,MAAM,CAACI,KAAK,CAACqC,KAAK,CAAC,KAAKL,IAAI,CAAC3C,KAAK,CAAC6C,OAAO,CAAC,EAAE;YAC/CC,SAAS,CAACG,IAAI,CAAC1C,MAAM,CAACI,KAAK,CAACqC,KAAK,CAAC,CAAC;UACrC;QACF;MACF,CAAC,MAAM;QACLF,SAAS,MAAAI,MAAA,CAAAC,kBAAA,CAAO5C,MAAM,CAACI,KAAK,IAAEgC,IAAI,CAAC3C,KAAK,CAAC6C,OAAO,CAAC,EAAC;MACpD;MACAtC,MAAM,CAACI,KAAK,GAAGmC,SAAS;MACxB5C,IAAI,CAAC,cAAc,EAAE4C,SAAS,CAAC;IACjC,CAAC;IACD,IAAMM,YAAY,GAAG,SAAfA,YAAYA,CAAIzC,KAAK,EAAK;MAC9BT,IAAI,CAAC,cAAc,EAAES,KAAK,CAAC;IAC7B,CAAC;IACD,IAAMiC,YAAY,GAAG,SAAfA,YAAYA,CAAIS,EAAE,EAAK;MAC3B,IAAI,OAAOrD,KAAK,CAAC4C,YAAY,KAAK,UAAU,EAAE;QAC5C,OAAO5C,KAAK,CAAC4C,YAAY,CAACS,EAAE,CAAC;MAC/B,CAAC,MAAM;QACL,OAAO,KAAK;MACd;IACF,CAAC;IACD,IAAMC,YAAY,GAAG,SAAfA,YAAYA,CAAIC,MAAM;MAAA,OAAM5D,mBAAmB,CAACgB,KAAK,GAAGjB,YAAY,CAAC6D,MAAM,CAAC,GAAGA,MAAM;IAAA,CAAC;IAC5FhE,KAAK,CACH;MAAA,OAAMS,KAAK,CAAC0B,IAAI;IAAA,GAChB,YAAM;MACJ;MACAJ,OAAO,CAAC,CAAC;MACT;MACAc,iBAAiB,CAACxB,aAAa,CAACO,eAAe,CAAC;IAClD,CAAC,EACD;MAAEqC,SAAS,EAAE,IAAI;MAAEC,IAAI,EAAE;IAAK,CAChC,CAAC;IACDhE,SAAS,CAAC,YAAM;MACdD,QAAQ,CAAC,YAAM;QACbO,GAAG,CAAC2D,QAAQ,CAACtD,WAAW,CAACO,KAAK,EAAE,UAACgD,CAAC,EAAK;UACrC/C,aAAa,CAACC,MAAM,GAAG8C,CAAC,CAACC,YAAY;UACrC;UACAtC,OAAO,CAAC,CAAC;UACT;UACAc,iBAAiB,CAACxB,aAAa,CAACO,eAAe,CAAC;QAClD,CAAC,CAAC;QACFpB,GAAG,CAAC2D,QAAQ,CAACpD,kBAAkB,CAACK,KAAK,EAAE,UAACgD,CAAC,EAAK;UAC5C/C,aAAa,CAACI,UAAU,GAAG2C,CAAC,CAACC,YAAY;UACzC;UACAtC,OAAO,CAAC,CAAC;UACT;UACAc,iBAAiB,CAACxB,aAAa,CAACO,eAAe,CAAC;QAClD,CAAC,CAAC;MACJ,CAAC,CAAC;IACJ,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
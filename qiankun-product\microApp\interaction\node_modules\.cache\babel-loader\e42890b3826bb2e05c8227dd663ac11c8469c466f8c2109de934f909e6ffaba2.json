{"ast": null, "code": "function _regeneratorRuntime() { \"use strict\"; /*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */ _regeneratorRuntime = function _regeneratorRuntime() { return e; }; var t, e = {}, r = Object.prototype, n = r.hasOwnProperty, o = Object.defineProperty || function (t, e, r) { t[e] = r.value; }, i = \"function\" == typeof Symbol ? Symbol : {}, a = i.iterator || \"@@iterator\", c = i.asyncIterator || \"@@asyncIterator\", u = i.toStringTag || \"@@toStringTag\"; function define(t, e, r) { return Object.defineProperty(t, e, { value: r, enumerable: !0, configurable: !0, writable: !0 }), t[e]; } try { define({}, \"\"); } catch (t) { define = function define(t, e, r) { return t[e] = r; }; } function wrap(t, e, r, n) { var i = e && e.prototype instanceof Generator ? e : Generator, a = Object.create(i.prototype), c = new Context(n || []); return o(a, \"_invoke\", { value: makeInvokeMethod(t, r, c) }), a; } function tryCatch(t, e, r) { try { return { type: \"normal\", arg: t.call(e, r) }; } catch (t) { return { type: \"throw\", arg: t }; } } e.wrap = wrap; var h = \"suspendedStart\", l = \"suspendedYield\", f = \"executing\", s = \"completed\", y = {}; function Generator() {} function GeneratorFunction() {} function GeneratorFunctionPrototype() {} var p = {}; define(p, a, function () { return this; }); var d = Object.getPrototypeOf, v = d && d(d(values([]))); v && v !== r && n.call(v, a) && (p = v); var g = GeneratorFunctionPrototype.prototype = Generator.prototype = Object.create(p); function defineIteratorMethods(t) { [\"next\", \"throw\", \"return\"].forEach(function (e) { define(t, e, function (t) { return this._invoke(e, t); }); }); } function AsyncIterator(t, e) { function invoke(r, o, i, a) { var c = tryCatch(t[r], t, o); if (\"throw\" !== c.type) { var u = c.arg, h = u.value; return h && \"object\" == typeof h && n.call(h, \"__await\") ? e.resolve(h.__await).then(function (t) { invoke(\"next\", t, i, a); }, function (t) { invoke(\"throw\", t, i, a); }) : e.resolve(h).then(function (t) { u.value = t, i(u); }, function (t) { return invoke(\"throw\", t, i, a); }); } a(c.arg); } var r; o(this, \"_invoke\", { value: function value(t, n) { function callInvokeWithMethodAndArg() { return new e(function (e, r) { invoke(t, n, e, r); }); } return r = r ? r.then(callInvokeWithMethodAndArg, callInvokeWithMethodAndArg) : callInvokeWithMethodAndArg(); } }); } function makeInvokeMethod(e, r, n) { var o = h; return function (i, a) { if (o === f) throw Error(\"Generator is already running\"); if (o === s) { if (\"throw\" === i) throw a; return { value: t, done: !0 }; } for (n.method = i, n.arg = a;;) { var c = n.delegate; if (c) { var u = maybeInvokeDelegate(c, n); if (u) { if (u === y) continue; return u; } } if (\"next\" === n.method) n.sent = n._sent = n.arg;else if (\"throw\" === n.method) { if (o === h) throw o = s, n.arg; n.dispatchException(n.arg); } else \"return\" === n.method && n.abrupt(\"return\", n.arg); o = f; var p = tryCatch(e, r, n); if (\"normal\" === p.type) { if (o = n.done ? s : l, p.arg === y) continue; return { value: p.arg, done: n.done }; } \"throw\" === p.type && (o = s, n.method = \"throw\", n.arg = p.arg); } }; } function maybeInvokeDelegate(e, r) { var n = r.method, o = e.iterator[n]; if (o === t) return r.delegate = null, \"throw\" === n && e.iterator.return && (r.method = \"return\", r.arg = t, maybeInvokeDelegate(e, r), \"throw\" === r.method) || \"return\" !== n && (r.method = \"throw\", r.arg = new TypeError(\"The iterator does not provide a '\" + n + \"' method\")), y; var i = tryCatch(o, e.iterator, r.arg); if (\"throw\" === i.type) return r.method = \"throw\", r.arg = i.arg, r.delegate = null, y; var a = i.arg; return a ? a.done ? (r[e.resultName] = a.value, r.next = e.nextLoc, \"return\" !== r.method && (r.method = \"next\", r.arg = t), r.delegate = null, y) : a : (r.method = \"throw\", r.arg = new TypeError(\"iterator result is not an object\"), r.delegate = null, y); } function pushTryEntry(t) { var e = { tryLoc: t[0] }; 1 in t && (e.catchLoc = t[1]), 2 in t && (e.finallyLoc = t[2], e.afterLoc = t[3]), this.tryEntries.push(e); } function resetTryEntry(t) { var e = t.completion || {}; e.type = \"normal\", delete e.arg, t.completion = e; } function Context(t) { this.tryEntries = [{ tryLoc: \"root\" }], t.forEach(pushTryEntry, this), this.reset(!0); } function values(e) { if (e || \"\" === e) { var r = e[a]; if (r) return r.call(e); if (\"function\" == typeof e.next) return e; if (!isNaN(e.length)) { var o = -1, i = function next() { for (; ++o < e.length;) if (n.call(e, o)) return next.value = e[o], next.done = !1, next; return next.value = t, next.done = !0, next; }; return i.next = i; } } throw new TypeError(typeof e + \" is not iterable\"); } return GeneratorFunction.prototype = GeneratorFunctionPrototype, o(g, \"constructor\", { value: GeneratorFunctionPrototype, configurable: !0 }), o(GeneratorFunctionPrototype, \"constructor\", { value: GeneratorFunction, configurable: !0 }), GeneratorFunction.displayName = define(GeneratorFunctionPrototype, u, \"GeneratorFunction\"), e.isGeneratorFunction = function (t) { var e = \"function\" == typeof t && t.constructor; return !!e && (e === GeneratorFunction || \"GeneratorFunction\" === (e.displayName || e.name)); }, e.mark = function (t) { return Object.setPrototypeOf ? Object.setPrototypeOf(t, GeneratorFunctionPrototype) : (t.__proto__ = GeneratorFunctionPrototype, define(t, u, \"GeneratorFunction\")), t.prototype = Object.create(g), t; }, e.awrap = function (t) { return { __await: t }; }, defineIteratorMethods(AsyncIterator.prototype), define(AsyncIterator.prototype, c, function () { return this; }), e.AsyncIterator = AsyncIterator, e.async = function (t, r, n, o, i) { void 0 === i && (i = Promise); var a = new AsyncIterator(wrap(t, r, n, o), i); return e.isGeneratorFunction(r) ? a : a.next().then(function (t) { return t.done ? t.value : a.next(); }); }, defineIteratorMethods(g), define(g, u, \"Generator\"), define(g, a, function () { return this; }), define(g, \"toString\", function () { return \"[object Generator]\"; }), e.keys = function (t) { var e = Object(t), r = []; for (var n in e) r.push(n); return r.reverse(), function next() { for (; r.length;) { var t = r.pop(); if (t in e) return next.value = t, next.done = !1, next; } return next.done = !0, next; }; }, e.values = values, Context.prototype = { constructor: Context, reset: function reset(e) { if (this.prev = 0, this.next = 0, this.sent = this._sent = t, this.done = !1, this.delegate = null, this.method = \"next\", this.arg = t, this.tryEntries.forEach(resetTryEntry), !e) for (var r in this) \"t\" === r.charAt(0) && n.call(this, r) && !isNaN(+r.slice(1)) && (this[r] = t); }, stop: function stop() { this.done = !0; var t = this.tryEntries[0].completion; if (\"throw\" === t.type) throw t.arg; return this.rval; }, dispatchException: function dispatchException(e) { if (this.done) throw e; var r = this; function handle(n, o) { return a.type = \"throw\", a.arg = e, r.next = n, o && (r.method = \"next\", r.arg = t), !!o; } for (var o = this.tryEntries.length - 1; o >= 0; --o) { var i = this.tryEntries[o], a = i.completion; if (\"root\" === i.tryLoc) return handle(\"end\"); if (i.tryLoc <= this.prev) { var c = n.call(i, \"catchLoc\"), u = n.call(i, \"finallyLoc\"); if (c && u) { if (this.prev < i.catchLoc) return handle(i.catchLoc, !0); if (this.prev < i.finallyLoc) return handle(i.finallyLoc); } else if (c) { if (this.prev < i.catchLoc) return handle(i.catchLoc, !0); } else { if (!u) throw Error(\"try statement without catch or finally\"); if (this.prev < i.finallyLoc) return handle(i.finallyLoc); } } } }, abrupt: function abrupt(t, e) { for (var r = this.tryEntries.length - 1; r >= 0; --r) { var o = this.tryEntries[r]; if (o.tryLoc <= this.prev && n.call(o, \"finallyLoc\") && this.prev < o.finallyLoc) { var i = o; break; } } i && (\"break\" === t || \"continue\" === t) && i.tryLoc <= e && e <= i.finallyLoc && (i = null); var a = i ? i.completion : {}; return a.type = t, a.arg = e, i ? (this.method = \"next\", this.next = i.finallyLoc, y) : this.complete(a); }, complete: function complete(t, e) { if (\"throw\" === t.type) throw t.arg; return \"break\" === t.type || \"continue\" === t.type ? this.next = t.arg : \"return\" === t.type ? (this.rval = this.arg = t.arg, this.method = \"return\", this.next = \"end\") : \"normal\" === t.type && e && (this.next = e), y; }, finish: function finish(t) { for (var e = this.tryEntries.length - 1; e >= 0; --e) { var r = this.tryEntries[e]; if (r.finallyLoc === t) return this.complete(r.completion, r.afterLoc), resetTryEntry(r), y; } }, catch: function _catch(t) { for (var e = this.tryEntries.length - 1; e >= 0; --e) { var r = this.tryEntries[e]; if (r.tryLoc === t) { var n = r.completion; if (\"throw\" === n.type) { var o = n.arg; resetTryEntry(r); } return o; } } throw Error(\"illegal catch attempt\"); }, delegateYield: function delegateYield(e, r, n) { return this.delegate = { iterator: values(e), resultName: r, nextLoc: n }, \"next\" === this.method && (this.arg = t), y; } }, e; }\nfunction asyncGeneratorStep(n, t, e, r, o, a, c) { try { var i = n[a](c), u = i.value; } catch (n) { return void e(n); } i.done ? t(u) : Promise.resolve(u).then(r, o); }\nfunction _asyncToGenerator(n) { return function () { var t = this, e = arguments; return new Promise(function (r, o) { var a = n.apply(t, e); function _next(n) { asyncGeneratorStep(a, r, o, _next, _throw, \"next\", n); } function _throw(n) { asyncGeneratorStep(a, r, o, _next, _throw, \"throw\", n); } _next(void 0); }); }; }\nimport { inject, getCurrentInstance, nextTick, computed, watch } from 'vue';\nimport '../../../form/index.mjs';\nimport '../../../../utils/index.mjs';\nimport { checkboxGroupContextKey } from '../constants.mjs';\nimport { useFormItem } from '../../../form/src/hooks/use-form-item.mjs';\nimport { debugWarn } from '../../../../utils/error.mjs';\nvar useCheckboxEvent = function useCheckboxEvent(props, _ref) {\n  var model = _ref.model,\n    isLimitExceeded = _ref.isLimitExceeded,\n    hasOwnLabel = _ref.hasOwnLabel,\n    isDisabled = _ref.isDisabled,\n    isLabeledByFormItem = _ref.isLabeledByFormItem;\n  var checkboxGroup = inject(checkboxGroupContextKey, void 0);\n  var _useFormItem = useFormItem(),\n    formItem = _useFormItem.formItem;\n  var _getCurrentInstance = getCurrentInstance(),\n    emit = _getCurrentInstance.emit;\n  function getLabeledValue(value) {\n    var _a, _b, _c, _d;\n    return [true, props.trueValue, props.trueLabel].includes(value) ? (_b = (_a = props.trueValue) != null ? _a : props.trueLabel) != null ? _b : true : (_d = (_c = props.falseValue) != null ? _c : props.falseLabel) != null ? _d : false;\n  }\n  function emitChangeEvent(checked, e) {\n    emit(\"change\", getLabeledValue(checked), e);\n  }\n  function handleChange(e) {\n    if (isLimitExceeded.value) return;\n    var target = e.target;\n    emit(\"change\", getLabeledValue(target.checked), e);\n  }\n  function onClickRoot(_x) {\n    return _onClickRoot.apply(this, arguments);\n  }\n  function _onClickRoot() {\n    _onClickRoot = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee(e) {\n      var eventTargets, hasLabel;\n      return _regeneratorRuntime().wrap(function _callee$(_context) {\n        while (1) switch (_context.prev = _context.next) {\n          case 0:\n            if (!isLimitExceeded.value) {\n              _context.next = 2;\n              break;\n            }\n            return _context.abrupt(\"return\");\n          case 2:\n            if (!(!hasOwnLabel.value && !isDisabled.value && isLabeledByFormItem.value)) {\n              _context.next = 10;\n              break;\n            }\n            eventTargets = e.composedPath();\n            hasLabel = eventTargets.some(function (item) {\n              return item.tagName === \"LABEL\";\n            });\n            if (hasLabel) {\n              _context.next = 10;\n              break;\n            }\n            model.value = getLabeledValue([false, props.falseValue, props.falseLabel].includes(model.value));\n            _context.next = 9;\n            return nextTick();\n          case 9:\n            emitChangeEvent(model.value, e);\n          case 10:\n          case \"end\":\n            return _context.stop();\n        }\n      }, _callee);\n    }));\n    return _onClickRoot.apply(this, arguments);\n  }\n  var validateEvent = computed(function () {\n    return (checkboxGroup == null ? void 0 : checkboxGroup.validateEvent) || props.validateEvent;\n  });\n  watch(function () {\n    return props.modelValue;\n  }, function () {\n    if (validateEvent.value) {\n      formItem == null ? void 0 : formItem.validate(\"change\").catch(function (err) {\n        return debugWarn(err);\n      });\n    }\n  });\n  return {\n    handleChange,\n    onClickRoot\n  };\n};\nexport { useCheckboxEvent };", "map": {"version": 3, "names": ["useCheckboxEvent", "props", "_ref", "model", "isLimitExceeded", "hasOwnLabel", "isDisabled", "isLabeledByFormItem", "checkboxGroup", "inject", "checkboxGroupContextKey", "_useFormItem", "useFormItem", "formItem", "_getCurrentInstance", "getCurrentInstance", "emit", "getLabeledValue", "value", "_a", "_b", "_c", "_d", "trueValue", "<PERSON><PERSON><PERSON><PERSON>", "includes", "falseValue", "<PERSON><PERSON><PERSON><PERSON>", "emitChangeEvent", "checked", "e", "handleChange", "target", "onClickRoot", "_x", "_onClickRoot", "apply", "arguments", "_asyncToGenerator", "_regeneratorRuntime", "mark", "_callee", "eventTargets", "<PERSON><PERSON><PERSON><PERSON>", "wrap", "_callee$", "_context", "prev", "next", "abrupt", "<PERSON><PERSON><PERSON>", "some", "item", "tagName", "nextTick", "stop", "validateEvent", "computed", "watch", "modelValue", "validate", "catch", "err", "debugWarn"], "sources": ["../../../../../../../packages/components/checkbox/src/composables/use-checkbox-event.ts"], "sourcesContent": ["import { computed, getCurrentInstance, inject, nextTick, watch } from 'vue'\nimport { useFormItem } from '@element-plus/components/form'\nimport { debugWarn } from '@element-plus/utils'\nimport { checkboxGroupContextKey } from '../constants'\n\nimport type { useFormItemInputId } from '@element-plus/components/form'\nimport type { CheckboxProps } from '../checkbox'\nimport type {\n  CheckboxDisabled,\n  CheckboxModel,\n  CheckboxStatus,\n} from '../composables'\n\nexport const useCheckboxEvent = (\n  props: CheckboxProps,\n  {\n    model,\n    isLimitExceeded,\n    hasOwnLabel,\n    isDisabled,\n    isLabeledByFormItem,\n  }: Pick<CheckboxModel, 'model' | 'isLimitExceeded'> &\n    Pick<CheckboxStatus, 'hasOwnLabel'> &\n    Pick<CheckboxDisabled, 'isDisabled'> &\n    Pick<ReturnType<typeof useFormItemInputId>, 'isLabeledByFormItem'>\n) => {\n  const checkboxGroup = inject(checkboxGroupContextKey, undefined)\n  const { formItem } = useFormItem()\n  const { emit } = getCurrentInstance()!\n\n  function getLabeledValue(value: string | number | boolean) {\n    return [true, props.trueValue, props.trueLabel].includes(value)\n      ? props.trueValue ?? props.trueLabel ?? true\n      : props.falseValue ?? props.falseLabel ?? false\n  }\n\n  function emitChangeEvent(\n    checked: string | number | boolean,\n    e: InputEvent | MouseEvent\n  ) {\n    emit('change', getLabeledValue(checked), e)\n  }\n\n  function handleChange(e: Event) {\n    if (isLimitExceeded.value) return\n\n    const target = e.target as HTMLInputElement\n    emit('change', getLabeledValue(target.checked), e)\n  }\n\n  async function onClickRoot(e: MouseEvent) {\n    if (isLimitExceeded.value) return\n\n    if (!hasOwnLabel.value && !isDisabled.value && isLabeledByFormItem.value) {\n      // fix: https://github.com/element-plus/element-plus/issues/9981\n      const eventTargets: EventTarget[] = e.composedPath()\n      const hasLabel = eventTargets.some(\n        (item) => (item as HTMLElement).tagName === 'LABEL'\n      )\n      if (!hasLabel) {\n        model.value = getLabeledValue(\n          [false, props.falseValue, props.falseLabel].includes(model.value)\n        )\n        await nextTick()\n        emitChangeEvent(model.value, e)\n      }\n    }\n  }\n\n  const validateEvent = computed(\n    () => checkboxGroup?.validateEvent || props.validateEvent\n  )\n\n  watch(\n    () => props.modelValue,\n    () => {\n      if (validateEvent.value) {\n        formItem?.validate('change').catch((err) => debugWarn(err))\n      }\n    }\n  )\n\n  return {\n    handleChange,\n    onClickRoot,\n  }\n}\n"], "mappings": ";;;;;;;;;AAIY,IAACA,gBAAgB,GAAG,SAAnBA,gBAAgBA,CAAIC,KAAK,EAAAC,IAAA,EAMhC;EAAA,IALJC,KAAK,GAAAD,IAAA,CAALC,KAAK;IACLC,eAAe,GAAAF,IAAA,CAAfE,eAAe;IACfC,WAAW,GAAAH,IAAA,CAAXG,WAAW;IACXC,UAAU,GAAAJ,IAAA,CAAVI,UAAU;IACVC,mBAAmB,GAAAL,IAAA,CAAnBK,mBAAmB;EAEnB,IAAMC,aAAa,GAAGC,MAAM,CAACC,uBAAuB,EAAE,KAAK,CAAC,CAAC;EAC7D,IAAAC,YAAA,GAAqBC,WAAW,EAAE;IAA1BC,QAAQ,GAAAF,YAAA,CAARE,QAAQ;EAChB,IAAAC,mBAAA,GAAiBC,kBAAkB,EAAE;IAA7BC,IAAI,GAAAF,mBAAA,CAAJE,IAAI;EACZ,SAASC,eAAeA,CAACC,KAAK,EAAE;IAC9B,IAAIC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE;IAClB,OAAO,CAAC,IAAI,EAAErB,KAAK,CAACsB,SAAS,EAAEtB,KAAK,CAACuB,SAAS,CAAC,CAACC,QAAQ,CAACP,KAAK,CAAC,GAAG,CAACE,EAAE,GAAG,CAACD,EAAE,GAAGlB,KAAK,CAACsB,SAAS,KAAK,IAAI,GAAGJ,EAAE,GAAGlB,KAAK,CAACuB,SAAS,KAAK,IAAI,GAAGJ,EAAE,GAAG,IAAI,GAAG,CAACE,EAAE,GAAG,CAACD,EAAE,GAAGpB,KAAK,CAACyB,UAAU,KAAK,IAAI,GAAGL,EAAE,GAAGpB,KAAK,CAAC0B,UAAU,KAAK,IAAI,GAAGL,EAAE,GAAG,KAAK;EAC5O;EACE,SAASM,eAAeA,CAACC,OAAO,EAAEC,CAAC,EAAE;IACnCd,IAAI,CAAC,QAAQ,EAAEC,eAAe,CAACY,OAAO,CAAC,EAAEC,CAAC,CAAC;EAC/C;EACE,SAASC,YAAYA,CAACD,CAAC,EAAE;IACvB,IAAI1B,eAAe,CAACc,KAAK,EACvB;IACF,IAAMc,MAAM,GAAGF,CAAC,CAACE,MAAM;IACvBhB,IAAI,CAAC,QAAQ,EAAEC,eAAe,CAACe,MAAM,CAACH,OAAO,CAAC,EAAEC,CAAC,CAAC;EACtD;EAAG,SACcG,WAAWA,CAAAC,EAAA;IAAA,OAAAC,YAAA,CAAAC,KAAA,OAAAC,SAAA;EAAA;EAAA,SAAAF,aAAA;IAAAA,YAAA,GAAAG,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,CAA1B,SAAAC,QAA2BX,CAAC;MAAA,IAAAY,YAAA,EAAAC,QAAA;MAAA,OAAAJ,mBAAA,GAAAK,IAAA,UAAAC,SAAAC,QAAA;QAAA,kBAAAA,QAAA,CAAAC,IAAA,GAAAD,QAAA,CAAAE,IAAA;UAAA;YAAA,KACtB5C,eAAe,CAACc,KAAK;cAAA4B,QAAA,CAAAE,IAAA;cAAA;YAAA;YAAA,OAAAF,QAAA,CAAAG,MAAA;UAAA;YAAA,MAErB,CAAC5C,WAAW,CAACa,KAAK,IAAI,CAACZ,UAAU,CAACY,KAAK,IAAIX,mBAAmB,CAACW,KAAK;cAAA4B,QAAA,CAAAE,IAAA;cAAA;YAAA;YAChEN,YAAY,GAAGZ,CAAC,CAACoB,YAAY,EAAE;YAC/BP,QAAQ,GAAGD,YAAY,CAACS,IAAI,CAAC,UAACC,IAAI;cAAA,OAAKA,IAAI,CAACC,OAAO,KAAK,OAAO;YAAA,EAAC;YAAA,IACjEV,QAAQ;cAAAG,QAAA,CAAAE,IAAA;cAAA;YAAA;YACX7C,KAAK,CAACe,KAAK,GAAGD,eAAe,CAAC,CAAC,KAAK,EAAEhB,KAAK,CAACyB,UAAU,EAAEzB,KAAK,CAAC0B,UAAU,CAAC,CAACF,QAAQ,CAACtB,KAAK,CAACe,KAAK,CAAC,CAAC;YAAC4B,QAAA,CAAAE,IAAA;YAAA,OAC3FM,QAAQ,EAAE;UAAA;YAChB1B,eAAe,CAACzB,KAAK,CAACe,KAAK,EAAEY,CAAC,CAAC;UAAC;UAAA;YAAA,OAAAgB,QAAA,CAAAS,IAAA;QAAA;MAAA,GAAAd,OAAA;IAAA,CAGrC;IAAA,OAAAN,YAAA,CAAAC,KAAA,OAAAC,SAAA;EAAA;EACD,IAAMmB,aAAa,GAAGC,QAAQ,CAAC;IAAA,OAAM,CAACjD,aAAa,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,aAAa,CAACgD,aAAa,KAAKvD,KAAK,CAACuD,aAAa;EAAA,EAAC;EAC3HE,KAAK,CAAC;IAAA,OAAMzD,KAAK,CAAC0D,UAAU;EAAA,GAAE,YAAM;IAClC,IAAIH,aAAa,CAACtC,KAAK,EAAE;MACvBL,QAAQ,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,QAAQ,CAAC+C,QAAQ,CAAC,QAAQ,CAAC,CAACC,KAAK,CAAC,UAACC,GAAG;QAAA,OAAKC,SAAS,CAACD,GAAG,CAAC;MAAA,EAAC;IAC5F;EACA,CAAG,CAAC;EACF,OAAO;IACL/B,YAAY;IACZE;EACJ,CAAG;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "function _createForOfIteratorHelper(r, e) { var t = \"undefined\" != typeof Symbol && r[Symbol.iterator] || r[\"@@iterator\"]; if (!t) { if (Array.isArray(r) || (t = _unsupportedIterableToArray(r)) || e && r && \"number\" == typeof r.length) { t && (r = t); var _n = 0, F = function F() {}; return { s: F, n: function n() { return _n >= r.length ? { done: !0 } : { done: !1, value: r[_n++] }; }, e: function e(r) { throw r; }, f: F }; } throw new TypeError(\"Invalid attempt to iterate non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); } var o, a = !0, u = !1; return { s: function s() { t = t.call(r); }, n: function n() { var r = t.next(); return a = r.done, r; }, e: function e(r) { u = !0, o = r; }, f: function f() { try { a || null == t.return || t.return(); } finally { if (u) throw o; } } }; }\nfunction _unsupportedIterableToArray(r, a) { if (r) { if (\"string\" == typeof r) return _arrayLikeToArray(r, a); var t = {}.toString.call(r).slice(8, -1); return \"Object\" === t && r.constructor && (t = r.constructor.name), \"Map\" === t || \"Set\" === t ? Array.from(r) : \"Arguments\" === t || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t) ? _arrayLikeToArray(r, a) : void 0; } }\nfunction _arrayLikeToArray(r, a) { (null == a || a > r.length) && (a = r.length); for (var e = 0, n = Array(a); e < a; e++) n[e] = r[e]; return n; }\nimport { ref, computed } from 'vue';\nimport { isFunction } from '@vue/shared';\nfunction useFilter(props, tree) {\n  var hiddenNodeKeySet = ref(/* @__PURE__ */new Set([]));\n  var hiddenExpandIconKeySet = ref(/* @__PURE__ */new Set([]));\n  var filterable = computed(function () {\n    return isFunction(props.filterMethod);\n  });\n  function doFilter(query) {\n    var _a;\n    if (!filterable.value) {\n      return;\n    }\n    var expandKeySet = /* @__PURE__ */new Set();\n    var hiddenExpandIconKeys = hiddenExpandIconKeySet.value;\n    var hiddenKeys = hiddenNodeKeySet.value;\n    var family = [];\n    var nodes = ((_a = tree.value) == null ? void 0 : _a.treeNodes) || [];\n    var filter = props.filterMethod;\n    hiddenKeys.clear();\n    function traverse(nodes2) {\n      nodes2.forEach(function (node) {\n        family.push(node);\n        if (filter == null ? void 0 : filter(query, node.data)) {\n          family.forEach(function (member) {\n            expandKeySet.add(member.key);\n          });\n        } else if (node.isLeaf) {\n          hiddenKeys.add(node.key);\n        }\n        var children = node.children;\n        if (children) {\n          traverse(children);\n        }\n        if (!node.isLeaf) {\n          if (!expandKeySet.has(node.key)) {\n            hiddenKeys.add(node.key);\n          } else if (children) {\n            var allHidden = true;\n            var _iterator = _createForOfIteratorHelper(children),\n              _step;\n            try {\n              for (_iterator.s(); !(_step = _iterator.n()).done;) {\n                var childNode = _step.value;\n                if (!hiddenKeys.has(childNode.key)) {\n                  allHidden = false;\n                  break;\n                }\n              }\n            } catch (err) {\n              _iterator.e(err);\n            } finally {\n              _iterator.f();\n            }\n            if (allHidden) {\n              hiddenExpandIconKeys.add(node.key);\n            } else {\n              hiddenExpandIconKeys.delete(node.key);\n            }\n          }\n        }\n        family.pop();\n      });\n    }\n    traverse(nodes);\n    return expandKeySet;\n  }\n  function isForceHiddenExpandIcon(node) {\n    return hiddenExpandIconKeySet.value.has(node.key);\n  }\n  return {\n    hiddenExpandIconKeySet,\n    hiddenNodeKeySet,\n    doFilter,\n    isForceHiddenExpandIcon\n  };\n}\nexport { useFilter };", "map": {"version": 3, "names": ["useFilter", "props", "tree", "hiddenNodeKeySet", "ref", "Set", "hiddenExpandIconKeySet", "filterable", "computed", "isFunction", "filterMethod", "<PERSON><PERSON><PERSON><PERSON>", "query", "_a", "value", "expandKeySet", "hiddenExpandIconKeys", "hiddenKeys", "family", "nodes", "treeNodes", "filter", "clear", "traverse", "nodes2", "for<PERSON>ach", "node", "push", "data", "member", "add", "key", "<PERSON><PERSON><PERSON><PERSON>", "children", "has", "allHidden", "_iterator", "_createForOfIteratorHelper", "_step", "s", "n", "done", "childNode", "err", "e", "f", "delete", "pop", "isForceHiddenExpandIcon"], "sources": ["../../../../../../../packages/components/tree-v2/src/composables/useFilter.ts"], "sourcesContent": ["import { computed, ref } from 'vue'\nimport { isFunction } from '@vue/shared'\nimport type { Ref } from 'vue'\nimport type { Tree, TreeKey, TreeNode, TreeProps } from '../types'\n\n// When the data volume is very large using filter will cause lag\n// I haven't found a better way to optimize it for now\n// Maybe this problem should be left to the server side\nexport function useFilter(props: TreeProps, tree: Ref<Tree | undefined>) {\n  const hiddenNodeKeySet = ref<Set<TreeKey>>(new Set([]))\n  const hiddenExpandIconKeySet = ref<Set<TreeKey>>(new Set([]))\n\n  const filterable = computed(() => {\n    return isFunction(props.filterMethod)\n  })\n\n  function doFilter(query: string) {\n    if (!filterable.value) {\n      return\n    }\n    const expandKeySet = new Set<TreeKey>()\n    const hiddenExpandIconKeys = hiddenExpandIconKeySet.value\n    const hiddenKeys = hiddenNodeKeySet.value\n    const family: TreeNode[] = []\n    const nodes = tree.value?.treeNodes || []\n    const filter = props.filterMethod\n    hiddenKeys.clear()\n    function traverse(nodes: TreeNode[]) {\n      nodes.forEach((node) => {\n        family.push(node)\n        if (filter?.(query, node.data)) {\n          family.forEach((member) => {\n            expandKeySet.add(member.key)\n          })\n        } else if (node.isLeaf) {\n          hiddenKeys.add(node.key)\n        }\n        const children = node.children\n        if (children) {\n          traverse(children)\n        }\n        if (!node.isLeaf) {\n          if (!expandKeySet.has(node.key)) {\n            hiddenKeys.add(node.key)\n          } else if (children) {\n            // If all child nodes are hidden, then the expand icon will be hidden\n            let allHidden = true\n            for (const childNode of children) {\n              if (!hiddenKeys.has(childNode.key)) {\n                allHidden = false\n                break\n              }\n            }\n            if (allHidden) {\n              hiddenExpandIconKeys.add(node.key)\n            } else {\n              hiddenExpandIconKeys.delete(node.key)\n            }\n          }\n        }\n        family.pop()\n      })\n    }\n    traverse(nodes)\n    return expandKeySet\n  }\n\n  function isForceHiddenExpandIcon(node: TreeNode): boolean {\n    return hiddenExpandIconKeySet.value.has(node.key)\n  }\n\n  return {\n    hiddenExpandIconKeySet,\n    hiddenNodeKeySet,\n    doFilter,\n    isForceHiddenExpandIcon,\n  }\n}\n"], "mappings": ";;;;;AAEO,SAASA,SAASA,CAACC,KAAK,EAAEC,IAAI,EAAE;EACrC,IAAMC,gBAAgB,GAAGC,GAAG,gBAAiB,IAAIC,GAAG,CAAC,EAAE,CAAC,CAAC;EACzD,IAAMC,sBAAsB,GAAGF,GAAG,gBAAiB,IAAIC,GAAG,CAAC,EAAE,CAAC,CAAC;EAC/D,IAAME,UAAU,GAAGC,QAAQ,CAAC,YAAM;IAChC,OAAOC,UAAU,CAACR,KAAK,CAACS,YAAY,CAAC;EACzC,CAAG,CAAC;EACF,SAASC,QAAQA,CAACC,KAAK,EAAE;IACvB,IAAIC,EAAE;IACN,IAAI,CAACN,UAAU,CAACO,KAAK,EAAE;MACrB;IACN;IACI,IAAMC,YAAY,kBAAmB,IAAIV,GAAG,EAAE;IAC9C,IAAMW,oBAAoB,GAAGV,sBAAsB,CAACQ,KAAK;IACzD,IAAMG,UAAU,GAAGd,gBAAgB,CAACW,KAAK;IACzC,IAAMI,MAAM,GAAG,EAAE;IACjB,IAAMC,KAAK,GAAG,CAAC,CAACN,EAAE,GAAGX,IAAI,CAACY,KAAK,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGD,EAAE,CAACO,SAAS,KAAK,EAAE;IACvE,IAAMC,MAAM,GAAGpB,KAAK,CAACS,YAAY;IACjCO,UAAU,CAACK,KAAK,EAAE;IAClB,SAASC,QAAQA,CAACC,MAAM,EAAE;MACxBA,MAAM,CAACC,OAAO,CAAC,UAACC,IAAI,EAAK;QACvBR,MAAM,CAACS,IAAI,CAACD,IAAI,CAAC;QACjB,IAAIL,MAAM,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,MAAM,CAACT,KAAK,EAAEc,IAAI,CAACE,IAAI,CAAC,EAAE;UACtDV,MAAM,CAACO,OAAO,CAAC,UAACI,MAAM,EAAK;YACzBd,YAAY,CAACe,GAAG,CAACD,MAAM,CAACE,GAAG,CAAC;UACxC,CAAW,CAAC;QACZ,CAAS,MAAM,IAAIL,IAAI,CAACM,MAAM,EAAE;UACtBf,UAAU,CAACa,GAAG,CAACJ,IAAI,CAACK,GAAG,CAAC;QAClC;QACQ,IAAME,QAAQ,GAAGP,IAAI,CAACO,QAAQ;QAC9B,IAAIA,QAAQ,EAAE;UACZV,QAAQ,CAACU,QAAQ,CAAC;QAC5B;QACQ,IAAI,CAACP,IAAI,CAACM,MAAM,EAAE;UAChB,IAAI,CAACjB,YAAY,CAACmB,GAAG,CAACR,IAAI,CAACK,GAAG,CAAC,EAAE;YAC/Bd,UAAU,CAACa,GAAG,CAACJ,IAAI,CAACK,GAAG,CAAC;UACpC,CAAW,MAAM,IAAIE,QAAQ,EAAE;YACnB,IAAIE,SAAS,GAAG,IAAI;YAAC,IAAAC,SAAA,GAAAC,0BAAA,CACGJ,QAAQ;cAAAK,KAAA;YAAA;cAAhC,KAAAF,SAAA,CAAAG,CAAA,MAAAD,KAAA,GAAAF,SAAA,CAAAI,CAAA,IAAAC,IAAA,GAAkC;gBAAA,IAAvBC,SAAS,GAAAJ,KAAA,CAAAxB,KAAA;gBAClB,IAAI,CAACG,UAAU,CAACiB,GAAG,CAACQ,SAAS,CAACX,GAAG,CAAC,EAAE;kBAClCI,SAAS,GAAG,KAAK;kBACjB;gBAChB;cACA;YAAa,SAAAQ,GAAA;cAAAP,SAAA,CAAAQ,CAAA,CAAAD,GAAA;YAAA;cAAAP,SAAA,CAAAS,CAAA;YAAA;YACD,IAAIV,SAAS,EAAE;cACbnB,oBAAoB,CAACc,GAAG,CAACJ,IAAI,CAACK,GAAG,CAAC;YAChD,CAAa,MAAM;cACLf,oBAAoB,CAAC8B,MAAM,CAACpB,IAAI,CAACK,GAAG,CAAC;YACnD;UACA;QACA;QACQb,MAAM,CAAC6B,GAAG,EAAE;MACpB,CAAO,CAAC;IACR;IACIxB,QAAQ,CAACJ,KAAK,CAAC;IACf,OAAOJ,YAAY;EACvB;EACE,SAASiC,uBAAuBA,CAACtB,IAAI,EAAE;IACrC,OAAOpB,sBAAsB,CAACQ,KAAK,CAACoB,GAAG,CAACR,IAAI,CAACK,GAAG,CAAC;EACrD;EACE,OAAO;IACLzB,sBAAsB;IACtBH,gBAAgB;IAChBQ,QAAQ;IACRqC;EACJ,CAAG;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
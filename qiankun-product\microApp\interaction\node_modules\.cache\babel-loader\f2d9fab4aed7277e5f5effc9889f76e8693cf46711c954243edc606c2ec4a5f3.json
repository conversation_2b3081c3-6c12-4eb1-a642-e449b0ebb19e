{"ast": null, "code": "\"use strict\";\n\nmodule.exports = function (options) {\n  var idGenerator = options.idGenerator;\n  var getState = options.stateHandler.getState;\n\n  /**\n   * Gets the resize detector id of the element.\n   * @public\n   * @param {element} element The target element to get the id of.\n   * @returns {string|number|null} The id of the element. Null if it has no id.\n   */\n  function getId(element) {\n    var state = getState(element);\n    if (state && state.id !== undefined) {\n      return state.id;\n    }\n    return null;\n  }\n\n  /**\n   * Sets the resize detector id of the element. Requires the element to have a resize detector state initialized.\n   * @public\n   * @param {element} element The target element to set the id of.\n   * @returns {string|number|null} The id of the element.\n   */\n  function setId(element) {\n    var state = getState(element);\n    if (!state) {\n      throw new Error(\"setId required the element to have a resize detection state.\");\n    }\n    var id = idGenerator.generate();\n    state.id = id;\n    return id;\n  }\n  return {\n    get: getId,\n    set: setId\n  };\n};", "map": {"version": 3, "names": ["module", "exports", "options", "idGenerator", "getState", "state<PERSON><PERSON><PERSON>", "getId", "element", "state", "id", "undefined", "setId", "Error", "generate", "get", "set"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/node_modules/.pnpm/element-resize-detector@1.2.4/node_modules/element-resize-detector/src/id-handler.js"], "sourcesContent": ["\"use strict\";\n\nmodule.exports = function(options) {\n    var idGenerator     = options.idGenerator;\n    var getState        = options.stateHandler.getState;\n\n    /**\n     * Gets the resize detector id of the element.\n     * @public\n     * @param {element} element The target element to get the id of.\n     * @returns {string|number|null} The id of the element. Null if it has no id.\n     */\n    function getId(element) {\n        var state = getState(element);\n\n        if (state && state.id !== undefined) {\n            return state.id;\n        }\n\n        return null;\n    }\n\n    /**\n     * Sets the resize detector id of the element. Requires the element to have a resize detector state initialized.\n     * @public\n     * @param {element} element The target element to set the id of.\n     * @returns {string|number|null} The id of the element.\n     */\n    function setId(element) {\n        var state = getState(element);\n\n        if (!state) {\n            throw new Error(\"setId required the element to have a resize detection state.\");\n        }\n\n        var id = idGenerator.generate();\n\n        state.id = id;\n\n        return id;\n    }\n\n    return {\n        get: getId,\n        set: setId\n    };\n};\n"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,OAAO,GAAG,UAASC,OAAO,EAAE;EAC/B,IAAIC,WAAW,GAAOD,OAAO,CAACC,WAAW;EACzC,IAAIC,QAAQ,GAAUF,OAAO,CAACG,YAAY,CAACD,QAAQ;;EAEnD;AACJ;AACA;AACA;AACA;AACA;EACI,SAASE,KAAKA,CAACC,OAAO,EAAE;IACpB,IAAIC,KAAK,GAAGJ,QAAQ,CAACG,OAAO,CAAC;IAE7B,IAAIC,KAAK,IAAIA,KAAK,CAACC,EAAE,KAAKC,SAAS,EAAE;MACjC,OAAOF,KAAK,CAACC,EAAE;IACnB;IAEA,OAAO,IAAI;EACf;;EAEA;AACJ;AACA;AACA;AACA;AACA;EACI,SAASE,KAAKA,CAACJ,OAAO,EAAE;IACpB,IAAIC,KAAK,GAAGJ,QAAQ,CAACG,OAAO,CAAC;IAE7B,IAAI,CAACC,KAAK,EAAE;MACR,MAAM,IAAII,KAAK,CAAC,8DAA8D,CAAC;IACnF;IAEA,IAAIH,EAAE,GAAGN,WAAW,CAACU,QAAQ,CAAC,CAAC;IAE/BL,KAAK,CAACC,EAAE,GAAGA,EAAE;IAEb,OAAOA,EAAE;EACb;EAEA,OAAO;IACHK,GAAG,EAAER,KAAK;IACVS,GAAG,EAAEJ;EACT,CAAC;AACL,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}
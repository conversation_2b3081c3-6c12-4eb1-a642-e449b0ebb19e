{"ast": null, "code": "function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nfunction _defineProperty(e, r, t) { return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, { value: t, enumerable: !0, configurable: !0, writable: !0 }) : e[r] = t, e; }\nfunction _toPropertyKey(t) { var i = _toPrimitive(t, \"string\"); return \"symbol\" == typeof i ? i : i + \"\"; }\nfunction _toPrimitive(t, r) { if (\"object\" != typeof t || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || \"default\"); if (\"object\" != typeof i) return i; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (\"string\" === r ? String : Number)(t); }\nimport { inject, computed, h } from 'vue';\nimport { merge } from 'lodash-unified';\nimport '../../../../hooks/index.mjs';\nimport { getRowIdentity } from '../util.mjs';\nimport { TABLE_INJECTION_KEY } from '../tokens.mjs';\nimport useEvents from './events-helper.mjs';\nimport useStyles from './styles-helper.mjs';\nimport { useNamespace } from '../../../../hooks/use-namespace/index.mjs';\nfunction useRender(props) {\n  var parent = inject(TABLE_INJECTION_KEY);\n  var ns = useNamespace(\"table\");\n  var _useEvents = useEvents(props),\n    handleDoubleClick = _useEvents.handleDoubleClick,\n    handleClick = _useEvents.handleClick,\n    handleContextMenu = _useEvents.handleContextMenu,\n    handleMouseEnter = _useEvents.handleMouseEnter,\n    handleMouseLeave = _useEvents.handleMouseLeave,\n    handleCellMouseEnter = _useEvents.handleCellMouseEnter,\n    handleCellMouseLeave = _useEvents.handleCellMouseLeave,\n    tooltipContent = _useEvents.tooltipContent,\n    tooltipTrigger = _useEvents.tooltipTrigger;\n  var _useStyles = useStyles(props),\n    getRowStyle = _useStyles.getRowStyle,\n    getRowClass = _useStyles.getRowClass,\n    getCellStyle = _useStyles.getCellStyle,\n    getCellClass = _useStyles.getCellClass,\n    getSpan = _useStyles.getSpan,\n    getColspanRealWidth = _useStyles.getColspanRealWidth;\n  var firstDefaultColumnIndex = computed(function () {\n    return props.store.states.columns.value.findIndex(function (_ref) {\n      var type = _ref.type;\n      return type === \"default\";\n    });\n  });\n  var getKeyOfRow = function getKeyOfRow(row, index) {\n    var rowKey = parent.props.rowKey;\n    if (rowKey) {\n      return getRowIdentity(row, rowKey);\n    }\n    return index;\n  };\n  var rowRender = function rowRender(row, $index, treeRowData) {\n    var expanded = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : false;\n    var tooltipEffect = props.tooltipEffect,\n      tooltipOptions = props.tooltipOptions,\n      store = props.store;\n    var _store$states = store.states,\n      indent = _store$states.indent,\n      columns = _store$states.columns;\n    var rowClasses = getRowClass(row, $index);\n    var display = true;\n    if (treeRowData) {\n      rowClasses.push(ns.em(\"row\", `level-${treeRowData.level}`));\n      display = treeRowData.display;\n    }\n    var displayStyle = display ? null : {\n      display: \"none\"\n    };\n    return h(\"tr\", {\n      style: [displayStyle, getRowStyle(row, $index)],\n      class: rowClasses,\n      key: getKeyOfRow(row, $index),\n      onDblclick: function onDblclick($event) {\n        return handleDoubleClick($event, row);\n      },\n      onClick: function onClick($event) {\n        return handleClick($event, row);\n      },\n      onContextmenu: function onContextmenu($event) {\n        return handleContextMenu($event, row);\n      },\n      onMouseenter: function onMouseenter() {\n        return handleMouseEnter($index);\n      },\n      onMouseleave: handleMouseLeave\n    }, columns.value.map(function (column, cellIndex) {\n      var _getSpan = getSpan(row, column, $index, cellIndex),\n        rowspan = _getSpan.rowspan,\n        colspan = _getSpan.colspan;\n      if (!rowspan || !colspan) {\n        return null;\n      }\n      var columnData = Object.assign({}, column);\n      columnData.realWidth = getColspanRealWidth(columns.value, colspan, cellIndex);\n      var data = {\n        store: props.store,\n        _self: props.context || parent,\n        column: columnData,\n        row,\n        $index,\n        cellIndex,\n        expanded\n      };\n      if (cellIndex === firstDefaultColumnIndex.value && treeRowData) {\n        data.treeNode = {\n          indent: treeRowData.level * indent.value,\n          level: treeRowData.level\n        };\n        if (typeof treeRowData.expanded === \"boolean\") {\n          data.treeNode.expanded = treeRowData.expanded;\n          if (\"loading\" in treeRowData) {\n            data.treeNode.loading = treeRowData.loading;\n          }\n          if (\"noLazyChildren\" in treeRowData) {\n            data.treeNode.noLazyChildren = treeRowData.noLazyChildren;\n          }\n        }\n      }\n      var baseKey = `${getKeyOfRow(row, $index)},${cellIndex}`;\n      var patchKey = columnData.columnKey || columnData.rawColumnKey || \"\";\n      var tdChildren = cellChildren(cellIndex, column, data);\n      var mergedTooltipOptions = column.showOverflowTooltip && merge({\n        effect: tooltipEffect\n      }, tooltipOptions, column.showOverflowTooltip);\n      return h(\"td\", {\n        style: getCellStyle($index, cellIndex, row, column),\n        class: getCellClass($index, cellIndex, row, column, colspan - 1),\n        key: `${patchKey}${baseKey}`,\n        rowspan,\n        colspan,\n        onMouseenter: function onMouseenter($event) {\n          return handleCellMouseEnter($event, row, mergedTooltipOptions);\n        },\n        onMouseleave: handleCellMouseLeave\n      }, [tdChildren]);\n    }));\n  };\n  var cellChildren = function cellChildren(cellIndex, column, data) {\n    return column.renderCell(data);\n  };\n  var wrappedRowRender = function wrappedRowRender(row, $index) {\n    var store = props.store;\n    var isRowExpanded = store.isRowExpanded,\n      assertRowKey = store.assertRowKey;\n    var _store$states2 = store.states,\n      treeData = _store$states2.treeData,\n      lazyTreeNodeMap = _store$states2.lazyTreeNodeMap,\n      childrenColumnName = _store$states2.childrenColumnName,\n      rowKey = _store$states2.rowKey;\n    var columns = store.states.columns.value;\n    var hasExpandColumn = columns.some(function (_ref2) {\n      var type = _ref2.type;\n      return type === \"expand\";\n    });\n    if (hasExpandColumn) {\n      var expanded = isRowExpanded(row);\n      var tr = rowRender(row, $index, void 0, expanded);\n      var renderExpanded = parent.renderExpanded;\n      if (expanded) {\n        if (!renderExpanded) {\n          console.error(\"[Element Error]renderExpanded is required.\");\n          return tr;\n        }\n        return [[tr, h(\"tr\", {\n          key: `expanded-row__${tr.key}`\n        }, [h(\"td\", {\n          colspan: columns.length,\n          class: `${ns.e(\"cell\")} ${ns.e(\"expanded-cell\")}`\n        }, [renderExpanded({\n          row,\n          $index,\n          store,\n          expanded\n        })])])]];\n      } else {\n        return [[tr]];\n      }\n    } else if (Object.keys(treeData.value).length) {\n      assertRowKey();\n      var key = getRowIdentity(row, rowKey.value);\n      var cur = treeData.value[key];\n      var treeRowData = null;\n      if (cur) {\n        treeRowData = {\n          expanded: cur.expanded,\n          level: cur.level,\n          display: true\n        };\n        if (typeof cur.lazy === \"boolean\") {\n          if (typeof cur.loaded === \"boolean\" && cur.loaded) {\n            treeRowData.noLazyChildren = !(cur.children && cur.children.length);\n          }\n          treeRowData.loading = cur.loading;\n        }\n      }\n      var tmp = [rowRender(row, $index, treeRowData)];\n      if (cur) {\n        var i = 0;\n        var _traverse = function traverse(children, parent2) {\n          if (!(children && children.length && parent2)) return;\n          children.forEach(function (node) {\n            var innerTreeRowData = {\n              display: parent2.display && parent2.expanded,\n              level: parent2.level + 1,\n              expanded: false,\n              noLazyChildren: false,\n              loading: false\n            };\n            var childKey = getRowIdentity(node, rowKey.value);\n            if (childKey === void 0 || childKey === null) {\n              throw new Error(\"For nested data item, row-key is required.\");\n            }\n            cur = _objectSpread({}, treeData.value[childKey]);\n            if (cur) {\n              innerTreeRowData.expanded = cur.expanded;\n              cur.level = cur.level || innerTreeRowData.level;\n              cur.display = !!(cur.expanded && innerTreeRowData.display);\n              if (typeof cur.lazy === \"boolean\") {\n                if (typeof cur.loaded === \"boolean\" && cur.loaded) {\n                  innerTreeRowData.noLazyChildren = !(cur.children && cur.children.length);\n                }\n                innerTreeRowData.loading = cur.loading;\n              }\n            }\n            i++;\n            tmp.push(rowRender(node, $index + i, innerTreeRowData));\n            if (cur) {\n              var nodes2 = lazyTreeNodeMap.value[childKey] || node[childrenColumnName.value];\n              _traverse(nodes2, cur);\n            }\n          });\n        };\n        cur.display = true;\n        var nodes = lazyTreeNodeMap.value[key] || row[childrenColumnName.value];\n        _traverse(nodes, cur);\n      }\n      return tmp;\n    } else {\n      return rowRender(row, $index, void 0);\n    }\n  };\n  return {\n    wrappedRowRender,\n    tooltipContent,\n    tooltipTrigger\n  };\n}\nexport { useRender as default };", "map": {"version": 3, "names": ["useRender", "props", "parent", "inject", "TABLE_INJECTION_KEY", "ns", "useNamespace", "_useEvents", "useEvents", "handleDoubleClick", "handleClick", "handleContextMenu", "handleMouseEnter", "handleMouseLeave", "handleCellMouseEnter", "handleCellMouseLeave", "tooltipContent", "tooltipTrigger", "_useStyles", "useStyles", "getRowStyle", "getRowClass", "getCellStyle", "getCellClass", "getSpan", "getColspanRealWidth", "firstDefaultColumnIndex", "computed", "store", "states", "columns", "value", "findIndex", "_ref", "type", "getKeyOfRow", "row", "index", "<PERSON><PERSON><PERSON>", "getRowIdentity", "rowRender", "$index", "treeRowData", "expanded", "arguments", "length", "undefined", "tooltipEffect", "tooltipOptions", "_store$states", "indent", "rowClasses", "display", "push", "em", "level", "displayStyle", "h", "style", "class", "key", "onDblclick", "$event", "onClick", "onContextmenu", "onMouseenter", "onMouseleave", "map", "column", "cellIndex", "_getSpan", "rowspan", "colspan", "columnData", "Object", "assign", "realWidth", "data", "_self", "context", "treeNode", "loading", "noLazyChildren", "baseKey", "<PERSON><PERSON><PERSON>", "column<PERSON>ey", "rawColumnKey", "td<PERSON><PERSON><PERSON><PERSON>", "cellChildren", "mergedTooltipOptions", "showOverflowTooltip", "merge", "effect", "renderCell", "wrappedRowRender", "isRowExpanded", "assertRowKey", "_store$states2", "treeData", "lazyTreeNodeMap", "childrenColumnName", "hasExpandColumn", "some", "_ref2", "tr", "renderExpanded", "console", "error", "e", "keys", "cur", "lazy", "loaded", "children", "tmp", "i", "traverse", "parent2", "for<PERSON>ach", "node", "innerTreeRowData", "<PERSON><PERSON><PERSON>", "Error", "_objectSpread", "nodes2", "nodes"], "sources": ["../../../../../../../packages/components/table/src/table-body/render-helper.ts"], "sourcesContent": ["// @ts-nocheck\nimport { computed, h, inject } from 'vue'\nimport { merge } from 'lodash-unified'\nimport { useNamespace } from '@element-plus/hooks'\nimport { getRowIdentity } from '../util'\nimport { TABLE_INJECTION_KEY } from '../tokens'\nimport useEvents from './events-helper'\nimport useStyles from './styles-helper'\nimport type { TableBodyProps } from './defaults'\nimport type { RenderRowData, TableProps, TreeNode } from '../table/defaults'\n\nfunction useRender<T>(props: Partial<TableBodyProps<T>>) {\n  const parent = inject(TABLE_INJECTION_KEY)\n  const ns = useNamespace('table')\n  const {\n    handleDoubleClick,\n    handleClick,\n    handleContextMenu,\n    handleMouseEnter,\n    handleMouseLeave,\n    handleCellMouseEnter,\n    handleCellMouseLeave,\n    tooltipContent,\n    tooltipTrigger,\n  } = useEvents(props)\n  const {\n    getRowStyle,\n    getRowClass,\n    getCellStyle,\n    getCellClass,\n    getSpan,\n    getColspanRealWidth,\n  } = useStyles(props)\n  const firstDefaultColumnIndex = computed(() => {\n    return props.store.states.columns.value.findIndex(\n      ({ type }) => type === 'default'\n    )\n  })\n  const getKeyOfRow = (row: T, index: number) => {\n    const rowKey = (parent.props as Partial<TableProps<T>>).rowKey\n    if (rowKey) {\n      return getRowIdentity(row, rowKey)\n    }\n    return index\n  }\n  const rowRender = (\n    row: T,\n    $index: number,\n    treeRowData?: TreeNode,\n    expanded = false\n  ) => {\n    const { tooltipEffect, tooltipOptions, store } = props\n    const { indent, columns } = store.states\n    const rowClasses = getRowClass(row, $index)\n    let display = true\n    if (treeRowData) {\n      rowClasses.push(ns.em('row', `level-${treeRowData.level}`))\n      display = treeRowData.display\n    }\n    const displayStyle = display\n      ? null\n      : {\n          display: 'none',\n        }\n    return h(\n      'tr',\n      {\n        style: [displayStyle, getRowStyle(row, $index)],\n        class: rowClasses,\n        key: getKeyOfRow(row, $index),\n        onDblclick: ($event) => handleDoubleClick($event, row),\n        onClick: ($event) => handleClick($event, row),\n        onContextmenu: ($event) => handleContextMenu($event, row),\n        onMouseenter: () => handleMouseEnter($index),\n        onMouseleave: handleMouseLeave,\n      },\n      columns.value.map((column, cellIndex) => {\n        const { rowspan, colspan } = getSpan(row, column, $index, cellIndex)\n        if (!rowspan || !colspan) {\n          return null\n        }\n        const columnData = Object.assign({}, column)\n        columnData.realWidth = getColspanRealWidth(\n          columns.value,\n          colspan,\n          cellIndex\n        )\n        const data: RenderRowData<T> = {\n          store: props.store,\n          _self: props.context || parent,\n          column: columnData,\n          row,\n          $index,\n          cellIndex,\n          expanded,\n        }\n        if (cellIndex === firstDefaultColumnIndex.value && treeRowData) {\n          data.treeNode = {\n            indent: treeRowData.level * indent.value,\n            level: treeRowData.level,\n          }\n          if (typeof treeRowData.expanded === 'boolean') {\n            data.treeNode.expanded = treeRowData.expanded\n            // 表明是懒加载\n            if ('loading' in treeRowData) {\n              data.treeNode.loading = treeRowData.loading\n            }\n            if ('noLazyChildren' in treeRowData) {\n              data.treeNode.noLazyChildren = treeRowData.noLazyChildren\n            }\n          }\n        }\n        const baseKey = `${getKeyOfRow(row, $index)},${cellIndex}`\n        const patchKey = columnData.columnKey || columnData.rawColumnKey || ''\n        const tdChildren = cellChildren(cellIndex, column, data)\n        const mergedTooltipOptions =\n          column.showOverflowTooltip &&\n          merge(\n            {\n              effect: tooltipEffect,\n            },\n            tooltipOptions,\n            column.showOverflowTooltip\n          )\n        return h(\n          'td',\n          {\n            style: getCellStyle($index, cellIndex, row, column),\n            class: getCellClass($index, cellIndex, row, column, colspan - 1),\n            key: `${patchKey}${baseKey}`,\n            rowspan,\n            colspan,\n            onMouseenter: ($event) =>\n              handleCellMouseEnter($event, row, mergedTooltipOptions),\n            onMouseleave: handleCellMouseLeave,\n          },\n          [tdChildren]\n        )\n      })\n    )\n  }\n  const cellChildren = (cellIndex, column, data) => {\n    return column.renderCell(data)\n  }\n\n  const wrappedRowRender = (row: T, $index: number) => {\n    const store = props.store\n    const { isRowExpanded, assertRowKey } = store\n    const { treeData, lazyTreeNodeMap, childrenColumnName, rowKey } =\n      store.states\n    const columns = store.states.columns.value\n    const hasExpandColumn = columns.some(({ type }) => type === 'expand')\n    if (hasExpandColumn) {\n      const expanded = isRowExpanded(row)\n      const tr = rowRender(row, $index, undefined, expanded)\n      const renderExpanded = parent.renderExpanded\n      if (expanded) {\n        if (!renderExpanded) {\n          console.error('[Element Error]renderExpanded is required.')\n          return tr\n        }\n        // 使用二维数组，避免修改 $index\n        // Use a matrix to avoid modifying $index\n        return [\n          [\n            tr,\n            h(\n              'tr',\n              {\n                key: `expanded-row__${tr.key as string}`,\n              },\n              [\n                h(\n                  'td',\n                  {\n                    colspan: columns.length,\n                    class: `${ns.e('cell')} ${ns.e('expanded-cell')}`,\n                  },\n                  [renderExpanded({ row, $index, store, expanded })]\n                ),\n              ]\n            ),\n          ],\n        ]\n      } else {\n        // 使用二维数组，避免修改 $index\n        // Use a two dimensional array avoid modifying $index\n        return [[tr]]\n      }\n    } else if (Object.keys(treeData.value).length) {\n      assertRowKey()\n      // TreeTable 时，rowKey 必须由用户设定，不使用 getKeyOfRow 计算\n      // 在调用 rowRender 函数时，仍然会计算 rowKey，不太好的操作\n      const key = getRowIdentity(row, rowKey.value)\n      let cur = treeData.value[key]\n      let treeRowData = null\n      if (cur) {\n        treeRowData = {\n          expanded: cur.expanded,\n          level: cur.level,\n          display: true,\n        }\n        if (typeof cur.lazy === 'boolean') {\n          if (typeof cur.loaded === 'boolean' && cur.loaded) {\n            treeRowData.noLazyChildren = !(cur.children && cur.children.length)\n          }\n          treeRowData.loading = cur.loading\n        }\n      }\n      const tmp = [rowRender(row, $index, treeRowData)]\n      // 渲染嵌套数据\n      if (cur) {\n        // currentRow 记录的是 index，所以还需主动增加 TreeTable 的 index\n        let i = 0\n        const traverse = (children, parent) => {\n          if (!(children && children.length && parent)) return\n          children.forEach((node) => {\n            // 父节点的 display 状态影响子节点的显示状态\n            const innerTreeRowData = {\n              display: parent.display && parent.expanded,\n              level: parent.level + 1,\n              expanded: false,\n              noLazyChildren: false,\n              loading: false,\n            }\n            const childKey = getRowIdentity(node, rowKey.value)\n            if (childKey === undefined || childKey === null) {\n              throw new Error('For nested data item, row-key is required.')\n            }\n            cur = { ...treeData.value[childKey] }\n            // 对于当前节点，分成有无子节点两种情况。\n            // 如果包含子节点的，设置 expanded 属性。\n            // 对于它子节点的 display 属性由它本身的 expanded 与 display 共同决定。\n            if (cur) {\n              innerTreeRowData.expanded = cur.expanded\n              // 懒加载的某些节点，level 未知\n              cur.level = cur.level || innerTreeRowData.level\n              cur.display = !!(cur.expanded && innerTreeRowData.display)\n              if (typeof cur.lazy === 'boolean') {\n                if (typeof cur.loaded === 'boolean' && cur.loaded) {\n                  innerTreeRowData.noLazyChildren = !(\n                    cur.children && cur.children.length\n                  )\n                }\n                innerTreeRowData.loading = cur.loading\n              }\n            }\n            i++\n            tmp.push(rowRender(node, $index + i, innerTreeRowData))\n            if (cur) {\n              const nodes =\n                lazyTreeNodeMap.value[childKey] ||\n                node[childrenColumnName.value]\n              traverse(nodes, cur)\n            }\n          })\n        }\n        // 对于 root 节点，display 一定为 true\n        cur.display = true\n        const nodes =\n          lazyTreeNodeMap.value[key] || row[childrenColumnName.value]\n        traverse(nodes, cur)\n      }\n      return tmp\n    } else {\n      return rowRender(row, $index, undefined)\n    }\n  }\n\n  return {\n    wrappedRowRender,\n    tooltipContent,\n    tooltipTrigger,\n  }\n}\n\nexport default useRender\n"], "mappings": ";;;;;;;;;;;;;AAOA,SAASA,SAASA,CAACC,KAAK,EAAE;EACxB,IAAMC,MAAM,GAAGC,MAAM,CAACC,mBAAmB,CAAC;EAC1C,IAAMC,EAAE,GAAGC,YAAY,CAAC,OAAO,CAAC;EAChC,IAAAC,UAAA,GAUIC,SAAS,CAACP,KAAK,CAAC;IATlBQ,iBAAiB,GAAAF,UAAA,CAAjBE,iBAAiB;IACjBC,WAAW,GAAAH,UAAA,CAAXG,WAAW;IACXC,iBAAiB,GAAAJ,UAAA,CAAjBI,iBAAiB;IACjBC,gBAAgB,GAAAL,UAAA,CAAhBK,gBAAgB;IAChBC,gBAAgB,GAAAN,UAAA,CAAhBM,gBAAgB;IAChBC,oBAAoB,GAAAP,UAAA,CAApBO,oBAAoB;IACpBC,oBAAoB,GAAAR,UAAA,CAApBQ,oBAAoB;IACpBC,cAAc,GAAAT,UAAA,CAAdS,cAAc;IACdC,cAAc,GAAAV,UAAA,CAAdU,cAAc;EAEhB,IAAAC,UAAA,GAOIC,SAAS,CAAClB,KAAK,CAAC;IANlBmB,WAAW,GAAAF,UAAA,CAAXE,WAAW;IACXC,WAAW,GAAAH,UAAA,CAAXG,WAAW;IACXC,YAAY,GAAAJ,UAAA,CAAZI,YAAY;IACZC,YAAY,GAAAL,UAAA,CAAZK,YAAY;IACZC,OAAO,GAAAN,UAAA,CAAPM,OAAO;IACPC,mBAAmB,GAAAP,UAAA,CAAnBO,mBAAmB;EAErB,IAAMC,uBAAuB,GAAGC,QAAQ,CAAC,YAAM;IAC7C,OAAO1B,KAAK,CAAC2B,KAAK,CAACC,MAAM,CAACC,OAAO,CAACC,KAAK,CAACC,SAAS,CAAC,UAAAC,IAAA;MAAA,IAAGC,IAAI,GAAAD,IAAA,CAAJC,IAAI;MAAA,OAAOA,IAAI,KAAK,SAAS;IAAA,EAAC;EACvF,CAAG,CAAC;EACF,IAAMC,WAAW,GAAG,SAAdA,WAAWA,CAAIC,GAAG,EAAEC,KAAK,EAAK;IAClC,IAAMC,MAAM,GAAGpC,MAAM,CAACD,KAAK,CAACqC,MAAM;IAClC,IAAIA,MAAM,EAAE;MACV,OAAOC,cAAc,CAACH,GAAG,EAAEE,MAAM,CAAC;IACxC;IACI,OAAOD,KAAK;EAChB,CAAG;EACD,IAAMG,SAAS,GAAG,SAAZA,SAASA,CAAIJ,GAAG,EAAEK,MAAM,EAAEC,WAAW,EAAuB;IAAA,IAArBC,QAAQ,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,KAAK;IAC3D,IAAQG,aAAa,GAA4B9C,KAAK,CAA9C8C,aAAa;MAAEC,cAAc,GAAY/C,KAAK,CAA/B+C,cAAc;MAAEpB,KAAK,GAAK3B,KAAK,CAAf2B,KAAK;IAC5C,IAAAqB,aAAA,GAA4BrB,KAAK,CAACC,MAAM;MAAhCqB,MAAM,GAAAD,aAAA,CAANC,MAAM;MAAEpB,OAAO,GAAAmB,aAAA,CAAPnB,OAAO;IACvB,IAAMqB,UAAU,GAAG9B,WAAW,CAACe,GAAG,EAAEK,MAAM,CAAC;IAC3C,IAAIW,OAAO,GAAG,IAAI;IAClB,IAAIV,WAAW,EAAE;MACfS,UAAU,CAACE,IAAI,CAAChD,EAAE,CAACiD,EAAE,CAAC,KAAK,EAAE,SAASZ,WAAW,CAACa,KAAK,EAAE,CAAC,CAAC;MAC3DH,OAAO,GAAGV,WAAW,CAACU,OAAO;IACnC;IACI,IAAMI,YAAY,GAAGJ,OAAO,GAAG,IAAI,GAAG;MACpCA,OAAO,EAAE;IACf,CAAK;IACD,OAAOK,CAAC,CAAC,IAAI,EAAE;MACbC,KAAK,EAAE,CAACF,YAAY,EAAEpC,WAAW,CAACgB,GAAG,EAAEK,MAAM,CAAC,CAAC;MAC/CkB,KAAK,EAAER,UAAU;MACjBS,GAAG,EAAEzB,WAAW,CAACC,GAAG,EAAEK,MAAM,CAAC;MAC7BoB,UAAU,EAAE,SAAZA,UAAUA,CAAGC,MAAM;QAAA,OAAKrD,iBAAiB,CAACqD,MAAM,EAAE1B,GAAG,CAAC;MAAA;MACtD2B,OAAO,EAAE,SAATA,OAAOA,CAAGD,MAAM;QAAA,OAAKpD,WAAW,CAACoD,MAAM,EAAE1B,GAAG,CAAC;MAAA;MAC7C4B,aAAa,EAAE,SAAfA,aAAaA,CAAGF,MAAM;QAAA,OAAKnD,iBAAiB,CAACmD,MAAM,EAAE1B,GAAG,CAAC;MAAA;MACzD6B,YAAY,EAAE,SAAdA,YAAYA,CAAA;QAAA,OAAQrD,gBAAgB,CAAC6B,MAAM,CAAC;MAAA;MAC5CyB,YAAY,EAAErD;IACpB,CAAK,EAAEiB,OAAO,CAACC,KAAK,CAACoC,GAAG,CAAC,UAACC,MAAM,EAAEC,SAAS,EAAK;MAC1C,IAAAC,QAAA,GAA6B9C,OAAO,CAACY,GAAG,EAAEgC,MAAM,EAAE3B,MAAM,EAAE4B,SAAS,CAAC;QAA5DE,OAAO,GAAAD,QAAA,CAAPC,OAAO;QAAEC,OAAO,GAAAF,QAAA,CAAPE,OAAO;MACxB,IAAI,CAACD,OAAO,IAAI,CAACC,OAAO,EAAE;QACxB,OAAO,IAAI;MACnB;MACM,IAAMC,UAAU,GAAGC,MAAM,CAACC,MAAM,CAAC,EAAE,EAAEP,MAAM,CAAC;MAC5CK,UAAU,CAACG,SAAS,GAAGnD,mBAAmB,CAACK,OAAO,CAACC,KAAK,EAAEyC,OAAO,EAAEH,SAAS,CAAC;MAC7E,IAAMQ,IAAI,GAAG;QACXjD,KAAK,EAAE3B,KAAK,CAAC2B,KAAK;QAClBkD,KAAK,EAAE7E,KAAK,CAAC8E,OAAO,IAAI7E,MAAM;QAC9BkE,MAAM,EAAEK,UAAU;QAClBrC,GAAG;QACHK,MAAM;QACN4B,SAAS;QACT1B;MACR,CAAO;MACD,IAAI0B,SAAS,KAAK3C,uBAAuB,CAACK,KAAK,IAAIW,WAAW,EAAE;QAC9DmC,IAAI,CAACG,QAAQ,GAAG;UACd9B,MAAM,EAAER,WAAW,CAACa,KAAK,GAAGL,MAAM,CAACnB,KAAK;UACxCwB,KAAK,EAAEb,WAAW,CAACa;QAC7B,CAAS;QACD,IAAI,OAAOb,WAAW,CAACC,QAAQ,KAAK,SAAS,EAAE;UAC7CkC,IAAI,CAACG,QAAQ,CAACrC,QAAQ,GAAGD,WAAW,CAACC,QAAQ;UAC7C,IAAI,SAAS,IAAID,WAAW,EAAE;YAC5BmC,IAAI,CAACG,QAAQ,CAACC,OAAO,GAAGvC,WAAW,CAACuC,OAAO;UACvD;UACU,IAAI,gBAAgB,IAAIvC,WAAW,EAAE;YACnCmC,IAAI,CAACG,QAAQ,CAACE,cAAc,GAAGxC,WAAW,CAACwC,cAAc;UACrE;QACA;MACA;MACM,IAAMC,OAAO,GAAG,GAAGhD,WAAW,CAACC,GAAG,EAAEK,MAAM,CAAC,IAAI4B,SAAS,EAAE;MAC1D,IAAMe,QAAQ,GAAGX,UAAU,CAACY,SAAS,IAAIZ,UAAU,CAACa,YAAY,IAAI,EAAE;MACtE,IAAMC,UAAU,GAAGC,YAAY,CAACnB,SAAS,EAAED,MAAM,EAAES,IAAI,CAAC;MACxD,IAAMY,oBAAoB,GAAGrB,MAAM,CAACsB,mBAAmB,IAAIC,KAAK,CAAC;QAC/DC,MAAM,EAAE7C;MAChB,CAAO,EAAEC,cAAc,EAAEoB,MAAM,CAACsB,mBAAmB,CAAC;MAC9C,OAAOjC,CAAC,CAAC,IAAI,EAAE;QACbC,KAAK,EAAEpC,YAAY,CAACmB,MAAM,EAAE4B,SAAS,EAAEjC,GAAG,EAAEgC,MAAM,CAAC;QACnDT,KAAK,EAAEpC,YAAY,CAACkB,MAAM,EAAE4B,SAAS,EAAEjC,GAAG,EAAEgC,MAAM,EAAEI,OAAO,GAAG,CAAC,CAAC;QAChEZ,GAAG,EAAE,GAAGwB,QAAQ,GAAGD,OAAO,EAAE;QAC5BZ,OAAO;QACPC,OAAO;QACPP,YAAY,EAAE,SAAdA,YAAYA,CAAGH,MAAM;UAAA,OAAKhD,oBAAoB,CAACgD,MAAM,EAAE1B,GAAG,EAAEqD,oBAAoB,CAAC;QAAA;QACjFvB,YAAY,EAAEnD;MACtB,CAAO,EAAE,CAACwE,UAAU,CAAC,CAAC;IACtB,CAAK,CAAC,CAAC;EACP,CAAG;EACD,IAAMC,YAAY,GAAG,SAAfA,YAAYA,CAAInB,SAAS,EAAED,MAAM,EAAES,IAAI,EAAK;IAChD,OAAOT,MAAM,CAACyB,UAAU,CAAChB,IAAI,CAAC;EAClC,CAAG;EACD,IAAMiB,gBAAgB,GAAG,SAAnBA,gBAAgBA,CAAI1D,GAAG,EAAEK,MAAM,EAAK;IACxC,IAAMb,KAAK,GAAG3B,KAAK,CAAC2B,KAAK;IACzB,IAAQmE,aAAa,GAAmBnE,KAAK,CAArCmE,aAAa;MAAEC,YAAY,GAAKpE,KAAK,CAAtBoE,YAAY;IACnC,IAAAC,cAAA,GAAkErE,KAAK,CAACC,MAAM;MAAtEqE,QAAQ,GAAAD,cAAA,CAARC,QAAQ;MAAEC,eAAe,GAAAF,cAAA,CAAfE,eAAe;MAAEC,kBAAkB,GAAAH,cAAA,CAAlBG,kBAAkB;MAAE9D,MAAM,GAAA2D,cAAA,CAAN3D,MAAM;IAC7D,IAAMR,OAAO,GAAGF,KAAK,CAACC,MAAM,CAACC,OAAO,CAACC,KAAK;IAC1C,IAAMsE,eAAe,GAAGvE,OAAO,CAACwE,IAAI,CAAC,UAAAC,KAAA;MAAA,IAAGrE,IAAI,GAAAqE,KAAA,CAAJrE,IAAI;MAAA,OAAOA,IAAI,KAAK,QAAQ;IAAA,EAAC;IACrE,IAAImE,eAAe,EAAE;MACnB,IAAM1D,QAAQ,GAAGoD,aAAa,CAAC3D,GAAG,CAAC;MACnC,IAAMoE,EAAE,GAAGhE,SAAS,CAACJ,GAAG,EAAEK,MAAM,EAAE,KAAK,CAAC,EAAEE,QAAQ,CAAC;MACnD,IAAM8D,cAAc,GAAGvG,MAAM,CAACuG,cAAc;MAC5C,IAAI9D,QAAQ,EAAE;QACZ,IAAI,CAAC8D,cAAc,EAAE;UACnBC,OAAO,CAACC,KAAK,CAAC,4CAA4C,CAAC;UAC3D,OAAOH,EAAE;QACnB;QACQ,OAAO,CACL,CACEA,EAAE,EACF/C,CAAC,CAAC,IAAI,EAAE;UACNG,GAAG,EAAE,iBAAiB4C,EAAE,CAAC5C,GAAG;QAC1C,CAAa,EAAE,CACDH,CAAC,CAAC,IAAI,EAAE;UACNe,OAAO,EAAE1C,OAAO,CAACe,MAAM;UACvBc,KAAK,EAAE,GAAGtD,EAAE,CAACuG,CAAC,CAAC,MAAM,CAAC,IAAIvG,EAAE,CAACuG,CAAC,CAAC,eAAe,CAAC;QAC/D,CAAe,EAAE,CAACH,cAAc,CAAC;UAAErE,GAAG;UAAEK,MAAM;UAAEb,KAAK;UAAEe;QAAQ,CAAE,CAAC,CAAC,CAAC,CACvD,CAAC,CACH,CACF;MACT,CAAO,MAAM;QACL,OAAO,CAAC,CAAC6D,EAAE,CAAC,CAAC;MACrB;IACA,CAAK,MAAM,IAAI9B,MAAM,CAACmC,IAAI,CAACX,QAAQ,CAACnE,KAAK,CAAC,CAACc,MAAM,EAAE;MAC7CmD,YAAY,EAAE;MACd,IAAMpC,GAAG,GAAGrB,cAAc,CAACH,GAAG,EAAEE,MAAM,CAACP,KAAK,CAAC;MAC7C,IAAI+E,GAAG,GAAGZ,QAAQ,CAACnE,KAAK,CAAC6B,GAAG,CAAC;MAC7B,IAAIlB,WAAW,GAAG,IAAI;MACtB,IAAIoE,GAAG,EAAE;QACPpE,WAAW,GAAG;UACZC,QAAQ,EAAEmE,GAAG,CAACnE,QAAQ;UACtBY,KAAK,EAAEuD,GAAG,CAACvD,KAAK;UAChBH,OAAO,EAAE;QACnB,CAAS;QACD,IAAI,OAAO0D,GAAG,CAACC,IAAI,KAAK,SAAS,EAAE;UACjC,IAAI,OAAOD,GAAG,CAACE,MAAM,KAAK,SAAS,IAAIF,GAAG,CAACE,MAAM,EAAE;YACjDtE,WAAW,CAACwC,cAAc,GAAG,EAAE4B,GAAG,CAACG,QAAQ,IAAIH,GAAG,CAACG,QAAQ,CAACpE,MAAM,CAAC;UAC/E;UACUH,WAAW,CAACuC,OAAO,GAAG6B,GAAG,CAAC7B,OAAO;QAC3C;MACA;MACM,IAAMiC,GAAG,GAAG,CAAC1E,SAAS,CAACJ,GAAG,EAAEK,MAAM,EAAEC,WAAW,CAAC,CAAC;MACjD,IAAIoE,GAAG,EAAE;QACP,IAAIK,CAAC,GAAG,CAAC;QACT,IAAMC,SAAQ,GAAG,SAAXA,QAAQA,CAAIH,QAAQ,EAAEI,OAAO,EAAK;UACtC,IAAI,EAAEJ,QAAQ,IAAIA,QAAQ,CAACpE,MAAM,IAAIwE,OAAO,CAAC,EAC3C;UACFJ,QAAQ,CAACK,OAAO,CAAC,UAACC,IAAI,EAAK;YACzB,IAAMC,gBAAgB,GAAG;cACvBpE,OAAO,EAAEiE,OAAO,CAACjE,OAAO,IAAIiE,OAAO,CAAC1E,QAAQ;cAC5CY,KAAK,EAAE8D,OAAO,CAAC9D,KAAK,GAAG,CAAC;cACxBZ,QAAQ,EAAE,KAAK;cACfuC,cAAc,EAAE,KAAK;cACrBD,OAAO,EAAE;YACvB,CAAa;YACD,IAAMwC,QAAQ,GAAGlF,cAAc,CAACgF,IAAI,EAAEjF,MAAM,CAACP,KAAK,CAAC;YACnD,IAAI0F,QAAQ,KAAK,KAAK,CAAC,IAAIA,QAAQ,KAAK,IAAI,EAAE;cAC5C,MAAM,IAAIC,KAAK,CAAC,4CAA4C,CAAC;YAC3E;YACYZ,GAAG,GAAAa,aAAA,KAAQzB,QAAQ,CAACnE,KAAK,CAAC0F,QAAQ,CAAC,CAAE;YACrC,IAAIX,GAAG,EAAE;cACPU,gBAAgB,CAAC7E,QAAQ,GAAGmE,GAAG,CAACnE,QAAQ;cACxCmE,GAAG,CAACvD,KAAK,GAAGuD,GAAG,CAACvD,KAAK,IAAIiE,gBAAgB,CAACjE,KAAK;cAC/CuD,GAAG,CAAC1D,OAAO,GAAG,CAAC,EAAE0D,GAAG,CAACnE,QAAQ,IAAI6E,gBAAgB,CAACpE,OAAO,CAAC;cAC1D,IAAI,OAAO0D,GAAG,CAACC,IAAI,KAAK,SAAS,EAAE;gBACjC,IAAI,OAAOD,GAAG,CAACE,MAAM,KAAK,SAAS,IAAIF,GAAG,CAACE,MAAM,EAAE;kBACjDQ,gBAAgB,CAACtC,cAAc,GAAG,EAAE4B,GAAG,CAACG,QAAQ,IAAIH,GAAG,CAACG,QAAQ,CAACpE,MAAM,CAAC;gBAC1F;gBACgB2E,gBAAgB,CAACvC,OAAO,GAAG6B,GAAG,CAAC7B,OAAO;cACtD;YACA;YACYkC,CAAC,EAAE;YACHD,GAAG,CAAC7D,IAAI,CAACb,SAAS,CAAC+E,IAAI,EAAE9E,MAAM,GAAG0E,CAAC,EAAEK,gBAAgB,CAAC,CAAC;YACvD,IAAIV,GAAG,EAAE;cACP,IAAMc,MAAM,GAAGzB,eAAe,CAACpE,KAAK,CAAC0F,QAAQ,CAAC,IAAIF,IAAI,CAACnB,kBAAkB,CAACrE,KAAK,CAAC;cAChFqF,SAAQ,CAACQ,MAAM,EAAEd,GAAG,CAAC;YACnC;UACA,CAAW,CAAC;QACZ,CAAS;QACDA,GAAG,CAAC1D,OAAO,GAAG,IAAI;QAClB,IAAMyE,KAAK,GAAG1B,eAAe,CAACpE,KAAK,CAAC6B,GAAG,CAAC,IAAIxB,GAAG,CAACgE,kBAAkB,CAACrE,KAAK,CAAC;QACzEqF,SAAQ,CAACS,KAAK,EAAEf,GAAG,CAAC;MAC5B;MACM,OAAOI,GAAG;IAChB,CAAK,MAAM;MACL,OAAO1E,SAAS,CAACJ,GAAG,EAAEK,MAAM,EAAE,KAAK,CAAC,CAAC;IAC3C;EACA,CAAG;EACD,OAAO;IACLqD,gBAAgB;IAChB9E,cAAc;IACdC;EACJ,CAAG;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
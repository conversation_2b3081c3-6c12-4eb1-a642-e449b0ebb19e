{"ast": null, "code": "import { resolveComponent as _resolveComponent, createVNode as _createVNode, withCtx as _withCtx, createTextVNode as _createTextVNode, createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock } from \"vue\";\nvar _hoisted_1 = {\n  class: \"ClusterManageNew\"\n};\nvar _hoisted_2 = {\n  class: \"globalFormButton\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  var _component_el_input = _resolveComponent(\"el-input\");\n  var _component_el_form_item = _resolveComponent(\"el-form-item\");\n  var _component_business_select_person = _resolveComponent(\"business-select-person\");\n  var _component_el_button = _resolveComponent(\"el-button\");\n  var _component_el_form = _resolveComponent(\"el-form\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createVNode(_component_el_form, {\n    ref: \"formRef\",\n    model: $setup.form,\n    rules: $setup.rules,\n    inline: \"\",\n    \"label-position\": \"top\",\n    class: \"globalForm\"\n  }, {\n    default: _withCtx(function () {\n      return [_createVNode(_component_el_form_item, {\n        label: \"消息内容\",\n        prop: \"content\",\n        class: \"globalFormTitle\"\n      }, {\n        default: _withCtx(function () {\n          return [_createVNode(_component_el_input, {\n            modelValue: $setup.form.content,\n            \"onUpdate:modelValue\": _cache[0] || (_cache[0] = function ($event) {\n              return $setup.form.content = $event;\n            }),\n            type: \"textarea\",\n            maxlength: \"800\",\n            \"show-word-limit\": \"\",\n            placeholder: \"请输入消息内容\",\n            rows: \"9\"\n          }, null, 8 /* PROPS */, [\"modelValue\"])];\n        }),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_form_item, {\n        label: \"接收人\",\n        class: \"globalFormTitle\"\n      }, {\n        default: _withCtx(function () {\n          return [_createVNode(_component_business_select_person, {\n            modelValue: $setup.form.receiverIds,\n            \"onUpdate:modelValue\": _cache[1] || (_cache[1] = function ($event) {\n              return $setup.form.receiverIds = $event;\n            }),\n            tabCode: $setup.tabCode\n          }, null, 8 /* PROPS */, [\"modelValue\", \"tabCode\"])];\n        }),\n        _: 1 /* STABLE */\n      }), _createElementVNode(\"div\", _hoisted_2, [_createVNode(_component_el_button, {\n        type: \"primary\",\n        onClick: _cache[2] || (_cache[2] = function ($event) {\n          return $setup.submitForm($setup.formRef);\n        })\n      }, {\n        default: _withCtx(function () {\n          return _cache[3] || (_cache[3] = [_createTextVNode(\"提交\")]);\n        }),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_button, {\n        onClick: $setup.resetForm\n      }, {\n        default: _withCtx(function () {\n          return _cache[4] || (_cache[4] = [_createTextVNode(\"取消\")]);\n        }),\n        _: 1 /* STABLE */\n      })])];\n    }),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"model\", \"rules\"])]);\n}", "map": {"version": 3, "names": ["class", "_createElementBlock", "_hoisted_1", "_createVNode", "_component_el_form", "ref", "model", "$setup", "form", "rules", "inline", "default", "_withCtx", "_component_el_form_item", "label", "prop", "_component_el_input", "modelValue", "content", "_cache", "$event", "type", "maxlength", "placeholder", "rows", "_", "_component_business_select_person", "receiverIds", "tabCode", "_createElementVNode", "_hoisted_2", "_component_el_button", "onClick", "submitForm", "formRef", "_createTextVNode", "resetForm"], "sources": ["D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\interaction\\src\\views\\BoxMessage\\PushMessageNew.vue"], "sourcesContent": ["<template>\r\n  <div class=\"ClusterManageNew\">\r\n    <el-form ref=\"formRef\"\r\n             :model=\"form\"\r\n             :rules=\"rules\"\r\n             inline\r\n             label-position=\"top\"\r\n             class=\"globalForm\">\r\n      <el-form-item label=\"消息内容\"\r\n                    prop=\"content\"\r\n                    class=\"globalFormTitle\">\r\n        <el-input v-model=\"form.content\"\r\n                  type=\"textarea\"\r\n                  maxlength=\"800\"\r\n                  show-word-limit\r\n                  placeholder=\"请输入消息内容\"\r\n                  rows=\"9\" />\r\n      </el-form-item>\r\n      <el-form-item label=\"接收人\"\r\n                    class=\"globalFormTitle\">\r\n        <business-select-person v-model=\"form.receiverIds\"\r\n                                :tabCode=\"tabCode\"></business-select-person>\r\n      </el-form-item>\r\n      <div class=\"globalFormButton\">\r\n        <el-button type=\"primary\"\r\n                   @click=\"submitForm(formRef)\">提交</el-button>\r\n        <el-button @click=\"resetForm\">取消</el-button>\r\n      </div>\r\n    </el-form>\r\n  </div>\r\n</template>\r\n<script>\r\nexport default { name: 'ClusterManageNew' }\r\n</script>\r\n<script setup>\r\nimport api from '@/api'\r\nimport { reactive, ref, onMounted, computed } from 'vue'\r\nimport { selectUser } from 'common/js/system_var.js'\r\nimport { ElMessage } from 'element-plus'\r\nconst emit = defineEmits(['callback'])\r\n\r\nconst tabCode = computed(() => selectUser.value.boxMessage)\r\nconst formRef = ref()\r\nconst form = reactive({\r\n  content: '', // 消息内容\r\n  receiverIds: [] // 接收人\r\n})\r\nconst rules = reactive({ content: [{ required: true, message: '请输入消息内容', trigger: ['blur', 'change'] }] })\r\n\r\nonMounted(() => { })\r\n\r\nconst submitForm = async (formEl) => {\r\n  if (!formEl) return\r\n  await formEl.validate((valid, fields) => {\r\n    if (valid) { globalJson() } else { ElMessage({ type: 'warning', message: '请根据提示信息完善字段内容！' }) }\r\n  })\r\n}\r\nconst globalJson = async () => {\r\n  const { code } = await api.globalJson('/boxMessage/add', {\r\n    form: {\r\n      businessCode: 'system',\r\n      content: form.content\r\n    },\r\n    receiverIds: form.receiverIds\r\n  })\r\n  if (code === 200) {\r\n    ElMessage({ type: 'success', message: '新增成功' })\r\n    emit('callback')\r\n  }\r\n}\r\nconst resetForm = () => { emit('callback') }\r\n</script>\r\n<style lang=\"scss\">\r\n.ClusterManageNew {\r\n  width: 990px;\r\n}\r\n</style>\r\n"], "mappings": ";;EACOA,KAAK,EAAC;AAAkB;;EAsBpBA,KAAK,EAAC;AAAkB;;;;;;;uBAtBjCC,mBAAA,CA4BM,OA5BNC,UA4BM,GA3BJC,YAAA,CA0BUC,kBAAA;IA1BDC,GAAG,EAAC,SAAS;IACZC,KAAK,EAAEC,MAAA,CAAAC,IAAI;IACXC,KAAK,EAAEF,MAAA,CAAAE,KAAK;IACbC,MAAM,EAAN,EAAM;IACN,gBAAc,EAAC,KAAK;IACpBV,KAAK,EAAC;;IAPnBW,OAAA,EAAAC,QAAA,CAQM;MAAA,OASe,CATfT,YAAA,CASeU,uBAAA;QATDC,KAAK,EAAC,MAAM;QACZC,IAAI,EAAC,SAAS;QACdf,KAAK,EAAC;;QAV1BW,OAAA,EAAAC,QAAA,CAWQ;UAAA,OAKqB,CALrBT,YAAA,CAKqBa,mBAAA;YAhB7BC,UAAA,EAW2BV,MAAA,CAAAC,IAAI,CAACU,OAAO;YAXvC,uBAAAC,MAAA,QAAAA,MAAA,gBAAAC,MAAA;cAAA,OAW2Bb,MAAA,CAAAC,IAAI,CAACU,OAAO,GAAAE,MAAA;YAAA;YACrBC,IAAI,EAAC,UAAU;YACfC,SAAS,EAAC,KAAK;YACf,iBAAe,EAAf,EAAe;YACfC,WAAW,EAAC,SAAS;YACrBC,IAAI,EAAC;;;QAhBvBC,CAAA;UAkBMtB,YAAA,CAIeU,uBAAA;QAJDC,KAAK,EAAC,KAAK;QACXd,KAAK,EAAC;;QAnB1BW,OAAA,EAAAC,QAAA,CAoBQ;UAAA,OACoE,CADpET,YAAA,CACoEuB,iCAAA;YArB5ET,UAAA,EAoByCV,MAAA,CAAAC,IAAI,CAACmB,WAAW;YApBzD,uBAAAR,MAAA,QAAAA,MAAA,gBAAAC,MAAA;cAAA,OAoByCb,MAAA,CAAAC,IAAI,CAACmB,WAAW,GAAAP,MAAA;YAAA;YACxBQ,OAAO,EAAErB,MAAA,CAAAqB;;;QArB1CH,CAAA;UAuBMI,mBAAA,CAIM,OAJNC,UAIM,GAHJ3B,YAAA,CACsD4B,oBAAA;QAD3CV,IAAI,EAAC,SAAS;QACbW,OAAK,EAAAb,MAAA,QAAAA,MAAA,gBAAAC,MAAA;UAAA,OAAEb,MAAA,CAAA0B,UAAU,CAAC1B,MAAA,CAAA2B,OAAO;QAAA;;QAzB7CvB,OAAA,EAAAC,QAAA,CAyBgD;UAAA,OAAEO,MAAA,QAAAA,MAAA,OAzBlDgB,gBAAA,CAyBgD,IAAE,E;;QAzBlDV,CAAA;UA0BQtB,YAAA,CAA4C4B,oBAAA;QAAhCC,OAAK,EAAEzB,MAAA,CAAA6B;MAAS;QA1BpCzB,OAAA,EAAAC,QAAA,CA0BsC;UAAA,OAAEO,MAAA,QAAAA,MAAA,OA1BxCgB,gBAAA,CA0BsC,IAAE,E;;QA1BxCV,CAAA;;;IAAAA,CAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
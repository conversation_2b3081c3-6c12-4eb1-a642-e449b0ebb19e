{"ast": null, "code": "import { resolveComponent as _resolveComponent, createVNode as _createVNode, withCtx as _withCtx, createTextVNode as _createTextVNode, createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock } from \"vue\";\nvar _hoisted_1 = {\n  class: \"MyCollectionMove\"\n};\nvar _hoisted_2 = {\n  class: \"globalFormButton\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  var _component_el_tree_select = _resolveComponent(\"el-tree-select\");\n  var _component_el_form_item = _resolveComponent(\"el-form-item\");\n  var _component_el_button = _resolveComponent(\"el-button\");\n  var _component_el_form = _resolveComponent(\"el-form\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createVNode(_component_el_form, {\n    ref: \"formRef\",\n    model: $setup.form,\n    rules: $setup.rules,\n    inline: \"\",\n    \"label-position\": \"top\",\n    class: \"globalForm\"\n  }, {\n    default: _withCtx(function () {\n      return [_createVNode(_component_el_form_item, {\n        label: \"分类\",\n        prop: \"typeId\",\n        class: \"globalFormTitle\"\n      }, {\n        default: _withCtx(function () {\n          return [_createVNode(_component_el_tree_select, {\n            modelValue: $setup.form.typeId,\n            \"onUpdate:modelValue\": _cache[0] || (_cache[0] = function ($event) {\n              return $setup.form.typeId = $event;\n            }),\n            data: $setup.treeData,\n            \"check-strictly\": \"\",\n            \"node-key\": \"id\",\n            \"render-after-expand\": false,\n            clearable: \"\"\n          }, null, 8 /* PROPS */, [\"modelValue\", \"data\"])];\n        }),\n        _: 1 /* STABLE */\n      }), _createElementVNode(\"div\", _hoisted_2, [_createVNode(_component_el_button, {\n        type: \"primary\",\n        onClick: _cache[1] || (_cache[1] = function ($event) {\n          return $setup.submitForm($setup.formRef);\n        })\n      }, {\n        default: _withCtx(function () {\n          return _cache[2] || (_cache[2] = [_createTextVNode(\"提交\")]);\n        }),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_button, {\n        onClick: $setup.resetForm\n      }, {\n        default: _withCtx(function () {\n          return _cache[3] || (_cache[3] = [_createTextVNode(\"取消\")]);\n        }),\n        _: 1 /* STABLE */\n      })])];\n    }),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"model\", \"rules\"])]);\n}", "map": {"version": 3, "names": ["class", "_createElementBlock", "_hoisted_1", "_createVNode", "_component_el_form", "ref", "model", "$setup", "form", "rules", "inline", "default", "_withCtx", "_component_el_form_item", "label", "prop", "_component_el_tree_select", "modelValue", "typeId", "_cache", "$event", "data", "treeData", "clearable", "_", "_createElementVNode", "_hoisted_2", "_component_el_button", "type", "onClick", "submitForm", "formRef", "_createTextVNode", "resetForm"], "sources": ["D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\interaction\\src\\views\\MyCollection\\component\\MyCollectionMove.vue"], "sourcesContent": ["<template>\r\n  <div class=\"MyCollectionMove\">\r\n    <el-form ref=\"formRef\"\r\n             :model=\"form\"\r\n             :rules=\"rules\"\r\n             inline\r\n             label-position=\"top\"\r\n             class=\"globalForm\">\r\n      <el-form-item label=\"分类\"\r\n                    prop=\"typeId\"\r\n                    class=\"globalFormTitle\">\r\n        <el-tree-select v-model=\"form.typeId\"\r\n                        :data=\"treeData\"\r\n                        check-strictly\r\n                        node-key=\"id\"\r\n                        :render-after-expand=\"false\"\r\n                        clearable />\r\n      </el-form-item>\r\n      <div class=\"globalFormButton\">\r\n        <el-button type=\"primary\"\r\n                   @click=\"submitForm(formRef)\">提交</el-button>\r\n        <el-button @click=\"resetForm\">取消</el-button>\r\n      </div>\r\n    </el-form>\r\n  </div>\r\n</template>\r\n<script>\r\nexport default { name: 'MyCollectionMove' }\r\n</script>\r\n<script setup>\r\nimport api from '@/api'\r\nimport { reactive, ref, onMounted } from 'vue'\r\nimport { ElMessage } from 'element-plus'\r\nconst emit = defineEmits(['callback'])\r\n\r\nconst formRef = ref()\r\nconst form = reactive({ typeId: '' })\r\nconst rules = reactive({ label: [{ required: true, message: '请选择分类', trigger: ['blur', 'change'] }] })\r\nconst treeData = ref([])\r\n\r\nonMounted(() => { favoriteFolderList() })\r\n\r\nconst favoriteFolderList = async () => {\r\n  const res = await api.favoriteFolderList()\r\n  var { data } = res\r\n  treeData.value = data\r\n}\r\nconst submitForm = async (formEl) => {\r\n  if (!formEl) return\r\n  await formEl.validate((valid, fields) => {\r\n    if (valid) { emit('callback', form.typeId) } else { ElMessage({ type: 'warning', message: '请根据提示信息完善字段内容！' }) }\r\n  })\r\n}\r\nconst resetForm = () => { emit('callback') }\r\n</script>\r\n<style lang=\"scss\">\r\n.MyCollectionMove {\r\n  width: 680px;\r\n}\r\n</style>\r\n"], "mappings": ";;EACOA,KAAK,EAAC;AAAkB;;EAiBpBA,KAAK,EAAC;AAAkB;;;;;;uBAjBjCC,mBAAA,CAuBM,OAvBNC,UAuBM,GAtBJC,YAAA,CAqBUC,kBAAA;IArBDC,GAAG,EAAC,SAAS;IACZC,KAAK,EAAEC,MAAA,CAAAC,IAAI;IACXC,KAAK,EAAEF,MAAA,CAAAE,KAAK;IACbC,MAAM,EAAN,EAAM;IACN,gBAAc,EAAC,KAAK;IACpBV,KAAK,EAAC;;IAPnBW,OAAA,EAAAC,QAAA,CAQM;MAAA,OASe,CATfT,YAAA,CASeU,uBAAA;QATDC,KAAK,EAAC,IAAI;QACVC,IAAI,EAAC,QAAQ;QACbf,KAAK,EAAC;;QAV1BW,OAAA,EAAAC,QAAA,CAWQ;UAAA,OAK4B,CAL5BT,YAAA,CAK4Ba,yBAAA;YAhBpCC,UAAA,EAWiCV,MAAA,CAAAC,IAAI,CAACU,MAAM;YAX5C,uBAAAC,MAAA,QAAAA,MAAA,gBAAAC,MAAA;cAAA,OAWiCb,MAAA,CAAAC,IAAI,CAACU,MAAM,GAAAE,MAAA;YAAA;YACnBC,IAAI,EAAEd,MAAA,CAAAe,QAAQ;YACf,gBAAc,EAAd,EAAc;YACd,UAAQ,EAAC,IAAI;YACZ,qBAAmB,EAAE,KAAK;YAC3BC,SAAS,EAAT;;;QAhBxBC,CAAA;UAkBMC,mBAAA,CAIM,OAJNC,UAIM,GAHJvB,YAAA,CACsDwB,oBAAA;QAD3CC,IAAI,EAAC,SAAS;QACbC,OAAK,EAAAV,MAAA,QAAAA,MAAA,gBAAAC,MAAA;UAAA,OAAEb,MAAA,CAAAuB,UAAU,CAACvB,MAAA,CAAAwB,OAAO;QAAA;;QApB7CpB,OAAA,EAAAC,QAAA,CAoBgD;UAAA,OAAEO,MAAA,QAAAA,MAAA,OApBlDa,gBAAA,CAoBgD,IAAE,E;;QApBlDR,CAAA;UAqBQrB,YAAA,CAA4CwB,oBAAA;QAAhCE,OAAK,EAAEtB,MAAA,CAAA0B;MAAS;QArBpCtB,OAAA,EAAAC,QAAA,CAqBsC;UAAA,OAAEO,MAAA,QAAAA,MAAA,OArBxCa,gBAAA,CAqBsC,IAAE,E;;QArBxCR,CAAA;;;IAAAA,CAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
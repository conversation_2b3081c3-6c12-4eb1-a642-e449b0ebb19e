{"ast": null, "code": "import '../../../../utils/index.mjs';\nimport { buildProps, definePropType } from '../../../../utils/vue/props/runtime.mjs';\nvar alphaSliderProps = buildProps({\n  color: {\n    type: definePropType(Object),\n    required: true\n  },\n  vertical: {\n    type: Boolean,\n    default: false\n  }\n});\nexport { alphaSliderProps };", "map": {"version": 3, "names": ["alphaSliderProps", "buildProps", "color", "type", "definePropType", "Object", "required", "vertical", "Boolean", "default"], "sources": ["../../../../../../../packages/components/color-picker/src/props/alpha-slider.ts"], "sourcesContent": ["import { buildProps, definePropType } from '@element-plus/utils'\n\nimport type { ExtractPropTypes } from 'vue'\nimport type Color from '../utils/color'\n\nexport const alphaSliderProps = buildProps({\n  color: {\n    type: definePropType<Color>(Object),\n    required: true,\n  },\n  vertical: {\n    type: Boolean,\n    default: false,\n  },\n} as const)\n\nexport type AlphaSliderProps = ExtractPropTypes<typeof alphaSliderProps>\n"], "mappings": ";;AACY,IAACA,gBAAgB,GAAGC,UAAU,CAAC;EACzCC,KAAK,EAAE;IACLC,IAAI,EAAEC,cAAc,CAACC,MAAM,CAAC;IAC5BC,QAAQ,EAAE;EACd,CAAG;EACDC,QAAQ,EAAE;IACRJ,IAAI,EAAEK,OAAO;IACbC,OAAO,EAAE;EACb;AACA,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
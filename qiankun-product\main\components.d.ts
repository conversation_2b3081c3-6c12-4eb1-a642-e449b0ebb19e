/* eslint-disable */
/* prettier-ignore */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
export {}

declare module 'vue' {
  export interface GlobalComponents {
    ElButton: typeof import('element-plus/es')['ElButton']
    ElIcon: typeof import('element-plus/es')['ElIcon']
    FilePreviewConfirm: typeof import('./src/components/file-preview-confirm/file-preview-confirm.vue')['default']
    GlobalCentralControl: typeof import('./src/components/global-central-control/global-central-control.vue')['default']
    GlobalFilePreview: typeof import('./src/components/global-file-preview/global-file-preview.vue')['default']
    PreviewExcel: typeof import('./src/components/global-file-preview/components/preview-excel.vue')['default']
    PreviewPdf: typeof import('./src/components/global-file-preview/components/preview-pdf.vue')['default']
    PreviewPdfOld: typeof import('./src/components/global-file-preview/components/PreviewPdfOld.vue')['default']
    PreviewPic: typeof import('./src/components/global-file-preview/components/preview-pic.vue')['default']
    PreviewPpt: typeof import('./src/components/global-file-preview/components/preview-ppt.vue')['default']
    PreviewText: typeof import('./src/components/global-file-preview/components/preview-text.vue')['default']
    PreviewVideo: typeof import('./src/components/global-file-preview/components/preview-video.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    XylMenu: typeof import('./src/components/xyl-menu/xyl-menu.vue')['default']
    XylMenuItem: typeof import('./src/components/xyl-menu/xyl-menu-item.vue')['default']
    XylRegion: typeof import('./src/components/xyl-region/xyl-region.vue')['default']
    XylTab: typeof import('./src/components/xyl-tab/xyl-tab.vue')['default']
    XylTabItem: typeof import('./src/components/xyl-tab/xyl-tab-item.vue')['default']
  }
}

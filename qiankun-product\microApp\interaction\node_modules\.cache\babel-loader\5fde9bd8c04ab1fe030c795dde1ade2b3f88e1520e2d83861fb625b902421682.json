{"ast": null, "code": "function _toConsumableArray(r) { return _arrayWithoutHoles(r) || _iterableToArray(r) || _unsupportedIterableToArray(r) || _nonIterableSpread(); }\nfunction _nonIterableSpread() { throw new TypeError(\"Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); }\nfunction _unsupportedIterableToArray(r, a) { if (r) { if (\"string\" == typeof r) return _arrayLikeToArray(r, a); var t = {}.toString.call(r).slice(8, -1); return \"Object\" === t && r.constructor && (t = r.constructor.name), \"Map\" === t || \"Set\" === t ? Array.from(r) : \"Arguments\" === t || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t) ? _arrayLikeToArray(r, a) : void 0; } }\nfunction _iterableToArray(r) { if (\"undefined\" != typeof Symbol && null != r[Symbol.iterator] || null != r[\"@@iterator\"]) return Array.from(r); }\nfunction _arrayWithoutHoles(r) { if (Array.isArray(r)) return _arrayLikeToArray(r); }\nfunction _arrayLikeToArray(r, a) { (null == a || a > r.length) && (a = r.length); for (var e = 0, n = Array(a); e < a; e++) n[e] = r[e]; return n; }\nimport { defineComponent, ref, inject, computed, unref, provide, readonly, toRef, watch, renderSlot } from 'vue';\nimport { useEventListener } from '@vueuse/core';\nimport '../../../utils/index.mjs';\nimport { rovingFocusGroupProps, ROVING_FOCUS_COLLECTION_INJECTION_KEY as COLLECTION_INJECTION_KEY } from './roving-focus-group.mjs';\nimport { ROVING_FOCUS_GROUP_INJECTION_KEY } from './tokens.mjs';\nimport { focusFirst } from './utils.mjs';\nimport _export_sfc from '../../../_virtual/plugin-vue_export-helper.mjs';\nimport { composeEventHandlers } from '../../../utils/dom/event.mjs';\nvar CURRENT_TAB_ID_CHANGE_EVT = \"currentTabIdChange\";\nvar ENTRY_FOCUS_EVT = \"rovingFocusGroup.entryFocus\";\nvar EVT_OPTS = {\n  bubbles: false,\n  cancelable: true\n};\nvar _sfc_main = defineComponent({\n  name: \"ElRovingFocusGroupImpl\",\n  inheritAttrs: false,\n  props: rovingFocusGroupProps,\n  emits: [CURRENT_TAB_ID_CHANGE_EVT, \"entryFocus\"],\n  setup(props, _ref) {\n    var emit = _ref.emit;\n    var _a;\n    var currentTabbedId = ref((_a = props.currentTabId || props.defaultCurrentTabId) != null ? _a : null);\n    var isBackingOut = ref(false);\n    var isClickFocus = ref(false);\n    var rovingFocusGroupRef = ref(null);\n    var _inject = inject(COLLECTION_INJECTION_KEY, void 0),\n      getItems = _inject.getItems;\n    var rovingFocusGroupRootStyle = computed(function () {\n      return [{\n        outline: \"none\"\n      }, props.style];\n    });\n    var onItemFocus = function onItemFocus(tabbedId) {\n      emit(CURRENT_TAB_ID_CHANGE_EVT, tabbedId);\n    };\n    var onItemShiftTab = function onItemShiftTab() {\n      isBackingOut.value = true;\n    };\n    var onMousedown = composeEventHandlers(function (e) {\n      var _a2;\n      (_a2 = props.onMousedown) == null ? void 0 : _a2.call(props, e);\n    }, function () {\n      isClickFocus.value = true;\n    });\n    var onFocus = composeEventHandlers(function (e) {\n      var _a2;\n      (_a2 = props.onFocus) == null ? void 0 : _a2.call(props, e);\n    }, function (e) {\n      var isKeyboardFocus = !unref(isClickFocus);\n      var target = e.target,\n        currentTarget = e.currentTarget;\n      if (target === currentTarget && isKeyboardFocus && !unref(isBackingOut)) {\n        var entryFocusEvt = new Event(ENTRY_FOCUS_EVT, EVT_OPTS);\n        currentTarget == null ? void 0 : currentTarget.dispatchEvent(entryFocusEvt);\n        if (!entryFocusEvt.defaultPrevented) {\n          var items = getItems().filter(function (item) {\n            return item.focusable;\n          });\n          var activeItem = items.find(function (item) {\n            return item.active;\n          });\n          var currentItem = items.find(function (item) {\n            return item.id === unref(currentTabbedId);\n          });\n          var candidates = [activeItem, currentItem].concat(_toConsumableArray(items)).filter(Boolean);\n          var candidateNodes = candidates.map(function (item) {\n            return item.ref;\n          });\n          focusFirst(candidateNodes);\n        }\n      }\n      isClickFocus.value = false;\n    });\n    var onBlur = composeEventHandlers(function (e) {\n      var _a2;\n      (_a2 = props.onBlur) == null ? void 0 : _a2.call(props, e);\n    }, function () {\n      isBackingOut.value = false;\n    });\n    var handleEntryFocus = function handleEntryFocus() {\n      for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n        args[_key] = arguments[_key];\n      }\n      emit.apply(void 0, [\"entryFocus\"].concat(args));\n    };\n    provide(ROVING_FOCUS_GROUP_INJECTION_KEY, {\n      currentTabbedId: readonly(currentTabbedId),\n      loop: toRef(props, \"loop\"),\n      tabIndex: computed(function () {\n        return unref(isBackingOut) ? -1 : 0;\n      }),\n      rovingFocusGroupRef,\n      rovingFocusGroupRootStyle,\n      orientation: toRef(props, \"orientation\"),\n      dir: toRef(props, \"dir\"),\n      onItemFocus,\n      onItemShiftTab,\n      onBlur,\n      onFocus,\n      onMousedown\n    });\n    watch(function () {\n      return props.currentTabId;\n    }, function (val) {\n      currentTabbedId.value = val != null ? val : null;\n    });\n    useEventListener(rovingFocusGroupRef, ENTRY_FOCUS_EVT, handleEntryFocus);\n  }\n});\nfunction _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {\n  return renderSlot(_ctx.$slots, \"default\");\n}\nvar ElRovingFocusGroupImpl = /* @__PURE__ */_export_sfc(_sfc_main, [[\"render\", _sfc_render], [\"__file\", \"roving-focus-group-impl.vue\"]]);\nexport { ElRovingFocusGroupImpl as default };", "map": {"version": 3, "names": ["CURRENT_TAB_ID_CHANGE_EVT", "ENTRY_FOCUS_EVT", "EVT_OPTS", "bubbles", "cancelable", "_sfc_main", "defineComponent", "name", "inheritAttrs", "props", "rovingFocusGroupProps", "emits", "setup", "_ref", "emit", "_a", "currentTabbedId", "ref", "currentTabId", "defaultCurrentTabId", "isBackingOut", "isClickFocus", "rovingFocusGroupRef", "_inject", "inject", "COLLECTION_INJECTION_KEY", "getItems", "rovingFocusGroupRootStyle", "computed", "outline", "style", "onItemFocus", "tabbedId", "onItemShiftTab", "value", "onMousedown", "composeEventHandlers", "e", "_a2", "call", "onFocus", "isKeyboardFocus", "unref", "target", "currentTarget", "entryFocusEvt", "Event", "dispatchEvent", "defaultPrevented", "items", "filter", "item", "focusable", "activeItem", "find", "active", "currentItem", "id", "candidates", "concat", "_toConsumableArray", "Boolean", "candidateNodes", "map", "focusFirst", "onBlur", "handleEntryFocus", "_len", "arguments", "length", "args", "Array", "_key", "apply", "provide", "ROVING_FOCUS_GROUP_INJECTION_KEY", "readonly", "loop", "toRef", "tabIndex", "orientation", "dir", "watch", "val"], "sources": ["../../../../../../packages/components/roving-focus-group/src/roving-focus-group-impl.vue"], "sourcesContent": ["<template>\n  <slot />\n</template>\n\n<script lang=\"ts\">\nimport {\n  computed,\n  defineComponent,\n  inject,\n  provide,\n  readonly,\n  ref,\n  toRef,\n  unref,\n  watch,\n} from 'vue'\nimport { useEventListener } from '@vueuse/core'\nimport { composeEventHandlers } from '@element-plus/utils'\nimport {\n  ROVING_FOCUS_COLLECTION_INJECTION_KEY,\n  rovingFocusGroupProps,\n} from './roving-focus-group'\nimport { ROVING_FOCUS_GROUP_INJECTION_KEY } from './tokens'\nimport { focusFirst } from './utils'\n\nimport type { StyleValue } from 'vue'\n\nconst CURRENT_TAB_ID_CHANGE_EVT = 'currentTabIdChange'\n\nconst ENTRY_FOCUS_EVT = 'rovingFocusGroup.entryFocus'\nconst EVT_OPTS: EventInit = { bubbles: false, cancelable: true }\nexport default defineComponent({\n  name: 'ElRovingFocusGroupImpl',\n  inheritAttrs: false,\n  props: rovingFocusGroupProps,\n  emits: [CURRENT_TAB_ID_CHANGE_EVT, 'entryFocus'],\n  setup(props, { emit }) {\n    const currentTabbedId = ref<string | null>(\n      (props.currentTabId || props.defaultCurrentTabId) ?? null\n    )\n    const isBackingOut = ref(false)\n    const isClickFocus = ref(false)\n    const rovingFocusGroupRef = ref<HTMLElement | null>(null)\n    const { getItems } = inject(\n      ROVING_FOCUS_COLLECTION_INJECTION_KEY,\n      undefined\n    )!\n    const rovingFocusGroupRootStyle = computed(() => {\n      // casting to any for fix compiler error since HTMLElement.StyleValue does not\n      // support CSSProperties\n      return [\n        {\n          outline: 'none',\n        },\n        props.style as StyleValue,\n      ] as any\n    })\n\n    const onItemFocus = (tabbedId: string) => {\n      emit(CURRENT_TAB_ID_CHANGE_EVT, tabbedId)\n    }\n\n    const onItemShiftTab = () => {\n      isBackingOut.value = true\n    }\n\n    const onMousedown = composeEventHandlers(\n      (e: Event) => {\n        props.onMousedown?.(e)\n      },\n      () => {\n        isClickFocus.value = true\n      }\n    )\n\n    const onFocus = composeEventHandlers(\n      (e: FocusEvent) => {\n        props.onFocus?.(e)\n      },\n      (e) => {\n        const isKeyboardFocus = !unref(isClickFocus)\n        const { target, currentTarget } = e\n        if (\n          target === currentTarget &&\n          isKeyboardFocus &&\n          !unref(isBackingOut)\n        ) {\n          const entryFocusEvt = new Event(ENTRY_FOCUS_EVT, EVT_OPTS)\n          currentTarget?.dispatchEvent(entryFocusEvt)\n\n          if (!entryFocusEvt.defaultPrevented) {\n            const items = getItems<{\n              id: string\n              focusable: boolean\n              active: boolean\n            }>().filter((item) => item.focusable)\n            const activeItem = items.find((item) => item.active)\n            const currentItem = items.find(\n              (item) => item.id === unref(currentTabbedId)\n            )\n            const candidates = [activeItem!, currentItem!, ...items].filter(\n              Boolean\n            )\n            const candidateNodes = candidates.map((item) => item.ref!)\n            focusFirst(candidateNodes)\n          }\n        }\n\n        isClickFocus.value = false\n      }\n    )\n\n    const onBlur = composeEventHandlers(\n      (e: Event) => {\n        props.onBlur?.(e)\n      },\n      () => {\n        isBackingOut.value = false\n      }\n    )\n\n    const handleEntryFocus = (...args: any[]) => {\n      emit('entryFocus', ...args)\n    }\n\n    provide(ROVING_FOCUS_GROUP_INJECTION_KEY, {\n      currentTabbedId: readonly(currentTabbedId),\n      loop: toRef(props, 'loop'),\n      tabIndex: computed(() => {\n        return unref(isBackingOut) ? -1 : 0\n      }),\n      rovingFocusGroupRef,\n      rovingFocusGroupRootStyle,\n      orientation: toRef(props, 'orientation'),\n      dir: toRef(props, 'dir'),\n      onItemFocus,\n      onItemShiftTab,\n      onBlur,\n      onFocus,\n      onMousedown,\n    })\n\n    watch(\n      () => props.currentTabId,\n      (val) => {\n        currentTabbedId.value = val ?? null\n      }\n    )\n\n    useEventListener(rovingFocusGroupRef, ENTRY_FOCUS_EVT, handleEntryFocus)\n  },\n})\n</script>\n"], "mappings": ";;;;;;;;;;;;;;AA2BA,IAAMA,yBAA4B;AAElC,IAAMC,eAAkB;AACxB,IAAMC,QAAsB;EAAEC,OAAS;EAAOC,UAAA,EAAY;AAAK;AAC/D,IAAKC,SAAA,GAAaC,eAAa;EAC7BC,IAAM;EACNC,YAAc;EACdC,KAAO,EAAAC,qBAAA;EACPC,KAAA,EAAO,CAACX,yBAAA,EAA2B,YAAY;EAC/CY,MAAMH,KAAO,EAAAI,IAAA,EAAU;IAAA,IAARC,IAAQ,GAAAD,IAAA,CAARC,IAAQ;IACrB,IAAAC,EAAM;IAGA,IAAAC,eAAe,GAAAC,GAAS,EAAAF,EAAA,GAAAN,KAAA,CAAAS,YAAA,IAAAT,KAAA,CAAAU,mBAAA,YAAAJ,EAAA;IACxB,IAAAK,YAAA,GAAeH,GAAA,CAAI,KAAK;IACxB,IAAAI,YAAA,GAAAJ,GAAA;IACN,IAAMK,mBACJ,GAAAL,GAAA;IAGI,IAAAM,OAAA,GAAAC,MAAA,CAAAC,wBAA2C;MAA3CC,QAAA,GAAAH,OAAA,CAAAG,QAAA;IAGG,IAAAC,yBAAA,GAAAC,QAAA;MACL;QAEAC,OAAA;MAAA,CACM,EACRpB,KAAA,CAAAqB,KAAA,CACD;IAED,CAAM;IACJ,IAAAC,WAAA,YAAAA,YAAAC,QAAA,EAAgC;MAClClB,IAAA,CAAAd,yBAAA,EAAAgC,QAAA;IAEA;IACE,IAAAC,cAAqB,YAArBA,cAAqBA,CAAA;MACvBb,YAAA,CAAAc,KAAA;IAEA,CAAM;IAEF,IAAAC,WAAA,GAAAC,oBAAqB,WAAAC,CAAA;MAAA,IAEjBC,GAAA;MACJ,CAAAA,GAAA,GAAA7B,KAAA,CAAa0B,WAAQ,qBAAAG,GAAA,CAAAC,IAAA,CAAA9B,KAAA,EAAA4B,CAAA;IAAA,CAEzB;MAEMhB,YAAA,CAAAa,KACJ;IACE;IACF,IACOM,OAAA,GAAAJ,oBAAA,WAAAC,CAAA;MACC,IAAAC,GAAA;MACA,CAAAA,GAAA,GAAA7B,KAAA,CAAA+B,OAA4B,qBAAAF,GAAA,CAAAC,IAAA,CAAA9B,KAAA,EAAA4B,CAAA;IAClC,aAAAA,CAAA;MAKE,IAAAI,eAAsB,IAAAC,KAAU,CAAArB,YAAA;MAChC,IAAAsB,MAAA,GAA0CN,CAAA,CAA1CM,MAAA;QAAAC,aAAA,GAA0CP,CAAA,CAA1CO,aAAA;MAEI,IAAAD,MAAA,KAAAC,aAAiC,IAAAH,eAAA,KAAAC,KAAA,CAAAtB,YAAA;QACnC,IAAAyB,aAIG,OAAAC,KAAA,CAAA7C,eAAA,EAAwBC,QAAS;QACpC0C,aAAA,QAAmB,GAAM,SAAMA,aAAA,CAAcG,aAAM,CAAAF,aAAA;QAC7C,KAAAA,aAAA,CAAAG,gBAAA;UAGA,IAAAC,KAAA,GAAAvB,QAA2B,GAAAwB,MAAA,WAAAC,IAAA;YAAA,OAAAA,IAAA,CAAAC,SAAA;UAAA;UAGjC,IAAMC,UAAA,GAAAJ,KAA4B,CAAAK,IAAA,WAAAH,IAAA;YAAA,OAAKA,IAAA,CAAAI,MAAA;UAAA;UACvC,IAAAC,WAAyB,GAAAP,KAAA,CAAAK,IAAA,WAAAH,IAAA;YAAA,OAAAA,IAAA,CAAAM,EAAA,KAAAf,KAAA,CAAA1B,eAAA;UAAA;UAC3B,IAAA0C,UAAA,IAAAL,UAAA,EAAAG,WAAA,EAAAG,MAAA,CAAAC,kBAAA,CAAAX,KAAA,GAAAC,MAAA,CAAAW,OAAA;UACF,IAAAC,cAAA,GAAAJ,UAAA,CAAAK,GAAA,WAAAZ,IAAA;YAAA,OAAAA,IAAA,CAAAlC,GAAA;UAAA;UAEA+C,UAAqB,CAAAF,cAAA;QAAA;MAIzB;MAEIzC,YAAA,CAAAa,KAAgB;IAAA;IAGhB,IAAA+B,MAAA,GAAa7B,oBAAQ,WAAAC,CAAA;MAEzB,IAAAC,GAAA;MAEM,CAAAA,GAAA,GAAA7B,KAAA,CAAAwD,MAAA,SAAuC,YAAA3B,GAAA,CAAAC,IAAA,CAAA9B,KAAA,EAAA4B,CAAA;IAC3C,CAAK;MACPjB,YAAA,CAAAc,KAAA;IAEA;IACE,IAAAgC,gBAAA,YAAAA,iBAAA,EAAyC;MAAA,SAAAC,IAAA,GAAAC,SAAA,CAAAC,MAAA,EAAAC,IAAA,OAAAC,KAAA,CAAAJ,IAAA,GAAAK,IAAA,MAAAA,IAAA,GAAAL,IAAA,EAAAK,IAAA;QAAAF,IAAA,CAAAE,IAAA,IAAAJ,SAAA,CAAAI,IAAA;MAAA;MACzC1D,IAAA,CAAA2D,KAAA,sBAAY,EAAAd,MAAA,CAAaW,IAAA;IAAA,CACzB;IACSI,OAAA,CAAAC,gCAA2B;MACpC3D,eAAC,EAAA4D,QAAA,CAAA5D,eAAA;MACD6D,IAAA,EAAAC,KAAA,CAAArE,KAAA;MACAsE,QAAA,EAAAnD,QAAA;QACA,OAAAc,KAAmB,CAAAtB,YAAoB;MAAA,CACvC;MACAE,mBAAA;MACAK,yBAAA;MACAqD,WAAA,EAAAF,KAAA,CAAArE,KAAA;MACAwE,GAAA,EAAAH,KAAA,CAAArE,KAAA;MACAsB,WAAA;MACDE,cAAA;MAEDgC,MACQ;MAEJzB,OAAA;MAEJL;IAEA,CAAiB;IACnB+C,KAAA;MAAA,OAAAzE,KAAA,CAAAS,YAAA;IAAA,aAAAiE,GAAA;MACDnE,eAAA,CAAAkB,KAAA,GAAAiD,GAAA,WAAAA,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
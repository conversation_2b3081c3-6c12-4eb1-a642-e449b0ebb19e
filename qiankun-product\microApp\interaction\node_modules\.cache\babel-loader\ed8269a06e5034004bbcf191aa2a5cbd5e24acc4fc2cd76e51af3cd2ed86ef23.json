{"ast": null, "code": "/**\n * Resize detection strategy that injects divs to elements in order to detect resize events on scroll events.\n * Heavily inspired by: https://github.com/marcj/css-element-queries/blob/master/src/ResizeSensor.js\n */\n\n\"use strict\";\n\nvar forEach = require(\"../collection-utils\").forEach;\nmodule.exports = function (options) {\n  options = options || {};\n  var reporter = options.reporter;\n  var batchProcessor = options.batchProcessor;\n  var getState = options.stateHandler.getState;\n  var hasState = options.stateHandler.hasState;\n  var idHandler = options.idHandler;\n  if (!batchProcessor) {\n    throw new Error(\"Missing required dependency: batchProcessor\");\n  }\n  if (!reporter) {\n    throw new Error(\"Missing required dependency: reporter.\");\n  }\n\n  //TODO: Could this perhaps be done at installation time?\n  var scrollbarSizes = getScrollbarSizes();\n  var styleId = \"erd_scroll_detection_scrollbar_style\";\n  var detectionContainerClass = \"erd_scroll_detection_container\";\n  function initDocument(targetDocument) {\n    // Inject the scrollbar styling that prevents them from appearing sometimes in Chrome.\n    // The injected container needs to have a class, so that it may be styled with CSS (pseudo elements).\n    injectScrollStyle(targetDocument, styleId, detectionContainerClass);\n  }\n  initDocument(window.document);\n  function buildCssTextString(rules) {\n    var seperator = options.important ? \" !important; \" : \"; \";\n    return (rules.join(seperator) + seperator).trim();\n  }\n  function getScrollbarSizes() {\n    var width = 500;\n    var height = 500;\n    var child = document.createElement(\"div\");\n    child.style.cssText = buildCssTextString([\"position: absolute\", \"width: \" + width * 2 + \"px\", \"height: \" + height * 2 + \"px\", \"visibility: hidden\", \"margin: 0\", \"padding: 0\"]);\n    var container = document.createElement(\"div\");\n    container.style.cssText = buildCssTextString([\"position: absolute\", \"width: \" + width + \"px\", \"height: \" + height + \"px\", \"overflow: scroll\", \"visibility: none\", \"top: \" + -width * 3 + \"px\", \"left: \" + -height * 3 + \"px\", \"visibility: hidden\", \"margin: 0\", \"padding: 0\"]);\n    container.appendChild(child);\n    document.body.insertBefore(container, document.body.firstChild);\n    var widthSize = width - container.clientWidth;\n    var heightSize = height - container.clientHeight;\n    document.body.removeChild(container);\n    return {\n      width: widthSize,\n      height: heightSize\n    };\n  }\n  function injectScrollStyle(targetDocument, styleId, containerClass) {\n    function injectStyle(style, method) {\n      method = method || function (element) {\n        targetDocument.head.appendChild(element);\n      };\n      var styleElement = targetDocument.createElement(\"style\");\n      styleElement.innerHTML = style;\n      styleElement.id = styleId;\n      method(styleElement);\n      return styleElement;\n    }\n    if (!targetDocument.getElementById(styleId)) {\n      var containerAnimationClass = containerClass + \"_animation\";\n      var containerAnimationActiveClass = containerClass + \"_animation_active\";\n      var style = \"/* Created by the element-resize-detector library. */\\n\";\n      style += \".\" + containerClass + \" > div::-webkit-scrollbar { \" + buildCssTextString([\"display: none\"]) + \" }\\n\\n\";\n      style += \".\" + containerAnimationActiveClass + \" { \" + buildCssTextString([\"-webkit-animation-duration: 0.1s\", \"animation-duration: 0.1s\", \"-webkit-animation-name: \" + containerAnimationClass, \"animation-name: \" + containerAnimationClass]) + \" }\\n\";\n      style += \"@-webkit-keyframes \" + containerAnimationClass + \" { 0% { opacity: 1; } 50% { opacity: 0; } 100% { opacity: 1; } }\\n\";\n      style += \"@keyframes \" + containerAnimationClass + \" { 0% { opacity: 1; } 50% { opacity: 0; } 100% { opacity: 1; } }\";\n      injectStyle(style);\n    }\n  }\n  function addAnimationClass(element) {\n    element.className += \" \" + detectionContainerClass + \"_animation_active\";\n  }\n  function addEvent(el, name, cb) {\n    if (el.addEventListener) {\n      el.addEventListener(name, cb);\n    } else if (el.attachEvent) {\n      el.attachEvent(\"on\" + name, cb);\n    } else {\n      return reporter.error(\"[scroll] Don't know how to add event listeners.\");\n    }\n  }\n  function removeEvent(el, name, cb) {\n    if (el.removeEventListener) {\n      el.removeEventListener(name, cb);\n    } else if (el.detachEvent) {\n      el.detachEvent(\"on\" + name, cb);\n    } else {\n      return reporter.error(\"[scroll] Don't know how to remove event listeners.\");\n    }\n  }\n  function getExpandElement(element) {\n    return getState(element).container.childNodes[0].childNodes[0].childNodes[0];\n  }\n  function getShrinkElement(element) {\n    return getState(element).container.childNodes[0].childNodes[0].childNodes[1];\n  }\n\n  /**\n   * Adds a resize event listener to the element.\n   * @public\n   * @param {element} element The element that should have the listener added.\n   * @param {function} listener The listener callback to be called for each resize event of the element. The element will be given as a parameter to the listener callback.\n   */\n  function addListener(element, listener) {\n    var listeners = getState(element).listeners;\n    if (!listeners.push) {\n      throw new Error(\"Cannot add listener to an element that is not detectable.\");\n    }\n    getState(element).listeners.push(listener);\n  }\n\n  /**\n   * Makes an element detectable and ready to be listened for resize events. Will call the callback when the element is ready to be listened for resize changes.\n   * @private\n   * @param {object} options Optional options object.\n   * @param {element} element The element to make detectable\n   * @param {function} callback The callback to be called when the element is ready to be listened for resize changes. Will be called with the element as first parameter.\n   */\n  function makeDetectable(options, element, callback) {\n    if (!callback) {\n      callback = element;\n      element = options;\n      options = null;\n    }\n    options = options || {};\n    function debug() {\n      if (options.debug) {\n        var args = Array.prototype.slice.call(arguments);\n        args.unshift(idHandler.get(element), \"Scroll: \");\n        if (reporter.log.apply) {\n          reporter.log.apply(null, args);\n        } else {\n          for (var i = 0; i < args.length; i++) {\n            reporter.log(args[i]);\n          }\n        }\n      }\n    }\n    function isDetached(element) {\n      function isInDocument(element) {\n        var isInShadowRoot = element.getRootNode && element.getRootNode().contains(element);\n        return element === element.ownerDocument.body || element.ownerDocument.body.contains(element) || isInShadowRoot;\n      }\n      if (!isInDocument(element)) {\n        return true;\n      }\n\n      // FireFox returns null style in hidden iframes. See https://github.com/wnr/element-resize-detector/issues/68 and https://bugzilla.mozilla.org/show_bug.cgi?id=795520\n      if (window.getComputedStyle(element) === null) {\n        return true;\n      }\n      return false;\n    }\n    function isUnrendered(element) {\n      // Check the absolute positioned container since the top level container is display: inline.\n      var container = getState(element).container.childNodes[0];\n      var style = window.getComputedStyle(container);\n      return !style.width || style.width.indexOf(\"px\") === -1; //Can only compute pixel value when rendered.\n    }\n    function getStyle() {\n      // Some browsers only force layouts when actually reading the style properties of the style object, so make sure that they are all read here,\n      // so that the user of the function can be sure that it will perform the layout here, instead of later (important for batching).\n      var elementStyle = window.getComputedStyle(element);\n      var style = {};\n      style.position = elementStyle.position;\n      style.width = element.offsetWidth;\n      style.height = element.offsetHeight;\n      style.top = elementStyle.top;\n      style.right = elementStyle.right;\n      style.bottom = elementStyle.bottom;\n      style.left = elementStyle.left;\n      style.widthCSS = elementStyle.width;\n      style.heightCSS = elementStyle.height;\n      return style;\n    }\n    function storeStartSize() {\n      var style = getStyle();\n      getState(element).startSize = {\n        width: style.width,\n        height: style.height\n      };\n      debug(\"Element start size\", getState(element).startSize);\n    }\n    function initListeners() {\n      getState(element).listeners = [];\n    }\n    function storeStyle() {\n      debug(\"storeStyle invoked.\");\n      if (!getState(element)) {\n        debug(\"Aborting because element has been uninstalled\");\n        return;\n      }\n      var style = getStyle();\n      getState(element).style = style;\n    }\n    function storeCurrentSize(element, width, height) {\n      getState(element).lastWidth = width;\n      getState(element).lastHeight = height;\n    }\n    function getExpandChildElement(element) {\n      return getExpandElement(element).childNodes[0];\n    }\n    function getWidthOffset() {\n      return 2 * scrollbarSizes.width + 1;\n    }\n    function getHeightOffset() {\n      return 2 * scrollbarSizes.height + 1;\n    }\n    function getExpandWidth(width) {\n      return width + 10 + getWidthOffset();\n    }\n    function getExpandHeight(height) {\n      return height + 10 + getHeightOffset();\n    }\n    function getShrinkWidth(width) {\n      return width * 2 + getWidthOffset();\n    }\n    function getShrinkHeight(height) {\n      return height * 2 + getHeightOffset();\n    }\n    function positionScrollbars(element, width, height) {\n      var expand = getExpandElement(element);\n      var shrink = getShrinkElement(element);\n      var expandWidth = getExpandWidth(width);\n      var expandHeight = getExpandHeight(height);\n      var shrinkWidth = getShrinkWidth(width);\n      var shrinkHeight = getShrinkHeight(height);\n      expand.scrollLeft = expandWidth;\n      expand.scrollTop = expandHeight;\n      shrink.scrollLeft = shrinkWidth;\n      shrink.scrollTop = shrinkHeight;\n    }\n    function injectContainerElement() {\n      var container = getState(element).container;\n      if (!container) {\n        container = document.createElement(\"div\");\n        container.className = detectionContainerClass;\n        container.style.cssText = buildCssTextString([\"visibility: hidden\", \"display: inline\", \"width: 0px\", \"height: 0px\", \"z-index: -1\", \"overflow: hidden\", \"margin: 0\", \"padding: 0\"]);\n        getState(element).container = container;\n        addAnimationClass(container);\n        element.appendChild(container);\n        var onAnimationStart = function onAnimationStart() {\n          getState(element).onRendered && getState(element).onRendered();\n        };\n        addEvent(container, \"animationstart\", onAnimationStart);\n\n        // Store the event handler here so that they may be removed when uninstall is called.\n        // See uninstall function for an explanation why it is needed.\n        getState(element).onAnimationStart = onAnimationStart;\n      }\n      return container;\n    }\n    function injectScrollElements() {\n      function alterPositionStyles() {\n        var style = getState(element).style;\n        if (style.position === \"static\") {\n          element.style.setProperty(\"position\", \"relative\", options.important ? \"important\" : \"\");\n          var removeRelativeStyles = function removeRelativeStyles(reporter, element, style, property) {\n            function getNumericalValue(value) {\n              return value.replace(/[^-\\d\\.]/g, \"\");\n            }\n            var value = style[property];\n            if (value !== \"auto\" && getNumericalValue(value) !== \"0\") {\n              reporter.warn(\"An element that is positioned static has style.\" + property + \"=\" + value + \" which is ignored due to the static positioning. The element will need to be positioned relative, so the style.\" + property + \" will be set to 0. Element: \", element);\n              element.style[property] = 0;\n            }\n          };\n\n          //Check so that there are no accidental styles that will make the element styled differently now that is is relative.\n          //If there are any, set them to 0 (this should be okay with the user since the style properties did nothing before [since the element was positioned static] anyway).\n          removeRelativeStyles(reporter, element, style, \"top\");\n          removeRelativeStyles(reporter, element, style, \"right\");\n          removeRelativeStyles(reporter, element, style, \"bottom\");\n          removeRelativeStyles(reporter, element, style, \"left\");\n        }\n      }\n      function getLeftTopBottomRightCssText(left, top, bottom, right) {\n        left = !left ? \"0\" : left + \"px\";\n        top = !top ? \"0\" : top + \"px\";\n        bottom = !bottom ? \"0\" : bottom + \"px\";\n        right = !right ? \"0\" : right + \"px\";\n        return [\"left: \" + left, \"top: \" + top, \"right: \" + right, \"bottom: \" + bottom];\n      }\n      debug(\"Injecting elements\");\n      if (!getState(element)) {\n        debug(\"Aborting because element has been uninstalled\");\n        return;\n      }\n      alterPositionStyles();\n      var rootContainer = getState(element).container;\n      if (!rootContainer) {\n        rootContainer = injectContainerElement();\n      }\n\n      // Due to this WebKit bug https://bugs.webkit.org/show_bug.cgi?id=80808 (currently fixed in Blink, but still present in WebKit browsers such as Safari),\n      // we need to inject two containers, one that is width/height 100% and another that is left/top -1px so that the final container always is 1x1 pixels bigger than\n      // the targeted element.\n      // When the bug is resolved, \"containerContainer\" may be removed.\n\n      // The outer container can occasionally be less wide than the targeted when inside inline elements element in WebKit (see https://bugs.webkit.org/show_bug.cgi?id=152980).\n      // This should be no problem since the inner container either way makes sure the injected scroll elements are at least 1x1 px.\n\n      var scrollbarWidth = scrollbarSizes.width;\n      var scrollbarHeight = scrollbarSizes.height;\n      var containerContainerStyle = buildCssTextString([\"position: absolute\", \"flex: none\", \"overflow: hidden\", \"z-index: -1\", \"visibility: hidden\", \"width: 100%\", \"height: 100%\", \"left: 0px\", \"top: 0px\"]);\n      var containerStyle = buildCssTextString([\"position: absolute\", \"flex: none\", \"overflow: hidden\", \"z-index: -1\", \"visibility: hidden\"].concat(getLeftTopBottomRightCssText(-(1 + scrollbarWidth), -(1 + scrollbarHeight), -scrollbarHeight, -scrollbarWidth)));\n      var expandStyle = buildCssTextString([\"position: absolute\", \"flex: none\", \"overflow: scroll\", \"z-index: -1\", \"visibility: hidden\", \"width: 100%\", \"height: 100%\"]);\n      var shrinkStyle = buildCssTextString([\"position: absolute\", \"flex: none\", \"overflow: scroll\", \"z-index: -1\", \"visibility: hidden\", \"width: 100%\", \"height: 100%\"]);\n      var expandChildStyle = buildCssTextString([\"position: absolute\", \"left: 0\", \"top: 0\"]);\n      var shrinkChildStyle = buildCssTextString([\"position: absolute\", \"width: 200%\", \"height: 200%\"]);\n      var containerContainer = document.createElement(\"div\");\n      var container = document.createElement(\"div\");\n      var expand = document.createElement(\"div\");\n      var expandChild = document.createElement(\"div\");\n      var shrink = document.createElement(\"div\");\n      var shrinkChild = document.createElement(\"div\");\n\n      // Some browsers choke on the resize system being rtl, so force it to ltr. https://github.com/wnr/element-resize-detector/issues/56\n      // However, dir should not be set on the top level container as it alters the dimensions of the target element in some browsers.\n      containerContainer.dir = \"ltr\";\n      containerContainer.style.cssText = containerContainerStyle;\n      containerContainer.className = detectionContainerClass;\n      container.className = detectionContainerClass;\n      container.style.cssText = containerStyle;\n      expand.style.cssText = expandStyle;\n      expandChild.style.cssText = expandChildStyle;\n      shrink.style.cssText = shrinkStyle;\n      shrinkChild.style.cssText = shrinkChildStyle;\n      expand.appendChild(expandChild);\n      shrink.appendChild(shrinkChild);\n      container.appendChild(expand);\n      container.appendChild(shrink);\n      containerContainer.appendChild(container);\n      rootContainer.appendChild(containerContainer);\n      function onExpandScroll() {\n        var state = getState(element);\n        if (state && state.onExpand) {\n          state.onExpand();\n        } else {\n          debug(\"Aborting expand scroll handler: element has been uninstalled\");\n        }\n      }\n      function onShrinkScroll() {\n        var state = getState(element);\n        if (state && state.onShrink) {\n          state.onShrink();\n        } else {\n          debug(\"Aborting shrink scroll handler: element has been uninstalled\");\n        }\n      }\n      addEvent(expand, \"scroll\", onExpandScroll);\n      addEvent(shrink, \"scroll\", onShrinkScroll);\n\n      // Store the event handlers here so that they may be removed when uninstall is called.\n      // See uninstall function for an explanation why it is needed.\n      getState(element).onExpandScroll = onExpandScroll;\n      getState(element).onShrinkScroll = onShrinkScroll;\n    }\n    function registerListenersAndPositionElements() {\n      function updateChildSizes(element, width, height) {\n        var expandChild = getExpandChildElement(element);\n        var expandWidth = getExpandWidth(width);\n        var expandHeight = getExpandHeight(height);\n        expandChild.style.setProperty(\"width\", expandWidth + \"px\", options.important ? \"important\" : \"\");\n        expandChild.style.setProperty(\"height\", expandHeight + \"px\", options.important ? \"important\" : \"\");\n      }\n      function updateDetectorElements(done) {\n        var width = element.offsetWidth;\n        var height = element.offsetHeight;\n\n        // Check whether the size has actually changed since last time the algorithm ran. If not, some steps may be skipped.\n        var sizeChanged = width !== getState(element).lastWidth || height !== getState(element).lastHeight;\n        debug(\"Storing current size\", width, height);\n\n        // Store the size of the element sync here, so that multiple scroll events may be ignored in the event listeners.\n        // Otherwise the if-check in handleScroll is useless.\n        storeCurrentSize(element, width, height);\n\n        // Since we delay the processing of the batch, there is a risk that uninstall has been called before the batch gets to execute.\n        // Since there is no way to cancel the fn executions, we need to add an uninstall guard to all fns of the batch.\n\n        batchProcessor.add(0, function performUpdateChildSizes() {\n          if (!sizeChanged) {\n            return;\n          }\n          if (!getState(element)) {\n            debug(\"Aborting because element has been uninstalled\");\n            return;\n          }\n          if (!areElementsInjected()) {\n            debug(\"Aborting because element container has not been initialized\");\n            return;\n          }\n          if (options.debug) {\n            var w = element.offsetWidth;\n            var h = element.offsetHeight;\n            if (w !== width || h !== height) {\n              reporter.warn(idHandler.get(element), \"Scroll: Size changed before updating detector elements.\");\n            }\n          }\n          updateChildSizes(element, width, height);\n        });\n        batchProcessor.add(1, function updateScrollbars() {\n          // This function needs to be invoked event though the size is unchanged. The element could have been resized very quickly and then\n          // been restored to the original size, which will have changed the scrollbar positions.\n\n          if (!getState(element)) {\n            debug(\"Aborting because element has been uninstalled\");\n            return;\n          }\n          if (!areElementsInjected()) {\n            debug(\"Aborting because element container has not been initialized\");\n            return;\n          }\n          positionScrollbars(element, width, height);\n        });\n        if (sizeChanged && done) {\n          batchProcessor.add(2, function () {\n            if (!getState(element)) {\n              debug(\"Aborting because element has been uninstalled\");\n              return;\n            }\n            if (!areElementsInjected()) {\n              debug(\"Aborting because element container has not been initialized\");\n              return;\n            }\n            done();\n          });\n        }\n      }\n      function areElementsInjected() {\n        return !!getState(element).container;\n      }\n      function notifyListenersIfNeeded() {\n        function isFirstNotify() {\n          return getState(element).lastNotifiedWidth === undefined;\n        }\n        debug(\"notifyListenersIfNeeded invoked\");\n        var state = getState(element);\n\n        // Don't notify if the current size is the start size, and this is the first notification.\n        if (isFirstNotify() && state.lastWidth === state.startSize.width && state.lastHeight === state.startSize.height) {\n          return debug(\"Not notifying: Size is the same as the start size, and there has been no notification yet.\");\n        }\n\n        // Don't notify if the size already has been notified.\n        if (state.lastWidth === state.lastNotifiedWidth && state.lastHeight === state.lastNotifiedHeight) {\n          return debug(\"Not notifying: Size already notified\");\n        }\n        debug(\"Current size not notified, notifying...\");\n        state.lastNotifiedWidth = state.lastWidth;\n        state.lastNotifiedHeight = state.lastHeight;\n        forEach(getState(element).listeners, function (listener) {\n          listener(element);\n        });\n      }\n      function handleRender() {\n        debug(\"startanimation triggered.\");\n        if (isUnrendered(element)) {\n          debug(\"Ignoring since element is still unrendered...\");\n          return;\n        }\n        debug(\"Element rendered.\");\n        var expand = getExpandElement(element);\n        var shrink = getShrinkElement(element);\n        if (expand.scrollLeft === 0 || expand.scrollTop === 0 || shrink.scrollLeft === 0 || shrink.scrollTop === 0) {\n          debug(\"Scrollbars out of sync. Updating detector elements...\");\n          updateDetectorElements(notifyListenersIfNeeded);\n        }\n      }\n      function handleScroll() {\n        debug(\"Scroll detected.\");\n        if (isUnrendered(element)) {\n          // Element is still unrendered. Skip this scroll event.\n          debug(\"Scroll event fired while unrendered. Ignoring...\");\n          return;\n        }\n        updateDetectorElements(notifyListenersIfNeeded);\n      }\n      debug(\"registerListenersAndPositionElements invoked.\");\n      if (!getState(element)) {\n        debug(\"Aborting because element has been uninstalled\");\n        return;\n      }\n      getState(element).onRendered = handleRender;\n      getState(element).onExpand = handleScroll;\n      getState(element).onShrink = handleScroll;\n      var style = getState(element).style;\n      updateChildSizes(element, style.width, style.height);\n    }\n    function finalizeDomMutation() {\n      debug(\"finalizeDomMutation invoked.\");\n      if (!getState(element)) {\n        debug(\"Aborting because element has been uninstalled\");\n        return;\n      }\n      var style = getState(element).style;\n      storeCurrentSize(element, style.width, style.height);\n      positionScrollbars(element, style.width, style.height);\n    }\n    function ready() {\n      callback(element);\n    }\n    function install() {\n      debug(\"Installing...\");\n      initListeners();\n      storeStartSize();\n      batchProcessor.add(0, storeStyle);\n      batchProcessor.add(1, injectScrollElements);\n      batchProcessor.add(2, registerListenersAndPositionElements);\n      batchProcessor.add(3, finalizeDomMutation);\n      batchProcessor.add(4, ready);\n    }\n    debug(\"Making detectable...\");\n    if (isDetached(element)) {\n      debug(\"Element is detached\");\n      injectContainerElement();\n      debug(\"Waiting until element is attached...\");\n      getState(element).onRendered = function () {\n        debug(\"Element is now attached\");\n        install();\n      };\n    } else {\n      install();\n    }\n  }\n  function uninstall(element) {\n    var state = getState(element);\n    if (!state) {\n      // Uninstall has been called on a non-erd element.\n      return;\n    }\n\n    // Uninstall may have been called in the following scenarios:\n    // (1) Right between the sync code and async batch (here state.busy = true, but nothing have been registered or injected).\n    // (2) In the ready callback of the last level of the batch by another element (here, state.busy = true, but all the stuff has been injected).\n    // (3) After the installation process (here, state.busy = false and all the stuff has been injected).\n    // So to be on the safe side, let's check for each thing before removing.\n\n    // We need to remove the event listeners, because otherwise the event might fire on an uninstall element which results in an error when trying to get the state of the element.\n    state.onExpandScroll && removeEvent(getExpandElement(element), \"scroll\", state.onExpandScroll);\n    state.onShrinkScroll && removeEvent(getShrinkElement(element), \"scroll\", state.onShrinkScroll);\n    state.onAnimationStart && removeEvent(state.container, \"animationstart\", state.onAnimationStart);\n    state.container && element.removeChild(state.container);\n  }\n  return {\n    makeDetectable: makeDetectable,\n    addListener: addListener,\n    uninstall: uninstall,\n    initDocument: initDocument\n  };\n};", "map": {"version": 3, "names": ["for<PERSON>ach", "require", "module", "exports", "options", "reporter", "batchProcessor", "getState", "state<PERSON><PERSON><PERSON>", "hasState", "id<PERSON><PERSON><PERSON>", "Error", "scrollbarSizes", "getScrollbarSizes", "styleId", "detectionContainerClass", "initDocument", "targetDocument", "injectScrollStyle", "window", "document", "buildCssTextString", "rules", "seperator", "important", "join", "trim", "width", "height", "child", "createElement", "style", "cssText", "container", "append<PERSON><PERSON><PERSON>", "body", "insertBefore", "<PERSON><PERSON><PERSON><PERSON>", "widthSize", "clientWidth", "heightSize", "clientHeight", "<PERSON><PERSON><PERSON><PERSON>", "containerClass", "injectStyle", "method", "element", "head", "styleElement", "innerHTML", "id", "getElementById", "containerAnimationClass", "containerAnimationActiveClass", "addAnimationClass", "className", "addEvent", "el", "name", "cb", "addEventListener", "attachEvent", "error", "removeEvent", "removeEventListener", "detachEvent", "getExpandElement", "childNodes", "getShrinkElement", "addListener", "listener", "listeners", "push", "makeDetectable", "callback", "debug", "args", "Array", "prototype", "slice", "call", "arguments", "unshift", "get", "log", "apply", "i", "length", "isDetached", "isInDocument", "isInShadowRoot", "getRootNode", "contains", "ownerDocument", "getComputedStyle", "isUnrendered", "indexOf", "getStyle", "elementStyle", "position", "offsetWidth", "offsetHeight", "top", "right", "bottom", "left", "widthCSS", "heightCSS", "storeStartSize", "startSize", "initListeners", "storeStyle", "storeCurrentSize", "lastWidth", "lastHeight", "getExpandChildElement", "getWidthOffset", "getHeightOffset", "getExpandWidth", "getExpandHeight", "getShrinkWidth", "getShrinkHeight", "positionScrollbars", "expand", "shrink", "expandWidth", "expandHeight", "shrinkWidth", "shrinkHeight", "scrollLeft", "scrollTop", "injectContainerElement", "onAnimationStart", "onRendered", "injectScrollElements", "alterPositionStyles", "setProperty", "removeRelativeStyles", "property", "getNumericalValue", "value", "replace", "warn", "getLeftTopBottomRightCssText", "rootContainer", "scrollbarWidth", "scrollbarHeight", "containerContainerStyle", "containerStyle", "concat", "expandStyle", "shrinkStyle", "expandChildStyle", "shrinkChildStyle", "containerContainer", "expand<PERSON><PERSON>d", "<PERSON><PERSON><PERSON>d", "dir", "onExpandScroll", "state", "onExpand", "onShrinkScroll", "onShrink", "registerListenersAndPositionElements", "updateChildSizes", "updateDetectorElements", "done", "sizeChanged", "add", "performUpdateChildSizes", "areElementsInjected", "w", "h", "updateScrollbars", "notifyListenersIfNeeded", "isFirstNotify", "lastNotifiedWidth", "undefined", "lastNotifiedHeight", "handleRender", "handleScroll", "finalizeDomMutation", "ready", "install", "uninstall"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/node_modules/.pnpm/element-resize-detector@1.2.4/node_modules/element-resize-detector/src/detection-strategy/scroll.js"], "sourcesContent": ["/**\n * Resize detection strategy that injects divs to elements in order to detect resize events on scroll events.\n * Heavily inspired by: https://github.com/marcj/css-element-queries/blob/master/src/ResizeSensor.js\n */\n\n\"use strict\";\n\nvar forEach = require(\"../collection-utils\").forEach;\n\nmodule.exports = function(options) {\n    options             = options || {};\n    var reporter        = options.reporter;\n    var batchProcessor  = options.batchProcessor;\n    var getState        = options.stateHandler.getState;\n    var hasState        = options.stateHandler.hasState;\n    var idHandler       = options.idHandler;\n\n    if (!batchProcessor) {\n        throw new Error(\"Missing required dependency: batchProcessor\");\n    }\n\n    if (!reporter) {\n        throw new Error(\"Missing required dependency: reporter.\");\n    }\n\n    //TODO: Could this perhaps be done at installation time?\n    var scrollbarSizes = getScrollbarSizes();\n\n    var styleId = \"erd_scroll_detection_scrollbar_style\";\n    var detectionContainerClass = \"erd_scroll_detection_container\";\n\n    function initDocument(targetDocument) {\n        // Inject the scrollbar styling that prevents them from appearing sometimes in Chrome.\n        // The injected container needs to have a class, so that it may be styled with CSS (pseudo elements).\n        injectScrollStyle(targetDocument, styleId, detectionContainerClass);\n    }\n\n    initDocument(window.document);\n\n    function buildCssTextString(rules) {\n        var seperator = options.important ? \" !important; \" : \"; \";\n\n        return (rules.join(seperator) + seperator).trim();\n    }\n\n    function getScrollbarSizes() {\n        var width = 500;\n        var height = 500;\n\n        var child = document.createElement(\"div\");\n        child.style.cssText = buildCssTextString([\"position: absolute\", \"width: \" + width*2 + \"px\", \"height: \" + height*2 + \"px\", \"visibility: hidden\", \"margin: 0\", \"padding: 0\"]);\n\n        var container = document.createElement(\"div\");\n        container.style.cssText = buildCssTextString([\"position: absolute\", \"width: \" + width + \"px\", \"height: \" + height + \"px\", \"overflow: scroll\", \"visibility: none\", \"top: \" + -width*3 + \"px\", \"left: \" + -height*3 + \"px\", \"visibility: hidden\", \"margin: 0\", \"padding: 0\"]);\n\n        container.appendChild(child);\n\n        document.body.insertBefore(container, document.body.firstChild);\n\n        var widthSize = width - container.clientWidth;\n        var heightSize = height - container.clientHeight;\n\n        document.body.removeChild(container);\n\n        return {\n            width: widthSize,\n            height: heightSize\n        };\n    }\n\n    function injectScrollStyle(targetDocument, styleId, containerClass) {\n        function injectStyle(style, method) {\n            method = method || function (element) {\n                targetDocument.head.appendChild(element);\n            };\n\n            var styleElement = targetDocument.createElement(\"style\");\n            styleElement.innerHTML = style;\n            styleElement.id = styleId;\n            method(styleElement);\n            return styleElement;\n        }\n\n        if (!targetDocument.getElementById(styleId)) {\n            var containerAnimationClass = containerClass + \"_animation\";\n            var containerAnimationActiveClass = containerClass + \"_animation_active\";\n            var style = \"/* Created by the element-resize-detector library. */\\n\";\n            style += \".\" + containerClass + \" > div::-webkit-scrollbar { \" + buildCssTextString([\"display: none\"]) + \" }\\n\\n\";\n            style += \".\" + containerAnimationActiveClass + \" { \" + buildCssTextString([\"-webkit-animation-duration: 0.1s\", \"animation-duration: 0.1s\", \"-webkit-animation-name: \" + containerAnimationClass, \"animation-name: \" + containerAnimationClass]) + \" }\\n\";\n            style += \"@-webkit-keyframes \" + containerAnimationClass +  \" { 0% { opacity: 1; } 50% { opacity: 0; } 100% { opacity: 1; } }\\n\";\n            style += \"@keyframes \" + containerAnimationClass +          \" { 0% { opacity: 1; } 50% { opacity: 0; } 100% { opacity: 1; } }\";\n            injectStyle(style);\n        }\n    }\n\n    function addAnimationClass(element) {\n        element.className += \" \" + detectionContainerClass + \"_animation_active\";\n    }\n\n    function addEvent(el, name, cb) {\n        if (el.addEventListener) {\n            el.addEventListener(name, cb);\n        } else if(el.attachEvent) {\n            el.attachEvent(\"on\" + name, cb);\n        } else {\n            return reporter.error(\"[scroll] Don't know how to add event listeners.\");\n        }\n    }\n\n    function removeEvent(el, name, cb) {\n        if (el.removeEventListener) {\n            el.removeEventListener(name, cb);\n        } else if(el.detachEvent) {\n            el.detachEvent(\"on\" + name, cb);\n        } else {\n            return reporter.error(\"[scroll] Don't know how to remove event listeners.\");\n        }\n    }\n\n    function getExpandElement(element) {\n        return getState(element).container.childNodes[0].childNodes[0].childNodes[0];\n    }\n\n    function getShrinkElement(element) {\n        return getState(element).container.childNodes[0].childNodes[0].childNodes[1];\n    }\n\n    /**\n     * Adds a resize event listener to the element.\n     * @public\n     * @param {element} element The element that should have the listener added.\n     * @param {function} listener The listener callback to be called for each resize event of the element. The element will be given as a parameter to the listener callback.\n     */\n    function addListener(element, listener) {\n        var listeners = getState(element).listeners;\n\n        if (!listeners.push) {\n            throw new Error(\"Cannot add listener to an element that is not detectable.\");\n        }\n\n        getState(element).listeners.push(listener);\n    }\n\n    /**\n     * Makes an element detectable and ready to be listened for resize events. Will call the callback when the element is ready to be listened for resize changes.\n     * @private\n     * @param {object} options Optional options object.\n     * @param {element} element The element to make detectable\n     * @param {function} callback The callback to be called when the element is ready to be listened for resize changes. Will be called with the element as first parameter.\n     */\n    function makeDetectable(options, element, callback) {\n        if (!callback) {\n            callback = element;\n            element = options;\n            options = null;\n        }\n\n        options = options || {};\n\n        function debug() {\n            if (options.debug) {\n                var args = Array.prototype.slice.call(arguments);\n                args.unshift(idHandler.get(element), \"Scroll: \");\n                if (reporter.log.apply) {\n                    reporter.log.apply(null, args);\n                } else {\n                    for (var i = 0; i < args.length; i++) {\n                        reporter.log(args[i]);\n                    }\n                }\n            }\n        }\n\n        function isDetached(element) {\n            function isInDocument(element) {\n                var isInShadowRoot = element.getRootNode && element.getRootNode().contains(element);\n                return element === element.ownerDocument.body || element.ownerDocument.body.contains(element) || isInShadowRoot;\n            }\n\n            if (!isInDocument(element)) {\n                return true;\n            }\n\n            // FireFox returns null style in hidden iframes. See https://github.com/wnr/element-resize-detector/issues/68 and https://bugzilla.mozilla.org/show_bug.cgi?id=795520\n            if (window.getComputedStyle(element) === null) {\n                return true;\n            }\n\n            return false;\n        }\n\n        function isUnrendered(element) {\n            // Check the absolute positioned container since the top level container is display: inline.\n            var container = getState(element).container.childNodes[0];\n            var style = window.getComputedStyle(container);\n            return !style.width || style.width.indexOf(\"px\") === -1; //Can only compute pixel value when rendered.\n        }\n\n        function getStyle() {\n            // Some browsers only force layouts when actually reading the style properties of the style object, so make sure that they are all read here,\n            // so that the user of the function can be sure that it will perform the layout here, instead of later (important for batching).\n            var elementStyle            = window.getComputedStyle(element);\n            var style                   = {};\n            style.position              = elementStyle.position;\n            style.width                 = element.offsetWidth;\n            style.height                = element.offsetHeight;\n            style.top                   = elementStyle.top;\n            style.right                 = elementStyle.right;\n            style.bottom                = elementStyle.bottom;\n            style.left                  = elementStyle.left;\n            style.widthCSS              = elementStyle.width;\n            style.heightCSS             = elementStyle.height;\n            return style;\n        }\n\n        function storeStartSize() {\n            var style = getStyle();\n            getState(element).startSize = {\n                width: style.width,\n                height: style.height\n            };\n            debug(\"Element start size\", getState(element).startSize);\n        }\n\n        function initListeners() {\n            getState(element).listeners = [];\n        }\n\n        function storeStyle() {\n            debug(\"storeStyle invoked.\");\n            if (!getState(element)) {\n                debug(\"Aborting because element has been uninstalled\");\n                return;\n            }\n\n            var style = getStyle();\n            getState(element).style = style;\n        }\n\n        function storeCurrentSize(element, width, height) {\n            getState(element).lastWidth = width;\n            getState(element).lastHeight  = height;\n        }\n\n        function getExpandChildElement(element) {\n            return getExpandElement(element).childNodes[0];\n        }\n\n        function getWidthOffset() {\n            return 2 * scrollbarSizes.width + 1;\n        }\n\n        function getHeightOffset() {\n            return 2 * scrollbarSizes.height + 1;\n        }\n\n        function getExpandWidth(width) {\n            return width + 10 + getWidthOffset();\n        }\n\n        function getExpandHeight(height) {\n            return height + 10 + getHeightOffset();\n        }\n\n        function getShrinkWidth(width) {\n            return width * 2 + getWidthOffset();\n        }\n\n        function getShrinkHeight(height) {\n            return height * 2 + getHeightOffset();\n        }\n\n        function positionScrollbars(element, width, height) {\n            var expand          = getExpandElement(element);\n            var shrink          = getShrinkElement(element);\n            var expandWidth     = getExpandWidth(width);\n            var expandHeight    = getExpandHeight(height);\n            var shrinkWidth     = getShrinkWidth(width);\n            var shrinkHeight    = getShrinkHeight(height);\n            expand.scrollLeft   = expandWidth;\n            expand.scrollTop    = expandHeight;\n            shrink.scrollLeft   = shrinkWidth;\n            shrink.scrollTop    = shrinkHeight;\n        }\n\n        function injectContainerElement() {\n            var container = getState(element).container;\n\n            if (!container) {\n                container                   = document.createElement(\"div\");\n                container.className         = detectionContainerClass;\n                container.style.cssText     = buildCssTextString([\"visibility: hidden\", \"display: inline\", \"width: 0px\", \"height: 0px\", \"z-index: -1\", \"overflow: hidden\", \"margin: 0\", \"padding: 0\"]);\n                getState(element).container = container;\n                addAnimationClass(container);\n                element.appendChild(container);\n\n                var onAnimationStart = function () {\n                    getState(element).onRendered && getState(element).onRendered();\n                };\n\n                addEvent(container, \"animationstart\", onAnimationStart);\n\n                // Store the event handler here so that they may be removed when uninstall is called.\n                // See uninstall function for an explanation why it is needed.\n                getState(element).onAnimationStart = onAnimationStart;\n            }\n\n            return container;\n        }\n\n        function injectScrollElements() {\n            function alterPositionStyles() {\n                var style = getState(element).style;\n\n                if(style.position === \"static\") {\n                    element.style.setProperty(\"position\", \"relative\",options.important ? \"important\" : \"\");\n\n                    var removeRelativeStyles = function(reporter, element, style, property) {\n                        function getNumericalValue(value) {\n                            return value.replace(/[^-\\d\\.]/g, \"\");\n                        }\n\n                        var value = style[property];\n\n                        if(value !== \"auto\" && getNumericalValue(value) !== \"0\") {\n                            reporter.warn(\"An element that is positioned static has style.\" + property + \"=\" + value + \" which is ignored due to the static positioning. The element will need to be positioned relative, so the style.\" + property + \" will be set to 0. Element: \", element);\n                            element.style[property] = 0;\n                        }\n                    };\n\n                    //Check so that there are no accidental styles that will make the element styled differently now that is is relative.\n                    //If there are any, set them to 0 (this should be okay with the user since the style properties did nothing before [since the element was positioned static] anyway).\n                    removeRelativeStyles(reporter, element, style, \"top\");\n                    removeRelativeStyles(reporter, element, style, \"right\");\n                    removeRelativeStyles(reporter, element, style, \"bottom\");\n                    removeRelativeStyles(reporter, element, style, \"left\");\n                }\n            }\n\n            function getLeftTopBottomRightCssText(left, top, bottom, right) {\n                left = (!left ? \"0\" : (left + \"px\"));\n                top = (!top ? \"0\" : (top + \"px\"));\n                bottom = (!bottom ? \"0\" : (bottom + \"px\"));\n                right = (!right ? \"0\" : (right + \"px\"));\n\n                return [\"left: \" + left, \"top: \" + top, \"right: \" + right, \"bottom: \" + bottom];\n            }\n\n            debug(\"Injecting elements\");\n\n            if (!getState(element)) {\n                debug(\"Aborting because element has been uninstalled\");\n                return;\n            }\n\n            alterPositionStyles();\n\n            var rootContainer = getState(element).container;\n\n            if (!rootContainer) {\n                rootContainer = injectContainerElement();\n            }\n\n            // Due to this WebKit bug https://bugs.webkit.org/show_bug.cgi?id=80808 (currently fixed in Blink, but still present in WebKit browsers such as Safari),\n            // we need to inject two containers, one that is width/height 100% and another that is left/top -1px so that the final container always is 1x1 pixels bigger than\n            // the targeted element.\n            // When the bug is resolved, \"containerContainer\" may be removed.\n\n            // The outer container can occasionally be less wide than the targeted when inside inline elements element in WebKit (see https://bugs.webkit.org/show_bug.cgi?id=152980).\n            // This should be no problem since the inner container either way makes sure the injected scroll elements are at least 1x1 px.\n\n            var scrollbarWidth          = scrollbarSizes.width;\n            var scrollbarHeight         = scrollbarSizes.height;\n            var containerContainerStyle = buildCssTextString([\"position: absolute\", \"flex: none\", \"overflow: hidden\", \"z-index: -1\", \"visibility: hidden\", \"width: 100%\", \"height: 100%\", \"left: 0px\", \"top: 0px\"]);\n            var containerStyle          = buildCssTextString([\"position: absolute\", \"flex: none\", \"overflow: hidden\", \"z-index: -1\", \"visibility: hidden\"].concat(getLeftTopBottomRightCssText(-(1 + scrollbarWidth), -(1 + scrollbarHeight), -scrollbarHeight, -scrollbarWidth)));\n            var expandStyle             = buildCssTextString([\"position: absolute\", \"flex: none\", \"overflow: scroll\", \"z-index: -1\", \"visibility: hidden\", \"width: 100%\", \"height: 100%\"]);\n            var shrinkStyle             = buildCssTextString([\"position: absolute\", \"flex: none\", \"overflow: scroll\", \"z-index: -1\", \"visibility: hidden\", \"width: 100%\", \"height: 100%\"]);\n            var expandChildStyle        = buildCssTextString([\"position: absolute\", \"left: 0\", \"top: 0\"]);\n            var shrinkChildStyle        = buildCssTextString([\"position: absolute\", \"width: 200%\", \"height: 200%\"]);\n\n            var containerContainer      = document.createElement(\"div\");\n            var container               = document.createElement(\"div\");\n            var expand                  = document.createElement(\"div\");\n            var expandChild             = document.createElement(\"div\");\n            var shrink                  = document.createElement(\"div\");\n            var shrinkChild             = document.createElement(\"div\");\n\n            // Some browsers choke on the resize system being rtl, so force it to ltr. https://github.com/wnr/element-resize-detector/issues/56\n            // However, dir should not be set on the top level container as it alters the dimensions of the target element in some browsers.\n            containerContainer.dir              = \"ltr\";\n\n            containerContainer.style.cssText    = containerContainerStyle;\n            containerContainer.className        = detectionContainerClass;\n            container.className                 = detectionContainerClass;\n            container.style.cssText             = containerStyle;\n            expand.style.cssText                = expandStyle;\n            expandChild.style.cssText           = expandChildStyle;\n            shrink.style.cssText                = shrinkStyle;\n            shrinkChild.style.cssText           = shrinkChildStyle;\n\n            expand.appendChild(expandChild);\n            shrink.appendChild(shrinkChild);\n            container.appendChild(expand);\n            container.appendChild(shrink);\n            containerContainer.appendChild(container);\n            rootContainer.appendChild(containerContainer);\n\n            function onExpandScroll() {\n                var state = getState(element);\n                if (state && state.onExpand) {\n                    state.onExpand();\n                } else {\n                    debug(\"Aborting expand scroll handler: element has been uninstalled\");\n                }\n            }\n\n            function onShrinkScroll() {\n                var state = getState(element);\n                if (state && state.onShrink) {\n                    state.onShrink();\n                } else {\n                    debug(\"Aborting shrink scroll handler: element has been uninstalled\");\n                }\n            }\n\n            addEvent(expand, \"scroll\", onExpandScroll);\n            addEvent(shrink, \"scroll\", onShrinkScroll);\n\n            // Store the event handlers here so that they may be removed when uninstall is called.\n            // See uninstall function for an explanation why it is needed.\n            getState(element).onExpandScroll = onExpandScroll;\n            getState(element).onShrinkScroll = onShrinkScroll;\n        }\n\n        function registerListenersAndPositionElements() {\n            function updateChildSizes(element, width, height) {\n                var expandChild             = getExpandChildElement(element);\n                var expandWidth             = getExpandWidth(width);\n                var expandHeight            = getExpandHeight(height);\n                expandChild.style.setProperty(\"width\", expandWidth + \"px\", options.important ? \"important\" : \"\");\n                expandChild.style.setProperty(\"height\", expandHeight + \"px\", options.important ? \"important\" : \"\");\n            }\n\n            function updateDetectorElements(done) {\n                var width           = element.offsetWidth;\n                var height          = element.offsetHeight;\n\n                // Check whether the size has actually changed since last time the algorithm ran. If not, some steps may be skipped.\n                var sizeChanged = width !== getState(element).lastWidth || height !== getState(element).lastHeight;\n\n                debug(\"Storing current size\", width, height);\n\n                // Store the size of the element sync here, so that multiple scroll events may be ignored in the event listeners.\n                // Otherwise the if-check in handleScroll is useless.\n                storeCurrentSize(element, width, height);\n\n                // Since we delay the processing of the batch, there is a risk that uninstall has been called before the batch gets to execute.\n                // Since there is no way to cancel the fn executions, we need to add an uninstall guard to all fns of the batch.\n\n                batchProcessor.add(0, function performUpdateChildSizes() {\n                    if (!sizeChanged) {\n                        return;\n                    }\n\n                    if (!getState(element)) {\n                        debug(\"Aborting because element has been uninstalled\");\n                        return;\n                    }\n\n                    if (!areElementsInjected()) {\n                        debug(\"Aborting because element container has not been initialized\");\n                        return;\n                    }\n\n                    if (options.debug) {\n                        var w = element.offsetWidth;\n                        var h = element.offsetHeight;\n\n                        if (w !== width || h !== height) {\n                            reporter.warn(idHandler.get(element), \"Scroll: Size changed before updating detector elements.\");\n                        }\n                    }\n\n                    updateChildSizes(element, width, height);\n                });\n\n                batchProcessor.add(1, function updateScrollbars() {\n                    // This function needs to be invoked event though the size is unchanged. The element could have been resized very quickly and then\n                    // been restored to the original size, which will have changed the scrollbar positions.\n\n                    if (!getState(element)) {\n                        debug(\"Aborting because element has been uninstalled\");\n                        return;\n                    }\n\n                    if (!areElementsInjected()) {\n                        debug(\"Aborting because element container has not been initialized\");\n                        return;\n                    }\n\n                    positionScrollbars(element, width, height);\n                });\n\n                if (sizeChanged && done) {\n                    batchProcessor.add(2, function () {\n                        if (!getState(element)) {\n                            debug(\"Aborting because element has been uninstalled\");\n                            return;\n                        }\n\n                        if (!areElementsInjected()) {\n                          debug(\"Aborting because element container has not been initialized\");\n                          return;\n                        }\n\n                        done();\n                    });\n                }\n            }\n\n            function areElementsInjected() {\n                return !!getState(element).container;\n            }\n\n            function notifyListenersIfNeeded() {\n                function isFirstNotify() {\n                    return getState(element).lastNotifiedWidth === undefined;\n                }\n\n                debug(\"notifyListenersIfNeeded invoked\");\n\n                var state = getState(element);\n\n                // Don't notify if the current size is the start size, and this is the first notification.\n                if (isFirstNotify() && state.lastWidth === state.startSize.width && state.lastHeight === state.startSize.height) {\n                    return debug(\"Not notifying: Size is the same as the start size, and there has been no notification yet.\");\n                }\n\n                // Don't notify if the size already has been notified.\n                if (state.lastWidth === state.lastNotifiedWidth && state.lastHeight === state.lastNotifiedHeight) {\n                    return debug(\"Not notifying: Size already notified\");\n                }\n\n\n                debug(\"Current size not notified, notifying...\");\n                state.lastNotifiedWidth = state.lastWidth;\n                state.lastNotifiedHeight = state.lastHeight;\n                forEach(getState(element).listeners, function (listener) {\n                    listener(element);\n                });\n            }\n\n            function handleRender() {\n                debug(\"startanimation triggered.\");\n\n                if (isUnrendered(element)) {\n                    debug(\"Ignoring since element is still unrendered...\");\n                    return;\n                }\n\n                debug(\"Element rendered.\");\n                var expand = getExpandElement(element);\n                var shrink = getShrinkElement(element);\n                if (expand.scrollLeft === 0 || expand.scrollTop === 0 || shrink.scrollLeft === 0 || shrink.scrollTop === 0) {\n                    debug(\"Scrollbars out of sync. Updating detector elements...\");\n                    updateDetectorElements(notifyListenersIfNeeded);\n                }\n            }\n\n            function handleScroll() {\n                debug(\"Scroll detected.\");\n\n                if (isUnrendered(element)) {\n                    // Element is still unrendered. Skip this scroll event.\n                    debug(\"Scroll event fired while unrendered. Ignoring...\");\n                    return;\n                }\n\n                updateDetectorElements(notifyListenersIfNeeded);\n            }\n\n            debug(\"registerListenersAndPositionElements invoked.\");\n\n            if (!getState(element)) {\n                debug(\"Aborting because element has been uninstalled\");\n                return;\n            }\n\n            getState(element).onRendered = handleRender;\n            getState(element).onExpand = handleScroll;\n            getState(element).onShrink = handleScroll;\n\n            var style = getState(element).style;\n            updateChildSizes(element, style.width, style.height);\n        }\n\n        function finalizeDomMutation() {\n            debug(\"finalizeDomMutation invoked.\");\n\n            if (!getState(element)) {\n                debug(\"Aborting because element has been uninstalled\");\n                return;\n            }\n\n            var style = getState(element).style;\n            storeCurrentSize(element, style.width, style.height);\n            positionScrollbars(element, style.width, style.height);\n        }\n\n        function ready() {\n            callback(element);\n        }\n\n        function install() {\n            debug(\"Installing...\");\n            initListeners();\n            storeStartSize();\n\n            batchProcessor.add(0, storeStyle);\n            batchProcessor.add(1, injectScrollElements);\n            batchProcessor.add(2, registerListenersAndPositionElements);\n            batchProcessor.add(3, finalizeDomMutation);\n            batchProcessor.add(4, ready);\n        }\n\n        debug(\"Making detectable...\");\n\n        if (isDetached(element)) {\n            debug(\"Element is detached\");\n\n            injectContainerElement();\n\n            debug(\"Waiting until element is attached...\");\n\n            getState(element).onRendered = function () {\n                debug(\"Element is now attached\");\n                install();\n            };\n        } else {\n            install();\n        }\n    }\n\n    function uninstall(element) {\n        var state = getState(element);\n\n        if (!state) {\n            // Uninstall has been called on a non-erd element.\n            return;\n        }\n\n        // Uninstall may have been called in the following scenarios:\n        // (1) Right between the sync code and async batch (here state.busy = true, but nothing have been registered or injected).\n        // (2) In the ready callback of the last level of the batch by another element (here, state.busy = true, but all the stuff has been injected).\n        // (3) After the installation process (here, state.busy = false and all the stuff has been injected).\n        // So to be on the safe side, let's check for each thing before removing.\n\n        // We need to remove the event listeners, because otherwise the event might fire on an uninstall element which results in an error when trying to get the state of the element.\n        state.onExpandScroll && removeEvent(getExpandElement(element), \"scroll\", state.onExpandScroll);\n        state.onShrinkScroll && removeEvent(getShrinkElement(element), \"scroll\", state.onShrinkScroll);\n        state.onAnimationStart && removeEvent(state.container, \"animationstart\", state.onAnimationStart);\n\n        state.container && element.removeChild(state.container);\n    }\n\n    return {\n        makeDetectable: makeDetectable,\n        addListener: addListener,\n        uninstall: uninstall,\n        initDocument: initDocument\n    };\n};\n"], "mappings": "AAAA;AACA;AACA;AACA;;AAEA,YAAY;;AAEZ,IAAIA,OAAO,GAAGC,OAAO,CAAC,qBAAqB,CAAC,CAACD,OAAO;AAEpDE,MAAM,CAACC,OAAO,GAAG,UAASC,OAAO,EAAE;EAC/BA,OAAO,GAAeA,OAAO,IAAI,CAAC,CAAC;EACnC,IAAIC,QAAQ,GAAUD,OAAO,CAACC,QAAQ;EACtC,IAAIC,cAAc,GAAIF,OAAO,CAACE,cAAc;EAC5C,IAAIC,QAAQ,GAAUH,OAAO,CAACI,YAAY,CAACD,QAAQ;EACnD,IAAIE,QAAQ,GAAUL,OAAO,CAACI,YAAY,CAACC,QAAQ;EACnD,IAAIC,SAAS,GAASN,OAAO,CAACM,SAAS;EAEvC,IAAI,CAACJ,cAAc,EAAE;IACjB,MAAM,IAAIK,KAAK,CAAC,6CAA6C,CAAC;EAClE;EAEA,IAAI,CAACN,QAAQ,EAAE;IACX,MAAM,IAAIM,KAAK,CAAC,wCAAwC,CAAC;EAC7D;;EAEA;EACA,IAAIC,cAAc,GAAGC,iBAAiB,CAAC,CAAC;EAExC,IAAIC,OAAO,GAAG,sCAAsC;EACpD,IAAIC,uBAAuB,GAAG,gCAAgC;EAE9D,SAASC,YAAYA,CAACC,cAAc,EAAE;IAClC;IACA;IACAC,iBAAiB,CAACD,cAAc,EAAEH,OAAO,EAAEC,uBAAuB,CAAC;EACvE;EAEAC,YAAY,CAACG,MAAM,CAACC,QAAQ,CAAC;EAE7B,SAASC,kBAAkBA,CAACC,KAAK,EAAE;IAC/B,IAAIC,SAAS,GAAGnB,OAAO,CAACoB,SAAS,GAAG,eAAe,GAAG,IAAI;IAE1D,OAAO,CAACF,KAAK,CAACG,IAAI,CAACF,SAAS,CAAC,GAAGA,SAAS,EAAEG,IAAI,CAAC,CAAC;EACrD;EAEA,SAASb,iBAAiBA,CAAA,EAAG;IACzB,IAAIc,KAAK,GAAG,GAAG;IACf,IAAIC,MAAM,GAAG,GAAG;IAEhB,IAAIC,KAAK,GAAGT,QAAQ,CAACU,aAAa,CAAC,KAAK,CAAC;IACzCD,KAAK,CAACE,KAAK,CAACC,OAAO,GAAGX,kBAAkB,CAAC,CAAC,oBAAoB,EAAE,SAAS,GAAGM,KAAK,GAAC,CAAC,GAAG,IAAI,EAAE,UAAU,GAAGC,MAAM,GAAC,CAAC,GAAG,IAAI,EAAE,oBAAoB,EAAE,WAAW,EAAE,YAAY,CAAC,CAAC;IAE3K,IAAIK,SAAS,GAAGb,QAAQ,CAACU,aAAa,CAAC,KAAK,CAAC;IAC7CG,SAAS,CAACF,KAAK,CAACC,OAAO,GAAGX,kBAAkB,CAAC,CAAC,oBAAoB,EAAE,SAAS,GAAGM,KAAK,GAAG,IAAI,EAAE,UAAU,GAAGC,MAAM,GAAG,IAAI,EAAE,kBAAkB,EAAE,kBAAkB,EAAE,OAAO,GAAG,CAACD,KAAK,GAAC,CAAC,GAAG,IAAI,EAAE,QAAQ,GAAG,CAACC,MAAM,GAAC,CAAC,GAAG,IAAI,EAAE,oBAAoB,EAAE,WAAW,EAAE,YAAY,CAAC,CAAC;IAE3QK,SAAS,CAACC,WAAW,CAACL,KAAK,CAAC;IAE5BT,QAAQ,CAACe,IAAI,CAACC,YAAY,CAACH,SAAS,EAAEb,QAAQ,CAACe,IAAI,CAACE,UAAU,CAAC;IAE/D,IAAIC,SAAS,GAAGX,KAAK,GAAGM,SAAS,CAACM,WAAW;IAC7C,IAAIC,UAAU,GAAGZ,MAAM,GAAGK,SAAS,CAACQ,YAAY;IAEhDrB,QAAQ,CAACe,IAAI,CAACO,WAAW,CAACT,SAAS,CAAC;IAEpC,OAAO;MACHN,KAAK,EAAEW,SAAS;MAChBV,MAAM,EAAEY;IACZ,CAAC;EACL;EAEA,SAAStB,iBAAiBA,CAACD,cAAc,EAAEH,OAAO,EAAE6B,cAAc,EAAE;IAChE,SAASC,WAAWA,CAACb,KAAK,EAAEc,MAAM,EAAE;MAChCA,MAAM,GAAGA,MAAM,IAAI,UAAUC,OAAO,EAAE;QAClC7B,cAAc,CAAC8B,IAAI,CAACb,WAAW,CAACY,OAAO,CAAC;MAC5C,CAAC;MAED,IAAIE,YAAY,GAAG/B,cAAc,CAACa,aAAa,CAAC,OAAO,CAAC;MACxDkB,YAAY,CAACC,SAAS,GAAGlB,KAAK;MAC9BiB,YAAY,CAACE,EAAE,GAAGpC,OAAO;MACzB+B,MAAM,CAACG,YAAY,CAAC;MACpB,OAAOA,YAAY;IACvB;IAEA,IAAI,CAAC/B,cAAc,CAACkC,cAAc,CAACrC,OAAO,CAAC,EAAE;MACzC,IAAIsC,uBAAuB,GAAGT,cAAc,GAAG,YAAY;MAC3D,IAAIU,6BAA6B,GAAGV,cAAc,GAAG,mBAAmB;MACxE,IAAIZ,KAAK,GAAG,yDAAyD;MACrEA,KAAK,IAAI,GAAG,GAAGY,cAAc,GAAG,8BAA8B,GAAGtB,kBAAkB,CAAC,CAAC,eAAe,CAAC,CAAC,GAAG,QAAQ;MACjHU,KAAK,IAAI,GAAG,GAAGsB,6BAA6B,GAAG,KAAK,GAAGhC,kBAAkB,CAAC,CAAC,kCAAkC,EAAE,0BAA0B,EAAE,0BAA0B,GAAG+B,uBAAuB,EAAE,kBAAkB,GAAGA,uBAAuB,CAAC,CAAC,GAAG,MAAM;MACxPrB,KAAK,IAAI,qBAAqB,GAAGqB,uBAAuB,GAAI,oEAAoE;MAChIrB,KAAK,IAAI,aAAa,GAAGqB,uBAAuB,GAAY,kEAAkE;MAC9HR,WAAW,CAACb,KAAK,CAAC;IACtB;EACJ;EAEA,SAASuB,iBAAiBA,CAACR,OAAO,EAAE;IAChCA,OAAO,CAACS,SAAS,IAAI,GAAG,GAAGxC,uBAAuB,GAAG,mBAAmB;EAC5E;EAEA,SAASyC,QAAQA,CAACC,EAAE,EAAEC,IAAI,EAAEC,EAAE,EAAE;IAC5B,IAAIF,EAAE,CAACG,gBAAgB,EAAE;MACrBH,EAAE,CAACG,gBAAgB,CAACF,IAAI,EAAEC,EAAE,CAAC;IACjC,CAAC,MAAM,IAAGF,EAAE,CAACI,WAAW,EAAE;MACtBJ,EAAE,CAACI,WAAW,CAAC,IAAI,GAAGH,IAAI,EAAEC,EAAE,CAAC;IACnC,CAAC,MAAM;MACH,OAAOtD,QAAQ,CAACyD,KAAK,CAAC,iDAAiD,CAAC;IAC5E;EACJ;EAEA,SAASC,WAAWA,CAACN,EAAE,EAAEC,IAAI,EAAEC,EAAE,EAAE;IAC/B,IAAIF,EAAE,CAACO,mBAAmB,EAAE;MACxBP,EAAE,CAACO,mBAAmB,CAACN,IAAI,EAAEC,EAAE,CAAC;IACpC,CAAC,MAAM,IAAGF,EAAE,CAACQ,WAAW,EAAE;MACtBR,EAAE,CAACQ,WAAW,CAAC,IAAI,GAAGP,IAAI,EAAEC,EAAE,CAAC;IACnC,CAAC,MAAM;MACH,OAAOtD,QAAQ,CAACyD,KAAK,CAAC,oDAAoD,CAAC;IAC/E;EACJ;EAEA,SAASI,gBAAgBA,CAACpB,OAAO,EAAE;IAC/B,OAAOvC,QAAQ,CAACuC,OAAO,CAAC,CAACb,SAAS,CAACkC,UAAU,CAAC,CAAC,CAAC,CAACA,UAAU,CAAC,CAAC,CAAC,CAACA,UAAU,CAAC,CAAC,CAAC;EAChF;EAEA,SAASC,gBAAgBA,CAACtB,OAAO,EAAE;IAC/B,OAAOvC,QAAQ,CAACuC,OAAO,CAAC,CAACb,SAAS,CAACkC,UAAU,CAAC,CAAC,CAAC,CAACA,UAAU,CAAC,CAAC,CAAC,CAACA,UAAU,CAAC,CAAC,CAAC;EAChF;;EAEA;AACJ;AACA;AACA;AACA;AACA;EACI,SAASE,WAAWA,CAACvB,OAAO,EAAEwB,QAAQ,EAAE;IACpC,IAAIC,SAAS,GAAGhE,QAAQ,CAACuC,OAAO,CAAC,CAACyB,SAAS;IAE3C,IAAI,CAACA,SAAS,CAACC,IAAI,EAAE;MACjB,MAAM,IAAI7D,KAAK,CAAC,2DAA2D,CAAC;IAChF;IAEAJ,QAAQ,CAACuC,OAAO,CAAC,CAACyB,SAAS,CAACC,IAAI,CAACF,QAAQ,CAAC;EAC9C;;EAEA;AACJ;AACA;AACA;AACA;AACA;AACA;EACI,SAASG,cAAcA,CAACrE,OAAO,EAAE0C,OAAO,EAAE4B,QAAQ,EAAE;IAChD,IAAI,CAACA,QAAQ,EAAE;MACXA,QAAQ,GAAG5B,OAAO;MAClBA,OAAO,GAAG1C,OAAO;MACjBA,OAAO,GAAG,IAAI;IAClB;IAEAA,OAAO,GAAGA,OAAO,IAAI,CAAC,CAAC;IAEvB,SAASuE,KAAKA,CAAA,EAAG;MACb,IAAIvE,OAAO,CAACuE,KAAK,EAAE;QACf,IAAIC,IAAI,GAAGC,KAAK,CAACC,SAAS,CAACC,KAAK,CAACC,IAAI,CAACC,SAAS,CAAC;QAChDL,IAAI,CAACM,OAAO,CAACxE,SAAS,CAACyE,GAAG,CAACrC,OAAO,CAAC,EAAE,UAAU,CAAC;QAChD,IAAIzC,QAAQ,CAAC+E,GAAG,CAACC,KAAK,EAAE;UACpBhF,QAAQ,CAAC+E,GAAG,CAACC,KAAK,CAAC,IAAI,EAAET,IAAI,CAAC;QAClC,CAAC,MAAM;UACH,KAAK,IAAIU,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGV,IAAI,CAACW,MAAM,EAAED,CAAC,EAAE,EAAE;YAClCjF,QAAQ,CAAC+E,GAAG,CAACR,IAAI,CAACU,CAAC,CAAC,CAAC;UACzB;QACJ;MACJ;IACJ;IAEA,SAASE,UAAUA,CAAC1C,OAAO,EAAE;MACzB,SAAS2C,YAAYA,CAAC3C,OAAO,EAAE;QAC3B,IAAI4C,cAAc,GAAG5C,OAAO,CAAC6C,WAAW,IAAI7C,OAAO,CAAC6C,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC9C,OAAO,CAAC;QACnF,OAAOA,OAAO,KAAKA,OAAO,CAAC+C,aAAa,CAAC1D,IAAI,IAAIW,OAAO,CAAC+C,aAAa,CAAC1D,IAAI,CAACyD,QAAQ,CAAC9C,OAAO,CAAC,IAAI4C,cAAc;MACnH;MAEA,IAAI,CAACD,YAAY,CAAC3C,OAAO,CAAC,EAAE;QACxB,OAAO,IAAI;MACf;;MAEA;MACA,IAAI3B,MAAM,CAAC2E,gBAAgB,CAAChD,OAAO,CAAC,KAAK,IAAI,EAAE;QAC3C,OAAO,IAAI;MACf;MAEA,OAAO,KAAK;IAChB;IAEA,SAASiD,YAAYA,CAACjD,OAAO,EAAE;MAC3B;MACA,IAAIb,SAAS,GAAG1B,QAAQ,CAACuC,OAAO,CAAC,CAACb,SAAS,CAACkC,UAAU,CAAC,CAAC,CAAC;MACzD,IAAIpC,KAAK,GAAGZ,MAAM,CAAC2E,gBAAgB,CAAC7D,SAAS,CAAC;MAC9C,OAAO,CAACF,KAAK,CAACJ,KAAK,IAAII,KAAK,CAACJ,KAAK,CAACqE,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;IAC7D;IAEA,SAASC,QAAQA,CAAA,EAAG;MAChB;MACA;MACA,IAAIC,YAAY,GAAc/E,MAAM,CAAC2E,gBAAgB,CAAChD,OAAO,CAAC;MAC9D,IAAIf,KAAK,GAAqB,CAAC,CAAC;MAChCA,KAAK,CAACoE,QAAQ,GAAgBD,YAAY,CAACC,QAAQ;MACnDpE,KAAK,CAACJ,KAAK,GAAmBmB,OAAO,CAACsD,WAAW;MACjDrE,KAAK,CAACH,MAAM,GAAkBkB,OAAO,CAACuD,YAAY;MAClDtE,KAAK,CAACuE,GAAG,GAAqBJ,YAAY,CAACI,GAAG;MAC9CvE,KAAK,CAACwE,KAAK,GAAmBL,YAAY,CAACK,KAAK;MAChDxE,KAAK,CAACyE,MAAM,GAAkBN,YAAY,CAACM,MAAM;MACjDzE,KAAK,CAAC0E,IAAI,GAAoBP,YAAY,CAACO,IAAI;MAC/C1E,KAAK,CAAC2E,QAAQ,GAAgBR,YAAY,CAACvE,KAAK;MAChDI,KAAK,CAAC4E,SAAS,GAAeT,YAAY,CAACtE,MAAM;MACjD,OAAOG,KAAK;IAChB;IAEA,SAAS6E,cAAcA,CAAA,EAAG;MACtB,IAAI7E,KAAK,GAAGkE,QAAQ,CAAC,CAAC;MACtB1F,QAAQ,CAACuC,OAAO,CAAC,CAAC+D,SAAS,GAAG;QAC1BlF,KAAK,EAAEI,KAAK,CAACJ,KAAK;QAClBC,MAAM,EAAEG,KAAK,CAACH;MAClB,CAAC;MACD+C,KAAK,CAAC,oBAAoB,EAAEpE,QAAQ,CAACuC,OAAO,CAAC,CAAC+D,SAAS,CAAC;IAC5D;IAEA,SAASC,aAAaA,CAAA,EAAG;MACrBvG,QAAQ,CAACuC,OAAO,CAAC,CAACyB,SAAS,GAAG,EAAE;IACpC;IAEA,SAASwC,UAAUA,CAAA,EAAG;MAClBpC,KAAK,CAAC,qBAAqB,CAAC;MAC5B,IAAI,CAACpE,QAAQ,CAACuC,OAAO,CAAC,EAAE;QACpB6B,KAAK,CAAC,+CAA+C,CAAC;QACtD;MACJ;MAEA,IAAI5C,KAAK,GAAGkE,QAAQ,CAAC,CAAC;MACtB1F,QAAQ,CAACuC,OAAO,CAAC,CAACf,KAAK,GAAGA,KAAK;IACnC;IAEA,SAASiF,gBAAgBA,CAAClE,OAAO,EAAEnB,KAAK,EAAEC,MAAM,EAAE;MAC9CrB,QAAQ,CAACuC,OAAO,CAAC,CAACmE,SAAS,GAAGtF,KAAK;MACnCpB,QAAQ,CAACuC,OAAO,CAAC,CAACoE,UAAU,GAAItF,MAAM;IAC1C;IAEA,SAASuF,qBAAqBA,CAACrE,OAAO,EAAE;MACpC,OAAOoB,gBAAgB,CAACpB,OAAO,CAAC,CAACqB,UAAU,CAAC,CAAC,CAAC;IAClD;IAEA,SAASiD,cAAcA,CAAA,EAAG;MACtB,OAAO,CAAC,GAAGxG,cAAc,CAACe,KAAK,GAAG,CAAC;IACvC;IAEA,SAAS0F,eAAeA,CAAA,EAAG;MACvB,OAAO,CAAC,GAAGzG,cAAc,CAACgB,MAAM,GAAG,CAAC;IACxC;IAEA,SAAS0F,cAAcA,CAAC3F,KAAK,EAAE;MAC3B,OAAOA,KAAK,GAAG,EAAE,GAAGyF,cAAc,CAAC,CAAC;IACxC;IAEA,SAASG,eAAeA,CAAC3F,MAAM,EAAE;MAC7B,OAAOA,MAAM,GAAG,EAAE,GAAGyF,eAAe,CAAC,CAAC;IAC1C;IAEA,SAASG,cAAcA,CAAC7F,KAAK,EAAE;MAC3B,OAAOA,KAAK,GAAG,CAAC,GAAGyF,cAAc,CAAC,CAAC;IACvC;IAEA,SAASK,eAAeA,CAAC7F,MAAM,EAAE;MAC7B,OAAOA,MAAM,GAAG,CAAC,GAAGyF,eAAe,CAAC,CAAC;IACzC;IAEA,SAASK,kBAAkBA,CAAC5E,OAAO,EAAEnB,KAAK,EAAEC,MAAM,EAAE;MAChD,IAAI+F,MAAM,GAAYzD,gBAAgB,CAACpB,OAAO,CAAC;MAC/C,IAAI8E,MAAM,GAAYxD,gBAAgB,CAACtB,OAAO,CAAC;MAC/C,IAAI+E,WAAW,GAAOP,cAAc,CAAC3F,KAAK,CAAC;MAC3C,IAAImG,YAAY,GAAMP,eAAe,CAAC3F,MAAM,CAAC;MAC7C,IAAImG,WAAW,GAAOP,cAAc,CAAC7F,KAAK,CAAC;MAC3C,IAAIqG,YAAY,GAAMP,eAAe,CAAC7F,MAAM,CAAC;MAC7C+F,MAAM,CAACM,UAAU,GAAKJ,WAAW;MACjCF,MAAM,CAACO,SAAS,GAAMJ,YAAY;MAClCF,MAAM,CAACK,UAAU,GAAKF,WAAW;MACjCH,MAAM,CAACM,SAAS,GAAMF,YAAY;IACtC;IAEA,SAASG,sBAAsBA,CAAA,EAAG;MAC9B,IAAIlG,SAAS,GAAG1B,QAAQ,CAACuC,OAAO,CAAC,CAACb,SAAS;MAE3C,IAAI,CAACA,SAAS,EAAE;QACZA,SAAS,GAAqBb,QAAQ,CAACU,aAAa,CAAC,KAAK,CAAC;QAC3DG,SAAS,CAACsB,SAAS,GAAWxC,uBAAuB;QACrDkB,SAAS,CAACF,KAAK,CAACC,OAAO,GAAOX,kBAAkB,CAAC,CAAC,oBAAoB,EAAE,iBAAiB,EAAE,YAAY,EAAE,aAAa,EAAE,aAAa,EAAE,kBAAkB,EAAE,WAAW,EAAE,YAAY,CAAC,CAAC;QACtLd,QAAQ,CAACuC,OAAO,CAAC,CAACb,SAAS,GAAGA,SAAS;QACvCqB,iBAAiB,CAACrB,SAAS,CAAC;QAC5Ba,OAAO,CAACZ,WAAW,CAACD,SAAS,CAAC;QAE9B,IAAImG,gBAAgB,GAAG,SAAnBA,gBAAgBA,CAAA,EAAe;UAC/B7H,QAAQ,CAACuC,OAAO,CAAC,CAACuF,UAAU,IAAI9H,QAAQ,CAACuC,OAAO,CAAC,CAACuF,UAAU,CAAC,CAAC;QAClE,CAAC;QAED7E,QAAQ,CAACvB,SAAS,EAAE,gBAAgB,EAAEmG,gBAAgB,CAAC;;QAEvD;QACA;QACA7H,QAAQ,CAACuC,OAAO,CAAC,CAACsF,gBAAgB,GAAGA,gBAAgB;MACzD;MAEA,OAAOnG,SAAS;IACpB;IAEA,SAASqG,oBAAoBA,CAAA,EAAG;MAC5B,SAASC,mBAAmBA,CAAA,EAAG;QAC3B,IAAIxG,KAAK,GAAGxB,QAAQ,CAACuC,OAAO,CAAC,CAACf,KAAK;QAEnC,IAAGA,KAAK,CAACoE,QAAQ,KAAK,QAAQ,EAAE;UAC5BrD,OAAO,CAACf,KAAK,CAACyG,WAAW,CAAC,UAAU,EAAE,UAAU,EAACpI,OAAO,CAACoB,SAAS,GAAG,WAAW,GAAG,EAAE,CAAC;UAEtF,IAAIiH,oBAAoB,GAAG,SAAvBA,oBAAoBA,CAAYpI,QAAQ,EAAEyC,OAAO,EAAEf,KAAK,EAAE2G,QAAQ,EAAE;YACpE,SAASC,iBAAiBA,CAACC,KAAK,EAAE;cAC9B,OAAOA,KAAK,CAACC,OAAO,CAAC,WAAW,EAAE,EAAE,CAAC;YACzC;YAEA,IAAID,KAAK,GAAG7G,KAAK,CAAC2G,QAAQ,CAAC;YAE3B,IAAGE,KAAK,KAAK,MAAM,IAAID,iBAAiB,CAACC,KAAK,CAAC,KAAK,GAAG,EAAE;cACrDvI,QAAQ,CAACyI,IAAI,CAAC,iDAAiD,GAAGJ,QAAQ,GAAG,GAAG,GAAGE,KAAK,GAAG,iHAAiH,GAAGF,QAAQ,GAAG,8BAA8B,EAAE5F,OAAO,CAAC;cAClQA,OAAO,CAACf,KAAK,CAAC2G,QAAQ,CAAC,GAAG,CAAC;YAC/B;UACJ,CAAC;;UAED;UACA;UACAD,oBAAoB,CAACpI,QAAQ,EAAEyC,OAAO,EAAEf,KAAK,EAAE,KAAK,CAAC;UACrD0G,oBAAoB,CAACpI,QAAQ,EAAEyC,OAAO,EAAEf,KAAK,EAAE,OAAO,CAAC;UACvD0G,oBAAoB,CAACpI,QAAQ,EAAEyC,OAAO,EAAEf,KAAK,EAAE,QAAQ,CAAC;UACxD0G,oBAAoB,CAACpI,QAAQ,EAAEyC,OAAO,EAAEf,KAAK,EAAE,MAAM,CAAC;QAC1D;MACJ;MAEA,SAASgH,4BAA4BA,CAACtC,IAAI,EAAEH,GAAG,EAAEE,MAAM,EAAED,KAAK,EAAE;QAC5DE,IAAI,GAAI,CAACA,IAAI,GAAG,GAAG,GAAIA,IAAI,GAAG,IAAM;QACpCH,GAAG,GAAI,CAACA,GAAG,GAAG,GAAG,GAAIA,GAAG,GAAG,IAAM;QACjCE,MAAM,GAAI,CAACA,MAAM,GAAG,GAAG,GAAIA,MAAM,GAAG,IAAM;QAC1CD,KAAK,GAAI,CAACA,KAAK,GAAG,GAAG,GAAIA,KAAK,GAAG,IAAM;QAEvC,OAAO,CAAC,QAAQ,GAAGE,IAAI,EAAE,OAAO,GAAGH,GAAG,EAAE,SAAS,GAAGC,KAAK,EAAE,UAAU,GAAGC,MAAM,CAAC;MACnF;MAEA7B,KAAK,CAAC,oBAAoB,CAAC;MAE3B,IAAI,CAACpE,QAAQ,CAACuC,OAAO,CAAC,EAAE;QACpB6B,KAAK,CAAC,+CAA+C,CAAC;QACtD;MACJ;MAEA4D,mBAAmB,CAAC,CAAC;MAErB,IAAIS,aAAa,GAAGzI,QAAQ,CAACuC,OAAO,CAAC,CAACb,SAAS;MAE/C,IAAI,CAAC+G,aAAa,EAAE;QAChBA,aAAa,GAAGb,sBAAsB,CAAC,CAAC;MAC5C;;MAEA;MACA;MACA;MACA;;MAEA;MACA;;MAEA,IAAIc,cAAc,GAAYrI,cAAc,CAACe,KAAK;MAClD,IAAIuH,eAAe,GAAWtI,cAAc,CAACgB,MAAM;MACnD,IAAIuH,uBAAuB,GAAG9H,kBAAkB,CAAC,CAAC,oBAAoB,EAAE,YAAY,EAAE,kBAAkB,EAAE,aAAa,EAAE,oBAAoB,EAAE,aAAa,EAAE,cAAc,EAAE,WAAW,EAAE,UAAU,CAAC,CAAC;MACvM,IAAI+H,cAAc,GAAY/H,kBAAkB,CAAC,CAAC,oBAAoB,EAAE,YAAY,EAAE,kBAAkB,EAAE,aAAa,EAAE,oBAAoB,CAAC,CAACgI,MAAM,CAACN,4BAA4B,CAAC,EAAE,CAAC,GAAGE,cAAc,CAAC,EAAE,EAAE,CAAC,GAAGC,eAAe,CAAC,EAAE,CAACA,eAAe,EAAE,CAACD,cAAc,CAAC,CAAC,CAAC;MACtQ,IAAIK,WAAW,GAAejI,kBAAkB,CAAC,CAAC,oBAAoB,EAAE,YAAY,EAAE,kBAAkB,EAAE,aAAa,EAAE,oBAAoB,EAAE,aAAa,EAAE,cAAc,CAAC,CAAC;MAC9K,IAAIkI,WAAW,GAAelI,kBAAkB,CAAC,CAAC,oBAAoB,EAAE,YAAY,EAAE,kBAAkB,EAAE,aAAa,EAAE,oBAAoB,EAAE,aAAa,EAAE,cAAc,CAAC,CAAC;MAC9K,IAAImI,gBAAgB,GAAUnI,kBAAkB,CAAC,CAAC,oBAAoB,EAAE,SAAS,EAAE,QAAQ,CAAC,CAAC;MAC7F,IAAIoI,gBAAgB,GAAUpI,kBAAkB,CAAC,CAAC,oBAAoB,EAAE,aAAa,EAAE,cAAc,CAAC,CAAC;MAEvG,IAAIqI,kBAAkB,GAAQtI,QAAQ,CAACU,aAAa,CAAC,KAAK,CAAC;MAC3D,IAAIG,SAAS,GAAiBb,QAAQ,CAACU,aAAa,CAAC,KAAK,CAAC;MAC3D,IAAI6F,MAAM,GAAoBvG,QAAQ,CAACU,aAAa,CAAC,KAAK,CAAC;MAC3D,IAAI6H,WAAW,GAAevI,QAAQ,CAACU,aAAa,CAAC,KAAK,CAAC;MAC3D,IAAI8F,MAAM,GAAoBxG,QAAQ,CAACU,aAAa,CAAC,KAAK,CAAC;MAC3D,IAAI8H,WAAW,GAAexI,QAAQ,CAACU,aAAa,CAAC,KAAK,CAAC;;MAE3D;MACA;MACA4H,kBAAkB,CAACG,GAAG,GAAgB,KAAK;MAE3CH,kBAAkB,CAAC3H,KAAK,CAACC,OAAO,GAAMmH,uBAAuB;MAC7DO,kBAAkB,CAACnG,SAAS,GAAUxC,uBAAuB;MAC7DkB,SAAS,CAACsB,SAAS,GAAmBxC,uBAAuB;MAC7DkB,SAAS,CAACF,KAAK,CAACC,OAAO,GAAeoH,cAAc;MACpDzB,MAAM,CAAC5F,KAAK,CAACC,OAAO,GAAkBsH,WAAW;MACjDK,WAAW,CAAC5H,KAAK,CAACC,OAAO,GAAawH,gBAAgB;MACtD5B,MAAM,CAAC7F,KAAK,CAACC,OAAO,GAAkBuH,WAAW;MACjDK,WAAW,CAAC7H,KAAK,CAACC,OAAO,GAAayH,gBAAgB;MAEtD9B,MAAM,CAACzF,WAAW,CAACyH,WAAW,CAAC;MAC/B/B,MAAM,CAAC1F,WAAW,CAAC0H,WAAW,CAAC;MAC/B3H,SAAS,CAACC,WAAW,CAACyF,MAAM,CAAC;MAC7B1F,SAAS,CAACC,WAAW,CAAC0F,MAAM,CAAC;MAC7B8B,kBAAkB,CAACxH,WAAW,CAACD,SAAS,CAAC;MACzC+G,aAAa,CAAC9G,WAAW,CAACwH,kBAAkB,CAAC;MAE7C,SAASI,cAAcA,CAAA,EAAG;QACtB,IAAIC,KAAK,GAAGxJ,QAAQ,CAACuC,OAAO,CAAC;QAC7B,IAAIiH,KAAK,IAAIA,KAAK,CAACC,QAAQ,EAAE;UACzBD,KAAK,CAACC,QAAQ,CAAC,CAAC;QACpB,CAAC,MAAM;UACHrF,KAAK,CAAC,8DAA8D,CAAC;QACzE;MACJ;MAEA,SAASsF,cAAcA,CAAA,EAAG;QACtB,IAAIF,KAAK,GAAGxJ,QAAQ,CAACuC,OAAO,CAAC;QAC7B,IAAIiH,KAAK,IAAIA,KAAK,CAACG,QAAQ,EAAE;UACzBH,KAAK,CAACG,QAAQ,CAAC,CAAC;QACpB,CAAC,MAAM;UACHvF,KAAK,CAAC,8DAA8D,CAAC;QACzE;MACJ;MAEAnB,QAAQ,CAACmE,MAAM,EAAE,QAAQ,EAAEmC,cAAc,CAAC;MAC1CtG,QAAQ,CAACoE,MAAM,EAAE,QAAQ,EAAEqC,cAAc,CAAC;;MAE1C;MACA;MACA1J,QAAQ,CAACuC,OAAO,CAAC,CAACgH,cAAc,GAAGA,cAAc;MACjDvJ,QAAQ,CAACuC,OAAO,CAAC,CAACmH,cAAc,GAAGA,cAAc;IACrD;IAEA,SAASE,oCAAoCA,CAAA,EAAG;MAC5C,SAASC,gBAAgBA,CAACtH,OAAO,EAAEnB,KAAK,EAAEC,MAAM,EAAE;QAC9C,IAAI+H,WAAW,GAAexC,qBAAqB,CAACrE,OAAO,CAAC;QAC5D,IAAI+E,WAAW,GAAeP,cAAc,CAAC3F,KAAK,CAAC;QACnD,IAAImG,YAAY,GAAcP,eAAe,CAAC3F,MAAM,CAAC;QACrD+H,WAAW,CAAC5H,KAAK,CAACyG,WAAW,CAAC,OAAO,EAAEX,WAAW,GAAG,IAAI,EAAEzH,OAAO,CAACoB,SAAS,GAAG,WAAW,GAAG,EAAE,CAAC;QAChGmI,WAAW,CAAC5H,KAAK,CAACyG,WAAW,CAAC,QAAQ,EAAEV,YAAY,GAAG,IAAI,EAAE1H,OAAO,CAACoB,SAAS,GAAG,WAAW,GAAG,EAAE,CAAC;MACtG;MAEA,SAAS6I,sBAAsBA,CAACC,IAAI,EAAE;QAClC,IAAI3I,KAAK,GAAamB,OAAO,CAACsD,WAAW;QACzC,IAAIxE,MAAM,GAAYkB,OAAO,CAACuD,YAAY;;QAE1C;QACA,IAAIkE,WAAW,GAAG5I,KAAK,KAAKpB,QAAQ,CAACuC,OAAO,CAAC,CAACmE,SAAS,IAAIrF,MAAM,KAAKrB,QAAQ,CAACuC,OAAO,CAAC,CAACoE,UAAU;QAElGvC,KAAK,CAAC,sBAAsB,EAAEhD,KAAK,EAAEC,MAAM,CAAC;;QAE5C;QACA;QACAoF,gBAAgB,CAAClE,OAAO,EAAEnB,KAAK,EAAEC,MAAM,CAAC;;QAExC;QACA;;QAEAtB,cAAc,CAACkK,GAAG,CAAC,CAAC,EAAE,SAASC,uBAAuBA,CAAA,EAAG;UACrD,IAAI,CAACF,WAAW,EAAE;YACd;UACJ;UAEA,IAAI,CAAChK,QAAQ,CAACuC,OAAO,CAAC,EAAE;YACpB6B,KAAK,CAAC,+CAA+C,CAAC;YACtD;UACJ;UAEA,IAAI,CAAC+F,mBAAmB,CAAC,CAAC,EAAE;YACxB/F,KAAK,CAAC,6DAA6D,CAAC;YACpE;UACJ;UAEA,IAAIvE,OAAO,CAACuE,KAAK,EAAE;YACf,IAAIgG,CAAC,GAAG7H,OAAO,CAACsD,WAAW;YAC3B,IAAIwE,CAAC,GAAG9H,OAAO,CAACuD,YAAY;YAE5B,IAAIsE,CAAC,KAAKhJ,KAAK,IAAIiJ,CAAC,KAAKhJ,MAAM,EAAE;cAC7BvB,QAAQ,CAACyI,IAAI,CAACpI,SAAS,CAACyE,GAAG,CAACrC,OAAO,CAAC,EAAE,yDAAyD,CAAC;YACpG;UACJ;UAEAsH,gBAAgB,CAACtH,OAAO,EAAEnB,KAAK,EAAEC,MAAM,CAAC;QAC5C,CAAC,CAAC;QAEFtB,cAAc,CAACkK,GAAG,CAAC,CAAC,EAAE,SAASK,gBAAgBA,CAAA,EAAG;UAC9C;UACA;;UAEA,IAAI,CAACtK,QAAQ,CAACuC,OAAO,CAAC,EAAE;YACpB6B,KAAK,CAAC,+CAA+C,CAAC;YACtD;UACJ;UAEA,IAAI,CAAC+F,mBAAmB,CAAC,CAAC,EAAE;YACxB/F,KAAK,CAAC,6DAA6D,CAAC;YACpE;UACJ;UAEA+C,kBAAkB,CAAC5E,OAAO,EAAEnB,KAAK,EAAEC,MAAM,CAAC;QAC9C,CAAC,CAAC;QAEF,IAAI2I,WAAW,IAAID,IAAI,EAAE;UACrBhK,cAAc,CAACkK,GAAG,CAAC,CAAC,EAAE,YAAY;YAC9B,IAAI,CAACjK,QAAQ,CAACuC,OAAO,CAAC,EAAE;cACpB6B,KAAK,CAAC,+CAA+C,CAAC;cACtD;YACJ;YAEA,IAAI,CAAC+F,mBAAmB,CAAC,CAAC,EAAE;cAC1B/F,KAAK,CAAC,6DAA6D,CAAC;cACpE;YACF;YAEA2F,IAAI,CAAC,CAAC;UACV,CAAC,CAAC;QACN;MACJ;MAEA,SAASI,mBAAmBA,CAAA,EAAG;QAC3B,OAAO,CAAC,CAACnK,QAAQ,CAACuC,OAAO,CAAC,CAACb,SAAS;MACxC;MAEA,SAAS6I,uBAAuBA,CAAA,EAAG;QAC/B,SAASC,aAAaA,CAAA,EAAG;UACrB,OAAOxK,QAAQ,CAACuC,OAAO,CAAC,CAACkI,iBAAiB,KAAKC,SAAS;QAC5D;QAEAtG,KAAK,CAAC,iCAAiC,CAAC;QAExC,IAAIoF,KAAK,GAAGxJ,QAAQ,CAACuC,OAAO,CAAC;;QAE7B;QACA,IAAIiI,aAAa,CAAC,CAAC,IAAIhB,KAAK,CAAC9C,SAAS,KAAK8C,KAAK,CAAClD,SAAS,CAAClF,KAAK,IAAIoI,KAAK,CAAC7C,UAAU,KAAK6C,KAAK,CAAClD,SAAS,CAACjF,MAAM,EAAE;UAC7G,OAAO+C,KAAK,CAAC,4FAA4F,CAAC;QAC9G;;QAEA;QACA,IAAIoF,KAAK,CAAC9C,SAAS,KAAK8C,KAAK,CAACiB,iBAAiB,IAAIjB,KAAK,CAAC7C,UAAU,KAAK6C,KAAK,CAACmB,kBAAkB,EAAE;UAC9F,OAAOvG,KAAK,CAAC,sCAAsC,CAAC;QACxD;QAGAA,KAAK,CAAC,yCAAyC,CAAC;QAChDoF,KAAK,CAACiB,iBAAiB,GAAGjB,KAAK,CAAC9C,SAAS;QACzC8C,KAAK,CAACmB,kBAAkB,GAAGnB,KAAK,CAAC7C,UAAU;QAC3ClH,OAAO,CAACO,QAAQ,CAACuC,OAAO,CAAC,CAACyB,SAAS,EAAE,UAAUD,QAAQ,EAAE;UACrDA,QAAQ,CAACxB,OAAO,CAAC;QACrB,CAAC,CAAC;MACN;MAEA,SAASqI,YAAYA,CAAA,EAAG;QACpBxG,KAAK,CAAC,2BAA2B,CAAC;QAElC,IAAIoB,YAAY,CAACjD,OAAO,CAAC,EAAE;UACvB6B,KAAK,CAAC,+CAA+C,CAAC;UACtD;QACJ;QAEAA,KAAK,CAAC,mBAAmB,CAAC;QAC1B,IAAIgD,MAAM,GAAGzD,gBAAgB,CAACpB,OAAO,CAAC;QACtC,IAAI8E,MAAM,GAAGxD,gBAAgB,CAACtB,OAAO,CAAC;QACtC,IAAI6E,MAAM,CAACM,UAAU,KAAK,CAAC,IAAIN,MAAM,CAACO,SAAS,KAAK,CAAC,IAAIN,MAAM,CAACK,UAAU,KAAK,CAAC,IAAIL,MAAM,CAACM,SAAS,KAAK,CAAC,EAAE;UACxGvD,KAAK,CAAC,uDAAuD,CAAC;UAC9D0F,sBAAsB,CAACS,uBAAuB,CAAC;QACnD;MACJ;MAEA,SAASM,YAAYA,CAAA,EAAG;QACpBzG,KAAK,CAAC,kBAAkB,CAAC;QAEzB,IAAIoB,YAAY,CAACjD,OAAO,CAAC,EAAE;UACvB;UACA6B,KAAK,CAAC,kDAAkD,CAAC;UACzD;QACJ;QAEA0F,sBAAsB,CAACS,uBAAuB,CAAC;MACnD;MAEAnG,KAAK,CAAC,+CAA+C,CAAC;MAEtD,IAAI,CAACpE,QAAQ,CAACuC,OAAO,CAAC,EAAE;QACpB6B,KAAK,CAAC,+CAA+C,CAAC;QACtD;MACJ;MAEApE,QAAQ,CAACuC,OAAO,CAAC,CAACuF,UAAU,GAAG8C,YAAY;MAC3C5K,QAAQ,CAACuC,OAAO,CAAC,CAACkH,QAAQ,GAAGoB,YAAY;MACzC7K,QAAQ,CAACuC,OAAO,CAAC,CAACoH,QAAQ,GAAGkB,YAAY;MAEzC,IAAIrJ,KAAK,GAAGxB,QAAQ,CAACuC,OAAO,CAAC,CAACf,KAAK;MACnCqI,gBAAgB,CAACtH,OAAO,EAAEf,KAAK,CAACJ,KAAK,EAAEI,KAAK,CAACH,MAAM,CAAC;IACxD;IAEA,SAASyJ,mBAAmBA,CAAA,EAAG;MAC3B1G,KAAK,CAAC,8BAA8B,CAAC;MAErC,IAAI,CAACpE,QAAQ,CAACuC,OAAO,CAAC,EAAE;QACpB6B,KAAK,CAAC,+CAA+C,CAAC;QACtD;MACJ;MAEA,IAAI5C,KAAK,GAAGxB,QAAQ,CAACuC,OAAO,CAAC,CAACf,KAAK;MACnCiF,gBAAgB,CAAClE,OAAO,EAAEf,KAAK,CAACJ,KAAK,EAAEI,KAAK,CAACH,MAAM,CAAC;MACpD8F,kBAAkB,CAAC5E,OAAO,EAAEf,KAAK,CAACJ,KAAK,EAAEI,KAAK,CAACH,MAAM,CAAC;IAC1D;IAEA,SAAS0J,KAAKA,CAAA,EAAG;MACb5G,QAAQ,CAAC5B,OAAO,CAAC;IACrB;IAEA,SAASyI,OAAOA,CAAA,EAAG;MACf5G,KAAK,CAAC,eAAe,CAAC;MACtBmC,aAAa,CAAC,CAAC;MACfF,cAAc,CAAC,CAAC;MAEhBtG,cAAc,CAACkK,GAAG,CAAC,CAAC,EAAEzD,UAAU,CAAC;MACjCzG,cAAc,CAACkK,GAAG,CAAC,CAAC,EAAElC,oBAAoB,CAAC;MAC3ChI,cAAc,CAACkK,GAAG,CAAC,CAAC,EAAEL,oCAAoC,CAAC;MAC3D7J,cAAc,CAACkK,GAAG,CAAC,CAAC,EAAEa,mBAAmB,CAAC;MAC1C/K,cAAc,CAACkK,GAAG,CAAC,CAAC,EAAEc,KAAK,CAAC;IAChC;IAEA3G,KAAK,CAAC,sBAAsB,CAAC;IAE7B,IAAIa,UAAU,CAAC1C,OAAO,CAAC,EAAE;MACrB6B,KAAK,CAAC,qBAAqB,CAAC;MAE5BwD,sBAAsB,CAAC,CAAC;MAExBxD,KAAK,CAAC,sCAAsC,CAAC;MAE7CpE,QAAQ,CAACuC,OAAO,CAAC,CAACuF,UAAU,GAAG,YAAY;QACvC1D,KAAK,CAAC,yBAAyB,CAAC;QAChC4G,OAAO,CAAC,CAAC;MACb,CAAC;IACL,CAAC,MAAM;MACHA,OAAO,CAAC,CAAC;IACb;EACJ;EAEA,SAASC,SAASA,CAAC1I,OAAO,EAAE;IACxB,IAAIiH,KAAK,GAAGxJ,QAAQ,CAACuC,OAAO,CAAC;IAE7B,IAAI,CAACiH,KAAK,EAAE;MACR;MACA;IACJ;;IAEA;IACA;IACA;IACA;IACA;;IAEA;IACAA,KAAK,CAACD,cAAc,IAAI/F,WAAW,CAACG,gBAAgB,CAACpB,OAAO,CAAC,EAAE,QAAQ,EAAEiH,KAAK,CAACD,cAAc,CAAC;IAC9FC,KAAK,CAACE,cAAc,IAAIlG,WAAW,CAACK,gBAAgB,CAACtB,OAAO,CAAC,EAAE,QAAQ,EAAEiH,KAAK,CAACE,cAAc,CAAC;IAC9FF,KAAK,CAAC3B,gBAAgB,IAAIrE,WAAW,CAACgG,KAAK,CAAC9H,SAAS,EAAE,gBAAgB,EAAE8H,KAAK,CAAC3B,gBAAgB,CAAC;IAEhG2B,KAAK,CAAC9H,SAAS,IAAIa,OAAO,CAACJ,WAAW,CAACqH,KAAK,CAAC9H,SAAS,CAAC;EAC3D;EAEA,OAAO;IACHwC,cAAc,EAAEA,cAAc;IAC9BJ,WAAW,EAAEA,WAAW;IACxBmH,SAAS,EAAEA,SAAS;IACpBxK,YAAY,EAAEA;EAClB,CAAC;AACL,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}
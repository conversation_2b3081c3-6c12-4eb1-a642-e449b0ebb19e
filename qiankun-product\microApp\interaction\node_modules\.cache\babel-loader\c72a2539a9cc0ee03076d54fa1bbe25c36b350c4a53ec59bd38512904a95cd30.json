{"ast": null, "code": "import toArray from './toArray.js';\n\n/**\n * Gets the next value on a wrapped object following the\n * [iterator protocol](https://mdn.io/iteration_protocols#iterator).\n *\n * @name next\n * @memberOf _\n * @since 4.0.0\n * @category Seq\n * @returns {Object} Returns the next iterator value.\n * @example\n *\n * var wrapped = _([1, 2]);\n *\n * wrapped.next();\n * // => { 'done': false, 'value': 1 }\n *\n * wrapped.next();\n * // => { 'done': false, 'value': 2 }\n *\n * wrapped.next();\n * // => { 'done': true, 'value': undefined }\n */\nfunction wrapperNext() {\n  if (this.__values__ === undefined) {\n    this.__values__ = toArray(this.value());\n  }\n  var done = this.__index__ >= this.__values__.length,\n    value = done ? undefined : this.__values__[this.__index__++];\n  return {\n    'done': done,\n    'value': value\n  };\n}\nexport default wrapperNext;", "map": {"version": 3, "names": ["toArray", "wrapperNext", "__values__", "undefined", "value", "done", "__index__", "length"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/node_modules/.pnpm/lodash-es@4.17.21/node_modules/lodash-es/next.js"], "sourcesContent": ["import toArray from './toArray.js';\n\n/**\n * Gets the next value on a wrapped object following the\n * [iterator protocol](https://mdn.io/iteration_protocols#iterator).\n *\n * @name next\n * @memberOf _\n * @since 4.0.0\n * @category Seq\n * @returns {Object} Returns the next iterator value.\n * @example\n *\n * var wrapped = _([1, 2]);\n *\n * wrapped.next();\n * // => { 'done': false, 'value': 1 }\n *\n * wrapped.next();\n * // => { 'done': false, 'value': 2 }\n *\n * wrapped.next();\n * // => { 'done': true, 'value': undefined }\n */\nfunction wrapperNext() {\n  if (this.__values__ === undefined) {\n    this.__values__ = toArray(this.value());\n  }\n  var done = this.__index__ >= this.__values__.length,\n      value = done ? undefined : this.__values__[this.__index__++];\n\n  return { 'done': done, 'value': value };\n}\n\nexport default wrapperNext;\n"], "mappings": "AAAA,OAAOA,OAAO,MAAM,cAAc;;AAElC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,WAAWA,CAAA,EAAG;EACrB,IAAI,IAAI,CAACC,UAAU,KAAKC,SAAS,EAAE;IACjC,IAAI,CAACD,UAAU,GAAGF,OAAO,CAAC,IAAI,CAACI,KAAK,CAAC,CAAC,CAAC;EACzC;EACA,IAAIC,IAAI,GAAG,IAAI,CAACC,SAAS,IAAI,IAAI,CAACJ,UAAU,CAACK,MAAM;IAC/CH,KAAK,GAAGC,IAAI,GAAGF,SAAS,GAAG,IAAI,CAACD,UAAU,CAAC,IAAI,CAACI,SAAS,EAAE,CAAC;EAEhE,OAAO;IAAE,MAAM,EAAED,IAAI;IAAE,OAAO,EAAED;EAAM,CAAC;AACzC;AAEA,eAAeH,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
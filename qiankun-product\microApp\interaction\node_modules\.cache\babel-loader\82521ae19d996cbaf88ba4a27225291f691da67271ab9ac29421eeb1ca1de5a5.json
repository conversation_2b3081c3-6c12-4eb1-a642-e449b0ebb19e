{"ast": null, "code": "function _slicedToArray(r, e) { return _arrayWithHoles(r) || _iterableToArrayLimit(r, e) || _unsupportedIterableToArray(r, e) || _nonIterableRest(); }\nfunction _nonIterableRest() { throw new TypeError(\"Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); }\nfunction _unsupportedIterableToArray(r, a) { if (r) { if (\"string\" == typeof r) return _arrayLikeToArray(r, a); var t = {}.toString.call(r).slice(8, -1); return \"Object\" === t && r.constructor && (t = r.constructor.name), \"Map\" === t || \"Set\" === t ? Array.from(r) : \"Arguments\" === t || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t) ? _arrayLikeToArray(r, a) : void 0; } }\nfunction _arrayLikeToArray(r, a) { (null == a || a > r.length) && (a = r.length); for (var e = 0, n = Array(a); e < a; e++) n[e] = r[e]; return n; }\nfunction _iterableToArrayLimit(r, l) { var t = null == r ? null : \"undefined\" != typeof Symbol && r[Symbol.iterator] || r[\"@@iterator\"]; if (null != t) { var e, n, i, u, a = [], f = !0, o = !1; try { if (i = (t = t.call(r)).next, 0 === l) { if (Object(t) !== t) return; f = !1; } else for (; !(f = (e = i.call(t)).done) && (a.push(e.value), a.length !== l); f = !0); } catch (r) { o = !0, n = r; } finally { try { if (!f && null != t.return && (u = t.return(), Object(u) !== u)) return; } finally { if (o) throw n; } } return a; } }\nfunction _arrayWithHoles(r) { if (Array.isArray(r)) return r; }\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nfunction _defineProperty(e, r, t) { return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, { value: t, enumerable: !0, configurable: !0, writable: !0 }) : e[r] = t, e; }\nfunction _toPropertyKey(t) { var i = _toPrimitive(t, \"string\"); return \"symbol\" == typeof i ? i : i + \"\"; }\nfunction _toPrimitive(t, r) { if (\"object\" != typeof t || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || \"default\"); if (\"object\" != typeof i) return i; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (\"string\" === r ? String : Number)(t); }\nimport { defineComponent, inject, watch, onBeforeUnmount, openBlock, createBlock, unref, withCtx, renderSlot, createElementBlock, mergeProps } from 'vue';\nimport '../../../utils/index.mjs';\nimport { tooltipV2RootKey } from './constants.mjs';\nimport ForwardRef from './forward-ref.mjs';\nimport { tooltipV2TriggerProps } from './trigger.mjs';\nimport { tooltipV2CommonProps } from './common.mjs';\nimport _export_sfc from '../../../_virtual/plugin-vue_export-helper.mjs';\nimport { composeEventHandlers } from '../../../utils/dom/event.mjs';\nvar __default__ = defineComponent({\n  name: \"ElTooltipV2Trigger\"\n});\nvar _sfc_main = /* @__PURE__ */defineComponent(_objectSpread(_objectSpread({}, __default__), {}, {\n  props: _objectSpread(_objectSpread({}, tooltipV2CommonProps), tooltipV2TriggerProps),\n  setup(__props) {\n    var props = __props;\n    var _inject = inject(tooltipV2RootKey),\n      onClose = _inject.onClose,\n      onOpen = _inject.onOpen,\n      onDelayOpen = _inject.onDelayOpen,\n      triggerRef = _inject.triggerRef,\n      contentId = _inject.contentId;\n    var isMousedown = false;\n    var setTriggerRef = function setTriggerRef(el) {\n      triggerRef.value = el;\n    };\n    var onMouseup = function onMouseup() {\n      isMousedown = false;\n    };\n    var onMouseenter = composeEventHandlers(props.onMouseEnter, onDelayOpen);\n    var onMouseleave = composeEventHandlers(props.onMouseLeave, onClose);\n    var onMousedown = composeEventHandlers(props.onMouseDown, function () {\n      onClose();\n      isMousedown = true;\n      document.addEventListener(\"mouseup\", onMouseup, {\n        once: true\n      });\n    });\n    var onFocus = composeEventHandlers(props.onFocus, function () {\n      if (!isMousedown) onOpen();\n    });\n    var onBlur = composeEventHandlers(props.onBlur, onClose);\n    var onClick = composeEventHandlers(props.onClick, function (e) {\n      if (e.detail === 0) onClose();\n    });\n    var events = {\n      blur: onBlur,\n      click: onClick,\n      focus: onFocus,\n      mousedown: onMousedown,\n      mouseenter: onMouseenter,\n      mouseleave: onMouseleave\n    };\n    var setEvents = function setEvents(el, events2, type) {\n      if (el) {\n        Object.entries(events2).forEach(function (_ref) {\n          var _ref2 = _slicedToArray(_ref, 2),\n            name = _ref2[0],\n            handler = _ref2[1];\n          el[type](name, handler);\n        });\n      }\n    };\n    watch(triggerRef, function (triggerEl, previousTriggerEl) {\n      setEvents(triggerEl, events, \"addEventListener\");\n      setEvents(previousTriggerEl, events, \"removeEventListener\");\n      if (triggerEl) {\n        triggerEl.setAttribute(\"aria-describedby\", contentId.value);\n      }\n    });\n    onBeforeUnmount(function () {\n      setEvents(triggerRef.value, events, \"removeEventListener\");\n      document.removeEventListener(\"mouseup\", onMouseup);\n    });\n    return function (_ctx, _cache) {\n      return _ctx.nowrap ? (openBlock(), createBlock(unref(ForwardRef), {\n        key: 0,\n        \"set-ref\": setTriggerRef,\n        \"only-child\": \"\"\n      }, {\n        default: withCtx(function () {\n          return [renderSlot(_ctx.$slots, \"default\")];\n        }),\n        _: 3\n      })) : (openBlock(), createElementBlock(\"button\", mergeProps({\n        key: 1,\n        ref_key: \"triggerRef\",\n        ref: triggerRef\n      }, _ctx.$attrs), [renderSlot(_ctx.$slots, \"default\")], 16));\n    };\n  }\n}));\nvar TooltipV2Trigger = /* @__PURE__ */_export_sfc(_sfc_main, [[\"__file\", \"trigger.vue\"]]);\nexport { TooltipV2Trigger as default };", "map": {"version": 3, "names": ["name", "_inject", "inject", "tooltipV2RootKey", "onClose", "onOpen", "onDelayOpen", "triggerRef", "contentId", "isMousedown", "setTriggerRef", "el", "value", "onMouseup", "onMouseenter", "composeEventHandlers", "props", "onMouseEnter", "onMouseleave", "onMouseLeave", "onMousedown", "onMouseDown", "document", "addEventListener", "once", "onFocus", "onBlur", "onClick", "e", "detail", "events", "blur", "click", "focus", "mousedown", "mouseenter", "mouseleave", "setEvents", "events2", "type", "Object", "entries", "for<PERSON>ach", "_ref", "_ref2", "_slicedToArray", "handler", "watch", "triggerEl", "previousTriggerEl", "setAttribute", "onBeforeUnmount", "removeEventListener"], "sources": ["../../../../../../packages/components/tooltip-v2/src/trigger.vue"], "sourcesContent": ["<template>\n  <forward-ref v-if=\"nowrap\" :set-ref=\"setTriggerRef\" only-child>\n    <slot />\n  </forward-ref>\n  <button v-else ref=\"triggerRef\" v-bind=\"$attrs\">\n    <slot />\n  </button>\n</template>\n\n<script setup lang=\"ts\">\nimport { inject, onBeforeUnmount, watch } from 'vue'\nimport { composeEventHandlers } from '@element-plus/utils'\nimport { tooltipV2RootKey } from './constants'\nimport ForwardRef from './forward-ref'\nimport { tooltipV2TriggerProps } from './trigger'\nimport { tooltipV2CommonProps } from './common'\n\ndefineOptions({\n  name: 'ElTooltipV2Trigger',\n})\n\nconst props = defineProps({\n  ...tooltipV2CommonProps,\n  ...tooltipV2TriggerProps,\n})\n\n/**\n * onOpen opens the tooltip instantly, onTrigger acts a lil bit differently,\n * it will check if delayDuration is set to greater than 0 and based on that result,\n * if true, it opens the tooltip after delayDuration, otherwise it opens it instantly.\n */\nconst { onClose, onOpen, onDelayOpen, triggerRef, contentId } =\n  inject(tooltipV2RootKey)!\n\nlet isMousedown = false\n\nconst setTriggerRef = (el: HTMLElement | null) => {\n  triggerRef.value = el\n}\n\nconst onMouseup = () => {\n  isMousedown = false\n}\n\nconst onMouseenter = composeEventHandlers(props.onMouseEnter, onDelayOpen)\n\nconst onMouseleave = composeEventHandlers(props.onMouseLeave, onClose)\n\nconst onMousedown = composeEventHandlers(props.onMouseDown, () => {\n  onClose()\n  isMousedown = true\n  document.addEventListener('mouseup', onMouseup, { once: true })\n})\n\nconst onFocus = composeEventHandlers(props.onFocus, () => {\n  if (!isMousedown) onOpen()\n})\n\nconst onBlur = composeEventHandlers(props.onBlur, onClose)\n\nconst onClick = composeEventHandlers(props.onClick, (e) => {\n  if ((e as MouseEvent).detail === 0) onClose()\n})\n\nconst events = {\n  blur: onBlur,\n  click: onClick,\n  focus: onFocus,\n  mousedown: onMousedown,\n  mouseenter: onMouseenter,\n  mouseleave: onMouseleave,\n}\n\nconst setEvents = <T extends (e: Event) => void>(\n  el: HTMLElement | null | undefined,\n  events: Record<string, T>,\n  type: 'addEventListener' | 'removeEventListener'\n) => {\n  if (el) {\n    Object.entries(events).forEach(([name, handler]) => {\n      el[type](name, handler)\n    })\n  }\n}\n\nwatch(triggerRef, (triggerEl, previousTriggerEl) => {\n  setEvents(triggerEl, events, 'addEventListener')\n  setEvents(previousTriggerEl, events, 'removeEventListener')\n\n  if (triggerEl) {\n    triggerEl.setAttribute('aria-describedby', contentId.value)\n  }\n})\n\nonBeforeUnmount(() => {\n  setEvents(triggerRef.value, events, 'removeEventListener')\n  document.removeEventListener('mouseup', onMouseup)\n})\n</script>\n"], "mappings": ";;;;;;;;;;;;;;;;;;;iCAiBc;EACZA,IAAM;AACR;;;;;IAYA,IAAAC,OAAA,GACEC,MAAA,CAAOC,gBAAgB;MADjBC,OAAS,GAAAH,OAAA,CAATG,OAAS;MAAAC,MAAA,GAAAJ,OAAA,CAAAI,MAAA;MAAQC,WAAA,GAAAL,OAAA,CAAAK,WAAA;MAAaC,UAAY,GAAAN,OAAA,CAAZM,UAAY;MAAAC,SAAA,GAAAP,OAAA,CAAAO,SAAA;IAGlD,IAAIC,WAAc;IAEZ,IAAAC,aAAA,GAAgB,SAAhBA,cAAiBC,EAA2B;MAChDJ,UAAA,CAAWK,KAAQ,GAAAD,EAAA;IAAA,CACrB;IAEA,IAAME,SAAA,GAAY,SAAZA,UAAA,EAAkB;MACRJ,WAAA;IAAA,CAChB;IAEA,IAAMK,YAAe,GAAAC,oBAAA,CAAqBC,KAAM,CAAAC,YAAA,EAAcX,WAAW;IAEzE,IAAMY,YAAe,GAAAH,oBAAA,CAAqBC,KAAM,CAAAG,YAAA,EAAcf,OAAO;IAErE,IAAMgB,WAAc,GAAAL,oBAAA,CAAqBC,KAAM,CAAAK,WAAA,EAAa,YAAM;MACxDjB,OAAA;MACMK,WAAA;MACda,QAAA,CAASC,gBAAA,CAAiB,SAAW,EAAAV,SAAA,EAAW;QAAEW,IAAA,EAAM;MAAA,CAAM;IAAA,CAC/D;IAED,IAAMC,OAAU,GAAAV,oBAAA,CAAqBC,KAAM,CAAAS,OAAA,EAAS,YAAM;MACxD,IAAI,CAAChB,WAAA,EAAoBJ,MAAA;IAAA,CAC1B;IAED,IAAMqB,MAAS,GAAAX,oBAAA,CAAqBC,KAAM,CAAAU,MAAA,EAAQtB,OAAO;IAEzD,IAAMuB,OAAU,GAAAZ,oBAAA,CAAqBC,KAAM,CAAAW,OAAA,EAAS,UAACC,CAAM;MACzD,IAAKA,CAAA,CAAiBC,MAAW,QAAWzB,OAAA;IAAA,CAC7C;IAED,IAAM0B,MAAS;MACbC,IAAM,EAAAL,MAAA;MACNM,KAAO,EAAAL,OAAA;MACPM,KAAO,EAAAR,OAAA;MACPS,SAAW,EAAAd,WAAA;MACXe,UAAY,EAAArB,YAAA;MACZsB,UAAY,EAAAlB;IAAA,CACd;IAEA,IAAMmB,SAAY,YAAZA,SAAYA,CAChB1B,EACA,EAAA2B,OAAA,EACAC,IACG;MACH,IAAI5B,EAAI;QACN6B,MAAA,CAAOC,OAAA,CAAQH,OAAM,EAAEI,OAAA,CAAQ,UAAAC,IAAA,EAAqB;UAAA,IAAAC,KAAA,GAAAC,cAAA,CAAAF,IAAA;YAAnB3C,IAAA,GAAA4C,KAAA;YAAME,OAAa,GAAAF,KAAA;UAC/CjC,EAAA,CAAA4B,IAAA,EAAMvC,IAAA,EAAM8C,OAAO;QAAA,CACvB;MAAA;IACH,CACF;IAEMC,KAAA,CAAAxC,UAAA,EAAY,UAACyC,SAAA,EAAWC,iBAAsB;MACxCZ,SAAA,CAAAW,SAAA,EAAWlB,MAAA,EAAQ,kBAAkB;MACrCO,SAAA,CAAAY,iBAAA,EAAmBnB,MAAA,EAAQ,qBAAqB;MAE1D,IAAIkB,SAAW;QACHA,SAAA,CAAAE,YAAA,CAAa,kBAAoB,EAAA1C,SAAA,CAAUI,KAAK;MAAA;IAC5D,CACD;IAEDuC,eAAA,CAAgB,YAAM;MACVd,SAAA,CAAA9B,UAAA,CAAWK,KAAO,EAAAkB,MAAA,EAAQ,qBAAqB;MAChDR,QAAA,CAAA8B,mBAAA,CAAoB,WAAWvC,SAAS;IAAA,CAClD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "import { resolveComponent as _resolveComponent, createVNode as _createVNode, withCtx as _withCtx, createTextVNode as _createTextVNode, openBlock as _openBlock, createBlock as _createBlock, createCommentVNode as _createCommentVNode, createElementVNode as _createElementVNode, createElementBlock as _createElementBlock } from \"vue\";\nvar _hoisted_1 = {\n  class: \"AddressBookUserNew\"\n};\nvar _hoisted_2 = {\n  class: \"globalFormButton\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  var _component_el_input = _resolveComponent(\"el-input\");\n  var _component_el_form_item = _resolveComponent(\"el-form-item\");\n  var _component_el_radio = _resolveComponent(\"el-radio\");\n  var _component_el_radio_group = _resolveComponent(\"el-radio-group\");\n  var _component_el_button = _resolveComponent(\"el-button\");\n  var _component_el_form = _resolveComponent(\"el-form\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createVNode(_component_el_form, {\n    ref: \"formRef\",\n    model: $setup.form,\n    rules: $setup.rules,\n    inline: \"\",\n    \"label-position\": \"top\",\n    class: \"globalForm\"\n  }, {\n    default: _withCtx(function () {\n      return [_createVNode(_component_el_form_item, {\n        label: \"姓名\",\n        prop: \"userName\"\n      }, {\n        default: _withCtx(function () {\n          return [_createVNode(_component_el_input, {\n            modelValue: $setup.form.userName,\n            \"onUpdate:modelValue\": _cache[0] || (_cache[0] = function ($event) {\n              return $setup.form.userName = $event;\n            }),\n            placeholder: \"请输入姓名\",\n            clearable: \"\"\n          }, null, 8 /* PROPS */, [\"modelValue\"])];\n        }),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_form_item, {\n        label: \"联系电话\",\n        prop: \"mobile\"\n      }, {\n        default: _withCtx(function () {\n          return [_createVNode(_component_el_input, {\n            modelValue: $setup.form.mobile,\n            \"onUpdate:modelValue\": _cache[1] || (_cache[1] = function ($event) {\n              return $setup.form.mobile = $event;\n            }),\n            onInput: _cache[2] || (_cache[2] = function ($event) {\n              return $setup.form.mobile = $setup.validNum($setup.form.mobile);\n            }),\n            placeholder: \"请输入联系电话\",\n            clearable: \"\"\n          }, null, 8 /* PROPS */, [\"modelValue\"])];\n        }),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_form_item, {\n        label: \"单位及职务\",\n        class: \"globalFormTitle\"\n      }, {\n        default: _withCtx(function () {\n          return [_createVNode(_component_el_input, {\n            modelValue: $setup.form.position,\n            \"onUpdate:modelValue\": _cache[3] || (_cache[3] = function ($event) {\n              return $setup.form.position = $event;\n            }),\n            placeholder: \"请输入单位及职务\",\n            clearable: \"\"\n          }, null, 8 /* PROPS */, [\"modelValue\"])];\n        }),\n        _: 1 /* STABLE */\n      }), !$setup.route.query.type ? (_openBlock(), _createBlock(_component_el_form_item, {\n        key: 0,\n        label: \"是否公开\",\n        prop: \"isOpen\"\n      }, {\n        default: _withCtx(function () {\n          return [_createVNode(_component_el_radio_group, {\n            modelValue: $setup.form.isOpen,\n            \"onUpdate:modelValue\": _cache[4] || (_cache[4] = function ($event) {\n              return $setup.form.isOpen = $event;\n            })\n          }, {\n            default: _withCtx(function () {\n              return [_createVNode(_component_el_radio, {\n                label: 1\n              }, {\n                default: _withCtx(function () {\n                  return _cache[6] || (_cache[6] = [_createTextVNode(\"公开\")]);\n                }),\n                _: 1 /* STABLE */\n              }), _createVNode(_component_el_radio, {\n                label: 0\n              }, {\n                default: _withCtx(function () {\n                  return _cache[7] || (_cache[7] = [_createTextVNode(\"不公开\")]);\n                }),\n                _: 1 /* STABLE */\n              })];\n            }),\n            _: 1 /* STABLE */\n          }, 8 /* PROPS */, [\"modelValue\"])];\n        }),\n        _: 1 /* STABLE */\n      })) : _createCommentVNode(\"v-if\", true), _createElementVNode(\"div\", _hoisted_2, [_createVNode(_component_el_button, {\n        type: \"primary\",\n        onClick: _cache[5] || (_cache[5] = function ($event) {\n          return $setup.submitForm($setup.formRef);\n        })\n      }, {\n        default: _withCtx(function () {\n          return _cache[8] || (_cache[8] = [_createTextVNode(\"提交\")]);\n        }),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_button, {\n        onClick: $setup.resetForm\n      }, {\n        default: _withCtx(function () {\n          return _cache[9] || (_cache[9] = [_createTextVNode(\"取消\")]);\n        }),\n        _: 1 /* STABLE */\n      })])];\n    }),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"model\", \"rules\"])]);\n}", "map": {"version": 3, "names": ["class", "_createElementBlock", "_hoisted_1", "_createVNode", "_component_el_form", "ref", "model", "$setup", "form", "rules", "inline", "default", "_withCtx", "_component_el_form_item", "label", "prop", "_component_el_input", "modelValue", "userName", "_cache", "$event", "placeholder", "clearable", "_", "mobile", "onInput", "validNum", "position", "route", "query", "type", "_createBlock", "key", "_component_el_radio_group", "isOpen", "_component_el_radio", "_createTextVNode", "_createCommentVNode", "_createElementVNode", "_hoisted_2", "_component_el_button", "onClick", "submitForm", "formRef", "resetForm"], "sources": ["D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\interaction\\src\\views\\AddressBook\\AddressBookUserNew.vue"], "sourcesContent": ["<template>\r\n  <div class=\"AddressBookUserNew\">\r\n    <el-form ref=\"formRef\"\r\n             :model=\"form\"\r\n             :rules=\"rules\"\r\n             inline\r\n             label-position=\"top\"\r\n             class=\"globalForm\">\r\n      <el-form-item label=\"姓名\"\r\n                    prop=\"userName\">\r\n        <el-input v-model=\"form.userName\"\r\n                  placeholder=\"请输入姓名\"\r\n                  clearable />\r\n      </el-form-item>\r\n      <el-form-item label=\"联系电话\"\r\n                    prop=\"mobile\">\r\n        <el-input v-model=\"form.mobile\"\r\n                  @input=\"form.mobile = validNum(form.mobile)\"\r\n                  placeholder=\"请输入联系电话\"\r\n                  clearable />\r\n      </el-form-item>\r\n      <el-form-item label=\"单位及职务\"\r\n                    class=\"globalFormTitle\">\r\n        <el-input v-model=\"form.position\"\r\n                  placeholder=\"请输入单位及职务\"\r\n                  clearable />\r\n      </el-form-item>\r\n      <el-form-item label=\"是否公开\"\r\n                    prop=\"isOpen\"\r\n                    v-if=\"!route.query.type\">\r\n        <el-radio-group v-model=\"form.isOpen\">\r\n          <el-radio :label=\"1\">公开</el-radio>\r\n          <el-radio :label=\"0\">不公开</el-radio>\r\n        </el-radio-group>\r\n      </el-form-item>\r\n      <div class=\"globalFormButton\">\r\n        <el-button type=\"primary\"\r\n                   @click=\"submitForm(formRef)\">提交</el-button>\r\n        <el-button @click=\"resetForm\">取消</el-button>\r\n      </div>\r\n    </el-form>\r\n  </div>\r\n</template>\r\n<script>\r\nexport default { name: 'AddressBookUserNew' }\r\n</script>\r\n<script setup>\r\nimport api from '@/api'\r\nimport { validNum } from 'common/js/utils.js'\r\nimport { reactive, ref, onMounted } from 'vue'\r\nimport { useRoute } from 'vue-router'\r\nimport { ElMessage } from 'element-plus'\r\nconst props = defineProps({ id: { type: String, default: '' }, groupId: { type: String, default: '' } })\r\nconst emit = defineEmits(['callback'])\r\nconst route = useRoute()\r\nconst formRef = ref()\r\nconst form = reactive({\r\n  userName: '', // 姓名\r\n  mobile: '', // 序号\r\n  position: '', // 单位及职务\r\n  isOpen: 1\r\n})\r\nconst rules = reactive({\r\n  userName: [{ required: true, message: '请输入姓名', trigger: ['blur', 'change'] }],\r\n  mobile: [{ required: true, message: '请输入联系电话', trigger: ['blur', 'change'] }]\r\n})\r\n\r\nonMounted(() => { if (props.id) { AddressBookUserInfo() } })\r\n\r\nconst AddressBookUserInfo = async () => {\r\n  const res = await api.AddressBookUserInfo({ detailId: props.id })\r\n  var { data } = res\r\n  form.userName = data.userName\r\n  form.mobile = data.mobile\r\n  form.position = data.position\r\n  form.isOpen = data.isOpen\r\n}\r\nconst submitForm = async (formEl) => {\r\n  if (!formEl) return\r\n  await formEl.validate((valid, fields) => {\r\n    if (valid) { globalJson() } else { ElMessage({ type: 'warning', message: '请根据提示信息完善字段内容！' }) }\r\n  })\r\n}\r\nconst globalJson = async () => {\r\n  const { code } = await api.globalJson(props.id ? '/relationBookMember/edit' : '/relationBookMember/add', {\r\n    form: {\r\n      id: props.id,\r\n      relationBookId: props.groupId,\r\n      userName: form.userName,\r\n      mobile: form.mobile,\r\n      position: form.position,\r\n      isOpen: form.isOpen\r\n    }\r\n  })\r\n  if (code === 200) {\r\n    ElMessage({ type: 'success', message: props.id ? '编辑成功' : '新增成功' })\r\n    emit('callback')\r\n  }\r\n}\r\nconst resetForm = () => { emit('callback') }\r\n</script>\r\n<style lang=\"scss\">\r\n.AddressBookUserNew {\r\n  width: 680px;\r\n}\r\n</style>\r\n"], "mappings": ";;EACOA,KAAK,EAAC;AAAoB;;EAkCtBA,KAAK,EAAC;AAAkB;;;;;;;;uBAlCjCC,mBAAA,CAwCM,OAxCNC,UAwCM,GAvCJC,YAAA,CAsCUC,kBAAA;IAtCDC,GAAG,EAAC,SAAS;IACZC,KAAK,EAAEC,MAAA,CAAAC,IAAI;IACXC,KAAK,EAAEF,MAAA,CAAAE,KAAK;IACbC,MAAM,EAAN,EAAM;IACN,gBAAc,EAAC,KAAK;IACpBV,KAAK,EAAC;;IAPnBW,OAAA,EAAAC,QAAA,CAQM;MAAA,OAKe,CALfT,YAAA,CAKeU,uBAAA;QALDC,KAAK,EAAC,IAAI;QACVC,IAAI,EAAC;;QATzBJ,OAAA,EAAAC,QAAA,CAUQ;UAAA,OAEsB,CAFtBT,YAAA,CAEsBa,mBAAA;YAZ9BC,UAAA,EAU2BV,MAAA,CAAAC,IAAI,CAACU,QAAQ;YAVxC,uBAAAC,MAAA,QAAAA,MAAA,gBAAAC,MAAA;cAAA,OAU2Bb,MAAA,CAAAC,IAAI,CAACU,QAAQ,GAAAE,MAAA;YAAA;YACtBC,WAAW,EAAC,OAAO;YACnBC,SAAS,EAAT;;;QAZlBC,CAAA;UAcMpB,YAAA,CAMeU,uBAAA;QANDC,KAAK,EAAC,MAAM;QACZC,IAAI,EAAC;;QAfzBJ,OAAA,EAAAC,QAAA,CAgBQ;UAAA,OAGsB,CAHtBT,YAAA,CAGsBa,mBAAA;YAnB9BC,UAAA,EAgB2BV,MAAA,CAAAC,IAAI,CAACgB,MAAM;YAhBtC,uBAAAL,MAAA,QAAAA,MAAA,gBAAAC,MAAA;cAAA,OAgB2Bb,MAAA,CAAAC,IAAI,CAACgB,MAAM,GAAAJ,MAAA;YAAA;YACnBK,OAAK,EAAAN,MAAA,QAAAA,MAAA,gBAAAC,MAAA;cAAA,OAAEb,MAAA,CAAAC,IAAI,CAACgB,MAAM,GAAGjB,MAAA,CAAAmB,QAAQ,CAACnB,MAAA,CAAAC,IAAI,CAACgB,MAAM;YAAA;YAC1CH,WAAW,EAAC,SAAS;YACrBC,SAAS,EAAT;;;QAnBlBC,CAAA;UAqBMpB,YAAA,CAKeU,uBAAA;QALDC,KAAK,EAAC,OAAO;QACbd,KAAK,EAAC;;QAtB1BW,OAAA,EAAAC,QAAA,CAuBQ;UAAA,OAEsB,CAFtBT,YAAA,CAEsBa,mBAAA;YAzB9BC,UAAA,EAuB2BV,MAAA,CAAAC,IAAI,CAACmB,QAAQ;YAvBxC,uBAAAR,MAAA,QAAAA,MAAA,gBAAAC,MAAA;cAAA,OAuB2Bb,MAAA,CAAAC,IAAI,CAACmB,QAAQ,GAAAP,MAAA;YAAA;YACtBC,WAAW,EAAC,UAAU;YACtBC,SAAS,EAAT;;;QAzBlBC,CAAA;WA6B2BhB,MAAA,CAAAqB,KAAK,CAACC,KAAK,CAACC,IAAI,I,cAFrCC,YAAA,CAOelB,uBAAA;QAlCrBmB,GAAA;QA2BoBlB,KAAK,EAAC,MAAM;QACZC,IAAI,EAAC;;QA5BzBJ,OAAA,EAAAC,QAAA,CA8BQ;UAAA,OAGiB,CAHjBT,YAAA,CAGiB8B,yBAAA;YAjCzBhB,UAAA,EA8BiCV,MAAA,CAAAC,IAAI,CAAC0B,MAAM;YA9B5C,uBAAAf,MAAA,QAAAA,MAAA,gBAAAC,MAAA;cAAA,OA8BiCb,MAAA,CAAAC,IAAI,CAAC0B,MAAM,GAAAd,MAAA;YAAA;;YA9B5CT,OAAA,EAAAC,QAAA,CA+BU;cAAA,OAAkC,CAAlCT,YAAA,CAAkCgC,mBAAA;gBAAvBrB,KAAK,EAAE;cAAC;gBA/B7BH,OAAA,EAAAC,QAAA,CA+B+B;kBAAA,OAAEO,MAAA,QAAAA,MAAA,OA/BjCiB,gBAAA,CA+B+B,IAAE,E;;gBA/BjCb,CAAA;kBAgCUpB,YAAA,CAAmCgC,mBAAA;gBAAxBrB,KAAK,EAAE;cAAC;gBAhC7BH,OAAA,EAAAC,QAAA,CAgC+B;kBAAA,OAAGO,MAAA,QAAAA,MAAA,OAhClCiB,gBAAA,CAgC+B,KAAG,E;;gBAhClCb,CAAA;;;YAAAA,CAAA;;;QAAAA,CAAA;YAAAc,mBAAA,gBAmCMC,mBAAA,CAIM,OAJNC,UAIM,GAHJpC,YAAA,CACsDqC,oBAAA;QAD3CV,IAAI,EAAC,SAAS;QACbW,OAAK,EAAAtB,MAAA,QAAAA,MAAA,gBAAAC,MAAA;UAAA,OAAEb,MAAA,CAAAmC,UAAU,CAACnC,MAAA,CAAAoC,OAAO;QAAA;;QArC7ChC,OAAA,EAAAC,QAAA,CAqCgD;UAAA,OAAEO,MAAA,QAAAA,MAAA,OArClDiB,gBAAA,CAqCgD,IAAE,E;;QArClDb,CAAA;UAsCQpB,YAAA,CAA4CqC,oBAAA;QAAhCC,OAAK,EAAElC,MAAA,CAAAqC;MAAS;QAtCpCjC,OAAA,EAAAC,QAAA,CAsCsC;UAAA,OAAEO,MAAA,QAAAA,MAAA,OAtCxCiB,gBAAA,CAsCsC,IAAE,E;;QAtCxCb,CAAA;;;IAAAA,CAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
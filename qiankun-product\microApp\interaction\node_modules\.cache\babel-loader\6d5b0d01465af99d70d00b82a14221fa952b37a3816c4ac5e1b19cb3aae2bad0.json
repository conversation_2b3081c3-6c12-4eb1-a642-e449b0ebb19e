{"ast": null, "code": "import { ref, getCurrentInstance, inject, computed } from 'vue';\nimport '../../../../utils/index.mjs';\nimport '../../../../constants/index.mjs';\nimport { checkboxGroupContextKey } from '../constants.mjs';\nimport { isUndefined } from '../../../../utils/types.mjs';\nimport { isArray } from '@vue/shared';\nimport { UPDATE_MODEL_EVENT } from '../../../../constants/event.mjs';\nvar useCheckboxModel = function useCheckboxModel(props) {\n  var selfModel = ref(false);\n  var _getCurrentInstance = getCurrentInstance(),\n    emit = _getCurrentInstance.emit;\n  var checkboxGroup = inject(checkboxGroupContextKey, void 0);\n  var isGroup = computed(function () {\n    return isUndefined(checkboxGroup) === false;\n  });\n  var isLimitExceeded = ref(false);\n  var model = computed({\n    get() {\n      var _a, _b;\n      return isGroup.value ? (_a = checkboxGroup == null ? void 0 : checkboxGroup.modelValue) == null ? void 0 : _a.value : (_b = props.modelValue) != null ? _b : selfModel.value;\n    },\n    set(val) {\n      var _a, _b;\n      if (isGroup.value && isArray(val)) {\n        isLimitExceeded.value = ((_a = checkboxGroup == null ? void 0 : checkboxGroup.max) == null ? void 0 : _a.value) !== void 0 && val.length > (checkboxGroup == null ? void 0 : checkboxGroup.max.value) && val.length > model.value.length;\n        isLimitExceeded.value === false && ((_b = checkboxGroup == null ? void 0 : checkboxGroup.changeEvent) == null ? void 0 : _b.call(checkboxGroup, val));\n      } else {\n        emit(UPDATE_MODEL_EVENT, val);\n        selfModel.value = val;\n      }\n    }\n  });\n  return {\n    model,\n    isGroup,\n    isLimitExceeded\n  };\n};\nexport { useCheckboxModel };", "map": {"version": 3, "names": ["useCheckboxModel", "props", "selfModel", "ref", "_getCurrentInstance", "getCurrentInstance", "emit", "checkboxGroup", "inject", "checkboxGroupContextKey", "isGroup", "computed", "isUndefined", "isLimitExceeded", "model", "get", "_a", "_b", "value", "modelValue", "set", "val", "isArray", "max", "length", "changeEvent", "call", "UPDATE_MODEL_EVENT"], "sources": ["../../../../../../../packages/components/checkbox/src/composables/use-checkbox-model.ts"], "sourcesContent": ["import { computed, getCurrentInstance, inject, ref } from 'vue'\nimport { isArray, isUndefined } from '@element-plus/utils'\nimport { UPDATE_MODEL_EVENT } from '@element-plus/constants'\nimport { checkboxGroupContextKey } from '../constants'\n\nimport type { CheckboxProps } from '../checkbox'\n\nexport const useCheckboxModel = (props: CheckboxProps) => {\n  const selfModel = ref<unknown>(false)\n  const { emit } = getCurrentInstance()!\n  const checkboxGroup = inject(checkboxGroupContextKey, undefined)\n  const isGroup = computed(() => isUndefined(checkboxGroup) === false)\n  const isLimitExceeded = ref(false)\n  const model = computed({\n    get() {\n      return isGroup.value\n        ? checkboxGroup?.modelValue?.value\n        : props.modelValue ?? selfModel.value\n    },\n\n    set(val: unknown) {\n      if (isGroup.value && isArray(val)) {\n        isLimitExceeded.value =\n          checkboxGroup?.max?.value !== undefined &&\n          val.length > checkboxGroup?.max.value &&\n          val.length > model.value.length\n        isLimitExceeded.value === false && checkboxGroup?.changeEvent?.(val)\n      } else {\n        emit(UPDATE_MODEL_EVENT, val)\n        selfModel.value = val\n      }\n    },\n  })\n\n  return {\n    model,\n    isGroup,\n    isLimitExceeded,\n  }\n}\n\nexport type CheckboxModel = ReturnType<typeof useCheckboxModel>\n"], "mappings": ";;;;;;;AAIY,IAACA,gBAAgB,GAAG,SAAnBA,gBAAgBA,CAAIC,KAAK,EAAK;EACzC,IAAMC,SAAS,GAAGC,GAAG,CAAC,KAAK,CAAC;EAC5B,IAAAC,mBAAA,GAAiBC,kBAAkB,EAAE;IAA7BC,IAAI,GAAAF,mBAAA,CAAJE,IAAI;EACZ,IAAMC,aAAa,GAAGC,MAAM,CAACC,uBAAuB,EAAE,KAAK,CAAC,CAAC;EAC7D,IAAMC,OAAO,GAAGC,QAAQ,CAAC;IAAA,OAAMC,WAAW,CAACL,aAAa,CAAC,KAAK,KAAK;EAAA,EAAC;EACpE,IAAMM,eAAe,GAAGV,GAAG,CAAC,KAAK,CAAC;EAClC,IAAMW,KAAK,GAAGH,QAAQ,CAAC;IACrBI,GAAGA,CAAA,EAAG;MACJ,IAAIC,EAAE,EAAEC,EAAE;MACV,OAAOP,OAAO,CAACQ,KAAK,GAAG,CAACF,EAAE,GAAGT,aAAa,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,aAAa,CAACY,UAAU,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGH,EAAE,CAACE,KAAK,GAAG,CAACD,EAAE,GAAGhB,KAAK,CAACkB,UAAU,KAAK,IAAI,GAAGF,EAAE,GAAGf,SAAS,CAACgB,KAAK;IAClL,CAAK;IACDE,GAAGA,CAACC,GAAG,EAAE;MACP,IAAIL,EAAE,EAAEC,EAAE;MACV,IAAIP,OAAO,CAACQ,KAAK,IAAII,OAAO,CAACD,GAAG,CAAC,EAAE;QACjCR,eAAe,CAACK,KAAK,GAAG,CAAC,CAACF,EAAE,GAAGT,aAAa,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,aAAa,CAACgB,GAAG,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGP,EAAE,CAACE,KAAK,MAAM,KAAK,CAAC,IAAIG,GAAG,CAACG,MAAM,IAAIjB,aAAa,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,aAAa,CAACgB,GAAG,CAACL,KAAK,CAAC,IAAIG,GAAG,CAACG,MAAM,GAAGV,KAAK,CAACI,KAAK,CAACM,MAAM;QACxOX,eAAe,CAACK,KAAK,KAAK,KAAK,KAAK,CAACD,EAAE,GAAGV,aAAa,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,aAAa,CAACkB,WAAW,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGR,EAAE,CAACS,IAAI,CAACnB,aAAa,EAAEc,GAAG,CAAC,CAAC;MAC7J,CAAO,MAAM;QACLf,IAAI,CAACqB,kBAAkB,EAAEN,GAAG,CAAC;QAC7BnB,SAAS,CAACgB,KAAK,GAAGG,GAAG;MAC7B;IACA;EACA,CAAG,CAAC;EACF,OAAO;IACLP,KAAK;IACLJ,OAAO;IACPG;EACJ,CAAG;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
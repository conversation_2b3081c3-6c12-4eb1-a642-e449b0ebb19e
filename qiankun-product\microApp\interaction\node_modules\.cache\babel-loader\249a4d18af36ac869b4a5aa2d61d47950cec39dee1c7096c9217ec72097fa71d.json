{"ast": null, "code": "import { getCurrentInstance, shallowRef, ref, onMounted, watch, computed } from 'vue';\nimport '../../../../utils/index.mjs';\nimport '../../../../hooks/index.mjs';\nimport { draggable } from '../utils/draggable.mjs';\nimport { getClientXY } from '../../../../utils/dom/position.mjs';\nimport { useNamespace } from '../../../../hooks/use-namespace/index.mjs';\nimport { addUnit } from '../../../../utils/dom/style.mjs';\nvar useAlphaSlider = function useAlphaSlider(props) {\n  var instance = getCurrentInstance();\n  var thumb = shallowRef();\n  var bar = shallowRef();\n  function handleClick(event) {\n    var target = event.target;\n    if (target !== thumb.value) {\n      handleDrag(event);\n    }\n  }\n  function handleDrag(event) {\n    if (!bar.value || !thumb.value) return;\n    var el = instance.vnode.el;\n    var rect = el.getBoundingClientRect();\n    var _getClientXY = getClientXY(event),\n      clientX = _getClientXY.clientX,\n      clientY = _getClientXY.clientY;\n    if (!props.vertical) {\n      var left = clientX - rect.left;\n      left = Math.max(thumb.value.offsetWidth / 2, left);\n      left = Math.min(left, rect.width - thumb.value.offsetWidth / 2);\n      props.color.set(\"alpha\", Math.round((left - thumb.value.offsetWidth / 2) / (rect.width - thumb.value.offsetWidth) * 100));\n    } else {\n      var top = clientY - rect.top;\n      top = Math.max(thumb.value.offsetHeight / 2, top);\n      top = Math.min(top, rect.height - thumb.value.offsetHeight / 2);\n      props.color.set(\"alpha\", Math.round((top - thumb.value.offsetHeight / 2) / (rect.height - thumb.value.offsetHeight) * 100));\n    }\n  }\n  return {\n    thumb,\n    bar,\n    handleDrag,\n    handleClick\n  };\n};\nvar useAlphaSliderDOM = function useAlphaSliderDOM(props, _ref) {\n  var bar = _ref.bar,\n    thumb = _ref.thumb,\n    handleDrag = _ref.handleDrag;\n  var instance = getCurrentInstance();\n  var ns = useNamespace(\"color-alpha-slider\");\n  var thumbLeft = ref(0);\n  var thumbTop = ref(0);\n  var background = ref();\n  function getThumbLeft() {\n    if (!thumb.value) return 0;\n    if (props.vertical) return 0;\n    var el = instance.vnode.el;\n    var alpha = props.color.get(\"alpha\");\n    if (!el) return 0;\n    return Math.round(alpha * (el.offsetWidth - thumb.value.offsetWidth / 2) / 100);\n  }\n  function getThumbTop() {\n    if (!thumb.value) return 0;\n    var el = instance.vnode.el;\n    if (!props.vertical) return 0;\n    var alpha = props.color.get(\"alpha\");\n    if (!el) return 0;\n    return Math.round(alpha * (el.offsetHeight - thumb.value.offsetHeight / 2) / 100);\n  }\n  function getBackground() {\n    if (props.color && props.color.value) {\n      var _props$color$toRgb = props.color.toRgb(),\n        r = _props$color$toRgb.r,\n        g = _props$color$toRgb.g,\n        b = _props$color$toRgb.b;\n      return `linear-gradient(to right, rgba(${r}, ${g}, ${b}, 0) 0%, rgba(${r}, ${g}, ${b}, 1) 100%)`;\n    }\n    return \"\";\n  }\n  function update() {\n    thumbLeft.value = getThumbLeft();\n    thumbTop.value = getThumbTop();\n    background.value = getBackground();\n  }\n  onMounted(function () {\n    if (!bar.value || !thumb.value) return;\n    var dragConfig = {\n      drag: function drag(event) {\n        handleDrag(event);\n      },\n      end: function end(event) {\n        handleDrag(event);\n      }\n    };\n    draggable(bar.value, dragConfig);\n    draggable(thumb.value, dragConfig);\n    update();\n  });\n  watch(function () {\n    return props.color.get(\"alpha\");\n  }, function () {\n    return update();\n  });\n  watch(function () {\n    return props.color.value;\n  }, function () {\n    return update();\n  });\n  var rootKls = computed(function () {\n    return [ns.b(), ns.is(\"vertical\", props.vertical)];\n  });\n  var barKls = computed(function () {\n    return ns.e(\"bar\");\n  });\n  var thumbKls = computed(function () {\n    return ns.e(\"thumb\");\n  });\n  var barStyle = computed(function () {\n    return {\n      background: background.value\n    };\n  });\n  var thumbStyle = computed(function () {\n    return {\n      left: addUnit(thumbLeft.value),\n      top: addUnit(thumbTop.value)\n    };\n  });\n  return {\n    rootKls,\n    barKls,\n    barStyle,\n    thumbKls,\n    thumbStyle,\n    update\n  };\n};\nexport { useAlphaSlider, useAlphaSliderDOM };", "map": {"version": 3, "names": ["useAlphaSlider", "props", "instance", "getCurrentInstance", "thumb", "shallowRef", "bar", "handleClick", "event", "target", "value", "handleDrag", "el", "vnode", "rect", "getBoundingClientRect", "_getClientXY", "getClientXY", "clientX", "clientY", "vertical", "left", "Math", "max", "offsetWidth", "min", "width", "color", "set", "round", "top", "offsetHeight", "height", "useAlphaSliderDOM", "_ref", "ns", "useNamespace", "thumbLeft", "ref", "thumbTop", "background", "getThumbLeft", "alpha", "get", "getThumbTop", "getBackground", "_props$color$toRgb", "toRgb", "r", "g", "b", "update", "onMounted", "dragConfig", "drag", "end", "draggable", "watch", "rootKls", "computed", "is", "barKls", "e", "thumbKls", "barStyle", "thumbStyle", "addUnit"], "sources": ["../../../../../../../packages/components/color-picker/src/composables/use-alpha-slider.ts"], "sourcesContent": ["import {\n  computed,\n  getCurrentInstance,\n  onMounted,\n  ref,\n  shallowRef,\n  watch,\n} from 'vue'\nimport { addUnit, getClientXY } from '@element-plus/utils'\nimport { useNamespace } from '@element-plus/hooks'\nimport { draggable } from '../utils/draggable'\n\nimport type { AlphaSliderProps } from '../props/alpha-slider'\n\nexport const useAlphaSlider = (props: AlphaSliderProps) => {\n  const instance = getCurrentInstance()!\n\n  const thumb = shallowRef<HTMLElement>()\n  const bar = shallowRef<HTMLElement>()\n\n  function handleClick(event: MouseEvent | TouchEvent) {\n    const target = event.target\n\n    if (target !== thumb.value) {\n      handleDrag(event)\n    }\n  }\n\n  function handleDrag(event: MouseEvent | TouchEvent) {\n    if (!bar.value || !thumb.value) return\n\n    const el = instance.vnode.el as HTMLElement\n    const rect = el.getBoundingClientRect()\n    const { clientX, clientY } = getClientXY(event)\n\n    if (!props.vertical) {\n      let left = clientX - rect.left\n      left = Math.max(thumb.value.offsetWidth / 2, left)\n      left = Math.min(left, rect.width - thumb.value.offsetWidth / 2)\n\n      props.color.set(\n        'alpha',\n        Math.round(\n          ((left - thumb.value.offsetWidth / 2) /\n            (rect.width - thumb.value.offsetWidth)) *\n            100\n        )\n      )\n    } else {\n      let top = clientY - rect.top\n      top = Math.max(thumb.value.offsetHeight / 2, top)\n      top = Math.min(top, rect.height - thumb.value.offsetHeight / 2)\n\n      props.color.set(\n        'alpha',\n        Math.round(\n          ((top - thumb.value.offsetHeight / 2) /\n            (rect.height - thumb.value.offsetHeight)) *\n            100\n        )\n      )\n    }\n  }\n\n  return {\n    thumb,\n    bar,\n    handleDrag,\n    handleClick,\n  }\n}\n\nexport const useAlphaSliderDOM = (\n  props: AlphaSliderProps,\n  {\n    bar,\n    thumb,\n    handleDrag,\n  }: Pick<ReturnType<typeof useAlphaSlider>, 'bar' | 'thumb' | 'handleDrag'>\n) => {\n  const instance = getCurrentInstance()!\n\n  const ns = useNamespace('color-alpha-slider')\n  // refs\n\n  const thumbLeft = ref(0)\n  const thumbTop = ref(0)\n  const background = ref<string>()\n\n  function getThumbLeft() {\n    if (!thumb.value) return 0\n\n    if (props.vertical) return 0\n    const el = instance.vnode.el\n    const alpha = props.color.get('alpha')\n\n    if (!el) return 0\n    return Math.round(\n      (alpha * (el.offsetWidth - thumb.value.offsetWidth / 2)) / 100\n    )\n  }\n\n  function getThumbTop() {\n    if (!thumb.value) return 0\n\n    const el = instance.vnode.el\n    if (!props.vertical) return 0\n    const alpha = props.color.get('alpha')\n\n    if (!el) return 0\n    return Math.round(\n      (alpha * (el.offsetHeight - thumb.value.offsetHeight / 2)) / 100\n    )\n  }\n\n  function getBackground() {\n    if (props.color && props.color.value) {\n      const { r, g, b } = props.color.toRgb()\n      return `linear-gradient(to right, rgba(${r}, ${g}, ${b}, 0) 0%, rgba(${r}, ${g}, ${b}, 1) 100%)`\n    }\n    return ''\n  }\n\n  function update() {\n    thumbLeft.value = getThumbLeft()\n    thumbTop.value = getThumbTop()\n    background.value = getBackground()\n  }\n\n  onMounted(() => {\n    if (!bar.value || !thumb.value) return\n\n    const dragConfig = {\n      drag: (event: MouseEvent | TouchEvent) => {\n        handleDrag(event)\n      },\n      end: (event: MouseEvent | TouchEvent) => {\n        handleDrag(event)\n      },\n    }\n\n    draggable(bar.value, dragConfig)\n    draggable(thumb.value, dragConfig)\n    update()\n  })\n\n  watch(\n    () => props.color.get('alpha'),\n    () => update()\n  )\n  watch(\n    () => props.color.value,\n    () => update()\n  )\n\n  const rootKls = computed(() => [ns.b(), ns.is('vertical', props.vertical)])\n  const barKls = computed(() => ns.e('bar'))\n  const thumbKls = computed(() => ns.e('thumb'))\n  const barStyle = computed(() => ({ background: background.value }))\n  const thumbStyle = computed(() => ({\n    left: addUnit(thumbLeft.value),\n    top: addUnit(thumbTop.value),\n  }))\n\n  return { rootKls, barKls, barStyle, thumbKls, thumbStyle, update }\n}\n"], "mappings": ";;;;;;;AAWY,IAACA,cAAc,GAAG,SAAjBA,cAAcA,CAAIC,KAAK,EAAK;EACvC,IAAMC,QAAQ,GAAGC,kBAAkB,EAAE;EACrC,IAAMC,KAAK,GAAGC,UAAU,EAAE;EAC1B,IAAMC,GAAG,GAAGD,UAAU,EAAE;EACxB,SAASE,WAAWA,CAACC,KAAK,EAAE;IAC1B,IAAMC,MAAM,GAAGD,KAAK,CAACC,MAAM;IAC3B,IAAIA,MAAM,KAAKL,KAAK,CAACM,KAAK,EAAE;MAC1BC,UAAU,CAACH,KAAK,CAAC;IACvB;EACA;EACE,SAASG,UAAUA,CAACH,KAAK,EAAE;IACzB,IAAI,CAACF,GAAG,CAACI,KAAK,IAAI,CAACN,KAAK,CAACM,KAAK,EAC5B;IACF,IAAME,EAAE,GAAGV,QAAQ,CAACW,KAAK,CAACD,EAAE;IAC5B,IAAME,IAAI,GAAGF,EAAE,CAACG,qBAAqB,EAAE;IACvC,IAAAC,YAAA,GAA6BC,WAAW,CAACT,KAAK,CAAC;MAAvCU,OAAO,GAAAF,YAAA,CAAPE,OAAO;MAAEC,OAAO,GAAAH,YAAA,CAAPG,OAAO;IACxB,IAAI,CAAClB,KAAK,CAACmB,QAAQ,EAAE;MACnB,IAAIC,IAAI,GAAGH,OAAO,GAAGJ,IAAI,CAACO,IAAI;MAC9BA,IAAI,GAAGC,IAAI,CAACC,GAAG,CAACnB,KAAK,CAACM,KAAK,CAACc,WAAW,GAAG,CAAC,EAAEH,IAAI,CAAC;MAClDA,IAAI,GAAGC,IAAI,CAACG,GAAG,CAACJ,IAAI,EAAEP,IAAI,CAACY,KAAK,GAAGtB,KAAK,CAACM,KAAK,CAACc,WAAW,GAAG,CAAC,CAAC;MAC/DvB,KAAK,CAAC0B,KAAK,CAACC,GAAG,CAAC,OAAO,EAAEN,IAAI,CAACO,KAAK,CAAC,CAACR,IAAI,GAAGjB,KAAK,CAACM,KAAK,CAACc,WAAW,GAAG,CAAC,KAAKV,IAAI,CAACY,KAAK,GAAGtB,KAAK,CAACM,KAAK,CAACc,WAAW,CAAC,GAAG,GAAG,CAAC,CAAC;IAC/H,CAAK,MAAM;MACL,IAAIM,GAAG,GAAGX,OAAO,GAAGL,IAAI,CAACgB,GAAG;MAC5BA,GAAG,GAAGR,IAAI,CAACC,GAAG,CAACnB,KAAK,CAACM,KAAK,CAACqB,YAAY,GAAG,CAAC,EAAED,GAAG,CAAC;MACjDA,GAAG,GAAGR,IAAI,CAACG,GAAG,CAACK,GAAG,EAAEhB,IAAI,CAACkB,MAAM,GAAG5B,KAAK,CAACM,KAAK,CAACqB,YAAY,GAAG,CAAC,CAAC;MAC/D9B,KAAK,CAAC0B,KAAK,CAACC,GAAG,CAAC,OAAO,EAAEN,IAAI,CAACO,KAAK,CAAC,CAACC,GAAG,GAAG1B,KAAK,CAACM,KAAK,CAACqB,YAAY,GAAG,CAAC,KAAKjB,IAAI,CAACkB,MAAM,GAAG5B,KAAK,CAACM,KAAK,CAACqB,YAAY,CAAC,GAAG,GAAG,CAAC,CAAC;IACjI;EACA;EACE,OAAO;IACL3B,KAAK;IACLE,GAAG;IACHK,UAAU;IACVJ;EACJ,CAAG;AACH;AACY,IAAC0B,iBAAiB,GAAG,SAApBA,iBAAiBA,CAAIhC,KAAK,EAAAiC,IAAA,EAIjC;EAAA,IAHJ5B,GAAG,GAAA4B,IAAA,CAAH5B,GAAG;IACHF,KAAK,GAAA8B,IAAA,CAAL9B,KAAK;IACLO,UAAU,GAAAuB,IAAA,CAAVvB,UAAU;EAEV,IAAMT,QAAQ,GAAGC,kBAAkB,EAAE;EACrC,IAAMgC,EAAE,GAAGC,YAAY,CAAC,oBAAoB,CAAC;EAC7C,IAAMC,SAAS,GAAGC,GAAG,CAAC,CAAC,CAAC;EACxB,IAAMC,QAAQ,GAAGD,GAAG,CAAC,CAAC,CAAC;EACvB,IAAME,UAAU,GAAGF,GAAG,EAAE;EACxB,SAASG,YAAYA,CAAA,EAAG;IACtB,IAAI,CAACrC,KAAK,CAACM,KAAK,EACd,OAAO,CAAC;IACV,IAAIT,KAAK,CAACmB,QAAQ,EAChB,OAAO,CAAC;IACV,IAAMR,EAAE,GAAGV,QAAQ,CAACW,KAAK,CAACD,EAAE;IAC5B,IAAM8B,KAAK,GAAGzC,KAAK,CAAC0B,KAAK,CAACgB,GAAG,CAAC,OAAO,CAAC;IACtC,IAAI,CAAC/B,EAAE,EACL,OAAO,CAAC;IACV,OAAOU,IAAI,CAACO,KAAK,CAACa,KAAK,IAAI9B,EAAE,CAACY,WAAW,GAAGpB,KAAK,CAACM,KAAK,CAACc,WAAW,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC;EACnF;EACE,SAASoB,WAAWA,CAAA,EAAG;IACrB,IAAI,CAACxC,KAAK,CAACM,KAAK,EACd,OAAO,CAAC;IACV,IAAME,EAAE,GAAGV,QAAQ,CAACW,KAAK,CAACD,EAAE;IAC5B,IAAI,CAACX,KAAK,CAACmB,QAAQ,EACjB,OAAO,CAAC;IACV,IAAMsB,KAAK,GAAGzC,KAAK,CAAC0B,KAAK,CAACgB,GAAG,CAAC,OAAO,CAAC;IACtC,IAAI,CAAC/B,EAAE,EACL,OAAO,CAAC;IACV,OAAOU,IAAI,CAACO,KAAK,CAACa,KAAK,IAAI9B,EAAE,CAACmB,YAAY,GAAG3B,KAAK,CAACM,KAAK,CAACqB,YAAY,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC;EACrF;EACE,SAASc,aAAaA,CAAA,EAAG;IACvB,IAAI5C,KAAK,CAAC0B,KAAK,IAAI1B,KAAK,CAAC0B,KAAK,CAACjB,KAAK,EAAE;MACpC,IAAAoC,kBAAA,GAAoB7C,KAAK,CAAC0B,KAAK,CAACoB,KAAK,EAAE;QAA/BC,CAAC,GAAAF,kBAAA,CAADE,CAAC;QAAEC,CAAC,GAAAH,kBAAA,CAADG,CAAC;QAAEC,CAAC,GAAAJ,kBAAA,CAADI,CAAC;MACf,OAAO,kCAAkCF,CAAC,KAAKC,CAAC,KAAKC,CAAC,iBAAiBF,CAAC,KAAKC,CAAC,KAAKC,CAAC,YAAY;IACtG;IACI,OAAO,EAAE;EACb;EACE,SAASC,MAAMA,CAAA,EAAG;IAChBd,SAAS,CAAC3B,KAAK,GAAG+B,YAAY,EAAE;IAChCF,QAAQ,CAAC7B,KAAK,GAAGkC,WAAW,EAAE;IAC9BJ,UAAU,CAAC9B,KAAK,GAAGmC,aAAa,EAAE;EACtC;EACEO,SAAS,CAAC,YAAM;IACd,IAAI,CAAC9C,GAAG,CAACI,KAAK,IAAI,CAACN,KAAK,CAACM,KAAK,EAC5B;IACF,IAAM2C,UAAU,GAAG;MACjBC,IAAI,EAAE,SAANA,IAAIA,CAAG9C,KAAK,EAAK;QACfG,UAAU,CAACH,KAAK,CAAC;MACzB,CAAO;MACD+C,GAAG,EAAE,SAALA,GAAGA,CAAG/C,KAAK,EAAK;QACdG,UAAU,CAACH,KAAK,CAAC;MACzB;IACA,CAAK;IACDgD,SAAS,CAAClD,GAAG,CAACI,KAAK,EAAE2C,UAAU,CAAC;IAChCG,SAAS,CAACpD,KAAK,CAACM,KAAK,EAAE2C,UAAU,CAAC;IAClCF,MAAM,EAAE;EACZ,CAAG,CAAC;EACFM,KAAK,CAAC;IAAA,OAAMxD,KAAK,CAAC0B,KAAK,CAACgB,GAAG,CAAC,OAAO,CAAC;EAAA,GAAE;IAAA,OAAMQ,MAAM,EAAE;EAAA,EAAC;EACrDM,KAAK,CAAC;IAAA,OAAMxD,KAAK,CAAC0B,KAAK,CAACjB,KAAK;EAAA,GAAE;IAAA,OAAMyC,MAAM,EAAE;EAAA,EAAC;EAC9C,IAAMO,OAAO,GAAGC,QAAQ,CAAC;IAAA,OAAM,CAACxB,EAAE,CAACe,CAAC,EAAE,EAAEf,EAAE,CAACyB,EAAE,CAAC,UAAU,EAAE3D,KAAK,CAACmB,QAAQ,CAAC,CAAC;EAAA,EAAC;EAC3E,IAAMyC,MAAM,GAAGF,QAAQ,CAAC;IAAA,OAAMxB,EAAE,CAAC2B,CAAC,CAAC,KAAK,CAAC;EAAA,EAAC;EAC1C,IAAMC,QAAQ,GAAGJ,QAAQ,CAAC;IAAA,OAAMxB,EAAE,CAAC2B,CAAC,CAAC,OAAO,CAAC;EAAA,EAAC;EAC9C,IAAME,QAAQ,GAAGL,QAAQ,CAAC;IAAA,OAAO;MAAEnB,UAAU,EAAEA,UAAU,CAAC9B;IAAK,CAAE;EAAA,CAAC,CAAC;EACnE,IAAMuD,UAAU,GAAGN,QAAQ,CAAC;IAAA,OAAO;MACjCtC,IAAI,EAAE6C,OAAO,CAAC7B,SAAS,CAAC3B,KAAK,CAAC;MAC9BoB,GAAG,EAAEoC,OAAO,CAAC3B,QAAQ,CAAC7B,KAAK;IAC/B,CAAG;EAAA,CAAC,CAAC;EACH,OAAO;IAAEgD,OAAO;IAAEG,MAAM;IAAEG,QAAQ;IAAED,QAAQ;IAAEE,UAAU;IAAEd;EAAM,CAAE;AACpE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
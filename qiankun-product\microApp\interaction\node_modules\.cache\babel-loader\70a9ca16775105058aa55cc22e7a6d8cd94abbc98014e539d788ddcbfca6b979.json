{"ast": null, "code": "import { createElementVNode as _createElementVNode, createCommentVNode as _createCommentVNode, normalizeStyle as _normalizeStyle, renderList as _renderList, Fragment as _Fragment, openBlock as _openBlock, createElementBlock as _createElementBlock, toDisplayString as _toDisplayString, resolveComponent as _resolveComponent, withModifiers as _withModifiers, createVNode as _createVNode, normalizeClass as _normalizeClass, withCtx as _withCtx } from \"vue\";\nvar _hoisted_1 = {\n  class: \"virtual-user\",\n  ref: \"virtualList\"\n};\nvar _hoisted_2 = {\n  class: \"virtual-placeholder\",\n  ref: \"virtualPlaceholder\"\n};\nvar _hoisted_3 = [\"onClick\"];\nvar _hoisted_4 = [\"title\"];\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  var _component_el_checkbox = _resolveComponent(\"el-checkbox\");\n  var _component_el_checkbox_group = _resolveComponent(\"el-checkbox-group\");\n  var _component_el_scrollbar = _resolveComponent(\"el-scrollbar\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createElementVNode(\"div\", _hoisted_2, _cache[2] || (_cache[2] = [_createElementVNode(\"div\", {\n    class: \"virtual-user-item\"\n  }, [_createElementVNode(\"div\", {\n    class: \"virtual-user-name ellipsis\"\n  }, \"占位\")], -1 /* HOISTED */)]), 512 /* NEED_PATCH */), _createVNode(_component_el_scrollbar, {\n    onScroll: $setup.handleScroll,\n    ref: \"virtualScrollbar\"\n  }, {\n    default: _withCtx(function () {\n      return [_createCommentVNode(\" 虚拟高度 \"), _createElementVNode(\"div\", {\n        class: \"virtualBody\",\n        style: _normalizeStyle({\n          height: $setup.virtualRecord.virtualHeight + 'px'\n        })\n      }, null, 4 /* STYLE */), _createCommentVNode(\" 真实列表 \"), _createVNode(_component_el_checkbox_group, {\n        modelValue: $setup.userId,\n        \"onUpdate:modelValue\": _cache[1] || (_cache[1] = function ($event) {\n          return $setup.userId = $event;\n        }),\n        class: _normalizeClass(['realBody']),\n        style: _normalizeStyle({\n          transform: `translateY(${$setup.virtualRecord.offset}px)`\n        }),\n        onChange: $setup.handleChange\n      }, {\n        default: _withCtx(function () {\n          return [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.virtualRecord.visibleData, function (item, index) {\n            return _openBlock(), _createElementBlock(\"div\", {\n              class: \"virtual-user-item\",\n              key: index + '_virtual-user',\n              onClick: function onClick($event) {\n                return $setup.handleClick(item);\n              }\n            }, [_createElementVNode(\"div\", {\n              class: \"virtual-user-name ellipsis\",\n              title: `${item.userName} - ${$setup.handleMobile(item.mobile)}`\n            }, _toDisplayString(item.userName) + \" - \" + _toDisplayString($setup.handleMobile(item.mobile)), 9 /* TEXT, PROPS */, _hoisted_4), _createVNode(_component_el_checkbox, {\n              onClick: _cache[0] || (_cache[0] = _withModifiers(function () {}, [\"stop\"])),\n              label: item[$setup.props.nodeKey],\n              disabled: $setup.disabledUser(item[$setup.props.nodeKey])\n            }, null, 8 /* PROPS */, [\"label\", \"disabled\"])], 8 /* PROPS */, _hoisted_3);\n          }), 128 /* KEYED_FRAGMENT */))];\n        }),\n        _: 1 /* STABLE */\n      }, 8 /* PROPS */, [\"modelValue\", \"style\"])];\n    }),\n    _: 1 /* STABLE */\n  }, 512 /* NEED_PATCH */)], 512 /* NEED_PATCH */);\n}", "map": {"version": 3, "names": ["class", "ref", "_createElementBlock", "_hoisted_1", "_createElementVNode", "_hoisted_2", "_cache", "_createVNode", "_component_el_scrollbar", "onScroll", "$setup", "handleScroll", "default", "_withCtx", "_createCommentVNode", "style", "_normalizeStyle", "height", "virtualRecord", "virtualHeight", "_component_el_checkbox_group", "modelValue", "userId", "$event", "_normalizeClass", "transform", "offset", "onChange", "handleChange", "_Fragment", "_renderList", "visibleData", "item", "index", "key", "onClick", "handleClick", "title", "userName", "handleMobile", "mobile", "_toDisplayString", "_hoisted_4", "_component_el_checkbox", "_withModifiers", "label", "props", "nodeKey", "disabled", "disabledUser", "_hoisted_3", "_"], "sources": ["D:\\zy\\xm\\pc\\5.0\\qiankun-product\\common\\components\\virtualElement\\virtual-user.vue"], "sourcesContent": ["<template>\r\n  <div class=\"virtual-user\" ref=\"virtualList\">\r\n    <div class=\"virtual-placeholder\" ref=\"virtualPlaceholder\">\r\n      <div class=\"virtual-user-item\">\r\n        <div class=\"virtual-user-name ellipsis\">占位</div>\r\n      </div>\r\n    </div>\r\n    <el-scrollbar @scroll=\"handleScroll\" ref=\"virtualScrollbar\">\r\n      <!-- 虚拟高度 -->\r\n      <div class=\"virtualBody\" :style=\"{ height: virtualRecord.virtualHeight + 'px' }\"></div>\r\n      <!-- 真实列表 -->\r\n      <el-checkbox-group\r\n        v-model=\"userId\"\r\n        :class=\"['realBody']\"\r\n        :style=\"{ transform: `translateY(${virtualRecord.offset}px)` }\"\r\n        @change=\"handleChange\">\r\n        <div\r\n          class=\"virtual-user-item\"\r\n          v-for=\"(item, index) in virtualRecord.visibleData\"\r\n          :key=\"index + '_virtual-user'\"\r\n          @click=\"handleClick(item)\">\r\n          <div class=\"virtual-user-name ellipsis\" :title=\"`${item.userName} - ${handleMobile(item.mobile)}`\">\r\n            {{ item.userName }} - {{ handleMobile(item.mobile) }}\r\n          </div>\r\n          <el-checkbox\r\n            @click.stop\r\n            :label=\"item[props.nodeKey]\"\r\n            :disabled=\"disabledUser(item[props.nodeKey])\"></el-checkbox>\r\n        </div>\r\n      </el-checkbox-group>\r\n    </el-scrollbar>\r\n  </div>\r\n</template>\r\n<script>\r\nexport default { name: 'VirtualUser' }\r\n</script>\r\n<script setup>\r\nimport { ref, reactive, computed, watch, nextTick, onMounted } from 'vue'\r\nimport { encryptPhone } from 'common/js/utils.js'\r\nimport { systemMobileEncrypt } from 'common/js/system_var.js'\r\nimport elementResizeDetectorMaker from 'element-resize-detector'\r\nconst erd = elementResizeDetectorMaker()\r\nconst props = defineProps({\r\n  modelValue: { type: Array, default: () => [] },\r\n  data: { type: Array, default: () => [] },\r\n  disabledUser: Function,\r\n  nodeKey: { type: String, default: 'id' }\r\n})\r\nconst emit = defineEmits(['update:modelValue', 'handleChange'])\r\nconst virtualList = ref()\r\nconst virtualScrollbar = ref()\r\nconst virtualPlaceholder = ref()\r\nconst userId = computed({\r\n  get() {\r\n    return props.modelValue\r\n  },\r\n  set(value) {\r\n    emit('update:modelValue', value)\r\n  }\r\n})\r\n// 组件记录(默认)\r\nconst virtualRecord = reactive({\r\n  height: 400,\r\n  // 展示几个\r\n  visibleCount: 66,\r\n  // 刷新频率\r\n  timeout: 4,\r\n  // 行高\r\n  itemHeight: 50,\r\n  // translateY偏移量\r\n  offset: 0,\r\n  // 虚拟占位高度\r\n  virtualHeight: 300,\r\n  // 记录滚动高度\r\n  recordScrollTop: 0,\r\n  dataList: [],\r\n  // 可展示的数据\r\n  visibleData: []\r\n})\r\n// 合并配置\r\nconst mergeFn = () => {\r\n  virtualRecord.dataList = JSON.parse(JSON.stringify(props.data))\r\n  // 虚拟高度\r\n  virtualRecord.virtualHeight = props.data.length * virtualRecord.itemHeight\r\n  // 展示数量\r\n  virtualRecord.visibleCount = Math.ceil(virtualRecord.height / virtualRecord.itemHeight)\r\n}\r\nlet lastTime = 0\r\nconst handleScroll = (scroll) => {\r\n  const currentTime = +new Date()\r\n  if (currentTime - lastTime > virtualRecord.timeout) {\r\n    virtualRecord.recordScrollTop = scroll.scrollTop\r\n    updateVisibleData(scroll.scrollTop)\r\n    lastTime = currentTime\r\n  }\r\n}\r\nconst updateVisibleData = (scrollTop) => {\r\n  let start = Math.floor(scrollTop / virtualRecord.itemHeight) - Math.floor(virtualRecord.visibleCount / 2)\r\n  start = start < 0 ? 0 : start\r\n  const end = start + virtualRecord.visibleCount * 2\r\n  virtualRecord.visibleData = virtualRecord.dataList.slice(start, end)\r\n  virtualRecord.offset = start * virtualRecord.itemHeight\r\n  nextTick(() => {\r\n    virtualScrollbar.value.update()\r\n  })\r\n}\r\nconst handleClick = (item) => {\r\n  if (disabledUser(item[props.nodeKey])) return\r\n  let newUserId = []\r\n  if (userId.value.includes(item[props.nodeKey])) {\r\n    for (let index = 0; index < userId.value.length; index++) {\r\n      if (userId.value[index] !== item[props.nodeKey]) {\r\n        newUserId.push(userId.value[index])\r\n      }\r\n    }\r\n  } else {\r\n    newUserId = [...userId.value, item[props.nodeKey]]\r\n  }\r\n  userId.value = newUserId\r\n  emit('handleChange', newUserId)\r\n}\r\nconst handleChange = (value) => {\r\n  emit('handleChange', value)\r\n}\r\nconst disabledUser = (id) => {\r\n  if (typeof props.disabledUser === 'function') {\r\n    return props.disabledUser(id)\r\n  } else {\r\n    return false\r\n  }\r\n}\r\nconst handleMobile = (mobile) => (systemMobileEncrypt.value ? encryptPhone(mobile) : mobile)\r\nwatch(\r\n  () => props.data,\r\n  () => {\r\n    // 合并数据\r\n    mergeFn()\r\n    // 更新视图\r\n    updateVisibleData(virtualRecord.recordScrollTop)\r\n  },\r\n  { immediate: true, deep: true }\r\n)\r\nonMounted(() => {\r\n  nextTick(() => {\r\n    erd.listenTo(virtualList.value, (e) => {\r\n      virtualRecord.height = e.offsetHeight\r\n      // 合并数据\r\n      mergeFn()\r\n      // 更新视图\r\n      updateVisibleData(virtualRecord.recordScrollTop)\r\n    })\r\n    erd.listenTo(virtualPlaceholder.value, (e) => {\r\n      virtualRecord.itemHeight = e.offsetHeight\r\n      // 合并数据\r\n      mergeFn()\r\n      // 更新视图\r\n      updateVisibleData(virtualRecord.recordScrollTop)\r\n    })\r\n  })\r\n})\r\n</script>\r\n<style lang=\"scss\">\r\n.virtual-user {\r\n  width: 100%;\r\n  height: calc(100% - ((var(--zy-name-font-size) * var(--zy-line-height)) + (var(--zy-distance-four) * 2)));\r\n\r\n  .virtual-placeholder {\r\n    position: fixed;\r\n    top: -200%;\r\n    left: -200%;\r\n  }\r\n\r\n  .zy-el-scrollbar {\r\n    height: 100%;\r\n\r\n    .zy-el-scrollbar__view {\r\n      position: relative;\r\n    }\r\n\r\n    .virtualBody {\r\n      width: 100%;\r\n      position: absolute;\r\n      z-index: -10;\r\n    }\r\n\r\n    .realBody {\r\n      width: 100%;\r\n      position: absolute;\r\n    }\r\n  }\r\n\r\n  .virtual-user-item {\r\n    display: flex;\r\n    align-items: center;\r\n    cursor: pointer;\r\n\r\n    .virtual-user-name {\r\n      width: calc(100% - 40px);\r\n      font-size: var(--zy-text-font-size);\r\n      line-height: var(--zy-line-height);\r\n      position: relative;\r\n      padding: var(--zy-distance-four) 0;\r\n      padding-left: var(--zy-distance-one);\r\n\r\n      &::after {\r\n        content: '';\r\n        position: absolute;\r\n        top: 50%;\r\n        left: var(--zy-distance-two);\r\n        width: var(--zy-text-font-size);\r\n        height: var(--zy-text-font-size);\r\n        transform: translate(-50%, -50%);\r\n        background: url('./img/select_person_user_icon.png') no-repeat;\r\n        background-size: 100% 100%;\r\n      }\r\n    }\r\n\r\n    .zy-el-checkbox {\r\n      width: 40px;\r\n      display: flex;\r\n      justify-content: flex-end;\r\n      padding-right: var(--zy-distance-three);\r\n\r\n      .zy-el-checkbox__label {\r\n        display: none;\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;EACOA,KAAK,EAAC,cAAc;EAACC,GAAG,EAAC;;;EACvBD,KAAK,EAAC,qBAAqB;EAACC,GAAG,EAAC;;iBAFzC;iBAAA;;;;;uBACEC,mBAAA,CA8BM,OA9BNC,UA8BM,GA7BJC,mBAAA,CAIM,OAJNC,UAIM,EAAAC,MAAA,QAAAA,MAAA,OAHJF,mBAAA,CAEM;IAFDJ,KAAK,EAAC;EAAmB,IAC5BI,mBAAA,CAAgD;IAA3CJ,KAAK,EAAC;EAA4B,GAAC,IAAE,E,8CAG9CO,YAAA,CAuBeC,uBAAA;IAvBAC,QAAM,EAAEC,MAAA,CAAAC,YAAY;IAAEV,GAAG,EAAC;;IAP7CW,OAAA,EAAAC,QAAA,CAQM;MAAA,OAAa,CAAbC,mBAAA,UAAa,EACbV,mBAAA,CAAuF;QAAlFJ,KAAK,EAAC,aAAa;QAAEe,KAAK,EATrCC,eAAA;UAAAC,MAAA,EASiDP,MAAA,CAAAQ,aAAa,CAACC,aAAa;QAAA;+BACtEL,mBAAA,UAAa,EACbP,YAAA,CAkBoBa,4BAAA;QA7B1BC,UAAA,EAYiBX,MAAA,CAAAY,MAAM;QAZvB,uBAAAhB,MAAA,QAAAA,MAAA,gBAAAiB,MAAA;UAAA,OAYiBb,MAAA,CAAAY,MAAM,GAAAC,MAAA;QAAA;QACdvB,KAAK,EAbdwB,eAAA,CAagB,YAAY;QACnBT,KAAK,EAddC,eAAA;UAAAS,SAAA,gBAc2Cf,MAAA,CAAAQ,aAAa,CAACQ,MAAM;QAAA;QACtDC,QAAM,EAAEjB,MAAA,CAAAkB;;QAfjBhB,OAAA,EAAAC,QAAA,CAkBU;UAAA,OAAkD,E,kBAFpDX,mBAAA,CAYM2B,SAAA,QA5BdC,WAAA,CAkBkCpB,MAAA,CAAAQ,aAAa,CAACa,WAAW,EAlB3D,UAkBkBC,IAAI,EAAEC,KAAK;iCAFrB/B,mBAAA,CAYM;cAXJF,KAAK,EAAC,mBAAmB;cAExBkC,GAAG,EAAED,KAAK;cACVE,OAAK,WAALA,OAAKA,CAAAZ,MAAA;gBAAA,OAAEb,MAAA,CAAA0B,WAAW,CAACJ,IAAI;cAAA;gBACxB5B,mBAAA,CAEM;cAFDJ,KAAK,EAAC,4BAA4B;cAAEqC,KAAK,KAAKL,IAAI,CAACM,QAAQ,MAAM5B,MAAA,CAAA6B,YAAY,CAACP,IAAI,CAACQ,MAAM;gCACzFR,IAAI,CAACM,QAAQ,IAAG,KAAG,GAAAG,gBAAA,CAAG/B,MAAA,CAAA6B,YAAY,CAACP,IAAI,CAACQ,MAAM,yBAtB7DE,UAAA,GAwBUnC,YAAA,CAG8DoC,sBAAA;cAF3DR,OAAK,EAAA7B,MAAA,QAAAA,MAAA,MAzBlBsC,cAAA,CAyBY,cAAW;cACVC,KAAK,EAAEb,IAAI,CAACtB,MAAA,CAAAoC,KAAK,CAACC,OAAO;cACzBC,QAAQ,EAAEtC,MAAA,CAAAuC,YAAY,CAACjB,IAAI,CAACtB,MAAA,CAAAoC,KAAK,CAACC,OAAO;4EA3BtDG,UAAA;;;QAAAC,CAAA;;;IAAAA,CAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
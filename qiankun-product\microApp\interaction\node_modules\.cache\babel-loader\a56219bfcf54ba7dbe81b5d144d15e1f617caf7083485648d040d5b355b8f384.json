{"ast": null, "code": "import { createVNode as _createVNode, normalizeStyle as _normalizeStyle, createElementVNode as _createElementVNode, resolveComponent as _resolveComponent, withCtx as _withCtx, createTextVNode as _createTextVNode, openBlock as _openBlock, createElementBlock as _createElementBlock } from \"vue\";\nvar _hoisted_1 = {\n  class: \"global-cropper forbidSelect\"\n};\nvar _hoisted_2 = {\n  class: \"global-cropper-body\"\n};\nvar _hoisted_3 = {\n  class: \"global-cropper-left\"\n};\nvar _hoisted_4 = {\n  class: \"global-cropper-controls\"\n};\nvar _hoisted_5 = {\n  class: \"global-cropper-right\"\n};\nvar _hoisted_6 = [\"src\"];\nvar _hoisted_7 = {\n  class: \"global-cropper-button\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  var _component_RefreshRight = _resolveComponent(\"RefreshRight\");\n  var _component_el_icon = _resolveComponent(\"el-icon\");\n  var _component_CirclePlus = _resolveComponent(\"CirclePlus\");\n  var _component_Remove = _resolveComponent(\"Remove\");\n  var _component_el_button = _resolveComponent(\"el-button\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createElementVNode(\"div\", _hoisted_2, [_createElementVNode(\"div\", _hoisted_3, [_createElementVNode(\"div\", {\n    class: \"global-element-cropper\",\n    style: _normalizeStyle($setup.getCropperStyle)\n  }, [_createVNode($setup[\"VueCropper\"], {\n    ref: \"cropperRef\",\n    img: $setup.cropperImgUrl,\n    \"auto-crop\": \"\",\n    \"fixed-box\": \"\",\n    info: false,\n    outputType: \"jpeg\",\n    enlarge: $setup.props.enlarge,\n    autoCropWidth: $setup.props.width,\n    autoCropHeight: $setup.props.height,\n    outputSize: 2,\n    onRealTime: $setup.previewHandle\n  }, null, 8 /* PROPS */, [\"img\", \"enlarge\", \"autoCropWidth\", \"autoCropHeight\"])], 4 /* STYLE */), _createElementVNode(\"div\", _hoisted_4, [_createVNode(_component_el_icon, {\n    class: \"rotate_right\",\n    onClick: $setup.rotateRight\n  }, {\n    default: _withCtx(function () {\n      return [_createVNode(_component_RefreshRight)];\n    }),\n    _: 1 /* STABLE */\n  }), _createVNode(_component_el_icon, {\n    class: \"rotate_right\",\n    onClick: _cache[0] || (_cache[0] = function ($event) {\n      return $setup.changeScale(1);\n    })\n  }, {\n    default: _withCtx(function () {\n      return [_createVNode(_component_CirclePlus)];\n    }),\n    _: 1 /* STABLE */\n  }), _createVNode(_component_el_icon, {\n    class: \"rotate_right\",\n    onClick: _cache[1] || (_cache[1] = function ($event) {\n      return $setup.changeScale(-1);\n    })\n  }, {\n    default: _withCtx(function () {\n      return [_createVNode(_component_Remove)];\n    }),\n    _: 1 /* STABLE */\n  })])]), _createElementVNode(\"div\", _hoisted_5, [_createElementVNode(\"div\", {\n    class: \"global-cropper-preview\",\n    style: _normalizeStyle($setup.getStyle)\n  }, [_createElementVNode(\"img\", {\n    style: _normalizeStyle($setup.previews.img),\n    src: $setup.previews.url\n  }, null, 12 /* STYLE, PROPS */, _hoisted_6)], 4 /* STYLE */)])]), _createElementVNode(\"div\", _hoisted_7, [_createVNode(_component_el_button, {\n    type: \"primary\",\n    onClick: $setup.handleConfirm\n  }, {\n    default: _withCtx(function () {\n      return _cache[2] || (_cache[2] = [_createTextVNode(\"确认\")]);\n    }),\n    _: 1 /* STABLE */\n  }), _createVNode(_component_el_button, {\n    onClick: $setup.handleCancel\n  }, {\n    default: _withCtx(function () {\n      return _cache[3] || (_cache[3] = [_createTextVNode(\"取消\")]);\n    }),\n    _: 1 /* STABLE */\n  })])]);\n}", "map": {"version": 3, "names": ["class", "_createElementBlock", "_hoisted_1", "_createElementVNode", "_hoisted_2", "_hoisted_3", "style", "_normalizeStyle", "$setup", "getCropperStyle", "_createVNode", "ref", "img", "cropperImgUrl", "info", "outputType", "enlarge", "props", "autoCropWidth", "width", "autoCropHeight", "height", "outputSize", "onRealTime", "previewHandle", "_hoisted_4", "_component_el_icon", "onClick", "rotateRight", "default", "_withCtx", "_component_RefreshRight", "_", "_cache", "$event", "changeScale", "_component_CirclePlus", "_component_Remove", "_hoisted_5", "getStyle", "previews", "src", "url", "_hoisted_6", "_hoisted_7", "_component_el_button", "type", "handleConfirm", "_createTextVNode", "handleCancel"], "sources": ["D:\\zy\\xm\\pc\\5.0\\qiankun-product\\common\\components\\global-cropper\\global-cropper.vue"], "sourcesContent": ["<template>\r\n  <div class=\"global-cropper forbidSelect\">\r\n    <div class=\"global-cropper-body\">\r\n      <div class=\"global-cropper-left\">\r\n        <div class=\"global-element-cropper\" :style=\"getCropperStyle\">\r\n          <vueCropper\r\n            ref=\"cropperRef\"\r\n            :img=\"cropperImgUrl\"\r\n            auto-crop\r\n            fixed-box\r\n            :info=\"false\"\r\n            outputType=\"jpeg\"\r\n            :enlarge=\"props.enlarge\"\r\n            :autoCropWidth=\"props.width\"\r\n            :autoCropHeight=\"props.height\"\r\n            :outputSize=\"2\"\r\n            @real-time=\"previewHandle\" />\r\n        </div>\r\n        <div class=\"global-cropper-controls\">\r\n          <el-icon class=\"rotate_right\" @click=\"rotateRight\">\r\n            <RefreshRight />\r\n          </el-icon>\r\n          <el-icon class=\"rotate_right\" @click=\"changeScale(1)\">\r\n            <CirclePlus />\r\n          </el-icon>\r\n          <el-icon class=\"rotate_right\" @click=\"changeScale(-1)\">\r\n            <Remove />\r\n          </el-icon>\r\n        </div>\r\n      </div>\r\n      <div class=\"global-cropper-right\">\r\n        <div class=\"global-cropper-preview\" :style=\"getStyle\">\r\n          <img :style=\"previews.img\" :src=\"previews.url\" />\r\n        </div>\r\n      </div>\r\n    </div>\r\n    <div class=\"global-cropper-button\">\r\n      <el-button type=\"primary\" @click=\"handleConfirm\">确认</el-button>\r\n      <el-button @click=\"handleCancel\">取消</el-button>\r\n    </div>\r\n  </div>\r\n</template>\r\n<script setup>\r\nimport { ref, computed, onMounted, onUnmounted } from 'vue'\r\nimport 'vue-cropper/dist/index.css'\r\nimport { VueCropper } from 'vue-cropper'\r\nconst props = defineProps({\r\n  file: { type: Object, default: () => ({}) },\r\n  enlarge: { type: Number, default: 1 },\r\n  width: { type: Number, default: 144 },\r\n  height: { type: Number, default: 192 },\r\n  cropperStyle: { type: Object, default: () => ({ width: '460px', height: '360px' }) }\r\n})\r\nconst emit = defineEmits(['callback'])\r\nconst getCropperStyle = computed(() => props.cropperStyle)\r\nconst getStyle = computed(() => ({ width: `${props.width}px`, height: `${props.height}px` }))\r\nconst cropperRef = ref()\r\nconst outputType = ref('')\r\nconst cropperImgUrl = ref('')\r\nconst previews = ref({})\r\n\r\nonMounted(() => {\r\n  const fileType = props.file.name.substring(props.file.name.lastIndexOf('.') + 1).toLowerCase()\r\n  if (['jpg', 'jpeg'].includes(fileType)) {\r\n    outputType.value = 'jpeg'\r\n  } else {\r\n    outputType.value = fileType\r\n  }\r\n  const URL = window.URL || window.webkitURL\r\n  cropperImgUrl.value = URL.createObjectURL(props.file)\r\n})\r\nonUnmounted(() => {\r\n  const URL = window.URL || window.webkitURL\r\n  URL.revokeObjectURL(cropperImgUrl.value)\r\n})\r\n/* 放大缩小图片比例 */\r\nconst changeScale = (num) => {\r\n  const scale = num || 1\r\n  cropperRef.value.changeScale(scale)\r\n}\r\n/* 右旋转图片 */\r\nconst rotateRight = () => {\r\n  cropperRef.value.rotateRight()\r\n}\r\nconst previewHandle = (data) => {\r\n  previews.value = data\r\n}\r\n// base64转图片文件\r\nconst dataURLtoFile = (dataUrl, filename) => {\r\n  const arr = dataUrl.split(',')\r\n  const mime = arr[0].match(/:(.*?);/)[1]\r\n  const bstr = atob(arr[1])\r\n  let len = bstr.length\r\n  const u8arr = new Uint8Array(len)\r\n  while (len--) {\r\n    u8arr[len] = bstr.charCodeAt(len)\r\n  }\r\n  return new File([u8arr], filename, { type: mime })\r\n}\r\nconst handleConfirm = () => {\r\n  cropperRef.value.getCropData(async (data) => {\r\n    const dataFile = dataURLtoFile(data, props.file.name)\r\n    emit('callback', dataFile)\r\n  })\r\n}\r\nconst handleCancel = () => {\r\n  emit('callback')\r\n}\r\n</script>\r\n<style lang=\"scss\">\r\n.global-cropper {\r\n  height: 460px;\r\n  padding: 20px;\r\n\r\n  .global-cropper-body {\r\n    width: 100%;\r\n    height: 380px;\r\n    display: flex;\r\n    justify-content: space-between;\r\n\r\n    .global-cropper-left {\r\n      height: 360px;\r\n      display: flex;\r\n\r\n      .global-element-cropper {\r\n        .vue-cropper {\r\n          width: 100%;\r\n          height: 100%;\r\n        }\r\n      }\r\n\r\n      .global-cropper-controls {\r\n        width: 38px;\r\n        display: flex;\r\n        flex-direction: column;\r\n        justify-content: flex-end;\r\n        padding-left: 6px;\r\n\r\n        .zy-el-icon {\r\n          font-size: 22px;\r\n          margin-top: 12px;\r\n          cursor: pointer;\r\n        }\r\n      }\r\n    }\r\n\r\n    .global-cropper-right {\r\n      .global-cropper-preview {\r\n        overflow: hidden;\r\n        border-radius: 2px;\r\n        border: 1px solid #e8e8e8;\r\n      }\r\n    }\r\n  }\r\n\r\n  .global-cropper-button {\r\n    width: 100%;\r\n    height: 40px;\r\n    display: flex;\r\n    align-items: flex-end;\r\n    justify-content: center;\r\n\r\n    .zy-el-button + .zy-el-button {\r\n      margin-left: var(--zy-distance-two);\r\n    }\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;EACOA,KAAK,EAAC;AAA6B;;EACjCA,KAAK,EAAC;AAAqB;;EACzBA,KAAK,EAAC;AAAqB;;EAezBA,KAAK,EAAC;AAAyB;;EAYjCA,KAAK,EAAC;AAAsB;iBA9BvC;;EAoCSA,KAAK,EAAC;AAAuB;;;;;;;uBAnCpCC,mBAAA,CAuCM,OAvCNC,UAuCM,GAtCJC,mBAAA,CAiCM,OAjCNC,UAiCM,GAhCJD,mBAAA,CA0BM,OA1BNE,UA0BM,GAzBJF,mBAAA,CAaM;IAbDH,KAAK,EAAC,wBAAwB;IAAEM,KAAK,EAJlDC,eAAA,CAIoDC,MAAA,CAAAC,eAAe;MACzDC,YAAA,CAW+BF,MAAA;IAV7BG,GAAG,EAAC,YAAY;IACfC,GAAG,EAAEJ,MAAA,CAAAK,aAAa;IACnB,WAAS,EAAT,EAAS;IACT,WAAS,EAAT,EAAS;IACRC,IAAI,EAAE,KAAK;IACZC,UAAU,EAAC,MAAM;IAChBC,OAAO,EAAER,MAAA,CAAAS,KAAK,CAACD,OAAO;IACtBE,aAAa,EAAEV,MAAA,CAAAS,KAAK,CAACE,KAAK;IAC1BC,cAAc,EAAEZ,MAAA,CAAAS,KAAK,CAACI,MAAM;IAC5BC,UAAU,EAAE,CAAC;IACbC,UAAS,EAAEf,MAAA,CAAAgB;mGAEhBrB,mBAAA,CAUM,OAVNsB,UAUM,GATJf,YAAA,CAEUgB,kBAAA;IAFD1B,KAAK,EAAC,cAAc;IAAE2B,OAAK,EAAEnB,MAAA,CAAAoB;;IAnBhDC,OAAA,EAAAC,QAAA,CAoBY;MAAA,OAAgB,CAAhBpB,YAAA,CAAgBqB,uBAAA,E;;IApB5BC,CAAA;MAsBUtB,YAAA,CAEUgB,kBAAA;IAFD1B,KAAK,EAAC,cAAc;IAAE2B,OAAK,EAAAM,MAAA,QAAAA,MAAA,gBAAAC,MAAA;MAAA,OAAE1B,MAAA,CAAA2B,WAAW;IAAA;;IAtB3DN,OAAA,EAAAC,QAAA,CAuBY;MAAA,OAAc,CAAdpB,YAAA,CAAc0B,qBAAA,E;;IAvB1BJ,CAAA;MAyBUtB,YAAA,CAEUgB,kBAAA;IAFD1B,KAAK,EAAC,cAAc;IAAE2B,OAAK,EAAAM,MAAA,QAAAA,MAAA,gBAAAC,MAAA;MAAA,OAAE1B,MAAA,CAAA2B,WAAW;IAAA;;IAzB3DN,OAAA,EAAAC,QAAA,CA0BY;MAAA,OAAU,CAAVpB,YAAA,CAAU2B,iBAAA,E;;IA1BtBL,CAAA;UA8BM7B,mBAAA,CAIM,OAJNmC,UAIM,GAHJnC,mBAAA,CAEM;IAFDH,KAAK,EAAC,wBAAwB;IAAEM,KAAK,EA/BlDC,eAAA,CA+BoDC,MAAA,CAAA+B,QAAQ;MAClDpC,mBAAA,CAAiD;IAA3CG,KAAK,EAhCrBC,eAAA,CAgCuBC,MAAA,CAAAgC,QAAQ,CAAC5B,GAAG;IAAG6B,GAAG,EAAEjC,MAAA,CAAAgC,QAAQ,CAACE;kCAhCpDC,UAAA,E,sBAoCIxC,mBAAA,CAGM,OAHNyC,UAGM,GAFJlC,YAAA,CAA+DmC,oBAAA;IAApDC,IAAI,EAAC,SAAS;IAAEnB,OAAK,EAAEnB,MAAA,CAAAuC;;IArCxClB,OAAA,EAAAC,QAAA,CAqCuD;MAAA,OAAEG,MAAA,QAAAA,MAAA,OArCzDe,gBAAA,CAqCuD,IAAE,E;;IArCzDhB,CAAA;MAsCMtB,YAAA,CAA+CmC,oBAAA;IAAnClB,OAAK,EAAEnB,MAAA,CAAAyC;EAAY;IAtCrCpB,OAAA,EAAAC,QAAA,CAsCuC;MAAA,OAAEG,MAAA,QAAAA,MAAA,OAtCzCe,gBAAA,CAsCuC,IAAE,E;;IAtCzChB,CAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
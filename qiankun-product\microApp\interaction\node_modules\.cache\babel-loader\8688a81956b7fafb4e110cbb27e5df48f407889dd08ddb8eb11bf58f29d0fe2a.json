{"ast": null, "code": "import { resolveComponent as _resolveComponent, createVNode as _createVNode, withCtx as _withCtx, createTextVNode as _createTextVNode, createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock } from \"vue\";\nvar _hoisted_1 = {\n  class: \"DictionaryTypeNew\"\n};\nvar _hoisted_2 = {\n  class: \"globalFormButton\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  var _component_el_input = _resolveComponent(\"el-input\");\n  var _component_el_form_item = _resolveComponent(\"el-form-item\");\n  var _component_el_tree_select = _resolveComponent(\"el-tree-select\");\n  var _component_el_radio = _resolveComponent(\"el-radio\");\n  var _component_el_radio_group = _resolveComponent(\"el-radio-group\");\n  var _component_el_button = _resolveComponent(\"el-button\");\n  var _component_el_form = _resolveComponent(\"el-form\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createVNode(_component_el_form, {\n    ref: \"formRef\",\n    model: $setup.form,\n    rules: $setup.rules,\n    inline: \"\",\n    \"label-position\": \"top\",\n    class: \"globalForm\"\n  }, {\n    default: _withCtx(function () {\n      return [_createVNode(_component_el_form_item, {\n        label: \"分组名称\",\n        prop: \"name\"\n      }, {\n        default: _withCtx(function () {\n          return [_createVNode(_component_el_input, {\n            modelValue: $setup.form.name,\n            \"onUpdate:modelValue\": _cache[0] || (_cache[0] = function ($event) {\n              return $setup.form.name = $event;\n            }),\n            placeholder: \"请输入分组名称\",\n            clearable: \"\"\n          }, null, 8 /* PROPS */, [\"modelValue\"])];\n        }),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_form_item, {\n        label: \"上级分组\"\n      }, {\n        default: _withCtx(function () {\n          return [_createVNode(_component_el_tree_select, {\n            modelValue: $setup.form.parentId,\n            \"onUpdate:modelValue\": _cache[1] || (_cache[1] = function ($event) {\n              return $setup.form.parentId = $event;\n            }),\n            data: $setup.groupData,\n            \"check-strictly\": \"\",\n            \"node-key\": \"id\",\n            \"render-after-expand\": false,\n            props: {\n              label: 'name'\n            },\n            clearable: \"\"\n          }, null, 8 /* PROPS */, [\"modelValue\", \"data\"])];\n        }),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_form_item, {\n        label: \"是否公开\",\n        prop: \"isOpen\"\n      }, {\n        default: _withCtx(function () {\n          return [_createVNode(_component_el_radio_group, {\n            modelValue: $setup.form.isOpen,\n            \"onUpdate:modelValue\": _cache[2] || (_cache[2] = function ($event) {\n              return $setup.form.isOpen = $event;\n            })\n          }, {\n            default: _withCtx(function () {\n              return [_createVNode(_component_el_radio, {\n                label: 1\n              }, {\n                default: _withCtx(function () {\n                  return _cache[4] || (_cache[4] = [_createTextVNode(\"公开\")]);\n                }),\n                _: 1 /* STABLE */\n              }), _createVNode(_component_el_radio, {\n                label: 0\n              }, {\n                default: _withCtx(function () {\n                  return _cache[5] || (_cache[5] = [_createTextVNode(\"不公开\")]);\n                }),\n                _: 1 /* STABLE */\n              })];\n            }),\n            _: 1 /* STABLE */\n          }, 8 /* PROPS */, [\"modelValue\"])];\n        }),\n        _: 1 /* STABLE */\n      }), _createElementVNode(\"div\", _hoisted_2, [_createVNode(_component_el_button, {\n        type: \"primary\",\n        onClick: _cache[3] || (_cache[3] = function ($event) {\n          return $setup.submitForm($setup.formRef);\n        })\n      }, {\n        default: _withCtx(function () {\n          return _cache[6] || (_cache[6] = [_createTextVNode(\"提交\")]);\n        }),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_button, {\n        onClick: $setup.resetForm\n      }, {\n        default: _withCtx(function () {\n          return _cache[7] || (_cache[7] = [_createTextVNode(\"取消\")]);\n        }),\n        _: 1 /* STABLE */\n      })])];\n    }),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"model\", \"rules\"])]);\n}", "map": {"version": 3, "names": ["class", "_createElementBlock", "_hoisted_1", "_createVNode", "_component_el_form", "ref", "model", "$setup", "form", "rules", "inline", "default", "_withCtx", "_component_el_form_item", "label", "prop", "_component_el_input", "modelValue", "name", "_cache", "$event", "placeholder", "clearable", "_", "_component_el_tree_select", "parentId", "data", "groupData", "props", "_component_el_radio_group", "isOpen", "_component_el_radio", "_createTextVNode", "_createElementVNode", "_hoisted_2", "_component_el_button", "type", "onClick", "submitForm", "formRef", "resetForm"], "sources": ["D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\interaction\\src\\views\\AddressBook\\AddressBookGroupNew.vue"], "sourcesContent": ["<template>\r\n  <div class=\"DictionaryTypeNew\">\r\n    <el-form ref=\"formRef\"\r\n             :model=\"form\"\r\n             :rules=\"rules\"\r\n             inline\r\n             label-position=\"top\"\r\n             class=\"globalForm\">\r\n      <el-form-item label=\"分组名称\"\r\n                    prop=\"name\">\r\n        <el-input v-model=\"form.name\"\r\n                  placeholder=\"请输入分组名称\"\r\n                  clearable />\r\n      </el-form-item>\r\n      <el-form-item label=\"上级分组\">\r\n        <el-tree-select v-model=\"form.parentId\"\r\n                        :data=\"groupData\"\r\n                        check-strictly\r\n                        node-key=\"id\"\r\n                        :render-after-expand=\"false\"\r\n                        :props=\"{ label: 'name' }\"\r\n                        clearable />\r\n      </el-form-item>\r\n      <el-form-item label=\"是否公开\"\r\n                    prop=\"isOpen\">\r\n        <el-radio-group v-model=\"form.isOpen\">\r\n          <el-radio :label=\"1\">公开</el-radio>\r\n          <el-radio :label=\"0\">不公开</el-radio>\r\n        </el-radio-group>\r\n      </el-form-item>\r\n      <div class=\"globalFormButton\">\r\n        <el-button type=\"primary\"\r\n                   @click=\"submitForm(formRef)\">提交</el-button>\r\n        <el-button @click=\"resetForm\">取消</el-button>\r\n      </div>\r\n    </el-form>\r\n  </div>\r\n</template>\r\n<script>\r\nexport default { name: 'DictionaryTypeNew' }\r\n</script>\r\n<script setup>\r\nimport api from '@/api'\r\nimport { reactive, ref, onMounted } from 'vue'\r\nimport { useRoute } from 'vue-router'\r\nconst route = useRoute()\r\nimport { ElMessage } from 'element-plus'\r\nconst props = defineProps({ id: { type: String, default: '' }, typeId: { type: String, default: '' } })\r\nconst emit = defineEmits(['callback'])\r\n\r\nconst formRef = ref()\r\nconst form = reactive({\r\n  parentId: '', // 上级分组\r\n  name: '', // 分组名称\r\n  isOpen: 1 // 是否公开\r\n})\r\nconst rules = reactive({\r\n  name: [{ required: true, message: '请输入分组名称', trigger: ['blur', 'change'] }],\r\n  isOpen: [{ required: true, message: '请选择是否公开', trigger: ['blur', 'change'] }]\r\n})\r\n\r\nconst groupData = ref()\r\nonMounted(() => {\r\n  if (props.id) { AddressBookGroupInfo() }\r\n  if (props.typeId) { groupTree() }\r\n})\r\n\r\nconst groupTree = async () => {\r\n  const res = await api.AddressBookGroupList({\r\n    query: { typeId: props.typeId }\r\n  })\r\n  var { data } = res\r\n  groupData.value = props.id ? filterData(data) : data\r\n}\r\nconst filterData = (list) => {\r\n  return list.filter(item => {\r\n    return item.id !== props.id\r\n  }).map(item => {\r\n    item = Object.assign({}, item)\r\n    if (item.children) {\r\n      item.children = filterData(item.children)\r\n    }\r\n    return item\r\n  })\r\n}\r\nconst AddressBookGroupInfo = async () => {\r\n  const res = await api.AddressBookGroupInfo({ detailId: props.id })\r\n  var { data } = res\r\n  form.parentId = data.parentId === '0' ? '' : data.parentId\r\n  form.name = data.name\r\n  form.isOpen = data.isOpen\r\n}\r\nconst submitForm = async (formEl) => {\r\n  if (!formEl) return\r\n  await formEl.validate((valid, fields) => {\r\n    if (valid) { globalJson() } else { ElMessage({ type: 'warning', message: '请根据提示信息完善字段内容！' }) }\r\n  })\r\n}\r\nconst globalJson = async () => {\r\n  const { code } = await api.globalJson(props.id ? '/relationBook/edit' : '/relationBook/add', {\r\n    form: {\r\n      id: props.id,\r\n      typeId: props.typeId,\r\n      parentId: form.parentId || '0',\r\n      name: form.name,\r\n      isOpen: form.isOpen,\r\n      ownerType: route.query.type || 'system'\r\n    }\r\n  })\r\n  if (code === 200) {\r\n    ElMessage({ type: 'success', message: props.id ? '编辑成功' : '新增成功' })\r\n    emit('callback')\r\n  }\r\n}\r\nconst resetForm = () => { emit('callback') }\r\n</script>\r\n<style lang=\"scss\">\r\n.DictionaryTypeNew {\r\n  width: 680px;\r\n}\r\n</style>\r\n"], "mappings": ";;EACOA,KAAK,EAAC;AAAmB;;EA6BrBA,KAAK,EAAC;AAAkB;;;;;;;;;uBA7BjCC,mBAAA,CAmCM,OAnCNC,UAmCM,GAlCJC,YAAA,CAiCUC,kBAAA;IAjCDC,GAAG,EAAC,SAAS;IACZC,KAAK,EAAEC,MAAA,CAAAC,IAAI;IACXC,KAAK,EAAEF,MAAA,CAAAE,KAAK;IACbC,MAAM,EAAN,EAAM;IACN,gBAAc,EAAC,KAAK;IACpBV,KAAK,EAAC;;IAPnBW,OAAA,EAAAC,QAAA,CAQM;MAAA,OAKe,CALfT,YAAA,CAKeU,uBAAA;QALDC,KAAK,EAAC,MAAM;QACZC,IAAI,EAAC;;QATzBJ,OAAA,EAAAC,QAAA,CAUQ;UAAA,OAEsB,CAFtBT,YAAA,CAEsBa,mBAAA;YAZ9BC,UAAA,EAU2BV,MAAA,CAAAC,IAAI,CAACU,IAAI;YAVpC,uBAAAC,MAAA,QAAAA,MAAA,gBAAAC,MAAA;cAAA,OAU2Bb,MAAA,CAAAC,IAAI,CAACU,IAAI,GAAAE,MAAA;YAAA;YAClBC,WAAW,EAAC,SAAS;YACrBC,SAAS,EAAT;;;QAZlBC,CAAA;UAcMpB,YAAA,CAQeU,uBAAA;QARDC,KAAK,EAAC;MAAM;QAdhCH,OAAA,EAAAC,QAAA,CAeQ;UAAA,OAM4B,CAN5BT,YAAA,CAM4BqB,yBAAA;YArBpCP,UAAA,EAeiCV,MAAA,CAAAC,IAAI,CAACiB,QAAQ;YAf9C,uBAAAN,MAAA,QAAAA,MAAA,gBAAAC,MAAA;cAAA,OAeiCb,MAAA,CAAAC,IAAI,CAACiB,QAAQ,GAAAL,MAAA;YAAA;YACrBM,IAAI,EAAEnB,MAAA,CAAAoB,SAAS;YAChB,gBAAc,EAAd,EAAc;YACd,UAAQ,EAAC,IAAI;YACZ,qBAAmB,EAAE,KAAK;YAC1BC,KAAK,EAAE;cAAAd,KAAA;YAAA,CAAiB;YACzBQ,SAAS,EAAT;;;QArBxBC,CAAA;UAuBMpB,YAAA,CAMeU,uBAAA;QANDC,KAAK,EAAC,MAAM;QACZC,IAAI,EAAC;;QAxBzBJ,OAAA,EAAAC,QAAA,CAyBQ;UAAA,OAGiB,CAHjBT,YAAA,CAGiB0B,yBAAA;YA5BzBZ,UAAA,EAyBiCV,MAAA,CAAAC,IAAI,CAACsB,MAAM;YAzB5C,uBAAAX,MAAA,QAAAA,MAAA,gBAAAC,MAAA;cAAA,OAyBiCb,MAAA,CAAAC,IAAI,CAACsB,MAAM,GAAAV,MAAA;YAAA;;YAzB5CT,OAAA,EAAAC,QAAA,CA0BU;cAAA,OAAkC,CAAlCT,YAAA,CAAkC4B,mBAAA;gBAAvBjB,KAAK,EAAE;cAAC;gBA1B7BH,OAAA,EAAAC,QAAA,CA0B+B;kBAAA,OAAEO,MAAA,QAAAA,MAAA,OA1BjCa,gBAAA,CA0B+B,IAAE,E;;gBA1BjCT,CAAA;kBA2BUpB,YAAA,CAAmC4B,mBAAA;gBAAxBjB,KAAK,EAAE;cAAC;gBA3B7BH,OAAA,EAAAC,QAAA,CA2B+B;kBAAA,OAAGO,MAAA,QAAAA,MAAA,OA3BlCa,gBAAA,CA2B+B,KAAG,E;;gBA3BlCT,CAAA;;;YAAAA,CAAA;;;QAAAA,CAAA;UA8BMU,mBAAA,CAIM,OAJNC,UAIM,GAHJ/B,YAAA,CACsDgC,oBAAA;QAD3CC,IAAI,EAAC,SAAS;QACbC,OAAK,EAAAlB,MAAA,QAAAA,MAAA,gBAAAC,MAAA;UAAA,OAAEb,MAAA,CAAA+B,UAAU,CAAC/B,MAAA,CAAAgC,OAAO;QAAA;;QAhC7C5B,OAAA,EAAAC,QAAA,CAgCgD;UAAA,OAAEO,MAAA,QAAAA,MAAA,OAhClDa,gBAAA,CAgCgD,IAAE,E;;QAhClDT,CAAA;UAiCQpB,YAAA,CAA4CgC,oBAAA;QAAhCE,OAAK,EAAE9B,MAAA,CAAAiC;MAAS;QAjCpC7B,OAAA,EAAAC,QAAA,CAiCsC;UAAA,OAAEO,MAAA,QAAAA,MAAA,OAjCxCa,gBAAA,CAiCsC,IAAE,E;;QAjCxCT,CAAA;;;IAAAA,CAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "import { ref } from 'vue';\nimport { isFunction } from '@vue/shared';\nimport '../../../utils/index.mjs';\nimport { isKorean } from '../../../utils/i18n.mjs';\nfunction useInput(handleInput) {\n  var isComposing = ref(false);\n  var handleCompositionStart = function handleCompositionStart() {\n    isComposing.value = true;\n  };\n  var handleCompositionUpdate = function handleCompositionUpdate(event) {\n    var text = event.target.value;\n    var lastCharacter = text[text.length - 1] || \"\";\n    isComposing.value = !isKorean(lastCharacter);\n  };\n  var handleCompositionEnd = function handleCompositionEnd(event) {\n    if (isComposing.value) {\n      isComposing.value = false;\n      if (isFunction(handleInput)) {\n        handleInput(event);\n      }\n    }\n  };\n  return {\n    handleCompositionStart,\n    handleCompositionUpdate,\n    handleCompositionEnd\n  };\n}\nexport { useInput };", "map": {"version": 3, "names": ["useInput", "handleInput", "isComposing", "ref", "handleCompositionStart", "value", "handleCompositionUpdate", "event", "text", "target", "lastCharacter", "length", "isKorean", "handleCompositionEnd", "isFunction"], "sources": ["../../../../../../packages/components/select-v2/src/useInput.ts"], "sourcesContent": ["// @ts-nocheck\nimport { ref } from 'vue'\nimport { isFunction } from '@vue/shared'\nimport { isKorean } from '@element-plus/utils'\n\nexport function useInput(handleInput: (event: InputEvent) => void) {\n  const isComposing = ref(false)\n\n  const handleCompositionStart = () => {\n    isComposing.value = true\n  }\n\n  const handleCompositionUpdate = (event) => {\n    const text = event.target.value\n    const lastCharacter = text[text.length - 1] || ''\n    isComposing.value = !isKorean(lastCharacter)\n  }\n\n  const handleCompositionEnd = (event) => {\n    if (isComposing.value) {\n      isComposing.value = false\n      if (isFunction(handleInput)) {\n        handleInput(event)\n      }\n    }\n  }\n\n  return {\n    handleCompositionStart,\n    handleCompositionUpdate,\n    handleCompositionEnd,\n  }\n}\n"], "mappings": ";;;;AAGO,SAASA,QAAQA,CAACC,WAAW,EAAE;EACpC,IAAMC,WAAW,GAAGC,GAAG,CAAC,KAAK,CAAC;EAC9B,IAAMC,sBAAsB,GAAG,SAAzBA,sBAAsBA,CAAA,EAAS;IACnCF,WAAW,CAACG,KAAK,GAAG,IAAI;EAC5B,CAAG;EACD,IAAMC,uBAAuB,GAAG,SAA1BA,uBAAuBA,CAAIC,KAAK,EAAK;IACzC,IAAMC,IAAI,GAAGD,KAAK,CAACE,MAAM,CAACJ,KAAK;IAC/B,IAAMK,aAAa,GAAGF,IAAI,CAACA,IAAI,CAACG,MAAM,GAAG,CAAC,CAAC,IAAI,EAAE;IACjDT,WAAW,CAACG,KAAK,GAAG,CAACO,QAAQ,CAACF,aAAa,CAAC;EAChD,CAAG;EACD,IAAMG,oBAAoB,GAAG,SAAvBA,oBAAoBA,CAAIN,KAAK,EAAK;IACtC,IAAIL,WAAW,CAACG,KAAK,EAAE;MACrBH,WAAW,CAACG,KAAK,GAAG,KAAK;MACzB,IAAIS,UAAU,CAACb,WAAW,CAAC,EAAE;QAC3BA,WAAW,CAACM,KAAK,CAAC;MAC1B;IACA;EACA,CAAG;EACD,OAAO;IACLH,sBAAsB;IACtBE,uBAAuB;IACvBO;EACJ,CAAG;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
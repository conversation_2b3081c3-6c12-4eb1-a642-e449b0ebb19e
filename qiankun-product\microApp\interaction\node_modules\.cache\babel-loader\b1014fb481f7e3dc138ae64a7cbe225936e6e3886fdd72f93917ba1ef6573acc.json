{"ast": null, "code": "import MapCache from './_MapCache.js';\n\n/** Error message constants. */\nvar FUNC_ERROR_TEXT = 'Expected a function';\n\n/**\n * Creates a function that memoizes the result of `func`. If `resolver` is\n * provided, it determines the cache key for storing the result based on the\n * arguments provided to the memoized function. By default, the first argument\n * provided to the memoized function is used as the map cache key. The `func`\n * is invoked with the `this` binding of the memoized function.\n *\n * **Note:** The cache is exposed as the `cache` property on the memoized\n * function. Its creation may be customized by replacing the `_.memoize.Cache`\n * constructor with one whose instances implement the\n * [`Map`](http://ecma-international.org/ecma-262/7.0/#sec-properties-of-the-map-prototype-object)\n * method interface of `clear`, `delete`, `get`, `has`, and `set`.\n *\n * @static\n * @memberOf _\n * @since 0.1.0\n * @category Function\n * @param {Function} func The function to have its output memoized.\n * @param {Function} [resolver] The function to resolve the cache key.\n * @returns {Function} Returns the new memoized function.\n * @example\n *\n * var object = { 'a': 1, 'b': 2 };\n * var other = { 'c': 3, 'd': 4 };\n *\n * var values = _.memoize(_.values);\n * values(object);\n * // => [1, 2]\n *\n * values(other);\n * // => [3, 4]\n *\n * object.a = 2;\n * values(object);\n * // => [1, 2]\n *\n * // Modify the result cache.\n * values.cache.set(object, ['a', 'b']);\n * values(object);\n * // => ['a', 'b']\n *\n * // Replace `_.memoize.Cache`.\n * _.memoize.Cache = WeakMap;\n */\nfunction memoize(func, resolver) {\n  if (typeof func != 'function' || resolver != null && typeof resolver != 'function') {\n    throw new TypeError(FUNC_ERROR_TEXT);\n  }\n  var _memoized = function memoized() {\n    var args = arguments,\n      key = resolver ? resolver.apply(this, args) : args[0],\n      cache = _memoized.cache;\n    if (cache.has(key)) {\n      return cache.get(key);\n    }\n    var result = func.apply(this, args);\n    _memoized.cache = cache.set(key, result) || cache;\n    return result;\n  };\n  _memoized.cache = new (memoize.Cache || MapCache)();\n  return _memoized;\n}\n\n// Expose `MapCache`.\nmemoize.Cache = MapCache;\nexport default memoize;", "map": {"version": 3, "names": ["MapCache", "FUNC_ERROR_TEXT", "memoize", "func", "resolver", "TypeError", "memoized", "args", "arguments", "key", "apply", "cache", "has", "get", "result", "set", "<PERSON><PERSON>"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/node_modules/.pnpm/lodash-es@4.17.21/node_modules/lodash-es/memoize.js"], "sourcesContent": ["import MapCache from './_MapCache.js';\n\n/** Error message constants. */\nvar FUNC_ERROR_TEXT = 'Expected a function';\n\n/**\n * Creates a function that memoizes the result of `func`. If `resolver` is\n * provided, it determines the cache key for storing the result based on the\n * arguments provided to the memoized function. By default, the first argument\n * provided to the memoized function is used as the map cache key. The `func`\n * is invoked with the `this` binding of the memoized function.\n *\n * **Note:** The cache is exposed as the `cache` property on the memoized\n * function. Its creation may be customized by replacing the `_.memoize.Cache`\n * constructor with one whose instances implement the\n * [`Map`](http://ecma-international.org/ecma-262/7.0/#sec-properties-of-the-map-prototype-object)\n * method interface of `clear`, `delete`, `get`, `has`, and `set`.\n *\n * @static\n * @memberOf _\n * @since 0.1.0\n * @category Function\n * @param {Function} func The function to have its output memoized.\n * @param {Function} [resolver] The function to resolve the cache key.\n * @returns {Function} Returns the new memoized function.\n * @example\n *\n * var object = { 'a': 1, 'b': 2 };\n * var other = { 'c': 3, 'd': 4 };\n *\n * var values = _.memoize(_.values);\n * values(object);\n * // => [1, 2]\n *\n * values(other);\n * // => [3, 4]\n *\n * object.a = 2;\n * values(object);\n * // => [1, 2]\n *\n * // Modify the result cache.\n * values.cache.set(object, ['a', 'b']);\n * values(object);\n * // => ['a', 'b']\n *\n * // Replace `_.memoize.Cache`.\n * _.memoize.Cache = WeakMap;\n */\nfunction memoize(func, resolver) {\n  if (typeof func != 'function' || (resolver != null && typeof resolver != 'function')) {\n    throw new TypeError(FUNC_ERROR_TEXT);\n  }\n  var memoized = function() {\n    var args = arguments,\n        key = resolver ? resolver.apply(this, args) : args[0],\n        cache = memoized.cache;\n\n    if (cache.has(key)) {\n      return cache.get(key);\n    }\n    var result = func.apply(this, args);\n    memoized.cache = cache.set(key, result) || cache;\n    return result;\n  };\n  memoized.cache = new (memoize.Cache || MapCache);\n  return memoized;\n}\n\n// Expose `MapCache`.\nmemoize.Cache = MapCache;\n\nexport default memoize;\n"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,gBAAgB;;AAErC;AACA,IAAIC,eAAe,GAAG,qBAAqB;;AAE3C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,OAAOA,CAACC,IAAI,EAAEC,QAAQ,EAAE;EAC/B,IAAI,OAAOD,IAAI,IAAI,UAAU,IAAKC,QAAQ,IAAI,IAAI,IAAI,OAAOA,QAAQ,IAAI,UAAW,EAAE;IACpF,MAAM,IAAIC,SAAS,CAACJ,eAAe,CAAC;EACtC;EACA,IAAIK,SAAQ,GAAG,SAAXA,QAAQA,CAAA,EAAc;IACxB,IAAIC,IAAI,GAAGC,SAAS;MAChBC,GAAG,GAAGL,QAAQ,GAAGA,QAAQ,CAACM,KAAK,CAAC,IAAI,EAAEH,IAAI,CAAC,GAAGA,IAAI,CAAC,CAAC,CAAC;MACrDI,KAAK,GAAGL,SAAQ,CAACK,KAAK;IAE1B,IAAIA,KAAK,CAACC,GAAG,CAACH,GAAG,CAAC,EAAE;MAClB,OAAOE,KAAK,CAACE,GAAG,CAACJ,GAAG,CAAC;IACvB;IACA,IAAIK,MAAM,GAAGX,IAAI,CAACO,KAAK,CAAC,IAAI,EAAEH,IAAI,CAAC;IACnCD,SAAQ,CAACK,KAAK,GAAGA,KAAK,CAACI,GAAG,CAACN,GAAG,EAAEK,MAAM,CAAC,IAAIH,KAAK;IAChD,OAAOG,MAAM;EACf,CAAC;EACDR,SAAQ,CAACK,KAAK,GAAG,KAAKT,OAAO,CAACc,KAAK,IAAIhB,QAAQ,GAAC;EAChD,OAAOM,SAAQ;AACjB;;AAEA;AACAJ,OAAO,CAACc,KAAK,GAAGhB,QAAQ;AAExB,eAAeE,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nfunction _defineProperty(e, r, t) { return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, { value: t, enumerable: !0, configurable: !0, writable: !0 }) : e[r] = t, e; }\nfunction _toPropertyKey(t) { var i = _toPrimitive(t, \"string\"); return \"symbol\" == typeof i ? i : i + \"\"; }\nfunction _toPrimitive(t, r) { if (\"object\" != typeof t || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || \"default\"); if (\"object\" != typeof i) return i; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (\"string\" === r ? String : Number)(t); }\nimport '../../../../utils/index.mjs';\nimport { datePickerSharedProps, selectionModeWithDefault } from './shared.mjs';\nimport { buildProps } from '../../../../utils/vue/props/runtime.mjs';\nvar basicMonthTableProps = buildProps(_objectSpread(_objectSpread({}, datePickerSharedProps), {}, {\n  selectionMode: selectionModeWithDefault(\"month\")\n}));\nexport { basicMonthTableProps };", "map": {"version": 3, "names": ["basicMonthTableProps", "buildProps", "_objectSpread", "datePickerSharedProps", "selectionMode", "selectionModeWithDefault"], "sources": ["../../../../../../../packages/components/date-picker/src/props/basic-month-table.ts"], "sourcesContent": ["import { buildProps } from '@element-plus/utils'\nimport { datePickerSharedProps, selectionModeWithDefault } from './shared'\n\nimport type { ExtractPropTypes } from 'vue'\n\nexport const basicMonthTableProps = buildProps({\n  ...datePickerSharedProps,\n  selectionMode: selectionModeWithDefault('month'),\n})\n\nexport type BasicMonthTableProps = ExtractPropTypes<typeof basicMonthTableProps>\n"], "mappings": ";;;;;;;;AAEY,IAACA,oBAAoB,GAAGC,UAAU,CAAAC,aAAA,CAAAA,aAAA,KACzCC,qBAAqB;EACxBC,aAAa,EAAEC,wBAAwB,CAAC,OAAO;AAAC,EACjD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
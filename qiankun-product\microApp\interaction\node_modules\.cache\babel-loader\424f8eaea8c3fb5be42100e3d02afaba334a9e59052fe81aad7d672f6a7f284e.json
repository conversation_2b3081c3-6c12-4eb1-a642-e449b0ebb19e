{"ast": null, "code": "\"use strict\";\n\nvar prop = \"_erd\";\nfunction initState(element) {\n  element[prop] = {};\n  return getState(element);\n}\nfunction getState(element) {\n  return element[prop];\n}\nfunction cleanState(element) {\n  delete element[prop];\n}\nmodule.exports = {\n  initState: initState,\n  getState: getState,\n  cleanState: cleanState\n};", "map": {"version": 3, "names": ["prop", "initState", "element", "getState", "cleanState", "module", "exports"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/node_modules/.pnpm/element-resize-detector@1.2.4/node_modules/element-resize-detector/src/state-handler.js"], "sourcesContent": ["\"use strict\";\n\nvar prop = \"_erd\";\n\nfunction initState(element) {\n    element[prop] = {};\n    return getState(element);\n}\n\nfunction getState(element) {\n    return element[prop];\n}\n\nfunction cleanState(element) {\n    delete element[prop];\n}\n\nmodule.exports = {\n    initState: initState,\n    getState: getState,\n    cleanState: cleanState\n};\n"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,IAAI,GAAG,MAAM;AAEjB,SAASC,SAASA,CAACC,OAAO,EAAE;EACxBA,OAAO,CAACF,IAAI,CAAC,GAAG,CAAC,CAAC;EAClB,OAAOG,QAAQ,CAACD,OAAO,CAAC;AAC5B;AAEA,SAASC,QAAQA,CAACD,OAAO,EAAE;EACvB,OAAOA,OAAO,CAACF,IAAI,CAAC;AACxB;AAEA,SAASI,UAAUA,CAACF,OAAO,EAAE;EACzB,OAAOA,OAAO,CAACF,IAAI,CAAC;AACxB;AAEAK,MAAM,CAACC,OAAO,GAAG;EACbL,SAAS,EAAEA,SAAS;EACpBE,QAAQ,EAAEA,QAAQ;EAClBC,UAAU,EAAEA;AAChB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}
{"ast": null, "code": "import { toDisplayString as _toDisplayString, createElementVNode as _createElementVNode, renderList as _renderList, Fragment as _Fragment, openBlock as _openBlock, createElementBlock as _createElementBlock, renderSlot as _renderSlot, withCtx as _withCtx, createBlock as _createBlock, createCommentVNode as _createCommentVNode, resolveDirective as _resolveDirective, withDirectives as _withDirectives, resolveComponent as _resolveComponent, normalizeClass as _normalizeClass, createVNode as _createVNode } from \"vue\";\nvar _hoisted_1 = {\n  class: \"xyl-global-comment\"\n};\nvar _hoisted_2 = {\n  class: \"xyl-global-comment-head\"\n};\nvar _hoisted_3 = {\n  class: \"xyl-global-comment-head-name\"\n};\nvar _hoisted_4 = {\n  class: \"xyl-global-comment-head-text\"\n};\nvar _hoisted_5 = {\n  key: 0,\n  \"infinite-scroll-distance\": 50,\n  class: \"xyl-global-comment-scroll\"\n};\nvar _hoisted_6 = {\n  key: 0,\n  class: \"xyl-global-comment-loading-text\"\n};\nvar _hoisted_7 = {\n  key: 1,\n  class: \"xyl-global-comment-loading-text\"\n};\nvar _hoisted_8 = {\n  key: 0,\n  class: \"xyl-global-comment-pagination\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  var _component_el_scrollbar = _resolveComponent(\"el-scrollbar\");\n  var _component_el_pagination = _resolveComponent(\"el-pagination\");\n  var _directive_infinite_scroll = _resolveDirective(\"infinite-scroll\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createElementVNode(\"div\", _hoisted_2, [_createElementVNode(\"div\", _hoisted_3, \"所有\" + _toDisplayString($setup.props.text), 1 /* TEXT */), _createElementVNode(\"div\", _hoisted_4, \"（共\" + _toDisplayString($setup.totals) + \"条\" + _toDisplayString($setup.props.text) + \"意见）\", 1 /* TEXT */)]), _createVNode(_component_el_scrollbar, {\n    class: _normalizeClass([\"xyl-global-comment-scrollbar\", {\n      'xyl-global-comment-scroll': $setup.props.scroll\n    }])\n  }, {\n    default: _withCtx(function () {\n      return [$setup.props.scroll ? _withDirectives((_openBlock(), _createElementBlock(\"div\", _hoisted_5, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.commentList, function (item) {\n        return _openBlock(), _createBlock($setup[\"XylGlobalCommentItem\"], {\n          key: item.id,\n          commentObj: item,\n          id: $setup.props.id,\n          type: $setup.props.type,\n          prompt: $setup.props.prompt,\n          isDel: $setup.props.isDel,\n          isReply: $setup.props.isReply,\n          onRefresh: $setup.refresh\n        }, {\n          default: _withCtx(function (_ref) {\n            var row = _ref.row;\n            return [_renderSlot(_ctx.$slots, \"default\", {\n              row: row\n            })];\n          }),\n          _: 2 /* DYNAMIC */\n        }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"commentObj\", \"id\", \"type\", \"prompt\", \"isDel\", \"isReply\"]);\n      }), 128 /* KEYED_FRAGMENT */)), $setup.loading ? (_openBlock(), _createElementBlock(\"div\", _hoisted_6, \"加载中...\")) : _createCommentVNode(\"v-if\", true), $setup.isShow ? (_openBlock(), _createElementBlock(\"div\", _hoisted_7, \"没有更多了\")) : _createCommentVNode(\"v-if\", true)])), [[_directive_infinite_scroll, $setup.load]]) : _createCommentVNode(\"v-if\", true), !$setup.props.scroll ? (_openBlock(true), _createElementBlock(_Fragment, {\n        key: 1\n      }, _renderList($setup.commentList, function (item) {\n        return _openBlock(), _createBlock($setup[\"XylGlobalCommentItem\"], {\n          key: item.id,\n          commentObj: item,\n          id: $setup.props.id,\n          type: $setup.props.type,\n          prompt: $setup.props.prompt,\n          isDel: $setup.props.isDel,\n          isReply: $setup.props.isReply,\n          checked: $setup.props.checked,\n          onRefresh: $setup.refresh\n        }, {\n          default: _withCtx(function (_ref2) {\n            var row = _ref2.row;\n            return [_renderSlot(_ctx.$slots, \"default\", {\n              row: row\n            })];\n          }),\n          _: 2 /* DYNAMIC */\n        }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"commentObj\", \"id\", \"type\", \"prompt\", \"isDel\", \"isReply\", \"checked\"]);\n      }), 128 /* KEYED_FRAGMENT */)) : _createCommentVNode(\"v-if\", true)];\n    }),\n    _: 3 /* FORWARDED */\n  }, 8 /* PROPS */, [\"class\"]), !$setup.props.scroll ? (_openBlock(), _createElementBlock(\"div\", _hoisted_8, [_createVNode(_component_el_pagination, {\n    currentPage: $setup.pageNo,\n    \"onUpdate:currentPage\": _cache[0] || (_cache[0] = function ($event) {\n      return $setup.pageNo = $event;\n    }),\n    \"page-size\": $setup.pageSize,\n    \"onUpdate:pageSize\": _cache[1] || (_cache[1] = function ($event) {\n      return $setup.pageSize = $event;\n    }),\n    \"page-sizes\": [10, 20, 50, 100, 200],\n    layout: \"total, sizes, prev, pager, next, jumper\",\n    onSizeChange: $setup.handleQuery,\n    onCurrentChange: $setup.handleQuery,\n    total: $setup.totals,\n    background: \"\"\n  }, null, 8 /* PROPS */, [\"currentPage\", \"page-size\", \"total\"])])) : _createCommentVNode(\"v-if\", true)]);\n}", "map": {"version": 3, "names": ["class", "key", "_createElementBlock", "_hoisted_1", "_createElementVNode", "_hoisted_2", "_hoisted_3", "_toDisplayString", "$setup", "props", "text", "_hoisted_4", "totals", "_createVNode", "_component_el_scrollbar", "_normalizeClass", "scroll", "default", "_withCtx", "_hoisted_5", "_Fragment", "_renderList", "commentList", "item", "_createBlock", "id", "commentObj", "type", "prompt", "isDel", "isReply", "onRefresh", "refresh", "_ref", "row", "_renderSlot", "_ctx", "$slots", "_", "loading", "_hoisted_6", "_createCommentVNode", "isShow", "_hoisted_7", "load", "checked", "_ref2", "_hoisted_8", "_component_el_pagination", "currentPage", "pageNo", "_cache", "$event", "pageSize", "layout", "onSizeChange", "handleQuery", "onCurrentChange", "total", "background"], "sources": ["D:\\zy\\xm\\pc\\5.0\\qiankun-product\\common\\components\\xyl-global-comment\\xyl-global-comment.vue"], "sourcesContent": ["<template>\r\n  <div class=\"xyl-global-comment\">\r\n    <div class=\"xyl-global-comment-head\">\r\n      <div class=\"xyl-global-comment-head-name\">所有{{ props.text }}</div>\r\n      <div class=\"xyl-global-comment-head-text\">（共{{ totals }}条{{ props.text }}意见）</div>\r\n    </div>\r\n    <el-scrollbar class=\"xyl-global-comment-scrollbar\" :class=\"{ 'xyl-global-comment-scroll': props.scroll }\">\r\n      <template v-if=\"props.scroll\">\r\n        <div v-infinite-scroll=\"load\" :infinite-scroll-distance=\"50\" class=\"xyl-global-comment-scroll\">\r\n          <xyl-global-comment-item v-for=\"item in commentList\" :key=\"item.id\" :commentObj=\"item\" :id=\"props.id\"\r\n            :type=\"props.type\" :prompt=\"props.prompt\" :isDel=\"props.isDel\" :isReply=\"props.isReply\" @refresh=\"refresh\"\r\n            v-slot=\"{ row }\">\r\n            <slot :row=\"row\"></slot>\r\n          </xyl-global-comment-item>\r\n          <div class=\"xyl-global-comment-loading-text\" v-if=\"loading\">加载中...</div>\r\n          <div class=\"xyl-global-comment-loading-text\" v-if=\"isShow\">没有更多了</div>\r\n        </div>\r\n      </template>\r\n      <template v-if=\"!props.scroll\">\r\n        <xyl-global-comment-item v-for=\"item in commentList\" :key=\"item.id\" :commentObj=\"item\" :id=\"props.id\"\r\n          :type=\"props.type\" :prompt=\"props.prompt\" :isDel=\"props.isDel\" :isReply=\"props.isReply\"\r\n          :checked=\"props.checked\" @refresh=\"refresh\" v-slot=\"{ row }\">\r\n          <slot :row=\"row\"></slot>\r\n        </xyl-global-comment-item>\r\n      </template>\r\n    </el-scrollbar>\r\n    <div class=\"xyl-global-comment-pagination\" v-if=\"!props.scroll\">\r\n      <el-pagination v-model:currentPage=\"pageNo\" v-model:page-size=\"pageSize\" :page-sizes=\"[10, 20, 50, 100, 200]\"\r\n        layout=\"total, sizes, prev, pager, next, jumper\" @size-change=\"handleQuery\" @current-change=\"handleQuery\"\r\n        :total=\"totals\" background />\r\n    </div>\r\n  </div>\r\n</template>\r\n<script>\r\nexport default { name: 'XylGlobalComment' }\r\n</script>\r\n<script setup>\r\nimport api from '@/api'\r\nimport { ref, onMounted, defineAsyncComponent } from 'vue'\r\nconst XylGlobalCommentItem = defineAsyncComponent(() => import('./xyl-global-comment-item.vue'))\r\nconst props = defineProps({\r\n  id: { type: String, default: '' },\r\n  type: { type: String, default: '' },\r\n  text: { type: String, default: '评论' },\r\n  prompt: { type: Boolean, default: true },\r\n  scroll: { type: Boolean, default: false },\r\n  isDel: { type: Boolean, default: true },\r\n  isReply: { type: Boolean, default: true },\r\n  checked: { type: Boolean, default: true },\r\n  params: { type: Object, default: () => ({}) }\r\n})\r\nonMounted(() => {\r\n  twoLevelTree()\r\n})\r\n\r\nconst pageNo = ref(1)\r\nconst pageSize = ref(10)\r\nconst totals = ref(0)\r\nconst isShow = ref(false)\r\nconst loading = ref(true)\r\nconst commentList = ref([])\r\n\r\nconst load = () => {\r\n  if (pageNo.value * pageSize.value >= totals.value) return\r\n  pageNo.value += 1\r\n  twoLevelTree()\r\n}\r\nconst handleQuery = () => {\r\n  twoLevelTree()\r\n}\r\nconst twoLevelTree = async (num) => {\r\n  const { data, total } = await api.twoLevelTree({\r\n    businessCode: props.type,\r\n    businessId: props.id,\r\n    pageNo: num ? 1 : pageNo.value,\r\n    pageSize: num ? num : pageSize.value,\r\n    ...props.params\r\n  })\r\n  commentList.value = num || !props.scroll ? data : [...commentList.value, ...data]\r\n  totals.value = total\r\n  loading.value = pageNo.value * pageSize.value < totals.value\r\n  isShow.value = pageNo.value * pageSize.value >= totals.value\r\n}\r\nconst refresh = (type) => {\r\n  if (type) {\r\n    twoLevelTree(props.scroll ? commentList.value.length + 1 : '')\r\n  } else {\r\n    twoLevelTree(props.scroll ? commentList.value.length - 1 : '')\r\n  }\r\n}\r\ndefineExpose({ id: props.id, refresh })\r\n</script>\r\n<style lang=\"scss\">\r\n.xyl-global-comment {\r\n  width: 100%;\r\n  height: 600px;\r\n\r\n  .xyl-global-comment-head {\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: space-between;\r\n    height: var(--zy-height);\r\n\r\n    .xyl-global-comment-head-name {\r\n      font-weight: bold;\r\n      font-size: var(--zy-navigation-font-size);\r\n    }\r\n\r\n    .xyl-global-comment-head-text {\r\n      font-size: var(--zy-text-font-size);\r\n    }\r\n  }\r\n\r\n  .xyl-global-comment-scrollbar {\r\n    width: 100%;\r\n    height: calc(100% - (var(--zy-height) + 42px));\r\n    border: 1px solid var(--zy-el-border-color-lighter);\r\n\r\n    .xyl-global-comment-loading-text {\r\n      color: var(--zy-el-text-color-regular);\r\n      text-align: center;\r\n      padding: var(--zy-distance-three) 0;\r\n      font-size: var(--zy-text-font-size);\r\n      line-height: var(--zy-line-height);\r\n    }\r\n  }\r\n\r\n  .xyl-global-comment-scroll {\r\n    height: calc(100% - var(--zy-height));\r\n  }\r\n\r\n  .xyl-global-comment-pagination {\r\n    width: 100%;\r\n    height: 42px;\r\n    line-height: normal;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: flex-end;\r\n    padding-right: var(--zy-distance-two);\r\n\r\n    .zy-el-pagination {\r\n      --zy-height: var(--zy-height-routine);\r\n      --zy-el-component-size: var(--zy-height);\r\n\r\n      .zy-el-select {\r\n        width: 128px;\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;EACOA,KAAK,EAAC;AAAoB;;EACxBA,KAAK,EAAC;AAAyB;;EAC7BA,KAAK,EAAC;AAA8B;;EACpCA,KAAK,EAAC;AAA8B;;EAJ/CC,GAAA;EAQuC,0BAAwB,EAAE,EAAE;EAAED,KAAK,EAAC;;;EAR3EC,GAAA;EAceD,KAAK,EAAC;;;EAdrBC,GAAA;EAeeD,KAAK,EAAC;;;EAfrBC,GAAA;EA0BSD,KAAK,EAAC;;;;;;uBAzBbE,mBAAA,CA8BM,OA9BNC,UA8BM,GA7BJC,mBAAA,CAGM,OAHNC,UAGM,GAFJD,mBAAA,CAAkE,OAAlEE,UAAkE,EAAxB,IAAE,GAAAC,gBAAA,CAAGC,MAAA,CAAAC,KAAK,CAACC,IAAI,kBACzDN,mBAAA,CAAkF,OAAlFO,UAAkF,EAAxC,IAAE,GAAAJ,gBAAA,CAAGC,MAAA,CAAAI,MAAM,IAAG,GAAC,GAAAL,gBAAA,CAAGC,MAAA,CAAAC,KAAK,CAACC,IAAI,IAAG,KAAG,gB,GAE9EG,YAAA,CAmBeC,uBAAA;IAnBDd,KAAK,EANvBe,eAAA,EAMwB,8BAA8B;MAAA,6BAAwCP,MAAA,CAAAC,KAAK,CAACO;IAAM;;IAN1GC,OAAA,EAAAC,QAAA,CAOM;MAAA,OAUW,CAVKV,MAAA,CAAAC,KAAK,CAACO,MAAM,G,+BAC1Bd,mBAAA,CAQM,OARNiB,UAQM,I,kBAPJjB,mBAAA,CAI0BkB,SAAA,QAbpCC,WAAA,CASkDb,MAAA,CAAAc,WAAW,EAT7D,UAS0CC,IAAI;6BAApCC,YAAA,CAI0BhB,MAAA;UAJ4BP,GAAG,EAAEsB,IAAI,CAACE,EAAE;UAAGC,UAAU,EAAEH,IAAI;UAAGE,EAAE,EAAEjB,MAAA,CAAAC,KAAK,CAACgB,EAAE;UACjGE,IAAI,EAAEnB,MAAA,CAAAC,KAAK,CAACkB,IAAI;UAAGC,MAAM,EAAEpB,MAAA,CAAAC,KAAK,CAACmB,MAAM;UAAGC,KAAK,EAAErB,MAAA,CAAAC,KAAK,CAACoB,KAAK;UAAGC,OAAO,EAAEtB,MAAA,CAAAC,KAAK,CAACqB,OAAO;UAAGC,SAAO,EAAEvB,MAAA,CAAAwB;;UAV9Gf,OAAA,EAAAC,QAAA,CAYY,UAAAe,IAAA;YAAA,IADUC,GAAG,GAAAD,IAAA,CAAHC,GAAG;YAAA,QACbC,WAAA,CAAwBC,IAAA,CAAAC,MAAA;cAAjBH,GAAG,EAAEA;YAAG,G;;UAZ3BI,CAAA;;sCAc6D9B,MAAA,CAAA+B,OAAO,I,cAA1DrC,mBAAA,CAAwE,OAAxEsC,UAAwE,EAAZ,QAAM,KAd5EC,mBAAA,gBAe6DjC,MAAA,CAAAkC,MAAM,I,cAAzDxC,mBAAA,CAAsE,OAAtEyC,UAAsE,EAAX,OAAK,KAf1EF,mBAAA,e,kCAQgCjC,MAAA,CAAAoC,IAAI,E,IARpCH,mBAAA,gB,CAkBuBjC,MAAA,CAAAC,KAAK,CAACO,MAAM,I,kBAC3Bd,mBAAA,CAI0BkB,SAAA;QAvBlCnB,GAAA;MAAA,GAAAoB,WAAA,CAmBgDb,MAAA,CAAAc,WAAW,EAnB3D,UAmBwCC,IAAI;6BAApCC,YAAA,CAI0BhB,MAAA;UAJ4BP,GAAG,EAAEsB,IAAI,CAACE,EAAE;UAAGC,UAAU,EAAEH,IAAI;UAAGE,EAAE,EAAEjB,MAAA,CAAAC,KAAK,CAACgB,EAAE;UACjGE,IAAI,EAAEnB,MAAA,CAAAC,KAAK,CAACkB,IAAI;UAAGC,MAAM,EAAEpB,MAAA,CAAAC,KAAK,CAACmB,MAAM;UAAGC,KAAK,EAAErB,MAAA,CAAAC,KAAK,CAACoB,KAAK;UAAGC,OAAO,EAAEtB,MAAA,CAAAC,KAAK,CAACqB,OAAO;UACrFe,OAAO,EAAErC,MAAA,CAAAC,KAAK,CAACoC,OAAO;UAAGd,SAAO,EAAEvB,MAAA,CAAAwB;;UArB7Cf,OAAA,EAAAC,QAAA,CAsBU,UAAA4B,KAAA;YAAA,IADsDZ,GAAG,GAAAY,KAAA,CAAHZ,GAAG;YAAA,QACzDC,WAAA,CAAwBC,IAAA,CAAAC,MAAA;cAAjBH,GAAG,EAAEA;YAAG,G;;UAtBzBI,CAAA;;uCAAAG,mBAAA,e;;IAAAH,CAAA;iCA0BsD9B,MAAA,CAAAC,KAAK,CAACO,MAAM,I,cAA9Dd,mBAAA,CAIM,OAJN6C,UAIM,GAHJlC,YAAA,CAE+BmC,wBAAA;IAFRC,WAAW,EAAEzC,MAAA,CAAA0C,MAAM;IA3BhD,wBAAAC,MAAA,QAAAA,MAAA,gBAAAC,MAAA;MAAA,OA2B0C5C,MAAA,CAAA0C,MAAM,GAAAE,MAAA;IAAA;IAAU,WAAS,EAAE5C,MAAA,CAAA6C,QAAQ;IA3B7E,qBAAAF,MAAA,QAAAA,MAAA,gBAAAC,MAAA;MAAA,OA2BqE5C,MAAA,CAAA6C,QAAQ,GAAAD,MAAA;IAAA;IAAG,YAAU,EAAE,sBAAsB;IAC1GE,MAAM,EAAC,yCAAyC;IAAEC,YAAW,EAAE/C,MAAA,CAAAgD,WAAW;IAAGC,eAAc,EAAEjD,MAAA,CAAAgD,WAAW;IACvGE,KAAK,EAAElD,MAAA,CAAAI,MAAM;IAAE+C,UAAU,EAAV;sEA7BxBlB,mBAAA,e", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
<template>
  <transition name="details-fade">
    <div v-show="modelValue" class="LiveBroadcastDetails">
      <div class="LiveBroadcastDetailsHeader">
        <img v-if="logoSrc" class="LiveBroadcastDetailsEmblem" :src="logoSrc" alt="emblem" />
        <div class="LiveBroadcastDetailsHeadInfo">
          <div class="LiveBroadcastDetailsTitle">{{ details.theme }}</div>
          <div class="LiveBroadcastDetailsTime">直播时间：{{ format(details.startTime) }} 到 {{
            format(details.endTime) }}</div>
        </div>
        <div class="LiveBroadcastDetailsActions">
          <el-button type="primary" @click="handleShare" class="share-btn">
            分享直播
          </el-button>
          <el-icon class="LiveBroadcastDetailsClose" @click="handleClose">
            <Close />
          </el-icon>
        </div>
      </div>
      <div class="LiveBroadcastDetailsBody">
        <div class="LiveBroadcastDetailsCanvas">
          <!-- 未开始：显示封面图 + 倒计时 -->
          <div v-if="details.meetingStatus === '未开始'" class="LiveBroadcastDetailsPoster">
            <img :src="imgUrl" alt="海报/播放画面区域" style="width: 100%;height: 100%;object-fit: contain;">
            <CountdownTimer :start-time="details.startTime" @countdown-end="handleCountdownEnd"
              ref="countdownTimerRef" />
          </div>
          <!-- 进行中：直播播放器 -->
          <div v-else-if="details.meetingStatus === '进行中'" class="LiveBroadcastDetailsLiveOverlay">
            <VideoPlayer v-if="details.liveUrl"
              :key="`video-player-live-${props.id}-${details.meetingStatus}-${isReplayMode}`"
              :live-url="details.liveUrl" :replay-url="details.liveReplayUrl" :is-replay="false" ref="videoPlayerRef" />
            <!-- 没有推流地址时显示提示 -->
            <div v-else class="LiveBroadcastDetailsNoStream">
              <img :src="imgUrl" alt="直播背景" class="no-stream-bg">
              <div class="no-stream-wrap">
                <div class="no-stream-title">暂无配置直播地址</div>
              </div>
            </div>
          </div>
          <!-- 已结束：回放播放器或背景图 + 悬浮内容 -->
          <div v-else-if="details.meetingStatus === '已结束'" class="LiveBroadcastDetailsEnded">
            <!-- 回放模式：显示回放播放器 -->
            <VideoPlayer v-if="isReplayMode && details.liveReplayUrl"
              :key="`video-player-replay-${props.id}-${details.meetingStatus}-${isReplayMode}`"
              :live-url="details.liveUrl" :replay-url="details.liveReplayUrl" :is-replay="true" ref="videoPlayerRef" />
            <!-- 非回放模式：显示背景图和按钮 -->
            <template v-else>
              <!-- 背景图 -->
              <img :src="imgUrl" alt="直播背景" class="LiveBroadcastDetailsEndedBg">
              <!-- 悬浮的结束状态内容 -->
              <div class="LiveBroadcastDetailsEndedWrap">
                <div class="endedTitle">直播已结束</div>
                <!-- 有回放地址：显示观看回放按钮 -->
                <el-button type="primary" class="replayBtn" v-if="details.isReplay == 1" @click="handleReplay">
                  观看回放
                </el-button>
              </div>
            </template>
          </div>
        </div>
        <div class="LiveBroadcastDetailsSidebar">
          <div class="LiveBroadcastDetailsTabs">
            <div :class="['LiveBroadcastDetailsTab', { active: activeTab === 'details' }]"
              @click="activeTab = 'details'">
              直播详情</div>
            <div :class="['LiveBroadcastDetailsTab', { active: activeTab === 'interact' }]"
              @click="activeTab = 'interact'">
              互动</div>
          </div>
          <div class="LiveBroadcastDetailsTabPane" v-if="activeTab === 'details'">
            <div class="detailsTitle">{{ details?.theme || '-' }}</div>
            <div class="detailsTimeBox">
              <img src="../../assets/img/icon_live_time.png" alt="" class="detailsTimeIcon">
              <span class="detailsTime">时间：{{ format(details?.startTime) }} - {{ format(details?.endTime) }}</span>
            </div>
            <div class="detailsDesc">{{ details.liveDescribes || '暂无简介' }}</div>
          </div>
          <div class="LiveBroadcastDetailsTabPane interact-pane" v-else>
            <!-- 评论列表 -->
            <div class="comments-container" ref="commentsContainer">
              <div v-for="comment in comments" :key="comment.id" class="comment-item">
                <!-- 主评论 -->
                <div class="comment-main">
                  <div class="comment-content">
                    <div class="comment-header">
                      <span class="comment-nickname">{{ comment.commentUserName }}</span>
                      <span class="comment-time">{{ format(comment.createDate) }}</span>
                    </div>
                    <div class="comment-text">{{ comment.commentContent }}</div>
                    <div class="comment-actions">
                      <span class="action-item" @click="showReplyInput(comment.id)">跟帖互动</span>
                      <span class="action-item like-btn" @click="toggleLike(comment)"
                        :class="{ liked: comment.hasClickPraises }">
                        <img
                          :src="comment.hasClickPraises ? require('../../assets/img/fabulous_o.png') : require('../../assets/img/fabulous.png')"
                          alt="点赞" class="like-icon" />
                        <span class="like-count">({{ comment.praisesCount || 0 }})</span>
                      </span>
                    </div>
                  </div>
                </div>

                <!-- 回复列表 -->
                <div v-if="comment.children && comment.children.length > 0" class="replies-container">
                  <div v-for="reply in comment.children" :key="reply.id" class="reply-item">
                    <div class="comment-content">
                      <div class="comment-header">
                        <span class="comment-nickname">{{ reply.commentUserName }}</span>
                        <!-- <span v-if="reply.replyTo" class="reply-to">回复 @{{ reply.replyTo }}</span> -->
                        <span class="comment-time">{{ format(reply.createDate) }}</span>
                      </div>
                      <div class="comment-text">{{ reply.commentContent }}</div>
                      <div class="comment-actions">
                        <span class="action-item like-btn" @click="toggleLike(reply)"
                          :class="{ liked: reply.hasClickPraises }">
                          <img
                            :src="reply.hasClickPraises ? require('../../assets/img/fabulous_o.png') : require('../../assets/img/fabulous.png')"
                            alt="点赞" class="like-icon" />
                          <span class="like-count">({{ reply.praisesCount || 0 }})</span>
                        </span>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- 回复输入框 -->
                <div v-if="replyingTo === comment.id" class="reply-input-container">
                  <div class="reply-input-wrapper">
                    <input v-model="replyContent" type="text"
                      :placeholder="replyToUser ? `回复 @${replyToUser}:` : '写下你的回复...'" class="reply-input"
                      @keyup.enter="submitReply(comment.id)" maxlength="200" ref="replyInputRef" />
                    <div class="reply-actions">
                      <button class="cancel-btn" @click="cancelReply">取消</button>
                      <button class="submit-btn" @click="submitReply(comment.id)" :disabled="!replyContent.trim()">
                        回复
                      </button>
                    </div>
                  </div>
                </div>
              </div>

              <!-- 空状态 -->
              <div v-if="comments.length === 0" class="comments-empty">
                <div class="empty-text">暂无评论</div>
              </div>
            </div>

            <!-- 发表评论输入框 -->
            <div class="comment-input-area">
              <div class="input-wrapper">
                <textarea v-model="newComment" placeholder="说点什么..." class="comment-input" @keyup.enter="submitComment"
                  maxlength="200" rows="3" style="width: 100%;height: 100%;"></textarea>
                <div class="submit-comment-btn" @click="submitComment">
                  <div class="comment-btn">发送</div>
                  <img src="../../assets/img/icon_live_send.png" alt="发送" class="send-icon" />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </transition>
  <!-- 分享弹窗 -->
  <ShareDialog v-model="shareDialogVisible" :live-id="props.id" :live-data="details" />
</template>
<script>
export default { name: 'LiveBroadcastDetails' }
</script>
<script setup>
import api from '@/api'
import { ref, onBeforeUnmount, onMounted, computed, watch, nextTick } from 'vue'
import { format } from 'common/js/time.js'
import { ElMessage } from 'element-plus'
import CountdownTimer from './components/CountdownTimer.vue'
import VideoPlayer from './components/VideoPlayer.vue'
import ShareDialog from './components/ShareDialog.vue'

const props = defineProps({
  modelValue: { type: Boolean, default: false },
  id: { type: String, default: '' }
})
const emit = defineEmits(['callback', 'update:modelValue'])
const details = ref({})
const logoSrc = ref('https://xazx.cszysoft.com:8131/lzt/pageImg/open/logo?areaId=610100')
const activeTab = ref('details')
const imgUrl = computed(() => details.value.coverImg ? api.fileURL(details.value.coverImg) : '')

// 组件引用
const countdownTimerRef = ref(null)
const videoPlayerRef = ref(null)

// 回放模式标识
const isReplayMode = ref(false)
// 分享弹窗状态
const shareDialogVisible = ref(false)
// 评论相关数据
const comments = ref([])
const newComment = ref('')
const replyingTo = ref(null)
const replyContent = ref('')
const replyToUser = ref('')
const replyInputRef = ref(null)
// 评论定时刷新
let commentTimer = null
// 监听状态变化
watch(() => details.value.meetingStatus, (newStatus, oldStatus) => {
  if (newStatus === '进行中' && oldStatus !== '进行中') {
    // 只有在有推流地址时才初始化播放器
    if (details.value.liveUrl) {
      nextTick(() => {
        if (videoPlayerRef.value) {
          videoPlayerRef.value.initPlayer()
        }
      })
    }
  } else if (newStatus === '已结束' && oldStatus !== '已结束') {
    // 如果是回放模式且有回放地址，初始化回放播放器
    if (isReplayMode.value && details.value.liveReplayUrl) {
      nextTick(() => {
        if (videoPlayerRef.value) {
          videoPlayerRef.value.initPlayer()
        }
      })
    }
  } else if ((newStatus !== '进行中' && oldStatus === '进行中') ||
    (newStatus !== '已结束' && oldStatus === '已结束' && isReplayMode.value)) {
    // 从进行中或回放状态变为其他状态，销毁播放器
    if (videoPlayerRef.value) {
      videoPlayerRef.value.destroyVideoPlayer()
    }
  }
})

// 倒计时结束处理
const handleCountdownEnd = () => {
  // 倒计时结束后切换到进行中状态
  if (details.value.meetingStatus === '未开始') {
    isReplayMode.value = false
    details.value.meetingStatus = '进行中'
  }
}

// 初始化播放器（根据当前状态）
const initPlayerByStatus = () => {
  if (details.value.meetingStatus === '进行中' && details.value.liveUrl) {
    // 进行中状态且有推流地址，初始化直播播放器
    setTimeout(() => {
      if (videoPlayerRef.value) {
        videoPlayerRef.value.initPlayer()
      }
    }, 100)
  } else if (details.value.meetingStatus === '已结束' && isReplayMode.value && details.value.liveReplayUrl) {
    // 已结束状态且是回放模式且有回放地址，初始化回放播放器
    setTimeout(() => {
      if (videoPlayerRef.value) {
        videoPlayerRef.value.initPlayer()
      }
    }, 100)
  }
}

onMounted(() => {
  // 在 onMounted 中，如果组件已经显示且有 id，则加载数据
  if (props.modelValue && props.id) {
    getInfo()
    getCommentData()
    // 开始评论定时刷新
    startCommentRefresh()
  }
})

// 监听 modelValue 变化 - 当组件从隐藏变为显示时加载数据
watch(() => props.modelValue, (newVal, oldVal) => {
  if (newVal && props.id && !oldVal) {
    // 从 false 变为 true 且有 id 时加载数据
    console.log('弹窗打开，开始加载数据')
    // 重置状态，确保每次打开都是干净的状态
    isReplayMode.value = false
    activeTab.value = 'details'
    getInfo()
    getCommentData()
    // 开始评论定时刷新
    startCommentRefresh()
  } else if (!newVal && oldVal) {
    // 从显示变为隐藏时，停止评论定时刷新
    console.log('弹窗关闭，停止定时刷新')
    stopCommentRefresh()
  }
})

// 监听 id 变化 - 当 id 改变且组件显示时加载数据
watch(() => props.id, (newVal, oldVal) => {
  if (newVal && props.modelValue && newVal !== oldVal) {
    getInfo()
    getCommentData()
    // 重新开始评论定时刷新
    startCommentRefresh()
  }
})

const getInfo = async () => {
  const res = await api.videoConnectionInfo({ detailId: props.id })
  var { data } = res
  details.value = data
  getLiveBrowseCount()
  // 根据状态初始化播放器
  initPlayerByStatus()
}

const getLiveBrowseCount = async () => {
  const res = await api.liveWatchCountAdd({ form: { dataId: props.id } })
  var { data } = res
  console.log('data===>', data)
}

// 获取评论数据
const getCommentData = async () => {
  const res = await api.twoLevelTree({
    businessCode: 'liveBroadcast',
    businessId: props.id,
    pageNo: 1,
    pageSize: 99,
  })
  console.log('评论数据:', res)
  comments.value = res.data || []
}

// 开始评论定时刷新
const startCommentRefresh = () => {
  if (commentTimer) clearInterval(commentTimer)
  commentTimer = setInterval(() => {
    getCommentData()
  }, 3000) // 每3秒刷新一次
}

// 停止评论定时刷新
const stopCommentRefresh = () => {
  if (commentTimer) {
    clearInterval(commentTimer)
    commentTimer = null
  }
}

// 打开回放
const handleReplay = () => {
  if (!details.value.liveReplayUrl) {
    ElMessage({ type: 'error', message: '没有回放地址' })
    return
  }
  // 先销毁现有的播放器
  if (videoPlayerRef.value) {
    videoPlayerRef.value.destroyVideoPlayer()
  }
  // 设置回放模式
  isReplayMode.value = true
  // 确保VideoPlayer组件能够正确初始化
  nextTick(() => {
    // 由于key变化，组件会重新创建，需要重新获取ref
    setTimeout(() => {
      if (videoPlayerRef.value) {
        videoPlayerRef.value.initPlayer()
      }
    }, 100)
  })
}

// 提交新评论
const submitComment = async () => {
  if (!newComment.value.trim()) return
  const res = await api.commentNew({
    form: {
      businessCode: 'liveBroadcast',
      businessId: props.id,
      checkedStatus: 1,
      commentContent: newComment.value,
      parentId: "",
      terminalName: 'PC'
    }
  })

  if (res.code == 200) {
    ElMessage({ type: 'success', message: '评论发表成功' })
    getCommentData()
  } else {
    ElMessage({ type: 'error', message: res.message || '评论发表失败' })
  }
  newComment.value = ''
}

// 点赞功能
const toggleLike = async (item) => {
  if (item.hasClickPraises) {
    // 取消点赞
    const res = await api.praisesDels({
      businessCode: "comment",
      businessId: item.id
    })

    if (res.code == 200) {
      item.hasClickPraises = false
      item.praisesCount = Math.max(0, (item.praisesCount || 0) - 1)
      ElMessage({
        message: '取消点赞成功',
        type: 'success'
      })
    } else {
      ElMessage({
        message: res.message || '取消点赞失败',
        type: 'error'
      })
    }
  } else {
    // 点赞
    const res = await api.praisesAdd({
      form: {
        businessCode: "comment",
        businessId: item.id
      }
    })

    if (res.code == 200) {
      item.hasClickPraises = true
      item.praisesCount = (item.praisesCount || 0) + 1
      ElMessage({
        message: '点赞成功',
        type: 'success'
      })
    } else {
      ElMessage({
        message: res.message || '点赞失败',
        type: 'error'
      })
    }
  }
}

// 显示回复输入框
const showReplyInput = (commentId, replyToUsername = '') => {
  replyingTo.value = commentId
  replyToUser.value = replyToUsername
  replyContent.value = ''

  nextTick(() => {
    if (replyInputRef.value) {
      replyInputRef.value.focus()
    }
  })
}

// 取消回复
const cancelReply = () => {
  replyingTo.value = null
  replyToUser.value = ''
  replyContent.value = ''
}

// 提交回复
const submitReply = async (commentId) => {
  if (!replyContent.value.trim()) return
  const res = await api.commentNew({
    form: {
      businessCode: "liveBroadcast",
      businessId: props.id,
      checkedStatus: 1,
      commentContent: replyContent.value.trim(),
      parentId: commentId,
      terminalName: "PC"
    }
  })
  if (res.code == 200) {
    ElMessage({
      message: '回复发表成功',
      type: 'success'
    })
    getCommentData()
    cancelReply()
  } else {
    ElMessage({
      message: res.message || '回复发表失败',
      type: 'error'
    })
  }
}

onBeforeUnmount(() => {
  // 清理评论定时器
  stopCommentRefresh()
  // 销毁视频播放器
  if (videoPlayerRef.value) {
    videoPlayerRef.value.destroyVideoPlayer()
  }
})

// 分享直播功能
const handleShare = () => {
  if (!props.id) {
    ElMessage.error('直播ID不能为空')
    return
  }

  // 打开分享弹窗
  shareDialogVisible.value = true
}

const handleClose = () => {
  // 停止评论定时刷新
  stopCommentRefresh()
  // 销毁视频播放器
  if (videoPlayerRef.value) {
    videoPlayerRef.value.destroyVideoPlayer()
  }
  // 重置状态
  isReplayMode.value = false
  activeTab.value = 'details'
  // 清空数据引用，确保下次打开时重新加载
  details.value = {}
  emit('update:modelValue', false)
  emit('callback')
}
</script>
<style lang="scss">
.LiveBroadcastDetails {
  position: fixed;
  inset: 0;
  width: 100vw;
  height: 100vh;
  background: #0F0F0F;
  display: flex;
  flex-direction: column;
  z-index: 1000;

  .LiveBroadcastDetailsHeader {
    position: relative;
    height: 80px;
    display: flex;
    align-items: center;
    padding: 0 16px;
    color: #fff;
    background: #2B2B2B;
    border-bottom: 1px solid rgba(255, 255, 255, 0.08);

    .LiveBroadcastDetailsEmblem {
      width: 58px;
      height: 58px;
      margin-right: 14px;
    }

    .LiveBroadcastDetailsHeadInfo {
      display: flex;
      flex-direction: column;
    }

    .LiveBroadcastDetailsTitle {
      font-family: Microsoft YaHei, Microsoft YaHei;
      font-weight: bold;
      font-size: 20px;
      color: #FFFFFF;
      margin-bottom: 12px;
    }

    .LiveBroadcastDetailsTime {
      font-weight: 400;
      font-size: 14px;
      color: #D9D9D9;
    }

    .LiveBroadcastDetailsActions {
      position: absolute;
      right: 16px;
      top: 50%;
      transform: translateY(-50%);
      display: flex;
      align-items: center;
      gap: 12px;

      .share-btn {
        background: linear-gradient(90deg, #2F04FF 0%, #00F0FE 100%);
        border: none;
        color: #FFFFFF;
        font-size: 14px;
        padding: 8px 16px;
        border-radius: 6px;

        &:hover {
          opacity: 0.9;
        }
      }
    }

    .LiveBroadcastDetailsClose {
      color: #fff;
      cursor: pointer;
      font-size: 18px;
      opacity: .85;

      &:hover {
        opacity: 1;
      }
    }
  }

  .LiveBroadcastDetailsBody {
    flex: 1;
    display: grid;
    grid-template-columns: 1fr 360px;
    gap: 16px;
    padding: 16px;
    overflow: hidden;
  }

  .LiveBroadcastDetailsCanvas {
    position: relative;
    height: 100%;
    background: #111;
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 0;

    .LiveBroadcastDetailsPoster {
      width: 100%;
      height: 100%;
      background: #000;
      display: flex;
      align-items: center;
      justify-content: center;
      color: #d23a2e;
      font-weight: bold;
      font-size: 22px;
    }

    .LiveBroadcastDetailsLiveOverlay {
      position: absolute;
      left: 0;
      right: 0;
      top: 0;
      bottom: 0;
      width: 100%;
      height: 100%;
      z-index: 1;
    }

    .LiveBroadcastDetailsNoStream {
      position: absolute;
      inset: 0;
      display: flex;
      align-items: center;
      justify-content: center;

      .no-stream-bg {
        position: absolute;
        inset: 0;
        width: 100%;
        height: 100%;
        object-fit: contain;
        z-index: 1;
      }

      // 遮罩层覆盖整个图片
      &::before {
        content: '';
        position: absolute;
        inset: 0;
        background: rgba(0, 0, 0, 0.45);
        z-index: 2;
      }

      .no-stream-wrap {
        position: relative;
        z-index: 3;
        text-align: center;

        .no-stream-title {
          font-weight: 400;
          font-size: 16px;
          color: #FFFFFF;
          text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
        }
      }
    }

    .LiveBroadcastDetailsEnded {
      position: absolute;
      inset: 0;
      display: flex;
      align-items: center;
      justify-content: center;

      .LiveBroadcastDetailsEndedBg {
        position: absolute;
        inset: 0;
        width: 100%;
        height: 100%;
        object-fit: contain;
        z-index: 1;
      }

      // 遮罩层覆盖整个图片
      &::before {
        content: '';
        position: absolute;
        inset: 0;
        background: rgba(0, 0, 0, 0.45);
        z-index: 2;
      }

      .LiveBroadcastDetailsEndedWrap {
        position: relative;
        z-index: 3;
        text-align: center;

        .endedTitle {
          font-weight: 400;
          font-size: 16px;
          color: #FFFFFF;
          margin-bottom: 20px;
          text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
        }

        .replayBtn {
          background: linear-gradient(90deg, #2F04FF 0%, #00F0FE 100%);
          border-radius: 6px;
          border: none;
          padding: 12px 0;
          font-size: 18px;
          color: #FFFFFF;
          width: 175px;
          height: 44px;
        }
      }
    }
  }

  .LiveBroadcastDetailsSidebar {
    height: 100%;
    background: #191919;
    border-left: 1px solid rgba(255, 255, 255, 0.05);
    color: #e8e8e8;
    // padding: 14px 16px 16px 16px;
    overflow: auto;

    .LiveBroadcastDetailsTabs {
      display: flex;
      align-items: center;
      justify-content: space-around;
      gap: 40px;
      margin-bottom: 10px;
      background: #2B2B2B;
      padding: 14px 16px;

      .LiveBroadcastDetailsTab {
        cursor: pointer;
        color: #999999;
        position: relative;
        font-size: 16px;
        line-height: 1;
        transition: color .2s ease;
        font-weight: 500;

        &.active {
          color: #ffffff;
          font-weight: 700;
        }

        &.active::after {
          content: '';
          position: absolute;
          left: 0;
          right: 0;
          bottom: -8px;
          height: 3px;
          background: #54BDFF;
          border-radius: 3px;
        }
      }
    }

    .LiveBroadcastDetailsTabPane {
      padding: 12px 15px;
    }

    .LiveBroadcastDetailsPanelTitle {
      font-weight: bold;
      margin-bottom: 10px;
      font-size: 15px;
    }

    .LiveBroadcastDetailsPanelText {
      font-size: 13px;
      line-height: 1.8;
      color: #cfcfcf;
    }

    /* 详情tab内样式 */
    .detailsTitle {
      font-weight: 400;
      font-size: 18px;
      color: #FFFFFF;
    }

    .detailsTimeBox {
      display: flex;
      align-items: center;
      margin-top: 14px;

      .detailsTime {
        font-weight: 400;
        font-size: 14px;
        color: #666666;
      }

      .detailsTimeIcon {
        width: 16px;
        height: 16px;
        margin-right: 5px;
      }
    }


    .detailsDesc {
      font-weight: 400;
      font-size: 14px;
      color: #FFFFFF;
      line-height: 24px;
      margin-top: 24px;
    }

    /* 互动评论样式 */
    .interact-pane {
      display: flex;
      flex-direction: column;
      height: calc(100vh - 80px - 60px - 32px);
      padding: 0 !important;
    }

    .comments-container {
      flex: 1;
      overflow-y: auto;
      padding: 12px 15px;

      &::-webkit-scrollbar {
        width: 4px;
      }

      &::-webkit-scrollbar-track {
        background: #2B2B2B;
      }

      &::-webkit-scrollbar-thumb {
        background: #666;
        border-radius: 2px;
      }
    }

    .comment-item {
      margin-bottom: 20px;

      .comment-main {
        display: flex;
        margin-bottom: 8px;
      }

      .comment-content {
        flex: 1;
        min-width: 0;

        .comment-header {
          display: flex;
          align-items: center;
          margin-bottom: 6px;

          .comment-nickname {
            font-size: 13px;
            color: #54BDFF;
            margin-right: 8px;
            font-weight: 500;
          }

          .reply-to {
            font-size: 12px;
            color: #999999;
            margin-right: 8px;
          }

          .comment-time {
            font-size: 11px;
            color: #666666;
          }
        }

        .comment-text {
          font-size: 14px;
          color: #FFFFFF;
          line-height: 20px;
          word-wrap: break-word;
          margin-bottom: 8px;
        }

        .comment-actions {
          display: flex;
          align-items: center;
          gap: 16px;
          margin-bottom: 8px;

          .action-item {
            display: flex;
            align-items: center;
            gap: 4px;
            font-size: 12px;
            color: #999999;
            cursor: pointer;
            transition: color 0.2s;

            &:hover {
              color: #54BDFF;
            }

            &.like-btn {
              &.liked {
                color: #54BDFF;
              }
            }

            .like-icon {
              width: 14px;
              height: 14px;
            }

            .like-count {
              font-size: 11px;
            }
          }
        }
      }
    }

    // 回复列表样式
    .replies-container {
      margin-left: 42px;
      border-left: 2px solid rgba(255, 255, 255, 0.05);
      padding-left: 12px;

      .reply-item {
        margin-bottom: 12px;

        .comment-content {
          .comment-header {
            .comment-nickname {
              font-size: 12px;
            }
          }

          .comment-text {
            font-size: 13px;
          }
        }
      }
    }

    // 回复输入框样式
    .reply-input-container {
      margin-left: 42px;
      margin-top: 8px;

      .reply-input-wrapper {
        background: #2B2B2B;
        border-radius: 6px;
        padding: 8px;

        .reply-input {
          width: 100%;
          height: 32px;
          padding: 0 8px;
          background: #191919;
          border: 1px solid rgba(255, 255, 255, 0.1);
          border-radius: 4px;
          color: #FFFFFF;
          font-size: 13px;
          outline: none;
          margin-bottom: 8px;

          &::placeholder {
            color: #666666;
          }

          &:focus {
            border-color: #54BDFF;
          }
        }

        .reply-actions {
          display: flex;
          justify-content: flex-end;
          gap: 8px;

          .cancel-btn,
          .submit-btn {
            height: 28px;
            padding: 0 12px;
            border: none;
            border-radius: 4px;
            font-size: 12px;
            cursor: pointer;
            transition: background-color 0.2s;
          }

          .cancel-btn {
            background: transparent;
            color: #999999;

            &:hover {
              background: rgba(255, 255, 255, 0.05);
            }
          }

          .submit-btn {
            background: #54BDFF;
            color: #FFFFFF;

            &:hover:not(:disabled) {
              background: #4AA8E8;
            }

            &:disabled {
              background: #333333;
              color: #666666;
              cursor: not-allowed;
            }
          }
        }
      }
    }

    .comments-empty {
      display: flex;
      align-items: center;
      justify-content: center;
      height: 200px;

      .empty-text {
        font-size: 14px;
        color: #666666;
      }
    }

    .comment-input-area {
      border-top: 1px solid rgba(255, 255, 255, 0.08);
      padding: 12px;

      .input-wrapper {
        background: #4A4A4A;
        border-radius: 8px;
        padding: 12px;
        height: 90px;
        position: relative;

        .comment-input {
          width: 100%;
          height: calc(100% - 40px);
          padding: 0;
          padding-right: 80px;
          background: transparent;
          border: none;
          color: #FFFFFF;
          font-size: 14px;
          outline: none;
          resize: none;
          font-family: inherit;
          line-height: 1.4;

          &::placeholder {
            color: #999999;
          }
        }

        .submit-comment-btn {
          position: absolute;
          bottom: 8px;
          right: 8px;
          cursor: pointer;
          display: flex;
          align-items: center;
          gap: 4px;

          .comment-btn {
            color: #54BDFF;
            font-size: 17px;
            font-weight: 500;
          }

          .send-icon {
            width: 18px;
            height: 18px;
          }

          &:disabled {
            background: #666666;
            color: #999999;
            cursor: not-allowed;
          }
        }
      }
    }
  }
}

.details-fade-enter-active,
.details-fade-leave-active {
  transition: opacity .2s ease;
}

.details-fade-enter-from,
.details-fade-leave-to {
  opacity: 0;
}
</style>

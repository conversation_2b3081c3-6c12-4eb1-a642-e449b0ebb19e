{"ast": null, "code": "import { resolveComponent as _resolveComponent, createVNode as _createVNode, withCtx as _withCtx, createTextVNode as _createTextVNode, createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock } from \"vue\";\nvar _hoisted_1 = {\n  class: \"TemplateManageNew\"\n};\nvar _hoisted_2 = {\n  class: \"globalFormButton\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  var _component_el_input = _resolveComponent(\"el-input\");\n  var _component_el_form_item = _resolveComponent(\"el-form-item\");\n  var _component_el_button = _resolveComponent(\"el-button\");\n  var _component_el_form = _resolveComponent(\"el-form\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createVNode(_component_el_form, {\n    ref: \"formRef\",\n    model: $setup.form,\n    rules: $setup.rules,\n    inline: \"\",\n    \"label-position\": \"top\",\n    class: \"globalForm\"\n  }, {\n    default: _withCtx(function () {\n      return [_createVNode(_component_el_form_item, {\n        label: \"模板内容\",\n        prop: \"content\",\n        class: \"globalFormTitle\"\n      }, {\n        default: _withCtx(function () {\n          return [_createVNode(_component_el_input, {\n            modelValue: $setup.form.content,\n            \"onUpdate:modelValue\": _cache[0] || (_cache[0] = function ($event) {\n              return $setup.form.content = $event;\n            }),\n            type: \"textarea\",\n            placeholder: \"请输入模板内容\",\n            rows: \"6\"\n          }, null, 8 /* PROPS */, [\"modelValue\"])];\n        }),\n        _: 1 /* STABLE */\n      }), _createElementVNode(\"div\", _hoisted_2, [_createVNode(_component_el_button, {\n        type: \"primary\",\n        onClick: _cache[1] || (_cache[1] = function ($event) {\n          return $setup.submitForm($setup.formRef);\n        })\n      }, {\n        default: _withCtx(function () {\n          return _cache[2] || (_cache[2] = [_createTextVNode(\"提交\")]);\n        }),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_button, {\n        onClick: $setup.resetForm\n      }, {\n        default: _withCtx(function () {\n          return _cache[3] || (_cache[3] = [_createTextVNode(\"取消\")]);\n        }),\n        _: 1 /* STABLE */\n      })])];\n    }),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"model\", \"rules\"])]);\n}", "map": {"version": 3, "names": ["class", "_createElementBlock", "_hoisted_1", "_createVNode", "_component_el_form", "ref", "model", "$setup", "form", "rules", "inline", "default", "_withCtx", "_component_el_form_item", "label", "prop", "_component_el_input", "modelValue", "content", "_cache", "$event", "type", "placeholder", "rows", "_", "_createElementVNode", "_hoisted_2", "_component_el_button", "onClick", "submitForm", "formRef", "_createTextVNode", "resetForm"], "sources": ["D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\interaction\\src\\views\\TemplateManage\\TemplateManageNew.vue"], "sourcesContent": ["<template>\r\n  <div class=\"TemplateManageNew\">\r\n    <el-form ref=\"formRef\"\r\n             :model=\"form\"\r\n             :rules=\"rules\"\r\n             inline\r\n             label-position=\"top\"\r\n             class=\"globalForm\">\r\n      <el-form-item label=\"模板内容\"\r\n                    prop=\"content\"\r\n                    class=\"globalFormTitle\">\r\n        <el-input v-model=\"form.content\"\r\n                  type=\"textarea\"\r\n                  placeholder=\"请输入模板内容\"\r\n                  rows=\"6\" />\r\n      </el-form-item>\r\n      <div class=\"globalFormButton\">\r\n        <el-button type=\"primary\"\r\n                   @click=\"submitForm(formRef)\">提交</el-button>\r\n        <el-button @click=\"resetForm\">取消</el-button>\r\n      </div>\r\n    </el-form>\r\n  </div>\r\n</template>\r\n<script>\r\nexport default { name: 'TemplateManageNew' }\r\n</script>\r\n<script setup>\r\nimport api from '@/api'\r\nimport { reactive, ref, onMounted } from 'vue'\r\nimport { ElMessage } from 'element-plus'\r\nconst props = defineProps({ id: { type: String, default: '' } })\r\nconst emit = defineEmits(['callback'])\r\n\r\nconst formRef = ref()\r\nconst form = reactive({\r\n  content: '' // 模板内容\r\n})\r\nconst rules = reactive({\r\n  content: [{ required: true, message: '请输入模板内容', trigger: ['blur', 'change'] }]\r\n})\r\n\r\nonMounted(() => {\r\n  if (props.id) { themeTemplateInfo() }\r\n})\r\n\r\nconst themeTemplateInfo = async () => {\r\n  const res = await api.themeTemplateInfo({ detailId: props.id })\r\n  var { data } = res\r\n  form.content = data.content\r\n}\r\nconst submitForm = async (formEl) => {\r\n  if (!formEl) return\r\n  await formEl.validate((valid, fields) => {\r\n    if (valid) { globalJson() } else { ElMessage({ type: 'warning', message: '请根据提示信息完善字段内容！' }) }\r\n  })\r\n}\r\nconst globalJson = async () => {\r\n  const { code } = await api.globalJson('/themeTemplate/edit', {\r\n    form: {\r\n      id: props.id,\r\n      content: form.content\r\n    }\r\n  })\r\n  if (code === 200) {\r\n    ElMessage({ type: 'success', message: '编辑成功' })\r\n    emit('callback')\r\n  }\r\n}\r\nconst resetForm = () => { emit('callback') }\r\n</script>\r\n<style lang=\"scss\">\r\n.TemplateManageNew {\r\n  width: 680px;\r\n}\r\n</style>\r\n"], "mappings": ";;EACOA,KAAK,EAAC;AAAmB;;EAerBA,KAAK,EAAC;AAAkB;;;;;;uBAfjCC,mBAAA,CAqBM,OArBNC,UAqBM,GApBJC,YAAA,CAmBUC,kBAAA;IAnBDC,GAAG,EAAC,SAAS;IACZC,KAAK,EAAEC,MAAA,CAAAC,IAAI;IACXC,KAAK,EAAEF,MAAA,CAAAE,KAAK;IACbC,MAAM,EAAN,EAAM;IACN,gBAAc,EAAC,KAAK;IACpBV,KAAK,EAAC;;IAPnBW,OAAA,EAAAC,QAAA,CAQM;MAAA,OAOe,CAPfT,YAAA,CAOeU,uBAAA;QAPDC,KAAK,EAAC,MAAM;QACZC,IAAI,EAAC,SAAS;QACdf,KAAK,EAAC;;QAV1BW,OAAA,EAAAC,QAAA,CAWQ;UAAA,OAGqB,CAHrBT,YAAA,CAGqBa,mBAAA;YAd7BC,UAAA,EAW2BV,MAAA,CAAAC,IAAI,CAACU,OAAO;YAXvC,uBAAAC,MAAA,QAAAA,MAAA,gBAAAC,MAAA;cAAA,OAW2Bb,MAAA,CAAAC,IAAI,CAACU,OAAO,GAAAE,MAAA;YAAA;YACrBC,IAAI,EAAC,UAAU;YACfC,WAAW,EAAC,SAAS;YACrBC,IAAI,EAAC;;;QAdvBC,CAAA;UAgBMC,mBAAA,CAIM,OAJNC,UAIM,GAHJvB,YAAA,CACsDwB,oBAAA;QAD3CN,IAAI,EAAC,SAAS;QACbO,OAAK,EAAAT,MAAA,QAAAA,MAAA,gBAAAC,MAAA;UAAA,OAAEb,MAAA,CAAAsB,UAAU,CAACtB,MAAA,CAAAuB,OAAO;QAAA;;QAlB7CnB,OAAA,EAAAC,QAAA,CAkBgD;UAAA,OAAEO,MAAA,QAAAA,MAAA,OAlBlDY,gBAAA,CAkBgD,IAAE,E;;QAlBlDP,CAAA;UAmBQrB,YAAA,CAA4CwB,oBAAA;QAAhCC,OAAK,EAAErB,MAAA,CAAAyB;MAAS;QAnBpCrB,OAAA,EAAAC,QAAA,CAmBsC;UAAA,OAAEO,MAAA,QAAAA,MAAA,OAnBxCY,gBAAA,CAmBsC,IAAE,E;;QAnBxCP,CAAA;;;IAAAA,CAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
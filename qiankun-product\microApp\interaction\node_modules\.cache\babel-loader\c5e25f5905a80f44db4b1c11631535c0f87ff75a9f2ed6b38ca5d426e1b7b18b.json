{"ast": null, "code": "import { defineComponent, inject, withDirectives, h } from 'vue';\nimport { isNil } from 'lodash-unified';\nimport '../../../utils/index.mjs';\nimport '../../../hooks/index.mjs';\nimport { descriptionsKey } from './token.mjs';\nimport { getNormalizedProps } from '../../../utils/vue/vnode.mjs';\nimport { addUnit } from '../../../utils/dom/style.mjs';\nimport { useNamespace } from '../../../hooks/use-namespace/index.mjs';\nvar ElDescriptionsCell = defineComponent({\n  name: \"ElDescriptionsCell\",\n  props: {\n    cell: {\n      type: Object\n    },\n    tag: {\n      type: String,\n      default: \"td\"\n    },\n    type: {\n      type: String\n    }\n  },\n  setup() {\n    var descriptions = inject(descriptionsKey, {});\n    return {\n      descriptions\n    };\n  },\n  render() {\n    var _a, _b, _c, _d, _e, _f, _g;\n    var item = getNormalizedProps(this.cell);\n    var directives = (((_a = this.cell) == null ? void 0 : _a.dirs) || []).map(function (dire) {\n      var dir = dire.dir,\n        arg = dire.arg,\n        modifiers = dire.modifiers,\n        value = dire.value;\n      return [dir, value, arg, modifiers];\n    });\n    var _this$descriptions = this.descriptions,\n      border = _this$descriptions.border,\n      direction = _this$descriptions.direction;\n    var isVertical = direction === \"vertical\";\n    var label = ((_d = (_c = (_b = this.cell) == null ? void 0 : _b.children) == null ? void 0 : _c.label) == null ? void 0 : _d.call(_c)) || item.label;\n    var content = (_g = (_f = (_e = this.cell) == null ? void 0 : _e.children) == null ? void 0 : _f.default) == null ? void 0 : _g.call(_f);\n    var span = item.span;\n    var align = item.align ? `is-${item.align}` : \"\";\n    var labelAlign = item.labelAlign ? `is-${item.labelAlign}` : align;\n    var className = item.className;\n    var labelClassName = item.labelClassName;\n    var style = {\n      width: addUnit(item.width),\n      minWidth: addUnit(item.minWidth)\n    };\n    var ns = useNamespace(\"descriptions\");\n    switch (this.type) {\n      case \"label\":\n        return withDirectives(h(this.tag, {\n          style,\n          class: [ns.e(\"cell\"), ns.e(\"label\"), ns.is(\"bordered-label\", border), ns.is(\"vertical-label\", isVertical), labelAlign, labelClassName],\n          colSpan: isVertical ? span : 1\n        }, label), directives);\n      case \"content\":\n        return withDirectives(h(this.tag, {\n          style,\n          class: [ns.e(\"cell\"), ns.e(\"content\"), ns.is(\"bordered-content\", border), ns.is(\"vertical-content\", isVertical), align, className],\n          colSpan: isVertical ? span : span * 2 - 1\n        }, content), directives);\n      default:\n        return withDirectives(h(\"td\", {\n          style,\n          class: [ns.e(\"cell\"), align],\n          colSpan: span\n        }, [!isNil(label) ? h(\"span\", {\n          class: [ns.e(\"label\"), labelClassName]\n        }, label) : void 0, h(\"span\", {\n          class: [ns.e(\"content\"), className]\n        }, content)]), directives);\n    }\n  }\n});\nexport { ElDescriptionsCell as default };", "map": {"version": 3, "names": ["ElDescriptionsCell", "defineComponent", "name", "props", "cell", "type", "Object", "tag", "String", "default", "setup", "descriptions", "inject", "<PERSON><PERSON><PERSON>", "render", "_a", "_b", "_c", "_d", "_e", "_f", "_g", "item", "getNormalizedProps", "directives", "dirs", "map", "dire", "dir", "arg", "modifiers", "value", "_this$descriptions", "border", "direction", "isVertical", "label", "children", "call", "content", "span", "align", "labelAlign", "className", "labelClassName", "style", "width", "addUnit", "min<PERSON><PERSON><PERSON>", "ns", "useNamespace", "withDirectives", "h", "class", "e", "is", "colSpan", "isNil"], "sources": ["../../../../../../packages/components/descriptions/src/descriptions-cell.ts"], "sourcesContent": ["import { defineComponent, h, inject, withDirectives } from 'vue'\nimport { isNil } from 'lodash-unified'\nimport { addUnit, getNormalizedProps } from '@element-plus/utils'\nimport { useNamespace } from '@element-plus/hooks'\nimport { descriptionsKey } from './token'\nimport type { DirectiveArguments, PropType, VNode } from 'vue'\n\nimport type {\n  IDescriptionsInject,\n  IDescriptionsItemInject,\n} from './descriptions.type'\nimport type { DescriptionItemVNode } from './description-item'\n\nexport default defineComponent({\n  name: 'ElDescriptionsCell',\n  props: {\n    cell: {\n      type: Object as PropType<DescriptionItemVNode>,\n    },\n    tag: {\n      type: String,\n      default: 'td',\n    },\n    type: {\n      type: String,\n    },\n  },\n  setup() {\n    const descriptions = inject(descriptionsKey, {} as IDescriptionsInject)\n\n    return {\n      descriptions,\n    }\n  },\n  render() {\n    const item = getNormalizedProps(\n      this.cell as VNode\n    ) as IDescriptionsItemInject\n\n    const directives = (this.cell?.dirs || []).map((dire) => {\n      const { dir, arg, modifiers, value } = dire\n      return [dir, value, arg, modifiers]\n    }) as DirectiveArguments\n\n    const { border, direction } = this.descriptions\n    const isVertical = direction === 'vertical'\n    const label = this.cell?.children?.label?.() || item.label\n    const content = this.cell?.children?.default?.()\n    const span = item.span\n    const align = item.align ? `is-${item.align}` : ''\n    const labelAlign = item.labelAlign ? `is-${item.labelAlign}` : '' || align\n    const className = item.className\n    const labelClassName = item.labelClassName\n    const style = {\n      width: addUnit(item.width),\n      minWidth: addUnit(item.minWidth),\n    }\n    const ns = useNamespace('descriptions')\n\n    switch (this.type) {\n      case 'label':\n        return withDirectives(\n          h(\n            this.tag,\n            {\n              style,\n              class: [\n                ns.e('cell'),\n                ns.e('label'),\n                ns.is('bordered-label', border),\n                ns.is('vertical-label', isVertical),\n                labelAlign,\n                labelClassName,\n              ],\n              colSpan: isVertical ? span : 1,\n            },\n            label\n          ),\n          directives\n        )\n      case 'content':\n        return withDirectives(\n          h(\n            this.tag,\n            {\n              style,\n              class: [\n                ns.e('cell'),\n                ns.e('content'),\n                ns.is('bordered-content', border),\n                ns.is('vertical-content', isVertical),\n                align,\n                className,\n              ],\n              colSpan: isVertical ? span : span * 2 - 1,\n            },\n            content\n          ),\n          directives\n        )\n      default:\n        return withDirectives(\n          h(\n            'td',\n            {\n              style,\n              class: [ns.e('cell'), align],\n              colSpan: span,\n            },\n            [\n              !isNil(label)\n                ? h(\n                    'span',\n                    {\n                      class: [ns.e('label'), labelClassName],\n                    },\n                    label\n                  )\n                : undefined,\n              h(\n                'span',\n                {\n                  class: [ns.e('content'), className],\n                },\n                content\n              ),\n            ]\n          ),\n          directives\n        )\n    }\n  },\n})\n"], "mappings": ";;;;;;;;AAKA,IAAAA,kBAAA,GAAeC,eAAe,CAAC;EAC7BC,IAAI,EAAE,oBAAoB;EAC1BC,KAAK,EAAE;IACLC,IAAI,EAAE;MACJC,IAAI,EAAEC;IACZ,CAAK;IACDC,GAAG,EAAE;MACHF,IAAI,EAAEG,MAAM;MACZC,OAAO,EAAE;IACf,CAAK;IACDJ,IAAI,EAAE;MACJA,IAAI,EAAEG;IACZ;EACA,CAAG;EACDE,KAAKA,CAAA,EAAG;IACN,IAAMC,YAAY,GAAGC,MAAM,CAACC,eAAe,EAAE,EAAE,CAAC;IAChD,OAAO;MACLF;IACN,CAAK;EACL,CAAG;EACDG,MAAMA,CAAA,EAAG;IACP,IAAIC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE;IAC9B,IAAMC,IAAI,GAAGC,kBAAkB,CAAC,IAAI,CAACnB,IAAI,CAAC;IAC1C,IAAMoB,UAAU,GAAG,CAAC,CAAC,CAACT,EAAE,GAAG,IAAI,CAACX,IAAI,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGW,EAAE,CAACU,IAAI,KAAK,EAAE,EAAEC,GAAG,CAAC,UAACC,IAAI,EAAK;MACrF,IAAQC,GAAG,GAA4BD,IAAI,CAAnCC,GAAG;QAAEC,GAAG,GAAuBF,IAAI,CAA9BE,GAAG;QAAEC,SAAS,GAAYH,IAAI,CAAzBG,SAAS;QAAEC,KAAK,GAAKJ,IAAI,CAAdI,KAAK;MAClC,OAAO,CAACH,GAAG,EAAEG,KAAK,EAAEF,GAAG,EAAEC,SAAS,CAAC;IACzC,CAAK,CAAC;IACF,IAAAE,kBAAA,GAA8B,IAAI,CAACrB,YAAY;MAAvCsB,MAAM,GAAAD,kBAAA,CAANC,MAAM;MAAEC,SAAS,GAAAF,kBAAA,CAATE,SAAS;IACzB,IAAMC,UAAU,GAAGD,SAAS,KAAK,UAAU;IAC3C,IAAME,KAAK,GAAG,CAAC,CAAClB,EAAE,GAAG,CAACD,EAAE,GAAG,CAACD,EAAE,GAAG,IAAI,CAACZ,IAAI,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGY,EAAE,CAACqB,QAAQ,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGpB,EAAE,CAACmB,KAAK,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGlB,EAAE,CAACoB,IAAI,CAACrB,EAAE,CAAC,KAAKK,IAAI,CAACc,KAAK;IACtJ,IAAMG,OAAO,GAAG,CAAClB,EAAE,GAAG,CAACD,EAAE,GAAG,CAACD,EAAE,GAAG,IAAI,CAACf,IAAI,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGe,EAAE,CAACkB,QAAQ,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGjB,EAAE,CAACX,OAAO,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGY,EAAE,CAACiB,IAAI,CAAClB,EAAE,CAAC;IAC1I,IAAMoB,IAAI,GAAGlB,IAAI,CAACkB,IAAI;IACtB,IAAMC,KAAK,GAAGnB,IAAI,CAACmB,KAAK,GAAG,MAAMnB,IAAI,CAACmB,KAAK,EAAE,GAAG,EAAE;IAClD,IAAMC,UAAU,GAAGpB,IAAI,CAACoB,UAAU,GAAG,MAAMpB,IAAI,CAACoB,UAAU,EAAE,GAAGD,KAAK;IACpE,IAAME,SAAS,GAAGrB,IAAI,CAACqB,SAAS;IAChC,IAAMC,cAAc,GAAGtB,IAAI,CAACsB,cAAc;IAC1C,IAAMC,KAAK,GAAG;MACZC,KAAK,EAAEC,OAAO,CAACzB,IAAI,CAACwB,KAAK,CAAC;MAC1BE,QAAQ,EAAED,OAAO,CAACzB,IAAI,CAAC0B,QAAQ;IACrC,CAAK;IACD,IAAMC,EAAE,GAAGC,YAAY,CAAC,cAAc,CAAC;IACvC,QAAQ,IAAI,CAAC7C,IAAI;MACf,KAAK,OAAO;QACV,OAAO8C,cAAc,CAACC,CAAC,CAAC,IAAI,CAAC7C,GAAG,EAAE;UAChCsC,KAAK;UACLQ,KAAK,EAAE,CACLJ,EAAE,CAACK,CAAC,CAAC,MAAM,CAAC,EACZL,EAAE,CAACK,CAAC,CAAC,OAAO,CAAC,EACbL,EAAE,CAACM,EAAE,CAAC,gBAAgB,EAAEtB,MAAM,CAAC,EAC/BgB,EAAE,CAACM,EAAE,CAAC,gBAAgB,EAAEpB,UAAU,CAAC,EACnCO,UAAU,EACVE,cAAc,CACf;UACDY,OAAO,EAAErB,UAAU,GAAGK,IAAI,GAAG;QACvC,CAAS,EAAEJ,KAAK,CAAC,EAAEZ,UAAU,CAAC;MACxB,KAAK,SAAS;QACZ,OAAO2B,cAAc,CAACC,CAAC,CAAC,IAAI,CAAC7C,GAAG,EAAE;UAChCsC,KAAK;UACLQ,KAAK,EAAE,CACLJ,EAAE,CAACK,CAAC,CAAC,MAAM,CAAC,EACZL,EAAE,CAACK,CAAC,CAAC,SAAS,CAAC,EACfL,EAAE,CAACM,EAAE,CAAC,kBAAkB,EAAEtB,MAAM,CAAC,EACjCgB,EAAE,CAACM,EAAE,CAAC,kBAAkB,EAAEpB,UAAU,CAAC,EACrCM,KAAK,EACLE,SAAS,CACV;UACDa,OAAO,EAAErB,UAAU,GAAGK,IAAI,GAAGA,IAAI,GAAG,CAAC,GAAG;QAClD,CAAS,EAAED,OAAO,CAAC,EAAEf,UAAU,CAAC;MAC1B;QACE,OAAO2B,cAAc,CAACC,CAAC,CAAC,IAAI,EAAE;UAC5BP,KAAK;UACLQ,KAAK,EAAE,CAACJ,EAAE,CAACK,CAAC,CAAC,MAAM,CAAC,EAAEb,KAAK,CAAC;UAC5Be,OAAO,EAAEhB;QACnB,CAAS,EAAE,CACD,CAACiB,KAAK,CAACrB,KAAK,CAAC,GAAGgB,CAAC,CAAC,MAAM,EAAE;UACxBC,KAAK,EAAE,CAACJ,EAAE,CAACK,CAAC,CAAC,OAAO,CAAC,EAAEV,cAAc;QACjD,CAAW,EAAER,KAAK,CAAC,GAAG,KAAK,CAAC,EAClBgB,CAAC,CAAC,MAAM,EAAE;UACRC,KAAK,EAAE,CAACJ,EAAE,CAACK,CAAC,CAAC,SAAS,CAAC,EAAEX,SAAS;QAC9C,CAAW,EAAEJ,OAAO,CAAC,CACZ,CAAC,EAAEf,UAAU,CAAC;IACvB;EACA;AACA,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
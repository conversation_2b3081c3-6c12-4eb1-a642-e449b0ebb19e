{"ast": null, "code": "function _classCallCheck(a, n) { if (!(a instanceof n)) throw new TypeError(\"Cannot call a class as a function\"); }\nfunction _defineProperties(e, r) { for (var t = 0; t < r.length; t++) { var o = r[t]; o.enumerable = o.enumerable || !1, o.configurable = !0, \"value\" in o && (o.writable = !0), Object.defineProperty(e, _toPropertyKey(o.key), o); } }\nfunction _createClass(e, r, t) { return r && _defineProperties(e.prototype, r), t && _defineProperties(e, t), Object.defineProperty(e, \"prototype\", { writable: !1 }), e; }\nfunction _toPropertyKey(t) { var i = _toPrimitive(t, \"string\"); return \"symbol\" == typeof i ? i : i + \"\"; }\nfunction _toPrimitive(t, r) { if (\"object\" != typeof t || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || \"default\"); if (\"object\" != typeof i) return i; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (\"string\" === r ? String : Number)(t); }\nimport '../../../../utils/index.mjs';\nimport '../../../../constants/index.mjs';\nimport { EVENT_CODE } from '../../../../constants/aria.mjs';\nimport { triggerEvent } from '../../../../utils/dom/aria.mjs';\nvar SubMenu = /*#__PURE__*/function () {\n  function SubMenu(parent, domNode) {\n    _classCallCheck(this, SubMenu);\n    this.parent = parent;\n    this.domNode = domNode;\n    this.subIndex = 0;\n    this.subIndex = 0;\n    this.init();\n  }\n  return _createClass(SubMenu, [{\n    key: \"init\",\n    value: function init() {\n      this.subMenuItems = this.domNode.querySelectorAll(\"li\");\n      this.addListeners();\n    }\n  }, {\n    key: \"gotoSubIndex\",\n    value: function gotoSubIndex(idx) {\n      if (idx === this.subMenuItems.length) {\n        idx = 0;\n      } else if (idx < 0) {\n        idx = this.subMenuItems.length - 1;\n      }\n      ;\n      this.subMenuItems[idx].focus();\n      this.subIndex = idx;\n    }\n  }, {\n    key: \"addListeners\",\n    value: function addListeners() {\n      var _this = this;\n      var parentNode = this.parent.domNode;\n      Array.prototype.forEach.call(this.subMenuItems, function (el) {\n        el.addEventListener(\"keydown\", function (event) {\n          var prevDef = false;\n          switch (event.code) {\n            case EVENT_CODE.down:\n              {\n                _this.gotoSubIndex(_this.subIndex + 1);\n                prevDef = true;\n                break;\n              }\n            case EVENT_CODE.up:\n              {\n                _this.gotoSubIndex(_this.subIndex - 1);\n                prevDef = true;\n                break;\n              }\n            case EVENT_CODE.tab:\n              {\n                triggerEvent(parentNode, \"mouseleave\");\n                break;\n              }\n            case EVENT_CODE.enter:\n            case EVENT_CODE.space:\n              {\n                prevDef = true;\n                event.currentTarget.click();\n                break;\n              }\n          }\n          if (prevDef) {\n            event.preventDefault();\n            event.stopPropagation();\n          }\n          return false;\n        });\n      });\n    }\n  }]);\n}();\nexport { SubMenu as default };", "map": {"version": 3, "names": ["SubMenu", "parent", "domNode", "_classCallCheck", "subIndex", "init", "_createClass", "key", "value", "subMenuItems", "querySelectorAll", "addListeners", "gotoSubIndex", "idx", "length", "focus", "_this", "parentNode", "Array", "prototype", "for<PERSON>ach", "call", "el", "addEventListener", "event", "prevDef", "code", "EVENT_CODE", "down", "up", "tab", "triggerEvent", "enter", "space", "currentTarget", "click", "preventDefault", "stopPropagation"], "sources": ["../../../../../../../packages/components/menu/src/utils/submenu.ts"], "sourcesContent": ["// @ts-nocheck\nimport { triggerEvent } from '@element-plus/utils'\nimport { EVENT_CODE } from '@element-plus/constants'\nimport type MenuItem from './menu-item'\n\nclass SubMenu {\n  public subMenuItems: NodeList\n  public subIndex = 0\n  constructor(public parent: MenuItem, public domNode: ParentNode) {\n    this.subIndex = 0\n    this.init()\n  }\n\n  init(): void {\n    this.subMenuItems = this.domNode.querySelectorAll('li')\n    this.addListeners()\n  }\n\n  gotoSubIndex(idx: number): void {\n    if (idx === this.subMenuItems.length) {\n      idx = 0\n    } else if (idx < 0) {\n      idx = this.subMenuItems.length - 1\n    }\n    ;(this.subMenuItems[idx] as HTMLElement).focus()\n    this.subIndex = idx\n  }\n\n  addListeners(): void {\n    const parentNode = this.parent.domNode\n    Array.prototype.forEach.call(this.subMenuItems, (el: Element) => {\n      el.addEventListener('keydown', (event: KeyboardEvent) => {\n        let prevDef = false\n        switch (event.code) {\n          case EVENT_CODE.down: {\n            this.gotoSubIndex(this.subIndex + 1)\n            prevDef = true\n            break\n          }\n          case EVENT_CODE.up: {\n            this.gotoSubIndex(this.subIndex - 1)\n            prevDef = true\n            break\n          }\n          case EVENT_CODE.tab: {\n            triggerEvent(parentNode as HTMLElement, 'mouseleave')\n            break\n          }\n          case EVENT_CODE.enter:\n          case EVENT_CODE.space: {\n            prevDef = true\n            ;(event.currentTarget as HTMLElement).click()\n            break\n          }\n        }\n        if (prevDef) {\n          event.preventDefault()\n          event.stopPropagation()\n        }\n        return false\n      })\n    })\n  }\n}\n\nexport default SubMenu\n"], "mappings": ";;;;;;;;;IAEMA,OAAO;EACX,SAAAA,QAAYC,MAAM,EAAEC,OAAO,EAAE;IAAAC,eAAA,OAAAH,OAAA;IAC3B,IAAI,CAACC,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACC,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACE,QAAQ,GAAG,CAAC;IACjB,IAAI,CAACA,QAAQ,GAAG,CAAC;IACjB,IAAI,CAACC,IAAI,EAAE;EACf;EAAG,OAAAC,YAAA,CAAAN,OAAA;IAAAO,GAAA;IAAAC,KAAA,EACD,SAAAH,IAAIA,CAAA,EAAG;MACL,IAAI,CAACI,YAAY,GAAG,IAAI,CAACP,OAAO,CAACQ,gBAAgB,CAAC,IAAI,CAAC;MACvD,IAAI,CAACC,YAAY,EAAE;IACvB;EAAG;IAAAJ,GAAA;IAAAC,KAAA,EACD,SAAAI,YAAYA,CAACC,GAAG,EAAE;MAChB,IAAIA,GAAG,KAAK,IAAI,CAACJ,YAAY,CAACK,MAAM,EAAE;QACpCD,GAAG,GAAG,CAAC;MACb,CAAK,MAAM,IAAIA,GAAG,GAAG,CAAC,EAAE;QAClBA,GAAG,GAAG,IAAI,CAACJ,YAAY,CAACK,MAAM,GAAG,CAAC;MACxC;MACI;MACA,IAAI,CAACL,YAAY,CAACI,GAAG,CAAC,CAACE,KAAK,EAAE;MAC9B,IAAI,CAACX,QAAQ,GAAGS,GAAG;IACvB;EAAG;IAAAN,GAAA;IAAAC,KAAA,EACD,SAAAG,YAAYA,CAAA,EAAG;MAAA,IAAAK,KAAA;MACb,IAAMC,UAAU,GAAG,IAAI,CAAChB,MAAM,CAACC,OAAO;MACtCgB,KAAK,CAACC,SAAS,CAACC,OAAO,CAACC,IAAI,CAAC,IAAI,CAACZ,YAAY,EAAE,UAACa,EAAE,EAAK;QACtDA,EAAE,CAACC,gBAAgB,CAAC,SAAS,EAAE,UAACC,KAAK,EAAK;UACxC,IAAIC,OAAO,GAAG,KAAK;UACnB,QAAQD,KAAK,CAACE,IAAI;YAChB,KAAKC,UAAU,CAACC,IAAI;cAAE;gBACpBZ,KAAI,CAACJ,YAAY,CAACI,KAAI,CAACZ,QAAQ,GAAG,CAAC,CAAC;gBACpCqB,OAAO,GAAG,IAAI;gBACd;cACZ;YACU,KAAKE,UAAU,CAACE,EAAE;cAAE;gBAClBb,KAAI,CAACJ,YAAY,CAACI,KAAI,CAACZ,QAAQ,GAAG,CAAC,CAAC;gBACpCqB,OAAO,GAAG,IAAI;gBACd;cACZ;YACU,KAAKE,UAAU,CAACG,GAAG;cAAE;gBACnBC,YAAY,CAACd,UAAU,EAAE,YAAY,CAAC;gBACtC;cACZ;YACU,KAAKU,UAAU,CAACK,KAAK;YACrB,KAAKL,UAAU,CAACM,KAAK;cAAE;gBACrBR,OAAO,GAAG,IAAI;gBACdD,KAAK,CAACU,aAAa,CAACC,KAAK,EAAE;gBAC3B;cACZ;UACA;UACQ,IAAIV,OAAO,EAAE;YACXD,KAAK,CAACY,cAAc,EAAE;YACtBZ,KAAK,CAACa,eAAe,EAAE;UACjC;UACQ,OAAO,KAAK;QACpB,CAAO,CAAC;MACR,CAAK,CAAC;IACN;EAAG;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
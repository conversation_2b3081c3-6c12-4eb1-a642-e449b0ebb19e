{"ast": null, "code": "import '../../../../utils/index.mjs';\nimport '../../../../constants/index.mjs';\nimport { buildProps, definePropType } from '../../../../utils/vue/props/runtime.mjs';\nimport { datePickTypes } from '../../../../constants/date.mjs';\nimport { isArray } from '@vue/shared';\nvar selectionModes = [\"date\", \"dates\", \"year\", \"years\", \"month\", \"week\", \"range\"];\nvar datePickerSharedProps = buildProps({\n  disabledDate: {\n    type: definePropType(Function)\n  },\n  date: {\n    type: definePropType(Object),\n    required: true\n  },\n  minDate: {\n    type: definePropType(Object)\n  },\n  maxDate: {\n    type: definePropType(Object)\n  },\n  parsedValue: {\n    type: definePropType([Object, Array])\n  },\n  rangeState: {\n    type: definePropType(Object),\n    default: function _default() {\n      return {\n        endDate: null,\n        selecting: false\n      };\n    }\n  }\n});\nvar panelSharedProps = buildProps({\n  type: {\n    type: definePropType(String),\n    required: true,\n    values: datePickTypes\n  },\n  dateFormat: String,\n  timeFormat: String\n});\nvar panelRangeSharedProps = buildProps({\n  unlinkPanels: Boolean,\n  parsedValue: {\n    type: definePropType(Array)\n  }\n});\nvar selectionModeWithDefault = function selectionModeWithDefault(mode) {\n  return {\n    type: String,\n    values: selectionModes,\n    default: mode\n  };\n};\nvar rangePickerSharedEmits = {\n  pick: function pick(range) {\n    return isArray(range);\n  }\n};\nexport { datePickerSharedProps, panelRangeSharedProps, panelSharedProps, rangePickerSharedEmits, selectionModeWithDefault };", "map": {"version": 3, "names": ["selectionModes", "datePickerSharedProps", "buildProps", "disabledDate", "type", "definePropType", "Function", "date", "Object", "required", "minDate", "maxDate", "parsedValue", "Array", "rangeState", "default", "endDate", "selecting", "panelSharedProps", "String", "values", "datePickTypes", "dateFormat", "timeFormat", "panelRangeSharedProps", "unlinkPanels", "Boolean", "selectionModeWithDefault", "mode", "rangePickerSharedEmits", "pick", "range", "isArray"], "sources": ["../../../../../../../packages/components/date-picker/src/props/shared.ts"], "sourcesContent": ["import { buildProps, definePropType, isArray } from '@element-plus/utils'\nimport { datePickTypes } from '@element-plus/constants'\n\nimport type { ExtractPropTypes } from 'vue'\nimport type { Dayjs } from 'dayjs'\nimport type { DatePickType } from '@element-plus/constants'\n\nconst selectionModes = [\n  'date',\n  'dates',\n  'year',\n  'years',\n  'month',\n  'week',\n  'range',\n]\n\nexport type RangeState = {\n  endDate: null | Dayjs\n  selecting: boolean\n}\n\nexport const datePickerSharedProps = buildProps({\n  disabledDate: {\n    type: definePropType<(date: Date) => boolean>(Function),\n  },\n  date: {\n    type: definePropType<Dayjs>(Object),\n    required: true,\n  },\n  minDate: {\n    type: definePropType<Dayjs | null>(Object),\n  },\n  maxDate: {\n    type: definePropType<Dayjs | null>(Object),\n  },\n  parsedValue: {\n    type: definePropType<Dayjs | Dayjs[]>([Object, Array]),\n  },\n  rangeState: {\n    type: definePropType<RangeState>(Object),\n    default: () => ({\n      endDate: null,\n      selecting: false,\n    }),\n  },\n} as const)\n\nexport const panelSharedProps = buildProps({\n  type: {\n    type: definePropType<DatePickType>(String),\n    required: true,\n    values: datePickTypes,\n  },\n  dateFormat: String,\n  timeFormat: String,\n} as const)\n\nexport const panelRangeSharedProps = buildProps({\n  unlinkPanels: Boolean,\n  parsedValue: {\n    type: definePropType<Dayjs[]>(Array),\n  },\n} as const)\n\nexport const selectionModeWithDefault = (\n  mode: typeof selectionModes[number]\n) => {\n  return {\n    type: String,\n    values: selectionModes,\n    default: mode,\n  }\n}\n\nexport const rangePickerSharedEmits = {\n  pick: (range: [Dayjs, Dayjs]) => isArray(range),\n}\n\nexport type RangePickerSharedEmits = typeof rangePickerSharedEmits\nexport type PanelRangeSharedProps = ExtractPropTypes<\n  typeof panelRangeSharedProps\n>\n"], "mappings": ";;;;;AAEA,IAAMA,cAAc,GAAG,CACrB,MAAM,EACN,OAAO,EACP,MAAM,EACN,OAAO,EACP,OAAO,EACP,MAAM,EACN,OAAO,CACR;AACW,IAACC,qBAAqB,GAAGC,UAAU,CAAC;EAC9CC,YAAY,EAAE;IACZC,IAAI,EAAEC,cAAc,CAACC,QAAQ;EACjC,CAAG;EACDC,IAAI,EAAE;IACJH,IAAI,EAAEC,cAAc,CAACG,MAAM,CAAC;IAC5BC,QAAQ,EAAE;EACd,CAAG;EACDC,OAAO,EAAE;IACPN,IAAI,EAAEC,cAAc,CAACG,MAAM;EAC/B,CAAG;EACDG,OAAO,EAAE;IACPP,IAAI,EAAEC,cAAc,CAACG,MAAM;EAC/B,CAAG;EACDI,WAAW,EAAE;IACXR,IAAI,EAAEC,cAAc,CAAC,CAACG,MAAM,EAAEK,KAAK,CAAC;EACxC,CAAG;EACDC,UAAU,EAAE;IACVV,IAAI,EAAEC,cAAc,CAACG,MAAM,CAAC;IAC5BO,OAAO,EAAE,SAATA,QAAOA,CAAA;MAAA,OAAS;QACdC,OAAO,EAAE,IAAI;QACbC,SAAS,EAAE;MACjB,CAAK;IAAA;EACL;AACA,CAAC;AACW,IAACC,gBAAgB,GAAGhB,UAAU,CAAC;EACzCE,IAAI,EAAE;IACJA,IAAI,EAAEC,cAAc,CAACc,MAAM,CAAC;IAC5BV,QAAQ,EAAE,IAAI;IACdW,MAAM,EAAEC;EACZ,CAAG;EACDC,UAAU,EAAEH,MAAM;EAClBI,UAAU,EAAEJ;AACd,CAAC;AACW,IAACK,qBAAqB,GAAGtB,UAAU,CAAC;EAC9CuB,YAAY,EAAEC,OAAO;EACrBd,WAAW,EAAE;IACXR,IAAI,EAAEC,cAAc,CAACQ,KAAK;EAC9B;AACA,CAAC;AACW,IAACc,wBAAwB,GAAG,SAA3BA,wBAAwBA,CAAIC,IAAI,EAAK;EAChD,OAAO;IACLxB,IAAI,EAAEe,MAAM;IACZC,MAAM,EAAEpB,cAAc;IACtBe,OAAO,EAAEa;EACb,CAAG;AACH;AACY,IAACC,sBAAsB,GAAG;EACpCC,IAAI,EAAE,SAANA,IAAIA,CAAGC,KAAK;IAAA,OAAKC,OAAO,CAACD,KAAK,CAAC;EAAA;AACjC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
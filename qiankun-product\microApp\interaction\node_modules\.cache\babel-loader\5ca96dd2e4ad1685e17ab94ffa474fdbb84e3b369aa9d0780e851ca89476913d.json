{"ast": null, "code": "import { createCommentVNode as _createCommentVNode, createVNode as _createVNode, openBlock as _openBlock, createElementBlock as _createElementBlock } from \"vue\";\nvar _hoisted_1 = {\n  class: \"LiveShare\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createCommentVNode(\" 直播详情组件，始终显示 \"), _createVNode($setup[\"LiveBroadcastDetails\"], {\n    \"model-value\": true,\n    id: $setup.liveId,\n    onCallback: $setup.handleCallback,\n    \"onUpdate:modelValue\": $setup.handleModelValueUpdate\n  }, null, 8 /* PROPS */, [\"id\"])]);\n}", "map": {"version": 3, "names": ["class", "_createElementBlock", "_hoisted_1", "_createCommentVNode", "_createVNode", "$setup", "id", "liveId", "onCallback", "handleCallback", "handleModelValueUpdate"], "sources": ["D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\interaction\\src\\views\\LiveManagement\\LiveShare.vue"], "sourcesContent": ["<template>\n  <div class=\"LiveShare\">\n    <!-- 直播详情组件，始终显示 -->\n    <LiveBroadcastDetails :model-value=\"true\" :id=\"liveId\" @callback=\"handleCallback\"\n      @update:modelValue=\"handleModelValueUpdate\" />\n  </div>\n</template>\n\n<script>\nexport default { name: 'LiveShare' }\n</script>\n\n<script setup>\nimport { ref, onMounted } from 'vue'\nimport { useRoute } from 'vue-router'\nimport { ElMessage } from 'element-plus'\nimport LiveBroadcastDetails from './LiveBroadcastDetails.vue'\n\nconst route = useRoute()\n// const router = useRouter()\nconst liveId = ref('')\n\nonMounted(() => {\n  // 从路由参数获取直播ID\n  liveId.value = route.params.id\n\n  if (!liveId.value) {\n    ElMessage.error('直播ID不能为空')\n    // 可以跳转到错误页面或首页\n    return\n  }\n})\n\n// 处理关闭回调 - 分享页面不需要关闭，可以跳转到其他页面\nconst handleCallback = () => {\n  // 可以根据需要跳转到首页或其他页面\n  // router.push('/')\n}\n\n// 处理模型值更新 - 分享页面始终保持显示\nconst handleModelValueUpdate = (value) => {\n  // 分享页面不允许关闭，如果用户点击关闭按钮，可以提示或跳转\n  if (!value) {\n    ElMessage.info('您可以通过浏览器返回按钮离开此页面')\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.LiveShare {\n  width: 100%;\n  height: 100vh;\n  overflow: hidden;\n}\n</style>\n"], "mappings": ";;EACOA,KAAK,EAAC;AAAW;;uBAAtBC,mBAAA,CAIM,OAJNC,UAIM,GAHJC,mBAAA,iBAAoB,EACpBC,YAAA,CACgDC,MAAA;IADzB,aAAW,EAAE,IAAI;IAAGC,EAAE,EAAED,MAAA,CAAAE,MAAM;IAAGC,UAAQ,EAAEH,MAAA,CAAAI,cAAc;IAC7E,qBAAiB,EAAEJ,MAAA,CAAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
import { globalLocation } from 'common/config/location'
/**
 * 分享工具函数
 */

/**
 * 生成直播分享链接
 * @param {string} liveId - 直播ID
 * @returns {string} 分享链接
 */
export const generateLiveShareUrl = (liveId) => {
  if (!liveId) {
    throw new Error('直播ID不能为空')
  }
  // 根据当前环境生成正确的分享链接
  const entryUrl = `${globalLocation(process.env.VUE_APP_NAME)}LiveShare?id=${liveId}`
  return entryUrl
}

/**
 * 复制文本到剪贴板
 * @param {string} text - 要复制的文本
 * @returns {Promise<boolean>} 是否复制成功
 */
export const copyToClipboard = async (text) => {
  try {
    // 优先使用现代 Clipboard API
    if (navigator.clipboard && window.isSecureContext) {
      await navigator.clipboard.writeText(text)
      return true
    } else {
      // 降级到传统方法
      return fallbackCopyTextToClipboard(text)
    }
  } catch (error) {
    console.error('复制失败:', error)
    return false
  }
}

/**
 * 兼容性复制方法
 * @param {string} text - 要复制的文本
 * @returns {boolean} 是否复制成功
 */
const fallbackCopyTextToClipboard = (text) => {
  const textArea = document.createElement('textarea')
  textArea.value = text
  textArea.style.top = '0'
  textArea.style.left = '0'
  textArea.style.position = 'fixed'
  textArea.style.opacity = '0'

  document.body.appendChild(textArea)
  textArea.focus()
  textArea.select()

  try {
    const successful = document.execCommand('copy')
    document.body.removeChild(textArea)
    return successful
  } catch (err) {
    document.body.removeChild(textArea)
    console.error('Fallback copy failed:', err)
    return false
  }
}



/**
 * 获取分享信息
 * @param {Object} liveData - 直播数据
 * @returns {Object} 分享信息
 */
export const getShareInfo = (liveData) => {
  const { theme, startTime, endTime, meetingStatus } = liveData

  let statusText = ''
  switch (meetingStatus) {
    case '未开始':
      statusText = '即将开始'
      break
    case '进行中':
      statusText = '正在直播'
      break
    case '已结束':
      statusText = '精彩回放'
      break
    default:
      statusText = '精彩直播'
  }

  return {
    title: `${statusText} - ${theme}`,
    description: `直播时间：${startTime} 到 ${endTime}`,
    status: statusText
  }
}

/**
 * 分享工具函数
 */

/**
 * 生成直播分享链接
 * @param {string} liveId - 直播ID
 * @returns {string} 分享链接
 */
export const generateLiveShareUrl = (liveId) => {
  if (!liveId) {
    throw new Error('直播ID不能为空')
  }
  
  const baseUrl = window.location.origin
  const pathname = window.location.pathname
  
  // 根据当前环境生成正确的分享链接
  return `${baseUrl}${pathname}#/MicroAppContainer/interaction/LiveShare/${liveId}`
}

/**
 * 复制文本到剪贴板
 * @param {string} text - 要复制的文本
 * @returns {Promise<boolean>} 是否复制成功
 */
export const copyToClipboard = async (text) => {
  try {
    // 优先使用现代 Clipboard API
    if (navigator.clipboard && window.isSecureContext) {
      await navigator.clipboard.writeText(text)
      return true
    } else {
      // 降级到传统方法
      return fallbackCopyTextToClipboard(text)
    }
  } catch (error) {
    console.error('复制失败:', error)
    return false
  }
}

/**
 * 兼容性复制方法
 * @param {string} text - 要复制的文本
 * @returns {boolean} 是否复制成功
 */
const fallbackCopyTextToClipboard = (text) => {
  const textArea = document.createElement('textarea')
  textArea.value = text
  textArea.style.top = '0'
  textArea.style.left = '0'
  textArea.style.position = 'fixed'
  textArea.style.opacity = '0'
  
  document.body.appendChild(textArea)
  textArea.focus()
  textArea.select()
  
  try {
    const successful = document.execCommand('copy')
    document.body.removeChild(textArea)
    return successful
  } catch (err) {
    document.body.removeChild(textArea)
    console.error('Fallback copy failed:', err)
    return false
  }
}

/**
 * 分享到社交平台
 * @param {string} url - 分享链接
 * @param {string} title - 分享标题
 * @param {string} platform - 平台类型 ('wechat', 'qq', 'weibo', 'copy')
 */
export const shareToSocial = (url, title = '精彩直播分享', platform = 'copy') => {
  const encodedUrl = encodeURIComponent(url)
  const encodedTitle = encodeURIComponent(title)
  
  switch (platform) {
    case 'wechat':
      // 微信分享通常需要微信 JS-SDK，这里提供复制链接的方式
      copyToClipboard(url)
      return '链接已复制，请在微信中粘贴分享'
      
    case 'qq':
      // QQ分享
      window.open(`https://connect.qq.com/widget/shareqq/index.html?url=${encodedUrl}&title=${encodedTitle}`)
      break
      
    case 'weibo':
      // 微博分享
      window.open(`https://service.weibo.com/share/share.php?url=${encodedUrl}&title=${encodedTitle}`)
      break
      
    case 'copy':
    default:
      return copyToClipboard(url)
  }
}

/**
 * 获取分享信息
 * @param {Object} liveData - 直播数据
 * @returns {Object} 分享信息
 */
export const getShareInfo = (liveData) => {
  const { theme, startTime, endTime, meetingStatus } = liveData
  
  let statusText = ''
  switch (meetingStatus) {
    case '未开始':
      statusText = '即将开始'
      break
    case '进行中':
      statusText = '正在直播'
      break
    case '已结束':
      statusText = '精彩回放'
      break
    default:
      statusText = '精彩直播'
  }
  
  return {
    title: `${statusText} - ${theme}`,
    description: `直播时间：${startTime} 到 ${endTime}`,
    status: statusText
  }
}

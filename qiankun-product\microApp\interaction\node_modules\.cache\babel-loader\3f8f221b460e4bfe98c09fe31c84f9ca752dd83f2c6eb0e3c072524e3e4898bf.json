{"ast": null, "code": "/**\n * Resize detection strategy that injects objects to elements in order to detect resize events.\n * Heavily inspired by: http://www.backalleycoder.com/2013/03/18/cross-browser-event-based-element-resize-detection/\n */\n\n\"use strict\";\n\nvar browserDetector = require(\"../browser-detector\");\nmodule.exports = function (options) {\n  options = options || {};\n  var reporter = options.reporter;\n  var batchProcessor = options.batchProcessor;\n  var getState = options.stateHandler.getState;\n  if (!reporter) {\n    throw new Error(\"Missing required dependency: reporter.\");\n  }\n\n  /**\n   * Adds a resize event listener to the element.\n   * @public\n   * @param {element} element The element that should have the listener added.\n   * @param {function} listener The listener callback to be called for each resize event of the element. The element will be given as a parameter to the listener callback.\n   */\n  function addListener(element, listener) {\n    function listenerProxy() {\n      listener(element);\n    }\n    if (browserDetector.isIE(8)) {\n      //IE 8 does not support object, but supports the resize event directly on elements.\n      getState(element).object = {\n        proxy: listenerProxy\n      };\n      element.attachEvent(\"onresize\", listenerProxy);\n    } else {\n      var object = getObject(element);\n      if (!object) {\n        throw new Error(\"Element is not detectable by this strategy.\");\n      }\n      object.contentDocument.defaultView.addEventListener(\"resize\", listenerProxy);\n    }\n  }\n  function buildCssTextString(rules) {\n    var seperator = options.important ? \" !important; \" : \"; \";\n    return (rules.join(seperator) + seperator).trim();\n  }\n\n  /**\n   * Makes an element detectable and ready to be listened for resize events. Will call the callback when the element is ready to be listened for resize changes.\n   * @private\n   * @param {object} options Optional options object.\n   * @param {element} element The element to make detectable\n   * @param {function} callback The callback to be called when the element is ready to be listened for resize changes. Will be called with the element as first parameter.\n   */\n  function makeDetectable(options, element, callback) {\n    if (!callback) {\n      callback = element;\n      element = options;\n      options = null;\n    }\n    options = options || {};\n    var debug = options.debug;\n    function injectObject(element, callback) {\n      var OBJECT_STYLE = buildCssTextString([\"display: block\", \"position: absolute\", \"top: 0\", \"left: 0\", \"width: 100%\", \"height: 100%\", \"border: none\", \"padding: 0\", \"margin: 0\", \"opacity: 0\", \"z-index: -1000\", \"pointer-events: none\"]);\n\n      //The target element needs to be positioned (everything except static) so the absolute positioned object will be positioned relative to the target element.\n\n      // Position altering may be performed directly or on object load, depending on if style resolution is possible directly or not.\n      var positionCheckPerformed = false;\n\n      // The element may not yet be attached to the DOM, and therefore the style object may be empty in some browsers.\n      // Since the style object is a reference, it will be updated as soon as the element is attached to the DOM.\n      var style = window.getComputedStyle(element);\n      var width = element.offsetWidth;\n      var height = element.offsetHeight;\n      getState(element).startSize = {\n        width: width,\n        height: height\n      };\n      function mutateDom() {\n        function alterPositionStyles() {\n          if (style.position === \"static\") {\n            element.style.setProperty(\"position\", \"relative\", options.important ? \"important\" : \"\");\n            var removeRelativeStyles = function removeRelativeStyles(reporter, element, style, property) {\n              function getNumericalValue(value) {\n                return value.replace(/[^-\\d\\.]/g, \"\");\n              }\n              var value = style[property];\n              if (value !== \"auto\" && getNumericalValue(value) !== \"0\") {\n                reporter.warn(\"An element that is positioned static has style.\" + property + \"=\" + value + \" which is ignored due to the static positioning. The element will need to be positioned relative, so the style.\" + property + \" will be set to 0. Element: \", element);\n                element.style.setProperty(property, \"0\", options.important ? \"important\" : \"\");\n              }\n            };\n\n            //Check so that there are no accidental styles that will make the element styled differently now that is is relative.\n            //If there are any, set them to 0 (this should be okay with the user since the style properties did nothing before [since the element was positioned static] anyway).\n            removeRelativeStyles(reporter, element, style, \"top\");\n            removeRelativeStyles(reporter, element, style, \"right\");\n            removeRelativeStyles(reporter, element, style, \"bottom\");\n            removeRelativeStyles(reporter, element, style, \"left\");\n          }\n        }\n        function onObjectLoad() {\n          // The object has been loaded, which means that the element now is guaranteed to be attached to the DOM.\n          if (!positionCheckPerformed) {\n            alterPositionStyles();\n          }\n\n          /*jshint validthis: true */\n\n          function getDocument(element, callback) {\n            //Opera 12 seem to call the object.onload before the actual document has been created.\n            //So if it is not present, poll it with an timeout until it is present.\n            //TODO: Could maybe be handled better with object.onreadystatechange or similar.\n            if (!element.contentDocument) {\n              var state = getState(element);\n              if (state.checkForObjectDocumentTimeoutId) {\n                window.clearTimeout(state.checkForObjectDocumentTimeoutId);\n              }\n              state.checkForObjectDocumentTimeoutId = setTimeout(function checkForObjectDocument() {\n                state.checkForObjectDocumentTimeoutId = 0;\n                getDocument(element, callback);\n              }, 100);\n              return;\n            }\n            callback(element.contentDocument);\n          }\n\n          //Mutating the object element here seems to fire another load event.\n          //Mutating the inner document of the object element is fine though.\n          var objectElement = this;\n\n          //Create the style element to be added to the object.\n          getDocument(objectElement, function onObjectDocumentReady(objectDocument) {\n            //Notify that the element is ready to be listened to.\n            callback(element);\n          });\n        }\n\n        // The element may be detached from the DOM, and some browsers does not support style resolving of detached elements.\n        // The alterPositionStyles needs to be delayed until we know the element has been attached to the DOM (which we are sure of when the onObjectLoad has been fired), if style resolution is not possible.\n        if (style.position !== \"\") {\n          alterPositionStyles(style);\n          positionCheckPerformed = true;\n        }\n\n        //Add an object element as a child to the target element that will be listened to for resize events.\n        var object = document.createElement(\"object\");\n        object.style.cssText = OBJECT_STYLE;\n        object.tabIndex = -1;\n        object.type = \"text/html\";\n        object.setAttribute(\"aria-hidden\", \"true\");\n        object.onload = onObjectLoad;\n\n        //Safari: This must occur before adding the object to the DOM.\n        //IE: Does not like that this happens before, even if it is also added after.\n        if (!browserDetector.isIE()) {\n          object.data = \"about:blank\";\n        }\n        if (!getState(element)) {\n          // The element has been uninstalled before the actual loading happened.\n          return;\n        }\n        element.appendChild(object);\n        getState(element).object = object;\n\n        //IE: This must occur after adding the object to the DOM.\n        if (browserDetector.isIE()) {\n          object.data = \"about:blank\";\n        }\n      }\n      if (batchProcessor) {\n        batchProcessor.add(mutateDom);\n      } else {\n        mutateDom();\n      }\n    }\n    if (browserDetector.isIE(8)) {\n      //IE 8 does not support objects properly. Luckily they do support the resize event.\n      //So do not inject the object and notify that the element is already ready to be listened to.\n      //The event handler for the resize event is attached in the utils.addListener instead.\n      callback(element);\n    } else {\n      injectObject(element, callback);\n    }\n  }\n\n  /**\n   * Returns the child object of the target element.\n   * @private\n   * @param {element} element The target element.\n   * @returns The object element of the target.\n   */\n  function getObject(element) {\n    return getState(element).object;\n  }\n  function uninstall(element) {\n    if (!getState(element)) {\n      return;\n    }\n    var object = getObject(element);\n    if (!object) {\n      return;\n    }\n    if (browserDetector.isIE(8)) {\n      element.detachEvent(\"onresize\", object.proxy);\n    } else {\n      element.removeChild(object);\n    }\n    if (getState(element).checkForObjectDocumentTimeoutId) {\n      window.clearTimeout(getState(element).checkForObjectDocumentTimeoutId);\n    }\n    delete getState(element).object;\n  }\n  return {\n    makeDetectable: makeDetectable,\n    addListener: addListener,\n    uninstall: uninstall\n  };\n};", "map": {"version": 3, "names": ["browserDetector", "require", "module", "exports", "options", "reporter", "batchProcessor", "getState", "state<PERSON><PERSON><PERSON>", "Error", "addListener", "element", "listener", "listenerProxy", "isIE", "object", "proxy", "attachEvent", "getObject", "contentDocument", "defaultView", "addEventListener", "buildCssTextString", "rules", "seperator", "important", "join", "trim", "makeDetectable", "callback", "debug", "injectObject", "OBJECT_STYLE", "positionCheckPerformed", "style", "window", "getComputedStyle", "width", "offsetWidth", "height", "offsetHeight", "startSize", "mutateDom", "alterPositionStyles", "position", "setProperty", "removeRelativeStyles", "property", "getNumericalValue", "value", "replace", "warn", "onObjectLoad", "getDocument", "state", "checkForObjectDocumentTimeoutId", "clearTimeout", "setTimeout", "checkForObjectDocument", "objectElement", "onObjectDocumentReady", "objectDocument", "document", "createElement", "cssText", "tabIndex", "type", "setAttribute", "onload", "data", "append<PERSON><PERSON><PERSON>", "add", "uninstall", "detachEvent", "<PERSON><PERSON><PERSON><PERSON>"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/node_modules/.pnpm/element-resize-detector@1.2.4/node_modules/element-resize-detector/src/detection-strategy/object.js"], "sourcesContent": ["/**\n * Resize detection strategy that injects objects to elements in order to detect resize events.\n * Heavily inspired by: http://www.backalleycoder.com/2013/03/18/cross-browser-event-based-element-resize-detection/\n */\n\n\"use strict\";\n\nvar browserDetector = require(\"../browser-detector\");\n\nmodule.exports = function(options) {\n    options             = options || {};\n    var reporter        = options.reporter;\n    var batchProcessor  = options.batchProcessor;\n    var getState        = options.stateHandler.getState;\n\n    if(!reporter) {\n        throw new Error(\"Missing required dependency: reporter.\");\n    }\n\n    /**\n     * Adds a resize event listener to the element.\n     * @public\n     * @param {element} element The element that should have the listener added.\n     * @param {function} listener The listener callback to be called for each resize event of the element. The element will be given as a parameter to the listener callback.\n     */\n    function addListener(element, listener) {\n        function listenerProxy() {\n            listener(element);\n        }\n\n        if(browserDetector.isIE(8)) {\n            //IE 8 does not support object, but supports the resize event directly on elements.\n            getState(element).object = {\n                proxy: listenerProxy\n            };\n            element.attachEvent(\"onresize\", listenerProxy);\n        } else {\n            var object = getObject(element);\n\n            if(!object) {\n                throw new Error(\"Element is not detectable by this strategy.\");\n            }\n\n            object.contentDocument.defaultView.addEventListener(\"resize\", listenerProxy);\n        }\n    }\n\n    function buildCssTextString(rules) {\n        var seperator = options.important ? \" !important; \" : \"; \";\n\n        return (rules.join(seperator) + seperator).trim();\n    }\n\n    /**\n     * Makes an element detectable and ready to be listened for resize events. Will call the callback when the element is ready to be listened for resize changes.\n     * @private\n     * @param {object} options Optional options object.\n     * @param {element} element The element to make detectable\n     * @param {function} callback The callback to be called when the element is ready to be listened for resize changes. Will be called with the element as first parameter.\n     */\n    function makeDetectable(options, element, callback) {\n        if (!callback) {\n            callback = element;\n            element = options;\n            options = null;\n        }\n\n        options = options || {};\n        var debug = options.debug;\n\n        function injectObject(element, callback) {\n            var OBJECT_STYLE = buildCssTextString([\"display: block\", \"position: absolute\", \"top: 0\", \"left: 0\", \"width: 100%\", \"height: 100%\", \"border: none\", \"padding: 0\", \"margin: 0\", \"opacity: 0\", \"z-index: -1000\", \"pointer-events: none\"]);\n\n            //The target element needs to be positioned (everything except static) so the absolute positioned object will be positioned relative to the target element.\n\n            // Position altering may be performed directly or on object load, depending on if style resolution is possible directly or not.\n            var positionCheckPerformed = false;\n\n            // The element may not yet be attached to the DOM, and therefore the style object may be empty in some browsers.\n            // Since the style object is a reference, it will be updated as soon as the element is attached to the DOM.\n            var style = window.getComputedStyle(element);\n            var width = element.offsetWidth;\n            var height = element.offsetHeight;\n\n            getState(element).startSize = {\n                width: width,\n                height: height\n            };\n\n            function mutateDom() {\n                function alterPositionStyles() {\n                    if(style.position === \"static\") {\n                        element.style.setProperty(\"position\", \"relative\", options.important ? \"important\" : \"\");\n\n                        var removeRelativeStyles = function(reporter, element, style, property) {\n                            function getNumericalValue(value) {\n                                return value.replace(/[^-\\d\\.]/g, \"\");\n                            }\n\n                            var value = style[property];\n\n                            if(value !== \"auto\" && getNumericalValue(value) !== \"0\") {\n                                reporter.warn(\"An element that is positioned static has style.\" + property + \"=\" + value + \" which is ignored due to the static positioning. The element will need to be positioned relative, so the style.\" + property + \" will be set to 0. Element: \", element);\n                                element.style.setProperty(property, \"0\", options.important ? \"important\" : \"\");\n                            }\n                        };\n\n                        //Check so that there are no accidental styles that will make the element styled differently now that is is relative.\n                        //If there are any, set them to 0 (this should be okay with the user since the style properties did nothing before [since the element was positioned static] anyway).\n                        removeRelativeStyles(reporter, element, style, \"top\");\n                        removeRelativeStyles(reporter, element, style, \"right\");\n                        removeRelativeStyles(reporter, element, style, \"bottom\");\n                        removeRelativeStyles(reporter, element, style, \"left\");\n                    }\n                }\n\n                function onObjectLoad() {\n                    // The object has been loaded, which means that the element now is guaranteed to be attached to the DOM.\n                    if (!positionCheckPerformed) {\n                        alterPositionStyles();\n                    }\n\n                    /*jshint validthis: true */\n\n                    function getDocument(element, callback) {\n                        //Opera 12 seem to call the object.onload before the actual document has been created.\n                        //So if it is not present, poll it with an timeout until it is present.\n                        //TODO: Could maybe be handled better with object.onreadystatechange or similar.\n                        if(!element.contentDocument) {\n                            var state = getState(element);\n                            if (state.checkForObjectDocumentTimeoutId) {\n                                window.clearTimeout(state.checkForObjectDocumentTimeoutId);\n                            }\n                            state.checkForObjectDocumentTimeoutId = setTimeout(function checkForObjectDocument() {\n                                state.checkForObjectDocumentTimeoutId = 0;\n                                getDocument(element, callback);\n                            }, 100);\n\n                            return;\n                        }\n\n                        callback(element.contentDocument);\n                    }\n\n                    //Mutating the object element here seems to fire another load event.\n                    //Mutating the inner document of the object element is fine though.\n                    var objectElement = this;\n\n                    //Create the style element to be added to the object.\n                    getDocument(objectElement, function onObjectDocumentReady(objectDocument) {\n                        //Notify that the element is ready to be listened to.\n                        callback(element);\n                    });\n                }\n\n                // The element may be detached from the DOM, and some browsers does not support style resolving of detached elements.\n                // The alterPositionStyles needs to be delayed until we know the element has been attached to the DOM (which we are sure of when the onObjectLoad has been fired), if style resolution is not possible.\n                if (style.position !== \"\") {\n                    alterPositionStyles(style);\n                    positionCheckPerformed = true;\n                }\n\n                //Add an object element as a child to the target element that will be listened to for resize events.\n                var object = document.createElement(\"object\");\n                object.style.cssText = OBJECT_STYLE;\n                object.tabIndex = -1;\n                object.type = \"text/html\";\n                object.setAttribute(\"aria-hidden\", \"true\");\n                object.onload = onObjectLoad;\n\n                //Safari: This must occur before adding the object to the DOM.\n                //IE: Does not like that this happens before, even if it is also added after.\n                if(!browserDetector.isIE()) {\n                    object.data = \"about:blank\";\n                }\n\n                if (!getState(element)) {\n                    // The element has been uninstalled before the actual loading happened.\n                    return;\n                }\n\n                element.appendChild(object);\n                getState(element).object = object;\n\n                //IE: This must occur after adding the object to the DOM.\n                if(browserDetector.isIE()) {\n                    object.data = \"about:blank\";\n                }\n            }\n\n            if(batchProcessor) {\n                batchProcessor.add(mutateDom);\n            } else {\n                mutateDom();\n            }\n        }\n\n        if(browserDetector.isIE(8)) {\n            //IE 8 does not support objects properly. Luckily they do support the resize event.\n            //So do not inject the object and notify that the element is already ready to be listened to.\n            //The event handler for the resize event is attached in the utils.addListener instead.\n            callback(element);\n        } else {\n            injectObject(element, callback);\n        }\n    }\n\n    /**\n     * Returns the child object of the target element.\n     * @private\n     * @param {element} element The target element.\n     * @returns The object element of the target.\n     */\n    function getObject(element) {\n        return getState(element).object;\n    }\n\n    function uninstall(element) {\n        if (!getState(element)) {\n            return;\n        }\n\n        var object = getObject(element);\n\n        if (!object) {\n            return;\n        }\n\n        if (browserDetector.isIE(8)) {\n            element.detachEvent(\"onresize\", object.proxy);\n        } else {\n            element.removeChild(object);\n        }\n\n        if (getState(element).checkForObjectDocumentTimeoutId) {\n            window.clearTimeout(getState(element).checkForObjectDocumentTimeoutId);\n        }\n\n        delete getState(element).object;\n    }\n\n    return {\n        makeDetectable: makeDetectable,\n        addListener: addListener,\n        uninstall: uninstall\n    };\n};\n"], "mappings": "AAAA;AACA;AACA;AACA;;AAEA,YAAY;;AAEZ,IAAIA,eAAe,GAAGC,OAAO,CAAC,qBAAqB,CAAC;AAEpDC,MAAM,CAACC,OAAO,GAAG,UAASC,OAAO,EAAE;EAC/BA,OAAO,GAAeA,OAAO,IAAI,CAAC,CAAC;EACnC,IAAIC,QAAQ,GAAUD,OAAO,CAACC,QAAQ;EACtC,IAAIC,cAAc,GAAIF,OAAO,CAACE,cAAc;EAC5C,IAAIC,QAAQ,GAAUH,OAAO,CAACI,YAAY,CAACD,QAAQ;EAEnD,IAAG,CAACF,QAAQ,EAAE;IACV,MAAM,IAAII,KAAK,CAAC,wCAAwC,CAAC;EAC7D;;EAEA;AACJ;AACA;AACA;AACA;AACA;EACI,SAASC,WAAWA,CAACC,OAAO,EAAEC,QAAQ,EAAE;IACpC,SAASC,aAAaA,CAAA,EAAG;MACrBD,QAAQ,CAACD,OAAO,CAAC;IACrB;IAEA,IAAGX,eAAe,CAACc,IAAI,CAAC,CAAC,CAAC,EAAE;MACxB;MACAP,QAAQ,CAACI,OAAO,CAAC,CAACI,MAAM,GAAG;QACvBC,KAAK,EAAEH;MACX,CAAC;MACDF,OAAO,CAACM,WAAW,CAAC,UAAU,EAAEJ,aAAa,CAAC;IAClD,CAAC,MAAM;MACH,IAAIE,MAAM,GAAGG,SAAS,CAACP,OAAO,CAAC;MAE/B,IAAG,CAACI,MAAM,EAAE;QACR,MAAM,IAAIN,KAAK,CAAC,6CAA6C,CAAC;MAClE;MAEAM,MAAM,CAACI,eAAe,CAACC,WAAW,CAACC,gBAAgB,CAAC,QAAQ,EAAER,aAAa,CAAC;IAChF;EACJ;EAEA,SAASS,kBAAkBA,CAACC,KAAK,EAAE;IAC/B,IAAIC,SAAS,GAAGpB,OAAO,CAACqB,SAAS,GAAG,eAAe,GAAG,IAAI;IAE1D,OAAO,CAACF,KAAK,CAACG,IAAI,CAACF,SAAS,CAAC,GAAGA,SAAS,EAAEG,IAAI,CAAC,CAAC;EACrD;;EAEA;AACJ;AACA;AACA;AACA;AACA;AACA;EACI,SAASC,cAAcA,CAACxB,OAAO,EAAEO,OAAO,EAAEkB,QAAQ,EAAE;IAChD,IAAI,CAACA,QAAQ,EAAE;MACXA,QAAQ,GAAGlB,OAAO;MAClBA,OAAO,GAAGP,OAAO;MACjBA,OAAO,GAAG,IAAI;IAClB;IAEAA,OAAO,GAAGA,OAAO,IAAI,CAAC,CAAC;IACvB,IAAI0B,KAAK,GAAG1B,OAAO,CAAC0B,KAAK;IAEzB,SAASC,YAAYA,CAACpB,OAAO,EAAEkB,QAAQ,EAAE;MACrC,IAAIG,YAAY,GAAGV,kBAAkB,CAAC,CAAC,gBAAgB,EAAE,oBAAoB,EAAE,QAAQ,EAAE,SAAS,EAAE,aAAa,EAAE,cAAc,EAAE,cAAc,EAAE,YAAY,EAAE,WAAW,EAAE,YAAY,EAAE,gBAAgB,EAAE,sBAAsB,CAAC,CAAC;;MAEtO;;MAEA;MACA,IAAIW,sBAAsB,GAAG,KAAK;;MAElC;MACA;MACA,IAAIC,KAAK,GAAGC,MAAM,CAACC,gBAAgB,CAACzB,OAAO,CAAC;MAC5C,IAAI0B,KAAK,GAAG1B,OAAO,CAAC2B,WAAW;MAC/B,IAAIC,MAAM,GAAG5B,OAAO,CAAC6B,YAAY;MAEjCjC,QAAQ,CAACI,OAAO,CAAC,CAAC8B,SAAS,GAAG;QAC1BJ,KAAK,EAAEA,KAAK;QACZE,MAAM,EAAEA;MACZ,CAAC;MAED,SAASG,SAASA,CAAA,EAAG;QACjB,SAASC,mBAAmBA,CAAA,EAAG;UAC3B,IAAGT,KAAK,CAACU,QAAQ,KAAK,QAAQ,EAAE;YAC5BjC,OAAO,CAACuB,KAAK,CAACW,WAAW,CAAC,UAAU,EAAE,UAAU,EAAEzC,OAAO,CAACqB,SAAS,GAAG,WAAW,GAAG,EAAE,CAAC;YAEvF,IAAIqB,oBAAoB,GAAG,SAAvBA,oBAAoBA,CAAYzC,QAAQ,EAAEM,OAAO,EAAEuB,KAAK,EAAEa,QAAQ,EAAE;cACpE,SAASC,iBAAiBA,CAACC,KAAK,EAAE;gBAC9B,OAAOA,KAAK,CAACC,OAAO,CAAC,WAAW,EAAE,EAAE,CAAC;cACzC;cAEA,IAAID,KAAK,GAAGf,KAAK,CAACa,QAAQ,CAAC;cAE3B,IAAGE,KAAK,KAAK,MAAM,IAAID,iBAAiB,CAACC,KAAK,CAAC,KAAK,GAAG,EAAE;gBACrD5C,QAAQ,CAAC8C,IAAI,CAAC,iDAAiD,GAAGJ,QAAQ,GAAG,GAAG,GAAGE,KAAK,GAAG,iHAAiH,GAAGF,QAAQ,GAAG,8BAA8B,EAAEpC,OAAO,CAAC;gBAClQA,OAAO,CAACuB,KAAK,CAACW,WAAW,CAACE,QAAQ,EAAE,GAAG,EAAE3C,OAAO,CAACqB,SAAS,GAAG,WAAW,GAAG,EAAE,CAAC;cAClF;YACJ,CAAC;;YAED;YACA;YACAqB,oBAAoB,CAACzC,QAAQ,EAAEM,OAAO,EAAEuB,KAAK,EAAE,KAAK,CAAC;YACrDY,oBAAoB,CAACzC,QAAQ,EAAEM,OAAO,EAAEuB,KAAK,EAAE,OAAO,CAAC;YACvDY,oBAAoB,CAACzC,QAAQ,EAAEM,OAAO,EAAEuB,KAAK,EAAE,QAAQ,CAAC;YACxDY,oBAAoB,CAACzC,QAAQ,EAAEM,OAAO,EAAEuB,KAAK,EAAE,MAAM,CAAC;UAC1D;QACJ;QAEA,SAASkB,YAAYA,CAAA,EAAG;UACpB;UACA,IAAI,CAACnB,sBAAsB,EAAE;YACzBU,mBAAmB,CAAC,CAAC;UACzB;;UAEA;;UAEA,SAASU,WAAWA,CAAC1C,OAAO,EAAEkB,QAAQ,EAAE;YACpC;YACA;YACA;YACA,IAAG,CAAClB,OAAO,CAACQ,eAAe,EAAE;cACzB,IAAImC,KAAK,GAAG/C,QAAQ,CAACI,OAAO,CAAC;cAC7B,IAAI2C,KAAK,CAACC,+BAA+B,EAAE;gBACvCpB,MAAM,CAACqB,YAAY,CAACF,KAAK,CAACC,+BAA+B,CAAC;cAC9D;cACAD,KAAK,CAACC,+BAA+B,GAAGE,UAAU,CAAC,SAASC,sBAAsBA,CAAA,EAAG;gBACjFJ,KAAK,CAACC,+BAA+B,GAAG,CAAC;gBACzCF,WAAW,CAAC1C,OAAO,EAAEkB,QAAQ,CAAC;cAClC,CAAC,EAAE,GAAG,CAAC;cAEP;YACJ;YAEAA,QAAQ,CAAClB,OAAO,CAACQ,eAAe,CAAC;UACrC;;UAEA;UACA;UACA,IAAIwC,aAAa,GAAG,IAAI;;UAExB;UACAN,WAAW,CAACM,aAAa,EAAE,SAASC,qBAAqBA,CAACC,cAAc,EAAE;YACtE;YACAhC,QAAQ,CAAClB,OAAO,CAAC;UACrB,CAAC,CAAC;QACN;;QAEA;QACA;QACA,IAAIuB,KAAK,CAACU,QAAQ,KAAK,EAAE,EAAE;UACvBD,mBAAmB,CAACT,KAAK,CAAC;UAC1BD,sBAAsB,GAAG,IAAI;QACjC;;QAEA;QACA,IAAIlB,MAAM,GAAG+C,QAAQ,CAACC,aAAa,CAAC,QAAQ,CAAC;QAC7ChD,MAAM,CAACmB,KAAK,CAAC8B,OAAO,GAAGhC,YAAY;QACnCjB,MAAM,CAACkD,QAAQ,GAAG,CAAC,CAAC;QACpBlD,MAAM,CAACmD,IAAI,GAAG,WAAW;QACzBnD,MAAM,CAACoD,YAAY,CAAC,aAAa,EAAE,MAAM,CAAC;QAC1CpD,MAAM,CAACqD,MAAM,GAAGhB,YAAY;;QAE5B;QACA;QACA,IAAG,CAACpD,eAAe,CAACc,IAAI,CAAC,CAAC,EAAE;UACxBC,MAAM,CAACsD,IAAI,GAAG,aAAa;QAC/B;QAEA,IAAI,CAAC9D,QAAQ,CAACI,OAAO,CAAC,EAAE;UACpB;UACA;QACJ;QAEAA,OAAO,CAAC2D,WAAW,CAACvD,MAAM,CAAC;QAC3BR,QAAQ,CAACI,OAAO,CAAC,CAACI,MAAM,GAAGA,MAAM;;QAEjC;QACA,IAAGf,eAAe,CAACc,IAAI,CAAC,CAAC,EAAE;UACvBC,MAAM,CAACsD,IAAI,GAAG,aAAa;QAC/B;MACJ;MAEA,IAAG/D,cAAc,EAAE;QACfA,cAAc,CAACiE,GAAG,CAAC7B,SAAS,CAAC;MACjC,CAAC,MAAM;QACHA,SAAS,CAAC,CAAC;MACf;IACJ;IAEA,IAAG1C,eAAe,CAACc,IAAI,CAAC,CAAC,CAAC,EAAE;MACxB;MACA;MACA;MACAe,QAAQ,CAAClB,OAAO,CAAC;IACrB,CAAC,MAAM;MACHoB,YAAY,CAACpB,OAAO,EAAEkB,QAAQ,CAAC;IACnC;EACJ;;EAEA;AACJ;AACA;AACA;AACA;AACA;EACI,SAASX,SAASA,CAACP,OAAO,EAAE;IACxB,OAAOJ,QAAQ,CAACI,OAAO,CAAC,CAACI,MAAM;EACnC;EAEA,SAASyD,SAASA,CAAC7D,OAAO,EAAE;IACxB,IAAI,CAACJ,QAAQ,CAACI,OAAO,CAAC,EAAE;MACpB;IACJ;IAEA,IAAII,MAAM,GAAGG,SAAS,CAACP,OAAO,CAAC;IAE/B,IAAI,CAACI,MAAM,EAAE;MACT;IACJ;IAEA,IAAIf,eAAe,CAACc,IAAI,CAAC,CAAC,CAAC,EAAE;MACzBH,OAAO,CAAC8D,WAAW,CAAC,UAAU,EAAE1D,MAAM,CAACC,KAAK,CAAC;IACjD,CAAC,MAAM;MACHL,OAAO,CAAC+D,WAAW,CAAC3D,MAAM,CAAC;IAC/B;IAEA,IAAIR,QAAQ,CAACI,OAAO,CAAC,CAAC4C,+BAA+B,EAAE;MACnDpB,MAAM,CAACqB,YAAY,CAACjD,QAAQ,CAACI,OAAO,CAAC,CAAC4C,+BAA+B,CAAC;IAC1E;IAEA,OAAOhD,QAAQ,CAACI,OAAO,CAAC,CAACI,MAAM;EACnC;EAEA,OAAO;IACHa,cAAc,EAAEA,cAAc;IAC9BlB,WAAW,EAAEA,WAAW;IACxB8D,SAAS,EAAEA;EACf,CAAC;AACL,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}
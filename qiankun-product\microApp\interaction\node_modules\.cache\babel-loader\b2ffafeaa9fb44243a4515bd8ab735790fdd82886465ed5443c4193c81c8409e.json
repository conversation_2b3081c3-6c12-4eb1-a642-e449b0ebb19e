{"ast": null, "code": ";\n(function (root, factory) {\n  if (typeof exports === \"object\") {\n    // CommonJS\n    module.exports = exports = factory(require(\"./core\"));\n  } else if (typeof define === \"function\" && define.amd) {\n    // AMD\n    define([\"./core\"], factory);\n  } else {\n    // Global (browser)\n    factory(root.CryptoJS);\n  }\n})(this, function (CryptoJS) {\n  (function () {\n    // Shortcuts\n    var C = CryptoJS;\n    var C_lib = C.lib;\n    var WordArray = C_lib.WordArray;\n    var Hasher = C_lib.Hasher;\n    var C_algo = C.algo;\n\n    // Reusable object\n    var W = [];\n\n    /**\n     * SHA-1 hash algorithm.\n     */\n    var SHA1 = C_algo.SHA1 = Hasher.extend({\n      _doReset: function _doReset() {\n        this._hash = new WordArray.init([0x67452301, 0xefcdab89, 0x98badcfe, 0x10325476, 0xc3d2e1f0]);\n      },\n      _doProcessBlock: function _doProcessBlock(M, offset) {\n        // Shortcut\n        var H = this._hash.words;\n\n        // Working variables\n        var a = H[0];\n        var b = H[1];\n        var c = H[2];\n        var d = H[3];\n        var e = H[4];\n\n        // Computation\n        for (var i = 0; i < 80; i++) {\n          if (i < 16) {\n            W[i] = M[offset + i] | 0;\n          } else {\n            var n = W[i - 3] ^ W[i - 8] ^ W[i - 14] ^ W[i - 16];\n            W[i] = n << 1 | n >>> 31;\n          }\n          var t = (a << 5 | a >>> 27) + e + W[i];\n          if (i < 20) {\n            t += (b & c | ~b & d) + 0x5a827999;\n          } else if (i < 40) {\n            t += (b ^ c ^ d) + 0x6ed9eba1;\n          } else if (i < 60) {\n            t += (b & c | b & d | c & d) - 0x70e44324;\n          } else /* if (i < 80) */{\n              t += (b ^ c ^ d) - 0x359d3e2a;\n            }\n          e = d;\n          d = c;\n          c = b << 30 | b >>> 2;\n          b = a;\n          a = t;\n        }\n\n        // Intermediate hash value\n        H[0] = H[0] + a | 0;\n        H[1] = H[1] + b | 0;\n        H[2] = H[2] + c | 0;\n        H[3] = H[3] + d | 0;\n        H[4] = H[4] + e | 0;\n      },\n      _doFinalize: function _doFinalize() {\n        // Shortcuts\n        var data = this._data;\n        var dataWords = data.words;\n        var nBitsTotal = this._nDataBytes * 8;\n        var nBitsLeft = data.sigBytes * 8;\n\n        // Add padding\n        dataWords[nBitsLeft >>> 5] |= 0x80 << 24 - nBitsLeft % 32;\n        dataWords[(nBitsLeft + 64 >>> 9 << 4) + 14] = Math.floor(nBitsTotal / 0x100000000);\n        dataWords[(nBitsLeft + 64 >>> 9 << 4) + 15] = nBitsTotal;\n        data.sigBytes = dataWords.length * 4;\n\n        // Hash final blocks\n        this._process();\n\n        // Return final computed hash\n        return this._hash;\n      },\n      clone: function clone() {\n        var clone = Hasher.clone.call(this);\n        clone._hash = this._hash.clone();\n        return clone;\n      }\n    });\n\n    /**\n     * Shortcut function to the hasher's object interface.\n     *\n     * @param {WordArray|string} message The message to hash.\n     *\n     * @return {WordArray} The hash.\n     *\n     * @static\n     *\n     * @example\n     *\n     *     var hash = CryptoJS.SHA1('message');\n     *     var hash = CryptoJS.SHA1(wordArray);\n     */\n    C.SHA1 = Hasher._createHelper(SHA1);\n\n    /**\n     * Shortcut function to the HMAC's object interface.\n     *\n     * @param {WordArray|string} message The message to hash.\n     * @param {WordArray|string} key The secret key.\n     *\n     * @return {WordArray} The HMAC.\n     *\n     * @static\n     *\n     * @example\n     *\n     *     var hmac = CryptoJS.HmacSHA1(message, key);\n     */\n    C.HmacSHA1 = Hasher._createHmacHelper(SHA1);\n  })();\n  return CryptoJS.SHA1;\n});", "map": {"version": 3, "names": ["root", "factory", "exports", "module", "require", "define", "amd", "CryptoJS", "C", "C_lib", "lib", "WordArray", "<PERSON><PERSON>", "C_algo", "algo", "W", "SHA1", "extend", "_doReset", "_hash", "init", "_doProcessBlock", "M", "offset", "H", "words", "a", "b", "c", "d", "e", "i", "n", "t", "_doFinalize", "data", "_data", "dataWords", "nBitsTotal", "_nDataBytes", "nBitsLeft", "sigBytes", "Math", "floor", "length", "_process", "clone", "call", "_createHelper", "HmacSHA1", "_createHmacHelper"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/node_modules/.pnpm/crypto-js@4.2.0/node_modules/crypto-js/sha1.js"], "sourcesContent": [";(function (root, factory) {\n\tif (typeof exports === \"object\") {\n\t\t// CommonJS\n\t\tmodule.exports = exports = factory(require(\"./core\"));\n\t}\n\telse if (typeof define === \"function\" && define.amd) {\n\t\t// AMD\n\t\tdefine([\"./core\"], factory);\n\t}\n\telse {\n\t\t// Global (browser)\n\t\tfactory(root.CryptoJS);\n\t}\n}(this, function (CryptoJS) {\n\n\t(function () {\n\t    // Shortcuts\n\t    var C = CryptoJS;\n\t    var C_lib = C.lib;\n\t    var WordArray = C_lib.WordArray;\n\t    var Hasher = C_lib.Hasher;\n\t    var C_algo = C.algo;\n\n\t    // Reusable object\n\t    var W = [];\n\n\t    /**\n\t     * SHA-1 hash algorithm.\n\t     */\n\t    var SHA1 = C_algo.SHA1 = Hasher.extend({\n\t        _doReset: function () {\n\t            this._hash = new WordArray.init([\n\t                0x67452301, 0xefcdab89,\n\t                0x98badcfe, 0x10325476,\n\t                0xc3d2e1f0\n\t            ]);\n\t        },\n\n\t        _doProcessBlock: function (M, offset) {\n\t            // Shortcut\n\t            var H = this._hash.words;\n\n\t            // Working variables\n\t            var a = H[0];\n\t            var b = H[1];\n\t            var c = H[2];\n\t            var d = H[3];\n\t            var e = H[4];\n\n\t            // Computation\n\t            for (var i = 0; i < 80; i++) {\n\t                if (i < 16) {\n\t                    W[i] = M[offset + i] | 0;\n\t                } else {\n\t                    var n = W[i - 3] ^ W[i - 8] ^ W[i - 14] ^ W[i - 16];\n\t                    W[i] = (n << 1) | (n >>> 31);\n\t                }\n\n\t                var t = ((a << 5) | (a >>> 27)) + e + W[i];\n\t                if (i < 20) {\n\t                    t += ((b & c) | (~b & d)) + 0x5a827999;\n\t                } else if (i < 40) {\n\t                    t += (b ^ c ^ d) + 0x6ed9eba1;\n\t                } else if (i < 60) {\n\t                    t += ((b & c) | (b & d) | (c & d)) - 0x70e44324;\n\t                } else /* if (i < 80) */ {\n\t                    t += (b ^ c ^ d) - 0x359d3e2a;\n\t                }\n\n\t                e = d;\n\t                d = c;\n\t                c = (b << 30) | (b >>> 2);\n\t                b = a;\n\t                a = t;\n\t            }\n\n\t            // Intermediate hash value\n\t            H[0] = (H[0] + a) | 0;\n\t            H[1] = (H[1] + b) | 0;\n\t            H[2] = (H[2] + c) | 0;\n\t            H[3] = (H[3] + d) | 0;\n\t            H[4] = (H[4] + e) | 0;\n\t        },\n\n\t        _doFinalize: function () {\n\t            // Shortcuts\n\t            var data = this._data;\n\t            var dataWords = data.words;\n\n\t            var nBitsTotal = this._nDataBytes * 8;\n\t            var nBitsLeft = data.sigBytes * 8;\n\n\t            // Add padding\n\t            dataWords[nBitsLeft >>> 5] |= 0x80 << (24 - nBitsLeft % 32);\n\t            dataWords[(((nBitsLeft + 64) >>> 9) << 4) + 14] = Math.floor(nBitsTotal / 0x100000000);\n\t            dataWords[(((nBitsLeft + 64) >>> 9) << 4) + 15] = nBitsTotal;\n\t            data.sigBytes = dataWords.length * 4;\n\n\t            // Hash final blocks\n\t            this._process();\n\n\t            // Return final computed hash\n\t            return this._hash;\n\t        },\n\n\t        clone: function () {\n\t            var clone = Hasher.clone.call(this);\n\t            clone._hash = this._hash.clone();\n\n\t            return clone;\n\t        }\n\t    });\n\n\t    /**\n\t     * Shortcut function to the hasher's object interface.\n\t     *\n\t     * @param {WordArray|string} message The message to hash.\n\t     *\n\t     * @return {WordArray} The hash.\n\t     *\n\t     * @static\n\t     *\n\t     * @example\n\t     *\n\t     *     var hash = CryptoJS.SHA1('message');\n\t     *     var hash = CryptoJS.SHA1(wordArray);\n\t     */\n\t    C.SHA1 = Hasher._createHelper(SHA1);\n\n\t    /**\n\t     * Shortcut function to the HMAC's object interface.\n\t     *\n\t     * @param {WordArray|string} message The message to hash.\n\t     * @param {WordArray|string} key The secret key.\n\t     *\n\t     * @return {WordArray} The HMAC.\n\t     *\n\t     * @static\n\t     *\n\t     * @example\n\t     *\n\t     *     var hmac = CryptoJS.HmacSHA1(message, key);\n\t     */\n\t    C.HmacSHA1 = Hasher._createHmacHelper(SHA1);\n\t}());\n\n\n\treturn CryptoJS.SHA1;\n\n}));"], "mappings": "AAAA;AAAE,WAAUA,IAAI,EAAEC,OAAO,EAAE;EAC1B,IAAI,OAAOC,OAAO,KAAK,QAAQ,EAAE;IAChC;IACAC,MAAM,CAACD,OAAO,GAAGA,OAAO,GAAGD,OAAO,CAACG,OAAO,CAAC,QAAQ,CAAC,CAAC;EACtD,CAAC,MACI,IAAI,OAAOC,MAAM,KAAK,UAAU,IAAIA,MAAM,CAACC,GAAG,EAAE;IACpD;IACAD,MAAM,CAAC,CAAC,QAAQ,CAAC,EAAEJ,OAAO,CAAC;EAC5B,CAAC,MACI;IACJ;IACAA,OAAO,CAACD,IAAI,CAACO,QAAQ,CAAC;EACvB;AACD,CAAC,EAAC,IAAI,EAAE,UAAUA,QAAQ,EAAE;EAE1B,aAAY;IACT;IACA,IAAIC,CAAC,GAAGD,QAAQ;IAChB,IAAIE,KAAK,GAAGD,CAAC,CAACE,GAAG;IACjB,IAAIC,SAAS,GAAGF,KAAK,CAACE,SAAS;IAC/B,IAAIC,MAAM,GAAGH,KAAK,CAACG,MAAM;IACzB,IAAIC,MAAM,GAAGL,CAAC,CAACM,IAAI;;IAEnB;IACA,IAAIC,CAAC,GAAG,EAAE;;IAEV;AACL;AACA;IACK,IAAIC,IAAI,GAAGH,MAAM,CAACG,IAAI,GAAGJ,MAAM,CAACK,MAAM,CAAC;MACnCC,QAAQ,EAAE,SAAVA,QAAQA,CAAA,EAAc;QAClB,IAAI,CAACC,KAAK,GAAG,IAAIR,SAAS,CAACS,IAAI,CAAC,CAC5B,UAAU,EAAE,UAAU,EACtB,UAAU,EAAE,UAAU,EACtB,UAAU,CACb,CAAC;MACN,CAAC;MAEDC,eAAe,EAAE,SAAjBA,eAAeA,CAAYC,CAAC,EAAEC,MAAM,EAAE;QAClC;QACA,IAAIC,CAAC,GAAG,IAAI,CAACL,KAAK,CAACM,KAAK;;QAExB;QACA,IAAIC,CAAC,GAAGF,CAAC,CAAC,CAAC,CAAC;QACZ,IAAIG,CAAC,GAAGH,CAAC,CAAC,CAAC,CAAC;QACZ,IAAII,CAAC,GAAGJ,CAAC,CAAC,CAAC,CAAC;QACZ,IAAIK,CAAC,GAAGL,CAAC,CAAC,CAAC,CAAC;QACZ,IAAIM,CAAC,GAAGN,CAAC,CAAC,CAAC,CAAC;;QAEZ;QACA,KAAK,IAAIO,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,EAAE,EAAEA,CAAC,EAAE,EAAE;UACzB,IAAIA,CAAC,GAAG,EAAE,EAAE;YACRhB,CAAC,CAACgB,CAAC,CAAC,GAAGT,CAAC,CAACC,MAAM,GAAGQ,CAAC,CAAC,GAAG,CAAC;UAC5B,CAAC,MAAM;YACH,IAAIC,CAAC,GAAGjB,CAAC,CAACgB,CAAC,GAAG,CAAC,CAAC,GAAGhB,CAAC,CAACgB,CAAC,GAAG,CAAC,CAAC,GAAGhB,CAAC,CAACgB,CAAC,GAAG,EAAE,CAAC,GAAGhB,CAAC,CAACgB,CAAC,GAAG,EAAE,CAAC;YACnDhB,CAAC,CAACgB,CAAC,CAAC,GAAIC,CAAC,IAAI,CAAC,GAAKA,CAAC,KAAK,EAAG;UAChC;UAEA,IAAIC,CAAC,GAAG,CAAEP,CAAC,IAAI,CAAC,GAAKA,CAAC,KAAK,EAAG,IAAII,CAAC,GAAGf,CAAC,CAACgB,CAAC,CAAC;UAC1C,IAAIA,CAAC,GAAG,EAAE,EAAE;YACRE,CAAC,IAAI,CAAEN,CAAC,GAAGC,CAAC,GAAK,CAACD,CAAC,GAAGE,CAAE,IAAI,UAAU;UAC1C,CAAC,MAAM,IAAIE,CAAC,GAAG,EAAE,EAAE;YACfE,CAAC,IAAI,CAACN,CAAC,GAAGC,CAAC,GAAGC,CAAC,IAAI,UAAU;UACjC,CAAC,MAAM,IAAIE,CAAC,GAAG,EAAE,EAAE;YACfE,CAAC,IAAI,CAAEN,CAAC,GAAGC,CAAC,GAAKD,CAAC,GAAGE,CAAE,GAAID,CAAC,GAAGC,CAAE,IAAI,UAAU;UACnD,CAAC,MAAM,iBAAkB;cACrBI,CAAC,IAAI,CAACN,CAAC,GAAGC,CAAC,GAAGC,CAAC,IAAI,UAAU;YACjC;UAEAC,CAAC,GAAGD,CAAC;UACLA,CAAC,GAAGD,CAAC;UACLA,CAAC,GAAID,CAAC,IAAI,EAAE,GAAKA,CAAC,KAAK,CAAE;UACzBA,CAAC,GAAGD,CAAC;UACLA,CAAC,GAAGO,CAAC;QACT;;QAEA;QACAT,CAAC,CAAC,CAAC,CAAC,GAAIA,CAAC,CAAC,CAAC,CAAC,GAAGE,CAAC,GAAI,CAAC;QACrBF,CAAC,CAAC,CAAC,CAAC,GAAIA,CAAC,CAAC,CAAC,CAAC,GAAGG,CAAC,GAAI,CAAC;QACrBH,CAAC,CAAC,CAAC,CAAC,GAAIA,CAAC,CAAC,CAAC,CAAC,GAAGI,CAAC,GAAI,CAAC;QACrBJ,CAAC,CAAC,CAAC,CAAC,GAAIA,CAAC,CAAC,CAAC,CAAC,GAAGK,CAAC,GAAI,CAAC;QACrBL,CAAC,CAAC,CAAC,CAAC,GAAIA,CAAC,CAAC,CAAC,CAAC,GAAGM,CAAC,GAAI,CAAC;MACzB,CAAC;MAEDI,WAAW,EAAE,SAAbA,WAAWA,CAAA,EAAc;QACrB;QACA,IAAIC,IAAI,GAAG,IAAI,CAACC,KAAK;QACrB,IAAIC,SAAS,GAAGF,IAAI,CAACV,KAAK;QAE1B,IAAIa,UAAU,GAAG,IAAI,CAACC,WAAW,GAAG,CAAC;QACrC,IAAIC,SAAS,GAAGL,IAAI,CAACM,QAAQ,GAAG,CAAC;;QAEjC;QACAJ,SAAS,CAACG,SAAS,KAAK,CAAC,CAAC,IAAI,IAAI,IAAK,EAAE,GAAGA,SAAS,GAAG,EAAG;QAC3DH,SAAS,CAAC,CAAGG,SAAS,GAAG,EAAE,KAAM,CAAC,IAAK,CAAC,IAAI,EAAE,CAAC,GAAGE,IAAI,CAACC,KAAK,CAACL,UAAU,GAAG,WAAW,CAAC;QACtFD,SAAS,CAAC,CAAGG,SAAS,GAAG,EAAE,KAAM,CAAC,IAAK,CAAC,IAAI,EAAE,CAAC,GAAGF,UAAU;QAC5DH,IAAI,CAACM,QAAQ,GAAGJ,SAAS,CAACO,MAAM,GAAG,CAAC;;QAEpC;QACA,IAAI,CAACC,QAAQ,CAAC,CAAC;;QAEf;QACA,OAAO,IAAI,CAAC1B,KAAK;MACrB,CAAC;MAED2B,KAAK,EAAE,SAAPA,KAAKA,CAAA,EAAc;QACf,IAAIA,KAAK,GAAGlC,MAAM,CAACkC,KAAK,CAACC,IAAI,CAAC,IAAI,CAAC;QACnCD,KAAK,CAAC3B,KAAK,GAAG,IAAI,CAACA,KAAK,CAAC2B,KAAK,CAAC,CAAC;QAEhC,OAAOA,KAAK;MAChB;IACJ,CAAC,CAAC;;IAEF;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACKtC,CAAC,CAACQ,IAAI,GAAGJ,MAAM,CAACoC,aAAa,CAAChC,IAAI,CAAC;;IAEnC;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACKR,CAAC,CAACyC,QAAQ,GAAGrC,MAAM,CAACsC,iBAAiB,CAAClC,IAAI,CAAC;EAC/C,CAAC,EAAC,CAAC;EAGH,OAAOT,QAAQ,CAACS,IAAI;AAErB,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}
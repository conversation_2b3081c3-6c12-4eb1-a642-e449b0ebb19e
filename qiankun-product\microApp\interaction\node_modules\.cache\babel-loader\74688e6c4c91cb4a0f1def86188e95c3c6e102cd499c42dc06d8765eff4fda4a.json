{"ast": null, "code": "import { resolveComponent as _resolveComponent, createVNode as _createVNode, withCtx as _withCtx, toDisplayString as _toDisplayString, createTextVNode as _createTextVNode, createElementVNode as _createElementVNode, openBlock as _openBlock, createBlock as _createBlock } from \"vue\";\nvar _hoisted_1 = {\n  class: \"verify-form\"\n};\nvar _hoisted_2 = {\n  class: \"code-input-group\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  var _component_el_input = _resolveComponent(\"el-input\");\n  var _component_el_form_item = _resolveComponent(\"el-form-item\");\n  var _component_el_button = _resolveComponent(\"el-button\");\n  var _component_el_form = _resolveComponent(\"el-form\");\n  var _component_el_dialog = _resolveComponent(\"el-dialog\");\n  return _openBlock(), _createBlock(_component_el_dialog, {\n    modelValue: $setup.visible,\n    \"onUpdate:modelValue\": _cache[3] || (_cache[3] = function ($event) {\n      return $setup.visible = $event;\n    }),\n    title: \"手机号验证\",\n    width: \"400px\",\n    \"before-close\": $setup.handleClose,\n    \"close-on-click-modal\": false,\n    class: \"phone-verify-dialog\"\n  }, {\n    default: _withCtx(function () {\n      return [_createElementVNode(\"div\", _hoisted_1, [_createVNode(_component_el_form, {\n        ref: \"formRef\",\n        model: $setup.form,\n        rules: $setup.rules,\n        \"label-position\": \"top\"\n      }, {\n        default: _withCtx(function () {\n          return [_createVNode(_component_el_form_item, {\n            prop: \"name\"\n          }, {\n            default: _withCtx(function () {\n              return [_createVNode(_component_el_input, {\n                modelValue: $setup.form.name,\n                \"onUpdate:modelValue\": _cache[0] || (_cache[0] = function ($event) {\n                  return $setup.form.name = $event;\n                }),\n                placeholder: \"请输入姓名\",\n                clearable: \"\",\n                class: \"form-input\"\n              }, null, 8 /* PROPS */, [\"modelValue\"])];\n            }),\n            _: 1 /* STABLE */\n          }), _createVNode(_component_el_form_item, {\n            prop: \"phone\"\n          }, {\n            default: _withCtx(function () {\n              return [_createVNode(_component_el_input, {\n                modelValue: $setup.form.phone,\n                \"onUpdate:modelValue\": _cache[1] || (_cache[1] = function ($event) {\n                  return $setup.form.phone = $event;\n                }),\n                placeholder: \"请输入手机号码\",\n                clearable: \"\",\n                maxlength: \"11\",\n                class: \"form-input\"\n              }, null, 8 /* PROPS */, [\"modelValue\"])];\n            }),\n            _: 1 /* STABLE */\n          }), _createVNode(_component_el_form_item, {\n            prop: \"code\"\n          }, {\n            default: _withCtx(function () {\n              return [_createElementVNode(\"div\", _hoisted_2, [_createVNode(_component_el_input, {\n                modelValue: $setup.form.code,\n                \"onUpdate:modelValue\": _cache[2] || (_cache[2] = function ($event) {\n                  return $setup.form.code = $event;\n                }),\n                placeholder: \"请输入验证码\",\n                clearable: \"\",\n                maxlength: \"6\",\n                class: \"code-input\"\n              }, null, 8 /* PROPS */, [\"modelValue\"]), _createVNode(_component_el_button, {\n                type: \"primary\",\n                disabled: !$setup.canSendCode || $setup.countdown > 0,\n                onClick: $setup.sendCode,\n                class: \"send-code-btn\"\n              }, {\n                default: _withCtx(function () {\n                  return [_createTextVNode(_toDisplayString($setup.countdown > 0 ? `${$setup.countdown}s` : '发送验证码'), 1 /* TEXT */)];\n                }),\n                _: 1 /* STABLE */\n              }, 8 /* PROPS */, [\"disabled\"])])];\n            }),\n            _: 1 /* STABLE */\n          })];\n        }),\n        _: 1 /* STABLE */\n      }, 8 /* PROPS */, [\"model\"]), _createVNode(_component_el_button, {\n        type: \"primary\",\n        onClick: $setup.handleVerify,\n        loading: $setup.verifying,\n        class: \"verify-btn\"\n      }, {\n        default: _withCtx(function () {\n          return _cache[4] || (_cache[4] = [_createTextVNode(\" 验证 \")]);\n        }),\n        _: 1 /* STABLE */\n      }, 8 /* PROPS */, [\"loading\"])])];\n    }),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"modelValue\"]);\n}", "map": {"version": 3, "names": ["class", "_createBlock", "_component_el_dialog", "modelValue", "$setup", "visible", "_cache", "$event", "title", "width", "handleClose", "default", "_withCtx", "_createElementVNode", "_hoisted_1", "_createVNode", "_component_el_form", "ref", "model", "form", "rules", "_component_el_form_item", "prop", "_component_el_input", "name", "placeholder", "clearable", "_", "phone", "maxlength", "_hoisted_2", "code", "_component_el_button", "type", "disabled", "canSendCode", "countdown", "onClick", "sendCode", "_createTextVNode", "_toDisplayString", "handleVerify", "loading", "verifying"], "sources": ["D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\interaction\\src\\views\\LiveManagement\\components\\PhoneVerifyDialog.vue"], "sourcesContent": ["<template>\n  <el-dialog v-model=\"visible\" title=\"手机号验证\" width=\"400px\" :before-close=\"handleClose\" :close-on-click-modal=\"false\"\n    class=\"phone-verify-dialog\">\n    <div class=\"verify-form\">\n      <el-form ref=\"formRef\" :model=\"form\" :rules=\"rules\" label-position=\"top\">\n        <el-form-item prop=\"name\">\n          <el-input v-model=\"form.name\" placeholder=\"请输入姓名\" clearable class=\"form-input\" />\n        </el-form-item>\n\n        <el-form-item prop=\"phone\">\n          <el-input v-model=\"form.phone\" placeholder=\"请输入手机号码\" clearable maxlength=\"11\" class=\"form-input\" />\n        </el-form-item>\n\n        <el-form-item prop=\"code\">\n          <div class=\"code-input-group\">\n            <el-input v-model=\"form.code\" placeholder=\"请输入验证码\" clearable maxlength=\"6\" class=\"code-input\" />\n            <el-button type=\"primary\" :disabled=\"!canSendCode || countdown > 0\" @click=\"sendCode\" class=\"send-code-btn\">\n              {{ countdown > 0 ? `${countdown}s` : '发送验证码' }}\n            </el-button>\n          </div>\n        </el-form-item>\n      </el-form>\n\n      <el-button type=\"primary\" @click=\"handleVerify\" :loading=\"verifying\" class=\"verify-btn\">\n        验证\n      </el-button>\n    </div>\n  </el-dialog>\n</template>\n\n<script>\nexport default { name: 'PhoneVerifyDialog' }\n</script>\n\n<script setup>\nimport { ref, computed, watch } from 'vue'\nimport { ElMessage } from 'element-plus'\nimport api from '@/api'\n\nconst props = defineProps({\n  modelValue: { type: Boolean, default: false }\n})\n\nconst emit = defineEmits(['update:modelValue', 'verify-success'])\n\nconst visible = computed({\n  get: () => props.modelValue,\n  set: (value) => emit('update:modelValue', value)\n})\n\nconst formRef = ref(null)\nconst form = ref({\n  name: '',\n  phone: '',\n  code: ''\n})\n\nconst verifying = ref(false)\nconst countdown = ref(0)\nlet countdownTimer = null\n\n// 表单验证规则\nconst rules = {\n  name: [\n    { required: true, message: '请输入姓名', trigger: 'blur' }\n  ],\n  phone: [\n    { required: true, message: '请输入手机号码', trigger: 'blur' },\n    { pattern: /^1[3-9]\\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }\n  ],\n  code: [\n    { required: true, message: '请输入验证码', trigger: 'blur' },\n    { pattern: /^\\d{6}$/, message: '请输入6位数字验证码', trigger: 'blur' }\n  ]\n}\n\n// 是否可以发送验证码\nconst canSendCode = computed(() => {\n  return form.value.phone && /^1[3-9]\\d{9}$/.test(form.value.phone)\n})\n\n// 监听弹窗关闭，重置表单\nwatch(() => props.modelValue, (newVal) => {\n  if (!newVal) {\n    resetForm()\n  }\n})\n\n// 发送验证码\nconst sendCode = async () => {\n  if (!canSendCode.value) {\n    ElMessage.error('请输入正确的手机号码')\n    return\n  }\n\n  try {\n    // 这里调用发送验证码的API\n    const res = await api.sendVerifyCode({\n      sendType: 'no_login', mobile: form.value.phone\n    })\n\n    if (res.code === 200) {\n      ElMessage.success('验证码已发送')\n      startCountdown()\n    } else {\n      ElMessage.error(res.message || '发送验证码失败')\n    }\n  } catch (error) {\n    ElMessage.error('发送验证码失败')\n    console.error('发送验证码失败:', error)\n  }\n}\n\n// 开始倒计时\nconst startCountdown = () => {\n  countdown.value = 60\n  countdownTimer = setInterval(() => {\n    countdown.value--\n    if (countdown.value <= 0) {\n      clearInterval(countdownTimer)\n      countdownTimer = null\n    }\n  }, 1000)\n}\n\n// 验证手机号\nconst handleVerify = async () => {\n  if (!formRef.value) return\n  try {\n    const valid = await formRef.value.validate()\n    if (!valid) return\n    verifying.value = true\n    const res = await api.login({\n      name: form.value.name,\n      phone: form.value.phone,\n      code: form.value.code\n    })\n    if (res.code === 200) {\n      ElMessage.success('验证成功')\n      // 将token存储到localStorage\n      localStorage.setItem('shareToken', res.data.token)\n      // 通知父组件验证成功\n      emit('verify-success', res.data)\n      // 关闭弹窗\n      visible.value = false\n    } else {\n      ElMessage.error(res.message || '验证失败')\n    }\n  } catch (error) {\n    ElMessage.error('验证失败')\n    console.error('验证失败:', error)\n  } finally {\n    verifying.value = false\n  }\n}\n\n// 重置表单\nconst resetForm = () => {\n  form.value = {\n    name: '',\n    phone: '',\n    code: ''\n  }\n  if (formRef.value) {\n    formRef.value.clearValidate()\n  }\n  if (countdownTimer) {\n    clearInterval(countdownTimer)\n    countdownTimer = null\n  }\n  countdown.value = 0\n  verifying.value = false\n}\n\n// 关闭弹窗\nconst handleClose = () => {\n  visible.value = false\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.phone-verify-dialog {\n  .verify-form {\n    padding: 20px 0;\n\n    .form-input {\n      height: 48px;\n      margin-bottom: 20px;\n\n      :deep(.el-input__inner) {\n        height: 48px;\n        line-height: 48px;\n        border-radius: 8px;\n        border: 1px solid #e0e0e0;\n        font-size: 16px;\n\n        &::placeholder {\n          color: #c0c0c0;\n        }\n      }\n    }\n\n    .code-input-group {\n      display: flex;\n      gap: 12px;\n      margin-bottom: 20px;\n\n      .code-input {\n        flex: 1;\n        height: 48px;\n\n        :deep(.el-input__inner) {\n          height: 48px;\n          line-height: 48px;\n          border-radius: 8px;\n          border: 1px solid #e0e0e0;\n          font-size: 16px;\n        }\n      }\n\n      .send-code-btn {\n        height: 48px;\n        padding: 0 16px;\n        border-radius: 8px;\n        background: #6c9fff;\n        border: none;\n        color: white;\n        font-size: 14px;\n        white-space: nowrap;\n\n        &:hover:not(:disabled) {\n          background: #5a8cff;\n        }\n\n        &:disabled {\n          background: #c0c0c0;\n          color: #fff;\n        }\n      }\n    }\n\n    .verify-btn {\n      width: 100%;\n      height: 48px;\n      background: #6c9fff;\n      border: none;\n      border-radius: 8px;\n      color: white;\n      font-size: 16px;\n      font-weight: 500;\n\n      &:hover:not(:disabled) {\n        background: #5a8cff;\n      }\n    }\n  }\n}\n\n:deep(.el-dialog__header) {\n  text-align: center;\n  padding: 20px 20px 0;\n\n  .el-dialog__title {\n    font-size: 18px;\n    font-weight: 500;\n    color: #333;\n  }\n}\n\n:deep(.el-dialog__body) {\n  padding: 10px 20px 20px;\n}\n</style>\n"], "mappings": ";;EAGSA,KAAK,EAAC;AAAa;;EAWbA,KAAK,EAAC;AAAkB;;;;;;;uBAbrCC,YAAA,CA0BYC,oBAAA;IA3BdC,UAAA,EACsBC,MAAA,CAAAC,OAAO;IAD7B,uBAAAC,MAAA,QAAAA,MAAA,gBAAAC,MAAA;MAAA,OACsBH,MAAA,CAAAC,OAAO,GAAAE,MAAA;IAAA;IAAEC,KAAK,EAAC,OAAO;IAACC,KAAK,EAAC,OAAO;IAAE,cAAY,EAAEL,MAAA,CAAAM,WAAW;IAAG,sBAAoB,EAAE,KAAK;IAC/GV,KAAK,EAAC;;IAFVW,OAAA,EAAAC,QAAA,CAGI;MAAA,OAuBM,CAvBNC,mBAAA,CAuBM,OAvBNC,UAuBM,GAtBJC,YAAA,CAiBUC,kBAAA;QAjBDC,GAAG,EAAC,SAAS;QAAEC,KAAK,EAAEd,MAAA,CAAAe,IAAI;QAAGC,KAAK,EAAEhB,MAAA,CAAAgB,KAAK;QAAE,gBAAc,EAAC;;QAJzET,OAAA,EAAAC,QAAA,CAKQ;UAAA,OAEe,CAFfG,YAAA,CAEeM,uBAAA;YAFDC,IAAI,EAAC;UAAM;YALjCX,OAAA,EAAAC,QAAA,CAMU;cAAA,OAAiF,CAAjFG,YAAA,CAAiFQ,mBAAA;gBAN3FpB,UAAA,EAM6BC,MAAA,CAAAe,IAAI,CAACK,IAAI;gBANtC,uBAAAlB,MAAA,QAAAA,MAAA,gBAAAC,MAAA;kBAAA,OAM6BH,MAAA,CAAAe,IAAI,CAACK,IAAI,GAAAjB,MAAA;gBAAA;gBAAEkB,WAAW,EAAC,OAAO;gBAACC,SAAS,EAAT,EAAS;gBAAC1B,KAAK,EAAC;;;YAN5E2B,CAAA;cASQZ,YAAA,CAEeM,uBAAA;YAFDC,IAAI,EAAC;UAAO;YATlCX,OAAA,EAAAC,QAAA,CAUU;cAAA,OAAmG,CAAnGG,YAAA,CAAmGQ,mBAAA;gBAV7GpB,UAAA,EAU6BC,MAAA,CAAAe,IAAI,CAACS,KAAK;gBAVvC,uBAAAtB,MAAA,QAAAA,MAAA,gBAAAC,MAAA;kBAAA,OAU6BH,MAAA,CAAAe,IAAI,CAACS,KAAK,GAAArB,MAAA;gBAAA;gBAAEkB,WAAW,EAAC,SAAS;gBAACC,SAAS,EAAT,EAAS;gBAACG,SAAS,EAAC,IAAI;gBAAC7B,KAAK,EAAC;;;YAV9F2B,CAAA;cAaQZ,YAAA,CAOeM,uBAAA;YAPDC,IAAI,EAAC;UAAM;YAbjCX,OAAA,EAAAC,QAAA,CAcU;cAAA,OAKM,CALNC,mBAAA,CAKM,OALNiB,UAKM,GAJJf,YAAA,CAAgGQ,mBAAA;gBAf5GpB,UAAA,EAe+BC,MAAA,CAAAe,IAAI,CAACY,IAAI;gBAfxC,uBAAAzB,MAAA,QAAAA,MAAA,gBAAAC,MAAA;kBAAA,OAe+BH,MAAA,CAAAe,IAAI,CAACY,IAAI,GAAAxB,MAAA;gBAAA;gBAAEkB,WAAW,EAAC,QAAQ;gBAACC,SAAS,EAAT,EAAS;gBAACG,SAAS,EAAC,GAAG;gBAAC7B,KAAK,EAAC;uDACjFe,YAAA,CAEYiB,oBAAA;gBAFDC,IAAI,EAAC,SAAS;gBAAEC,QAAQ,GAAG9B,MAAA,CAAA+B,WAAW,IAAI/B,MAAA,CAAAgC,SAAS;gBAAOC,OAAK,EAAEjC,MAAA,CAAAkC,QAAQ;gBAAEtC,KAAK,EAAC;;gBAhBxGW,OAAA,EAAAC,QAAA,CAiBc;kBAAA,OAA+C,CAjB7D2B,gBAAA,CAAAC,gBAAA,CAiBiBpC,MAAA,CAAAgC,SAAS,UAAUhC,MAAA,CAAAgC,SAAS,8B;;gBAjB7CT,CAAA;;;YAAAA,CAAA;;;QAAAA,CAAA;oCAuBMZ,YAAA,CAEYiB,oBAAA;QAFDC,IAAI,EAAC,SAAS;QAAEI,OAAK,EAAEjC,MAAA,CAAAqC,YAAY;QAAGC,OAAO,EAAEtC,MAAA,CAAAuC,SAAS;QAAE3C,KAAK,EAAC;;QAvBjFW,OAAA,EAAAC,QAAA,CAuB8F;UAAA,OAExFN,MAAA,QAAAA,MAAA,OAzBNiC,gBAAA,CAuB8F,MAExF,E;;QAzBNZ,CAAA;;;IAAAA,CAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "\"use strict\";\n\nmodule.exports = function () {\n  var idCount = 1;\n\n  /**\n   * Generates a new unique id in the context.\n   * @public\n   * @returns {number} A unique id in the context.\n   */\n  function generate() {\n    return idCount++;\n  }\n  return {\n    generate: generate\n  };\n};", "map": {"version": 3, "names": ["module", "exports", "idCount", "generate"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/node_modules/.pnpm/element-resize-detector@1.2.4/node_modules/element-resize-detector/src/id-generator.js"], "sourcesContent": ["\"use strict\";\n\nmodule.exports = function() {\n    var idCount = 1;\n\n    /**\n     * Generates a new unique id in the context.\n     * @public\n     * @returns {number} A unique id in the context.\n     */\n    function generate() {\n        return idCount++;\n    }\n\n    return {\n        generate: generate\n    };\n};\n"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,OAAO,GAAG,YAAW;EACxB,IAAIC,OAAO,GAAG,CAAC;;EAEf;AACJ;AACA;AACA;AACA;EACI,SAASC,QAAQA,CAAA,EAAG;IAChB,OAAOD,OAAO,EAAE;EACpB;EAEA,OAAO;IACHC,QAAQ,EAAEA;EACd,CAAC;AACL,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}
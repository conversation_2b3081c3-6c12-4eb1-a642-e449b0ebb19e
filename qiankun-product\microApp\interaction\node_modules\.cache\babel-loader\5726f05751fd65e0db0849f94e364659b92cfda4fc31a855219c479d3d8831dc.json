{"ast": null, "code": "import { resolveComponent as _resolveComponent, createVNode as _createVNode, createTextVNode as _createTextVNode, withCtx as _withCtx, createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock } from \"vue\";\nvar _hoisted_1 = {\n  class: \"xyl-import-record\"\n};\nvar _hoisted_2 = {\n  class: \"globalTable\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  var _component_el_table_column = _resolveComponent(\"el-table-column\");\n  var _component_el_button = _resolveComponent(\"el-button\");\n  var _component_el_table = _resolveComponent(\"el-table\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createElementVNode(\"div\", _hoisted_2, [_createVNode(_component_el_table, {\n    data: $setup.tableData\n  }, {\n    default: _withCtx(function () {\n      return [_createVNode(_component_el_table_column, {\n        label: \"导入日期\",\n        \"min-width\": \"160\",\n        prop: \"importDate\"\n      }), _createVNode(_component_el_table_column, {\n        label: \"导入数据条数\",\n        \"min-width\": \"120\",\n        \"show-overflow-tooltip\": \"\",\n        prop: \"receiveLine\"\n      }), _createVNode(_component_el_table_column, {\n        label: \"成功条数\",\n        \"min-width\": \"120\",\n        \"show-overflow-tooltip\": \"\",\n        prop: \"successLine\"\n      }), _createVNode(_component_el_table_column, {\n        label: \"失败条数\",\n        \"min-width\": \"120\",\n        \"show-overflow-tooltip\": \"\",\n        prop: \"falseLine\"\n      }), _createVNode(_component_el_table_column, {\n        label: \"未处理条数\",\n        \"min-width\": \"120\",\n        \"show-overflow-tooltip\": \"\",\n        prop: \"noHandleLine\"\n      }), _createVNode(_component_el_table_column, {\n        label: \"操作\",\n        width: \"260\",\n        fixed: \"right\",\n        \"class-name\": \"globalTableCustom\"\n      }, {\n        default: _withCtx(function (scope) {\n          return [_createVNode(_component_el_button, {\n            onClick: function onClick($event) {\n              return $setup.handleDownload(scope.row);\n            },\n            type: \"primary\",\n            plain: \"\"\n          }, {\n            default: _withCtx(function () {\n              return _cache[0] || (_cache[0] = [_createTextVNode(\"下载源文件\")]);\n            }),\n            _: 2 /* DYNAMIC */\n          }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"onClick\"]), _createVNode(_component_el_button, {\n            onClick: function onClick($event) {\n              return $setup.handleResult(scope.row);\n            },\n            type: \"primary\",\n            plain: \"\"\n          }, {\n            default: _withCtx(function () {\n              return _cache[1] || (_cache[1] = [_createTextVNode(\"下载结果\")]);\n            }),\n            _: 2 /* DYNAMIC */\n          }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"onClick\"])];\n        }),\n        _: 1 /* STABLE */\n      })];\n    }),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"data\"])])]);\n}", "map": {"version": 3, "names": ["class", "_createElementBlock", "_hoisted_1", "_createElementVNode", "_hoisted_2", "_createVNode", "_component_el_table", "data", "$setup", "tableData", "default", "_withCtx", "_component_el_table_column", "label", "prop", "width", "fixed", "scope", "_component_el_button", "onClick", "$event", "handleDownload", "row", "type", "plain", "_cache", "_createTextVNode", "_", "handleResult"], "sources": ["D:\\zy\\xm\\pc\\5.0\\qiankun-product\\common\\components\\xyl-import-excel\\xyl-import-record.vue"], "sourcesContent": ["<template>\r\n  <div class=\"xyl-import-record\">\r\n    <div class=\"globalTable\">\r\n      <el-table :data=\"tableData\">\r\n        <el-table-column label=\"导入日期\" min-width=\"160\" prop=\"importDate\" />\r\n        <el-table-column label=\"导入数据条数\" min-width=\"120\" show-overflow-tooltip prop=\"receiveLine\" />\r\n        <el-table-column label=\"成功条数\" min-width=\"120\" show-overflow-tooltip prop=\"successLine\" />\r\n        <el-table-column label=\"失败条数\" min-width=\"120\" show-overflow-tooltip prop=\"falseLine\" />\r\n        <el-table-column label=\"未处理条数\" min-width=\"120\" show-overflow-tooltip prop=\"noHandleLine\" />\r\n        <el-table-column label=\"操作\" width=\"260\" fixed=\"right\" class-name=\"globalTableCustom\">\r\n          <template #default=\"scope\">\r\n            <el-button @click=\"handleDownload(scope.row)\" type=\"primary\" plain>下载源文件</el-button>\r\n            <el-button @click=\"handleResult(scope.row)\" type=\"primary\" plain>下载结果</el-button>\r\n          </template>\r\n        </el-table-column>\r\n      </el-table>\r\n    </div>\r\n  </div>\r\n</template>\r\n<script>\r\nexport default { name: 'XylImportRecord' }\r\n</script>\r\n<script setup>\r\nimport api from '@/api'\r\nimport { ref, onMounted } from 'vue'\r\nimport { user } from '../../js/system_var.js'\r\nimport { downloadFile, extendDownloadFile } from '../../config/MicroGlobal'\r\nconst props = defineProps({ name: { type: String, default: '' }, module: { type: String, default: '' } })\r\nconst emit = defineEmits(['callback'])\r\nconst tableData = ref([])\r\nonMounted(() => {\r\n  globalExcelHistoryImport()\r\n})\r\nconst globalExcelHistoryImport = async () => {\r\n  const res = await api.globalExcelHistoryImport(props.module)\r\n  var { data } = res\r\n  tableData.value = data.map((v) => ({ ...v, isDisabled: data.map((v) => v.areaId).includes(user.value.areaId) }))\r\n}\r\nconst handleDownload = (row) => {\r\n  if (window.__POWERED_BY_QIANKUN__) {\r\n    downloadFile({\r\n      fileId: row.importFileId,\r\n      fileType: 'xlsx',\r\n      fileName: `${props.name}导入源文件 -- 批次号：${row.batchNumber} -- 导入日期：${row.importDate}.xlsx`\r\n    })\r\n  } else {\r\n    store.commit('setDownloadFile', {\r\n      fileId: row.importFileId,\r\n      fileType: 'xlsx',\r\n      fileName: `${props.name}导入源文件 -- 批次号：${row.batchNumber} -- 导入日期：${row.importDate}.xlsx`\r\n    })\r\n  }\r\n}\r\nconst handleResult = (row) => {\r\n  if (window.__POWERED_BY_QIANKUN__) {\r\n    extendDownloadFile({\r\n      url: `/excel/downloadImportResult/${row.batchNumber}`,\r\n      params: {},\r\n      fileType: 'xlsx',\r\n      fileName: `${props.name}导入结果 -- 批次号：${row.batchNumber} -- 导入日期：${row.importDate}.xlsx`\r\n    })\r\n  } else {\r\n    store.commit('setExtendDownloadFile', {\r\n      url: `/excel/downloadImportResult/${row.batchNumber}`,\r\n      params: {},\r\n      fileType: 'xlsx',\r\n      fileName: `${props.name}导入结果 -- 批次号：${row.batchNumber} -- 导入日期：${row.importDate}.xlsx`\r\n    })\r\n  }\r\n}\r\n</script>\r\n<style lang=\"scss\">\r\n.xyl-import-record {\r\n  width: 990px;\r\n  height: calc(85vh - 52px);\r\n  padding: var(--zy-distance-three) var(--zy-distance-two);\r\n  border: 1px solid var(--zy-el-border-color-lighter);\r\n\r\n  .globalTable {\r\n    width: 100%;\r\n    height: 100%;\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;EACOA,KAAK,EAAC;AAAmB;;EACvBA,KAAK,EAAC;AAAa;;;;;uBAD1BC,mBAAA,CAgBM,OAhBNC,UAgBM,GAfJC,mBAAA,CAcM,OAdNC,UAcM,GAbJC,YAAA,CAYWC,mBAAA;IAZAC,IAAI,EAAEC,MAAA,CAAAC;EAAS;IAHhCC,OAAA,EAAAC,QAAA,CAIQ;MAAA,OAAkE,CAAlEN,YAAA,CAAkEO,0BAAA;QAAjDC,KAAK,EAAC,MAAM;QAAC,WAAS,EAAC,KAAK;QAACC,IAAI,EAAC;UACnDT,YAAA,CAA2FO,0BAAA;QAA1EC,KAAK,EAAC,QAAQ;QAAC,WAAS,EAAC,KAAK;QAAC,uBAAqB,EAArB,EAAqB;QAACC,IAAI,EAAC;UAC3ET,YAAA,CAAyFO,0BAAA;QAAxEC,KAAK,EAAC,MAAM;QAAC,WAAS,EAAC,KAAK;QAAC,uBAAqB,EAArB,EAAqB;QAACC,IAAI,EAAC;UACzET,YAAA,CAAuFO,0BAAA;QAAtEC,KAAK,EAAC,MAAM;QAAC,WAAS,EAAC,KAAK;QAAC,uBAAqB,EAArB,EAAqB;QAACC,IAAI,EAAC;UACzET,YAAA,CAA2FO,0BAAA;QAA1EC,KAAK,EAAC,OAAO;QAAC,WAAS,EAAC,KAAK;QAAC,uBAAqB,EAArB,EAAqB;QAACC,IAAI,EAAC;UAC1ET,YAAA,CAKkBO,0BAAA;QALDC,KAAK,EAAC,IAAI;QAACE,KAAK,EAAC,KAAK;QAACC,KAAK,EAAC,OAAO;QAAC,YAAU,EAAC;;QACpDN,OAAO,EAAAC,QAAA,CAChB,UAAoFM,KAD7D;UAAA,QACvBZ,YAAA,CAAoFa,oBAAA;YAAxEC,OAAK,WAALA,OAAKA,CAAAC,MAAA;cAAA,OAAEZ,MAAA,CAAAa,cAAc,CAACJ,KAAK,CAACK,GAAG;YAAA;YAAGC,IAAI,EAAC,SAAS;YAACC,KAAK,EAAL;;YAXzEd,OAAA,EAAAC,QAAA,CAW+E;cAAA,OAAKc,MAAA,QAAAA,MAAA,OAXpFC,gBAAA,CAW+E,OAAK,E;;YAXpFC,CAAA;4DAYYtB,YAAA,CAAiFa,oBAAA;YAArEC,OAAK,WAALA,OAAKA,CAAAC,MAAA;cAAA,OAAEZ,MAAA,CAAAoB,YAAY,CAACX,KAAK,CAACK,GAAG;YAAA;YAAGC,IAAI,EAAC,SAAS;YAACC,KAAK,EAAL;;YAZvEd,OAAA,EAAAC,QAAA,CAY6E;cAAA,OAAIc,MAAA,QAAAA,MAAA,OAZjFC,gBAAA,CAY6E,MAAI,E;;YAZjFC,CAAA;;;QAAAA,CAAA;;;IAAAA,CAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
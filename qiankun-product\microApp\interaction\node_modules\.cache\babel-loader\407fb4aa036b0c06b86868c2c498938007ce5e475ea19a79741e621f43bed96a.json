{"ast": null, "code": "function _toConsumableArray(r) { return _arrayWithoutHoles(r) || _iterableToArray(r) || _unsupportedIterableToArray(r) || _nonIterableSpread(); }\nfunction _nonIterableSpread() { throw new TypeError(\"Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); }\nfunction _unsupportedIterableToArray(r, a) { if (r) { if (\"string\" == typeof r) return _arrayLikeToArray(r, a); var t = {}.toString.call(r).slice(8, -1); return \"Object\" === t && r.constructor && (t = r.constructor.name), \"Map\" === t || \"Set\" === t ? Array.from(r) : \"Arguments\" === t || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t) ? _arrayLikeToArray(r, a) : void 0; } }\nfunction _iterableToArray(r) { if (\"undefined\" != typeof Symbol && null != r[Symbol.iterator] || null != r[\"@@iterator\"]) return Array.from(r); }\nfunction _arrayWithoutHoles(r) { if (Array.isArray(r)) return _arrayLikeToArray(r); }\nfunction _arrayLikeToArray(r, a) { (null == a || a > r.length) && (a = r.length); for (var e = 0, n = Array(a); e < a; e++) n[e] = r[e]; return n; }\nfunction _classCallCheck(a, n) { if (!(a instanceof n)) throw new TypeError(\"Cannot call a class as a function\"); }\nfunction _defineProperties(e, r) { for (var t = 0; t < r.length; t++) { var o = r[t]; o.enumerable = o.enumerable || !1, o.configurable = !0, \"value\" in o && (o.writable = !0), Object.defineProperty(e, _toPropertyKey(o.key), o); } }\nfunction _createClass(e, r, t) { return r && _defineProperties(e.prototype, r), t && _defineProperties(e, t), Object.defineProperty(e, \"prototype\", { writable: !1 }), e; }\nfunction _toPropertyKey(t) { var i = _toPrimitive(t, \"string\"); return \"symbol\" == typeof i ? i : i + \"\"; }\nfunction _toPrimitive(t, r) { if (\"object\" != typeof t || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || \"default\"); if (\"object\" != typeof i) return i; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (\"string\" === r ? String : Number)(t); }\nimport { reactive } from 'vue';\nimport '../../../../utils/index.mjs';\nimport { markNodeData, NODE_KEY } from './util.mjs';\nimport { hasOwn } from '@vue/shared';\nvar getChildState = function getChildState(node) {\n  var all = true;\n  var none = true;\n  var allWithoutDisable = true;\n  for (var i = 0, j = node.length; i < j; i++) {\n    var n = node[i];\n    if (n.checked !== true || n.indeterminate) {\n      all = false;\n      if (!n.disabled) {\n        allWithoutDisable = false;\n      }\n    }\n    if (n.checked !== false || n.indeterminate) {\n      none = false;\n    }\n  }\n  return {\n    all,\n    none,\n    allWithoutDisable,\n    half: !all && !none\n  };\n};\nvar _reInitChecked = function reInitChecked(node) {\n  if (node.childNodes.length === 0 || node.loading) return;\n  var _getChildState = getChildState(node.childNodes),\n    all = _getChildState.all,\n    none = _getChildState.none,\n    half = _getChildState.half;\n  if (all) {\n    node.checked = true;\n    node.indeterminate = false;\n  } else if (half) {\n    node.checked = false;\n    node.indeterminate = true;\n  } else if (none) {\n    node.checked = false;\n    node.indeterminate = false;\n  }\n  var parent = node.parent;\n  if (!parent || parent.level === 0) return;\n  if (!node.store.checkStrictly) {\n    _reInitChecked(parent);\n  }\n};\nvar getPropertyFromData = function getPropertyFromData(node, prop) {\n  var props = node.store.props;\n  var data = node.data || {};\n  var config = props[prop];\n  if (typeof config === \"function\") {\n    return config(data, node);\n  } else if (typeof config === \"string\") {\n    return data[config];\n  } else if (typeof config === \"undefined\") {\n    var dataProp = data[prop];\n    return dataProp === void 0 ? \"\" : dataProp;\n  }\n};\nvar nodeIdSeed = 0;\nvar Node = /*#__PURE__*/function () {\n  function Node(options) {\n    _classCallCheck(this, Node);\n    this.id = nodeIdSeed++;\n    this.text = null;\n    this.checked = false;\n    this.indeterminate = false;\n    this.data = null;\n    this.expanded = false;\n    this.parent = null;\n    this.visible = true;\n    this.isCurrent = false;\n    this.canFocus = false;\n    for (var name in options) {\n      if (hasOwn(options, name)) {\n        this[name] = options[name];\n      }\n    }\n    this.level = 0;\n    this.loaded = false;\n    this.childNodes = [];\n    this.loading = false;\n    if (this.parent) {\n      this.level = this.parent.level + 1;\n    }\n  }\n  return _createClass(Node, [{\n    key: \"initialize\",\n    value: function initialize() {\n      var store = this.store;\n      if (!store) {\n        throw new Error(\"[Node]store is required!\");\n      }\n      store.registerNode(this);\n      var props = store.props;\n      if (props && typeof props.isLeaf !== \"undefined\") {\n        var isLeaf = getPropertyFromData(this, \"isLeaf\");\n        if (typeof isLeaf === \"boolean\") {\n          this.isLeafByUser = isLeaf;\n        }\n      }\n      if (store.lazy !== true && this.data) {\n        this.setData(this.data);\n        if (store.defaultExpandAll) {\n          this.expanded = true;\n          this.canFocus = true;\n        }\n      } else if (this.level > 0 && store.lazy && store.defaultExpandAll) {\n        this.expand();\n      }\n      if (!Array.isArray(this.data)) {\n        markNodeData(this, this.data);\n      }\n      if (!this.data) return;\n      var defaultExpandedKeys = store.defaultExpandedKeys;\n      var key = store.key;\n      if (key && defaultExpandedKeys && defaultExpandedKeys.includes(this.key)) {\n        this.expand(null, store.autoExpandParent);\n      }\n      if (key && store.currentNodeKey !== void 0 && this.key === store.currentNodeKey) {\n        store.currentNode = this;\n        store.currentNode.isCurrent = true;\n      }\n      if (store.lazy) {\n        store._initDefaultCheckedNode(this);\n      }\n      this.updateLeafState();\n      if (this.parent && (this.level === 1 || this.parent.expanded === true)) this.canFocus = true;\n    }\n  }, {\n    key: \"setData\",\n    value: function setData(data) {\n      if (!Array.isArray(data)) {\n        markNodeData(this, data);\n      }\n      this.data = data;\n      this.childNodes = [];\n      var children;\n      if (this.level === 0 && Array.isArray(this.data)) {\n        children = this.data;\n      } else {\n        children = getPropertyFromData(this, \"children\") || [];\n      }\n      for (var i = 0, j = children.length; i < j; i++) {\n        this.insertChild({\n          data: children[i]\n        });\n      }\n    }\n  }, {\n    key: \"label\",\n    get: function get() {\n      return getPropertyFromData(this, \"label\");\n    }\n  }, {\n    key: \"key\",\n    get: function get() {\n      var nodeKey = this.store.key;\n      if (this.data) return this.data[nodeKey];\n      return null;\n    }\n  }, {\n    key: \"disabled\",\n    get: function get() {\n      return getPropertyFromData(this, \"disabled\");\n    }\n  }, {\n    key: \"nextSibling\",\n    get: function get() {\n      var parent = this.parent;\n      if (parent) {\n        var index = parent.childNodes.indexOf(this);\n        if (index > -1) {\n          return parent.childNodes[index + 1];\n        }\n      }\n      return null;\n    }\n  }, {\n    key: \"previousSibling\",\n    get: function get() {\n      var parent = this.parent;\n      if (parent) {\n        var index = parent.childNodes.indexOf(this);\n        if (index > -1) {\n          return index > 0 ? parent.childNodes[index - 1] : null;\n        }\n      }\n      return null;\n    }\n  }, {\n    key: \"contains\",\n    value: function contains(target) {\n      var deep = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : true;\n      return (this.childNodes || []).some(function (child) {\n        return child === target || deep && child.contains(target);\n      });\n    }\n  }, {\n    key: \"remove\",\n    value: function remove() {\n      var parent = this.parent;\n      if (parent) {\n        parent.removeChild(this);\n      }\n    }\n  }, {\n    key: \"insertChild\",\n    value: function insertChild(child, index, batch) {\n      if (!child) throw new Error(\"InsertChild error: child is required.\");\n      if (!(child instanceof Node)) {\n        if (!batch) {\n          var children = this.getChildren(true);\n          if (!children.includes(child.data)) {\n            if (typeof index === \"undefined\" || index < 0) {\n              children.push(child.data);\n            } else {\n              children.splice(index, 0, child.data);\n            }\n          }\n        }\n        Object.assign(child, {\n          parent: this,\n          store: this.store\n        });\n        child = reactive(new Node(child));\n        if (child instanceof Node) {\n          child.initialize();\n        }\n      }\n      ;\n      child.level = this.level + 1;\n      if (typeof index === \"undefined\" || index < 0) {\n        this.childNodes.push(child);\n      } else {\n        this.childNodes.splice(index, 0, child);\n      }\n      this.updateLeafState();\n    }\n  }, {\n    key: \"insertBefore\",\n    value: function insertBefore(child, ref) {\n      var index;\n      if (ref) {\n        index = this.childNodes.indexOf(ref);\n      }\n      this.insertChild(child, index);\n    }\n  }, {\n    key: \"insertAfter\",\n    value: function insertAfter(child, ref) {\n      var index;\n      if (ref) {\n        index = this.childNodes.indexOf(ref);\n        if (index !== -1) index += 1;\n      }\n      this.insertChild(child, index);\n    }\n  }, {\n    key: \"removeChild\",\n    value: function removeChild(child) {\n      var children = this.getChildren() || [];\n      var dataIndex = children.indexOf(child.data);\n      if (dataIndex > -1) {\n        children.splice(dataIndex, 1);\n      }\n      var index = this.childNodes.indexOf(child);\n      if (index > -1) {\n        this.store && this.store.deregisterNode(child);\n        child.parent = null;\n        this.childNodes.splice(index, 1);\n      }\n      this.updateLeafState();\n    }\n  }, {\n    key: \"removeChildByData\",\n    value: function removeChildByData(data) {\n      var targetNode = null;\n      for (var i = 0; i < this.childNodes.length; i++) {\n        if (this.childNodes[i].data === data) {\n          targetNode = this.childNodes[i];\n          break;\n        }\n      }\n      if (targetNode) {\n        this.removeChild(targetNode);\n      }\n    }\n  }, {\n    key: \"expand\",\n    value: function expand(callback, expandParent) {\n      var _this = this;\n      var done = function done() {\n        if (expandParent) {\n          var parent = _this.parent;\n          while (parent.level > 0) {\n            parent.expanded = true;\n            parent = parent.parent;\n          }\n        }\n        _this.expanded = true;\n        if (callback) callback();\n        _this.childNodes.forEach(function (item) {\n          item.canFocus = true;\n        });\n      };\n      if (this.shouldLoadData()) {\n        this.loadData(function (data) {\n          if (Array.isArray(data)) {\n            if (_this.checked) {\n              _this.setChecked(true, true);\n            } else if (!_this.store.checkStrictly) {\n              _reInitChecked(_this);\n            }\n            done();\n          }\n        });\n      } else {\n        done();\n      }\n    }\n  }, {\n    key: \"doCreateChildren\",\n    value: function doCreateChildren(array) {\n      var _this2 = this;\n      var defaultProps = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n      array.forEach(function (item) {\n        _this2.insertChild(Object.assign({\n          data: item\n        }, defaultProps), void 0, true);\n      });\n    }\n  }, {\n    key: \"collapse\",\n    value: function collapse() {\n      this.expanded = false;\n      this.childNodes.forEach(function (item) {\n        item.canFocus = false;\n      });\n    }\n  }, {\n    key: \"shouldLoadData\",\n    value: function shouldLoadData() {\n      return this.store.lazy === true && this.store.load && !this.loaded;\n    }\n  }, {\n    key: \"updateLeafState\",\n    value: function updateLeafState() {\n      if (this.store.lazy === true && this.loaded !== true && typeof this.isLeafByUser !== \"undefined\") {\n        this.isLeaf = this.isLeafByUser;\n        return;\n      }\n      var childNodes = this.childNodes;\n      if (!this.store.lazy || this.store.lazy === true && this.loaded === true) {\n        this.isLeaf = !childNodes || childNodes.length === 0;\n        return;\n      }\n      this.isLeaf = false;\n    }\n  }, {\n    key: \"setChecked\",\n    value: function setChecked(value, deep, recursion, passValue) {\n      var _this3 = this;\n      this.indeterminate = value === \"half\";\n      this.checked = value === true;\n      if (this.store.checkStrictly) return;\n      if (!(this.shouldLoadData() && !this.store.checkDescendants)) {\n        var _getChildState2 = getChildState(this.childNodes),\n          all = _getChildState2.all,\n          allWithoutDisable = _getChildState2.allWithoutDisable;\n        if (!this.isLeaf && !all && allWithoutDisable) {\n          this.checked = false;\n          value = false;\n        }\n        var handleDescendants = function handleDescendants() {\n          if (deep) {\n            var childNodes = _this3.childNodes;\n            for (var i = 0, j = childNodes.length; i < j; i++) {\n              var child = childNodes[i];\n              passValue = passValue || value !== false;\n              var isCheck = child.disabled ? child.checked : passValue;\n              child.setChecked(isCheck, deep, true, passValue);\n            }\n            var _getChildState3 = getChildState(childNodes),\n              half = _getChildState3.half,\n              all2 = _getChildState3.all;\n            if (!all2) {\n              _this3.checked = all2;\n              _this3.indeterminate = half;\n            }\n          }\n        };\n        if (this.shouldLoadData()) {\n          this.loadData(function () {\n            handleDescendants();\n            _reInitChecked(_this3);\n          }, {\n            checked: value !== false\n          });\n          return;\n        } else {\n          handleDescendants();\n        }\n      }\n      var parent = this.parent;\n      if (!parent || parent.level === 0) return;\n      if (!recursion) {\n        _reInitChecked(parent);\n      }\n    }\n  }, {\n    key: \"getChildren\",\n    value: function getChildren() {\n      var forceInit = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : false;\n      if (this.level === 0) return this.data;\n      var data = this.data;\n      if (!data) return null;\n      var props = this.store.props;\n      var children = \"children\";\n      if (props) {\n        children = props.children || \"children\";\n      }\n      if (data[children] === void 0) {\n        data[children] = null;\n      }\n      if (forceInit && !data[children]) {\n        data[children] = [];\n      }\n      return data[children];\n    }\n  }, {\n    key: \"updateChildren\",\n    value: function updateChildren() {\n      var _this4 = this;\n      var newData = this.getChildren() || [];\n      var oldData = this.childNodes.map(function (node) {\n        return node.data;\n      });\n      var newDataMap = {};\n      var newNodes = [];\n      newData.forEach(function (item, index) {\n        var key = item[NODE_KEY];\n        var isNodeExists = !!key && oldData.findIndex(function (data) {\n          return data[NODE_KEY] === key;\n        }) >= 0;\n        if (isNodeExists) {\n          newDataMap[key] = {\n            index,\n            data: item\n          };\n        } else {\n          newNodes.push({\n            index,\n            data: item\n          });\n        }\n      });\n      if (!this.store.lazy) {\n        oldData.forEach(function (item) {\n          if (!newDataMap[item[NODE_KEY]]) _this4.removeChildByData(item);\n        });\n      }\n      newNodes.forEach(function (_ref) {\n        var index = _ref.index,\n          data = _ref.data;\n        _this4.insertChild({\n          data\n        }, index);\n      });\n      this.updateLeafState();\n    }\n  }, {\n    key: \"loadData\",\n    value: function loadData(callback) {\n      var _this5 = this;\n      var defaultProps = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n      if (this.store.lazy === true && this.store.load && !this.loaded && (!this.loading || Object.keys(defaultProps).length)) {\n        this.loading = true;\n        var resolve = function resolve(children) {\n          _this5.childNodes = [];\n          _this5.doCreateChildren(children, defaultProps);\n          _this5.loaded = true;\n          _this5.loading = false;\n          _this5.updateLeafState();\n          if (callback) {\n            callback.call(_this5, children);\n          }\n        };\n        var reject = function reject() {\n          _this5.loading = false;\n        };\n        this.store.load(this, resolve, reject);\n      } else {\n        if (callback) {\n          callback.call(this);\n        }\n      }\n    }\n  }, {\n    key: \"eachNode\",\n    value: function eachNode(callback) {\n      var arr = [this];\n      while (arr.length) {\n        var node = arr.shift();\n        arr.unshift.apply(arr, _toConsumableArray(node.childNodes));\n        callback(node);\n      }\n    }\n  }, {\n    key: \"reInitChecked\",\n    value: function reInitChecked() {\n      if (this.store.checkStrictly) return;\n      _reInitChecked(this);\n    }\n  }]);\n}();\nexport { Node as default, getChildState };", "map": {"version": 3, "names": ["getChildState", "node", "all", "none", "allWithoutDisable", "i", "j", "length", "n", "checked", "indeterminate", "disabled", "half", "reInitChecked", "childNodes", "loading", "_getChildState", "parent", "level", "store", "checkStrictly", "getPropertyFromData", "prop", "props", "data", "config", "dataProp", "nodeIdSeed", "Node", "options", "_classCallCheck", "id", "text", "expanded", "visible", "isCurrent", "canFocus", "name", "hasOwn", "loaded", "_createClass", "key", "value", "initialize", "Error", "registerNode", "<PERSON><PERSON><PERSON><PERSON>", "isLeafByUser", "lazy", "setData", "defaultExpandAll", "expand", "Array", "isArray", "markNodeData", "defaultExpandedKeys", "includes", "autoExpandParent", "currentNodeKey", "currentNode", "_initDefaultCheckedNode", "updateLeafState", "children", "<PERSON><PERSON><PERSON><PERSON>", "get", "nodeKey", "index", "indexOf", "contains", "target", "deep", "arguments", "undefined", "some", "child", "remove", "<PERSON><PERSON><PERSON><PERSON>", "batch", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "push", "splice", "Object", "assign", "reactive", "insertBefore", "ref", "insertAfter", "dataIndex", "deregisterNode", "removeChildByData", "targetNode", "callback", "expandParent", "_this", "done", "for<PERSON>ach", "item", "shouldLoadData", "loadData", "setChecked", "doCreate<PERSON><PERSON><PERSON>n", "array", "_this2", "defaultProps", "collapse", "load", "recursion", "passValue", "_this3", "checkDescendants", "_getChildState2", "handleDescendants", "is<PERSON><PERSON><PERSON>", "_getChildState3", "all2", "forceInit", "update<PERSON><PERSON><PERSON>n", "_this4", "newData", "oldData", "map", "newDataMap", "newNodes", "NODE_KEY", "isNodeExists", "findIndex", "_ref", "_this5", "keys", "resolve", "call", "reject", "eachNode", "arr", "shift", "unshift", "apply", "_toConsumableArray"], "sources": ["../../../../../../../packages/components/tree/src/model/node.ts"], "sourcesContent": ["// @ts-nocheck\nimport { reactive } from 'vue'\nimport { hasOwn } from '@element-plus/utils'\nimport { NODE_KEY, markNodeData } from './util'\nimport type TreeStore from './tree-store'\n\nimport type { Nullable } from '@element-plus/utils'\nimport type {\n  FakeNode,\n  TreeKey,\n  TreeNodeChildState,\n  TreeNodeData,\n  TreeNodeLoadedDefaultProps,\n  TreeNodeOptions,\n} from '../tree.type'\n\nexport const getChildState = (node: Node[]): TreeNodeChildState => {\n  let all = true\n  let none = true\n  let allWithoutDisable = true\n  for (let i = 0, j = node.length; i < j; i++) {\n    const n = node[i]\n    if (n.checked !== true || n.indeterminate) {\n      all = false\n      if (!n.disabled) {\n        allWithoutDisable = false\n      }\n    }\n    if (n.checked !== false || n.indeterminate) {\n      none = false\n    }\n  }\n\n  return { all, none, allWithoutDisable, half: !all && !none }\n}\n\nconst reInitChecked = function (node: Node): void {\n  if (node.childNodes.length === 0 || node.loading) return\n\n  const { all, none, half } = getChildState(node.childNodes)\n  if (all) {\n    node.checked = true\n    node.indeterminate = false\n  } else if (half) {\n    node.checked = false\n    node.indeterminate = true\n  } else if (none) {\n    node.checked = false\n    node.indeterminate = false\n  }\n\n  const parent = node.parent\n  if (!parent || parent.level === 0) return\n\n  if (!node.store.checkStrictly) {\n    reInitChecked(parent)\n  }\n}\n\nconst getPropertyFromData = function (node: Node, prop: string): any {\n  const props = node.store.props\n  const data = node.data || {}\n  const config = props[prop]\n\n  if (typeof config === 'function') {\n    return config(data, node)\n  } else if (typeof config === 'string') {\n    return data[config]\n  } else if (typeof config === 'undefined') {\n    const dataProp = data[prop]\n    return dataProp === undefined ? '' : dataProp\n  }\n}\n\nlet nodeIdSeed = 0\n\nclass Node {\n  id: number\n  text: string\n  checked: boolean\n  indeterminate: boolean\n  data: TreeNodeData\n  expanded: boolean\n  parent: Node\n  visible: boolean\n  isCurrent: boolean\n  store: TreeStore\n  isLeafByUser: boolean\n  isLeaf: boolean\n  canFocus: boolean\n\n  level: number\n  loaded: boolean\n  childNodes: Node[]\n  loading: boolean\n\n  constructor(options: TreeNodeOptions) {\n    this.id = nodeIdSeed++\n    this.text = null\n    this.checked = false\n    this.indeterminate = false\n    this.data = null\n    this.expanded = false\n    this.parent = null\n    this.visible = true\n    this.isCurrent = false\n    this.canFocus = false\n\n    for (const name in options) {\n      if (hasOwn(options, name)) {\n        this[name] = options[name]\n      }\n    }\n\n    // internal\n    this.level = 0\n    this.loaded = false\n    this.childNodes = []\n    this.loading = false\n\n    if (this.parent) {\n      this.level = this.parent.level + 1\n    }\n  }\n\n  initialize() {\n    const store = this.store\n    if (!store) {\n      throw new Error('[Node]store is required!')\n    }\n    store.registerNode(this)\n\n    const props = store.props\n    if (props && typeof props.isLeaf !== 'undefined') {\n      const isLeaf = getPropertyFromData(this, 'isLeaf')\n      if (typeof isLeaf === 'boolean') {\n        this.isLeafByUser = isLeaf\n      }\n    }\n\n    if (store.lazy !== true && this.data) {\n      this.setData(this.data)\n\n      if (store.defaultExpandAll) {\n        this.expanded = true\n        this.canFocus = true\n      }\n    } else if (this.level > 0 && store.lazy && store.defaultExpandAll) {\n      this.expand()\n    }\n    if (!Array.isArray(this.data)) {\n      markNodeData(this, this.data)\n    }\n    if (!this.data) return\n\n    const defaultExpandedKeys = store.defaultExpandedKeys\n    const key = store.key\n\n    if (key && defaultExpandedKeys && defaultExpandedKeys.includes(this.key)) {\n      this.expand(null, store.autoExpandParent)\n    }\n\n    if (\n      key &&\n      store.currentNodeKey !== undefined &&\n      this.key === store.currentNodeKey\n    ) {\n      store.currentNode = this\n      store.currentNode.isCurrent = true\n    }\n\n    if (store.lazy) {\n      store._initDefaultCheckedNode(this)\n    }\n\n    this.updateLeafState()\n    if (this.parent && (this.level === 1 || this.parent.expanded === true))\n      this.canFocus = true\n  }\n\n  setData(data: TreeNodeData): void {\n    if (!Array.isArray(data)) {\n      markNodeData(this, data)\n    }\n\n    this.data = data\n    this.childNodes = []\n\n    let children\n    if (this.level === 0 && Array.isArray(this.data)) {\n      children = this.data\n    } else {\n      children = getPropertyFromData(this, 'children') || []\n    }\n\n    for (let i = 0, j = children.length; i < j; i++) {\n      this.insertChild({ data: children[i] })\n    }\n  }\n\n  get label(): string {\n    return getPropertyFromData(this, 'label')\n  }\n\n  get key(): TreeKey {\n    const nodeKey = this.store.key\n    if (this.data) return this.data[nodeKey]\n    return null\n  }\n\n  get disabled(): boolean {\n    return getPropertyFromData(this, 'disabled')\n  }\n\n  get nextSibling(): Nullable<Node> {\n    const parent = this.parent\n    if (parent) {\n      const index = parent.childNodes.indexOf(this)\n      if (index > -1) {\n        return parent.childNodes[index + 1]\n      }\n    }\n    return null\n  }\n\n  get previousSibling(): Nullable<Node> {\n    const parent = this.parent\n    if (parent) {\n      const index = parent.childNodes.indexOf(this)\n      if (index > -1) {\n        return index > 0 ? parent.childNodes[index - 1] : null\n      }\n    }\n    return null\n  }\n\n  contains(target: Node, deep = true): boolean {\n    return (this.childNodes || []).some(\n      (child) => child === target || (deep && child.contains(target))\n    )\n  }\n\n  remove(): void {\n    const parent = this.parent\n    if (parent) {\n      parent.removeChild(this)\n    }\n  }\n\n  insertChild(child?: FakeNode | Node, index?: number, batch?: boolean): void {\n    if (!child) throw new Error('InsertChild error: child is required.')\n\n    if (!(child instanceof Node)) {\n      if (!batch) {\n        const children = this.getChildren(true)\n        if (!children.includes(child.data)) {\n          if (typeof index === 'undefined' || index < 0) {\n            children.push(child.data)\n          } else {\n            children.splice(index, 0, child.data)\n          }\n        }\n      }\n      Object.assign(child, {\n        parent: this,\n        store: this.store,\n      })\n      child = reactive(new Node(child as TreeNodeOptions))\n      if (child instanceof Node) {\n        child.initialize()\n      }\n    }\n\n    ;(child as Node).level = this.level + 1\n\n    if (typeof index === 'undefined' || index < 0) {\n      this.childNodes.push(child as Node)\n    } else {\n      this.childNodes.splice(index, 0, child as Node)\n    }\n\n    this.updateLeafState()\n  }\n\n  insertBefore(child: FakeNode | Node, ref: Node): void {\n    let index\n    if (ref) {\n      index = this.childNodes.indexOf(ref)\n    }\n    this.insertChild(child, index)\n  }\n\n  insertAfter(child: FakeNode | Node, ref: Node): void {\n    let index\n    if (ref) {\n      index = this.childNodes.indexOf(ref)\n      if (index !== -1) index += 1\n    }\n    this.insertChild(child, index)\n  }\n\n  removeChild(child: Node): void {\n    const children = this.getChildren() || []\n    const dataIndex = children.indexOf(child.data)\n    if (dataIndex > -1) {\n      children.splice(dataIndex, 1)\n    }\n\n    const index = this.childNodes.indexOf(child)\n\n    if (index > -1) {\n      this.store && this.store.deregisterNode(child)\n      child.parent = null\n      this.childNodes.splice(index, 1)\n    }\n\n    this.updateLeafState()\n  }\n\n  removeChildByData(data: TreeNodeData): void {\n    let targetNode: Node = null\n\n    for (let i = 0; i < this.childNodes.length; i++) {\n      if (this.childNodes[i].data === data) {\n        targetNode = this.childNodes[i]\n        break\n      }\n    }\n\n    if (targetNode) {\n      this.removeChild(targetNode)\n    }\n  }\n\n  expand(callback?: () => void, expandParent?: boolean): void {\n    const done = (): void => {\n      if (expandParent) {\n        let parent = this.parent\n        while (parent.level > 0) {\n          parent.expanded = true\n          parent = parent.parent\n        }\n      }\n      this.expanded = true\n      if (callback) callback()\n      this.childNodes.forEach((item) => {\n        item.canFocus = true\n      })\n    }\n\n    if (this.shouldLoadData()) {\n      this.loadData((data) => {\n        if (Array.isArray(data)) {\n          if (this.checked) {\n            this.setChecked(true, true)\n          } else if (!this.store.checkStrictly) {\n            reInitChecked(this)\n          }\n          done()\n        }\n      })\n    } else {\n      done()\n    }\n  }\n\n  doCreateChildren(\n    array: TreeNodeData[],\n    defaultProps: TreeNodeLoadedDefaultProps = {}\n  ): void {\n    array.forEach((item) => {\n      this.insertChild(\n        Object.assign({ data: item }, defaultProps),\n        undefined,\n        true\n      )\n    })\n  }\n\n  collapse(): void {\n    this.expanded = false\n    this.childNodes.forEach((item) => {\n      item.canFocus = false\n    })\n  }\n\n  shouldLoadData(): boolean {\n    return this.store.lazy === true && this.store.load && !this.loaded\n  }\n\n  updateLeafState(): void {\n    if (\n      this.store.lazy === true &&\n      this.loaded !== true &&\n      typeof this.isLeafByUser !== 'undefined'\n    ) {\n      this.isLeaf = this.isLeafByUser\n      return\n    }\n    const childNodes = this.childNodes\n    if (\n      !this.store.lazy ||\n      (this.store.lazy === true && this.loaded === true)\n    ) {\n      this.isLeaf = !childNodes || childNodes.length === 0\n      return\n    }\n    this.isLeaf = false\n  }\n\n  setChecked(\n    value?: boolean | string,\n    deep?: boolean,\n    recursion?: boolean,\n    passValue?: boolean\n  ) {\n    this.indeterminate = value === 'half'\n    this.checked = value === true\n\n    if (this.store.checkStrictly) return\n\n    if (!(this.shouldLoadData() && !this.store.checkDescendants)) {\n      const { all, allWithoutDisable } = getChildState(this.childNodes)\n\n      if (!this.isLeaf && !all && allWithoutDisable) {\n        this.checked = false\n        value = false\n      }\n\n      const handleDescendants = (): void => {\n        if (deep) {\n          const childNodes = this.childNodes\n          for (let i = 0, j = childNodes.length; i < j; i++) {\n            const child = childNodes[i]\n            passValue = passValue || value !== false\n            const isCheck = child.disabled ? child.checked : passValue\n            child.setChecked(isCheck, deep, true, passValue)\n          }\n          const { half, all } = getChildState(childNodes)\n          if (!all) {\n            this.checked = all\n            this.indeterminate = half\n          }\n        }\n      }\n\n      if (this.shouldLoadData()) {\n        // Only work on lazy load data.\n        this.loadData(\n          () => {\n            handleDescendants()\n            reInitChecked(this)\n          },\n          {\n            checked: value !== false,\n          }\n        )\n        return\n      } else {\n        handleDescendants()\n      }\n    }\n\n    const parent = this.parent\n    if (!parent || parent.level === 0) return\n\n    if (!recursion) {\n      reInitChecked(parent)\n    }\n  }\n\n  getChildren(forceInit = false): TreeNodeData | TreeNodeData[] {\n    // this is data\n    if (this.level === 0) return this.data\n    const data = this.data\n    if (!data) return null\n\n    const props = this.store.props\n    let children = 'children'\n    if (props) {\n      children = props.children || 'children'\n    }\n\n    if (data[children] === undefined) {\n      data[children] = null\n    }\n\n    if (forceInit && !data[children]) {\n      data[children] = []\n    }\n\n    return data[children]\n  }\n\n  updateChildren(): void {\n    const newData = (this.getChildren() || []) as TreeNodeData[]\n    const oldData = this.childNodes.map((node) => node.data)\n\n    const newDataMap = {}\n    const newNodes = []\n\n    newData.forEach((item, index) => {\n      const key = item[NODE_KEY]\n      const isNodeExists =\n        !!key && oldData.findIndex((data) => data[NODE_KEY] === key) >= 0\n      if (isNodeExists) {\n        newDataMap[key] = { index, data: item }\n      } else {\n        newNodes.push({ index, data: item })\n      }\n    })\n\n    if (!this.store.lazy) {\n      oldData.forEach((item) => {\n        if (!newDataMap[item[NODE_KEY]]) this.removeChildByData(item)\n      })\n    }\n\n    newNodes.forEach(({ index, data }) => {\n      this.insertChild({ data }, index)\n    })\n\n    this.updateLeafState()\n  }\n\n  loadData(\n    callback: (node: Node) => void,\n    defaultProps: TreeNodeLoadedDefaultProps = {}\n  ) {\n    if (\n      this.store.lazy === true &&\n      this.store.load &&\n      !this.loaded &&\n      (!this.loading || Object.keys(defaultProps).length)\n    ) {\n      this.loading = true\n\n      const resolve = (children) => {\n        this.childNodes = []\n\n        this.doCreateChildren(children, defaultProps)\n        this.loaded = true\n        this.loading = false\n\n        this.updateLeafState()\n        if (callback) {\n          callback.call(this, children)\n        }\n      }\n      const reject = () => {\n        this.loading = false\n      }\n\n      this.store.load(this, resolve, reject)\n    } else {\n      if (callback) {\n        callback.call(this)\n      }\n    }\n  }\n\n  eachNode(callback: (node: Node) => void) {\n    const arr: Node[] = [this]\n    while (arr.length) {\n      const node = arr.shift()!\n      arr.unshift(...node.childNodes)\n      callback(node)\n    }\n  }\n\n  reInitChecked() {\n    if (this.store.checkStrictly) return\n    reInitChecked(this)\n  }\n}\n\nexport default Node\n"], "mappings": ";;;;;;;;;;;;;;;AAGY,IAACA,aAAa,GAAG,SAAhBA,aAAaA,CAAIC,IAAI,EAAK;EACrC,IAAIC,GAAG,GAAG,IAAI;EACd,IAAIC,IAAI,GAAG,IAAI;EACf,IAAIC,iBAAiB,GAAG,IAAI;EAC5B,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEC,CAAC,GAAGL,IAAI,CAACM,MAAM,EAAEF,CAAC,GAAGC,CAAC,EAAED,CAAC,EAAE,EAAE;IAC3C,IAAMG,CAAC,GAAGP,IAAI,CAACI,CAAC,CAAC;IACjB,IAAIG,CAAC,CAACC,OAAO,KAAK,IAAI,IAAID,CAAC,CAACE,aAAa,EAAE;MACzCR,GAAG,GAAG,KAAK;MACX,IAAI,CAACM,CAAC,CAACG,QAAQ,EAAE;QACfP,iBAAiB,GAAG,KAAK;MACjC;IACA;IACI,IAAII,CAAC,CAACC,OAAO,KAAK,KAAK,IAAID,CAAC,CAACE,aAAa,EAAE;MAC1CP,IAAI,GAAG,KAAK;IAClB;EACA;EACE,OAAO;IAAED,GAAG;IAAEC,IAAI;IAAEC,iBAAiB;IAAEQ,IAAI,EAAE,CAACV,GAAG,IAAI,CAACC;EAAI,CAAE;AAC9D;AACA,IAAMU,cAAa,GAAG,SAAhBA,aAAaA,CAAYZ,IAAI,EAAE;EACnC,IAAIA,IAAI,CAACa,UAAU,CAACP,MAAM,KAAK,CAAC,IAAIN,IAAI,CAACc,OAAO,EAC9C;EACF,IAAAC,cAAA,GAA4BhB,aAAa,CAACC,IAAI,CAACa,UAAU,CAAC;IAAlDZ,GAAG,GAAAc,cAAA,CAAHd,GAAG;IAAEC,IAAI,GAAAa,cAAA,CAAJb,IAAI;IAAES,IAAI,GAAAI,cAAA,CAAJJ,IAAI;EACvB,IAAIV,GAAG,EAAE;IACPD,IAAI,CAACQ,OAAO,GAAG,IAAI;IACnBR,IAAI,CAACS,aAAa,GAAG,KAAK;EAC9B,CAAG,MAAM,IAAIE,IAAI,EAAE;IACfX,IAAI,CAACQ,OAAO,GAAG,KAAK;IACpBR,IAAI,CAACS,aAAa,GAAG,IAAI;EAC7B,CAAG,MAAM,IAAIP,IAAI,EAAE;IACfF,IAAI,CAACQ,OAAO,GAAG,KAAK;IACpBR,IAAI,CAACS,aAAa,GAAG,KAAK;EAC9B;EACE,IAAMO,MAAM,GAAGhB,IAAI,CAACgB,MAAM;EAC1B,IAAI,CAACA,MAAM,IAAIA,MAAM,CAACC,KAAK,KAAK,CAAC,EAC/B;EACF,IAAI,CAACjB,IAAI,CAACkB,KAAK,CAACC,aAAa,EAAE;IAC7BP,cAAa,CAACI,MAAM,CAAC;EACzB;AACA,CAAC;AACD,IAAMI,mBAAmB,GAAG,SAAtBA,mBAAmBA,CAAYpB,IAAI,EAAEqB,IAAI,EAAE;EAC/C,IAAMC,KAAK,GAAGtB,IAAI,CAACkB,KAAK,CAACI,KAAK;EAC9B,IAAMC,IAAI,GAAGvB,IAAI,CAACuB,IAAI,IAAI,EAAE;EAC5B,IAAMC,MAAM,GAAGF,KAAK,CAACD,IAAI,CAAC;EAC1B,IAAI,OAAOG,MAAM,KAAK,UAAU,EAAE;IAChC,OAAOA,MAAM,CAACD,IAAI,EAAEvB,IAAI,CAAC;EAC7B,CAAG,MAAM,IAAI,OAAOwB,MAAM,KAAK,QAAQ,EAAE;IACrC,OAAOD,IAAI,CAACC,MAAM,CAAC;EACvB,CAAG,MAAM,IAAI,OAAOA,MAAM,KAAK,WAAW,EAAE;IACxC,IAAMC,QAAQ,GAAGF,IAAI,CAACF,IAAI,CAAC;IAC3B,OAAOI,QAAQ,KAAK,KAAK,CAAC,GAAG,EAAE,GAAGA,QAAQ;EAC9C;AACA,CAAC;AACD,IAAIC,UAAU,GAAG,CAAC;AAAC,IACbC,IAAI;EACR,SAAAA,KAAYC,OAAO,EAAE;IAAAC,eAAA,OAAAF,IAAA;IACnB,IAAI,CAACG,EAAE,GAAGJ,UAAU,EAAE;IACtB,IAAI,CAACK,IAAI,GAAG,IAAI;IAChB,IAAI,CAACvB,OAAO,GAAG,KAAK;IACpB,IAAI,CAACC,aAAa,GAAG,KAAK;IAC1B,IAAI,CAACc,IAAI,GAAG,IAAI;IAChB,IAAI,CAACS,QAAQ,GAAG,KAAK;IACrB,IAAI,CAAChB,MAAM,GAAG,IAAI;IAClB,IAAI,CAACiB,OAAO,GAAG,IAAI;IACnB,IAAI,CAACC,SAAS,GAAG,KAAK;IACtB,IAAI,CAACC,QAAQ,GAAG,KAAK;IACrB,KAAK,IAAMC,IAAI,IAAIR,OAAO,EAAE;MAC1B,IAAIS,MAAM,CAACT,OAAO,EAAEQ,IAAI,CAAC,EAAE;QACzB,IAAI,CAACA,IAAI,CAAC,GAAGR,OAAO,CAACQ,IAAI,CAAC;MAClC;IACA;IACI,IAAI,CAACnB,KAAK,GAAG,CAAC;IACd,IAAI,CAACqB,MAAM,GAAG,KAAK;IACnB,IAAI,CAACzB,UAAU,GAAG,EAAE;IACpB,IAAI,CAACC,OAAO,GAAG,KAAK;IACpB,IAAI,IAAI,CAACE,MAAM,EAAE;MACf,IAAI,CAACC,KAAK,GAAG,IAAI,CAACD,MAAM,CAACC,KAAK,GAAG,CAAC;IACxC;EACA;EAAG,OAAAsB,YAAA,CAAAZ,IAAA;IAAAa,GAAA;IAAAC,KAAA,EACD,SAAAC,UAAUA,CAAA,EAAG;MACX,IAAMxB,KAAK,GAAG,IAAI,CAACA,KAAK;MACxB,IAAI,CAACA,KAAK,EAAE;QACV,MAAM,IAAIyB,KAAK,CAAC,0BAA0B,CAAC;MACjD;MACIzB,KAAK,CAAC0B,YAAY,CAAC,IAAI,CAAC;MACxB,IAAMtB,KAAK,GAAGJ,KAAK,CAACI,KAAK;MACzB,IAAIA,KAAK,IAAI,OAAOA,KAAK,CAACuB,MAAM,KAAK,WAAW,EAAE;QAChD,IAAMA,MAAM,GAAGzB,mBAAmB,CAAC,IAAI,EAAE,QAAQ,CAAC;QAClD,IAAI,OAAOyB,MAAM,KAAK,SAAS,EAAE;UAC/B,IAAI,CAACC,YAAY,GAAGD,MAAM;QAClC;MACA;MACI,IAAI3B,KAAK,CAAC6B,IAAI,KAAK,IAAI,IAAI,IAAI,CAACxB,IAAI,EAAE;QACpC,IAAI,CAACyB,OAAO,CAAC,IAAI,CAACzB,IAAI,CAAC;QACvB,IAAIL,KAAK,CAAC+B,gBAAgB,EAAE;UAC1B,IAAI,CAACjB,QAAQ,GAAG,IAAI;UACpB,IAAI,CAACG,QAAQ,GAAG,IAAI;QAC5B;MACA,CAAK,MAAM,IAAI,IAAI,CAAClB,KAAK,GAAG,CAAC,IAAIC,KAAK,CAAC6B,IAAI,IAAI7B,KAAK,CAAC+B,gBAAgB,EAAE;QACjE,IAAI,CAACC,MAAM,EAAE;MACnB;MACI,IAAI,CAACC,KAAK,CAACC,OAAO,CAAC,IAAI,CAAC7B,IAAI,CAAC,EAAE;QAC7B8B,YAAY,CAAC,IAAI,EAAE,IAAI,CAAC9B,IAAI,CAAC;MACnC;MACI,IAAI,CAAC,IAAI,CAACA,IAAI,EACZ;MACF,IAAM+B,mBAAmB,GAAGpC,KAAK,CAACoC,mBAAmB;MACrD,IAAMd,GAAG,GAAGtB,KAAK,CAACsB,GAAG;MACrB,IAAIA,GAAG,IAAIc,mBAAmB,IAAIA,mBAAmB,CAACC,QAAQ,CAAC,IAAI,CAACf,GAAG,CAAC,EAAE;QACxE,IAAI,CAACU,MAAM,CAAC,IAAI,EAAEhC,KAAK,CAACsC,gBAAgB,CAAC;MAC/C;MACI,IAAIhB,GAAG,IAAItB,KAAK,CAACuC,cAAc,KAAK,KAAK,CAAC,IAAI,IAAI,CAACjB,GAAG,KAAKtB,KAAK,CAACuC,cAAc,EAAE;QAC/EvC,KAAK,CAACwC,WAAW,GAAG,IAAI;QACxBxC,KAAK,CAACwC,WAAW,CAACxB,SAAS,GAAG,IAAI;MACxC;MACI,IAAIhB,KAAK,CAAC6B,IAAI,EAAE;QACd7B,KAAK,CAACyC,uBAAuB,CAAC,IAAI,CAAC;MACzC;MACI,IAAI,CAACC,eAAe,EAAE;MACtB,IAAI,IAAI,CAAC5C,MAAM,KAAK,IAAI,CAACC,KAAK,KAAK,CAAC,IAAI,IAAI,CAACD,MAAM,CAACgB,QAAQ,KAAK,IAAI,CAAC,EACpE,IAAI,CAACG,QAAQ,GAAG,IAAI;IAC1B;EAAG;IAAAK,GAAA;IAAAC,KAAA,EACD,SAAAO,OAAOA,CAACzB,IAAI,EAAE;MACZ,IAAI,CAAC4B,KAAK,CAACC,OAAO,CAAC7B,IAAI,CAAC,EAAE;QACxB8B,YAAY,CAAC,IAAI,EAAE9B,IAAI,CAAC;MAC9B;MACI,IAAI,CAACA,IAAI,GAAGA,IAAI;MAChB,IAAI,CAACV,UAAU,GAAG,EAAE;MACpB,IAAIgD,QAAQ;MACZ,IAAI,IAAI,CAAC5C,KAAK,KAAK,CAAC,IAAIkC,KAAK,CAACC,OAAO,CAAC,IAAI,CAAC7B,IAAI,CAAC,EAAE;QAChDsC,QAAQ,GAAG,IAAI,CAACtC,IAAI;MAC1B,CAAK,MAAM;QACLsC,QAAQ,GAAGzC,mBAAmB,CAAC,IAAI,EAAE,UAAU,CAAC,IAAI,EAAE;MAC5D;MACI,KAAK,IAAIhB,CAAC,GAAG,CAAC,EAAEC,CAAC,GAAGwD,QAAQ,CAACvD,MAAM,EAAEF,CAAC,GAAGC,CAAC,EAAED,CAAC,EAAE,EAAE;QAC/C,IAAI,CAAC0D,WAAW,CAAC;UAAEvC,IAAI,EAAEsC,QAAQ,CAACzD,CAAC;QAAC,CAAE,CAAC;MAC7C;IACA;EAAG;IAAAoC,GAAA;IAAAuB,GAAA,EACD,SAAAA,IAAA,EAAY;MACV,OAAO3C,mBAAmB,CAAC,IAAI,EAAE,OAAO,CAAC;IAC7C;EAAG;IAAAoB,GAAA;IAAAuB,GAAA,EACD,SAAAA,IAAA,EAAU;MACR,IAAMC,OAAO,GAAG,IAAI,CAAC9C,KAAK,CAACsB,GAAG;MAC9B,IAAI,IAAI,CAACjB,IAAI,EACX,OAAO,IAAI,CAACA,IAAI,CAACyC,OAAO,CAAC;MAC3B,OAAO,IAAI;IACf;EAAG;IAAAxB,GAAA;IAAAuB,GAAA,EACD,SAAAA,IAAA,EAAe;MACb,OAAO3C,mBAAmB,CAAC,IAAI,EAAE,UAAU,CAAC;IAChD;EAAG;IAAAoB,GAAA;IAAAuB,GAAA,EACD,SAAAA,IAAA,EAAkB;MAChB,IAAM/C,MAAM,GAAG,IAAI,CAACA,MAAM;MAC1B,IAAIA,MAAM,EAAE;QACV,IAAMiD,KAAK,GAAGjD,MAAM,CAACH,UAAU,CAACqD,OAAO,CAAC,IAAI,CAAC;QAC7C,IAAID,KAAK,GAAG,CAAC,CAAC,EAAE;UACd,OAAOjD,MAAM,CAACH,UAAU,CAACoD,KAAK,GAAG,CAAC,CAAC;QAC3C;MACA;MACI,OAAO,IAAI;IACf;EAAG;IAAAzB,GAAA;IAAAuB,GAAA,EACD,SAAAA,IAAA,EAAsB;MACpB,IAAM/C,MAAM,GAAG,IAAI,CAACA,MAAM;MAC1B,IAAIA,MAAM,EAAE;QACV,IAAMiD,KAAK,GAAGjD,MAAM,CAACH,UAAU,CAACqD,OAAO,CAAC,IAAI,CAAC;QAC7C,IAAID,KAAK,GAAG,CAAC,CAAC,EAAE;UACd,OAAOA,KAAK,GAAG,CAAC,GAAGjD,MAAM,CAACH,UAAU,CAACoD,KAAK,GAAG,CAAC,CAAC,GAAG,IAAI;QAC9D;MACA;MACI,OAAO,IAAI;IACf;EAAG;IAAAzB,GAAA;IAAAC,KAAA,EACD,SAAA0B,QAAQA,CAACC,MAAM,EAAe;MAAA,IAAbC,IAAI,GAAAC,SAAA,CAAAhE,MAAA,QAAAgE,SAAA,QAAAC,SAAA,GAAAD,SAAA,MAAG,IAAI;MAC1B,OAAO,CAAC,IAAI,CAACzD,UAAU,IAAI,EAAE,EAAE2D,IAAI,CAAC,UAACC,KAAK;QAAA,OAAKA,KAAK,KAAKL,MAAM,IAAIC,IAAI,IAAII,KAAK,CAACN,QAAQ,CAACC,MAAM,CAAC;MAAA,EAAC;IACtG;EAAG;IAAA5B,GAAA;IAAAC,KAAA,EACD,SAAAiC,MAAMA,CAAA,EAAG;MACP,IAAM1D,MAAM,GAAG,IAAI,CAACA,MAAM;MAC1B,IAAIA,MAAM,EAAE;QACVA,MAAM,CAAC2D,WAAW,CAAC,IAAI,CAAC;MAC9B;IACA;EAAG;IAAAnC,GAAA;IAAAC,KAAA,EACD,SAAAqB,WAAWA,CAACW,KAAK,EAAER,KAAK,EAAEW,KAAK,EAAE;MAC/B,IAAI,CAACH,KAAK,EACR,MAAM,IAAI9B,KAAK,CAAC,uCAAuC,CAAC;MAC1D,IAAI,EAAE8B,KAAK,YAAY9C,IAAI,CAAC,EAAE;QAC5B,IAAI,CAACiD,KAAK,EAAE;UACV,IAAMf,QAAQ,GAAG,IAAI,CAACgB,WAAW,CAAC,IAAI,CAAC;UACvC,IAAI,CAAChB,QAAQ,CAACN,QAAQ,CAACkB,KAAK,CAAClD,IAAI,CAAC,EAAE;YAClC,IAAI,OAAO0C,KAAK,KAAK,WAAW,IAAIA,KAAK,GAAG,CAAC,EAAE;cAC7CJ,QAAQ,CAACiB,IAAI,CAACL,KAAK,CAAClD,IAAI,CAAC;YACrC,CAAW,MAAM;cACLsC,QAAQ,CAACkB,MAAM,CAACd,KAAK,EAAE,CAAC,EAAEQ,KAAK,CAAClD,IAAI,CAAC;YACjD;UACA;QACA;QACMyD,MAAM,CAACC,MAAM,CAACR,KAAK,EAAE;UACnBzD,MAAM,EAAE,IAAI;UACZE,KAAK,EAAE,IAAI,CAACA;QACpB,CAAO,CAAC;QACFuD,KAAK,GAAGS,QAAQ,CAAC,IAAIvD,IAAI,CAAC8C,KAAK,CAAC,CAAC;QACjC,IAAIA,KAAK,YAAY9C,IAAI,EAAE;UACzB8C,KAAK,CAAC/B,UAAU,EAAE;QAC1B;MACA;MACI;MACA+B,KAAK,CAACxD,KAAK,GAAG,IAAI,CAACA,KAAK,GAAG,CAAC;MAC5B,IAAI,OAAOgD,KAAK,KAAK,WAAW,IAAIA,KAAK,GAAG,CAAC,EAAE;QAC7C,IAAI,CAACpD,UAAU,CAACiE,IAAI,CAACL,KAAK,CAAC;MACjC,CAAK,MAAM;QACL,IAAI,CAAC5D,UAAU,CAACkE,MAAM,CAACd,KAAK,EAAE,CAAC,EAAEQ,KAAK,CAAC;MAC7C;MACI,IAAI,CAACb,eAAe,EAAE;IAC1B;EAAG;IAAApB,GAAA;IAAAC,KAAA,EACD,SAAA0C,YAAYA,CAACV,KAAK,EAAEW,GAAG,EAAE;MACvB,IAAInB,KAAK;MACT,IAAImB,GAAG,EAAE;QACPnB,KAAK,GAAG,IAAI,CAACpD,UAAU,CAACqD,OAAO,CAACkB,GAAG,CAAC;MAC1C;MACI,IAAI,CAACtB,WAAW,CAACW,KAAK,EAAER,KAAK,CAAC;IAClC;EAAG;IAAAzB,GAAA;IAAAC,KAAA,EACD,SAAA4C,WAAWA,CAACZ,KAAK,EAAEW,GAAG,EAAE;MACtB,IAAInB,KAAK;MACT,IAAImB,GAAG,EAAE;QACPnB,KAAK,GAAG,IAAI,CAACpD,UAAU,CAACqD,OAAO,CAACkB,GAAG,CAAC;QACpC,IAAInB,KAAK,KAAK,CAAC,CAAC,EACdA,KAAK,IAAI,CAAC;MAClB;MACI,IAAI,CAACH,WAAW,CAACW,KAAK,EAAER,KAAK,CAAC;IAClC;EAAG;IAAAzB,GAAA;IAAAC,KAAA,EACD,SAAAkC,WAAWA,CAACF,KAAK,EAAE;MACjB,IAAMZ,QAAQ,GAAG,IAAI,CAACgB,WAAW,EAAE,IAAI,EAAE;MACzC,IAAMS,SAAS,GAAGzB,QAAQ,CAACK,OAAO,CAACO,KAAK,CAAClD,IAAI,CAAC;MAC9C,IAAI+D,SAAS,GAAG,CAAC,CAAC,EAAE;QAClBzB,QAAQ,CAACkB,MAAM,CAACO,SAAS,EAAE,CAAC,CAAC;MACnC;MACI,IAAMrB,KAAK,GAAG,IAAI,CAACpD,UAAU,CAACqD,OAAO,CAACO,KAAK,CAAC;MAC5C,IAAIR,KAAK,GAAG,CAAC,CAAC,EAAE;QACd,IAAI,CAAC/C,KAAK,IAAI,IAAI,CAACA,KAAK,CAACqE,cAAc,CAACd,KAAK,CAAC;QAC9CA,KAAK,CAACzD,MAAM,GAAG,IAAI;QACnB,IAAI,CAACH,UAAU,CAACkE,MAAM,CAACd,KAAK,EAAE,CAAC,CAAC;MACtC;MACI,IAAI,CAACL,eAAe,EAAE;IAC1B;EAAG;IAAApB,GAAA;IAAAC,KAAA,EACD,SAAA+C,iBAAiBA,CAACjE,IAAI,EAAE;MACtB,IAAIkE,UAAU,GAAG,IAAI;MACrB,KAAK,IAAIrF,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACS,UAAU,CAACP,MAAM,EAAEF,CAAC,EAAE,EAAE;QAC/C,IAAI,IAAI,CAACS,UAAU,CAACT,CAAC,CAAC,CAACmB,IAAI,KAAKA,IAAI,EAAE;UACpCkE,UAAU,GAAG,IAAI,CAAC5E,UAAU,CAACT,CAAC,CAAC;UAC/B;QACR;MACA;MACI,IAAIqF,UAAU,EAAE;QACd,IAAI,CAACd,WAAW,CAACc,UAAU,CAAC;MAClC;IACA;EAAG;IAAAjD,GAAA;IAAAC,KAAA,EACD,SAAAS,MAAMA,CAACwC,QAAQ,EAAEC,YAAY,EAAE;MAAA,IAAAC,KAAA;MAC7B,IAAMC,IAAI,GAAG,SAAPA,IAAIA,CAAA,EAAS;QACjB,IAAIF,YAAY,EAAE;UAChB,IAAI3E,MAAM,GAAG4E,KAAI,CAAC5E,MAAM;UACxB,OAAOA,MAAM,CAACC,KAAK,GAAG,CAAC,EAAE;YACvBD,MAAM,CAACgB,QAAQ,GAAG,IAAI;YACtBhB,MAAM,GAAGA,MAAM,CAACA,MAAM;UAChC;QACA;QACM4E,KAAI,CAAC5D,QAAQ,GAAG,IAAI;QACpB,IAAI0D,QAAQ,EACVA,QAAQ,EAAE;QACZE,KAAI,CAAC/E,UAAU,CAACiF,OAAO,CAAC,UAACC,IAAI,EAAK;UAChCA,IAAI,CAAC5D,QAAQ,GAAG,IAAI;QAC5B,CAAO,CAAC;MACR,CAAK;MACD,IAAI,IAAI,CAAC6D,cAAc,EAAE,EAAE;QACzB,IAAI,CAACC,QAAQ,CAAC,UAAC1E,IAAI,EAAK;UACtB,IAAI4B,KAAK,CAACC,OAAO,CAAC7B,IAAI,CAAC,EAAE;YACvB,IAAIqE,KAAI,CAACpF,OAAO,EAAE;cAChBoF,KAAI,CAACM,UAAU,CAAC,IAAI,EAAE,IAAI,CAAC;YACvC,CAAW,MAAM,IAAI,CAACN,KAAI,CAAC1E,KAAK,CAACC,aAAa,EAAE;cACpCP,cAAa,CAACgF,KAAI,CAAC;YAC/B;YACUC,IAAI,EAAE;UAChB;QACA,CAAO,CAAC;MACR,CAAK,MAAM;QACLA,IAAI,EAAE;MACZ;IACA;EAAG;IAAArD,GAAA;IAAAC,KAAA,EACD,SAAA0D,gBAAgBA,CAACC,KAAK,EAAqB;MAAA,IAAAC,MAAA;MAAA,IAAnBC,YAAY,GAAAhC,SAAA,CAAAhE,MAAA,QAAAgE,SAAA,QAAAC,SAAA,GAAAD,SAAA,MAAG,EAAE;MACvC8B,KAAK,CAACN,OAAO,CAAC,UAACC,IAAI,EAAK;QACtBM,MAAI,CAACvC,WAAW,CAACkB,MAAM,CAACC,MAAM,CAAC;UAAE1D,IAAI,EAAEwE;QAAI,CAAE,EAAEO,YAAY,CAAC,EAAE,KAAK,CAAC,EAAE,IAAI,CAAC;MACjF,CAAK,CAAC;IACN;EAAG;IAAA9D,GAAA;IAAAC,KAAA,EACD,SAAA8D,QAAQA,CAAA,EAAG;MACT,IAAI,CAACvE,QAAQ,GAAG,KAAK;MACrB,IAAI,CAACnB,UAAU,CAACiF,OAAO,CAAC,UAACC,IAAI,EAAK;QAChCA,IAAI,CAAC5D,QAAQ,GAAG,KAAK;MAC3B,CAAK,CAAC;IACN;EAAG;IAAAK,GAAA;IAAAC,KAAA,EACD,SAAAuD,cAAcA,CAAA,EAAG;MACf,OAAO,IAAI,CAAC9E,KAAK,CAAC6B,IAAI,KAAK,IAAI,IAAI,IAAI,CAAC7B,KAAK,CAACsF,IAAI,IAAI,CAAC,IAAI,CAAClE,MAAM;IACtE;EAAG;IAAAE,GAAA;IAAAC,KAAA,EACD,SAAAmB,eAAeA,CAAA,EAAG;MAChB,IAAI,IAAI,CAAC1C,KAAK,CAAC6B,IAAI,KAAK,IAAI,IAAI,IAAI,CAACT,MAAM,KAAK,IAAI,IAAI,OAAO,IAAI,CAACQ,YAAY,KAAK,WAAW,EAAE;QAChG,IAAI,CAACD,MAAM,GAAG,IAAI,CAACC,YAAY;QAC/B;MACN;MACI,IAAMjC,UAAU,GAAG,IAAI,CAACA,UAAU;MAClC,IAAI,CAAC,IAAI,CAACK,KAAK,CAAC6B,IAAI,IAAI,IAAI,CAAC7B,KAAK,CAAC6B,IAAI,KAAK,IAAI,IAAI,IAAI,CAACT,MAAM,KAAK,IAAI,EAAE;QACxE,IAAI,CAACO,MAAM,GAAG,CAAChC,UAAU,IAAIA,UAAU,CAACP,MAAM,KAAK,CAAC;QACpD;MACN;MACI,IAAI,CAACuC,MAAM,GAAG,KAAK;IACvB;EAAG;IAAAL,GAAA;IAAAC,KAAA,EACD,SAAAyD,UAAUA,CAACzD,KAAK,EAAE4B,IAAI,EAAEoC,SAAS,EAAEC,SAAS,EAAE;MAAA,IAAAC,MAAA;MAC5C,IAAI,CAAClG,aAAa,GAAGgC,KAAK,KAAK,MAAM;MACrC,IAAI,CAACjC,OAAO,GAAGiC,KAAK,KAAK,IAAI;MAC7B,IAAI,IAAI,CAACvB,KAAK,CAACC,aAAa,EAC1B;MACF,IAAI,EAAE,IAAI,CAAC6E,cAAc,EAAE,IAAI,CAAC,IAAI,CAAC9E,KAAK,CAAC0F,gBAAgB,CAAC,EAAE;QAC5D,IAAAC,eAAA,GAAmC9G,aAAa,CAAC,IAAI,CAACc,UAAU,CAAC;UAAzDZ,GAAG,GAAA4G,eAAA,CAAH5G,GAAG;UAAEE,iBAAiB,GAAA0G,eAAA,CAAjB1G,iBAAiB;QAC9B,IAAI,CAAC,IAAI,CAAC0C,MAAM,IAAI,CAAC5C,GAAG,IAAIE,iBAAiB,EAAE;UAC7C,IAAI,CAACK,OAAO,GAAG,KAAK;UACpBiC,KAAK,GAAG,KAAK;QACrB;QACM,IAAMqE,iBAAiB,GAAG,SAApBA,iBAAiBA,CAAA,EAAS;UAC9B,IAAIzC,IAAI,EAAE;YACR,IAAMxD,UAAU,GAAG8F,MAAI,CAAC9F,UAAU;YAClC,KAAK,IAAIT,CAAC,GAAG,CAAC,EAAEC,CAAC,GAAGQ,UAAU,CAACP,MAAM,EAAEF,CAAC,GAAGC,CAAC,EAAED,CAAC,EAAE,EAAE;cACjD,IAAMqE,KAAK,GAAG5D,UAAU,CAACT,CAAC,CAAC;cAC3BsG,SAAS,GAAGA,SAAS,IAAIjE,KAAK,KAAK,KAAK;cACxC,IAAMsE,OAAO,GAAGtC,KAAK,CAAC/D,QAAQ,GAAG+D,KAAK,CAACjE,OAAO,GAAGkG,SAAS;cAC1DjC,KAAK,CAACyB,UAAU,CAACa,OAAO,EAAE1C,IAAI,EAAE,IAAI,EAAEqC,SAAS,CAAC;YAC5D;YACU,IAAAM,eAAA,GAA4BjH,aAAa,CAACc,UAAU,CAAC;cAA7CF,IAAI,GAAAqG,eAAA,CAAJrG,IAAI;cAAOsG,IAAI,GAAAD,eAAA,CAAT/G,GAAG;YACjB,IAAI,CAACgH,IAAI,EAAE;cACTN,MAAI,CAACnG,OAAO,GAAGyG,IAAI;cACnBN,MAAI,CAAClG,aAAa,GAAGE,IAAI;YACrC;UACA;QACA,CAAO;QACD,IAAI,IAAI,CAACqF,cAAc,EAAE,EAAE;UACzB,IAAI,CAACC,QAAQ,CAAC,YAAM;YAClBa,iBAAiB,EAAE;YACnBlG,cAAa,CAAC+F,MAAI,CAAC;UAC7B,CAAS,EAAE;YACDnG,OAAO,EAAEiC,KAAK,KAAK;UAC7B,CAAS,CAAC;UACF;QACR,CAAO,MAAM;UACLqE,iBAAiB,EAAE;QAC3B;MACA;MACI,IAAM9F,MAAM,GAAG,IAAI,CAACA,MAAM;MAC1B,IAAI,CAACA,MAAM,IAAIA,MAAM,CAACC,KAAK,KAAK,CAAC,EAC/B;MACF,IAAI,CAACwF,SAAS,EAAE;QACd7F,cAAa,CAACI,MAAM,CAAC;MAC3B;IACA;EAAG;IAAAwB,GAAA;IAAAC,KAAA,EACD,SAAAoC,WAAWA,CAAA,EAAoB;MAAA,IAAnBqC,SAAS,GAAA5C,SAAA,CAAAhE,MAAA,QAAAgE,SAAA,QAAAC,SAAA,GAAAD,SAAA,MAAG,KAAK;MAC3B,IAAI,IAAI,CAACrD,KAAK,KAAK,CAAC,EAClB,OAAO,IAAI,CAACM,IAAI;MAClB,IAAMA,IAAI,GAAG,IAAI,CAACA,IAAI;MACtB,IAAI,CAACA,IAAI,EACP,OAAO,IAAI;MACb,IAAMD,KAAK,GAAG,IAAI,CAACJ,KAAK,CAACI,KAAK;MAC9B,IAAIuC,QAAQ,GAAG,UAAU;MACzB,IAAIvC,KAAK,EAAE;QACTuC,QAAQ,GAAGvC,KAAK,CAACuC,QAAQ,IAAI,UAAU;MAC7C;MACI,IAAItC,IAAI,CAACsC,QAAQ,CAAC,KAAK,KAAK,CAAC,EAAE;QAC7BtC,IAAI,CAACsC,QAAQ,CAAC,GAAG,IAAI;MAC3B;MACI,IAAIqD,SAAS,IAAI,CAAC3F,IAAI,CAACsC,QAAQ,CAAC,EAAE;QAChCtC,IAAI,CAACsC,QAAQ,CAAC,GAAG,EAAE;MACzB;MACI,OAAOtC,IAAI,CAACsC,QAAQ,CAAC;IACzB;EAAG;IAAArB,GAAA;IAAAC,KAAA,EACD,SAAA0E,cAAcA,CAAA,EAAG;MAAA,IAAAC,MAAA;MACf,IAAMC,OAAO,GAAG,IAAI,CAACxC,WAAW,EAAE,IAAI,EAAE;MACxC,IAAMyC,OAAO,GAAG,IAAI,CAACzG,UAAU,CAAC0G,GAAG,CAAC,UAACvH,IAAI;QAAA,OAAKA,IAAI,CAACuB,IAAI;MAAA,EAAC;MACxD,IAAMiG,UAAU,GAAG,EAAE;MACrB,IAAMC,QAAQ,GAAG,EAAE;MACnBJ,OAAO,CAACvB,OAAO,CAAC,UAACC,IAAI,EAAE9B,KAAK,EAAK;QAC/B,IAAMzB,GAAG,GAAGuD,IAAI,CAAC2B,QAAQ,CAAC;QAC1B,IAAMC,YAAY,GAAG,CAAC,CAACnF,GAAG,IAAI8E,OAAO,CAACM,SAAS,CAAC,UAACrG,IAAI;UAAA,OAAKA,IAAI,CAACmG,QAAQ,CAAC,KAAKlF,GAAG;QAAA,EAAC,IAAI,CAAC;QACtF,IAAImF,YAAY,EAAE;UAChBH,UAAU,CAAChF,GAAG,CAAC,GAAG;YAAEyB,KAAK;YAAE1C,IAAI,EAAEwE;UAAI,CAAE;QAC/C,CAAO,MAAM;UACL0B,QAAQ,CAAC3C,IAAI,CAAC;YAAEb,KAAK;YAAE1C,IAAI,EAAEwE;UAAI,CAAE,CAAC;QAC5C;MACA,CAAK,CAAC;MACF,IAAI,CAAC,IAAI,CAAC7E,KAAK,CAAC6B,IAAI,EAAE;QACpBuE,OAAO,CAACxB,OAAO,CAAC,UAACC,IAAI,EAAK;UACxB,IAAI,CAACyB,UAAU,CAACzB,IAAI,CAAC2B,QAAQ,CAAC,CAAC,EAC7BN,MAAI,CAAC5B,iBAAiB,CAACO,IAAI,CAAC;QACtC,CAAO,CAAC;MACR;MACI0B,QAAQ,CAAC3B,OAAO,CAAC,UAAA+B,IAAA,EAAqB;QAAA,IAAlB5D,KAAK,GAAA4D,IAAA,CAAL5D,KAAK;UAAE1C,IAAI,GAAAsG,IAAA,CAAJtG,IAAI;QAC7B6F,MAAI,CAACtD,WAAW,CAAC;UAAEvC;QAAI,CAAE,EAAE0C,KAAK,CAAC;MACvC,CAAK,CAAC;MACF,IAAI,CAACL,eAAe,EAAE;IAC1B;EAAG;IAAApB,GAAA;IAAAC,KAAA,EACD,SAAAwD,QAAQA,CAACP,QAAQ,EAAqB;MAAA,IAAAoC,MAAA;MAAA,IAAnBxB,YAAY,GAAAhC,SAAA,CAAAhE,MAAA,QAAAgE,SAAA,QAAAC,SAAA,GAAAD,SAAA,MAAG,EAAE;MAClC,IAAI,IAAI,CAACpD,KAAK,CAAC6B,IAAI,KAAK,IAAI,IAAI,IAAI,CAAC7B,KAAK,CAACsF,IAAI,IAAI,CAAC,IAAI,CAAClE,MAAM,KAAK,CAAC,IAAI,CAACxB,OAAO,IAAIkE,MAAM,CAAC+C,IAAI,CAACzB,YAAY,CAAC,CAAChG,MAAM,CAAC,EAAE;QACtH,IAAI,CAACQ,OAAO,GAAG,IAAI;QACnB,IAAMkH,OAAO,GAAG,SAAVA,OAAOA,CAAInE,QAAQ,EAAK;UAC5BiE,MAAI,CAACjH,UAAU,GAAG,EAAE;UACpBiH,MAAI,CAAC3B,gBAAgB,CAACtC,QAAQ,EAAEyC,YAAY,CAAC;UAC7CwB,MAAI,CAACxF,MAAM,GAAG,IAAI;UAClBwF,MAAI,CAAChH,OAAO,GAAG,KAAK;UACpBgH,MAAI,CAAClE,eAAe,EAAE;UACtB,IAAI8B,QAAQ,EAAE;YACZA,QAAQ,CAACuC,IAAI,CAACH,MAAI,EAAEjE,QAAQ,CAAC;UACvC;QACA,CAAO;QACD,IAAMqE,MAAM,GAAG,SAATA,MAAMA,CAAA,EAAS;UACnBJ,MAAI,CAAChH,OAAO,GAAG,KAAK;QAC5B,CAAO;QACD,IAAI,CAACI,KAAK,CAACsF,IAAI,CAAC,IAAI,EAAEwB,OAAO,EAAEE,MAAM,CAAC;MAC5C,CAAK,MAAM;QACL,IAAIxC,QAAQ,EAAE;UACZA,QAAQ,CAACuC,IAAI,CAAC,IAAI,CAAC;QAC3B;MACA;IACA;EAAG;IAAAzF,GAAA;IAAAC,KAAA,EACD,SAAA0F,QAAQA,CAACzC,QAAQ,EAAE;MACjB,IAAM0C,GAAG,GAAG,CAAC,IAAI,CAAC;MAClB,OAAOA,GAAG,CAAC9H,MAAM,EAAE;QACjB,IAAMN,IAAI,GAAGoI,GAAG,CAACC,KAAK,EAAE;QACxBD,GAAG,CAACE,OAAO,CAAAC,KAAA,CAAXH,GAAG,EAAAI,kBAAA,CAAYxI,IAAI,CAACa,UAAU,EAAC;QAC/B6E,QAAQ,CAAC1F,IAAI,CAAC;MACpB;IACA;EAAG;IAAAwC,GAAA;IAAAC,KAAA,EACD,SAAA7B,aAAaA,CAAA,EAAG;MACd,IAAI,IAAI,CAACM,KAAK,CAACC,aAAa,EAC1B;MACFP,cAAa,CAAC,IAAI,CAAC;IACvB;EAAG;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "import { createCommentVNode as _createCommentVNode, toDisplayString as _toDisplayString, createElementVNode as _createElementVNode, resolveComponent as _resolveComponent, createVNode as _createVNode, createTextVNode as _createTextVNode, withCtx as _withCtx, openBlock as _openBlock, createBlock as _createBlock } from \"vue\";\nvar _hoisted_1 = {\n  class: \"share-content\"\n};\nvar _hoisted_2 = {\n  class: \"live-info\"\n};\nvar _hoisted_3 = {\n  class: \"live-title\"\n};\nvar _hoisted_4 = {\n  class: \"live-desc\"\n};\nvar _hoisted_5 = {\n  class: \"share-link-section\"\n};\nvar _hoisted_6 = {\n  class: \"link-input-group\"\n};\nvar _hoisted_7 = {\n  class: \"qr-section\"\n};\nvar _hoisted_8 = {\n  class: \"qr-code\",\n  ref: \"qrCodeRef\"\n};\nvar _hoisted_9 = {\n  class: \"qr-placeholder\"\n};\nvar _hoisted_10 = {\n  class: \"qr-text\"\n};\nvar _hoisted_11 = {\n  class: \"dialog-footer\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  var _component_el_input = _resolveComponent(\"el-input\");\n  var _component_el_button = _resolveComponent(\"el-button\");\n  var _component_el_dialog = _resolveComponent(\"el-dialog\");\n  return _openBlock(), _createBlock(_component_el_dialog, {\n    modelValue: $setup.visible,\n    \"onUpdate:modelValue\": _cache[1] || (_cache[1] = function ($event) {\n      return $setup.visible = $event;\n    }),\n    title: \"分享直播\",\n    width: \"500px\",\n    \"before-close\": $setup.handleClose,\n    class: \"share-dialog\"\n  }, {\n    footer: _withCtx(function () {\n      return [_createElementVNode(\"div\", _hoisted_11, [_createVNode(_component_el_button, {\n        onClick: $setup.handleClose\n      }, {\n        default: _withCtx(function () {\n          return _cache[9] || (_cache[9] = [_createTextVNode(\"关闭\")]);\n        }),\n        _: 1 /* STABLE */\n      })])];\n    }),\n    default: _withCtx(function () {\n      return [_createElementVNode(\"div\", _hoisted_1, [_createCommentVNode(\" 直播信息 \"), _createElementVNode(\"div\", _hoisted_2, [_createElementVNode(\"div\", _hoisted_3, _toDisplayString($setup.shareInfo.title), 1 /* TEXT */), _createElementVNode(\"div\", _hoisted_4, _toDisplayString($setup.shareInfo.description), 1 /* TEXT */)]), _createCommentVNode(\" 分享链接 \"), _createElementVNode(\"div\", _hoisted_5, [_cache[2] || (_cache[2] = _createElementVNode(\"div\", {\n        class: \"section-title\"\n      }, \"分享链接\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_6, [_createVNode(_component_el_input, {\n        modelValue: $setup.shareUrl,\n        \"onUpdate:modelValue\": _cache[0] || (_cache[0] = function ($event) {\n          return $setup.shareUrl = $event;\n        }),\n        readonly: \"\",\n        class: \"share-input\",\n        placeholder: \"生成分享链接中...\"\n      }, null, 8 /* PROPS */, [\"modelValue\"]), _createVNode(_component_el_button, {\n        type: \"primary\",\n        onClick: $setup.copyLink,\n        loading: $setup.copying\n      }, {\n        default: _withCtx(function () {\n          return [_createTextVNode(_toDisplayString($setup.copying ? '复制中' : '复制链接'), 1 /* TEXT */)];\n        }),\n        _: 1 /* STABLE */\n      }, 8 /* PROPS */, [\"loading\"])])]), _createCommentVNode(\" 分享方式 \"), _createElementVNode(\"div\", {\n        class: \"share-methods\"\n      }, [_cache[6] || (_cache[6] = _createElementVNode(\"div\", {\n        class: \"section-title\"\n      }, \"分享到\", -1 /* HOISTED */)), _createElementVNode(\"div\", {\n        class: \"share-buttons\"\n      }, [_createElementVNode(\"div\", {\n        class: \"share-btn\",\n        onClick: $setup.shareToWeChat\n      }, _cache[3] || (_cache[3] = [_createElementVNode(\"div\", {\n        class: \"share-icon wechat-icon\"\n      }, \"微\", -1 /* HOISTED */), _createElementVNode(\"span\", null, \"微信\", -1 /* HOISTED */)])), _createElementVNode(\"div\", {\n        class: \"share-btn\",\n        onClick: $setup.shareToQQ\n      }, _cache[4] || (_cache[4] = [_createElementVNode(\"div\", {\n        class: \"share-icon qq-icon\"\n      }, \"Q\", -1 /* HOISTED */), _createElementVNode(\"span\", null, \"QQ\", -1 /* HOISTED */)])), _createElementVNode(\"div\", {\n        class: \"share-btn\",\n        onClick: $setup.shareToWeibo\n      }, _cache[5] || (_cache[5] = [_createElementVNode(\"div\", {\n        class: \"share-icon weibo-icon\"\n      }, \"微\", -1 /* HOISTED */), _createElementVNode(\"span\", null, \"微博\", -1 /* HOISTED */)]))])]), _createCommentVNode(\" 二维码分享 \"), _createElementVNode(\"div\", _hoisted_7, [_cache[8] || (_cache[8] = _createElementVNode(\"div\", {\n        class: \"section-title\"\n      }, \"扫码观看\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_8, [_createCommentVNode(\" 这里可以集成二维码生成库 \"), _createElementVNode(\"div\", _hoisted_9, [_cache[7] || (_cache[7] = _createTextVNode(\" 二维码 \")), _createElementVNode(\"div\", _hoisted_10, _toDisplayString($setup.shareUrl), 1 /* TEXT */)])], 512 /* NEED_PATCH */)])])];\n    }),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"modelValue\"]);\n}", "map": {"version": 3, "names": ["class", "ref", "_createBlock", "_component_el_dialog", "modelValue", "$setup", "visible", "_cache", "$event", "title", "width", "handleClose", "footer", "_withCtx", "_createElementVNode", "_hoisted_11", "_createVNode", "_component_el_button", "onClick", "default", "_createTextVNode", "_", "_hoisted_1", "_createCommentVNode", "_hoisted_2", "_hoisted_3", "_toDisplayString", "shareInfo", "_hoisted_4", "description", "_hoisted_5", "_hoisted_6", "_component_el_input", "shareUrl", "readonly", "placeholder", "type", "copyLink", "loading", "copying", "shareToWeChat", "shareToQQ", "shareToWeibo", "_hoisted_7", "_hoisted_8", "_hoisted_9", "_hoisted_10"], "sources": ["D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\interaction\\src\\views\\LiveManagement\\components\\ShareDialog.vue"], "sourcesContent": ["<template>\n  <el-dialog\n    v-model=\"visible\"\n    title=\"分享直播\"\n    width=\"500px\"\n    :before-close=\"handleClose\"\n    class=\"share-dialog\"\n  >\n    <div class=\"share-content\">\n      <!-- 直播信息 -->\n      <div class=\"live-info\">\n        <div class=\"live-title\">{{ shareInfo.title }}</div>\n        <div class=\"live-desc\">{{ shareInfo.description }}</div>\n      </div>\n\n      <!-- 分享链接 -->\n      <div class=\"share-link-section\">\n        <div class=\"section-title\">分享链接</div>\n        <div class=\"link-input-group\">\n          <el-input\n            v-model=\"shareUrl\"\n            readonly\n            class=\"share-input\"\n            placeholder=\"生成分享链接中...\"\n          />\n          <el-button type=\"primary\" @click=\"copyLink\" :loading=\"copying\">\n            {{ copying ? '复制中' : '复制链接' }}\n          </el-button>\n        </div>\n      </div>\n\n      <!-- 分享方式 -->\n      <div class=\"share-methods\">\n        <div class=\"section-title\">分享到</div>\n        <div class=\"share-buttons\">\n          <div class=\"share-btn\" @click=\"shareToWeChat\">\n            <div class=\"share-icon wechat-icon\">微</div>\n            <span>微信</span>\n          </div>\n          <div class=\"share-btn\" @click=\"shareToQQ\">\n            <div class=\"share-icon qq-icon\">Q</div>\n            <span>QQ</span>\n          </div>\n          <div class=\"share-btn\" @click=\"shareToWeibo\">\n            <div class=\"share-icon weibo-icon\">微</div>\n            <span>微博</span>\n          </div>\n        </div>\n      </div>\n\n      <!-- 二维码分享 -->\n      <div class=\"qr-section\">\n        <div class=\"section-title\">扫码观看</div>\n        <div class=\"qr-code\" ref=\"qrCodeRef\">\n          <!-- 这里可以集成二维码生成库 -->\n          <div class=\"qr-placeholder\">\n            二维码\n            <div class=\"qr-text\">{{ shareUrl }}</div>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <template #footer>\n      <div class=\"dialog-footer\">\n        <el-button @click=\"handleClose\">关闭</el-button>\n      </div>\n    </template>\n  </el-dialog>\n</template>\n\n<script>\nexport default { name: 'ShareDialog' }\n</script>\n\n<script setup>\nimport { ref, computed, watch } from 'vue'\nimport { ElMessage } from 'element-plus'\nimport { generateLiveShareUrl, copyToClipboard, shareToSocial, getShareInfo } from '@/utils/shareUtils.js'\n\nconst props = defineProps({\n  modelValue: { type: Boolean, default: false },\n  liveId: { type: String, default: '' },\n  liveData: { type: Object, default: () => ({}) }\n})\n\nconst emit = defineEmits(['update:modelValue'])\n\nconst visible = computed({\n  get: () => props.modelValue,\n  set: (value) => emit('update:modelValue', value)\n})\n\nconst shareUrl = ref('')\nconst copying = ref(false)\nconst qrCodeRef = ref(null)\n\n// 计算分享信息\nconst shareInfo = computed(() => {\n  return getShareInfo(props.liveData)\n})\n\n// 监听弹窗打开，生成分享链接\nwatch(() => props.modelValue, (newVal) => {\n  if (newVal && props.liveId) {\n    generateShareUrl()\n  }\n})\n\n// 生成分享链接\nconst generateShareUrl = () => {\n  try {\n    shareUrl.value = generateLiveShareUrl(props.liveId)\n  } catch (error) {\n    ElMessage.error('生成分享链接失败')\n    console.error('生成分享链接失败:', error)\n  }\n}\n\n// 复制链接\nconst copyLink = async () => {\n  if (!shareUrl.value) {\n    ElMessage.error('分享链接为空')\n    return\n  }\n\n  copying.value = true\n  try {\n    const success = await copyToClipboard(shareUrl.value)\n    if (success) {\n      ElMessage.success('分享链接已复制到剪贴板')\n    } else {\n      ElMessage.error('复制失败')\n    }\n  } catch (error) {\n    ElMessage.error('复制失败')\n  } finally {\n    copying.value = false\n  }\n}\n\n// 分享到微信\nconst shareToWeChat = async () => {\n  const success = await copyToClipboard(shareUrl.value)\n  if (success) {\n    ElMessage.success('链接已复制，请在微信中粘贴分享')\n  }\n}\n\n// 分享到QQ\nconst shareToQQ = () => {\n  shareToSocial(shareUrl.value, shareInfo.value.title, 'qq')\n}\n\n// 分享到微博\nconst shareToWeibo = () => {\n  shareToSocial(shareUrl.value, shareInfo.value.title, 'weibo')\n}\n\n// 关闭弹窗\nconst handleClose = () => {\n  visible.value = false\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.share-dialog {\n  .share-content {\n    .live-info {\n      margin-bottom: 24px;\n      padding: 16px;\n      background: #f8f9fa;\n      border-radius: 8px;\n\n      .live-title {\n        font-size: 16px;\n        font-weight: 600;\n        color: #333;\n        margin-bottom: 8px;\n      }\n\n      .live-desc {\n        font-size: 14px;\n        color: #666;\n      }\n    }\n\n    .section-title {\n      font-size: 14px;\n      font-weight: 600;\n      color: #333;\n      margin-bottom: 12px;\n    }\n\n    .share-link-section {\n      margin-bottom: 24px;\n\n      .link-input-group {\n        display: flex;\n        gap: 12px;\n\n        .share-input {\n          flex: 1;\n        }\n      }\n    }\n\n    .share-methods {\n      margin-bottom: 24px;\n\n      .share-buttons {\n        display: flex;\n        gap: 16px;\n\n        .share-btn {\n          display: flex;\n          flex-direction: column;\n          align-items: center;\n          gap: 8px;\n          padding: 12px;\n          border-radius: 8px;\n          cursor: pointer;\n          transition: background-color 0.2s;\n\n          &:hover {\n            background: #f5f5f5;\n          }\n\n          .share-icon {\n            width: 40px;\n            height: 40px;\n            border-radius: 50%;\n            display: flex;\n            align-items: center;\n            justify-content: center;\n            color: white;\n            font-weight: bold;\n\n            &.wechat-icon {\n              background: #07c160;\n            }\n\n            &.qq-icon {\n              background: #12b7f5;\n            }\n\n            &.weibo-icon {\n              background: #e6162d;\n            }\n          }\n\n          span {\n            font-size: 12px;\n            color: #666;\n          }\n        }\n      }\n    }\n\n    .qr-section {\n      .qr-code {\n        display: flex;\n        justify-content: center;\n\n        .qr-placeholder {\n          width: 120px;\n          height: 120px;\n          border: 2px dashed #ddd;\n          display: flex;\n          flex-direction: column;\n          align-items: center;\n          justify-content: center;\n          color: #999;\n          font-size: 14px;\n\n          .qr-text {\n            font-size: 10px;\n            margin-top: 8px;\n            word-break: break-all;\n            text-align: center;\n            padding: 0 8px;\n          }\n        }\n      }\n    }\n  }\n}\n</style>\n"], "mappings": ";;EAQSA,KAAK,EAAC;AAAe;;EAEnBA,KAAK,EAAC;AAAW;;EACfA,KAAK,EAAC;AAAY;;EAClBA,KAAK,EAAC;AAAW;;EAInBA,KAAK,EAAC;AAAoB;;EAExBA,KAAK,EAAC;AAAkB;;EAiC1BA,KAAK,EAAC;AAAY;;EAEhBA,KAAK,EAAC,SAAS;EAACC,GAAG,EAAC;;;EAElBD,KAAK,EAAC;AAAgB;;EAEpBA,KAAK,EAAC;AAAS;;EAOrBA,KAAK,EAAC;AAAe;;;;;uBA/D9BE,YAAA,CAmEYC,oBAAA;IApEdC,UAAA,EAEaC,MAAA,CAAAC,OAAO;IAFpB,uBAAAC,MAAA,QAAAA,MAAA,gBAAAC,MAAA;MAAA,OAEaH,MAAA,CAAAC,OAAO,GAAAE,MAAA;IAAA;IAChBC,KAAK,EAAC,MAAM;IACZC,KAAK,EAAC,OAAO;IACZ,cAAY,EAAEL,MAAA,CAAAM,WAAW;IAC1BX,KAAK,EAAC;;IAyDKY,MAAM,EAAAC,QAAA,CACf;MAAA,OAEM,CAFNC,mBAAA,CAEM,OAFNC,WAEM,GADJC,YAAA,CAA8CC,oBAAA;QAAlCC,OAAK,EAAEb,MAAA,CAAAM;MAAW;QAjEtCQ,OAAA,EAAAN,QAAA,CAiEwC;UAAA,OAAEN,MAAA,QAAAA,MAAA,OAjE1Ca,gBAAA,CAiEwC,IAAE,E;;QAjE1CC,CAAA;;;IAAAF,OAAA,EAAAN,QAAA,CAQI;MAAA,OAqDM,CArDNC,mBAAA,CAqDM,OArDNQ,UAqDM,GApDJC,mBAAA,UAAa,EACbT,mBAAA,CAGM,OAHNU,UAGM,GAFJV,mBAAA,CAAmD,OAAnDW,UAAmD,EAAAC,gBAAA,CAAxBrB,MAAA,CAAAsB,SAAS,CAAClB,KAAK,kBAC1CK,mBAAA,CAAwD,OAAxDc,UAAwD,EAAAF,gBAAA,CAA9BrB,MAAA,CAAAsB,SAAS,CAACE,WAAW,iB,GAGjDN,mBAAA,UAAa,EACbT,mBAAA,CAaM,OAbNgB,UAaM,G,0BAZJhB,mBAAA,CAAqC;QAAhCd,KAAK,EAAC;MAAe,GAAC,MAAI,sBAC/Bc,mBAAA,CAUM,OAVNiB,UAUM,GATJf,YAAA,CAKEgB,mBAAA;QAxBZ5B,UAAA,EAoBqBC,MAAA,CAAA4B,QAAQ;QApB7B,uBAAA1B,MAAA,QAAAA,MAAA,gBAAAC,MAAA;UAAA,OAoBqBH,MAAA,CAAA4B,QAAQ,GAAAzB,MAAA;QAAA;QACjB0B,QAAQ,EAAR,EAAQ;QACRlC,KAAK,EAAC,aAAa;QACnBmC,WAAW,EAAC;+CAEdnB,YAAA,CAEYC,oBAAA;QAFDmB,IAAI,EAAC,SAAS;QAAElB,OAAK,EAAEb,MAAA,CAAAgC,QAAQ;QAAGC,OAAO,EAAEjC,MAAA,CAAAkC;;QAzBhEpB,OAAA,EAAAN,QAAA,CA0BY;UAAA,OAA8B,CA1B1CO,gBAAA,CAAAM,gBAAA,CA0BerB,MAAA,CAAAkC,OAAO,kC;;QA1BtBlB,CAAA;0CA+BME,mBAAA,UAAa,EACbT,mBAAA,CAgBM;QAhBDd,KAAK,EAAC;MAAe,I,0BACxBc,mBAAA,CAAoC;QAA/Bd,KAAK,EAAC;MAAe,GAAC,KAAG,sBAC9Bc,mBAAA,CAaM;QAbDd,KAAK,EAAC;MAAe,IACxBc,mBAAA,CAGM;QAHDd,KAAK,EAAC,WAAW;QAAEkB,OAAK,EAAEb,MAAA,CAAAmC;oCAC7B1B,mBAAA,CAA2C;QAAtCd,KAAK,EAAC;MAAwB,GAAC,GAAC,qBACrCc,mBAAA,CAAe,cAAT,IAAE,oB,IAEVA,mBAAA,CAGM;QAHDd,KAAK,EAAC,WAAW;QAAEkB,OAAK,EAAEb,MAAA,CAAAoC;oCAC7B3B,mBAAA,CAAuC;QAAlCd,KAAK,EAAC;MAAoB,GAAC,GAAC,qBACjCc,mBAAA,CAAe,cAAT,IAAE,oB,IAEVA,mBAAA,CAGM;QAHDd,KAAK,EAAC,WAAW;QAAEkB,OAAK,EAAEb,MAAA,CAAAqC;oCAC7B5B,mBAAA,CAA0C;QAArCd,KAAK,EAAC;MAAuB,GAAC,GAAC,qBACpCc,mBAAA,CAAe,cAAT,IAAE,oB,QAKdS,mBAAA,WAAc,EACdT,mBAAA,CASM,OATN6B,UASM,G,0BARJ7B,mBAAA,CAAqC;QAAhCd,KAAK,EAAC;MAAe,GAAC,MAAI,sBAC/Bc,mBAAA,CAMM,OANN8B,UAMM,GALJrB,mBAAA,kBAAqB,EACrBT,mBAAA,CAGM,OAHN+B,UAGM,G,0BA1DhBzB,gBAAA,CAuDsC,OAE1B,IAAAN,mBAAA,CAAyC,OAAzCgC,WAAyC,EAAApB,gBAAA,CAAjBrB,MAAA,CAAA4B,QAAQ,iB;;IAzD5CZ,CAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "import LazyWrapper from './_LazyWrapper.js';\nimport LodashWrapper from './_LodashWrapper.js';\nimport copyArray from './_copyArray.js';\n\n/**\n * Creates a clone of `wrapper`.\n *\n * @private\n * @param {Object} wrapper The wrapper to clone.\n * @returns {Object} Returns the cloned wrapper.\n */\nfunction wrapperClone(wrapper) {\n  if (wrapper instanceof LazyWrapper) {\n    return wrapper.clone();\n  }\n  var result = new LodashWrapper(wrapper.__wrapped__, wrapper.__chain__);\n  result.__actions__ = copyArray(wrapper.__actions__);\n  result.__index__ = wrapper.__index__;\n  result.__values__ = wrapper.__values__;\n  return result;\n}\nexport default wrapperClone;", "map": {"version": 3, "names": ["LazyWrapper", "LodashWrapper", "copyArray", "wrapperClone", "wrapper", "clone", "result", "__wrapped__", "__chain__", "__actions__", "__index__", "__values__"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/node_modules/.pnpm/lodash-es@4.17.21/node_modules/lodash-es/_wrapperClone.js"], "sourcesContent": ["import LazyWrapper from './_LazyWrapper.js';\nimport LodashWrapper from './_LodashWrapper.js';\nimport copyArray from './_copyArray.js';\n\n/**\n * Creates a clone of `wrapper`.\n *\n * @private\n * @param {Object} wrapper The wrapper to clone.\n * @returns {Object} Returns the cloned wrapper.\n */\nfunction wrapperClone(wrapper) {\n  if (wrapper instanceof LazyWrapper) {\n    return wrapper.clone();\n  }\n  var result = new LodashWrapper(wrapper.__wrapped__, wrapper.__chain__);\n  result.__actions__ = copyArray(wrapper.__actions__);\n  result.__index__  = wrapper.__index__;\n  result.__values__ = wrapper.__values__;\n  return result;\n}\n\nexport default wrapperClone;\n"], "mappings": "AAAA,OAAOA,WAAW,MAAM,mBAAmB;AAC3C,OAAOC,aAAa,MAAM,qBAAqB;AAC/C,OAAOC,SAAS,MAAM,iBAAiB;;AAEvC;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,YAAYA,CAACC,OAAO,EAAE;EAC7B,IAAIA,OAAO,YAAYJ,WAAW,EAAE;IAClC,OAAOI,OAAO,CAACC,KAAK,CAAC,CAAC;EACxB;EACA,IAAIC,MAAM,GAAG,IAAIL,aAAa,CAACG,OAAO,CAACG,WAAW,EAAEH,OAAO,CAACI,SAAS,CAAC;EACtEF,MAAM,CAACG,WAAW,GAAGP,SAAS,CAACE,OAAO,CAACK,WAAW,CAAC;EACnDH,MAAM,CAACI,SAAS,GAAIN,OAAO,CAACM,SAAS;EACrCJ,MAAM,CAACK,UAAU,GAAGP,OAAO,CAACO,UAAU;EACtC,OAAOL,MAAM;AACf;AAEA,eAAeH,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
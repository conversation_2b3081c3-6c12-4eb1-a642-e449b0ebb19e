{"ast": null, "code": "import { resolveComponent as _resolveComponent, createVNode as _createVNode, withCtx as _withCtx, createTextVNode as _createTextVNode, createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock } from \"vue\";\nvar _hoisted_1 = {\n  class: \"ClusterManageNew\"\n};\nvar _hoisted_2 = {\n  class: \"globalFormButton\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  var _component_el_input = _resolveComponent(\"el-input\");\n  var _component_el_form_item = _resolveComponent(\"el-form-item\");\n  var _component_input_select_person_account = _resolveComponent(\"input-select-person-account\");\n  var _component_business_select_person_account = _resolveComponent(\"business-select-person-account\");\n  var _component_el_button = _resolveComponent(\"el-button\");\n  var _component_el_form = _resolveComponent(\"el-form\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createVNode(_component_el_form, {\n    ref: \"formRef\",\n    model: $setup.form,\n    rules: $setup.rules,\n    inline: \"\",\n    \"label-position\": \"top\",\n    class: \"globalForm\"\n  }, {\n    default: _withCtx(function () {\n      return [_createVNode(_component_el_form_item, {\n        label: \"群组名称\",\n        prop: \"groupName\"\n      }, {\n        default: _withCtx(function () {\n          return [_createVNode(_component_el_input, {\n            modelValue: $setup.form.groupName,\n            \"onUpdate:modelValue\": _cache[0] || (_cache[0] = function ($event) {\n              return $setup.form.groupName = $event;\n            }),\n            placeholder: \"请输入群组名称\",\n            clearable: \"\"\n          }, null, 8 /* PROPS */, [\"modelValue\"])];\n        }),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_form_item, {\n        label: \"群主\",\n        prop: \"ownerUserId\"\n      }, {\n        default: _withCtx(function () {\n          return [_createVNode(_component_input_select_person_account, {\n            modelValue: $setup.form.ownerUserId,\n            \"onUpdate:modelValue\": _cache[1] || (_cache[1] = function ($event) {\n              return $setup.form.ownerUserId = $event;\n            }),\n            placeholder: \"请选择群主\"\n          }, null, 8 /* PROPS */, [\"modelValue\"])];\n        }),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_form_item, {\n        label: \"群成员\",\n        prop: \"memberUserIds\",\n        class: \"globalFormTitle\"\n      }, {\n        default: _withCtx(function () {\n          return [_createVNode(_component_business_select_person_account, {\n            modelValue: $setup.form.memberUserIds,\n            \"onUpdate:modelValue\": _cache[2] || (_cache[2] = function ($event) {\n              return $setup.form.memberUserIds = $event;\n            }),\n            onCallback: $setup.userCallback\n          }, null, 8 /* PROPS */, [\"modelValue\"])];\n        }),\n        _: 1 /* STABLE */\n      }), _createElementVNode(\"div\", _hoisted_2, [_createVNode(_component_el_button, {\n        type: \"primary\",\n        onClick: _cache[3] || (_cache[3] = function ($event) {\n          return $setup.submitForm($setup.formRef);\n        })\n      }, {\n        default: _withCtx(function () {\n          return _cache[4] || (_cache[4] = [_createTextVNode(\"提交\")]);\n        }),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_button, {\n        onClick: $setup.resetForm\n      }, {\n        default: _withCtx(function () {\n          return _cache[5] || (_cache[5] = [_createTextVNode(\"取消\")]);\n        }),\n        _: 1 /* STABLE */\n      })])];\n    }),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"model\", \"rules\"])]);\n}", "map": {"version": 3, "names": ["class", "_createElementBlock", "_hoisted_1", "_createVNode", "_component_el_form", "ref", "model", "$setup", "form", "rules", "inline", "default", "_withCtx", "_component_el_form_item", "label", "prop", "_component_el_input", "modelValue", "groupName", "_cache", "$event", "placeholder", "clearable", "_", "_component_input_select_person_account", "ownerUserId", "_component_business_select_person_account", "memberUserIds", "onCallback", "userCallback", "_createElementVNode", "_hoisted_2", "_component_el_button", "type", "onClick", "submitForm", "formRef", "_createTextVNode", "resetForm"], "sources": ["D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\interaction\\src\\views\\ClusterManage\\component\\ClusterManageNew.vue"], "sourcesContent": ["<template>\r\n  <div class=\"ClusterManageNew\">\r\n    <el-form ref=\"formRef\"\r\n             :model=\"form\"\r\n             :rules=\"rules\"\r\n             inline\r\n             label-position=\"top\"\r\n             class=\"globalForm\">\r\n      <el-form-item label=\"群组名称\"\r\n                    prop=\"groupName\">\r\n        <el-input v-model=\"form.groupName\"\r\n                  placeholder=\"请输入群组名称\"\r\n                  clearable />\r\n      </el-form-item>\r\n      <el-form-item label=\"群主\"\r\n                    prop=\"ownerUserId\">\r\n        <input-select-person-account v-model=\"form.ownerUserId\"\r\n                                     placeholder=\"请选择群主\" />\r\n      </el-form-item>\r\n      <el-form-item label=\"群成员\"\r\n                    prop=\"memberUserIds\"\r\n                    class=\"globalFormTitle\">\r\n        <business-select-person-account v-model=\"form.memberUserIds\"\r\n                                        @callback=\"userCallback\"></business-select-person-account>\r\n      </el-form-item>\r\n      <div class=\"globalFormButton\">\r\n        <el-button type=\"primary\"\r\n                   @click=\"submitForm(formRef)\">提交</el-button>\r\n        <el-button @click=\"resetForm\">取消</el-button>\r\n      </div>\r\n    </el-form>\r\n  </div>\r\n</template>\r\n<script>\r\nexport default { name: 'ClusterManageNew' }\r\n</script>\r\n<script setup>\r\nimport api from '@/api'\r\nimport { reactive, ref, onMounted } from 'vue'\r\nimport { user } from 'common/js/system_var.js'\r\nimport { ElMessage } from 'element-plus'\r\nimport { useRoute } from 'vue-router'\r\nconst route = useRoute()\r\nconst props = defineProps({ id: { type: String, default: '' } })\r\nconst emit = defineEmits(['callback'])\r\n\r\nconst formRef = ref()\r\nconst form = reactive({\r\n  groupName: '', // 群组名称\r\n  ownerUserId: '', // 群主id\r\n  chatGroupType: '1', // 群组类型\r\n  memberUserIds: [] // 群成员\r\n})\r\nconst rules = reactive({\r\n  groupName: [{ required: true, message: '请输入群组名称', trigger: ['blur', 'change'] }],\r\n  ownerUserId: [{ required: true, message: '请选择群主', trigger: ['blur', 'change'] }],\r\n  chatGroupType: [{ required: true, message: '请选择群组类型', trigger: ['blur', 'change'] }],\r\n  memberUserIds: [{ required: true, message: '请选择群成员', trigger: ['blur', 'change'] }]\r\n})\r\n\r\nonMounted(() => { if (props.id) { ClusterInfo() } else { form.ownerUserId = user.value.accountId } })\r\n\r\nconst ClusterInfo = async () => {\r\n  const res = await api.ClusterInfo({ detailId: props.id })\r\n  var { data } = res\r\n  form.groupName = data.groupName\r\n  form.ownerUserId = data.ownerUserId\r\n  form.chatGroupType = data.chatGroupType\r\n  form.memberUserIds = data.memberUserIds\r\n}\r\nconst userCallback = () => {\r\n  formRef.value.validateField('memberUserIds')\r\n}\r\nconst submitForm = async (formEl) => {\r\n  if (!formEl) return\r\n  await formEl.validate((valid, fields) => {\r\n    if (valid) { globalJson() } else { ElMessage({ type: 'warning', message: '请根据提示信息完善字段内容！' }) }\r\n  })\r\n}\r\nconst globalJson = async () => {\r\n  const { code } = await api.globalJson(props.id ? '/chatGroup/edit' : '/chatGroup/add', {\r\n    form: {\r\n      id: props.id,\r\n      groupName: form.groupName,\r\n      chatGroupType: route.query.chatGroupType || form.chatGroupType\r\n    },\r\n    ownerUserId: form.ownerUserId,\r\n    memberUserIds: form.memberUserIds\r\n  })\r\n  if (code === 200) {\r\n    ElMessage({ type: 'success', message: props.id ? '编辑成功' : '新增成功' })\r\n    emit('callback')\r\n  }\r\n}\r\nconst resetForm = () => { emit('callback') }\r\n</script>\r\n<style lang=\"scss\">\r\n.ClusterManageNew {\r\n  width: 990px;\r\n}\r\n</style>\r\n"], "mappings": ";;EACOA,KAAK,EAAC;AAAkB;;EAwBpBA,KAAK,EAAC;AAAkB;;;;;;;;uBAxBjCC,mBAAA,CA8BM,OA9BNC,UA8BM,GA7BJC,YAAA,CA4BUC,kBAAA;IA5BDC,GAAG,EAAC,SAAS;IACZC,KAAK,EAAEC,MAAA,CAAAC,IAAI;IACXC,KAAK,EAAEF,MAAA,CAAAE,KAAK;IACbC,MAAM,EAAN,EAAM;IACN,gBAAc,EAAC,KAAK;IACpBV,KAAK,EAAC;;IAPnBW,OAAA,EAAAC,QAAA,CAQM;MAAA,OAKe,CALfT,YAAA,CAKeU,uBAAA;QALDC,KAAK,EAAC,MAAM;QACZC,IAAI,EAAC;;QATzBJ,OAAA,EAAAC,QAAA,CAUQ;UAAA,OAEsB,CAFtBT,YAAA,CAEsBa,mBAAA;YAZ9BC,UAAA,EAU2BV,MAAA,CAAAC,IAAI,CAACU,SAAS;YAVzC,uBAAAC,MAAA,QAAAA,MAAA,gBAAAC,MAAA;cAAA,OAU2Bb,MAAA,CAAAC,IAAI,CAACU,SAAS,GAAAE,MAAA;YAAA;YACvBC,WAAW,EAAC,SAAS;YACrBC,SAAS,EAAT;;;QAZlBC,CAAA;UAcMpB,YAAA,CAIeU,uBAAA;QAJDC,KAAK,EAAC,IAAI;QACVC,IAAI,EAAC;;QAfzBJ,OAAA,EAAAC,QAAA,CAgBQ;UAAA,OACmD,CADnDT,YAAA,CACmDqB,sCAAA;YAjB3DP,UAAA,EAgB8CV,MAAA,CAAAC,IAAI,CAACiB,WAAW;YAhB9D,uBAAAN,MAAA,QAAAA,MAAA,gBAAAC,MAAA;cAAA,OAgB8Cb,MAAA,CAAAC,IAAI,CAACiB,WAAW,GAAAL,MAAA;YAAA;YACzBC,WAAW,EAAC;;;QAjBjDE,CAAA;UAmBMpB,YAAA,CAKeU,uBAAA;QALDC,KAAK,EAAC,KAAK;QACXC,IAAI,EAAC,eAAe;QACpBf,KAAK,EAAC;;QArB1BW,OAAA,EAAAC,QAAA,CAsBQ;UAAA,OAC0F,CAD1FT,YAAA,CAC0FuB,yCAAA;YAvBlGT,UAAA,EAsBiDV,MAAA,CAAAC,IAAI,CAACmB,aAAa;YAtBnE,uBAAAR,MAAA,QAAAA,MAAA,gBAAAC,MAAA;cAAA,OAsBiDb,MAAA,CAAAC,IAAI,CAACmB,aAAa,GAAAP,MAAA;YAAA;YAC1BQ,UAAQ,EAAErB,MAAA,CAAAsB;;;QAvBnDN,CAAA;UAyBMO,mBAAA,CAIM,OAJNC,UAIM,GAHJ5B,YAAA,CACsD6B,oBAAA;QAD3CC,IAAI,EAAC,SAAS;QACbC,OAAK,EAAAf,MAAA,QAAAA,MAAA,gBAAAC,MAAA;UAAA,OAAEb,MAAA,CAAA4B,UAAU,CAAC5B,MAAA,CAAA6B,OAAO;QAAA;;QA3B7CzB,OAAA,EAAAC,QAAA,CA2BgD;UAAA,OAAEO,MAAA,QAAAA,MAAA,OA3BlDkB,gBAAA,CA2BgD,IAAE,E;;QA3BlDd,CAAA;UA4BQpB,YAAA,CAA4C6B,oBAAA;QAAhCE,OAAK,EAAE3B,MAAA,CAAA+B;MAAS;QA5BpC3B,OAAA,EAAAC,QAAA,CA4BsC;UAAA,OAAEO,MAAA,QAAAA,MAAA,OA5BxCkB,gBAAA,CA4BsC,IAAE,E;;QA5BxCd,CAAA;;;IAAAA,CAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
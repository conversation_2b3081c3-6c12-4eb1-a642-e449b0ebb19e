{"ast": null, "code": "import { renderList as _renderList, Fragment as _Fragment, openBlock as _openBlock, createElementBlock as _createElementBlock, toDisplayString as _toDisplayString, createTextVNode as _createTextVNode, resolveComponent as _resolveComponent, withCtx as _withCtx, createBlock as _createBlock, createVNode as _createVNode, createCommentVNode as _createCommentVNode } from \"vue\";\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  var _component_el_button = _resolveComponent(\"el-button\");\n  var _component_el_dropdown_item = _resolveComponent(\"el-dropdown-item\");\n  var _component_el_dropdown_menu = _resolveComponent(\"el-dropdown-menu\");\n  var _component_el_dropdown = _resolveComponent(\"el-dropdown\");\n  return _openBlock(), _createElementBlock(_Fragment, null, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.dataOne, function (item) {\n    return _openBlock(), _createBlock(_component_el_button, {\n      key: item.id,\n      disabled: item.disabled,\n      onClick: function onClick($event) {\n        return $setup.handleButton(item.id);\n      },\n      type: \"primary\",\n      plain: \"\"\n    }, {\n      default: _withCtx(function () {\n        return [_createTextVNode(_toDisplayString(item.name), 1 /* TEXT */)];\n      }),\n      _: 2 /* DYNAMIC */\n    }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"disabled\", \"onClick\"]);\n  }), 128 /* KEYED_FRAGMENT */)), $setup.dataTwo.length ? (_openBlock(), _createBlock(_component_el_dropdown, {\n    key: 0,\n    onCommand: $setup.handleCommand,\n    trigger: \"click\"\n  }, {\n    dropdown: _withCtx(function () {\n      return [_createVNode(_component_el_dropdown_menu, null, {\n        default: _withCtx(function () {\n          return [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.dataTwo, function (item) {\n            return _openBlock(), _createBlock(_component_el_dropdown_item, {\n              key: item.id,\n              disabled: item.disabled,\n              command: {\n                id: item.id\n              }\n            }, {\n              default: _withCtx(function () {\n                return [_createTextVNode(_toDisplayString(item.name), 1 /* TEXT */)];\n              }),\n              _: 2 /* DYNAMIC */\n            }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"disabled\", \"command\"]);\n          }), 128 /* KEYED_FRAGMENT */))];\n        }),\n        _: 1 /* STABLE */\n      })];\n    }),\n    default: _withCtx(function () {\n      return [_createVNode(_component_el_button, {\n        type: \"primary\",\n        plain: \"\"\n      }, {\n        default: _withCtx(function () {\n          return _cache[0] || (_cache[0] = [_createTextVNode(\"管理\")]);\n        }),\n        _: 1 /* STABLE */\n      })];\n    }),\n    _: 1 /* STABLE */\n  })) : _createCommentVNode(\"v-if\", true)], 64 /* STABLE_FRAGMENT */);\n}", "map": {"version": 3, "names": ["_createElementBlock", "_Fragment", "_renderList", "$setup", "dataOne", "item", "_createBlock", "_component_el_button", "key", "id", "disabled", "onClick", "$event", "handleButton", "type", "plain", "default", "_withCtx", "_createTextVNode", "_toDisplayString", "name", "_", "dataTwo", "length", "_component_el_dropdown", "onCommand", "handleCommand", "trigger", "dropdown", "_createVNode", "_component_el_dropdown_menu", "_component_el_dropdown_item", "command", "_cache", "_createCommentVNode"], "sources": ["D:\\zy\\xm\\pc\\5.0\\qiankun-product\\common\\components\\xyl-global-table\\xyl-global-table-button-item.vue"], "sourcesContent": ["<!--\r\n * @FileDescription: 权限控制按钮每一行\r\n * @Author: 谢育林\r\n * @Date: 2024-4-2\r\n * @LastEditors: 谢育林\r\n * @LastEditTime: 2024-4-2\r\n -->\r\n<template>\r\n  <el-button\r\n    v-for=\"item in dataOne\"\r\n    :key=\"item.id\"\r\n    :disabled=\"item.disabled\"\r\n    @click=\"handleButton(item.id)\"\r\n    type=\"primary\"\r\n    plain>\r\n    {{ item.name }}\r\n  </el-button>\r\n  <el-dropdown v-if=\"dataTwo.length\" @command=\"handleCommand\" trigger=\"click\">\r\n    <el-button type=\"primary\" plain>管理</el-button>\r\n    <template #dropdown>\r\n      <el-dropdown-menu>\r\n        <el-dropdown-item v-for=\"item in dataTwo\" :key=\"item.id\" :disabled=\"item.disabled\" :command=\"{ id: item.id }\">\r\n          {{ item.name }}\r\n        </el-dropdown-item>\r\n      </el-dropdown-menu>\r\n    </template>\r\n  </el-dropdown>\r\n</template>\r\n<script>\r\nexport default { name: 'XylGlobalTableButtonItem' }\r\n</script>\r\n<script setup>\r\nimport { ref, watch } from 'vue'\r\nconst props = defineProps({\r\n  dataRow: { type: Object, default: () => ({}) },\r\n  data: { type: Array, default: () => [] },\r\n  max: { type: Number, default: 1 },\r\n  elWhetherDisabled: Function,\r\n  elWhetherShow: Function\r\n})\r\nconst emit = defineEmits(['click'])\r\n\r\nconst dataOne = ref([])\r\nconst dataTwo = ref([])\r\n\r\n/**\r\n * @description: 按钮点击事件\r\n * @return void\r\n */\r\nconst handleButton = (id) => {\r\n  emit('click', props.dataRow, id)\r\n}\r\nconst handleCommand = ({ id }) => {\r\n  emit('click', props.dataRow, id)\r\n}\r\n/**\r\n * @description: 按钮禁用方法\r\n * @return true 禁用  /  false 不禁用\r\n */\r\nconst handleElWhetherDisabled = (id, type) => {\r\n  if (!type) return false\r\n  if (typeof props.elWhetherDisabled === 'function') {\r\n    return props.elWhetherDisabled(props.dataRow, id)\r\n  } else {\r\n    return false\r\n  }\r\n}\r\n/**\r\n * @description: 按钮显示方法\r\n * @return true 显示  /  false 不显示\r\n */\r\nconst handleElWhetherShow = (id, type) => {\r\n  if (!type) return true\r\n  if (typeof props.elWhetherShow === 'function') {\r\n    return props.elWhetherShow(props.dataRow, id)\r\n  } else {\r\n    return true\r\n  }\r\n}\r\nconst initButton = () => {\r\n  var newTableData = []\r\n  for (let index = 0; index < props.data.length; index++) {\r\n    const item = props.data[index]\r\n    if (handleElWhetherShow(item.id, item.whetherShow)) {\r\n      const disabled = handleElWhetherDisabled(item.id, item.whetherDisabled)\r\n      newTableData.push({ id: item.id, name: item.name, disabled: disabled })\r\n    }\r\n  }\r\n  if (newTableData.length === props.max || newTableData.length === props.max + 1) {\r\n    dataOne.value = newTableData\r\n    dataTwo.value = []\r\n  } else {\r\n    dataOne.value = newTableData.slice(0, props.max)\r\n    dataTwo.value = newTableData.slice(props.max)\r\n  }\r\n}\r\nwatch(\r\n  [() => props.dataRow, () => props.data],\r\n  () => {\r\n    initButton()\r\n  },\r\n  { immediate: true }\r\n)\r\n</script>\r\n"], "mappings": ";;;;;;uBAAAA,mBAAA,CAAAC,SAAA,U,kBAQED,mBAAA,CAQYC,SAAA,QAhBdC,WAAA,CASmBC,MAAA,CAAAC,OAAO,EAT1B,UASWC,IAAI;yBADbC,YAAA,CAQYC,oBAAA;MANTC,GAAG,EAAEH,IAAI,CAACI,EAAE;MACZC,QAAQ,EAAEL,IAAI,CAACK,QAAQ;MACvBC,OAAK,WAALA,OAAKA,CAAAC,MAAA;QAAA,OAAET,MAAA,CAAAU,YAAY,CAACR,IAAI,CAACI,EAAE;MAAA;MAC5BK,IAAI,EAAC,SAAS;MACdC,KAAK,EAAL;;MAdJC,OAAA,EAAAC,QAAA,CAeI;QAAA,OAAe,CAfnBC,gBAAA,CAAAC,gBAAA,CAeOd,IAAI,CAACe,IAAI,iB;;MAfhBC,CAAA;;kCAiBqBlB,MAAA,CAAAmB,OAAO,CAACC,MAAM,I,cAAjCjB,YAAA,CASckB,sBAAA;IA1BhBhB,GAAA;IAiBsCiB,SAAO,EAAEtB,MAAA,CAAAuB,aAAa;IAAEC,OAAO,EAAC;;IAEvDC,QAAQ,EAAAX,QAAA,CACjB;MAAA,OAImB,CAJnBY,YAAA,CAImBC,2BAAA;QAxBzBd,OAAA,EAAAC,QAAA,CAqB0B;UAAA,OAAuB,E,kBAAzCjB,mBAAA,CAEmBC,SAAA,QAvB3BC,WAAA,CAqByCC,MAAA,CAAAmB,OAAO,EArBhD,UAqBiCjB,IAAI;iCAA7BC,YAAA,CAEmByB,2BAAA;cAFwBvB,GAAG,EAAEH,IAAI,CAACI,EAAE;cAAGC,QAAQ,EAAEL,IAAI,CAACK,QAAQ;cAAGsB,OAAO;gBAAAvB,EAAA,EAAQJ,IAAI,CAACI;cAAE;;cArBlHO,OAAA,EAAAC,QAAA,CAsBU;gBAAA,OAAe,CAtBzBC,gBAAA,CAAAC,gBAAA,CAsBad,IAAI,CAACe,IAAI,iB;;cAtBtBC,CAAA;;;;QAAAA,CAAA;;;IAAAL,OAAA,EAAAC,QAAA,CAkBI;MAAA,OAA8C,CAA9CY,YAAA,CAA8CtB,oBAAA;QAAnCO,IAAI,EAAC,SAAS;QAACC,KAAK,EAAL;;QAlB9BC,OAAA,EAAAC,QAAA,CAkBoC;UAAA,OAAEgB,MAAA,QAAAA,MAAA,OAlBtCf,gBAAA,CAkBoC,IAAE,E;;QAlBtCG,CAAA;;;IAAAA,CAAA;QAAAa,mBAAA,e", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
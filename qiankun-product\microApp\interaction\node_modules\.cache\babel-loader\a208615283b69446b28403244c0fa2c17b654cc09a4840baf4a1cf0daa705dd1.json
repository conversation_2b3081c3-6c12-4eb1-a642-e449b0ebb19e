{"ast": null, "code": "\"use strict\";\n\nmodule.exports = function (idHandler) {\n  var eventListeners = {};\n\n  /**\n   * Gets all listeners for the given element.\n   * @public\n   * @param {element} element The element to get all listeners for.\n   * @returns All listeners for the given element.\n   */\n  function getListeners(element) {\n    var id = idHandler.get(element);\n    if (id === undefined) {\n      return [];\n    }\n    return eventListeners[id] || [];\n  }\n\n  /**\n   * Stores the given listener for the given element. Will not actually add the listener to the element.\n   * @public\n   * @param {element} element The element that should have the listener added.\n   * @param {function} listener The callback that the element has added.\n   */\n  function addListener(element, listener) {\n    var id = idHandler.get(element);\n    if (!eventListeners[id]) {\n      eventListeners[id] = [];\n    }\n    eventListeners[id].push(listener);\n  }\n  function removeListener(element, listener) {\n    var listeners = getListeners(element);\n    for (var i = 0, len = listeners.length; i < len; ++i) {\n      if (listeners[i] === listener) {\n        listeners.splice(i, 1);\n        break;\n      }\n    }\n  }\n  function removeAllListeners(element) {\n    var listeners = getListeners(element);\n    if (!listeners) {\n      return;\n    }\n    listeners.length = 0;\n  }\n  return {\n    get: getListeners,\n    add: addListener,\n    removeListener: removeListener,\n    removeAllListeners: removeAllListeners\n  };\n};", "map": {"version": 3, "names": ["module", "exports", "id<PERSON><PERSON><PERSON>", "eventListeners", "getListeners", "element", "id", "get", "undefined", "addListener", "listener", "push", "removeListener", "listeners", "i", "len", "length", "splice", "removeAllListeners", "add"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/node_modules/.pnpm/element-resize-detector@1.2.4/node_modules/element-resize-detector/src/listener-handler.js"], "sourcesContent": ["\"use strict\";\n\nmodule.exports = function(idHandler) {\n    var eventListeners = {};\n\n    /**\n     * Gets all listeners for the given element.\n     * @public\n     * @param {element} element The element to get all listeners for.\n     * @returns All listeners for the given element.\n     */\n    function getListeners(element) {\n        var id = idHandler.get(element);\n\n        if (id === undefined) {\n            return [];\n        }\n\n        return eventListeners[id] || [];\n    }\n\n    /**\n     * Stores the given listener for the given element. Will not actually add the listener to the element.\n     * @public\n     * @param {element} element The element that should have the listener added.\n     * @param {function} listener The callback that the element has added.\n     */\n    function addListener(element, listener) {\n        var id = idHandler.get(element);\n\n        if(!eventListeners[id]) {\n            eventListeners[id] = [];\n        }\n\n        eventListeners[id].push(listener);\n    }\n\n    function removeListener(element, listener) {\n        var listeners = getListeners(element);\n        for (var i = 0, len = listeners.length; i < len; ++i) {\n            if (listeners[i] === listener) {\n              listeners.splice(i, 1);\n              break;\n            }\n        }\n    }\n\n    function removeAllListeners(element) {\n      var listeners = getListeners(element);\n      if (!listeners) { return; }\n      listeners.length = 0;\n    }\n\n    return {\n        get: getListeners,\n        add: addListener,\n        removeListener: removeListener,\n        removeAllListeners: removeAllListeners\n    };\n};\n"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,OAAO,GAAG,UAASC,SAAS,EAAE;EACjC,IAAIC,cAAc,GAAG,CAAC,CAAC;;EAEvB;AACJ;AACA;AACA;AACA;AACA;EACI,SAASC,YAAYA,CAACC,OAAO,EAAE;IAC3B,IAAIC,EAAE,GAAGJ,SAAS,CAACK,GAAG,CAACF,OAAO,CAAC;IAE/B,IAAIC,EAAE,KAAKE,SAAS,EAAE;MAClB,OAAO,EAAE;IACb;IAEA,OAAOL,cAAc,CAACG,EAAE,CAAC,IAAI,EAAE;EACnC;;EAEA;AACJ;AACA;AACA;AACA;AACA;EACI,SAASG,WAAWA,CAACJ,OAAO,EAAEK,QAAQ,EAAE;IACpC,IAAIJ,EAAE,GAAGJ,SAAS,CAACK,GAAG,CAACF,OAAO,CAAC;IAE/B,IAAG,CAACF,cAAc,CAACG,EAAE,CAAC,EAAE;MACpBH,cAAc,CAACG,EAAE,CAAC,GAAG,EAAE;IAC3B;IAEAH,cAAc,CAACG,EAAE,CAAC,CAACK,IAAI,CAACD,QAAQ,CAAC;EACrC;EAEA,SAASE,cAAcA,CAACP,OAAO,EAAEK,QAAQ,EAAE;IACvC,IAAIG,SAAS,GAAGT,YAAY,CAACC,OAAO,CAAC;IACrC,KAAK,IAAIS,CAAC,GAAG,CAAC,EAAEC,GAAG,GAAGF,SAAS,CAACG,MAAM,EAAEF,CAAC,GAAGC,GAAG,EAAE,EAAED,CAAC,EAAE;MAClD,IAAID,SAAS,CAACC,CAAC,CAAC,KAAKJ,QAAQ,EAAE;QAC7BG,SAAS,CAACI,MAAM,CAACH,CAAC,EAAE,CAAC,CAAC;QACtB;MACF;IACJ;EACJ;EAEA,SAASI,kBAAkBA,CAACb,OAAO,EAAE;IACnC,IAAIQ,SAAS,GAAGT,YAAY,CAACC,OAAO,CAAC;IACrC,IAAI,CAACQ,SAAS,EAAE;MAAE;IAAQ;IAC1BA,SAAS,CAACG,MAAM,GAAG,CAAC;EACtB;EAEA,OAAO;IACHT,GAAG,EAAEH,YAAY;IACjBe,GAAG,EAAEV,WAAW;IAChBG,cAAc,EAAEA,cAAc;IAC9BM,kBAAkB,EAAEA;EACxB,CAAC;AACL,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}
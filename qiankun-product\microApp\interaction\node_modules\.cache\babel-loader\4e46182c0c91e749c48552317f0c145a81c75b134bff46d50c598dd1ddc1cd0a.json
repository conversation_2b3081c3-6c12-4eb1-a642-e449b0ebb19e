{"ast": null, "code": "import arrayMap from './_arrayMap.js';\nimport baseIndexOf from './_baseIndexOf.js';\nimport baseIndexOfWith from './_baseIndexOfWith.js';\nimport baseUnary from './_baseUnary.js';\nimport copyArray from './_copyArray.js';\n\n/** Used for built-in method references. */\nvar arrayProto = Array.prototype;\n\n/** Built-in value references. */\nvar splice = arrayProto.splice;\n\n/**\n * The base implementation of `_.pullAllBy` without support for iteratee\n * shorthands.\n *\n * @private\n * @param {Array} array The array to modify.\n * @param {Array} values The values to remove.\n * @param {Function} [iteratee] The iteratee invoked per element.\n * @param {Function} [comparator] The comparator invoked per element.\n * @returns {Array} Returns `array`.\n */\nfunction basePullAll(array, values, iteratee, comparator) {\n  var indexOf = comparator ? baseIndexOfWith : baseIndexOf,\n    index = -1,\n    length = values.length,\n    seen = array;\n  if (array === values) {\n    values = copyArray(values);\n  }\n  if (iteratee) {\n    seen = arrayMap(array, baseUnary(iteratee));\n  }\n  while (++index < length) {\n    var fromIndex = 0,\n      value = values[index],\n      computed = iteratee ? iteratee(value) : value;\n    while ((fromIndex = indexOf(seen, computed, fromIndex, comparator)) > -1) {\n      if (seen !== array) {\n        splice.call(seen, fromIndex, 1);\n      }\n      splice.call(array, fromIndex, 1);\n    }\n  }\n  return array;\n}\nexport default basePullAll;", "map": {"version": 3, "names": ["arrayMap", "baseIndexOf", "baseIndexOfWith", "baseUnary", "copyArray", "arrayProto", "Array", "prototype", "splice", "basePullAll", "array", "values", "iteratee", "comparator", "indexOf", "index", "length", "seen", "fromIndex", "value", "computed", "call"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/node_modules/.pnpm/lodash-es@4.17.21/node_modules/lodash-es/_basePullAll.js"], "sourcesContent": ["import arrayMap from './_arrayMap.js';\nimport baseIndexOf from './_baseIndexOf.js';\nimport baseIndexOfWith from './_baseIndexOfWith.js';\nimport baseUnary from './_baseUnary.js';\nimport copyArray from './_copyArray.js';\n\n/** Used for built-in method references. */\nvar arrayProto = Array.prototype;\n\n/** Built-in value references. */\nvar splice = arrayProto.splice;\n\n/**\n * The base implementation of `_.pullAllBy` without support for iteratee\n * shorthands.\n *\n * @private\n * @param {Array} array The array to modify.\n * @param {Array} values The values to remove.\n * @param {Function} [iteratee] The iteratee invoked per element.\n * @param {Function} [comparator] The comparator invoked per element.\n * @returns {Array} Returns `array`.\n */\nfunction basePullAll(array, values, iteratee, comparator) {\n  var indexOf = comparator ? baseIndexOfWith : baseIndexOf,\n      index = -1,\n      length = values.length,\n      seen = array;\n\n  if (array === values) {\n    values = copyArray(values);\n  }\n  if (iteratee) {\n    seen = arrayMap(array, baseUnary(iteratee));\n  }\n  while (++index < length) {\n    var fromIndex = 0,\n        value = values[index],\n        computed = iteratee ? iteratee(value) : value;\n\n    while ((fromIndex = indexOf(seen, computed, fromIndex, comparator)) > -1) {\n      if (seen !== array) {\n        splice.call(seen, fromIndex, 1);\n      }\n      splice.call(array, fromIndex, 1);\n    }\n  }\n  return array;\n}\n\nexport default basePullAll;\n"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,gBAAgB;AACrC,OAAOC,WAAW,MAAM,mBAAmB;AAC3C,OAAOC,eAAe,MAAM,uBAAuB;AACnD,OAAOC,SAAS,MAAM,iBAAiB;AACvC,OAAOC,SAAS,MAAM,iBAAiB;;AAEvC;AACA,IAAIC,UAAU,GAAGC,KAAK,CAACC,SAAS;;AAEhC;AACA,IAAIC,MAAM,GAAGH,UAAU,CAACG,MAAM;;AAE9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,WAAWA,CAACC,KAAK,EAAEC,MAAM,EAAEC,QAAQ,EAAEC,UAAU,EAAE;EACxD,IAAIC,OAAO,GAAGD,UAAU,GAAGX,eAAe,GAAGD,WAAW;IACpDc,KAAK,GAAG,CAAC,CAAC;IACVC,MAAM,GAAGL,MAAM,CAACK,MAAM;IACtBC,IAAI,GAAGP,KAAK;EAEhB,IAAIA,KAAK,KAAKC,MAAM,EAAE;IACpBA,MAAM,GAAGP,SAAS,CAACO,MAAM,CAAC;EAC5B;EACA,IAAIC,QAAQ,EAAE;IACZK,IAAI,GAAGjB,QAAQ,CAACU,KAAK,EAAEP,SAAS,CAACS,QAAQ,CAAC,CAAC;EAC7C;EACA,OAAO,EAAEG,KAAK,GAAGC,MAAM,EAAE;IACvB,IAAIE,SAAS,GAAG,CAAC;MACbC,KAAK,GAAGR,MAAM,CAACI,KAAK,CAAC;MACrBK,QAAQ,GAAGR,QAAQ,GAAGA,QAAQ,CAACO,KAAK,CAAC,GAAGA,KAAK;IAEjD,OAAO,CAACD,SAAS,GAAGJ,OAAO,CAACG,IAAI,EAAEG,QAAQ,EAAEF,SAAS,EAAEL,UAAU,CAAC,IAAI,CAAC,CAAC,EAAE;MACxE,IAAII,IAAI,KAAKP,KAAK,EAAE;QAClBF,MAAM,CAACa,IAAI,CAACJ,IAAI,EAAEC,SAAS,EAAE,CAAC,CAAC;MACjC;MACAV,MAAM,CAACa,IAAI,CAACX,KAAK,EAAEQ,SAAS,EAAE,CAAC,CAAC;IAClC;EACF;EACA,OAAOR,KAAK;AACd;AAEA,eAAeD,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}